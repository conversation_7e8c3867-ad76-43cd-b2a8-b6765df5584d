package com.pax.market.mq.shared.kafka.billing;

import java.util.List;

public interface BillingTopicNames {
    String T_SYNC_TERMINAL_DETAIL = "t_sync_terminal_detail";
    String T_SYNC_TERMINAL_ENROLL_DETAIL = "t_sync_terminal_enroll_detail";
    String T_SYNC_TERMINAL_INSTALL_LAUNCHER = "t_sync_install_launcher";
    String T_SYNC_SKYHOOK_ACCESS_TIME = "t_sync_skyhook_access_time";

    String T_SYNC_ADUP_USAGE_DETAIL = "t_sync_adup_usage_detail";
    String T_SYNC_AIRSHIELD_ACCESS_TIME = "t_sync_airshield_access_time";
    String T_SYNC_EMM_USAGE_DETAIL = "t_sync_emm_usage_detail";
    String T_RERUN_BILLING_JOB = "t_rerun_billing_job";
    String T_UPDATE_RESELLER_MARKET = "t_update_reseller_market";
    String T_UPDATE_TERMINAL_RESELLER = "t_update_terminal_reseller";
    String T_UPDATE_AIRVIEWER_USAGE_BY_MIGRATION = "t_update_airviewer_usage_by_migration";
    String T_SYNC_INSTALLED_SOLUTION_APP = "t_sync_solution_app";
    String T_SYNC_SOLUTION_APP_COUNT = "t_sync_solution_app_count";
    String T_SYNC_SUBSCRIBED_SOLUTION_APP = "t_sync_subscribed_solution_app";
    String T_SYNC_SERVICE_TERMINAL_COUNT = "t_sync_service_terminal_count";
    String T_SYNC_TERMINAL_DELETED = "t_sync_terminal_deleted";
    String T_SYNC_TERMINAL_ACTIVATED = "t_sync_terminal_activated";
    String T_SYNC_TERMINAL_REPLACEMENT = "t_sync_terminal_replace";
    String T_SYNC_AIRLINK_USAGE_DETAIL = "t_sync_air_link_usage_detail";


    List<String> BILLING_TOPIC_NAME_LIST = List.of(
            T_SYNC_TERMINAL_DETAIL,
            T_SYNC_TERMINAL_ENROLL_DETAIL,
            T_SYNC_TERMINAL_INSTALL_LAUNCHER,
            T_SYNC_SKYHOOK_ACCESS_TIME,
            T_SYNC_ADUP_USAGE_DETAIL,
            T_SYNC_AIRSHIELD_ACCESS_TIME,
            T_SYNC_EMM_USAGE_DETAIL,
            T_RERUN_BILLING_JOB,
            T_UPDATE_RESELLER_MARKET,
            T_UPDATE_TERMINAL_RESELLER,
            T_SYNC_INSTALLED_SOLUTION_APP,
            T_SYNC_SOLUTION_APP_COUNT,
            T_SYNC_SUBSCRIBED_SOLUTION_APP,
            T_SYNC_SERVICE_TERMINAL_COUNT,
            T_UPDATE_AIRVIEWER_USAGE_BY_MIGRATION,
            T_SYNC_TERMINAL_DELETED,
            T_SYNC_TERMINAL_ACTIVATED,
            T_SYNC_TERMINAL_REPLACEMENT,
            T_SYNC_AIRLINK_USAGE_DETAIL
    );

    List<String> BILLING_PRODUCER_ONLY_TOPIC_NAME_LIST = List.of(
            T_SYNC_TERMINAL_DETAIL,
            T_SYNC_TERMINAL_ENROLL_DETAIL,
            T_SYNC_TERMINAL_INSTALL_LAUNCHER,
            T_SYNC_SKYHOOK_ACCESS_TIME,
            T_SYNC_ADUP_USAGE_DETAIL,
            T_RERUN_BILLING_JOB,
            T_UPDATE_RESELLER_MARKET,
            T_UPDATE_TERMINAL_RESELLER,
            T_SYNC_AIRSHIELD_ACCESS_TIME,
            T_SYNC_EMM_USAGE_DETAIL,
            T_SYNC_INSTALLED_SOLUTION_APP,
            T_SYNC_SUBSCRIBED_SOLUTION_APP,
            T_SYNC_SOLUTION_APP_COUNT,
            T_SYNC_SERVICE_TERMINAL_COUNT,
            T_UPDATE_AIRVIEWER_USAGE_BY_MIGRATION,
            T_SYNC_TERMINAL_DELETED,
            T_SYNC_TERMINAL_ACTIVATED,
            T_SYNC_TERMINAL_REPLACEMENT,
            T_SYNC_AIRLINK_USAGE_DETAIL
    );

    List<String> BILLING_TOPIC_MSG_IN_JSON_LIST = List.of(
            T_SYNC_TERMINAL_DETAIL,
            T_SYNC_TERMINAL_ENROLL_DETAIL,
            T_SYNC_TERMINAL_INSTALL_LAUNCHER,
            T_SYNC_SKYHOOK_ACCESS_TIME,
            T_SYNC_ADUP_USAGE_DETAIL,
            T_RERUN_BILLING_JOB,
            T_UPDATE_RESELLER_MARKET,
            T_UPDATE_TERMINAL_RESELLER,
            T_SYNC_AIRSHIELD_ACCESS_TIME,
            T_SYNC_EMM_USAGE_DETAIL,
            T_SYNC_INSTALLED_SOLUTION_APP,
            T_SYNC_SUBSCRIBED_SOLUTION_APP,
            T_SYNC_SOLUTION_APP_COUNT,
            T_SYNC_SERVICE_TERMINAL_COUNT,
            T_UPDATE_AIRVIEWER_USAGE_BY_MIGRATION,
            T_SYNC_TERMINAL_DELETED,
            T_SYNC_TERMINAL_ACTIVATED,
            T_SYNC_TERMINAL_REPLACEMENT,
            T_SYNC_AIRLINK_USAGE_DETAIL
    );
}
