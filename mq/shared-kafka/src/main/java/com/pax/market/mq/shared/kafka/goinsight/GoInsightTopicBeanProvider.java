/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.mq.shared.kafka.goinsight;

import com.pax.support.mq.kafka.TopicBeanProvider;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class GoInsightTopicBeanProvider implements TopicBeanProvider, ApplicationContextAware {
    public static final String CONSUMER_CONTAINER_FACTORY_BEAN_SUFFIX = "_data_cf";
    private static String consumerGroupId;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        consumerGroupId = applicationContext.getEnvironment().getProperty("kafka.consumer.group-id.data-analysis", "paxstore_insight_consumer_grp");
    }

    @Override
    public List<String> getTopicNames() {
        return GoinsightTopicNames.DATA_TOPIC_NAME_LIST;
    }

    @Override
    public List<String> getJsonTopicNames() {
        return GoinsightTopicNames.DATA_TOPIC_MSG_IN_JSON_LIST;
    }

    @Override
    public String getConsumerGroupId() {
        return consumerGroupId;
    }

    @Override
    public String getConsumerContainerFactoryBeanSuffix() {
        return CONSUMER_CONTAINER_FACTORY_BEAN_SUFFIX;
    }
}
