/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.mq.shared.kafka.goinsight;

import java.util.List;


/**
 * The interface Goinsight topic names.
 */
public interface GoinsightTopicNames {

    //同步Goinsight - general
    String T_TERMINAL_HISTORY_INFO_SYNC = "t_terminal_history_info_sync";
    String T_TERMINAL_REALTIME_DETAIL_SYNC = "t_terminal_realtime_detail_sync";
    String T_SYNC_APP_BIZ_DATA = "t_sync_biz_data";
    String T_TERMINAL_CHECK_UP_DATA_SYNC = "t_sync_terminal_checkup_data";
    String T_SYNC_BURIED_POINT = "t_sync_buried_point";
    String T_NAVIGO_SYNC_BURIED_POINT = "t_navigo_sync_buried_point";
    String T_SYNC_TERMINAL_INFO = "t_sync_terminal_info";
    String T_COLLECT_TERMINAL_INFO_TO_INSIGHT = "t_collect_terminal_info_to_insight";
    String T_COLLECT_TERMINAL_APP_TO_INSIGHT = "t_collect_terminal_app_to_insight";
    String T_ACTIVE_APP_INSIGHT_DATASET = "t_active_app_insight_dataset";
    String T_SEND_DICTIONARY_TO_INSIGHT = "t_send_dictionary_to_insight";
    String T_SYNC_MARKET_INFO = "t_sync_market_info";
    String T_SYNC_APP_SANDBOX_BIZ_DATA = "t_sync_sandbox_biz_data";

    //GoInsight - 代理商移动,用户邮箱重置
    String T_RESELLER_MOVE = "t_reseller_move";
    String T_USER_STATUS_CHANGE = "t_user_email_reset";

    String T_SEND_DOWNLOAD_FILE_INFO_TO_INSIGHT = "t_send_download_file_info_to_insight";


    /**
     * The constant DATA_TOPIC_NAME_LIST.
     */
    //Events for insight process in these topics && related insight
    List<String> DATA_TOPIC_NAME_LIST = List.of(
            T_TERMINAL_HISTORY_INFO_SYNC,
            T_TERMINAL_REALTIME_DETAIL_SYNC,
            T_SYNC_APP_BIZ_DATA,
            T_SYNC_APP_SANDBOX_BIZ_DATA,
            T_ACTIVE_APP_INSIGHT_DATASET,
            T_SEND_DICTIONARY_TO_INSIGHT,
            T_SYNC_BURIED_POINT,
            T_NAVIGO_SYNC_BURIED_POINT,
            T_SYNC_TERMINAL_INFO,
            T_COLLECT_TERMINAL_INFO_TO_INSIGHT,
            T_COLLECT_TERMINAL_APP_TO_INSIGHT,
            T_SYNC_MARKET_INFO,
            T_RESELLER_MOVE,
            T_USER_STATUS_CHANGE,
            T_TERMINAL_CHECK_UP_DATA_SYNC,
            T_SEND_DOWNLOAD_FILE_INFO_TO_INSIGHT
    );

    List<String> DATA_TOPIC_MSG_IN_JSON_LIST = List.of(
            T_SYNC_TERMINAL_INFO,
            T_COLLECT_TERMINAL_INFO_TO_INSIGHT,
            T_COLLECT_TERMINAL_APP_TO_INSIGHT,
            T_TERMINAL_REALTIME_DETAIL_SYNC,
            T_TERMINAL_HISTORY_INFO_SYNC,
            T_SYNC_APP_BIZ_DATA,
            T_SYNC_APP_SANDBOX_BIZ_DATA,
            T_NAVIGO_SYNC_BURIED_POINT,
            T_SYNC_BURIED_POINT,
            T_SYNC_MARKET_INFO,
            T_TERMINAL_CHECK_UP_DATA_SYNC,
            T_SEND_DOWNLOAD_FILE_INFO_TO_INSIGHT
    );
}
