/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.producer.gateway.webhook;

import com.pax.market.mq.contract.market.thirdparty.WebHookMessage;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
public interface WebHookPushGateWay {

    void send(WebHookMessage message);
}