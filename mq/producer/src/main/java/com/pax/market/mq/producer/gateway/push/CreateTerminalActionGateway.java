/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.producer.gateway.push;

import com.pax.market.mq.contract.push.CreateTerminalActionMessage;


/**
 * The interface Create terminal action gateway.
 */
public interface CreateTerminalActionGateway {

    /**
     * Send.
     *
     * @param message the message
     */
    void send(CreateTerminalActionMessage message);

}