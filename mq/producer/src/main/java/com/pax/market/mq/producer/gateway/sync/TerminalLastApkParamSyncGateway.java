/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.mq.producer.gateway.sync;

import com.pax.market.mq.contract.sync.TerminalLastApkParamMessage;
import com.pax.market.mq.contract.sync.TerminalLastLauncherMessage;


/**
 * The interface Terminal last apk param sync gateway.
 */
public interface TerminalLastApkParamSyncGateway {

    /**
     * Send.
     *
     * @param message the message
     */
    void send(TerminalLastApkParamMessage message);

    /**
     * Send sync launcher message
     *
     * @param message the message
     */
    void sendSyncLauncher(TerminalLastLauncherMessage message);

}