/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.mq.contract.push;


import com.pax.market.push.common.protocol.BizCommand;

/**
 * The type Rki download push message.
 */
public class TerminalRkiPushMessage extends PushCommandMessage {

    private static final long serialVersionUID = 1L;

    /**
     * Instantiates a new Rki download push message.
     */
    public TerminalRkiPushMessage() {
        super();
    }

    public TerminalRkiPushMessage(Long terminalId) {
        super(terminalId);
    }

    public BizCommand getCommand() {
        return BizCommand.TERMINAL_RKI;
    }
}
