/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.contract.sync;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
public class AppDetail implements Serializable {
    @Serial
    private static final long serialVersionUID = 9025497872203226176L;
    private String packageName;
    private String versionName;
    private String appName;
    private String source;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppDetail appDetail = (AppDetail) o;
        return StringUtils.equals(packageName, appDetail.getPackageName()) &&
                StringUtils.equals(versionName, appDetail.getVersionName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(packageName, versionName);
    }
}
