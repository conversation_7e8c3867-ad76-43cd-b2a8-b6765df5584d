/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.contract.stock;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * The type Terminal stock migration message.
 * Merge terminal data to asset when market asset management flag off --> on
 *
 * <AUTHOR>
 * @date 2018 /11/26
 */
@Getter
@Setter
@AllArgsConstructor
public class TerminalStockMigrationMessage extends AbstractMessage {
    private static final long serialVersionUID = -4795898188917934724L;

    private Long marketId;
}
