package com.pax.market.mq.contract.billing;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Builder
public class SyncDeleteTerminalMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 497258746844889035L;

    private Long terminalId;
    private Long marketId;
    private String serialNo;
    private Date deleteDate;
}
