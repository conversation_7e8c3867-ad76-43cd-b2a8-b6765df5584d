/*
 *  *******************************************************************************
 *  COPYRIGHT
 *                PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *    This software is supplied under the terms of a license agreement or
 *    nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *    or disclosed except in accordance with the terms in that agreement.
 *
 *       Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  *******************************************************************************
 */

package com.pax.market.mq.contract.apk;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ResetApkSignatureMessage extends ApkOperationMessage {

    private static final long serialVersionUID = -5691406332487708158L;
    private Long marketId;
    private Long resellerId;
    private Long factoryId;
    private String signType;
}
