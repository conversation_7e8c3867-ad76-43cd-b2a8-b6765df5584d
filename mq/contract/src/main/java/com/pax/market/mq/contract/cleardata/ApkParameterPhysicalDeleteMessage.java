package com.pax.market.mq.contract.cleardata;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
public class ApkParameterPhysicalDeleteMessage extends AbstractMessage {
    @Serial
    private static final long serialVersionUID = 8593871626343299684L;

    private Long id;

    public ApkParameterPhysicalDeleteMessage(Long marketId, Long id) {
        super.setMarketId(marketId);
        this.id = id;
    }
}
