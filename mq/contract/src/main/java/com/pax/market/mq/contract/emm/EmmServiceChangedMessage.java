package com.pax.market.mq.contract.emm;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/7/15
 */
@Getter
@Setter
public class EmmServiceChangedMessage extends AbstractMessage {
    @Serial
    private static final long serialVersionUID = -1797070746232250794L;

    private Set<Long> resellerIds;
}
