package com.pax.market.mq.contract.market;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pax.support.mq.core.message.AbstractMessage;
import lombok.*;

import java.io.Serial;

/**
 * Abstract Audit Message
 *
 * <AUTHOR>
 * @date Mar 28, 2022
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
public class AbstractMarketInfoMessage extends AbstractMessage {
    @Serial
    private static final long serialVersionUID = 5950462264669000358L;
    private Long eventTime;
    @JsonProperty(value = "_eventtime")
    public Long getEventTime() {
        return eventTime;
    }
}
