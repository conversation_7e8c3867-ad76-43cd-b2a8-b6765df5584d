package com.pax.market.mq.contract.cleardata;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
public class MerchantPhysicalDeleteMessage extends AbstractMessage {

    @Serial
    private static final long serialVersionUID = 6841010733619618698L;

    private Long merchantId;
    private boolean physicalDelete;

    public MerchantPhysicalDeleteMessage(Long marketId, Long merchantId, boolean physicalDelete) {
        super.setMarketId(marketId);
        this.merchantId = merchantId;
        this.physicalDelete = physicalDelete;
    }
}
