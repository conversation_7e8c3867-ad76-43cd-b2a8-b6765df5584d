/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.contract.orgnization;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * The type Merchant delete message.
 */
@Getter
@Setter
@AllArgsConstructor
public class MerchantCreatedMessage extends MerchantChangedMessage {

    @Serial
    private static final long serialVersionUID = 1L;
    private Long merchantId;
    private boolean merchantUserCreated;

}
