package com.pax.market.mq.contract.billing;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
public class MarketBillingSettingMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 5144039973869220173L;

    private Long id;
    private Long marketId;
    private String billType;
    private boolean isCharge;
    private String chargeType;
    private BigDecimal packagePrice;
    private BigDecimal packageQuantity;
    private Date createdDate;
    private Date updatedDate;

    List<MarketBillingSettingDiscountMessage> discountSettings;
    List<MarketBillingSettingPriceMessage> priceSettings;
    CyberLabBillingSettingMessage cyberLabSetting;
}
