package com.pax.market.mq.contract.billing;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/27 13:38
 */
@Getter
@Setter
@Builder
public class SyncAirLinkUsageDetailMessage extends AbstractMessage {

    @Serial
    private static final long serialVersionUID = -2149084594899489570L;

    private Long airlinkTerminalId;
    private Long marketId;
    private String serialNo;
    private Long resellerId;
    private String resellerName;
    private String resellerParentIds;
    private String organization;

    private String imei;
    private BigDecimal dataUsage;
    private Date esimActivateTime;
    private Date syncTime;

    private boolean isDelete;
    private boolean isDeleteCurrent;
}
