package com.pax.market.mq.contract.vas;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.*;

import java.io.Serial;


/**
 * <AUTHOR>
 * @since 9.2
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
public class VasNotifyMessage extends AbstractMessage {

    @Serial
    private static final long serialVersionUID = 1823107455660475343L;
    private String loginName;

}
