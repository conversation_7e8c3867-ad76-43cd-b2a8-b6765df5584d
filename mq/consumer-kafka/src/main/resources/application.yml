spring:
  main.allow-bean-definition-overriding: true
  main.allow-circular-references: true
  banner:
    charset: UTF-8

#Global setting. globalProvinceFlag: Whether allow save province/city when create reseller/merchant.PMAR-5586. Only paxus will open it
global:
  setting:
    province:
      enabled: ${global-province-flag}

######### Pre-defined roles and merchant types
pre:
  defined:
    reseller:
      roles: "${pre-defined-reseller-roles}"
    merchant:
      types: "${pre-defined-merchant-types}"

terminal:
  forceupdate:
    rate:
      cfg:
        enabled: true
        checkPeriod: 1000
        batchSize: 100

#============================================#
#             MQ Configuration               #
#============================================#
kafka:

  consumer:
    group-id:
      core: paxstore_core_consumer_grp
      general: paxstore_general_consumer_grp
      data-analysis: paxstore_insight_consumer_grp
      notification: paxstore_notification_consumer_grp

    auto-commit: true
    auto-commit-interval-ms: 100
    session-timeout-ms: 15000
    container-poll-timeout-ms: 3000
    max-poll-records: 100
    max-poll-interval-ms: 300000

  customization:

    concurrency:
      t_push_cmd: 6
      t_terminal_app_sync: 4
      t_terminal_config_sync: 4
      t_terminal_detail_sync: 4
      t_terminal_monitor_sync: 4
      t_terminal_battery_sync: 4
      t_terminal_installed_param_sync: 2
      t_validate_group_apk_param: 6
      t_validate_traditional_group_apk_param: 6
      t_terminal_protocol_sync: 4
      t_terminal_apk_param_sync: 4
      t_terminal_launcher_sync: 4
      t_internal_data_sync: 4
      t_push_pending_terminal_action: 6
      t_push_pending_traditional_terminal_action: 6
      t_push_profile_terminal: 6
      t_create_pending_terminal_action: 6
      t_check_new_terminal_action: 6
      t_create_terminal_action: 4
      t_create_terminal_history_action: 10
      t_move_terminal_action: 4
      t_backup_terminal_action_history: 4
      t_resume_terminal_action: 4
      t_save_api_audit: 3
      t_build_audit_trail: 4
      t_apk_param_template_migration: 2
      t_terminal_action_update: 10
      t_traditional_terminal_action_update: 6
      t_after_app_download_action: 2
      t_terminal_client_download: 2
      t_google_loc_resolver: 2
      t_terminal_geofence_sync: 2
      t_terminal_geolocation_calibration: 2
      t_terminal_geo_alarm: 2
      t_terminal_location_refresh: 2
      t_push_event: 2
      t_sync_terminal_devinfo: 2
      t_notification_request: 2
      t_notification_source_message: 2
      t_notification_console_message: 2
      t_notification_email_message: 2
      t_notification_mobile_message: 2
      t_check_pending_terminal_action: 5
      t_terminal_history_info_sync: 7
      t_terminal_realtime_detail_sync: 4
      t_sync_biz_data: 6
      t_sync_terminal_info: 6
      t_collect_terminal_info_to_insight: 4
      t_collect_terminal_app_to_insight: 4
      t_send_dictionary_to_insight: 2
      t_sync_storeclient_crashlog: 1
      t_update_group_action_count: 4
      t_check_init_terminal_action: 4
      t_send_download_file_info_to_insight: 4
      t_sync_terminal_airshield_detect_data: 4
      t_dc_status_update: 1
      t_data_sync_event_in: 2
      t_data_sync_event_out: 2

      t_cm_p: 1
      t_cm_sft: 1
      t_cm_snft: 2
      t_cm_ssm: 4

      t_airlink_terminal_deduct: 1
      t_airlink_terminal_cancel_active: 1
      t_airlink_terminal_active_result: 2
      t_airlink_event_call_back: 1

    max-poll-records:
      t_file_diff: 1
      t_data_sync_event_out: 1
      t_cm_ssm: 1
      t_cm_snft: 1
      t_check_new_terminal_action: 1
      t_import_activity: 1
      t_export_activity: 1
      t_internal_data_sync: 1
      t_create_terminal_action: 10
      t_move_terminal_action: 10
      t_save_api_audit: 10
      t_build_audit_trail: 10
      t_vas_status_changed: 1
      t_vas_notify: 1
      t_vas_notification: 1
      t_push_web_hooks_message: 10
      t_vas_detect_attr: 10
      t_send_dictionary_to_insight: 1
      t_sync_terminal_info: 50
      t_terminal_history_info_sync: 20
      t_airlink_terminal_deduct: 1
      t_airlink_terminal_active: 1
      t_airlink_terminal_cancel_active: 1
      t_airlink_card_number_change: 1

    session-timeout-ms:
      t_file_diff: 360000
      t_save_api_audit: 300000
      t_terminal_action_update: 60000
      t_create_terminal_action: 60000
      t_move_terminal_action: 60000
      t_terminal_app_sync: 30000
      t_terminal_manual_app_sync: 30000
      t_validate_group_apk_param: 30000
      t_create_push_task: 60000
      t_bind_terminal_rki_key: 60000
      t_reversal_terminal_rki_key: 60000
      t_cm_snft: 300000
      t_cm_ssm: 300000
      t_check_new_terminal_action: 300000
      t_build_audit_trail: 60000
      t_import_activity: 360000
      t_export_activity: 360000
      t_internal_data_sync: 360000
      t_send_dictionary_to_insight: 60000
      t_sync_terminal_info: 30000
      t_terminal_history_info_sync: 30000

    batch-listener:
      t_push_cmd: true
      t_push_event: true
      t_push_diagnosis: true
      t_sync_storeclient_crashlog: true
      t_sync_storeclient_event: true
      t_backup_terminal_action_history: true
      t_update_group_action_count: true
      t_sync_terminal_airshield_detect_data: true
      t_vas_detect_attr: true
      t_terminal_history_info_sync: true

    consumer-auto-commit:
      t_data_sync_event_out: false
      t_file_diff: false
      t_export_activity: false
      t_airlink_terminal_deduct: false
      t_airlink_card_number_change: false

    listener-ack:
      t_data_sync_event_out: MANUAL
      t_file_diff: MANUAL
      t_export_activity: MANUAL
      t_airlink_terminal_deduct: MANUAL
      t_airlink_card_number_change: MANUAL

    key-deserializer:
      t_push_event: org.apache.kafka.common.serialization.StringDeserializer




#Push Client Biz Configuration
push-client-cfg:
  push-request-timeout: ${push_command_timeout:20000}
  ##### Commands list which do not need to persist in mpush
  non-persist-commands:
    - DISABLE_POS
    - START_AIRVIEWER
  ##### The expire time of the persisted commands, related to above configuration
  persist-command-expire-time: ${push_command_expire_time:19800}
  ##### Whether to ignore the same online status event message if the current is online and previous is online too
  ignore-send-same-online-status: true
  ##### Commands which need to delete the previous terminal online status before send, related to above configuration
  commands-need-refresh-online-status:
    - DISABLE_POS
    - TERMINAL_ACTIVATION