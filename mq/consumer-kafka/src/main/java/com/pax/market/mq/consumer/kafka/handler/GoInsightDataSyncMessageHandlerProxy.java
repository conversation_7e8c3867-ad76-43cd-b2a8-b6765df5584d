package com.pax.market.mq.consumer.kafka.handler;

import com.pax.market.mq.consumer.handler.buriedpoint.BuriedPointMsgHandler;
import com.pax.market.mq.consumer.handler.buriedpoint.NavigoBuriedPointMsgHandler;
import com.pax.market.mq.consumer.handler.goinsight.*;
import com.pax.market.mq.consumer.handler.sync.MarketInfoSyncMsgHandler;
import com.pax.market.mq.consumer.handler.sync.TerminalDetailSyncHandler;
import com.pax.market.mq.contract.goinsight.AppDataSetActiveMessage;
import com.pax.market.mq.contract.goinsight.GoInsightDictionarySyncMessage;
import com.pax.market.mq.contract.goinsight.ResellerMoveMessage;
import com.pax.market.mq.contract.goinsight.UserStatusChangeNotifyGoInsightMessage;
import com.pax.market.mq.shared.kafka.goinsight.GoInsightTopicBeanProvider;
import com.pax.market.mq.shared.kafka.goinsight.GoinsightTopicNames;
import com.pax.support.mq.kafka.consumer.condition.StoreConsumerGroupEnabledConditions;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.context.annotation.Conditional;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

@RequiredArgsConstructor
@Component
@Conditional(StoreConsumerGroupEnabledConditions.StoreGoInsightConsumerEnabled.class)
public class GoInsightDataSyncMessageHandlerProxy {

    public static final String GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX = GoInsightTopicBeanProvider.CONSUMER_CONTAINER_FACTORY_BEAN_SUFFIX;
    private final AppBizDataHandler appBizDataHandler;
    private final TerminalHistoryInfoStatisticHandler terminalHistoryInfoStatisticHandler;
    private final TerminalDetailSyncHandler terminalDetailSyncHandler;
    private final AppDataSetActiveHandler appDataSetActiveHandler;
    private final GoInsightDictionaryHandler goInsightDictionaryHandler;
    private final BuriedPointMsgHandler buriedPointMsgHandler;
    private final NavigoBuriedPointMsgHandler navigoBuriedPointMsgHandler;
    private final TerminalInfoToGoInsightSenderMsgHandler terminalInfoToGoInsightSenderMsgHandler;
    private final TerminalInfoToGoInsightCollectMsgHandler terminalInfoToGoInsightCollectMsgHandler;
    private final MarketInfoSyncMsgHandler marketInfoSyncMsgHandler;
    private final ResellerMoveHandler resellerMoveHandler;
    private final UserStatusChangeNotifyGoInsightHandler userStatusChangeNotifyGoInsightHandler;
    private final TerminalCheckupInfoToGoInsightHandler terminalCheckupInfoToGoInsightHandler;
    private final DownloadFileInfoToGoInsightHandler downloadFileInfoToGoInsightHandler;

    @KafkaListener(topics = GoinsightTopicNames.T_TERMINAL_HISTORY_INFO_SYNC,
            containerFactory = GoinsightTopicNames.T_TERMINAL_HISTORY_INFO_SYNC + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void terminalHistoryInfoSync(List<ConsumerRecord<Integer, String>> records) {
        //terminalHistoryInfoStatisticHandler.handleJsonContent(record.value());
        terminalHistoryInfoStatisticHandler.handleConsumerRecords(records);;
    }

    @KafkaListener(topics = GoinsightTopicNames.T_TERMINAL_REALTIME_DETAIL_SYNC,
            containerFactory = GoinsightTopicNames.T_TERMINAL_REALTIME_DETAIL_SYNC + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void terminalDetailSync(ConsumerRecord<Integer, String> record) {
        terminalDetailSyncHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_TERMINAL_CHECK_UP_DATA_SYNC,
            containerFactory = GoinsightTopicNames.T_TERMINAL_CHECK_UP_DATA_SYNC + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void terminalCheckupDataSync(ConsumerRecord<Integer, String> record) {
        terminalCheckupInfoToGoInsightHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SYNC_APP_BIZ_DATA,
            containerFactory = GoinsightTopicNames.T_SYNC_APP_BIZ_DATA + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void syncThirdPartyAppBizData(ConsumerRecord<Integer, String> record) {
        appBizDataHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SYNC_APP_SANDBOX_BIZ_DATA,
            containerFactory = GoinsightTopicNames.T_SYNC_APP_SANDBOX_BIZ_DATA + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void syncThirdPartyAppSandboxBizData(ConsumerRecord<Integer, String> record) {
        appBizDataHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_ACTIVE_APP_INSIGHT_DATASET,
            containerFactory = GoinsightTopicNames.T_ACTIVE_APP_INSIGHT_DATASET + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void notify(AppDataSetActiveMessage msg) {
        appDataSetActiveHandler.handleMessage(msg);
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SEND_DICTIONARY_TO_INSIGHT,
            containerFactory = GoinsightTopicNames.T_SEND_DICTIONARY_TO_INSIGHT + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void syncDictionaryData(GoInsightDictionarySyncMessage msg) {
        goInsightDictionaryHandler.handleMessage(msg);
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SYNC_BURIED_POINT,
            containerFactory = GoinsightTopicNames.T_SYNC_BURIED_POINT + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void receiveBuriedPointMsg(ConsumerRecord<Integer, String> record) {
        buriedPointMsgHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_NAVIGO_SYNC_BURIED_POINT,
            containerFactory = GoinsightTopicNames.T_NAVIGO_SYNC_BURIED_POINT + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void receiveNavigoBuriedPointMsg(ConsumerRecord<Integer, String> record) {
        navigoBuriedPointMsgHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_COLLECT_TERMINAL_INFO_TO_INSIGHT,
            containerFactory = GoinsightTopicNames.T_COLLECT_TERMINAL_INFO_TO_INSIGHT + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void collectTerminalInfoMsg(ConsumerRecord<Integer, String> record) {
        terminalInfoToGoInsightCollectMsgHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_COLLECT_TERMINAL_APP_TO_INSIGHT,
            containerFactory = GoinsightTopicNames.T_COLLECT_TERMINAL_APP_TO_INSIGHT + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void collectTerminalAppMsg(ConsumerRecord<Integer, String> record) {
        terminalInfoToGoInsightCollectMsgHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SYNC_TERMINAL_INFO,
            containerFactory = GoinsightTopicNames.T_SYNC_TERMINAL_INFO + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void receiveTerminalInfoMsg(ConsumerRecord<Integer, String> record) {
        terminalInfoToGoInsightSenderMsgHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SYNC_MARKET_INFO,
            containerFactory = GoinsightTopicNames.T_SYNC_MARKET_INFO + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void receiveMarketInfoMsg(ConsumerRecord<Integer, String> record) {
        marketInfoSyncMsgHandler.handleJsonContent(record.value());
    }

    @KafkaListener(topics = GoinsightTopicNames.T_RESELLER_MOVE,
            containerFactory = GoinsightTopicNames.T_RESELLER_MOVE + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void moveReseller(ResellerMoveMessage message) {
        resellerMoveHandler.handleMessage(message);
    }

    @KafkaListener(topics = GoinsightTopicNames.T_USER_STATUS_CHANGE,
            containerFactory = GoinsightTopicNames.T_USER_STATUS_CHANGE + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void userEmailReset(UserStatusChangeNotifyGoInsightMessage message) {
        userStatusChangeNotifyGoInsightHandler.handleMessage(message);
    }

    @KafkaListener(topics = GoinsightTopicNames.T_SEND_DOWNLOAD_FILE_INFO_TO_INSIGHT,
            containerFactory = GoinsightTopicNames.T_SEND_DOWNLOAD_FILE_INFO_TO_INSIGHT + GOINSIGHT_CONTAINER_FACTORY_BEAN_SUFFIX)
    public void sendDownloadFileInfo(ConsumerRecord<Integer, String> record) {
        downloadFileInfoToGoInsightHandler.handleJsonContent(record.value());
    }
}
