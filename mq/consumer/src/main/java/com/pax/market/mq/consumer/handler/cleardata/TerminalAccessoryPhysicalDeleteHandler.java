package com.pax.market.mq.consumer.handler.cleardata;

import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.cleardata.TerminalAccessoryPhysicalDeleteMessage;
import com.paxstore.market.domain.service.cleardata.TerminalAccessoryPhysicalDeleteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TerminalAccessoryPhysicalDeleteHandler extends AbstractHandler<TerminalAccessoryPhysicalDeleteMessage> {
    private final TerminalAccessoryPhysicalDeleteService terminalAccessoryPhysicalDeleteService;

    @Override
    protected void handleInternal(TerminalAccessoryPhysicalDeleteMessage message) {
        String cacheKey = String.format("distributionLock:terminalAccessoryPhysicalDelete:%d", message.getAccessoryId());
        RedisUtils.tryLock(cacheKey, "physical delete accessory error", () -> {
            terminalAccessoryPhysicalDeleteService.deleteTerminalAccessory(message.getAccessoryId());
            terminalAccessoryPhysicalDeleteService.deleteTerminalAccessoryDetail(message.getAccessoryId());
            terminalAccessoryPhysicalDeleteService.deleteTerminalAccessoryEvent(message.getAccessoryId());
            terminalAccessoryPhysicalDeleteService.deleteTerminalAccessoryAction(message.getAccessoryId());
        });
    }
}
