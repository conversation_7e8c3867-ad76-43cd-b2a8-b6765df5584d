package com.pax.market.mq.consumer.handler.sync;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pax.market.constants.TerminalAccessoryType;
import com.pax.market.framework.common.utils.StringUtils;
import com.zolon.iot.base.devicemodel.DeviceDescriptor;
import org.springframework.stereotype.Component;

@Component
public class AccessoryObjectModelDescriptor {
    private final DeviceDescriptor printerDd;
    private final DeviceDescriptor scannerDd;

    public AccessoryObjectModelDescriptor (ObjectMapper objectMapper) {
        printerDd = loadDeviceDescriptor(objectMapper, TerminalAccessoryType.PRINTER);
        scannerDd = loadDeviceDescriptor(objectMapper, TerminalAccessoryType.SCANNER);
    }

    public DeviceDescriptor getDeviceDescriptor(String type) {
        if (StringUtils.equals(TerminalAccessoryType.PRINTER, type)) {
            return printerDd;
        }
        if (StringUtils.equals(TerminalAccessoryType.SCANNER, type)) {
            return scannerDd;
        }
        return null;
    }

    private DeviceDescriptor loadDeviceDescriptor(ObjectMapper objectMapper, String type) {
        try {
            return objectMapper.readValue(getClass().getClassLoader().getResource(type + "_dd.json"), DeviceDescriptor.class);
        } catch (Exception e) {
            throw new IllegalArgumentException("failed to load & deser DeviceDescriptor", e);
        }
    }
}
