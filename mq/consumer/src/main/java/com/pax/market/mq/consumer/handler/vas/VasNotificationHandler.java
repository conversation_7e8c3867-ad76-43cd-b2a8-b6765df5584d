package com.pax.market.mq.consumer.handler.vas;

import com.pax.market.functional.vas.VasNotificationFunctionService;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.vas.VasNotificationMessage;
import com.zolon.saas.notification.func.enums.Operations;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> @since 9.7
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class VasNotificationHandler extends AbstractHandler<VasNotificationMessage> {

    private final VasNotificationFunctionService vasNotificationFunctionService;
    protected static final long SYSTEM_USERID = 1L;

    @Override
    protected void handleInternal(VasNotificationMessage message) {
        Long senderId = Objects.nonNull(message.getSenderId()) ? message.getSenderId() : SYSTEM_USERID;
        String operationsValue = message.getOperationsValue();
        Long developerId = message.getDeveloperId();
        Long marketId = message.getMarketId();
        if (Objects.nonNull(operationsValue)) {
            Operations operations = Operations.convert(operationsValue);
            if (Objects.nonNull(developerId)) {
                vasNotificationFunctionService.sendDeveloperNotificationByOperations(marketId, developerId, senderId, operations, message.getServiceName());
            } else {
                vasNotificationFunctionService.sendAdminNotificationByOperations(marketId, senderId, operations, message.getArgs(), message.isAdminOnly());
            }
        }
    }


}
