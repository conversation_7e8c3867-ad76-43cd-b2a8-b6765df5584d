package com.pax.market.mq.consumer.handler.airlink;

import com.google.common.collect.Sets;
import com.pax.market.constants.RoleID;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.airlink.AirLinkDeductType;
import com.pax.market.constants.airlink.AirLinkTerminalActiveHistoryStatus;
import com.pax.market.constants.airlink.AirLinkTerminalOperateLevel;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrder;
import com.pax.market.domain.entity.global.vas.airlink.MarketAirLinkSetting;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalActiveHistory;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalMonthDeductDetail;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.airlink.AirLinkTerminalStatisticsInfo;
import com.pax.market.dto.vas.AirLinkUsageInfo;
import com.pax.market.dto.vas.MarketAirLinkSubscriptionPlanInfo;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.support.AirLinkTerminalSupport;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.airlink.*;
import com.pax.market.mq.producer.gateway.airlink.AirLinkTerminalCacelActiveGateway;
import com.pax.market.notification.AirLinkNotificationService;
import com.pax.market.notification.EmailNotificationService;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.vas.common.VasConstants;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.vas.ServiceResellerService;
import com.paxstore.global.domain.service.vas.airlink.AirLinkOrderService;
import com.paxstore.global.domain.service.vas.airlink.AirLinkUsageDetailService;
import com.paxstore.global.domain.service.vas.airlink.MarketAirLinkSettingService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalActiveHistoryService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * author mengxiaoxian
 * Date   2025/3/28 16:33
 */
@Component
@RequiredArgsConstructor
public class AirLinkTerminalDeductHandler extends AbstractHandler<AirLinkTerminalDeductMessage> {

    private final AirLinkTerminalActiveHistoryService airLinkTerminalActiveHistoryService;
    private final AirLinkTerminalService airLinkTerminalService;
    private final AirLinkTerminalSupport airLinkTerminalSupport;
    private final MarketAirLinkSettingService marketAirLinkSettingService;
    private final ServiceCenterFunc serviceCenterFunc;
    private final MarketService marketService;
    private final AirLinkOrderService airLinkOrderService;
    private final RoleService roleService;
    private final AirLinkNotificationService airLinkNotificationService;
    private final EmailNotificationService emailNotificationService;
    private final AirLinkUsageDetailService airLinkUsageDetailService;
    private final ResellerService resellerService;
    private final ServiceResellerService serviceResellerService;
    private final AirLinkTerminalCacelActiveGateway airLinkTerminalCacelActiveGateway;


    @Override
    protected void handleInternal(AirLinkTerminalDeductMessage message) {
        if (message instanceof AirLinkTerminalActiveHistoryDeductMessage deductMessage){
            deductActiveFee(deductMessage);
        }else if (message instanceof AirLinkTerminalMonthDeductMessage deductMessage){
            deductMonthOrTrafficFee(deductMessage.getMarketId(), deductMessage.getDeductTime(), true);
        }else if (message instanceof AirLinkTerminalOverageTrafficDeductMessage deductMessage){
            deductMonthOrTrafficFee(deductMessage.getMarketId(), deductMessage.getDeductTime(), false);
        } else if (message instanceof AirLinkTerminalResumeMonthDeductMessage deductMessage){
            deductResumeMonthFee(deductMessage.getMarketId(), deductMessage.getDeductTime(), deductMessage.getImeiList());
        }
    }

    private void deductResumeMonthFee(Long currentMarketId, Date deductTime, List<String> imeiList) {
        List<AirLinkTerminal> airLinkTerminals = airLinkTerminalService.findByImeis(currentMarketId,imeiList);
        boolean isBalanceInsufficient = false;
        if (CollectionUtils.isNotEmpty(airLinkTerminals)) {
            isBalanceInsufficient = airLinkTerminalSupport.batchResumeAirLinkTerminal(currentMarketId, deductTime, airLinkTerminals);
        }
        sendBalanceInsufficientAlarm(currentMarketId, isBalanceInsufficient);
    }

    /**
     * 扣除激活费用
     */
    private void deductActiveFee(AirLinkTerminalActiveHistoryDeductMessage deductMessage) {
        List<AirLinkTerminalActiveHistory> activeHistoryList = findActiveHistorys(deductMessage);
        if (CollectionUtils.isNotEmpty(activeHistoryList)) {
            Date deductTime = deductMessage.getDeductTime();
            String period = DateUtils.formatDate(deductTime, DateUtils.PERIOD_DATE_FORMAT);
            Long marketId = deductMessage.getMarketId();
            MarketAirLinkSetting airLinkSetting = marketAirLinkSettingService.getByMarketId(marketId);
            MarketAirLinkSubscriptionPlanInfo subscriptionPlan = serviceCenterFunc.getSubscriptionPlanByPeriod(marketId, period);
            for (AirLinkTerminalActiveHistory activeHistory : activeHistoryList) {
                statisticsTerminalNum(activeHistory, subscriptionPlan);
                deductFee(activeHistory, deductMessage, airLinkSetting, subscriptionPlan, period);
            }
        }
    }

    private List<AirLinkTerminalActiveHistory> findActiveHistorys(AirLinkTerminalActiveHistoryDeductMessage message){
        Date deductTime = message.getDeductTime();
        Date startTime = DateUtils.beginOfMonth(deductTime);
        Date endTime = DateUtils.endOfMonth(deductTime);
        List<AirLinkTerminalActiveHistory> activeHistoryList = new ArrayList<>();
        AirLinkTerminalOperateLevel terminalOperateLevel = message.getTerminalOperateLevel();
        Long marketId = message.getMarketId();
        Long resellerId = message.getResellerId();
        String activeHistoryStatus = AirLinkTerminalActiveHistoryStatus.PROCESSING.getCode();
        if (terminalOperateLevel == AirLinkTerminalOperateLevel.MARKET){
            if (message.getDeductLastMonth()){
                activeHistoryList = airLinkTerminalActiveHistoryService.findLastMonthNotDeductActiveHistoryByMarket(marketId, startTime, endTime);
            }else{
                activeHistoryList = airLinkTerminalActiveHistoryService.findMarketActiveHistory(marketId, activeHistoryStatus);
            }
        }else if(terminalOperateLevel == AirLinkTerminalOperateLevel.RESELLER && resellerId != null){
            Reseller reseller = resellerService.get(resellerId);
            if (reseller != null && reseller.getMarketId().equals(marketId)){
                if (!serviceResellerService.checkServiceResellerSpecific(VasConstants.ServiceType.AIR_LINK, marketId, resellerId)) {
                    activeHistoryList = airLinkTerminalActiveHistoryService.findResellerActiveHistory(message.getResellerId(), activeHistoryStatus);
                }
            }
        }
        return activeHistoryList;
    }

    private void statisticsTerminalNum(AirLinkTerminalActiveHistory activeHistory, MarketAirLinkSubscriptionPlanInfo subscriptionPlan){
        BigDecimal fee = subscriptionPlan.getPackageFee().add(subscriptionPlan.getActivationFee());
        Long id = activeHistory.getId();
        AirLinkTerminalStatisticsInfo airLinkTerminalStatisticsInfo = airLinkTerminalService.statisticsTerminalNum(id);
        int activeNum = airLinkTerminalStatisticsInfo.getActiveNum();
        int failNum = airLinkTerminalStatisticsInfo.getFailNum();
        int pendingNum = airLinkTerminalStatisticsInfo.getPendingNum();
        activeHistory.setActiveNum(activeNum);
        activeHistory.setFailNum(failNum);
        activeHistory.setPendingNum(pendingNum);
        int occupationNum = activeHistory.getActiveNum() - activeHistory.getDeductNum() + activeHistory.getPendingNum();
        activeHistory.setOccupationFee(fee.multiply(BigDecimal.valueOf(occupationNum)));
        airLinkTerminalActiveHistoryService.updateActiveHistoryTerminalNum(activeHistory);
    }

    /**
     * 下面三种情况会扣除激活费用
     * 1.月底扣除当月激活费用
     * 2.终端全部激活完成
     * 3.任务过期
     */
    private void deductFee(AirLinkTerminalActiveHistory activeHistory,
                           AirLinkTerminalActiveHistoryDeductMessage message,
                           MarketAirLinkSetting airLinkSetting,
                           MarketAirLinkSubscriptionPlanInfo subscriptionPlan,
                           String period){
        Date deductTime = message.getDeductTime();
        long duration = SystemPropertyHelper.getAirLinkTerminalActiveHistoryDuration() * 24 * 3600 * 1000L;
        long expireTime = activeHistory.getCreatedDate().getTime() + duration;
        boolean isExpire = expireTime <= deductTime.getTime();
        boolean isRealActiveAll = activeHistory.getPendingNum() == 0 && activeHistory.getActiveNum() + activeHistory.getFailNum() == activeHistory.getTotalNum();
        Date startTime = DateUtils.beginOfMonth(deductTime);
        Date endTime = DateUtils.endOfMonth(deductTime);
        if (message.getNeedDeduct() || isRealActiveAll || isExpire) {
            List<Long> activeTermianls = airLinkTerminalService.findCurrentMonthNotDeductActiveTermianls(activeHistory.getId(), startTime, endTime, period);
            List<AirLinkTerminalMonthDeductDetail> terminalDeductDetails = airLinkTerminalSupport.getTerminalDeductDetails(activeTermianls, period);
            boolean isBalanceInsufficient = airLinkTerminalSupport.deductActiveFee(activeHistory, terminalDeductDetails, airLinkSetting, subscriptionPlan, period, isExpire, isRealActiveAll, message);
            sendCancelActivePendingTerminalMessage(activeHistory, message, isExpire);
            sendBalanceInsufficientAlarm(activeHistory.getMarketId(), isBalanceInsufficient);
        }
    }

    /**
     * 扣除包月/超出流量费用
     */
    public void deductMonthOrTrafficFee(Long marketId, Date deductTime, boolean isMonthDeduct){
        MarketAirLinkSetting marketAirLinkSetting = marketAirLinkSettingService.getByMarketId(marketId);
        if (marketAirLinkSetting == null) {
            return;
        }
        boolean isBalanceInsufficient;
        if (isMonthDeduct){
            isBalanceInsufficient = deductMarketMonthlyFee(marketAirLinkSetting, deductTime);
        }else{
            isBalanceInsufficient = deductMarketOverageTrafficFee(marketAirLinkSetting, deductTime);
        }
        sendBalanceInsufficientAlarm(marketId, isBalanceInsufficient);
    }

    private boolean deductMarketMonthlyFee(MarketAirLinkSetting marketAirLinkSetting, Date deductTime){
        String period = DateUtils.formatDate(deductTime, DateUtils.PERIOD_DATE_FORMAT);
        Date monthStartTime = DateUtils.beginOfMonth(deductTime);
        Long marketId = marketAirLinkSetting.getMarketId();
        Date overdueDate = marketAirLinkSetting.getOverdueDate();
        if (overdueDate == null){
            MarketAirLinkSubscriptionPlanInfo currentSubscriptionPlan = serviceCenterFunc.getSubscriptionPlanByPeriod(marketId, null);
            if (currentSubscriptionPlan != null) {
                AirLinkOrder currentMonthOrder = airLinkOrderService.getDeductOrderByPeriod(marketId, AirLinkDeductType.MONTHLY.getCode(), period);
                if (currentMonthOrder == null) {
                    List<Long> currentActiveTerminals = airLinkTerminalService.findNonCurrentMonthActiveAvailTerminals(marketId, monthStartTime, period);
                    if (currentActiveTerminals.size() > 0) {
                        List<AirLinkTerminalMonthDeductDetail> terminalDeductDetails = airLinkTerminalSupport.getTerminalDeductDetails(currentActiveTerminals, period);
                        return airLinkTerminalSupport.deductMonthlyFee(marketAirLinkSetting,
                                currentSubscriptionPlan.getPackageFee(), period, terminalDeductDetails, AirLinkDeductType.MONTHLY);
                    }
                }
            }
        }
        return false;
    }

    private boolean deductMarketOverageTrafficFee(MarketAirLinkSetting marketAirLinkSetting, Date deductTime) {
        String period = DateUtils.formatDate(deductTime, DateUtils.PERIOD_DATE_FORMAT);
        Long marketId = marketAirLinkSetting.getMarketId();
        AirLinkUsageInfo lastMonthUsage = airLinkUsageDetailService.getByMarketAndPeriod(marketId, period, true);
        if (lastMonthUsage != null) {
            long useTraffic = lastMonthUsage.getTerminalCount() * lastMonthUsage.getPackageLimit() * 1024;
            BigDecimal overageTraffic = lastMonthUsage.getDataUsage().subtract(BigDecimal.valueOf(useTraffic)).divide(new BigDecimal(1024*1024), 2, RoundingMode.HALF_UP);
            if (overageTraffic.compareTo(BigDecimal.ZERO) > 0) {
                AirLinkOrder lastMonthTrafficOrder = airLinkOrderService.getDeductOrderByPeriod(marketId, AirLinkDeductType.TRAFFIC_OVER.getCode(), period);
                if (lastMonthTrafficOrder == null) {
                    return airLinkOrderService.deductOverageTrafficFee(marketAirLinkSetting, overageTraffic, period, lastMonthUsage);
                }
            }
        }
        return false;
    }

    private void sendCancelActivePendingTerminalMessage(AirLinkTerminalActiveHistory activeHistory,
                                                        AirLinkTerminalActiveHistoryDeductMessage deductMessage,
                                                        boolean isExpire){
        if ((isExpire || deductMessage.getCloseActiveHistory()) && !deductMessage.getDeductLastMonth()){
            //取消激活所有pending状态的终端
            AirLinkTerminalCancelActiveMessage message = new AirLinkTerminalCancelActiveMessage(activeHistory.getMarketId());
            message.setResellerId(activeHistory.getResellerId());
            message.setActiveHistoryId(activeHistory.getId());
            message.setCancelTerminalActiveReason(deductMessage.getTerminalActiveFailReason());
            airLinkTerminalCacelActiveGateway.send(message);
        }
    }

    private void sendBalanceInsufficientAlarm(Long marketId, boolean isBalanceInsufficient){
        if (isBalanceInsufficient){
            Market market = marketService.get(marketId);
            sendMarketMonthDeductFailNotification(market);
            sendMarketMonthDeductFailEmail(market);
        }
    }

    private void sendMarketMonthDeductFailNotification(Market market){
        List<Long> receiveIds = new ArrayList<>();
        List<Long> marketReceiveIds = getMarketReceiveIds(market.getId(), RoleID.MARKET_ADMIN);
        List<Long> superReceiveIds = getMarketReceiveIds(SystemConstants.SUPER_MARKET_ID, RoleID.SUPER_ADMIN);
        receiveIds.addAll(marketReceiveIds);
        receiveIds.addAll(superReceiveIds);
        airLinkNotificationService.sendMarketMonthDeductFailNotification(receiveIds, market.getName());
    }

    private void sendMarketMonthDeductFailEmail(Market market) {
        Set<String> emails = new HashSet<>();
        //获取支持团队email
        List<String> supportTeamEmails = SystemPropertyHelper.getSupportTeamEmails();
        if(CollectionUtils.isNotEmpty(supportTeamEmails)) {
            emails.addAll(supportTeamEmails);
        }
        if (StringUtils.isNotBlank(market.getRecipientEmails())) {
            emails.addAll(List.of(market.getRecipientEmails().split(";")));
        }
        emailNotificationService.sendMarketMonthDeductFailEmail(
                emails,
                market.getName(),
                RequestLocaleHolder.getLocale());
    }

    private List<Long> getMarketReceiveIds(Long marketId, Long roleId) {
        User user = new User();
        user.setMarketIds(Sets.newHashSet(marketId));
        user.setRoleIds(Sets.newHashSet(roleId));
        return roleService.findMarketUserIds(user);
    }

}
