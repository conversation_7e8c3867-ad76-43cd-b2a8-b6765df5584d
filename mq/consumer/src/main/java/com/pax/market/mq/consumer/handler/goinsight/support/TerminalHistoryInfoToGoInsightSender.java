package com.pax.market.mq.consumer.handler.goinsight.support;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.hash.Hashing;
import com.pax.market.domain.insight.InsightTerminalDeviceInfo;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.vas.goinsight.GoInsightTerminalDeviceInfoFunc;
import com.pax.market.functional.vas.goinsight.converter.GoInsightDataIngestionRequestConverter;
import com.paxstore.integration.mq.message.TerminalSyncHistoricInfoMessage;
import com.zolon.saas.vas.func.goinsight.DataIngestionFunc;
import com.zolon.saas.vas.func.goinsight.dto.history.GoInsightTerminalDailyIngestionRequest;
import com.zolon.saas.vas.func.goinsight.dto.history.StoreTerminalAppUsageInfo;
import com.zolon.saas.vas.func.goinsight.dto.history.StoreTerminalDailyActivityInfo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class TerminalHistoryInfoToGoInsightSender {

    private static final StringRedisSerializer STR_SERIALIZER = new StringRedisSerializer();
    @Autowired
    private GoInsightTerminalDeviceInfoFunc goInsightTerminalDeviceInfoFunc;
    @Autowired
    private DataIngestionFunc dataIngestionFunc;
    private RedisConnectionFactory connectionFactory;
    @Autowired
    private GoInsightDataIngestionRequestConverter insightRequestConverter;

    public void send(TerminalSyncHistoricInfoMessage message) {
        //先检查批次内不能出现重复的 app+eventTime, 24h 内不能出现重复的
        int appUsageDataListSize = CollectionUtils.isEmpty(message.getHistoricAppUsageList())? 0:message.getHistoricAppUsageList().size();
        int activityDataListSize = CollectionUtils.isEmpty(message.getDailyActivityInfoList())? 0:message.getDailyActivityInfoList().size();
        log.debug("terminal-{} received appList:{}, activityList:{}", message.getTerminalId(), appUsageDataListSize, activityDataListSize);
        if(activityDataListSize == 0 && appUsageDataListSize == 0){
            return;
        }

        List<byte[]> dailyUniqueKeyList = Lists.newArrayListWithExpectedSize(appUsageDataListSize+activityDataListSize);
        List<StoreTerminalAppUsageInfo> finalAppInfosReq = Lists.newArrayListWithExpectedSize(appUsageDataListSize);
        if(appUsageDataListSize > 0) {
            List<byte[]> dailyAppUsageUniqueKeyList = Lists.newArrayListWithExpectedSize(appUsageDataListSize);
            Set<String> currentAuUniqueSet = Sets.newHashSetWithExpectedSize(appUsageDataListSize);
            //Set<byte[]> trafficAppRedisKeys = fetchKeys(DataType.TRAFFIC_APP, message.getTerminalId());
            List<StoreTerminalAppUsageInfo> appInfosReq = Lists.newArrayListWithExpectedSize(appUsageDataListSize);
            //检查过滤批次中是否存在重复以及超过30天的数据
            message.getHistoricAppUsageList().stream()
                    .filter(s -> Objects.nonNull(s) && Objects.nonNull(s.getEventTime()) && !isDataOver30Days(s.getEventTime()))
                    .forEach(s -> {
                        //先检查批次内不能出现重复的 app+eventTime, 24h 内不能出现重复的
                        String uniqueKey = hashCode(s.getEventTime(), s.getPackageName());;
                        byte[] redisKey = key(DataType.TRAFFIC_APP, message.getTerminalId(), uniqueKey);
                        //!currentAuUniqueSet.contains(uniqueKey)
                        if(!currentAuUniqueSet.contains(uniqueKey)){
                            currentAuUniqueSet.add(uniqueKey);
                            StoreTerminalAppUsageInfo t = new StoreTerminalAppUsageInfo();
                            BeanUtils.copyProperties(s, t);
                            t.setOperator(message.getOperator());
                            appInfosReq.add(t);
                            dailyAppUsageUniqueKeyList.add(redisKey);
                        }
                    });
            //检查redis中是否存在
            List<Object> checkResult = checkKeysExistence(dailyAppUsageUniqueKeyList);
            for(int i = 0; i < appInfosReq.size(); i++) {
                if ((Long) checkResult.get(i) == 1L) {
                    log.warn("HistoricAppUsageList exist already processed data in 24h:{}", message.getTerminalId());
                } else {
                    finalAppInfosReq.add(appInfosReq.get(i));
                    dailyUniqueKeyList.add(dailyAppUsageUniqueKeyList.get(i));
                }
            }
        }
        log.debug("terminal-{} appList after process:{}", message.getTerminalId(), finalAppInfosReq.size());

        List<StoreTerminalDailyActivityInfo> finalActivityInfosReq = Lists.newArrayListWithExpectedSize(activityDataListSize);
        if(activityDataListSize > 0) {
            List<byte[]> dailyActivityUniqueKeyList = Lists.newArrayListWithExpectedSize(activityDataListSize);
            Set<Long> currentAtUniqueSet = Sets.newHashSetWithExpectedSize(activityDataListSize);
            //Set<byte[]> activityDataRedisKeys = fetchKeys(DataType.DAILY_ACTIVITY, message.getTerminalId());
            List<StoreTerminalDailyActivityInfo> activityInfosReq = Lists.newArrayListWithExpectedSize(activityDataListSize);
            //检查过滤批次中是否存在重复以及超过30天的数据
            message.getDailyActivityInfoList().stream()
                    .filter(s -> Objects.nonNull(s) && Objects.nonNull(s.getEventTime()) && !isDataOver30Days(s.getEventTime()))
                    .forEach(s -> {
                        Long uniqueKey = s.getEventTime();
                        byte[] redisKey = key(DataType.DAILY_ACTIVITY, message.getTerminalId(), uniqueKey.toString());
                        //shouldProcess(activityDataRedisKeys, redisKey)
                        if(!currentAtUniqueSet.contains(uniqueKey)){
                            currentAtUniqueSet.add(uniqueKey);
                            StoreTerminalDailyActivityInfo t = new StoreTerminalDailyActivityInfo();
                            BeanUtils.copyProperties(s, t);
                            t.setOperator(message.getOperator());
                            activityInfosReq.add(t);
                            dailyActivityUniqueKeyList.add(redisKey);
                        }
                    });
            //检查redis中是否存在
            List<Object> checkResult = checkKeysExistence(dailyActivityUniqueKeyList);
            for(int i = 0; i < activityInfosReq.size(); i++) {
                if ((Long) checkResult.get(i) == 1L) {
                    log.warn("DailyActivityInfoList exist already processed data in 24h:{}", message.getTerminalId());
                } else {
                    finalActivityInfosReq.add(activityInfosReq.get(i));
                    dailyUniqueKeyList.add(dailyActivityUniqueKeyList.get(i));
                }
            }
        }
        log.debug("terminal-{} activityList after process:{}", message.getTerminalId(), finalActivityInfosReq.size());

        if(CollectionUtils.isEmpty(finalAppInfosReq) && CollectionUtils.isEmpty(finalActivityInfosReq)){
            return;
        }
        InsightTerminalDeviceInfo terminal = goInsightTerminalDeviceInfoFunc.getTerminal(message.getTerminalId());
        GoInsightTerminalDailyIngestionRequest req = insightRequestConverter.buildHistoryReqByInsightDeviceInfo.apply(terminal);
        req.setAppUsageInfos(finalAppInfosReq);
        req.setActivityInfos(finalActivityInfosReq);
        dataIngestionFunc.sendStoreTerminalDailyData(req);
        log.debug("terminal-{} send to insight success", message.getTerminalId());

        saveToRedis(dailyUniqueKeyList);
    }

    private boolean isDataOver30Days(Long eventTime) {
        if (Objects.isNull(eventTime)){
            return true;
        }
        if (!SystemPropertyHelper.getInsightTerminalHistoryDataTimeLimitSwitch()){
            return false;
        }
        long diff = System.currentTimeMillis() - eventTime;
        //drop the data if the latest date is over 2 days
        return diff > 30 * DateUtils.MILLIS_PER_DAY;
    }

    /*private boolean shouldProcess(Set<byte[]> keys, byte[] key) {
        if(CollectionUtils.isEmpty(keys)) {
            return true;
        }
        return !keys.contains(key);
    }

    private Set<byte[]> fetchKeys(DataType dataType, Long terminalId) {
        try (RedisConnection conn = getConnection()) {
            byte[] pattern = String.format("mq:data:insight:%s:%s:*", dataType.getCode(), terminalId).getBytes(StandardCharsets.UTF_8);
            return conn.keys(pattern);
        }
    }*/

    private void saveToRedis(List<byte[]> uniqueKeyList) {
        try (RedisConnection conn = getConnection()) {
            conn.openPipeline();
            for (byte[] t : uniqueKeyList) {
                conn.set(t,
                        Objects.requireNonNull(STR_SERIALIZER.serialize("1")),
                        Expiration.from(1, TimeUnit.DAYS),
                        RedisStringCommands.SetOption.SET_IF_ABSENT);
            }
            conn.closePipeline();
        }
    }

    private List<Object> checkKeysExistence(List<byte[]> keys) {
        try (RedisConnection conn = getConnection()) {
            conn.openPipeline();
            for (byte[] k : keys){
                conn.exists(k);
            }
            return conn.closePipeline();
        }
    }

    private byte[] key(DataType dataType, Long terminalId, String uniqueKey) {
        return String.format("mq:data:insight:%s:%s:%s", dataType.getCode(), terminalId, uniqueKey).getBytes(StandardCharsets.UTF_8);
    }

    protected RedisConnection getConnection() {
        if (connectionFactory == null) {
            connectionFactory = SpringContextHolder.getBean(RedisConnectionFactory.class);
        }
        return connectionFactory.getConnection();
    }

    private String hashCode(Long eventTime, String otherColumn) {
        return Hashing.crc32()
                .newHasher()
                .putLong(eventTime)
                .putString(otherColumn, StandardCharsets.UTF_8)
                .hash()
                .toString();
    }

    @Getter
    private enum DataType {
        TRAFFIC_APP("ta"),
        DAILY_ACTIVITY("da");

        DataType(String code) {
            this.code = code;
        }

        @EnumValue
        private final String code;
    }
}
