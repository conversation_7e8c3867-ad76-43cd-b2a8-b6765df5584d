/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.consumer.handler.sync;


import com.paxstore.integration.mq.message.TerminalSyncHardwareMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.pax.market.mq.consumer.handler.AbstractJsonMessageHandler;


@Slf4j
@Component
public class TerminalHardwareSyncHandler extends AbstractJsonMessageHandler<TerminalSyncHardwareMessage> {


    @Override
    protected void handleInternal(TerminalSyncHardwareMessage message) {
        //ignore, do nothing
        log.info("This message（'TerminalSyncHardwareMessage'） is obsolete, and the data is sent to detail func");
    }

}
