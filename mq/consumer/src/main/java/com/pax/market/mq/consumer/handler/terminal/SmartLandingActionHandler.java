/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.mq.consumer.handler.terminal;

import com.google.common.collect.Sets;
import com.pax.market.constants.ApkFileType;
import com.pax.market.constants.OsType;
import com.pax.market.constants.SmartLandingActionType;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.terminal.BaseTerminalEntity;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.terminal.SmartLandingActionMessage;
import com.pax.market.mq.smartlanding.contract.*;
import com.pax.market.mq.smartlanding.gateway.RemoveTerminalTaskGateway;
import com.pax.market.mq.smartlanding.gateway.TerminalCommandGateway;
import com.pax.market.mq.smartlanding.gateway.TerminalTaskGateway;
import com.pax.market.push.common.protocol.BizCommand;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.market.domain.service.terminal.TerminalProtocolService;
import com.paxstore.global.domain.service.terminal.TerminalPushHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

import static com.pax.market.constants.ProductTypeUtils.isAndroid;


/**
 * <AUTHOR>
 * @create 2023/10/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmartLandingActionHandler extends AbstractHandler<SmartLandingActionMessage> {

    private final ApkService apkService;
    private final TerminalRegistryService terminalRegistryService;
    private final ModelService modelService;
    private final TerminalProtocolService terminalProtocolService;
    private final RemoveTerminalTaskGateway removeTerminalTaskGateway;
    private final TerminalCommandGateway terminalCommandGateway;
    private final TerminalPushHistoryService pushHistoryService;
    private final TerminalTaskGateway terminalTaskGateway;

    @Override
    protected void handleInternal(SmartLandingActionMessage message) {
        //do
        switch (message.getActionType()) {
            case SmartLandingActionType.REMOVE_TERMINAL_TASK:
                sendRemoveTerminalTask(message);
                break;
            case SmartLandingActionType.TERMINAL_COMMAND:
                sendTerminalCommand(message);
                break;
            case SmartLandingActionType.DOWNLOAD_APP_TASK:
                sendDownloadAppTask(message);
                break;

        }


    }

    /**
     * Send remove terminal task to pax landing.
     */
    private void sendRemoveTerminalTask(SmartLandingActionMessage message) {
        if (LongUtils.isBlankOrNotPositive(message.getApkId())) {
            log.warn("SmartLandingActionHandler: apkId is null, apk id:{}", message.getApkId());
            return;
        }
        Apk apk = apkService.get(message.getApkId());
        if (apk == null) {
            log.warn("SmartLandingActionHandler: apkId is null, apk id:{}", message.getApkId());
            return;
        }
        if (StringUtils.equals(ApkFileType.ANDROID, apk.getApkFileType())) {
            log.warn("SmartLandingActionHandler: ApkFileType is ANDROID, apk id:{}", message.getApkId());
            return;
        }
        TerminalRegistry terminal = terminalRegistryService.get(message.getTerminalId());
        if (terminal == null) {
            log.warn("SmartLandingActionHandler: terminal is null, terminal id:{}", message.getTerminalId());
            return;
        }
        if (terminalProtocolService.get(terminal) > 0) {
            modelService.loadDetails(terminal.getModel());
            TerminalTaskDetail terminalTaskDetail = new TerminalTaskDetail(terminal.getSerialNo(), message.getTerminalActionId(), terminal.getModel().getFactory().getName(), terminal.getModel().getName());
            removeTerminalTaskGateway.send(new RemoveTerminalTaskMessage(apk.getApkFileType(), Sets.newHashSet(terminalTaskDetail)));
        }

    }

    /**
     * Send terminal command to pax landing.
     */
    private void sendTerminalCommand(SmartLandingActionMessage message) {
        BaseTerminalEntity<?> terminal;
        if (message.getCommand().equals(BizCommand.TERMINAL_DELETION)){
            terminal = BeanMapper.map(message.getTerminal(), BaseTerminalEntity.class);
        }else {
            terminal = terminalRegistryService.get(message.getTerminalId());
        }
        if (terminal == null) {
            log.warn("SmartLandingActionHandler: terminal is null, terminal id:{}", message.getTerminalId());
            return;
        }
        if (terminal.getModel() == null) {
            log.warn("SmartLandingActionHandler: terminal model is null, terminal id:{}", message.getTerminalId());
            return;
        }
        modelService.loadDetails(terminal.getModel());
        if (isAndroid(terminal.getProductType())) {
            log.warn("SmartLandingActionHandler: terminal model platform is ANDROID, terminal id:{}", message.getTerminalId());
            return;
        }
        if (StringUtils.isEmpty(terminal.getSerialNo()) || Objects.isNull(terminal.getModel())) {
            log.warn("SmartLandingActionHandler: terminal sn is null, terminal model is null, terminal id:{}", message.getTerminalId());
            return;
        }
        if (IntegerUtils.isBlankOrNotPositive(message.getTerminalProtocol()) && terminalProtocolService.get(terminal) <= 0) {
            log.warn("SmartLandingActionHandler: no terminal protocol, terminal id:{}", message.getTerminalId());
            return;
        }
        terminalCommandGateway.send(new TerminalCommandMessage(Collections.singleton(new TerminalDetail(terminal.getSerialNo(), terminal.getFactoryName(), terminal.getModelName())), message.getCommand().cmd));
        pushHistoryService.createPushHistories(message.getCommand().cmd, Collections.singleton(terminal.getId()));
    }

    private void sendDownloadAppTask(SmartLandingActionMessage message) {
        TerminalRegistry terminal = terminalRegistryService.get(message.getTerminalId());
        if (terminal == null) {
            log.warn("SmartLandingActionHandler: terminal is null, terminal id:{}", message.getTerminalId());
            return;
        }
        modelService.loadDetails(terminal.getModel());
        Apk apk = apkService.get(message.getApkId());
        if (apk == null) {
            log.warn("SmartLandingActionHandler: apkId is null, apk id:{}", message.getApkId());
            return;
        }
        if (terminalProtocolService.get(terminal) > 0) {
            TerminalDetail terminalDetail = new TerminalDetail(terminal.getSerialNo(), terminal.getFactoryName(), terminal.getModelName());
            terminalTaskGateway.send(new TerminalTaskMessage(terminalDetail, apk.getApkFileType(), BizCommand.DOWNLOAD_APP.cmd));
            pushHistoryService.createPushHistories(BizCommand.DOWNLOAD_APP.cmd, Collections.singleton(terminal.getId()));
        }
    }
}
