package com.pax.market.mq.consumer.geofence.config;

import com.pax.market.mq.consumer.geofence.client.GeolocationCalibrationClient;
import com.pax.market.mq.consumer.geofence.client.impl.AwsGeolocationCalibrationClientImpl;
import com.pax.support.aws.credential.AutoReloadableAwsCredentialsProvider;
import com.pax.support.aws.credential.AwsCredentialLoaderProvider;
import com.pax.support.config.props.AwsConfigProps;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.iotwireless.IotWirelessClient;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
@Conditional(GeolocationCalibrationConditions.AwsIotWirelessClient.class)
@Import({AwsConfigProps.class})
public class AwsIotWirelessClientConfig {

    protected static final String DEFAULT_REGION = "us-east-1";

    private static final Duration DEFAULT_CONN_TIMEOUT = Duration.ofSeconds(2);
    public static final int DEFAULT_MAX_CONNECTIONS = 80;

    private final AwsConfigProps awsConfigProps;

    @Value("${aws.iot-wireless-region}")
    private String region;

    @Bean
    public IotWirelessClient iotWirelessClient() {
        var awsCredentialProvider = new AutoReloadableAwsCredentialsProvider(AwsCredentialLoaderProvider
                .of(awsConfigProps).get());
        return IotWirelessClient
                .builder()
                .region(Region.of(StringUtils.isNotBlank(region) ? region : DEFAULT_REGION))
                .credentialsProvider(awsCredentialProvider)
                .httpClientBuilder(ApacheHttpClient.builder()
                        .connectionTimeout(DEFAULT_CONN_TIMEOUT)
                        .connectionMaxIdleTime(Duration.ofSeconds(5))
                        .maxConnections(DEFAULT_MAX_CONNECTIONS))
                .build();
    }

    @Bean
    public GeolocationCalibrationClient paxIotWirelessClient() {
        return new AwsGeolocationCalibrationClientImpl(iotWirelessClient());
    }
}
