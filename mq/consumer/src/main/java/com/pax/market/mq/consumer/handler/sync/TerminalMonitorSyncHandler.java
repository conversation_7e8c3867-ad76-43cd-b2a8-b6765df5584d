/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.mq.consumer.handler.sync;

import com.pax.market.domain.entity.market.terminal.TerminalMonitor;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.market.domain.service.terminal.TerminalMonitorService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.mq.consumer.handler.AbstractJsonMessageHandler;
import com.paxstore.integration.mq.message.TerminalSyncMonitorMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class TerminalMonitorSyncHandler extends AbstractJsonMessageHandler<TerminalSyncMonitorMessage> {
    @Autowired
    private TerminalMonitorService terminalMonitorService;
    @Autowired
    private MarketTerminalService marketTerminalService;
    @Autowired
    private TerminalRegistryService terminalRegistryService;

    @Override
    protected void handleInternal(TerminalSyncMonitorMessage message) {
        TerminalMonitor terminalMonitor = new TerminalMonitor();
        terminalMonitor.setTerminalId(message.getTerminalId());
        terminalMonitor.setCpuRatio(message.getCpuRatio());
        terminalMonitor.setRamUsed(message.getRamUsed());
        terminalMonitor.setStorageUsed(message.getStorageUsed());
        terminalMonitor.setBattery(message.getBattery());
        terminalMonitor.setNetwork(message.getNetwork());
        terminalMonitor.setScreenLock(message.getScreenLock());
        terminalMonitor.setCharging(message.getCharging());
        terminalMonitor.setSyncDate(new Date());

        TerminalMonitor existTerminalMonitor = terminalMonitorService.getByTerminal(message.getTerminalId());
        if (existTerminalMonitor == null) {
            terminalMonitorService.createTerminalMonitor(terminalMonitor);
        } else {
            terminalMonitor.setId(existTerminalMonitor.getId());
            terminalMonitorService.updateTerminalMonitor(terminalMonitor);
        }
        //上送信息中包含network，上送的network和已有的不一样需要更新
        TerminalRegistry terminalRegistry = terminalRegistryService.get(message.getTerminalId());
        if (terminalRegistry != null && StringUtils.isNotEmpty(message.getNetwork())
                && (existTerminalMonitor == null || !StringUtils.equalsIgnoreCase(existTerminalMonitor.getNetwork(), message.getNetwork()))) {
            marketTerminalService.updateTerminalNetwork(message.getTerminalId(), message.getNetwork());
        }
    }
}
