package com.pax.market.mq.consumer.handler.cleardata;

import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.mq.contract.cleardata.RolePhysicalDeleteMessage;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.producer.gateway.cleardata.RolePhysicalDeleteGateway;
import com.paxstore.global.domain.service.cleardata.RolePhysicalDeleteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RolePhysicalDeleteHandler extends AbstractHandler<RolePhysicalDeleteMessage> {
    private final RolePhysicalDeleteGateway rolePhysicalDeleteGateway;
    private final RolePhysicalDeleteService rolePhysicalDeleteService;

    @Override
    protected void handleInternal(RolePhysicalDeleteMessage message) {
        String cacheKey = String.format("distributionLock:rolePhysicalDelete:%d-%d-%d", message.getMarketId(), message.getResellerId(), message.getRoleId());
        RedisUtils.tryLock(cacheKey, "clear data error", () -> {
            if (LongUtils.isNotBlankAndPositive(message.getMarketId()) && LongUtils.isBlankOrNotPositive(message.getRoleId())) {
                rolePhysicalDeleteService.findDeletedRoleIds(message.getMarketId()).parallelStream().forEach(roleId -> rolePhysicalDeleteGateway.send(new RolePhysicalDeleteMessage(null, null, roleId)));
                return;
            }
            if (LongUtils.isNotBlankAndPositive(message.getResellerId())) {
                rolePhysicalDeleteService.findDeletedRolesByResellerId(message.getResellerId()).parallelStream().forEach(roleId -> rolePhysicalDeleteGateway.send(new RolePhysicalDeleteMessage(null, null, roleId)));
                return;
            }
            if (LongUtils.isNotBlankAndPositive(message.getRoleId())) {
                rolePhysicalDeleteService.deleteRole(message.getRoleId());
                rolePhysicalDeleteService.deleteUserRole(message.getRoleId());
                rolePhysicalDeleteService.deleteRolePrivilege(message.getRoleId());
            }
        });
    }
}
