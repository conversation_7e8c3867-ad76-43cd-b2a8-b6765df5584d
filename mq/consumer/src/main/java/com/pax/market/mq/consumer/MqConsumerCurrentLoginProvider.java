/**
 * ******************************************************************************** COPYRIGHT PAX
 * TECHNOLOGY, Inc. PROPRIETARY INFORMATION This software is supplied under the terms of a license
 * agreement or nondisclosure agreement with PAX Technology, Inc. and may not be copied or disclosed
 * except in accordance with the terms in that agreement.
 * 
 * Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.mq.consumer;

import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.DeveloperInfo;
import com.pax.market.dto.ThirdPartySysInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.app.AppInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.terminal.TerminalInfo;
import com.pax.market.framework.common.CurrentLoginProvider;

/**
 * 
 *
 * <AUTHOR>
 * @date Apr 11, 2017
 */
public class MqConsumerCurrentLoginProvider implements CurrentLoginProvider {

    private static final ThreadLocal<MarketInfo> currentMarket = new ThreadLocal<>();
    private static final ThreadLocal<UserInfo> currentUser = new ThreadLocal<>();

    public void setCurrentMarket(Long marketId) {
        MarketInfo marketInfo = new MarketInfo();
        marketInfo.setId(marketId);
        currentMarket.set(marketInfo);
    }
    
    public void setCurrentMarket(MarketInfo marketInfo) {
    	currentMarket.set(marketInfo);
    }

    public void setCurrentUser(Long userId) {
        UserInfo user = new UserInfo();
        user.setId(userId != null ? userId : SystemConstants.UPDATED_BY_SYSTEM_ID);
        currentUser.set(user);
    }
    
    public void setCurrentUser(UserInfo user) {
    	currentUser.set(user);
    }

    public void clear() {
        currentMarket.remove();
        currentUser.remove();
    }

    @Override
    public MarketInfo getCurrentMarketInfo() {
        if (currentMarket.get() == null) {
            setCurrentMarket(new MarketInfo(0L));
        }
        return currentMarket.get();
    }

    @Override
    public UserInfo getCurrentUserInfo() {
        if (currentUser.get() == null || currentUser.get().getId() == null) {
            setCurrentUser(new UserInfo(SystemConstants.UPDATED_BY_SYSTEM_ID));
        }
        return currentUser.get();
    }

    @Override
    public TerminalInfo getCurrentTerminalInfo() {
        return null;
    }

    @Override
    public AppInfo getCurrentAppInfo() {
        return null;
    }

	@Override
	public ThirdPartySysInfo getCurrentThirdPartySysInfo() {
		return null;
	}

    @Override
    public DeveloperInfo getCurrentDeveloperInfo() {
        UserInfo currentUserInfo = getCurrentUserInfo();
        if (currentUserInfo!=null) {
            return currentUserInfo.getCurrentDeveloper();
        }
        return null;
    }

    @Override
    public void initUser(Long userId) {
        UserInfo user = new UserInfo();
        user.setId(userId != null ? userId : SystemConstants.UPDATED_BY_SYSTEM_ID);
        currentUser.set(user);
    }

    @Override
    public void clearInitializedUser() {
        currentUser.remove();
    }
}
