package com.pax.market.mq.consumer.handler.airlink;

import com.pax.core.json.JsonMapper;
import com.pax.market.constants.airlink.AirLinkTerminalProfileStatus;
import com.pax.market.constants.airlink.AirLinkTerminalStatus;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkEstate;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.support.AirLinkTerminalSupport;
import com.pax.market.mq.consumer.handler.AbstractJsonMessageHandler;
import com.pax.market.mq.contract.airlink.AirLinkEventCallBackMessage;
import com.pax.market.mq.contract.billing.SyncAirLinkUsageDetailMessage;
import com.pax.market.mq.producer.gateway.billing.SyncData2BillingGateway;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.global.domain.service.vas.airlink.AirLinkEstateService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalProfileService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * author mengxiaoxian
 * Date   2025/5/15 15:58
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AirLinkEventCallBackHandler extends AbstractJsonMessageHandler<AirLinkEventCallBackMessage>{

    private final AirLinkEstateService airLinkEstateService;
    private final AirLinkTerminalService airLinkTerminalService;
    private final AirLinkTerminalSupport airLinkTerminalSupport;
    private final AirLinkTerminalProfileService airLinkTerminalProfileService;
    private final ResellerService resellerService;
    private final SyncData2BillingGateway syncData2BillingGateway;

    @Override
    protected void handleInternal(AirLinkEventCallBackMessage message) {
        String imei = message.getImei();
        AirLinkEstate estate = airLinkEstateService.getByImei(imei);
        if (Objects.isNull(estate)){
            log.warn("IMEI {} not found in estate", message.getImei());
            return;
        }
        Long marketId = estate.getMarketId();
        PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
        AirLinkTerminal airLinkTerminal = airLinkTerminalService.getNotActiveTerminalByMarketAndImei(marketId, imei);
        if (Objects.nonNull(airLinkTerminal)) {//终端原来是P或I,终端激活
            airLinkTerminal.setActiveTime(new Date());
            airLinkTerminal.setEid(message.getEid());
            airLinkTerminal.setStatus(AirLinkTerminalStatus.USE);//db里面查出来的状态可能是P, I, U，都改成U
            airLinkTerminal.setUpdatedDate(new Date());
            List<AirLinkTerminalProfile> cardProfiles = airLinkTerminalSupport.getCardProfiles(airLinkTerminal.getId(), imei);
            if (CollectionUtils.isNotEmpty(cardProfiles)){
                airLinkTerminalService.addAirLinkTerminalProfiles(airLinkTerminal, cardProfiles, null);
                sendAirLinkTerminalUsage(airLinkTerminal);
            }
        }else{
            airLinkTerminal = airLinkTerminalService.getActiveTerminalByMarketAndImei(marketId, imei);//终端已经是激活状态，
            if(Objects.nonNull(airLinkTerminal)) {
                boolean eidChanged = StringUtils.equals(message.getEid(), airLinkTerminal.getEid());
                if(eidChanged) {
                    airLinkTerminal.setEid(message.getEid());
                    airLinkTerminal.setUpdatedDate(new Date());
                }
                List<AirLinkTerminalProfile> airLinkTerminalProfilesInDb = airLinkTerminalProfileService.getProfileList(airLinkTerminal.getId());
                if(StringUtils.isNotBlank(message.getCurrentIccid()) && CollectionUtils.isNotEmpty(airLinkTerminalProfilesInDb)) {
                    if(airLinkTerminalProfilesInDb.stream()
                            .anyMatch(profileIndb -> StringUtils.equals(profileIndb.getIccid(), message.getCurrentIccid()) && StringUtils.equals(profileIndb.getStatus(),AirLinkTerminalProfileStatus.USE ))){
                        //当消息中的当前在用的profile iccic在db中的profile表存在，并且db中的状态是USE状态，忽略消息
                        return;
                    }
                }
                List<AirLinkTerminalProfile> cardProfiles = airLinkTerminalSupport.getCardProfiles(airLinkTerminal.getId(), imei);
                if (CollectionUtils.isNotEmpty(cardProfiles)){
                    if(CollectionUtils.isNotEmpty(airLinkTerminalProfilesInDb)) {
                        List<AirLinkTerminalProfile> profilesNeed2Delete = getProfiesNeedDelete(cardProfiles, airLinkTerminalProfilesInDb);
                        List<Long> profileIds2Delete = null;
                        if(CollectionUtils.isNotEmpty(profilesNeed2Delete)) {
                            profileIds2Delete = profilesNeed2Delete.stream().map(AirLinkTerminalProfile::getId).collect(Collectors.toList());
                        }
                        List<AirLinkTerminalProfile> switchProfile = airLinkTerminalProfilesInDb.stream().filter(item -> item.getStatus().equals(AirLinkTerminalProfileStatus.SWITCH)).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(switchProfile)) {
                            airLinkTerminalService.addAirLinkTerminalProfiles(eidChanged?airLinkTerminal:null, cardProfiles, profileIds2Delete);
                        }else{
                            if(switchProfile.size() > 1) {
                                log.error("Find more than 1 switch iccids for imei {}", message.getImei());
                            }else{
                                String switchIccid = switchProfile.get(0).getIccid();
                                if(cardProfiles.stream().anyMatch(item -> item.getIccid().equals(switchIccid) && item.getStatus().equals(AirLinkTerminalProfileStatus.USE))){
                                    airLinkTerminalService.addAirLinkTerminalProfiles(eidChanged?airLinkTerminal:null, cardProfiles, profileIds2Delete);
                                }
                            }
                        }
                    }
                }
            }
        }

        PaxDynamicDsThreadLocal.removePreferenceMarketId();
    }

    /**
     * 如果一个运营商的profile在db里面有但是从过API获取的profile里面没有，那这个profile需要删除
     * @param profilesFromRemoteApi
     * @param profilesInDb
     * @return
     */
    private List<AirLinkTerminalProfile> getProfiesNeedDelete(List<AirLinkTerminalProfile> profilesFromRemoteApi, List<AirLinkTerminalProfile> profilesInDb) {
        List<AirLinkTerminalProfile> profilesToDelete = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(profilesInDb) && CollectionUtils.isNotEmpty(profilesFromRemoteApi)) {
            for(AirLinkTerminalProfile profileInDb:profilesInDb) {
                if(!profilesFromRemoteApi.stream().anyMatch(item -> StringUtils.equals(profileInDb.getOperator(), item.getOperator()))){
                    profilesToDelete.add(profileInDb);
                }
            }
        }
        return profilesToDelete;
    }

    private void sendAirLinkTerminalUsage(AirLinkTerminal airLinkTerminal) {
        Reseller reseller = resellerService.get(airLinkTerminal.getResellerId());
        SyncAirLinkUsageDetailMessage message = SyncAirLinkUsageDetailMessage.builder()
                .airlinkTerminalId(airLinkTerminal.getId())
                .dataUsage(BigDecimal.ZERO)
                .esimActivateTime(airLinkTerminal.getActiveTime())
                .resellerId(airLinkTerminal.getResellerId())
                .resellerName(Objects.nonNull(reseller) ? reseller.getName() : null)
                .syncTime(new Date())
                .resellerParentIds(Objects.nonNull(reseller) ? reseller.getParentIds() : null)
                .serialNo(airLinkTerminal.getSerialNo())
                .imei(airLinkTerminal.getImei())
                .marketId(airLinkTerminal.getMarketId())
                .organization(resellerService.getResellerOrganization(reseller))
                .build();
        syncData2BillingGateway.sendAirLinkTerminalUsage(JsonMapper.toJsonString(message));
    }
}
