/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2019. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.mq.consumer.handler.terminal;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pax.market.constants.CacheNames;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.mq.consumer.handler.AbstractJsonMessageHandler;
import com.paxstore.integration.mq.message.TerminalSyncLastAccessMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * The type Terminal last access handler.
 */
@Component
@Slf4j
public class TerminalLastAccessHandler extends AbstractJsonMessageHandler<TerminalSyncLastAccessMessage> {

    private static final String KEY = "TLA";
    private final Map<String, Set<Long>> terminalIdsMap = new ConcurrentHashMap<>();

    @Scheduled(fixedRate = 5000) // 每隔5秒钟执行一次
    public void process() {
        Set<Long> terminalIds = terminalIdsMap.remove(KEY);
        if (Collections3.isEmpty(terminalIds)) {
            return;
        }
        Map<byte[], byte[]> terminalLastAccessTimeMap = Maps.newHashMapWithExpectedSize(terminalIds.size());
        byte[] date = RedisUtils.serializeObj(new Date());
        for (Long terminalId : terminalIds) {
            terminalLastAccessTimeMap.put(RedisUtils.serializeStr(String.valueOf(terminalId)), date);
        }
        RedisUtils.call(redis -> redis.hMSet(RedisUtils.serializeStr(CacheNames.TERMINAL_ACCESS_TIME_CACHE), terminalLastAccessTimeMap));
    }

    @Override
    protected void handleInternal(TerminalSyncLastAccessMessage message) {
        Set<Long> terminalIds = terminalIdsMap.computeIfAbsent(KEY, k -> Sets.newHashSet());
        terminalIds.add(message.getTerminalId());
    }
}
