package com.pax.market.mq.consumer.handler.push;

import com.google.common.collect.Sets;
import com.pax.market.constants.TerminalOnlineStatus;
import com.pax.market.dto.terminal.TerminalOnlineStatusInfo;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.paxstore.market.domain.service.terminal.TerminalOnlineStatusService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.pax.market.mq.consumer.handler.AbstractPushAwareHandler;
import com.pax.market.mq.contract.push.TerminalOnlineStatusChangeMessage;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.support.dynamic.datasource.tools.PaxDsUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class PushEventHandler extends AbstractPushAwareHandler<TerminalOnlineStatusChangeMessage> {
    private final TerminalRegistryService terminalRegistryService;
    private final TerminalOnlineStatusService terminalOnlineStatusService;

    @Override
    protected void handleBatch(List<TerminalOnlineStatusChangeMessage> messages) {
        Map<String, UpdateOnlineStatus> statusMap = new HashMap<>();
        Set<Long> terminalIds = messages.stream().map(TerminalOnlineStatusChangeMessage::getTid).collect(Collectors.toSet());
        Map<Long, Long> terminalId2MarketIdMapping = terminalRegistryService.getTerminalMarketMapping(terminalIds);
        Set<Long> tids = Sets.newHashSetWithExpectedSize(messages.size());
        for (int i = messages.size() - 1; i >= 0; i--) {
            TerminalOnlineStatusChangeMessage msg = messages.get(i);
            if (tids.contains(msg.getTid())) {
                continue;
            }
            tids.add(msg.getTid());
            Long marketId = terminalId2MarketIdMapping.get(msg.getTid());
            if (Objects.nonNull(marketId)) {
                String dataSourceName = PaxDsUtils.getDataSourceNameByMarketId(marketId);
                UpdateOnlineStatus updateOnlineStatus = statusMap.get(dataSourceName);
                if (updateOnlineStatus == null) {
                    updateOnlineStatus = new UpdateOnlineStatus();
                }
                if (msg.getOl() == 1) {
                    updateOnlineStatus.addOnlineTids(msg.getTid());
                } else {
                    updateOnlineStatus.addOfflineTids(msg.getTid());
                }
                statusMap.put(dataSourceName, updateOnlineStatus);
            }
        }
        statusMap.forEach(this::updateTerminalOnlineStatus);
    }

    private void updateTerminalOnlineStatus(String dataSourceName, UpdateOnlineStatus temp) {
        try {
            PaxDynamicDsThreadLocal.setPreferenceDatasource(dataSourceName);
            if (!temp.onlineTids.isEmpty()) {
                batchUpdateTerminalOnlineStatus(temp.onlineTids, TerminalOnlineStatus.ONLINE);
            }
            if (!temp.offlineTids.isEmpty()) {
                batchUpdateTerminalOnlineStatus(temp.offlineTids, TerminalOnlineStatus.OFFLINE);
            }
        } finally {
            PaxDynamicDsThreadLocal.removePreferenceDatasource();
        }
    }

    private void batchUpdateTerminalOnlineStatus(Set<Long> terminalIds, Integer onlineStatus) {
        List<TerminalOnlineStatusInfo> terminalOnlineStatusInfoList = terminalOnlineStatusService.findTerminalOnlineStatusInfoList(terminalIds);
        Set<Long> existTerminalIds = terminalOnlineStatusInfoList.parallelStream().map(TerminalOnlineStatusInfo::getId).collect(Collectors.toSet());
        //数据库中不存在的需要插入在线状态
        Set<Long> createList = terminalIds.parallelStream().filter(terminalId -> !existTerminalIds.contains(terminalId)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(createList)) {
            terminalOnlineStatusService.batchCreateTerminalOnlineStatus(createList, onlineStatus);
        }
        //在线状态不一样的需要更新在线状态
        Set<Long> updateList = terminalOnlineStatusInfoList.parallelStream().filter(each -> !IntegerUtils.equals(each.getOnlineStatus(), onlineStatus)).map(TerminalOnlineStatusInfo::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(updateList)) {
            terminalOnlineStatusService.batchUpdateTerminalOnlineStatus(updateList, onlineStatus);
        }
    }

    @Getter
    @Setter
    static class UpdateOnlineStatus {
        private Set<Long> onlineTids = Sets.newHashSetWithExpectedSize(20);
        private Set<Long> offlineTids = Sets.newHashSetWithExpectedSize(20);

        void addOnlineTids(Long tid) {
            onlineTids.add(tid);
        }

        void addOfflineTids(Long tid) {
            offlineTids.add(tid);
        }
    }
}
