/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.mq.consumer.config;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import com.mpush.client.PushService;
import com.mpush.client.callback.ClearUserPreviousStatusCallback;
import com.mpush.client.callback.PrePushCommandCallback;
import com.mpush.client.service.impl.DefaultPushServiceImpl;
import com.mpush.diagnosis.connect.DiagnosisClient;
import com.mpush.diagnosis.connect.DiagnosisConnClient;
import com.pax.market.framework.common.config.condition.MessageEngineConditions;
import com.pax.market.mq.consumer.manager.PushClientShutdownManager;
import com.pax.market.push.common.protocol.BizCommand;
import com.pax.support.config.props.StoreDeployInfoConfigProps;
import com.pax.vas.config.mp.Mp8Config;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by fanjun on 2017/7/13.
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "push-client-cfg")
@DependsOn()
public class PushClientConfig extends Mp8Config implements ApplicationContextAware {

    @Value("${push-config.push-request-timeout:60000}")
    private int pushRequestTimeout = 60000;


    private List<BizCommand> commandsNeedRefreshOnlineStatus;

    private boolean ignoreSendSameOnlineStatus;

    private Set<BizCommand> nonPersistCommands;

    private int persistCommandExpireTime = 3600;

    private ApplicationContext applicationContext;

    @Autowired
    private StoreDeployInfoConfigProps deployInfoConfigProps;

    @Bean
    public PrePushCommandCallback prePushCommandCallback(){
        Set<Byte> commandsNeedRefreshOnlineStatusSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(commandsNeedRefreshOnlineStatus)) {
            commandsNeedRefreshOnlineStatus.forEach(cmd -> commandsNeedRefreshOnlineStatusSet.add(cmd.cmd)
            );
        }
        return new ClearUserPreviousStatusCallback(ignoreSendSameOnlineStatus,commandsNeedRefreshOnlineStatusSet);
    }

    @Bean
    @Conditional({MessageEngineConditions.KafkaEnabledCondition.class})
    public PushService pushService(PrePushCommandCallback prePushCommandCallback){
        return new DefaultPushServiceImpl(pushRequestTimeout, prePushCommandCallback);
    }

    @Bean(initMethod = "startConnClients")
    @Conditional({MessageEngineConditions.KafkaEnabledCondition.class})
    public DiagnosisClient diagnosisClient(){
        return DiagnosisConnClient.I;
    }

    @Bean(initMethod = "init", destroyMethod = "destroy")
    public PushClientShutdownManager pushClientShutdownManager(){
        return new PushClientShutdownManager();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

}
