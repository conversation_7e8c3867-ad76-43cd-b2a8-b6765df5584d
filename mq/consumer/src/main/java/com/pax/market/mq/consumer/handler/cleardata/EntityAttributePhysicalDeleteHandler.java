package com.pax.market.mq.consumer.handler.cleardata;

import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.cleardata.EntityAttributePhysicalDeleteMessage;
import com.paxstore.market.domain.service.cleardata.EntityAttributePhysicalDeleteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EntityAttributePhysicalDeleteHandler extends AbstractHandler<EntityAttributePhysicalDeleteMessage> {
    private final EntityAttributePhysicalDeleteService entityAttributePhysicalDeleteService;

    @Override
    protected void handleInternal(EntityAttributePhysicalDeleteMessage message) {
        String cacheKey = String.format("distributionLock:entityAttributePhysicalDelete:%d", message.getEntityAttributeId());
        RedisUtils.tryLock(cacheKey, "clear data error", () -> {
            entityAttributePhysicalDeleteService.deleteEntityAttribute(message.getEntityAttributeId());
            entityAttributePhysicalDeleteService.deleteEntityAttributeLabel(message.getEntityAttributeId());
            entityAttributePhysicalDeleteService.deleteEntityAttributeValue(message.getEntityAttributeId());
        });
    }
}
