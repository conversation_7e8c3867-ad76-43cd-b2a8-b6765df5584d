package com.pax.market.mq.consumer.handler.billing;

import com.pax.market.billing.MarketBillingPriceSettingService;
import com.pax.market.dto.request.market.billing.MarketBillingPriceSettingRequest;
import com.pax.market.functional.billing.MarketBillingSettingFuncServiceV2;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.billing.PriceSettingMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 8.8
 */
@Component
@RequiredArgsConstructor
public class PriceSettingHandler extends AbstractHandler<PriceSettingMessage> {

    private final MarketBillingPriceSettingService marketBillingPriceSettingService;
    private final MarketBillingSettingFuncServiceV2 marketBillingSettingFuncService;

    @Override
    protected void handleInternal(PriceSettingMessage message) {
        String serviceType = message.getServiceType();
        Long marketId = message.getMarketId();
        MarketBillingPriceSettingRequest request = new MarketBillingPriceSettingRequest();
        request.setMarketId(marketId);
        request.setServiceType(serviceType);
        request.setCharge(true);
        request.setDefaultPrice(true);
        marketBillingSettingFuncService.updateMarketBillingPriceSetting(request);
    }


}
