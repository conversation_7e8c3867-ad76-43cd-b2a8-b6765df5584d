/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.consumer.handler.sync;

import com.google.common.collect.Sets;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.market.terminal.ResellerInstalledApk;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.sync.AppDetail;
import com.pax.market.mq.contract.sync.ResellerAppSyncMessage;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.market.domain.service.terminal.ResellerInstalledApkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ResellerAppSyncHandler extends AbstractHandler<ResellerAppSyncMessage> {
    private final Map<String, Set<AppDetail>> resellerAppListMap = new ConcurrentHashMap<>();
    @Autowired
    private ResellerInstalledApkService resellerInstalledApkService;

    @Scheduled(fixedRate = 60000) // 每隔1分钟执行一次
    public void process() {
        Set<String> keys = resellerAppListMap.keySet();
        if (Collections3.isEmpty(keys)) {
            return;
        }

        for (String key : keys) {
            try {
                String[] marketIdAndResellerId = key.split(SystemConstants.CONNECTOR);
                Set<AppDetail> resellerApps = resellerAppListMap.remove(key);
                if (Collections3.isEmpty(resellerApps)) {
                    continue;
                }
                PaxDynamicDsThreadLocal.setPreferenceMarketId(Long.valueOf(marketIdAndResellerId[0]));
                Long resellerId = Long.valueOf(marketIdAndResellerId[1]);
                List<ResellerInstalledApk> existResellerInstalledApks = resellerInstalledApkService.findResellerInstalledApkList(resellerId);
                resellerInstalledApkService.createResellerInstalledApks(resellerId, existResellerInstalledApks, resellerApps);
            } catch (Exception e) {
                logger.error("createResellerInstalledApks error", e);
            } finally {
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
    }

    @Override
    protected void handleInternal(ResellerAppSyncMessage message) {
        logger.debug(JsonMapper.toJsonString(message.getAppList()));
        if (Collections3.isEmpty(message.getAppList())) {
            return;
        }
        Set<AppDetail> resellerAppList = resellerAppListMap.computeIfAbsent(String.format("%s-%s", message.getMarketId(), message.getResellerId()), k -> Sets.newHashSet());
        resellerAppList.addAll(message.getAppList());
    }
}
