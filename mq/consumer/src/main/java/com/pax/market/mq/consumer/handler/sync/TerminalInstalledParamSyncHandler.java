/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.mq.consumer.handler.sync;

import com.pax.core.json.JsonMapper;
import com.pax.market.constants.ApkType;
import com.pax.market.constants.ParamSource;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.market.pushtask.TerminalApkParamHistory;
import com.pax.market.domain.entity.market.pushtask.TerminalLastApkParam;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.mq.consumer.handler.AbstractJsonMessageHandler;
import com.pax.market.mq.contract.sync.TerminalApkParamHistoryMessage;
import com.pax.market.mq.producer.gateway.sync.TerminalApkParamHistoryGateway;
import com.paxstore.global.domain.service.app.ApkParamTemplateService;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.integration.mq.message.TerminalSyncInstalledParamMessage;
import com.paxstore.market.domain.service.terminal.TerminalApkParamHistoryService;
import com.paxstore.market.domain.service.terminal.TerminalLastApkParamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Terminal installed param sync handler.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TerminalInstalledParamSyncHandler extends AbstractJsonMessageHandler<TerminalSyncInstalledParamMessage> {

    private final TerminalRegistryService terminalRegistryService;
    private final ApkService apkService;
    private final CurrentLoginProvider currentLoginProvider;
    private final TerminalApkParamHistoryGateway terminalApkParamHistoryGateway;
    private final ApkParamTemplateService apkParamTemplateService;
    private final TerminalApkParamHistoryService terminalApkParamHistoryService;
    private final TerminalLastApkParamService terminalLastApkParamService;

    @Override
    protected void handleInternal(TerminalSyncInstalledParamMessage message) {
        if (message.getParamChangedList() == null && message.getParamChangedList().isEmpty()) {
            return;
        }
        if (currentLoginProvider.getCurrentMarketInfo().getAllowUploadLocalParameter()) {
            return;
        }
        if (terminalRegistryService.get(message.getTerminalId()) == null) {
            return;
        }
        Apk apk = apkService.get(message.getApkId());
        if (apk == null) {
            return;
        }
        if (!ApkType.PARAMETER_APP.equals(apk.getApkType())) {
            return;
        }
        ApkParamTemplate apkParamTemplate = apkParamTemplateService.getParamTemplate(apk.getId(), message.getParamTemplateName());
        if (apkParamTemplate == null) {
            return;
        }
        TerminalApkParamHistoryMessage terminalApkParamHistoryMessage = new TerminalApkParamHistoryMessage();
        terminalApkParamHistoryMessage.setTerminalId(message.getTerminalId());
        terminalApkParamHistoryMessage.setAppId(message.getAppId());
        terminalApkParamHistoryMessage.setApkId(message.getApkId());
        terminalApkParamHistoryMessage.setParamTemplateName(message.getParamTemplateName());
        terminalApkParamHistoryMessage.setFrom(ParamSource.CLIENT);

        TerminalApkParamHistory latestTerminalApkParamHistory = terminalApkParamHistoryService.getLatestTerminalApkParamHistory(message.getTerminalId(), message.getAppId(), message.getParamTemplateName());
        if (latestTerminalApkParamHistory != null) {
            terminalApkParamHistoryMessage.setActionType(latestTerminalApkParamHistory.getActionType());
            terminalApkParamHistoryMessage.setReferenceId(latestTerminalApkParamHistory.getReferenceId());
            terminalApkParamHistoryMessage.setParamVariables(latestTerminalApkParamHistory.getParamVariables());
            terminalApkParamHistoryMessage.setConvertedParamVariables(latestTerminalApkParamHistory.getConvertedParamVariables());
            terminalApkParamHistoryMessage.setLocalParam(mergeLocalParam(latestTerminalApkParamHistory.getLocalParam(), message.getParamChangedList()));
        } else {
            TerminalLastApkParam terminalLastApkParam = terminalLastApkParamService.getTerminalLastApkParam(message.getTerminalId(), message.getApkId(), message.getParamTemplateName());
            if (terminalLastApkParam != null) {
                terminalApkParamHistoryMessage.setActionType(terminalLastApkParam.getActionType());
                terminalApkParamHistoryMessage.setReferenceId(terminalLastApkParam.getReferenceId());
                terminalApkParamHistoryMessage.setParamVariables(terminalLastApkParam.getParamVariables());
                terminalApkParamHistoryMessage.setConvertedParamVariables(terminalLastApkParam.getConvertedParamVariables());
                terminalApkParamHistoryMessage.setLocalParam(JsonMapper.toJsonString(message.getParamChangedList()));
            }
        }
        if (terminalApkParamHistoryMessage.getReferenceId() != null) {
            terminalApkParamHistoryGateway.send(terminalApkParamHistoryMessage);
        }
    }

    private String mergeLocalParam(String originalLocalParam, Map<String, String> newLocalParamInfo) {
        Map<String, String> originalLocalParamMap = JsonMapper.fromJsonString(originalLocalParam, JsonMapper.getInstance().createCollectionType(Map.class, String.class, String.class));
        if (originalLocalParamMap == null) {
            originalLocalParamMap = new HashMap<>();
        }
        originalLocalParamMap.putAll(newLocalParamInfo);
        return JsonMapper.toJsonString(originalLocalParamMap);
    }
}
