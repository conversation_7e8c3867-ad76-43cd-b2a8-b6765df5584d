/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.consumer.handler.sync;

import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.market.pushtask.TerminalLastLauncher;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.sync.TerminalLastLauncherMessage;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.market.domain.service.terminal.TerminalLastLauncherService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TerminalLastLauncherSyncHandler extends AbstractHandler<TerminalLastLauncherMessage> {

    private final TerminalLastLauncherService terminalLastLauncherService;
    private final TerminalRegistryService terminalRegistryService;

    @Override
    protected void handleInternal(TerminalLastLauncherMessage message) {
        TerminalRegistry terminal = terminalRegistryService.get(message.getTerminalId());
        if (terminal == null) {
            return;
        }
        syncTerminalLastApkParam(message);
    }

    private void syncTerminalLastApkParam(TerminalLastLauncherMessage message) {
        TerminalLastLauncher terminalLastLauncher = BeanMapper.map(message, TerminalLastLauncher.class);
        TerminalLastLauncher existTerminalLastLauncher = terminalLastLauncherService.get(terminalLastLauncher);
        if (existTerminalLastLauncher == null) {
            terminalLastLauncherService.save(terminalLastLauncher);
        } else if (terminalLastLauncher.getDownloadTime().after(existTerminalLastLauncher.getDownloadTime())) {
            terminalLastLauncher.setId(existTerminalLastLauncher.getId());
            terminalLastLauncherService.save(terminalLastLauncher);
        }
    }
}
