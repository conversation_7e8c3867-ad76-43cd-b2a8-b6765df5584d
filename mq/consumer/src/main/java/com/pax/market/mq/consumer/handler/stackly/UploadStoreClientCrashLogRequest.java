package com.pax.market.mq.consumer.handler.stackly;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter @Setter @ToString
public class UploadStoreClientCrashLogRequest {
	
	@JsonProperty("l")
    private List<CrashReport> crashReports;

    @Getter
    @Setter
    @ToString @Accessors(chain = true)
    public static class CrashReport {
        @JsonProperty("id")
        private String reportId;
        @JsonProperty("vc")
        private Integer appVersionCode;
        @JsonProperty("vn")
        private String appVersionName;
        @JsonProperty("p")
        private String packageName;
        @JsonProperty("b")
        private Build build;
        @JsonProperty("ts")
        private Long totalMemSize;
        @JsonProperty("as")
        private Long availableMemSize;
        @JsonProperty("cd")
        private String customData;
        @JsonProperty("st")
        private String stackTrace;
        @JsonProperty("sy")
        private String stackType;
        @JsonProperty("ua")
        private String userAppStartDate;
        @JsonProperty("uc")
        private String userCrashDate;
        @JsonProperty("i")
        private String installationId;
        @JsonProperty("a")
        private String alias;
        @JsonProperty("d")
        private String description;
        @JsonProperty("h")
        private String hash;
        @JsonProperty("c")
        private Integer count;
        @JsonProperty("dm")
        private String deviceModel;
        @JsonProperty("o")
        private String osVersion;
        @JsonProperty("f")
        private List<String> fileNames;
        @JsonProperty("ot")
        private String osType;
    }

    @Getter
    @Setter
    @ToString
    public static class Build {
        @JsonProperty("b")
        private String board;
        @JsonProperty("bo")
        private String bootloader;
        @JsonProperty("br")
        private String brand;
        @JsonProperty("c")
        private String cpuAbi;
        @JsonProperty("c2")
        private String cpuAbi2;
        @JsonProperty("d")
        private String display;
        @JsonProperty("h")
        private String hardware;
        @JsonProperty("dg")
        private boolean isDebuggable;
        @JsonProperty("m")
        private String manufacturer;
        @JsonProperty("mo")
        private String model;
        @JsonProperty("sn")
        private String serial;
    }
	
}
