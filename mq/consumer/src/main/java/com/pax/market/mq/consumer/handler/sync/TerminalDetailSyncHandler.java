/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.mq.consumer.handler.sync;

import com.google.common.collect.Sets;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.TerminalDetailKeys;
import com.pax.market.domain.entity.global.market.MarketGeneralSetting;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalDetail;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.persistence.BatchOperator;
import com.pax.market.framework.common.persistence.handler.SingleConsumerHandler;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.ListUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.terminal.TerminalFunctionService;
import com.pax.market.functional.utils.AlarmUtils;
import com.pax.market.functional.vas.goinsight.converter.TerminalSyncLatestInfoToGoInsightConverter;
import com.pax.market.mq.consumer.handler.AbstractJsonMessageHandler;
import com.pax.market.mq.contract.goinsight.TerminalInfoToGoInsightCollectMessage;
import com.pax.market.mq.producer.gateway.insight.TerminalInfoToGoInsightCollectGateway;
import com.paxstore.global.domain.service.market.MarketGeneralSettingService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.global.domain.service.vas.VasConfigEntityService;
import com.paxstore.integration.mq.message.TerminalSyncDetailInfoMessage;
import com.paxstore.integration.mq.message.support.DetailInfo;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.market.domain.service.terminal.TerminalDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The type Terminal detail sync handler.
 */
@Slf4j
@Component
public class TerminalDetailSyncHandler extends AbstractJsonMessageHandler<TerminalSyncDetailInfoMessage> {

    private static final Set<String> sendInsightKeys = Sets.newHashSet(
            TerminalDetailKeys.BATTERY_AVL_CAPACITY,
            TerminalDetailKeys.BATTERY_CYCLE_TIMES,
            TerminalDetailKeys.BATTERY_TOTAL_CAPACITY,
            TerminalDetailKeys.MAG_READS,
            TerminalDetailKeys.MAG_SUC_RATE,
            TerminalDetailKeys.IC_READS,
            TerminalDetailKeys.IC_SUC_RATE,
            TerminalDetailKeys.PICC_READS,
            TerminalDetailKeys.PICC_SUC_RATE,
            TerminalDetailKeys.SIGNAL_INTENSITY,
            TerminalDetailKeys.STORAGE,
            TerminalDetailKeys.CPU,
            TerminalDetailKeys.RAM,
            TerminalDetailKeys.MOBILE_CAR,
            TerminalDetailKeys.SIGNAL_INTENSITY_ONE,
            TerminalDetailKeys.SIGNAL_INTENSITY_TWO,
            TerminalDetailKeys.BATTERY_CYCLE_TIMES_Q,
            TerminalDetailKeys.BATTERY_AVL_CAPACITY_Q,
            TerminalDetailKeys.PRINTER_NUM_Q,
            TerminalDetailKeys.MAG_READS_Q,
            TerminalDetailKeys.MAG_SUC_RATE_Q,
            TerminalDetailKeys.IC_READS_Q,
            TerminalDetailKeys.IC_SUC_RATE_Q,
            TerminalDetailKeys.PICC_READS_Q,
            TerminalDetailKeys.PICC_SUC_RATE_Q,
            TerminalDetailKeys.BATTERY_CYCLE_TIMES_Y,
            TerminalDetailKeys.BATTERY_AVL_CAPACITY_Y,
            TerminalDetailKeys.PRINTER_NUM_Y,
            TerminalDetailKeys.MAG_READS_Y,
            TerminalDetailKeys.MAG_SUC_RATE_Y,
            TerminalDetailKeys.IC_READS_Y,
            TerminalDetailKeys.IC_SUC_RATE_Y,
            TerminalDetailKeys.PICC_READS_Y,
            TerminalDetailKeys.PICC_SUC_RATE_Y,
            TerminalDetailKeys.CUMULATIVE_BOOT_TIME,
            TerminalDetailKeys.EMMC_SLC_USAGE,
            TerminalDetailKeys.EMMC_MLC_USAGE,
            TerminalDetailKeys.SIM_ONE_ICCID,
            TerminalDetailKeys.SIM_TWO_ICCID,
            TerminalDetailKeys.PUK_INJECT,
            TerminalDetailKeys.PUK_OWNER,
            TerminalDetailKeys.DAILY_BOOTTIME
    );
    @Autowired
    private TerminalDetailService terminalDetailService;
    @Autowired
    private MarketTerminalService marketTerminalService;
    @Autowired
    private TerminalRegistryService terminalRegistryService;
    @Autowired
    private VasConfigEntityService vasConfigEntityService;
    @Autowired(required = false)
    private TerminalInfoToGoInsightCollectGateway terminalInfoToGoInsightCollectGateway;
    @Autowired
    private TerminalSyncLatestInfoToGoInsightConverter terminalSyncLatestInfoToGoInsightConverter;
    @Autowired
    private TerminalFunctionService terminalFunctionService;
    @Autowired
    private MarketGeneralSettingService marketGeneralSettingService;

    /**
     * 获取并且剔除对应的key
     * 因为这个是临时新增出来的
     */
    private static String getKeyValue(List<DetailInfo> message, String key) {
        String value = null;
        Iterator<DetailInfo> iterator = message.iterator();
        while (iterator.hasNext()) {
            DetailInfo next = iterator.next();
            if (next.getKey().equals(key)) {
                value = next.getValue();
                //拿到之后再删除
                iterator.remove();
                break;
            }
        }
        return value;
    }

    @Override
    protected void handleInternal(TerminalSyncDetailInfoMessage message) {
        //信息同步处理点
        List<TerminalDetail> createList = new ArrayList<>();
        List<TerminalDetail> updateList = new ArrayList<>();
        List<TerminalDetail> existList = terminalDetailService.findByTerminal(message.getTerminalId());
        List<TerminalDetail> deleteList = Collections3.removeDuplicateElements(existList);
        TerminalRegistry terminalRegistry = terminalRegistryService.get(message.getTerminalId());

        if (terminalRegistry == null) {
            log.warn("TerminalDetailSyncHandler: terminal id = {}, terminal is not found", message.getTerminalId());
            return;
        }

        //上送的终端详情信息
        Map<String, List<TerminalDetail>> existTerminalDetailMap = convertlDetailListToMap(existList);
        if (CollectionUtils.isNotEmpty(message.getTerminalDetails())) {
            //将Key-Value对转换为后端的TerminalDetail对象
            List<TerminalDetail> terminalDetails = getTerminalDetails(message.getTerminalId(), message.getTerminalDetails());
            //识别出那些是新增的，那些是要更新的
            makeCreateAndUpdateList(terminalDetails, existTerminalDetailMap, createList, updateList, terminalRegistry);
            //如果已存在的数据不在上送列表里面，加入删除列表

            Map<String, List<TerminalDetail>> terminalDetailMap = convertlDetailListToMap(terminalDetails);
            for (TerminalDetail existTerminalDetail : existList) {
                if (!isGoInsightDailyUpdatedDetails(existTerminalDetail.getKey())
                        && terminalDetailMap.get(existTerminalDetail.getKey()) == null) {
                    deleteList.add(existTerminalDetail);
                }
            }
        } else {
            List<DetailInfo> createUpdateList = ListUtils.union(message.getCreateTerminalDetails(), message.getUpdateTerminalDetails());
            //将Key-Value对转换为后端的TerminalDetail对象
            makeCreateAndUpdateList(getTerminalDetails(message.getTerminalId(), createUpdateList), existTerminalDetailMap, createList, updateList, terminalRegistry);
            //如果删除的数据在已有列表中，加入删除列表，否则忽略
            for (TerminalDetail deleteTerminalDetail : getTerminalDetails(message.getTerminalId(), message.getDeleteTerminalDetails())) {
                TerminalDetail existTerminalDetail = CollectionUtils.isNotEmpty(existTerminalDetailMap.get(deleteTerminalDetail.getKey()))?
                        existTerminalDetailMap.get(deleteTerminalDetail.getKey()).get(0) : null;
                if (existTerminalDetail != null) {
                    deleteTerminalDetail.setId(existTerminalDetail.getId());
                    deleteList.add(deleteTerminalDetail);
                }
            }
        }

        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                SingleConsumerHandler.of(createList, terminalDetailService::insert),
                SingleConsumerHandler.of(updateList, terminalDetailService::update),
                SingleConsumerHandler.of(deleteList, terminalDetailService::delete)
        );
        Terminal terminal = updateTerminalDebugModeAndNetwork(message.getTerminalId(), createList, updateList, message.getMarketId());
        //同步终端机型
        syncTerminalModel(message);
        //发送同步Terminal Detail到insight的message(异步上送数据)
        buildAndSyncTerminalBasicInfoMessage(terminal);
    }

    private Terminal updateTerminalDebugModeAndNetwork(Long terminalId, List<TerminalDetail> createList, List<TerminalDetail> updateList, Long marketId) {
        String changedDebugMode = null;
        String changedNetwork = null;

        boolean isBreakLoop = false;
        for (TerminalDetail createTerminalDetail : createList) {
            if (StringUtils.equals(createTerminalDetail.getKey(), TerminalDetailKeys.DEBUG_MODE)) {
                changedDebugMode = createTerminalDetail.getValue();
            } else if (StringUtils.equals(createTerminalDetail.getKey(), TerminalDetailKeys.NETWORK)) {
                changedNetwork = createTerminalDetail.getValue();
            }

            if (StringUtils.isNotEmpty(changedDebugMode) && StringUtils.isNotEmpty(changedNetwork)) {
                isBreakLoop = true;
            }
            if (isBreakLoop) {
                break;
            }
        }
        for (TerminalDetail updateTerminalDetail : updateList) {
            if (StringUtils.equals(updateTerminalDetail.getKey(), TerminalDetailKeys.DEBUG_MODE)) {
                changedDebugMode = updateTerminalDetail.getValue();
            } else if (StringUtils.equals(updateTerminalDetail.getKey(), TerminalDetailKeys.NETWORK)) {
                changedNetwork = updateTerminalDetail.getValue();
            }

            if (StringUtils.isNotEmpty(changedDebugMode) && StringUtils.isNotEmpty(changedNetwork)) {
                isBreakLoop = true;
            }
            if (isBreakLoop) {
                break;
            }
        }

        if (StringUtils.isNotEmpty(changedDebugMode)) {
            marketTerminalService.updateTerminalDebugMode(terminalId, changedDebugMode);
        }
        if (StringUtils.isNotEmpty(changedNetwork)) {
            marketTerminalService.updateTerminalNetwork(terminalId, changedNetwork);
        }
        return marketTerminalService.get(terminalId);
    }

    private void makeCreateAndUpdateList(List<TerminalDetail> terminalDetails, Map<String, List<TerminalDetail>> terminalDetailMap, List<TerminalDetail> createList, List<TerminalDetail> updateList, TerminalRegistry terminal) {
        //如果新增或更新的数据不在已有列表中，加入新增列表，否则加入更新列表
        for (TerminalDetail terminalDetail : terminalDetails) {
            List<TerminalDetail> existTerminalDetails = terminalDetailMap.get(terminalDetail.getKey());
            TerminalDetail existTerminalDetail = CollectionUtils.isNotEmpty(existTerminalDetails) ? existTerminalDetails.get(0) : null;
            if (existTerminalDetail == null) {
                AlarmUtils.checkTerminalDetailSyncPutAlarm(terminalDetail, terminal);
                if (isAllowAddList(terminalDetail)){
                    createList.add(terminalDetail);
                }
            } else if (isChanged(terminalDetail, existTerminalDetail)) {
                terminalDetail.setId(existTerminalDetail.getId());
                if (isAllowAddList(terminalDetail)){
                    updateList.add(terminalDetail);
                }
                //CHECK
                if (StringUtils.equals(terminalDetail.getKey(), TerminalDetailKeys.COIN_BATTERY) && StringUtils.equalsIgnoreCase(terminalDetail.getStatus(), existTerminalDetail.getStatus())) {
                    return;
                }
                AlarmUtils.updateNewOrClearOldAlarmCache(terminalDetail, existTerminalDetail, terminal);
            }
        }
    }

    private boolean isAllowAddList(TerminalDetail terminalDetail) {
        String key = terminalDetail.getKey();
        if (TerminalDetailKeys.COIN_BATTERY.equals(key)) {
            return IntegerUtils.parse(terminalDetail.getValue()) >= 0;
        }
        return true;
    }


    private List<TerminalDetail> getTerminalDetails(Long terminalId, List<DetailInfo> syncList) {
        List<TerminalDetail> terminalDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(syncList)) {
            Date syncDate = new Date();

            Set<String> keys = new HashSet<>();
            for (DetailInfo di : syncList) {
                TerminalDetail terminalDetail = new TerminalDetail();
                terminalDetail.setTerminalId(terminalId);
                terminalDetail.setKey(StringUtils.trim(di.getKey()));
                terminalDetail.setValue(StringUtils.trim(di.getValue()));
                terminalDetail.setStatus(StringUtils.trim(di.getStatus()));
                terminalDetail.setSwitchable(StringUtils.trim(di.getSwitchable()));
                terminalDetail.setSyncDate(syncDate);

                if (StringUtils.isEmpty(terminalDetail.getKey()) || keys.contains(terminalDetail.getKey())) {
                    continue;
                }
                terminalDetails.add(terminalDetail);
                keys.add(terminalDetail.getKey());
            }
        }
        return terminalDetails;
    }

    private TerminalDetail getFromList(TerminalDetail terminalDetail, List<TerminalDetail> list) {
        for (TerminalDetail item : list) {
            if (StringUtils.equalsIgnoreCase(terminalDetail.getKey(), item.getKey())) {
                return item;
            }
        }
        return null;
    }

    private boolean isChanged(TerminalDetail terminalDetail, TerminalDetail existTerminalDetail) {
        return !StringUtils.equalsIgnoreCase(terminalDetail.getValue(), existTerminalDetail.getValue()) ||
                !StringUtils.equalsIgnoreCase(terminalDetail.getStatus(), existTerminalDetail.getStatus()) ||
                !StringUtils.equalsIgnoreCase(terminalDetail.getSwitchable(), existTerminalDetail.getSwitchable());
    }

    private void buildAndSyncTerminalBasicInfoMessage(Terminal terminal) {

        if (Objects.isNull(terminal)
                || Boolean.FALSE.equals(vasConfigEntityService.isVasEnabledGlobally())
                || !SystemPropertyHelper.getAllowSyncTerminalBasicInfo()) {
            return;
        }

        //异步查询终端所有数据后再全量上送
        TerminalInfoToGoInsightCollectMessage collectMessage = terminalSyncLatestInfoToGoInsightConverter.buildTerminalCollectMessage(terminal);
        collectMessage.setSendTerminalBasic(Boolean.TRUE);
        terminalInfoToGoInsightCollectGateway.send(collectMessage);
    }

    /**
     * 终端每天都会上送这些key 到GoInsight。避免重装client或者其它使client re-sync terminal details 时，会删除这些key
     */
    private boolean isGoInsightDailyUpdatedDetails(String key) {
        return sendInsightKeys.contains(key);
    }


    /**
     * 同步终端机型-制造商信息
     * @param message the message
     */
    private void syncTerminalModel(TerminalSyncDetailInfoMessage message){
        String modelName = getSyncValue(message, TerminalDetailKeys.MODEL_NAME);
        if (StringUtils.isNotBlank(modelName) && SystemPropertyHelper.isAllowChangeTerminalModel() && SystemPropertyHelper.isAllowModelAutoSync()) {
            MarketGeneralSetting marketGeneralSetting = marketGeneralSettingService.getByMarketId(message.getMarketId());
            if (Objects.nonNull(marketGeneralSetting) && BooleanUtils.toBoolean(marketGeneralSetting.getAllowModelAutoSync())){
                try {
                    terminalFunctionService.changeTerminalModelByName(message.getTerminalId(), modelName);
                } catch (BusinessException ex) {
                    log.warn("Unable to change terminal model: {}", ex.getBusinessCode());
                } catch (Exception ex) {
                    log.warn("Unable to change terminal model", ex);
                }
            }
        }
    }

    /**
     * 获取终端同步过来的Key - 目前只针对model
     */
    private String getSyncValue(TerminalSyncDetailInfoMessage message, String key) {
        String syncValue = null;

        if (CollectionUtils.isNotEmpty(message.getTerminalDetails())) {
            //全量上送
            syncValue = getKeyValue(message.getTerminalDetails(), key);
        } else if (CollectionUtils.isNotEmpty(message.getCreateTerminalDetails()) || CollectionUtils.isNotEmpty(message.getUpdateTerminalDetails())) {
            //增量上送
            List<DetailInfo> createUpdateList = ListUtils.union(message.getCreateTerminalDetails(), message.getUpdateTerminalDetails());
            syncValue = getKeyValue(createUpdateList, key);
        }
        return syncValue;
    }

    /**
     * 将terminalDetailList转换为insensitiveMap
     * @param terminalDetails terminalDetailList
     * @return insensitiveMap
     */
    private Map<String, List<TerminalDetail>> convertlDetailListToMap(List<TerminalDetail> terminalDetails){
        Map<String, List<TerminalDetail>> terminalDetailInsensitiveMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        Map<String, List<TerminalDetail>> terminalDetailMap = terminalDetails.stream().filter(x -> x.getKey() != null)
                .collect(Collectors.groupingBy(TerminalDetail::getKey));
        terminalDetailInsensitiveMap.putAll(terminalDetailMap);
        return terminalDetailInsensitiveMap;
    }

}
