/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.consumer.handler.push;

import com.google.common.collect.Lists;
import com.pax.market.constants.PushTaskStatus;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.TerminalActionType;
import com.pax.market.domain.entity.market.pushtask.BasePushTaskEntity;
import com.pax.market.domain.entity.market.rki.RkiDeductionRecord;
import com.pax.market.domain.entity.market.rki.RkiDeductionTerminal;
import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.utils.ListUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.contract.push.CreateTerminalActionMessage;
import com.pax.market.mq.contract.push.UpdateGroupActionCountMessage;
import com.pax.market.mq.producer.gateway.push.UpdateGroupActionCountGateway;
import com.paxstore.market.domain.service.pushtask.PushTaskUtils;
import com.paxstore.market.domain.service.rki.RkiDeductionService;
import com.paxstore.market.domain.service.terminal.TerminalActionService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CreateTerminalActionHandler extends AbstractHandler<CreateTerminalActionMessage> {
    @Autowired
    private TerminalActionService terminalActionService;
    @Autowired
    private UpdateGroupActionCountGateway updateGroupActionCountGateway;
    @Autowired
    private RkiDeductionService deductionService;

    @Override
    public void handleInternal(CreateTerminalActionMessage message) {
        BasePushTaskEntity<?> pushTask = PushTaskUtils.getPushTaskService(message.getActionType()).get(message.getReferenceId());
        if (pushTask == null || !StringUtils.equals(pushTask.getStatus(), PushTaskStatus.ACTIVE)) {
            return;
        }
        List<TerminalAction> terminalActionList = getTerminalActions(message);
        List<TerminalAction> groupApkParamTerminalActions = getGroupApkParamTerminalActions(message);//插入到terminalAction表
        terminalActionService.createPendingTerminalActions(ListUtils.union(terminalActionList, groupApkParamTerminalActions));

        if (CollectionUtils.isNotEmpty(terminalActionList)) {
            //保存分组RKI扣费终端列表明细
            saveDeductTerminalList(message.getDeductionId(), message.getReferenceId(), message.getActionType(), terminalActionList);
        }

        if (CollectionUtils.isNotEmpty(terminalActionList)) {
            updateGroupActionCountGateway.send(new UpdateGroupActionCountMessage(message.getActionType(), message.getReferenceId(), terminalActionList.size(), 0, 0, 0));
        }
        if (CollectionUtils.isNotEmpty(groupApkParamTerminalActions)) {
            updateGroupActionCountGateway.send(new UpdateGroupActionCountMessage(TerminalActionType.DOWNLOAD_GROUP_PARAM, message.getParamReferenceId(), groupApkParamTerminalActions.size(), 0, 0, 0));
        }
    }

    /**
     * 扣费终端明细
     */
    void saveDeductTerminalList(Long deductionId, Long referenceId, int actionType, List<TerminalAction> actionList) {
        if (SystemPropertyHelper.getRkiAuthSystemEnabled()) {
            RkiDeductionRecord rkiDeductionRecord;
            //如果是特殊处理DeductionId则跳过不处理
            if (SystemConstants.DUMMY_DEDUCTION_ID.equals(deductionId)) {
                return;
            }

            if (deductionId == null) {
                //如果没有则从DB中捞取最近的扣费操作记录
                rkiDeductionRecord = deductionService.getByReferenceIdAndActionType(referenceId, actionType);
                if (rkiDeductionRecord == null) {
                    logger.info("***saveDeductTerminalList skip , Reason: rkiDeductionRecord is null***");
                    return;
                }
            } else {
                //如果有值就用消息头中的扣费记录id
                rkiDeductionRecord = new RkiDeductionRecord();
                rkiDeductionRecord.setId(deductionId);
            }

            List<RkiDeductionTerminal> deductionTerminalList = actionList.stream().map(r -> {
                RkiDeductionTerminal terminal = new RkiDeductionTerminal();
                terminal.setDeductionId(rkiDeductionRecord.getId());
                terminal.setTerminalId(r.getTerminalId());
                return terminal;
            }).collect(Collectors.toList());
            deductionService.saveDeductionTerminal(deductionTerminalList);
        }
    }

    List<TerminalAction> getTerminalActions(CreateTerminalActionMessage message) {
        return message.getTerminalIdList().stream().map(terminalId -> {
            TerminalAction terminalAction = new TerminalAction();
            terminalAction.setTerminalId(terminalId);
            terminalAction.setActionType(message.getActionType());
            terminalAction.setReferenceId(message.getReferenceId());
            terminalAction.setStatus(message.getStatus());
            terminalAction.setRemarks(message.getRemarks());
            return terminalAction;
        }).toList();
    }

    List<TerminalAction> getGroupApkParamTerminalActions(CreateTerminalActionMessage message) {
        if (LongUtils.isNotBlankAndPositive(message.getParamReferenceId())) {
            return message.getTerminalIdList().stream().map(terminalId -> {
                TerminalAction paramAction = new TerminalAction();
                paramAction.setTerminalId(terminalId);
                paramAction.setActionType(TerminalActionType.DOWNLOAD_GROUP_PARAM);
                paramAction.setReferenceId(message.getParamReferenceId());
                paramAction.setStatus(message.getParamActionStatus());
                return paramAction;
            }).toList();
        }
        return Lists.newArrayList();
    }
}
