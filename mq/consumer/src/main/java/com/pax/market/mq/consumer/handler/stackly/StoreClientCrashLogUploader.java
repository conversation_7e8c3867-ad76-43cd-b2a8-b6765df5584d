package com.pax.market.mq.consumer.handler.stackly;

import com.paxstore.global.domain.service.setting.SystemPropertyService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.support.config.props.StoreDeployInfoConfigProps;
import com.pax.support.http.HttpClient;
import com.pax.support.http.HttpInvoker;
import com.pax.support.http.HttpMethod;
import com.pax.support.http.UrlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class StoreClientCrashLogUploader {
    private static final long DEFAULT_CONN_TIMEOUT = 5L; //seconds
    private static final long DEFAULT_SOCKET_TIMEOUT = 30L; //seconds
    private static final String DEFAULT_KEY = "PAXSTOREClient_126FCBD6E7C068C04C0997745831D245";
    private static final String DEFAULT_SECRET = "ffJ0aXRsZSI6ImZ1Y4sgeW91IHBheSBtSZAsImxlYWQiOik9";
    private static final String DEFAULT_REQ_ID = "DUMMY_REQ_ID";
    private static final String APP_KEY_HEADER_PARAM = "X-APP-KEY";
    private static final String REQUEST_ID_HEADER_PARAM = "X-PDC-REQ-ID";
    private static final String SIGNATURE_HEADER_PARAM = "X-PDC-SIGNATURE";
    private static final String STORE_ENV_HEADER_PARAM = "X-STORE-ENV";

    private HttpClient httpClient;
    private String signature;
    private String stacklyHost;
    private final String envCode;
    private final byte[] dummyReqIdBytes = DEFAULT_REQ_ID.getBytes(StandardCharsets.UTF_8);

    public StoreClientCrashLogUploader(StoreDeployInfoConfigProps deployInfo) {
        this.envCode = deployInfo.getEnvCode();
    }

    public void uploadEvent(UploadStoreClientEventRequest request) {
        ensureInit();
        if (Objects.isNull(httpClient)) {
            return;
        }
        HttpInvoker.newInstance(httpClient)
                .addHeader(REQUEST_ID_HEADER_PARAM, DEFAULT_REQ_ID)
                .addHeader(SIGNATURE_HEADER_PARAM, signature)
                .addHeader(APP_KEY_HEADER_PARAM, DEFAULT_KEY)
                .addHeader(STORE_ENV_HEADER_PARAM, envCode)
                .setHttpMethod(HttpMethod.POST)
                .setRequestBody(request)
                .setRetryEnabled(false)
                .setUrlBuilder(UrlBuilder.create().setHostUri(stacklyHost).setSubPaths("/v1/store/store_client/custom-event"))
                .onFailure(resp -> log.warn("failed to send StoreClient CustomEvent to stackly: {}", resp))
                .onError(t -> log.warn("error when send StoreClient CustomEvent to stackly", t))
                .getVoidResult();
        log.debug("executed StoreClient CustomEvent uploading");

    }

    public void upload(UploadStoreClientCrashLogRequest request) {
        ensureInit();
        if (Objects.isNull(httpClient)) {
            return;
        }

        HttpInvoker.newInstance(httpClient)
                .addHeader(REQUEST_ID_HEADER_PARAM, DEFAULT_REQ_ID)
                .addHeader(SIGNATURE_HEADER_PARAM, signature)
                .addHeader(APP_KEY_HEADER_PARAM, DEFAULT_KEY)
                .addHeader(STORE_ENV_HEADER_PARAM, envCode)
                .setHttpMethod(HttpMethod.POST)
                .setRequestBody(request)
                .setRetryEnabled(false) //no need retry
                .setUrlBuilder(UrlBuilder.create().setHostUri(stacklyHost).setSubPaths("/v1/store/store_client/crash_report"))
                .onFailure(resp -> log.warn("failed to send StoreClient CrashReport to stackly: {}", resp))
                .onError(t -> log.warn("error when send StoreClient CrashReport to stackly", t))
                .getVoidResult();

        if (log.isDebugEnabled()) {
            log.debug("executed {} StoreClient CrashReports uploading", request.getCrashReports().size());
        }
    }

    private void ensureInit() {
        if (Objects.nonNull(httpClient)) {
            return;
        }

        stacklyHost = SystemPropertyService.getPropertyValue(SystemPropertyHelper.STACKLY_HOST_4_STORE_CLIENT_CRASH_LOG_ACTION);
        if (StringUtils.isBlank(stacklyHost)) {
            log.warn("received StoreClient CrashReport, but stackly host not configured in system props");
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug("configured stackly host in SystemProperty: {}", stacklyHost);
        }

        signature = buildSignature();
        if (StringUtils.isBlank(signature)) {
            log.warn("received StoreClient CrashReport, but build stackly signature failed, this should not happen");
            return;
        }

        httpClient = HttpClient.builder()
                .connectTimeout(DEFAULT_CONN_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.SECONDS)
                .build();
    }

    private String buildSignature() {
        try {
            return byte2hex(encryptHMAC());
        } catch (Exception e) {
            log.warn("build stackly signature failed", e);
            return null;
        }
    }

    private byte[] encryptHMAC() throws GeneralSecurityException {
        byte[] secretBytes = DEFAULT_SECRET.getBytes(StandardCharsets.UTF_8);
        SecretKey secretKey = new SecretKeySpec(secretBytes, "HmacMD5");
        Mac mac = Mac.getInstance(secretKey.getAlgorithm());
        mac.init(secretKey);
        return mac.doFinal(dummyReqIdBytes);
    }

    private String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (byte aByte : bytes) {
            String hex = Integer.toHexString(aByte & 0xFF);
            if (hex.length() == 1) {
                sign.append('0');
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }
}