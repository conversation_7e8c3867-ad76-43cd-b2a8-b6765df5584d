/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.consumer.util;

import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.developer.Developer;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.dto.LicenseInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.functional.market.MarketUserFuncService;
import com.pax.market.mq.consumer.MqConsumerCurrentLoginProvider;
import com.paxstore.global.domain.cachable.LocalCacheMarketInfoService;
import com.paxstore.global.domain.service.developer.DeveloperService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.market.domain.cachable.LocalMarketRootResellerService;

import java.util.Locale;
import java.util.Objects;

/**
 * login provider for MqConsumerCurrentLoginProvider
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
public class LoginProviderUtil {

    private static CurrentLoginProvider getCurrentLoginProvider() {
        return SpringContextHolder.getBean(CurrentLoginProvider.class);
    }


    public static void setLoginProviderProperties(Long marketId) {
        setLoginProviderProperties(marketId, null, null, null);
    }

    public static void setLoginProviderProperties(Long marketId, Long resellerId, Long userId, Long developerId) {
        CurrentLoginProvider loginProvider = getCurrentLoginProvider();
        if (loginProvider instanceof MqConsumerCurrentLoginProvider mqConsumerCurrentLoginProvider) {
            if (marketId != null && !LongUtils.equals(marketId, 0L)) {
                mqConsumerCurrentLoginProvider.setCurrentMarket(marketId);
                LocalCacheMarketInfoService marketInfoService = SpringContextHolder.getBean(LocalCacheMarketInfoService.class);
                MarketInfo marketInfo = marketInfoService.getMarketInfo(marketId);
                if (marketInfo == null) {
                    loadLicensePropertyToMarketInfo();
                    return;
                }
                mqConsumerCurrentLoginProvider.setCurrentMarket(marketInfo);
                LocalMarketRootResellerService marketRootResellerService = SpringContextHolder.getBean(LocalMarketRootResellerService.class);
                marketRootResellerService.loadMarketRootResellerId(marketInfo);
                if (userId != null) {
                    UserService userService = SpringContextHolder.getBean(UserService.class);
                    MarketUserFuncService marketUserFuncService = SpringContextHolder.getBean(MarketUserFuncService.class);
                    User user = userService.get(userId);
                    if (user != null) {
                        if (Objects.nonNull(resellerId)) {
                            marketUserFuncService.loadUserResellerRoleInfo(user, marketInfo, String.valueOf(resellerId));
                        } else if (Objects.nonNull(developerId)) {
                            DeveloperService developerService = SpringContextHolder.getBean(DeveloperService.class);
                            Developer developer = developerService.get(developerId);
                            if(developer != null) {
                                user.setCurrentDeveloper(developerService.get(developerId));
                            }
                        }
                        UserInfo userInfo = BeanMapper.map(user, UserInfo.class);
                        ((MqConsumerCurrentLoginProvider) loginProvider).setCurrentUser(userInfo);
                    } else {
                        ((MqConsumerCurrentLoginProvider) loginProvider).setCurrentUser(userId);
                    }
                }
            } else {
                loadLicensePropertyToMarketInfo();
            }
        }
    }

    private static void loadLicensePropertyToMarketInfo() {
        CurrentLoginProvider loginProvider = getCurrentLoginProvider();
        LicenseService licenseService = SpringContextHolder.getBean(LicenseService.class);
        LicenseInfo licenseInfo = licenseService.getLicenseInfo();
        loginProvider.getCurrentMarketInfo().setAllowVas(licenseInfo.isAllowVas());
        loginProvider.getCurrentMarketInfo().setAllowGroupPushLimit(licenseInfo.isAllowGroupPushLimit());
    }

    public static void clearLoginProviderProperties() {
        CurrentLoginProvider loginProvider = getCurrentLoginProvider();
        if (loginProvider instanceof MqConsumerCurrentLoginProvider) {
            ((MqConsumerCurrentLoginProvider) loginProvider).clear();
        }
    }

    public static void setLocale(String localeStr) {
        if (StringUtils.startsWith(localeStr, "zh")) {
            localeStr = Locale.SIMPLIFIED_CHINESE.toString();
        }
        if (StringUtils.startsWith(localeStr, "ja")) {
            localeStr = "jp";
        }
        if (StringUtils.isNotBlank(localeStr) && isLocaleAvailable(localeStr)) {
            if (localeStr.contains(String.valueOf('_'))) {
                final String[] localeArr = localeStr.split(String.valueOf('_'));
                RequestLocaleHolder.setLocale(new Locale(localeArr[0], localeArr[1]).toString());
            } else {
                RequestLocaleHolder.setLocale(new Locale(localeStr, "").toString());
            }
        } else {
            RequestLocaleHolder.setLocale(SystemConstants.DEFAULT_LOCALE);
        }
    }

    private static boolean isLocaleAvailable(String localeStr) {
        for (String availableLocale : SystemConstants.SUPPORT_LOCALES) {
            if (StringUtils.equalsIgnoreCase(availableLocale, localeStr)) {
                return true;
            }
        }
        return false;
    }
}
