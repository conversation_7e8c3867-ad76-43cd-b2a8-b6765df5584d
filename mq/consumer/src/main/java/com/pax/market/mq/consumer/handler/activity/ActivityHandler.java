/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.consumer.handler.activity;

import com.pax.market.audit.biz.utils.AuditLogChangeRecordUtils;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.constants.ActivityStatus;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.market.Activity;
import com.pax.core.exception.BusinessException;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.dto.audit.AuditRecordInfo;
import com.pax.market.framework.common.audit.AuditRecordContext;
import com.pax.market.framework.common.utils.Collections3;
import com.paxstore.market.domain.service.ActivityService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.global.domain.utils.UserUtils;
import com.pax.market.functional.activity.exports.BaseExportService;
import com.pax.market.functional.activity.imports.BaseImportService;
import com.pax.market.notification.ActivityNotificationService;
import com.pax.market.framework.common.audit.AuditTrailContextHolder;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.exception.ServiceException;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.mq.consumer.audit.ConsumerAuditContext;
import com.pax.market.mq.consumer.handler.AbstractHandler;
import com.pax.market.mq.consumer.util.LoginProviderUtil;
import com.pax.market.mq.contract.activity.ActivityMessage;
import com.pax.market.mq.contract.activity.ExportActivityMessage;
import com.pax.market.mq.contract.activity.ImportActivityMessage;
import com.pax.market.mq.contract.audit.AuditTrailMessage;
import com.pax.market.mq.producer.gateway.audit.ApiLogGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * The type Activity handler.
 */
@Component
public class ActivityHandler extends AbstractHandler<ActivityMessage> {
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ActivityNotificationService activityNotificationService;
    @Autowired
    private ApiLogGateway apiLogGateway;
    @Autowired
    private UserService userService;
    @Autowired
    private ResellerService resellerService;

    @Override
    protected void handleInternal(ActivityMessage message) {
        Activity activity = activityService.getWithoutDataScopeFilter(message.getTaskId());
        if (activity == null || !StringUtils.equalsIgnoreCase(activity.getStatus(), ActivityStatus.PENDING)) {
            //如果任务找不到,或者不是Pending状态，直接返回
            return;
        }
        UserUtils.loadUserNameAndEmail(activity.getCreatedBy());
        LoginProviderUtil.setLoginProviderProperties(activity.getMarketId(), activity.getResellerId(), activity.getUserId(), null);
        initAuditTrailContext(message);

        try {
            activityService.updateActivityStatus(activity, ActivityStatus.IN_PROGRESS, "Activity in progress.");

            if (message instanceof ImportActivityMessage) {
                BaseImportService importService = getImportService(activity.getType());
                if (importService == null) {
                    activityService.updateActivityStatus(activity, ActivityStatus.FAILED, "No import activity service implemented.");
                } else {
                    importService.importData(activity);
                }
            } else if (message instanceof ExportActivityMessage) {
                BaseExportService exportService = getExportService(activity.getType());
                if (exportService == null) {
                    activityService.updateActivityStatus(activity, ActivityStatus.FAILED, "No export activity service implemented.");
                } else {
                    exportService.exportData(activity);
                }
            }
        } catch (BusinessException ex) {
            logger.warn("Error occurred when execute the activity service", ex);
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, MessageUtils.getErrorMessage(ex.getBusinessCode(), activity.getLocale(), ex.getArgObjects()));
        } catch (ServiceException ex) {
            logger.warn("Error occurred when execute the activity service", ex);
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, MessageUtils.getErrorMessage(ex.getBusinessCode(), activity.getLocale(), ex.getArgObjects()));
        } catch (Exception ex) {
            logger.error("Error occurred when execute the activity service", ex);
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, "Error occurred when execute the activity service");
        } finally {
            try {
                processAuditTrail(activity);
            } catch (Exception ex) {
                logger.error("Error occurred when execute the audit log", ex);
            } finally {
                LoginProviderUtil.clearLoginProviderProperties();
            }
        }

        activityNotificationService.sendActivityNotification(activity);
    }

    private BaseExportService getExportService(String activityType) {
        Map<String, BaseExportService> exportServiceMap = SpringContextHolder.getBeansOfType(BaseExportService.class);
        for (BaseExportService exportService : exportServiceMap.values()) {
            if (StringUtils.equals(exportService.getActivityType(), activityType)) {
                return exportService;
            }
        }
        return null;
    }

    private BaseImportService getImportService(String activityType) {
        Map<String, BaseImportService> exportServiceMap = SpringContextHolder.getBeansOfType(BaseImportService.class);
        for (BaseImportService importService : exportServiceMap.values()) {
            if (StringUtils.equals(importService.getActivityType(), activityType)) {
                return importService;
            }
        }
        return null;
    }

    private void initAuditTrailContext(ActivityMessage message) {
        if (message instanceof ImportActivityMessage) {
            ImportActivityMessage importActivityMessage = (ImportActivityMessage) message;
            ConsumerAuditContext context = new ConsumerAuditContext();
            context.setAction(importActivityMessage.getAuditAction());
            context.setAction2(importActivityMessage.getAuditAction2());
            context.setType(importActivityMessage.getAuditType());
            context.setClientIp(importActivityMessage.getClientIp());
            context.setRequestUri(importActivityMessage.getRequestUri());
            context.setDataType(importActivityMessage.getDataType());
            context.setAuditDate(new Date());

            AuditTrailContextHolder.getInstance().set(context);
        }
    }

    private void processAuditTrail(Activity activity) {
        ConsumerAuditContext context = (ConsumerAuditContext) AuditTrailContextHolder.getInstance().getCurrent();
        if (context != null) {
            AuditTrailMessage message = new AuditTrailMessage();
            message.setUserId(activity.getUserId());
            message.setMarketId(activity.getMarketId());
            message.setResellerId(activity.getResellerId());
            message.setEntityIds(context.getEntityIds());
            message.setAuditAction(context.getAction());
            message.setAuditAction2(context.getAction2() > 0 ? context.getAction2() : null);
            message.setAuditType(context.getType());
            message.setBizCode(context.getBusinessCode());
            message.setClientIp(context.getClientIp());
            message.setCreatedDate(context.getAuditDate());
            message.setRequestUri(context.getRequestUri());
            message.setDataType(context.getDataType());
            message.setTimeSpent(System.currentTimeMillis() - context.getAuditDate().getTime());
            User user = userService.get(activity.getUserId());
            if (Objects.nonNull(user)) {
                message.setUserLoginName(user.getLoginName());
                message.setUsername(user.getName());
            }
            Reseller reseller = resellerService.get(activity.getResellerId());
            if (Objects.nonNull(reseller)) {
                message.setResellerName(reseller.getName());
            }
            if (StringUtils.equals(activity.getStatus(), ActivityStatus.FAILED)) {
                message.setHttpCode(400);
                message.setBizCode(ApiCodes.ACTIVITY_FAILED);
            } else {
                message.setHttpCode(200);
                message.setBizCode(0);
            }
            if (Objects.nonNull(context.getEntityIds())) {
                if (!Collections3.isEmpty(context.getRecords())) {
                    Map<Long, AuditRecordInfo> map = new HashMap<>();
                    for (Map.Entry<Long, AuditRecordContext> contextEntry : context.getRecords().entrySet()) {
                        map.put(contextEntry.getKey(), new AuditRecordInfo(contextEntry.getValue().getOldRecord(), contextEntry.getValue().getNewRecord()));
                    }
                    message.setRecords(map);
                }
                if (!AuditDataTypes.getLoadListRecordDataType().contains(context.getDataType()) && Objects.nonNull(context.getEntityIds()) && context.getEntityIds().length == 1) {
                    AuditLogChangeRecordUtils.resolveBasicInfo(message.getDataType(), message);
                }
            }
            apiLogGateway.send(message);
            AuditTrailContextHolder.getInstance().clear();
        }
    }
}
