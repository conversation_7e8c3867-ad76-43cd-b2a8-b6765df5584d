<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pax.market</groupId>
		<artifactId>p-market-mq</artifactId>
		<version>9.8.0-SNAPSHOT</version>
	</parent>
	<artifactId>p-market-mq-consumer</artifactId>
	<name>PAX Market :: Message Queue :: Consumer</name>

	<properties>
		<iot.base.version>1.1.0</iot.base.version>
		<api-common.module.path>${project.basedir}/../../api-servers/common</api-common.module.path>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-mq-contract</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-mq-producer</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>paxstore-core-functional-common</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-framework-payment</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.mpusher</groupId>
			<artifactId>mpush-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.mpusher</groupId>
			<artifactId>mpush-diagnosis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.mpusher</groupId>
			<artifactId>mpush-biz-comm</artifactId>
		</dependency>
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-framework-signature</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-framework-rki</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.pax.support.mq</groupId>
			<artifactId>pax-support-mq-core</artifactId>
		</dependency>
		<dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-bsdiff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.vas.config</groupId>
            <artifactId>paxvas-config-mp-adapt</artifactId>
        </dependency>
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf</artifactId>
			<version>9.1.22</version>
		</dependency>
		<dependency>
			<groupId>com.zolon.saas</groupId>
			<artifactId>saas-api-store-message</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zolon.saas</groupId>
			<artifactId>vas-goinsight-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zolon.iot.base</groupId>
			<artifactId>iot-base-device-model</artifactId>
			<version>${iot.base.version}</version>
		</dependency>

		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>paxstore-core-emm-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!--   定位服务	-->
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>iotwireless</artifactId>
			<version>2.28.29</version>
		</dependency>
		<dependency>
			<groupId>com.google.maps</groupId>
			<artifactId>google-maps-services</artifactId>
			<version>2.2.0</version>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>validate</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${basedir}/target/classes/messages</outputDirectory>
							<resources>
								<resource>
									<directory>${api-common.module.path}/src/main/resources/messages</directory>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
