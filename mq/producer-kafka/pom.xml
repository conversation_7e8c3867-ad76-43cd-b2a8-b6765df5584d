<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pax.market</groupId>
		<artifactId>p-market-mq</artifactId>
		<version>9.8.0-SNAPSHOT</version>
	</parent>
	<artifactId>p-market-mq-producer-kafka</artifactId>
	<name>PAX Market :: Message Queue :: Kafka Producer Module</name>

	<dependencies>
		<dependency>
			<groupId>com.pax.support.mq</groupId>
			<artifactId>pax-support-mq-kafka</artifactId>
		</dependency>

		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-mq-shared-kafka</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-mq-producer</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>paxstore-core-integration</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pax.support</groupId>
			<artifactId>pax-support-dynamic-datasource</artifactId>
		</dependency>
	</dependencies>

</project>