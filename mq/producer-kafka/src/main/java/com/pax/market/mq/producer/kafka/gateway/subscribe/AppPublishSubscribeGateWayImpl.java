/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.mq.producer.kafka.gateway.subscribe;

import com.pax.market.mq.contract.subscribe.AppPublishSubscribeMessage;
import com.pax.market.mq.producer.gateway.subscribe.AppPublishSubscribeGateway;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import org.springframework.stereotype.Component;

/**
 * 固件发布市场，自动订阅
 * <AUTHOR>
 * @create 2023/8/23
 */
@Component
public class AppPublishSubscribeGateWayImpl extends DefaultKafkaGateway implements AppPublishSubscribeGateway {
    @Override
    public void send(AppPublishSubscribeMessage message) {
        send(TopicNames.T_SUBSCRIBE_APP,message);
    }
}
