package com.pax.market.mq.producer.kafka.gateway;

import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.support.mq.core.message.Message;
import com.pax.support.mq.kafka.producer.gateway.AbstractKafkaGateway;
import com.paxstore.integration.mq.message.MqMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultKafkaGateway extends AbstractKafkaGateway {
    @Override
    public void setupMessage(Object messageObj) {
        if (messageObj instanceof Message message) {
            String datasource = PaxDynamicDsThreadLocal.getPreferenceDatasource();
            if (StringUtils.isNotEmpty(datasource)) {
                message.setDataSource(datasource);
            }
            if (message.getMarketId() != null) {
                return;
            }
            MarketInfo currentMarketInfo = SpringContextHolder.getBean(CurrentLoginProvider.class).getCurrentMarketInfo();
            if (currentMarketInfo != null && !LongUtils.equals(currentMarketInfo.getId(), 0L)) {
                message.setMarketId(currentMarketInfo.getId());
            }
            if (StringUtils.isEmpty(message.getDataSource()) && message.getMarketId() == null && log.isDebugEnabled()) {
                log.error("No marketId and datasource found in the message", new Throwable(message.getClass().getSimpleName()));
            }
        } else if (messageObj instanceof MqMessage message) {
            if (message.getMarketId() != null || !log.isDebugEnabled()) {
                return;
            }
            log.error("No marketId found in the message", new Throwable(message.getClass().getSimpleName()));
        }
    }
}
