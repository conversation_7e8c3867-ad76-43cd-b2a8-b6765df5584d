/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.producer.kafka.gateway.stock;

import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.mq.contract.stock.TerminalStockSyncMessage;
import com.pax.market.mq.producer.gateway.stock.TerminalStockSyncGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class TerminalStockSyncGatewayImpl extends DefaultKafkaGateway implements TerminalStockSyncGateway {
    @Value("${kafka.customization.partitions.t_terminal_stock_sync:1}")
    private long terminalStockSyncPartitions;

    public void send(TerminalStockSyncMessage message) {
        if (StringUtils.isEmpty(message.getSerialNo())) {
            return;
        }
        int partitionNo = (terminalStockSyncPartitions > 1) ? (int) (message.getSerialNo().length() % terminalStockSyncPartitions) : 0;
        send(TopicNames.T_TERMINAL_STOCK_SYNC, message, partitionNo);
    }
}
