/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.mq.producer.kafka.gateway.cleardata;

import com.pax.market.mq.contract.cleardata.FirmwarePhysicalDeleteMessage;
import com.pax.market.mq.producer.gateway.cleardata.FirmwarePhysicalDeleteGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import com.pax.support.mq.kafka.producer.gateway.AbstractKafkaGateway;
import org.springframework.stereotype.Component;

@Component
public class FirmwarePhysicalDeleteGatewayImpl extends AbstractKafkaGateway implements FirmwarePhysicalDeleteGateway {

    @Override
    public void send(FirmwarePhysicalDeleteMessage message) {
        send(TopicNames.T_FIRMWARE_PHYSICAL_DELETE, message);
    }
}
