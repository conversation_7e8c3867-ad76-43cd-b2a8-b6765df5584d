/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.mq.producer.kafka.gateway.goinsight;

import com.pax.market.mq.contract.goinsight.ResellerMoveMessage;
import com.pax.market.mq.producer.gateway.insight.ResellerMoveGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import org.springframework.stereotype.Component;


/**
 * The type Reseller move gateway.
 */
@Component
public class ResellerMoveGatewayImpl extends DefaultKafkaGateway implements ResellerMoveGateway {

    /**
     * Send.
     *
     * @param message the message
     */
    @Override
    public void send(ResellerMoveMessage message) {
        send(TopicNames.T_RESELLER_MOVE, message);
    }
}
