/**
 * ********************************************************************************
 * COPYRIGHT      
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION     
 *   This software is supplied under the terms of a license agreement or      
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied     
 *   or disclosed except in accordance with the terms in that agreement.
 *         
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.mq.producer.kafka.gateway.buriedpoint;

import com.pax.market.mq.contract.buriedpoint.BuriedPointSyncLatestInfoMessage;
import com.pax.market.mq.producer.gateway.buriedpoint.BuriedPointGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import com.pax.support.mq.kafka.producer.gateway.AbstractKafkaGateway;
import org.springframework.stereotype.Component;

/**
 * 
 *
 * <AUTHOR>
 * @date Jan 17, 2021
 */
@Component
public class BuriedPointGatewayImpl extends AbstractKafkaGateway implements BuriedPointGateway {
	
	@Override
	public void send(BuriedPointSyncLatestInfoMessage message) {
		sendObjectAsJSON(TopicNames.T_SYNC_BURIED_POINT, message);
	}

}
