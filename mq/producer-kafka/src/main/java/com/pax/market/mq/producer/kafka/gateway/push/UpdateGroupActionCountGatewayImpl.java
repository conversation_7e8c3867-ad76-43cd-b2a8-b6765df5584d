/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.mq.producer.kafka.gateway.push;

import com.pax.market.mq.contract.push.UpdateGroupActionCountMessage;
import com.pax.market.mq.producer.gateway.push.UpdateGroupActionCountGateway;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class UpdateGroupActionCountGatewayImpl extends DefaultKafkaGateway implements UpdateGroupActionCountGateway {
    @Value("${kafka.customization.partitions.t_update_group_action_count:2}")
    private long partitionsCount;

    @Override
    public void send(UpdateGroupActionCountMessage message) {
        int partitionNo = (partitionsCount > 1) ? (int) (message.getReferenceId() % partitionsCount) : 0;
        send(TopicNames.T_UPDATE_GROUP_ACTION_COUNT, message, partitionNo);
    }

}
