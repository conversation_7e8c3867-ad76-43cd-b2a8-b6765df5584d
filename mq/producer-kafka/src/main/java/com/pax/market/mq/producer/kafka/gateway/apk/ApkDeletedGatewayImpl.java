/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.mq.producer.kafka.gateway.apk;

import com.pax.market.mq.contract.apk.ApkDeletedMessage;
import com.pax.market.mq.producer.gateway.apk.ApkDeletedGateway;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import org.springframework.stereotype.Component;

/**
 * The type Clear apk data gateway.
 */
@Component
public class ApkDeletedGatewayImpl extends DefaultKafkaGateway implements ApkDeletedGateway {

    @Override
    public void send(ApkDeletedMessage message) {
        send(TopicNames.T_CLEAR_APK_DATA, message);
    }
}
