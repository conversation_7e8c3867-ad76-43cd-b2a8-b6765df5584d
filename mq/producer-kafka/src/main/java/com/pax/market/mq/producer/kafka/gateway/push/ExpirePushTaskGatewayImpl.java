package com.pax.market.mq.producer.kafka.gateway.push;

import com.pax.market.mq.contract.push.ExpirePushTaskMessage;
import com.pax.market.mq.producer.gateway.push.ExpirePushTaskGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Time 2019/4/29 16:00
 */
@Component
public class ExpirePushTaskGatewayImpl extends DefaultKafkaGateway implements ExpirePushTaskGateway {

    @Override
    public void send(ExpirePushTaskMessage message) {
        send(TopicNames.T_EXPIRE_PUSH_TASK, message);
    }
}
