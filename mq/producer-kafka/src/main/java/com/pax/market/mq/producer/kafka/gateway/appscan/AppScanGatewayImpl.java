package com.pax.market.mq.producer.kafka.gateway.appscan;

import com.pax.market.mq.contract.appscan.CreateScanTaskMessage;
import com.pax.market.mq.producer.gateway.appscan.AppScanGateway;
import com.pax.market.mq.shared.kafka.TopicNames;
import com.pax.market.mq.producer.kafka.gateway.DefaultKafkaGateway;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/7/15 16:17
 */
@Component
public class AppScanGatewayImpl extends DefaultKafkaGateway implements AppScanGateway {

    @Override
    public void send(CreateScanTaskMessage message) {
        send(TopicNames.T_APP_SCAN_MESSAGE, message);
    }
}
