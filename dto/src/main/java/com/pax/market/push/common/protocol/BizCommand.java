/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.push.common.protocol;

/**
 * 推送消息的业务逻辑命令
 */
public enum BizCommand {

    /**
     * Push app biz command.
     */
    DOWNLOAD_APP(1),       //推送应用列表
    /**
     * Push param biz command.
     */
    DOWNLOAD_PARAM(2),          //推送参数文件
    /**
     * Stop app biz command.
     */
    STOP_APP(3),            //停用应用
    /**
     * Lock pos biz command.
     */
    LOCK_POS(4),            //锁定POS
    /**
     * Uninstall app biz command.
     */
    UNINSTALL_APP(5),       //卸载应用
    /**
     * Test push biz command
     */
    TEST(6),                //推送测试
    /**
     * Push firmware biz command
     */
    DOWNLOAD_FIRMWARE(7),      //推送固件
    /**
     * Disable pos biz command
     */
    DISABLE_POS(8),         //停用终端
    /**
     * Switch PED biz command
     */
    SWITCH_PED(9),         //设置PED
    /**
     * Collect android logcat biz command.
     */
    COLLECT_LOGCAT(10),     //收集logcat

    /**
     * Terminal operation biz command.
     */
    TERMINAL_OPERATION(11),

    /**
     * Refresh pos detail biz command.
     */
    REFRESH_POS_DETAIL(12),  //

    /**
     * Force update biz command
     */
    FORCE_UPDATE(13),        //强制更新

    /**
     * set terminal profile
     */
    TERMINAL_PROFILE(14),        //终端Profile

    /**
     * Terminal activate operation
     */
    TERMINAL_ACTIVATION(15),  //终端激活

    /**
     * Bypass server trust
     */
    BYPASS_SERVER_TRUST(16),    //忽略服务端证书校验

    /**
     * Check server trust
     */
    CHECK_SERVER_TRUST(17),    //校验服务端证书

    /**
     * Terminal remote key injection
     */
    TERMINAL_RKI(18),          //终端RKI远程注入

    /**
     * Reseller certificate biz command.
     */
    RESELLER_CERTIFICATE(19),          //终端获取代理商证书

    /**
     * Switch push channel biz command.
     */
    @Deprecated
    SWITCH_PUSH_CHANNEL(20),         //切换推送通道

    /**
     * Push channel update biz command.
     */
    @Deprecated
    PUSH_CHANNEL_UPDATE(21),            //检查推送通道，强制client将当前通道通过API上送到PAXSTORE

    /**
     * Terminal deletion biz command.
     */
    TERMINAL_DELETION(22),            //删除终端

    /**
     * Terminal rki deletion biz command.
     */
    TERMINAL_RKI_DELETION(23),          //终端RKI远程注入任务取消

    @Deprecated
    SWITCH_PUSH_CHANNEL_TO_LOCAL(24),   //将终端mpush通道切换到本地，必须client版本支持，终端收到命令不需要请求server，直接将通道切换到本地通道

    START_AIRVIEWER(25),   //启动AirViewer

    /**
     * Collect android logcat biz command.
     */
    COLLECT_LOGCAT_DETAIL(26),     //收集logcat

    /**
     * Refresh terminal map
     */
    REFRESH_TERMINAL_MAP(27),

    /**
     * Collect Skyhook logcat biz command.
     */
    COLLECT_LOGS_SKYHOOK(28), //收集Skyhook geolocation log

    /**
     * Get accessory profile
     */
    TERMINAL_ACCESSORY_PROFILE(29),

    /**
     * The terminal accessory operation bizCommand
     */
    TERMINAL_ACCESSORY_OPERATION(30),

    NAVIGO_COLLECT_LOGCAT(31),     //NaviGo收集logcat
    NAVIGO_COLLECT_LOGCAT_DETAIL(32),     //NaviGo收集logcat
    NAVIGO_COLLECT_CLIENT_LOG(33),

    TRADITIONAL_TERMINAL_OPERATION_CANCEL(34), //传统终端取消操作
    AIRVIEWER_APPOINTMENT_INFORM(35), //airviewer 预约授权的 拉取更新操作

    /**
     * Unknown biz command.
     */
    UNKNOWN(-1);            //未知命令

    BizCommand(int cmd) {
        this.cmd = (byte) cmd;
    }

    /**
     * The Cmd.
     */
    public final byte cmd;

    /**
     * To cmd biz command.
     *
     * @param b the b
     * @return the biz command
     */
    public static BizCommand toCMD(byte b) {
        if (b > 0 && b < values().length) return values()[b - 1];
        return UNKNOWN;
    }
}
