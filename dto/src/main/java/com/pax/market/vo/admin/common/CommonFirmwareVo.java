package com.pax.market.vo.admin.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Locale;

@Getter
@Setter
@Builder
public class CommonFirmwareVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String fmName;
    private String displayFileSize;
    private Boolean forceUpdate;
    private Date updatedDate;
    private String firmwareType;
    @JsonIgnore
    private Long fileSize;

    public String getDisplayFileSize() {
        long kb = 1024;
        long mb = kb * 1024;
        long gb = mb * 1024;

        if (fileSize == null || fileSize <= 0) {
            return "";
        }
        if (fileSize >= gb) {
            return String.format(Locale.US, "%.1f GB", (float) fileSize / gb);
        } else if (fileSize >= mb) {
            float f = (float) fileSize / mb;
            return String.format(Locale.US, f > 100 ? "%.0f MB" : "%.1f MB", f);
        } else if (fileSize >= kb) {
            float f = (float) fileSize / kb;
            return String.format(Locale.US, f > 100 ? "%.0f KB" : "%.1f KB", f);
        } else
            return String.format(Locale.US, "%d B", fileSize);
    }
}
