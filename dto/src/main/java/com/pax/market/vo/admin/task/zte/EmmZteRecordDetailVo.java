package com.pax.market.vo.admin.task.zte;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/11/28
 */
@Getter
@Setter
public class EmmZteRecordDetailVo extends BaseResponse {

    @Serial
    private static final long serialVersionUID = -192374221496759303L;
    private Long id;
    private String type;
    private Long customerId;
    private int deviceNumber;
    private int enrolledCount;
    private int approvedCount;
    private String rejectedReason;
    private String creator;
    private String operator;
    private Date createdDate;
    private Date updatedDate;
    
}
