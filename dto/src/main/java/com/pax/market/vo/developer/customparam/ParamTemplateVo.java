/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.developer.customparam;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 自定义参数模板信息 list使用
 * <AUTHOR>
 * @date 2022/5/6
 */
@Getter
@Setter
public class ParamTemplateVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String appName;
    private String name;
    private String description;
    private Date createdDate;
    private Date updatedDate;
}