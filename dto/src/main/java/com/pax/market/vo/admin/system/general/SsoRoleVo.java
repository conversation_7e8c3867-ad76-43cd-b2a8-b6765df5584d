/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.system.general;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Getter
@Setter
public class SsoRoleVo extends BaseResponse {
    private static final long serialVersionUID = -6968190693126737930L;
    private Long id;
    private String name;

}