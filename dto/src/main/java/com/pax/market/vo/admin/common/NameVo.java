package com.pax.market.vo.admin.common;

import com.pax.market.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 用于仅返回name的对象
 */
@Getter
@Setter
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class NameVo extends BaseResponse {

    private static final long serialVersionUID = 1L;

    private String name;
}
