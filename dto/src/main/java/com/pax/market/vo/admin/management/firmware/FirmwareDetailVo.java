package com.pax.market.vo.admin.management.firmware;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pax.market.constants.ProductTypeUtils;
import com.pax.market.dto.BaseResponse;
import com.pax.market.vo.admin.common.IdNameVo;
import com.pax.market.vo.admin.common.NameVo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class FirmwareDetailVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private IdNameVo factory;
    private List<ModelVo> modelList;
    private Boolean forceUpdate;
    private NameVo createdBy;
    private Date createdDate;
    private Date updatedDate;
    private String releaseNotes;
    private String firmwareType;
    private String fmStatus;
    private String fmName;
    private FirmwareFileVo fmFile;
    private List<FirmwareFileVo> fmDiffFileList;
    private Boolean specificMarket;
    private Boolean specificReseller;
    private List<FirmwareOrgVo> fmOrgList;
    private List<IdNameVo> publishMarketList;
    private String comment;
    private String productType;
    private String parentProductType;
    private String productTypeLabel;
    private String parentProductTypeLabel;

    @Getter @Setter
    public static class ModelVo extends BaseResponse {
        private static final long serialVersionUID = 1L;

        private Long id;
        private String name;
    }

    @Getter @Setter
    public static class FirmwareFileVo extends BaseResponse {
        private static final long serialVersionUID = 1L;

        private Long id;
        private String fileName;
        private String displayFileSize;
        @JsonIgnore
        private Long fileSize;
        private String fileId;
        private String md5;
        private String targetVersion;

        public String getDisplayFileSize() {
            long kb = 1024;
            long mb = kb * 1024;
            long gb = mb * 1024;

            if (fileSize == null || fileSize <= 0) {
                return "";
            }
            if (fileSize >= gb) {
                return String.format(Locale.US, "%.1f GB", (float) fileSize / gb);
            } else if (fileSize >= mb) {
                float f = (float) fileSize / mb;
                return String.format(Locale.US, f > 100 ? "%.0f MB" : "%.1f MB", f);
            } else if (fileSize >= kb) {
                float f = (float) fileSize / kb;
                return String.format(Locale.US, f > 100 ? "%.0f KB" : "%.1f KB", f);
            } else
                return String.format(Locale.US, "%d B", fileSize);
        }
    }

    @Getter @Setter
    public static class FirmwareOrgVo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;
        private IdNameVo reseller;
    }

    public String getParentProductType() {
        return ProductTypeUtils.getParentType(productType);
    }

    public String getProductTypeLabel() {
        return productTypeLabel;
    }

    public void setProductTypeLabel(String productTypeLabel) {
        this.productTypeLabel = productTypeLabel;
    }

    public String getParentProductTypeLabel() {
        return parentProductTypeLabel;
    }

    public void setParentProductTypeLabel(String parentProductTypeLabel) {
        this.parentProductTypeLabel = parentProductTypeLabel;
    }
}
