/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.task.subscription.app;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * 应用详情页设置栏相关设置信息
 * <AUTHOR>
 * @date 2022/8/12
 */
@Getter
@Setter
@Builder
public class AppSettingVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long appId;

    /** app-apk类型 参数/标准应用 **/
    private String apkType;
    private Boolean autoUpdate;
    private String osType;
    private Integer visualScope;
    private Long marketId;
    private String downloadAuthentication;
    private Boolean subscribed;


}