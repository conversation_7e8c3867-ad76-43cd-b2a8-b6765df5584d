/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.developer.app;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * 应用相关配置信息 ---目前是和增值服务相关的
 * <AUTHOR>
 * @date 2022/4/15
 */
@Getter
@Setter
public class AppVasSettingVo extends BaseResponse {

    private static final long serialVersionUID = 1L;
    /** 应用bizData上送到PAX Insight 状态（已创建，开启上送，关闭上送）*/
    private String syncBizDataStatus;

    private Boolean syncedSandboxData;
    private Boolean activeInsightSandbox;

    private Boolean vasCloudDataSupported;
    private Boolean vasCloudMsgSupported;
    private Boolean vasStacklySupported;

    private boolean cloudMsgEnabledMarketLevel;
    private boolean insightEnabledMarketLevel;
    private boolean cyberlabEnabledMarketLevel;
    private boolean stacklyEnabledMarketLevel;

    private boolean cloudMsgEnabledInDevCenter;

    private boolean showCyberlab;
    private boolean showCloudMsg;
    private boolean showStackly;

    private boolean stacklyAgreement = true;
    private boolean cloudMsgAgreement = true;
    private boolean cyberLabAgreement = true;
    private boolean insightAgreement = true;

}