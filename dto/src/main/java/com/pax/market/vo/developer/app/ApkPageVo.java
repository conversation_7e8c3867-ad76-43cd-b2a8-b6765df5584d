/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.developer.app;

import com.pax.market.dto.BaseResponse;

import java.util.List;

/**
 * The type Apk page info.
 *
 * @param <ApkVo> the type parameter
 */
public class ApkPageVo<ApkVo> extends BaseResponse {

    private static final long serialVersionUID = 1L;
    private List<ApkVo> list;
    private long totalCount;
    private int totalOnlineApkCount;

    /**
     * Instantiates a new Page info.
     */
    public ApkPageVo() {
    }

    /**
     * Instantiates a new Page info.
     *
     * @param list the list
     */
    public ApkPageVo(List<ApkVo> list) {
        this.list = list;
    }

    /**
     * Instantiates a new Page info.
     *
     * @param list       the list
     * @param totalCount the total count
     */
    public ApkPageVo(List<ApkVo> list, long totalCount) {
        this.list = list;
        this.totalCount = totalCount;
    }

    /**
     * Gets list.
     *
     * @return the list
     */
    public List<ApkVo> getList() {
        return list;
    }

    /**
     * Sets list.
     *
     * @param list the list
     */
    public void setList(List<ApkVo> list) {
        this.list = list;
    }

    /**
     * Gets total count.
     *
     * @return the total count
     */
    public long getTotalCount() {
        if (totalCount == 0 && list != null) {
            return list.size();
        }

        return totalCount;
    }

    /**
     * Sets total count.
     *
     * @param totalCount the total count
     */
    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * Gets total online apk count.
     *
     * @return the total online apk count
     */
    public int getTotalOnlineApkCount() {
        return totalOnlineApkCount;
    }

    /**
     * Sets total online apk count.
     *
     * @param totalOnlineApkCount the total online apk count
     */
    public void setTotalOnlineApkCount(int totalOnlineApkCount) {
        this.totalOnlineApkCount = totalOnlineApkCount;
    }
}
