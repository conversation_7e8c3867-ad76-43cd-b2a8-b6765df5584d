package com.pax.market.vo.admin.market;

import com.pax.market.dto.BaseResponse;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@Builder
public class SimpleMarketVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String domain;
    private String adminEmail;
    private Date createdDate;
    private Date expireDate;
    private AdminMarketSettingVo adminMarketSetting;


    @Getter
    @Setter
    public static class AdminMarketSettingVo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String logo;
    }
}
