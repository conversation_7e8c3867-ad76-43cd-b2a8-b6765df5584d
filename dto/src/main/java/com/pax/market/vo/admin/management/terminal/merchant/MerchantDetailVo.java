/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.management.terminal.merchant;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 商户详情
 * <AUTHOR>
 * @date 2022/9/21
 */
@Getter
@Setter
public class MerchantDetailVo extends BaseResponse {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private String name;
    private String email;
    private String contact;
    private String phone;
    private String country;
    private String province;
    private String city;
    private String area;
    private String address;
    private String postcode;
    private Date createdDate;
    private Date updatedDate;
    private String description;
    private String userStatus;
    private String status;

    private ResellerVo reseller;
    private List<MerchantCategoryVo> merchantCategoryList;
    private List<ResellerVo> parents;
    private Boolean createUserFlag;
    private Boolean usedOnlyInGroup;
    private Boolean existUserFlag;
    private LinkedHashMap<String, String> entityAttributeValues;



    @Getter
    @Setter
    public static class ResellerVo implements Serializable {

        @Serial
        private static final long serialVersionUID = -956087494578372842L;

        private Long id;
        private String name;
        private String status;
        private boolean emmAvailable;
    }

    @Getter
    @Setter
    public static class MerchantCategoryVo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
    }

}