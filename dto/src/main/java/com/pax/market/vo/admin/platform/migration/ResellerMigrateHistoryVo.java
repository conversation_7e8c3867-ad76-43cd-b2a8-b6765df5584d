package com.pax.market.vo.admin.platform.migration;

import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.UserInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ResellerMigrateHistoryVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String sourceMarketName;
    private String sourceResellerName;
    private String targetMarketName;
    private String targetResellerName;
    private String status;
    private String resultMessage;
    private UserInfo createdBy;
    private Date createdDate;
}
