package com.pax.market.vo.admin.service.airshield;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TerminalDetectionDataInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = -8647203784844458488L;
    private Long marketId;
    private Long terminalId;
    private Boolean isRooted;
    private Boolean isEmulatorEnv;
    private Boolean beHooked;
    private Boolean devDebugMode;
    private Boolean beCloned;
    private List<String> blackApps;
    private List<String> riskFiles;
    private String osVersion;
    private String deviceID;
    private Integer cellIpNum;
    private Integer wifiIpNum;
    private String androidVersion;
    private Integer androidCode;
    private Date syncTime;
}
