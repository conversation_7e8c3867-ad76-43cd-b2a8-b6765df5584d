/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.monitoring.alarm;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 当前可见告警类型以及接收方式
 * <AUTHOR>
 * @date 2022/6/7
 */
@Getter
@Setter
@Builder
public class AlarmTypeVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private String type;
    private String receiveType;
}