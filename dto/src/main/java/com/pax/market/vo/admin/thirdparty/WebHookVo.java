/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.thirdparty;

import com.pax.market.dto.BaseResponse;
import com.pax.market.vo.admin.common.specific.SpecificSimpleMarketVo;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/30
 */
@Getter
@Setter
public class WebHookVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String url;
    private String type;
    private String status;
    private Date createdDate;
    private Date updatedDate;

    private List<SpecificSimpleMarketVo> marketList;

}