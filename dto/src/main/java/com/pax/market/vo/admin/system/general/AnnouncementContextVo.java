/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.system.general;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
@Getter
@Setter
public class AnnouncementContextVo extends BaseResponse {
    private static final long serialVersionUID = 614004967369304472L;
    private Long id;
    private String title;
    private String content;
    private Date startDate;
    private String status;
    private Date createdDate;
    private Date expireDate;
    private String receiverType;


}