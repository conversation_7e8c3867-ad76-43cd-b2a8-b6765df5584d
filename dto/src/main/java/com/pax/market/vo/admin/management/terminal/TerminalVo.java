/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.management.terminal;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端列表 终端信息Vo
 * <AUTHOR>
 * @date 2022/10/9
 */
@Getter
@Setter
public class TerminalVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String TID;
    private String name;
    private String serialNo;
    private String status;
    private Integer onlineStatus;
    private String onlineTime;
    private Boolean cloudPushChannel;
    private Date createdDate;
    private Date updatedDate;
    private Date lastActiveTime;
    private Date lastAccessTime;
    private ModelVo model;
    private ResellerVo reseller;
    private MerchantVo merchant;


    @Getter
    @Setter
    public static class ModelVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
        private String productType;
        private FactoryVo factory;
    }


    @Getter
    @Setter
    public static class FactoryVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
    }



    @Getter
    @Setter
    public static class ResellerVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
        private String parentIds;
    }

    @Getter
    @Setter
    public static class MerchantVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
    }

}