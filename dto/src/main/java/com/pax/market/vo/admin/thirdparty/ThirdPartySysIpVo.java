/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.thirdparty;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/4/22
 */
@Getter
@Setter
public class ThirdPartySysIpVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String ip;
    private String startIp;
    private String endIp;
    private String type;
    private String description;
}