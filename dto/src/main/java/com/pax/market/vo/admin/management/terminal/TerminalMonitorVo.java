/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.management.terminal;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 获取终端详情仪表盘-监视信息
 * <AUTHOR>
 * @date 2022/11/17
 */
@Getter
@Setter
public class TerminalMonitorVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long terminalId;
    private Float cpuRatio;
    private Long ramUsed;
    private Long storageUsed;
    private Float battery;
    private String network;
    private String screenLock;
    private Date syncDate;
    private Boolean charging;

}