package com.pax.market.vo.admin.management.emm;

import com.pax.market.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmmAppConfigListVo extends BaseResponse {

    @Serial
    private static final long serialVersionUID = -3586193686207748593L;

    private List<EmmAppConfigSimpleVo> configList;

    @Getter
    @Setter
    public static class EmmAppConfigSimpleVo implements Serializable {
        @Serial
        private static final long serialVersionUID = 6567991714792958855L;
        private String name;
        private String mcmId;
    }

}
