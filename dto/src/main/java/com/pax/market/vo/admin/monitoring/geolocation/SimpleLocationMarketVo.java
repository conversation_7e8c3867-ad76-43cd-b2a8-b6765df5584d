/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.monitoring.geolocation;

import com.pax.market.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * 地图页需要返回的简单市场信息---供市场列表返回
 * <AUTHOR>
 * @date 2022/1/26
 */
@Getter
@Setter
@AllArgsConstructor
public class SimpleLocationMarketVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String name;
}
