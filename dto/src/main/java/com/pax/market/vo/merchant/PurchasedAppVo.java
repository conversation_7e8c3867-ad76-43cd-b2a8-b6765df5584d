package com.pax.market.vo.merchant;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Builder
public class PurchasedAppVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private String terminalSN;
    private String appName;
    private String apkIconFileId;
    private BigDecimal price;
    private Date purchaseDate;
    private Integer chargeType;
    private String currency;
}
