package com.pax.market.vo.admin.management.group.launcher;

import com.pax.market.vo.admin.management.group.BaseGroupPushTaskDetailVo;
import com.pax.market.vo.admin.management.group.apk.TerminalGroupApkParamVo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter @Setter @Accessors(chain = true)
public class TerminalGroupLauncherDetailVo extends BaseGroupPushTaskDetailVo {
    private static final long serialVersionUID = 1L;
    private ApkVo apk;
    private TerminalGroupApkParamVo groupApkParam;
    private Boolean apkAvailable;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class ApkVo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;
        private Long appId;
        private String appName;
        private String appStatus;
        private String status;
        private String packageName;
        private String versionName;
        private String developerNickName;
        private List<ModelVo> apkModelList;
        private String osType;
        private String displayFileSize;
        private String apkType;
        private String apkIconFileId;
        private Date updatedDate;
        List<String> paramTemplateNameList;
        private Boolean allowUpdateParamTemplate;
        private String apkSignatureStatus;
        private String message;
        private String apkFileType;
    }

    @Getter
    @Setter
    public static class ModelVo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String name;
    }
}
