/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.task.subscription.app;

import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.app.ApkSignatureInfo;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * apk详情页信息
 * <AUTHOR>
 * @date 2022/8/9
 */
@Getter
@Setter
@Builder
public class ApkDetailVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    /** app  */
    private Long appId;
    private String packageName;
    private Boolean subscribed;
    private Boolean launcherApp;
    private boolean distributionInWhiteList;


    /** apk */
    private Long id;
    private String status;
    private String displayFileSize;
    private Boolean allowUpdateParamTemplate;
    private String versionName;
    private Long versionCode;
    private String osType;
    private Date submitDate;
    private Date approvedDate;
    /** 上下线的申请状态 */
    private Integer statusRequest;
    /** 上下线申请时的评论 */
    private String statusRequestComment;
    private String apkIconFileId;
    private String message;

    /** 参数模板名称列表 **/
    private List<String> paramTemplateNameList;

    /** 获取label，有父节点的则组装成parentLabel-label **/
    private List<String> apkCategoryList;
    /** 可以只返回labelList **/
    private List<String> apkModelList;


    /** apkFile*/
    private String minSdkVersion;
    private Set<String> permissionList;
    private Set<String> paxPermissionList;
    private String signatureScheme;

    /** 开启多厂商时对应apk文件需要的厂商信息 */
    private List<ApkFileFactoryVo> apkFileFactoryList;


    /** 对应Detail表中的app名字 */
    private String appName;
    /** apkDetail*/
    private String screenshot0;
    private String screenshot1;
    private String screenshot2;
    private String screenshot3;
    private String screenshot4;
    private String shortDesc;
    private String description;
    private String releaseNotes;
    private String featuredImg;
    private String accessUrl;

    /** 签名失败的数量 **/
    private int signatureFailedCount;

    /** 是否定向发布至代理商 **/
    private Boolean specificReseller;
    //ReleaseNote上传附件
    private String attachment;
    private String attachmentName;

    /**
     * apk多厂商需要的制造商对应信息
     */
    @Getter
    @Setter
    @Builder
    public static class ApkFileFactoryVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long factoryId;
        private String factoryName;
    }

}