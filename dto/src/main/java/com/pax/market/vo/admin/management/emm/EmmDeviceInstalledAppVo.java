package com.pax.market.vo.admin.management.emm;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/8/7
 */
@Getter
@Setter
public class EmmDeviceInstalledAppVo extends BaseResponse {

    @Serial
    private static final long serialVersionUID = 5167072190745351258L;

    private Long id;
    private Long terminalId;
    private String name;
    private String version;
    private String type;
    private Long size;
    private String iconUrl;
    private Date installTime;
    private Date lastTimeUpdate;
    private Boolean isLauncher;
    private Boolean isDefaultLauncher;

}
