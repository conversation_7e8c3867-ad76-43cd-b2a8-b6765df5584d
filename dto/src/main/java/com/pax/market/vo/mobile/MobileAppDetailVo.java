/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.mobile;

import com.pax.market.dto.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;

/**
 * admin app 详情
 */
@Getter
@Setter
@Builder
public class MobileAppDetailVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String packageName;
    private String name;
    @Schema(name = "downloads", description = "下载量")
    private Long downloads;
    @Schema(name = "apkType", description = "N:标准应用，P:参数应用")
    private String apkType;
    private String status;
    @Schema(name = "chargeType", description = "0:免费，1:安次数收费，2:按月收费")
    private Integer chargeType;
    @Schema(name = "price", description = "价格,有小数位，可能为null，当免费时(chargeType=0)，这个值为null, ", nullable = true, example = "9.9")
    private BigDecimal price;

    private String currency;
    @Schema(name = "autoUpdate", description = "应用自动更新")
    private boolean autoUpdate;
    @Schema(name = "allowAppAttribute", description = "是否允许添加app属性")
    private boolean allowAppAttribute;
    private LinkedHashMap<String, String> entityAttributeValues;
    private MobileSimpleDeveloperVo developer;
    @Schema(name = "vasCloudMsgSupported", description = "消息推送服务")
    private boolean vasCloudMsgSupported;
    @Schema(name = "vasCloudDataSupported", description = "GoInsight服务")
    private boolean vasCloudDataSupported;
    @Schema(name = "vasStacklySupported", description = "Stackly服务")
    private boolean vasStacklySupported;
    @Schema(name = "freeTrialDay", description = "免费试用天数,null或0表示无免费试用天数", nullable = true)
    private Integer freeTrialDay;
    private AppCostVo appCost;


    @Getter
    @Setter
    @Builder
    @Schema(name = "developer", description = "开发者信息,没有开发者时，返回null", nullable = true)
    public static class MobileSimpleDeveloperVo implements Serializable {
        private static final long serialVersionUID = 1L;
        @Schema(name = "nickname", description = "开发者")
        private String nickname;
        @Schema(name = "email", description = "邮箱")
        private String email;

    }

    @Getter
    @Setter
    @Builder
    public static class AppCostVo implements Serializable {
        @Serial
        private static final long serialVersionUID = -2645043793612006644L;
        private boolean paid;
        private Integer chargeType;
        private String text;
        private String currency;
        private BigDecimal price;
        private Integer freeTrialDay;
    }
}