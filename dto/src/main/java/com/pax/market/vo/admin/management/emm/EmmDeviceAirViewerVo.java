package com.pax.market.vo.admin.management.emm;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
@Builder
public class EmmDeviceAirViewerVo extends BaseResponse {

    @Serial
    private static final long serialVersionUID = -7023383145594436444L;

    private Long terminalId;
    private Boolean installedAirViewer;
    private Boolean lowerVersionAirViewer;
    private Boolean allowInsight;
    private Boolean airViewerEncrypted;
    private Boolean allowAirViewer;
    private Boolean allowConnection;
    private Boolean unattendedModel;
}
