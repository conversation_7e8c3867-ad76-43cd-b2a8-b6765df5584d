package com.pax.market.vo.admin.service.airshield;

import com.pax.market.dto.BaseResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AirShieldDetectRules extends BaseResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 5674694959934648564L;
    private Long marketId;

    private Long terminalId;

    private String deviceId;

    private String androidVersion;
    private Integer androidCode;

    private String osVersion;

    private List<String> blackApps;

    private List<String> limitSysFilePaths;

    private Integer interval;

    private Integer cellIPLimitNum = 3;
    private Integer wifiIPLimitNum = 10;

    private Boolean rootLimit = false;
    private Boolean emulatorEnvLimit = false;
    private Boolean hookLimit = false;
    private Boolean devDebugModeLimit = false;
    private Boolean clonedLimit = false;


}
