package com.pax.market.vo.developer.sandbox;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Builder
public class SandboxTerminalApkVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String apkVersionName;
    private Long apkVersionCode;
    private Long apkId;
    private String terminalName;
    private String serialNo;
    private String apkParamName;
    private Date createdDate;
    private String status;
    private Date updatedDate;
    private Integer actionStatus;
    private int errorCode;
    private String errorMessage;
    private String remarks;
    private String modelName;
    private String factoryName;
    private SandboxTerminalApkParamVo terminalApkParam;
}
