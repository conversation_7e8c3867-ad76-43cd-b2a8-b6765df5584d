package com.pax.market.vo.admin.platform.configuration;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: <PERSON>
 * @Date: 2023/1/6 15:20
 */
@Getter
@Setter
public class LoginConfigVo extends BaseResponse {
    private static final long serialVersionUID = -7647611708297046235L;

    private Boolean allowConcurrentLogin;
    private Boolean allowLockUserIfPasswdMismatch;
    private Boolean allowSuspendUserBySystem;
    private Integer numOfFailedTrysToLockUser;
    private Integer minutesToActivateLockedUser;
    private Integer daysToSuspendUser;
}
