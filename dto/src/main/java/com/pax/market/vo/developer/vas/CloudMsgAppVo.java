/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.developer.vas;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * cloud msg-应用相关数据
 * <AUTHOR>
 * @date 2023/3/28
 */
@Getter
@Setter
@Accessors(chain = true)
public class CloudMsgAppVo extends BaseResponse {
    private static final long serialVersionUID = -431155318513097297L;
    private Long id;
    private String apkIconFileId;
    private String name;
    private String packageName;
    private Integer taskPendingCount;
    private Integer arrivalMessageCount;
    private String versionName;
    private Date updatedDate;



}