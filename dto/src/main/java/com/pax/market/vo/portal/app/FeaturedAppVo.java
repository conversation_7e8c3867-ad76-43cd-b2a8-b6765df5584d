/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.portal.app;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * portal模块返回需要的精品应用信息
 * <AUTHOR>
 * @date 2022/1/27
 */
@Getter
@Setter
@Builder
public class FeaturedAppVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Integer chargeType;
    private BigDecimal price;
    private String currency;
    private String appName;
    private String featuredImg;
    private String screenshot0;
    private List<String> apkCategoryList;
    private String apkIconFileId;
}
