package com.pax.market.vo.admin.platform.market;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class MarketInfoSummaryVo extends BaseResponse {
    private static final long serialVersionUID = 1L;

    private MarketNumVo marketNum;
    private ApplicationNumVo applicationNum;
    private DeveloperNumVo developerNum;
    private TerminalNumVo terminalNum;

    @Getter @Setter
    public static class MarketNumVo extends BaseResponse {
        private static final long serialVersionUID = 1L;

        private int marketNum;
        private int expireMarketNum;
    }

    @Getter @Setter
    public static class ApplicationNumVo extends BaseResponse {
        private static final long serialVersionUID = 1L;

        private int total;
        private int onlineNum;
    }

    @Getter @Setter
    public static class DeveloperNumVo extends BaseResponse {
        private static final long serialVersionUID = 1L;

        private int total;
        private int monthlyNum;
    }

    @Getter @Setter
    public static class TerminalNumVo extends BaseResponse {
        private static final long serialVersionUID = 1L;

        private int total;
        private int activeNum;
    }
}
