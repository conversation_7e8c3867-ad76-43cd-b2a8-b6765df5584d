/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.management.model;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 厂商定向发布给市场-市场信息
 * <AUTHOR>
 * @date 2022/11/28
 */
@Getter
@Setter
public class SpecificMarketVo extends BaseResponse {
    private static final long serialVersionUID = -7883903507330621467L;
    private Long id;
    private String name;
    private String domain;
    private String logo;
    private Date createdDate;

}