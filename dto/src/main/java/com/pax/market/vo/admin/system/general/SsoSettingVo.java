/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.vo.admin.system.general;

import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.sso.SsoOpenIdSettingInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description sso setting detail
 * @Author: Shawn
 * @Date: 2020/3/11
 */

@Getter
@Setter
public class SsoSettingVo extends BaseResponse {
    private static final long serialVersionUID = -1214879825259389914L;
    private Long id;
    private Long marketId;
    private String ssoType;
    private SsoOpenIdSettingVo ssoOpenIdSetting;


    @Getter
    @Setter
    public static class SsoOpenIdSettingVo implements Serializable {
        private static final long serialVersionUID = 9105878776104009329L;
        private String authorizationUrl;
        private String tokenUrl;
        private String clientId;
        private String clientSecret;
        private String issuer;
        private String defaultScopes;
        private String forwardedQueryParameters;
        private String encryption;
        private String jwksUrl;
        private Long defaultRole;
        private String authMethod;

    }
}
