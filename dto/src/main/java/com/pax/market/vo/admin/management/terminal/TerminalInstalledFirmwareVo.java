/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.management.terminal;

import com.pax.market.dto.BaseResponse;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@<PERSON><PERSON>
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TerminalInstalledFirmwareVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private String name;
    private String resourceName;
    private Date installTime;
    private String modemName;


}