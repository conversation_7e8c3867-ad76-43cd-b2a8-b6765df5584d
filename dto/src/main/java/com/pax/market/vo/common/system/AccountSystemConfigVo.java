/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.common.system;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * account模块用到的系统配置
 * <AUTHOR>
 * @date 2022/4/2
 */
@Getter
@Setter
@Builder
public class AccountSystemConfigVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private boolean allowUserChangeEmail;
}