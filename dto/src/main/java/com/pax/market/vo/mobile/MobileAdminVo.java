package com.pax.market.vo.mobile;

import com.pax.market.dto.BaseResponse;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/5 20:27
 */
@Data
public class MobileAdminVo extends BaseResponse {
    @Schema(name = "isMobileAppAdmin", description = "是否是mobile app admin", type = "boolean")
    private boolean isMobileAppAdmin;
    @ArraySchema(schema = @Schema(implementation = Long.class, name = "list", description = "等待用户同意的协议列表"))
    private List<Long> agreements;
}
