/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.common.app;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端/分组管理模块 应用参数模板信息
 * <AUTHOR>
 * @date 2022/10/11
 */
@Getter
@Setter
public class ApkParameterVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String name;
    private String paramTemplateName;
    private Date updatedDate;
    private ApkVo apk;


    @Getter
    @Setter
    public static class ApkVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private Long appId;
        private String appName;
        private String versionName;
        private String displayFileSize;

    }

}