/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.management.terminal.log;

import com.pax.market.dto.BaseResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 终端推送历史vo
 * <AUTHOR>
 * @date 2022/10/13
 */
@Getter
@Setter
@Builder
public class TerminalPushHistoryVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String api;
    private Date pushTime;

}