/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.admin.task.activity;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/19
 */
@Getter
@Setter
public class ActivityVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String type;
    private String name;
    private String status;
    private String fileName;
    private Date createdDate;
    private Date updatedDate;
}