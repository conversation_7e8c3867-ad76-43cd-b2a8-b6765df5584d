/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.vo.mobile;

import com.pax.market.dto.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 开发者详情信息
 * <AUTHOR>
 * @date 2022/8/22
 */
@Getter
@Setter
@Builder
public class MobileDeveloperDetailVo extends BaseResponse {
    private static final long serialVersionUID = 1L;
    @Schema(name = "id", description = "id")
    private Long id;
    String SUSPEND = "S";
    @Schema(name = "status", description = "状态", example = "P:待审核、A：已审核、R：已拒绝、S：已停用")
    private String status;
    @Schema(name = "realName", description = "真实姓名")
    private String realName;
    @Schema(name = "nickname", description = "昵称")
    private String nickname;
    @Schema(name = "email", description = "邮箱")
    private String email;
    @Schema(name = "country", description = "开发者国家", example = "CN：中国")
    private String country;
    @Schema(name = "area", description = "开发者国家", example = "CN：中国")
    private String area;
    @Schema(name = "idType", description = "证件类型", example = "I：身份证, P：护照")
    private String idType;
    @Schema(name = "idCardNo", description = "证件号码", example = "CN：中国")
    private String idCardNo;
    @Schema(name = "idCardFrontImg", description = "身份证正面照片")
    private String idCardFrontImg;
    @Schema(name = "phone", description = "电话")
    private String phone;
    @Schema(name = "companyName", description = "公司名称")
    private String companyName;
    @Schema(name = "companyAddr", description = "公司地址")
    private String companyAddr;
    @Schema(name = "companyWebsite", description = "公司网址")
    private String companyWebsite;
    @Schema(name = "developerType", description = "开发者类型", example = "E：企业开发者，I：个人开发者")
    private String developerType;
    @Schema(name = "companyRegistrationNumber", description = "公司商业注册编号")
    private String companyRegistrationNumber;
    /**
     * 拒绝原因
     **/
    @Schema(name = "rejectReason", description = "审核不通过原因")
    private String rejectReason;
    /** 注册原因 **/
    @Schema(name = "reason", description = "注册原因")
    private String reason;
    @Schema(name = "submitDate", description = "提交日期")
    private Date submitDate;
    @Schema(name = "approveDate", description = "审核通过日期")
    private Date approveDate;

    /** 权限相关 **/
    private Boolean allow3rdAccess;
    private Boolean allowIndustrySolution;

    /** 开发者是否定向发布到代理商 **/
    private Boolean specificReseller;
    private String developerPermission;
    private String thirdApiUrl;

    private DeveloperPaymentVo developerPayment;
    private UserVo user;
    /** 定向发布代理商列表 **/
    private List<ResellerVo> resellerList;



    @Getter
    @Setter
    @Builder
    public static class UserVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String photo;
        private String name;
        private String email;

    }

    @Getter
    @Setter
    @Builder
    public static class DeveloperPaymentVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String status;
        private Date paymentDate;
        private String paymentType;
        private String voucherFileId;
        private String invoiceNo;
        private String comment;
    }

    @Getter
    @Setter
    @Builder
    public static class ResellerVo implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
        private String company;
        private String contact;
        private String status;
    }


}