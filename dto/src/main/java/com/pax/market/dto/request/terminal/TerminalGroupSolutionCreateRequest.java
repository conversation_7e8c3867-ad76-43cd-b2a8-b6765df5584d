/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.terminal;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * 分组创建solution 推送请求
 * <AUTHOR>
 * @date 2023/3/31
 */
@Getter
@Setter
public class TerminalGroupSolutionCreateRequest extends BaseRequest {
    private static final long serialVersionUID = -7722473746826650809L;
    private Long groupId;
    private Set<Long> apkIdList;
    private Set<Long> apkParameterIdList;
}