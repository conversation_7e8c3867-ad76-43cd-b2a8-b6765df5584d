/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.parameter;

import jakarta.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 参数模板分组DTO
 *
 * <AUTHOR>
 * @date 16 /9/12
 */
@SuppressWarnings("restriction")
public class GroupInfo implements Comparable<GroupInfo>, Serializable {
    private static final long serialVersionUID = -338843168199387385L;
    private String id = "";
    private String title;
    private String singleTitle;
    private int order;
    private String description;
    private List<HeaderInfo> headerList = new ArrayList<HeaderInfo>();
    private List<ParameterInfo> parameterList = new ArrayList<ParameterInfo>();

    /**
     * Gets id.
     *
     * @return the id
     */
    @XmlElement(name = "ID")
    public String getId() {
        return id;
    }

    /**
     * Sets id.
     *
     * @param id the id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * Gets title.
     *
     * @return the title
     */
    @XmlElement(name = "Title")
    public String getTitle() {
        return title;
    }

    /**
     * Sets title.
     *
     * @param title the title
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * Gets single title.
     *
     * @return the single title
     */
    public String getSingleTitle() {
        return singleTitle;
    }

    /**
     * Sets single title.
     *
     * @param singleTitle the single title
     */
    public void setSingleTitle(String singleTitle) {
        this.singleTitle = singleTitle;
    }

    /**
     * Gets order.
     *
     * @return the order
     */
    @XmlElement(name = "Order")
    public int getOrder() {
        return order;
    }

    /**
     * Sets order.
     *
     * @param order the order
     */
    public void setOrder(int order) {
        this.order = order;
    }

    /**
     * Gets description.
     *
     * @return the description
     */
    @XmlElement(name = "Description")
    public String getDescription() {
        return description;
    }

    /**
     * Sets description.
     *
     * @param description the description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Gets parameter list.
     *
     * @return the parameter list
     */
    @XmlElement(name = "Parameter")
    public List<ParameterInfo> getParameterList() {
        return parameterList;
    }

    /**
     * Sets parameter list.
     *
     * @param parameterList the parameter list
     */
    public void setParameterList(List<ParameterInfo> parameterList) {
        this.parameterList = parameterList;
    }

    /**
     * Gets header list.
     *
     * @return the header list
     */
    @XmlElement(name = "Header")
    public List<HeaderInfo> getHeaderList() {
        return headerList;
    }

    /**
     * Sets header list.
     *
     * @param headerList the header list
     */
    public void setHeaderList(List<HeaderInfo> headerList) {
        this.headerList = headerList;
    }

    /**
     * Add parameter.
     *
     * @param parameter the parameter
     */
    public void addParameter(ParameterInfo parameter) {
        if (parameter == null) {
            return;
        }

        parameterList.add(parameter);
    }

    /**
     * Add header.
     *
     * @param header the header
     */
    public void addHeader(HeaderInfo header) {
        if (header == null) {
            return;
        }

        headerList.add(header);
    }

    public int compareTo(GroupInfo groupInfo) {
        if (this.getOrder() > groupInfo.getOrder()) {
            return 1;
        } else if (this.getOrder() < groupInfo.getOrder()) {
            return -1;
        } else {
            return this.getId().compareTo(groupInfo.getId());
        }
    }
}
