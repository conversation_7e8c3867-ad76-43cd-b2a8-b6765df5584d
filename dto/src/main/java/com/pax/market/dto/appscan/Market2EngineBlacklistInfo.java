package com.pax.market.dto.appscan;

import com.pax.market.dto.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/23 10:25
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class Market2EngineBlacklistInfo extends BaseResponse {
    private static final long serialVersionUID = -9036222502981911238L;
    private Long engineId;
    private String engineName;
    private List<EngineMarketInfo> markets;
}
