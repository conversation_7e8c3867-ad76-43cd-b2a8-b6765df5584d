/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: TopicExtInfo.
 *
 * Revision History:
 * Date	                	Author	            	Action
 * 20181210  	        	jianrong           		Create
 * ===========================================================================================
 */
package com.pax.market.dto.notification;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TopicExtInfo extends BaseResponse {

    private static final long serialVersionUID = -1862994071711678040L;

    private Long id;
    private String title;
    private String description;
    private int category;
    private Long externalId;
    private String iconUrl;
    private String market;
}
