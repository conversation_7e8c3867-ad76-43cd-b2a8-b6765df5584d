package com.pax.market.dto.billing;

import com.pax.market.dto.*;

import com.pax.market.dto.vas.AirLinkDataPoolPriceSettingInfo;
import com.pax.market.dto.vas.AirLinkDataPoolSettingInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/1
 */
@Getter
@Setter
@NoArgsConstructor
public class MarketBillingDefaultPriceInfo implements Serializable {


    @Serial
    private static final long serialVersionUID = -2177184200484090220L;
    private String serviceType;

    private List<TerminalEnrollmentSettingInfo> terminalEnrollmentSettingRefPrices;

    private DiscountSettingInfo discountSetting;

    private MonthlyPackageSettingInfo monthlyPackageSettingRefPrice;

    private CyberLabBillingSettingInfo cyberLabSettingRefPrice;

    private AdUpBillingSettingInfo adUpSettingRefPrice;

    private List<CloudMsgPriceSettingInfo> cloudMsgRefPrices;
    private List<AirLinkDataPoolSettingInfo> airLinkRefPrices;

    private AirLinkDataPoolPriceSettingInfo airLinkDataPoolPriceSetting;
}
