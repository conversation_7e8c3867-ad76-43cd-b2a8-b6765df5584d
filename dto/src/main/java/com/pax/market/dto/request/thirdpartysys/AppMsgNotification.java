package com.pax.market.dto.request.thirdpartysys;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Notification message object")
public class AppMsgNotification implements Serializable {
    private static final long serialVersionUID = 1355870649761441140L;

    @Schema(name = "title", description = "The title of notification message, it is required if message type is notification of mixed type")
    private String title;

    @Schema(name = "content", description = "The content of notification message, it is required if message type is notification of mixed type")
    private String content;
}
