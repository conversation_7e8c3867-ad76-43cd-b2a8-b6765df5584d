package com.pax.market.dto.vas;

import com.pax.market.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 9.6
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VasAppInfo extends BaseResponse {
    @Serial
    private static final long serialVersionUID = -8591253222268458984L;
    private Long id;
    private Long apkId;
    private Integer mode;
    private String serviceStatus;
    private Integer trialNum;
    private Integer trialUsage;
    private Boolean specificReseller;
    private BigDecimal price;
}
