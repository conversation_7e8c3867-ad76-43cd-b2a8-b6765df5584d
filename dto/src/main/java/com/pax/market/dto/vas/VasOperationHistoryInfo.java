package com.pax.market.dto.vas;

import com.pax.market.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 9.2
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VasOperationHistoryInfo extends BaseResponse {

    @Serial
    private static final long serialVersionUID = 2231506939361611904L;
    private String serviceType; //服务类型
    private String action;  //操作动作
    private String name; //登录人名称
    private String fromType; //来源类型
    private String chargeVersion;
    private Date createdDate;


}
