/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.terminalstock;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * The type Terminal stock stock create request.
 *
 * <AUTHOR>
 * @date 2018 /9/5
 */
@Getter
@Setter
public class TerminalStockCreateRequest extends BaseRequest {

    private static final long serialVersionUID = 2401876002749114074L;
    private String serialNo;
    private Long modelId;
    private Long number;


}
