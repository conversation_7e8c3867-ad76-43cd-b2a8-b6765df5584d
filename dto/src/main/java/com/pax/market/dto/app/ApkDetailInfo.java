/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.app;

import com.pax.market.constants.ScreenShotType;
import com.pax.market.dto.BaseResponse;

/**
 * APK详情传输对象
 *
 * <AUTHOR>
 */
public class ApkDetailInfo extends BaseResponse {
    private static final long serialVersionUID = -1L;

    private Long apkId;
    private String appName;
    private String shortDesc;
    private String keyWords;
    private String description;
    private String releaseNotes;
    private String screenshot0;
    private String screenshot1;
    private String screenshot2;
    private String screenshot3;
    private String screenshot4;
    private String screenShotType;
    private String featuredImg;
    private String accessUrl;
    private String trackAlias;
    //Release note attachment
    private String attachment;
    private String attachmentName;

    /**
     * Gets apk id.
     *
     * @return the apk id
     */
    public Long getApkId() {
        return apkId;
    }

    /**
     * Sets apk id.
     *
     * @param apkId the apk id
     */
    public void setApkId(Long apkId) {
        this.apkId = apkId;
    }

    /**
     * Gets app name.
     *
     * @return the app name
     */
    public String getAppName() {
        return appName;
    }

    /**
     * Sets app name.
     *
     * @param appName the app name
     */
    public void setAppName(String appName) {
        this.appName = appName;
    }

    /**
     * Gets short desc.
     *
     * @return the short desc
     */
    public String getShortDesc() {
        return shortDesc;
    }

    /**
     * Sets short desc.
     *
     * @param shortDesc the short desc
     */
    public void setShortDesc(String shortDesc) {
        this.shortDesc = shortDesc;
    }

    /**
     * Gets key words.
     *
     * @return the key words
     */
    public String getKeyWords() {
        return keyWords;
    }

    /**
     * Sets key words.
     *
     * @param keyWords the key words
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
    }

    /**
     * Gets description.
     *
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets description.
     *
     * @param description the description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Gets release notes.
     *
     * @return the release notes
     */
    public String getReleaseNotes() {
        return releaseNotes;
    }

    /**
     * Sets release notes.
     *
     * @param releaseNotes the release notes
     */
    public void setReleaseNotes(String releaseNotes) {
        this.releaseNotes = releaseNotes;
    }

    /**
     * Gets screenshot 0.
     *
     * @return the screenshot 0
     */
    public String getScreenshot0() {
        return screenshot0;
    }

    /**
     * Sets screenshot 0.
     *
     * @param screenshot0 the screenshot 0
     */
    public void setScreenshot0(String screenshot0) {
        this.screenshot0 = screenshot0;
    }

    /**
     * Gets screenshot 1.
     *
     * @return the screenshot 1
     */
    public String getScreenshot1() {
        return screenshot1;
    }

    /**
     * Sets screenshot 1.
     *
     * @param screenshot1 the screenshot 1
     */
    public void setScreenshot1(String screenshot1) {
        this.screenshot1 = screenshot1;
    }

    /**
     * Gets screenshot 2.
     *
     * @return the screenshot 2
     */
    public String getScreenshot2() {
        return screenshot2;
    }

    /**
     * Sets screenshot 2.
     *
     * @param screenshot2 the screenshot 2
     */
    public void setScreenshot2(String screenshot2) {
        this.screenshot2 = screenshot2;
    }

    /**
     * Gets screenshot 3.
     *
     * @return the screenshot 3
     */
    public String getScreenshot3() {
        return screenshot3;
    }

    /**
     * Sets screenshot 3.
     *
     * @param screenshot3 the screenshot 3
     */
    public void setScreenshot3(String screenshot3) {
        this.screenshot3 = screenshot3;
    }

    /**
     * Gets screenshot 4.
     *
     * @return the screenshot 4
     */
    public String getScreenshot4() {
        return screenshot4;
    }

    /**
     * Sets screenshot 4.
     *
     * @param screenshot4 the screenshot 4
     */
    public void setScreenshot4(String screenshot4) {
        this.screenshot4 = screenshot4;
    }

    /**
     * Gets screen shot type.
     *
     * @return the screen shot type
     */
    public String getScreenShotType() {

        if (screenShotType == null) {
            screenShotType = ScreenShotType.VERTICAL;
        }

        return screenShotType;
    }

    /**
     * Sets screen shot type.
     *
     * @param screenShotType the screen shot type
     */
    public void setScreenShotType(String screenShotType) {
        this.screenShotType = screenShotType;
    }

    /**
     * Gets featured img
     * @return the featured img
     */
    public String getFeaturedImg() {
        return featuredImg;
    }

    /**
     * Sets featured img
     * @param featuredImg featuredImg the featured img
     */
    public void setFeaturedImg(String featuredImg) {
        this.featuredImg = featuredImg;
    }

    /**
     * Gets access url
     *
     * @return the access url
     */
    public String getAccessUrl() {
        return accessUrl;
    }

    /**
     * Sets access url
     *
     * @param accessUrl the access url
     */
    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }

    public String getTrackAlias() {
        return trackAlias;
    }

    public void setTrackAlias(String trackAlias) {
        this.trackAlias = trackAlias;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }
}
