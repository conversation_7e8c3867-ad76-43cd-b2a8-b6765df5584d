package com.pax.market.dto.request.vas;

import com.pax.market.dto.audit.AuditableBatchOperationAwareRequest;
import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Set;

@Getter
@Setter
public class AirLoadCardBatchRequest extends BaseRequest implements AuditableBatchOperationAwareRequest {

    @Serial
    private static final long serialVersionUID = -6551639956346546473L;
    private Set<Long> ids;

    @Override
    public Long[] getEntityIds() {
        Long[] longs = new Long[0];
        if (ids != null) {
            return ids.toArray(longs);
        }
        return longs;

    }
}