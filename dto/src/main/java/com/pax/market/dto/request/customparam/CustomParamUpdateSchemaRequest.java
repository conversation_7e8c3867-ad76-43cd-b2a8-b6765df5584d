/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.dto.request.customparam;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 自定义参数模板更新schema或者defaultValues
 * <AUTHOR>
 */
@Getter
@Setter
public class CustomParamUpdateSchemaRequest extends BaseRequest {

    private static final long serialVersionUID = 7325716681159841148L;
    private String schema;
    private Map<String, String> defaultValues;
}
