package com.pax.market.dto.audit.log.admin.vas;

import com.pax.market.dto.audit.log.BaseLog;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;

@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
@Getter
@Setter
public class AirLinkTerminalLog extends BaseLog{
    @Serial
    private static final long serialVersionUID = -6082180873032571447L;
    private String imei;
}
