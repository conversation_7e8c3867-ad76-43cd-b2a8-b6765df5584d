/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.dto.emm;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>" rel="nofollow">suyunlong</a>
 * @date 2024/11/15 
 */
@Getter
@Setter
public class EmmZteDeviceNumberInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = -4108830089893349031L;

    private String number;
    private String manufacturer;
    private String model;
}
