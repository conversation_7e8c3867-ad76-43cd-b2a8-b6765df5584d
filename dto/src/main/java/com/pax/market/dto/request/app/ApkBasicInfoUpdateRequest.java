/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.app;

import com.pax.market.dto.request.BaseRequest;
import com.pax.market.dto.ModelInfo;
import com.pax.market.dto.code.CodeInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * File Description
 *
 * <AUTHOR>
 * @date 16 /5/26
 */
@Getter
@Setter
public class ApkBasicInfoUpdateRequest extends BaseRequest {
    private String apkType;
    private List<CodeInfo> apkCategoryList;
    private String onlineType;
    private String onlineSchedule;
    private List<ModelInfo> apkModelList;
    private String baseType;
    private boolean isPublic;
    private String paramTemplateName;
}
