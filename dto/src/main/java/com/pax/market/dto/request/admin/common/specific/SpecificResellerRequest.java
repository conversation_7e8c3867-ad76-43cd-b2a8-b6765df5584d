/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.admin.common.specific;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * 定向发布代理商request
 * <AUTHOR>
 * @date 2022/8/12
 */
@Getter
@Setter
public class SpecificResellerRequest extends BaseRequest {
    private static final long serialVersionUID = -5861457706131362597L;
    private Set<Long> resellerIds;


}