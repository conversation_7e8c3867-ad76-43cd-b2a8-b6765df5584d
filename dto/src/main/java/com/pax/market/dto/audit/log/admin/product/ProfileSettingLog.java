package com.pax.market.dto.audit.log.admin.product;

import com.pax.market.dto.audit.log.BaseLog;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Getter
@Setter
public class ProfileSettingLog extends BaseLog {
    @Serial
    private static final long serialVersionUID = 1L;
    private String productType;
    private List<SettingLog> profileSettingList;

    @Getter @Setter
    public static class SettingLog implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        private String key;
        private String value;
        private String uiType;
    }



}
