package com.pax.market.dto.request.emm;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
@Getter
@Setter
public class EmmZteQuickUploadRecordCreateRequest extends BaseRequest  {

    @Serial
    private static final long serialVersionUID = -6774368176246973859L;

    private Long marketId;
    private Long resellerId;
    private Long merchantId;

    private String identifierType;
    private String manufacturer;
    private String model;

    private String numbers;
    private List<String> numberList;

}
