/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.dto.audit.log;

import com.pax.market.dto.audit.log.search.SearchFieldLog;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class BasicInfoAndSearchFieldLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private BasicInfoLog basicInfo;
    private SearchFieldLog searchField;
}
