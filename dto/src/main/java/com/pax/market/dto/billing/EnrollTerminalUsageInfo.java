package com.pax.market.dto.billing;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: dd
 * @Date: 2020/1/16
 * @Desc:
 */
@Getter
@Setter
@Accessors(chain = true)
public class EnrollTerminalUsageInfo extends BaseResponse {
    private static final long serialVersionUID = -5235927144112551150L;
    private int activeTerminal;
    private int year;
    private int month;
    private String snapshotFileId;
    private BigDecimal totalAmount;
    private List<EnrollTerminalBillInfo> debtorTop10;
    private boolean dataUpdating = false;

}
