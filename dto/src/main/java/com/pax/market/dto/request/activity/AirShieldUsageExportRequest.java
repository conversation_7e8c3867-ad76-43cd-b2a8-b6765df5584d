package com.pax.market.dto.request.activity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 1.0.0
 **/
@Getter
@Setter
public class AirShieldUsageExportRequest extends BaseExportRequest {
    @Serial
    private static final long serialVersionUID = -3142201705996137351L;
    private Long marketId;
    private Long resellerId;
    private String period;
    private boolean includeSubReseller;
    private boolean globalOperation;
    private boolean currentMonth;
}
