/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.client;

import com.pax.market.dto.request.BaseRequest;
import com.pax.market.dto.ModelInfo;

import java.util.List;

/**
 * Created by liukai on 2017/2/15.
 */
public class ClientApkUpdateRequest extends BaseRequest {

    private ClientAppUpdateRequest app;
    private boolean forceUpdate;            // 是否强制更新
    private String releaseNotes;            // 发布说明

    private List<ModelInfo> apkModelList;

    /**
     * Is force update boolean.
     *
     * @return the boolean
     */
    public boolean isForceUpdate() {
        return forceUpdate;
    }

    /**
     * Sets force update.
     *
     * @param forceUpdate the force update
     */
    public void setForceUpdate(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    /**
     * Gets release notes.
     *
     * @return the release notes
     */
    public String getReleaseNotes() {
        return releaseNotes;
    }

    /**
     * Sets release notes.
     *
     * @param releaseNotes the release notes
     */
    public void setReleaseNotes(String releaseNotes) {
        this.releaseNotes = releaseNotes;
    }

    /**
     * Gets app info.
     *
     * @return the app info
     */
    public ClientAppUpdateRequest getApp() {
        return app;
    }

    /**
     * Sets app info.
     *
     * @param app the app info
     */
    public void setApp(ClientAppUpdateRequest app) {
        this.app = app;
    }

    /**
     * Gets apk model list.
     *
     * @return the apk model list
     */
    public List<ModelInfo> getApkModelList() {
        return apkModelList;
    }

    /**
     * Sets apk model list.
     *
     * @param apkModelList the apk model list
     */
    public void setApkModelList(List<ModelInfo> apkModelList) {
        this.apkModelList = apkModelList;
    }

}
