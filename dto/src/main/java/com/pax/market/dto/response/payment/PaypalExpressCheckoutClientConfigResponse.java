/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.dto.response.payment;

import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
 * @date Oct 30, 2017
 */
public class PaypalExpressCheckoutClientConfigResponse implements Serializable {
    private static final long serialVersionUID = 2205990692348867292L;
    private String mode;
    private String clientID;

    public String getClientID() {
        return clientID;
    }

    public void setClientID(String clientID) {
        this.clientID = clientID;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
}
