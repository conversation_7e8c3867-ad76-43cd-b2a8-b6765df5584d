package com.pax.market.dto.billing;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2021/7/14 15:31
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MarketTerminalEnrollItemInfo implements Serializable {
    private Long id;
    private BigDecimal quantity;
    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private String type;
}
