/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.dto.audit.log.admin.general.setting;

import com.pax.market.dto.audit.log.BaseLog;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class MarketSignatureConfigServerLog extends BaseLog {
    private static final long serialVersionUID = 1L;

    private Boolean customSignature;
    private String certificateName;
    private String url;
    private String accessKey;
    private String accessSecret;
    private String password;
    private String publicCertificateName;
    private Integer authenticationMode;
    private Boolean paxPuk;
    private Boolean paxPuk4096;
    private Boolean isRootReseller;
    private String provider;
    private String signType;
    private Boolean allowUseGlobalSignature;
    private Boolean signatureSupport;
    private FactoryPukLog factoryPuk;

    @Getter
    @Setter
    public static class FactoryPukLog implements Serializable {
        private static final long serialVersionUID = 6919196314213222057L;
        private String pukCertificateName;
    }
}
