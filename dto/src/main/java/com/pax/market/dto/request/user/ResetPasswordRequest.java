/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.user;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * File Description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ResetPasswordRequest extends BaseRequest {
    private static final long serialVersionUID = -493584812917966669L;
    private String email;
    private String registerCode;
    private String token;
    private String plainPassword;
}
