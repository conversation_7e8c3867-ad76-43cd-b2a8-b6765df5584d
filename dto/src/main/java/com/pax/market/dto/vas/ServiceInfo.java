/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto.vas;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ServiceInfo implements Serializable {

    private static final long serialVersionUID = -1263375019022089435L;

    private String serviceName;
    private String serviceType;
    private boolean enabled;
    private boolean vasEnabled;
    private String icon;
    private String status;
    private String description;
    private String serviceStatus;
}
