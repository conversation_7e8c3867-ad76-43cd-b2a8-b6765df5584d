/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * The type Merchant user info.
 */
@Getter
@Setter
public class MerchantUserInfo extends BaseResponse {

    private static final long serialVersionUID = 484604187106693404L;
    private String name;
    private String loginName;
    private String userStatus;
    private boolean optEnabled;

}