/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto.request.terminal;

import com.pax.market.dto.request.BaseRequest;

/**
 * 终端分组应用APK参数添加Dto
 *
 * <AUTHOR>
 */
public class GroupApkParamCreateRequest extends BaseRequest {

    private static final long serialVersionUID = -4160926379296842706L;
    private Long groupApkParamId;

    /**
     * Gets group apk param id.
     *
     * @return the group apk param id
     */
    public Long getGroupApkParamId() {
        return groupApkParamId;
    }

    /**
     * Sets group apk param id.
     *
     * @param groupApkParamId the group apk param id
     */
    public void setGroupApkParamId(Long groupApkParamId) {
        this.groupApkParamId = groupApkParamId;
    }
}