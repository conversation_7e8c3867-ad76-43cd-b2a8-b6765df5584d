/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.dto.parameter.CustomParamSchema;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;


/**
 * @Description
 * @Author: Shawn
 * @Date: 2019/7/29 16:49
 * @Version 1.0
 */

@JsonPropertyOrder({"Groups", "Files", "Parameters"})
@XmlRootElement(name = "Schema")
public class SchemaDto implements Serializable {
    private static final long serialVersionUID = 151929278033834480L;


    private GroupsDto Groups;
    private FilesDto Files;
    private ParametersDto Parameters;

    @JsonProperty("Groups")
    @XmlElement(name = "Groups")
    public GroupsDto getGroups() {
        return Groups;
    }

    public void setGroups(GroupsDto groups) {
        Groups = groups;
    }

    @JsonProperty("Files")
    @XmlElement(name = "Files")
    public FilesDto getFiles() {
        return Files;
    }

    public void setFiles(FilesDto files) {
        Files = files;
    }

    @JsonProperty("Parameters")
    @XmlElement(name = "Parameters")
    public ParametersDto getParameters() {
        return Parameters;
    }

    public void setParameters(ParametersDto parameters) {
        Parameters = parameters;
    }

}