/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.dto.audit.log.admin;

import com.pax.market.dto.audit.log.BaseLog;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ClientApkSpecificResellerLog extends BaseLog {
    @Serial
    private static final long serialVersionUID = 1L;

    private List<String> resellerNames;
}
