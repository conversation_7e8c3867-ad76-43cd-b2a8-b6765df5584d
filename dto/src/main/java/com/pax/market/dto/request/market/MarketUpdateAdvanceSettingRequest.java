package com.pax.market.dto.request.market;

import com.pax.market.constants.MarketUsbAndDeveloperModeStatus;
import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * 2 * @Author: <PERSON>
 * 3 * @Date: 2020/5/22 14:11
 * 4
 */
@Getter
@Setter
public class MarketUpdateAdvanceSettingRequest extends BaseRequest {
    private static final long serialVersionUID = 1L;

    private Boolean allowPaymentSecurityControl = Boolean.FALSE;
    private Boolean allowPaymentSecurityControlWhitelist = Boolean.FALSE;
    private Boolean allowIpWhitelist = Boolean.FALSE;
    private Boolean allowStaticIp = Boolean.FALSE;
    private Boolean allowUnattendedModelWhitelist=Boolean.FALSE;
    private Boolean allowPrinterWhitelist = Boolean.FALSE;
    private Boolean allowResellerPuk = Boolean.FALSE;
    private Boolean allowResellerSignature = Boolean.FALSE;
    private Boolean allowMultipleApkFiles = Boolean.FALSE;
    private Boolean paxPuk = Boolean.FALSE;
    private Boolean paxPuk4096 = Boolean.FALSE;
    private Boolean allowRKI = Boolean.FALSE;
    private Boolean allowAppInstallWhitelist = Boolean.FALSE;
    private Boolean allowWifiBlacklist = Boolean.FALSE;
    private String terminalUsbAndDeveloperMode = MarketUsbAndDeveloperModeStatus.DISABLE;
    private Long rkiServerId;
    private Boolean allowTerminalConfigProxy;
    private Boolean allowWifiLanStaticIp = Boolean.FALSE;
    private Boolean allowUniqueCredentialPerDevice = Boolean.FALSE;
    private Boolean allowMqtt = Boolean.FALSE;
    private Boolean allowWebHook = Boolean.TRUE;
    private Boolean allowTechnicalSupport = Boolean.TRUE;
    private Boolean allowFileVirusScan = Boolean.FALSE;
    private Boolean allowMarketPuk = Boolean.FALSE;
    private Boolean allowThirdPartySystem = Boolean.FALSE;
    private Boolean allowTerminalSystemConfig = Boolean.FALSE;
    private Boolean allowDynamicGroup = Boolean.FALSE;
    private Boolean allowMpush = Boolean.FALSE;
    private Boolean allowAppFirmwareSpecific = Boolean.FALSE;
    private Boolean allowIndustrySolution = Boolean.FALSE;
    private Boolean allowGeoLocation = Boolean.FALSE;
    private Boolean allowGeoFence = Boolean.FALSE;
    private Boolean allowGeolocationCalibration = Boolean.FALSE;
    private Integer geolocationCalibrationLimit;
    private Boolean allowOperationControl = Boolean.FALSE;
    private Boolean allowAdvanceSetting = Boolean.FALSE;
    private Boolean allowResellerLogin = Boolean.FALSE;
    private Boolean allowMobileApp = Boolean.FALSE;
    private Boolean allowQueryTerminalByOnlineStatus = Boolean.FALSE;
    private Boolean allowBoundariesGeofencing = Boolean.FALSE;
    private Boolean allowAppCostSpecific = Boolean.FALSE;
    private Boolean allowPushPaidApp = Boolean.FALSE;
    private Boolean allowSmartAssistant = Boolean.FALSE;
    private Boolean allowGroupPushLimit = Boolean.TRUE;
    private Boolean allowPci7Signature = Boolean.FALSE;
    private Boolean allowPushTaskApproval = Boolean.FALSE;
    private Integer firmwareUpdateMinBatteryLevel = 50;
    private Boolean allowTerminalWhiteList = Boolean.FALSE;
    private Boolean allowUploadLocalParameter = Boolean.FALSE;
}
