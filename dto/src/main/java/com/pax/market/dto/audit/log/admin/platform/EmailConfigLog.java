/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.dto.audit.log.admin.platform;

import com.pax.market.dto.audit.log.BaseLog;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class EmailConfigLog extends BaseLog {

    @Serial
    private static final long serialVersionUID = 1L;

    private String host;
    private int port;
    private String authUser;
    private String authPassword;
    private String from;
    private String fromName;
    private String reply;
    private String replyName;
    private int securityMode;
}
