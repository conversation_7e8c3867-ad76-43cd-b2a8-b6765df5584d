/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.code;

import java.io.Serializable;

/**
 * Created by gubin_ on 2016/12/7.
 */
public class CodeLangInfo implements Serializable {

    private static final long serialVersionUID = -242484635031259859L;

    private String langCode;

    private String label;

    private String action;

    private Long parentId;

    private Long id;


    /**
     * Gets lang code.
     *
     * @return the lang code
     */
    public String getLangCode() {
        return langCode;
    }

    /**
     * Sets lang code.
     *
     * @param langCode the lang code
     */
    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    /**
     * Gets label.
     *
     * @return the label
     */
    public String getLabel() {
        return label;
    }

    /**
     * Sets label.
     *
     * @param label the label
     */
    public void setLabel(String label) {
        this.label = label;
    }

    /**
     * Gets action.
     *
     * @return the action
     */
    public String getAction() {
        return action;
    }

    /**
     * Sets action.
     *
     * @param action the action
     */
    public void setAction(String action) {
        this.action = action;
    }


    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
