package com.pax.market.dto.request.admin.task.app;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Set;

@Getter
@Setter
public class AppCostSpecificResellerRequest extends BaseRequest {

    @Serial
    private static final long serialVersionUID = 8307539683748797982L;

    private Set<Long> resellerIds;
    private Long resellerId;
    private Long templateId;
}
