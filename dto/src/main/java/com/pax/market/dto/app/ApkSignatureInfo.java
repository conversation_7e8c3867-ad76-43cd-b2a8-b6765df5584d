/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.app;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * The type Apk signature info.
 */
@Getter
@Setter
public class ApkSignatureInfo extends BaseResponse {
    private static final long serialVersionUID = -1L;

    private Long factoryId;
    private String factoryName;
    private String signatureStatus;
    private String message;
    private boolean resignable;
    private String signType;

}
