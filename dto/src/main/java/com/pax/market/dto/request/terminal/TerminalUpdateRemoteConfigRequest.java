/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.terminal;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * The type Terminal update remote config request.
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TerminalUpdateRemoteConfigRequest extends BaseRequest {

    private static final long serialVersionUID = -1851364480130685147L;
    private boolean allowRemoteChange;
}
