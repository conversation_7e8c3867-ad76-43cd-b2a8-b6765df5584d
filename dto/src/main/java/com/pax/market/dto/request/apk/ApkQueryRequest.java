/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.apk;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/8/11
 */
@Getter
@Setter
public class ApkQueryRequest extends BaseRequest {
    private static final long serialVersionUID = -4793744742519162439L;
    private Long appId;
    private String name;
    private String osType;
    private Boolean specificMarket;
    private Boolean specificReseller;
    private Boolean isGlobal;
    private String baseType;
    private String status;
    private String versionName;


}