/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto.pushtask;

import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.simple.SimpleApkInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Simple终端应用Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SimpleTerminalApkInfo extends BaseResponse {
    private static final long serialVersionUID = 5244668151298814546L;
    private Long id;
    private SimpleApkInfo apk;
    private Date createdDate;
    private Date updatedDate;
    private Date effectiveTime;
    private Date expiredTime;
    private String status;
    private Long actionId;
    private int actionStatus;
    private Date actionTime;
    private int errorCode;
    private String errorMessage;
    private String groupName;
    private TerminalApkParamInfo terminalApkParam;
}