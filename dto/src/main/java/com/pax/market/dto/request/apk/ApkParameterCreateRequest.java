/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto.request.apk;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

/**
 * 应用参数创建Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ApkParameterCreateRequest extends BaseRequest {

    private Long apkId;
    private Long terminalApkParamId;
    private Boolean replaceParameterVariables;
    private Long groupApkParamId;
    private Long terminalActionId;
    private Long apkParameterId;
    private String name;
    private LinkedHashMap<String, String> defaultValues;
    private String paramTemplateName;
}

