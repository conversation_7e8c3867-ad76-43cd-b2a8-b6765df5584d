/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.dto.request.factory;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;


/**
 * @Description Publish factory to market
 * @Author: Shawn
 * @Date: 2020/6/2
 */
@Getter
@Setter
public class SpecificFactoryMarketRequest extends BaseRequest {

    private static final long serialVersionUID = -2977918239069950974L;
    private String marketIds;
}
