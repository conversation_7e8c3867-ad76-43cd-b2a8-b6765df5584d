/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.dto.audit.log.developer;

import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.search.NameLog;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DeveloperApkLog extends BaseLog {
    private static final long serialVersionUID = 1L;

    private String appName;
    private String packageName;
    private String shortDesc;
    private String description;
    private String releaseNotes;
    private String accessUrl;
    private String trackAlias;
    private String attachmentName;
    private Integer appChargeType;
    private BigDecimal appPrice;
    private Integer chargeMode;
    private String apkType;
    private String appType;
    private List<String> paramTemplateNameList;
    private List<String> apkCategoryList;
    private List<ModelLog> apkModelList;

    @Getter
    @Setter
    public static class ModelLog implements Serializable {
        private static final long serialVersionUID = 1L;

        private String label;
        private NameLog factory;
    }
}
