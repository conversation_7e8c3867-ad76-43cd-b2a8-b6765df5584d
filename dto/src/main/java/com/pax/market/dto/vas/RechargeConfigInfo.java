package com.pax.market.dto.vas;

import com.pax.market.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 9.7
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RechargeConfigInfo extends BaseResponse {

    @Serial
    private static final long serialVersionUID = 8138979521341986231L;
    private Integer minCharge;


}
