/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto;

import java.util.List;

/**
 * 全局配置传输对象
 *
 * <AUTHOR>
 */
public class GlobalConfigInfo extends BaseResponse {
    private static final long serialVersionUID = -1L;

    private List<GlobalConfigLangInfo> globalLangList;


    /**
     * Gets global lang list.
     *
     * @return the global lang list
     */
    public List<GlobalConfigLangInfo> getGlobalLangList() {
        return globalLangList;
    }

    /**
     * Sets global lang list.
     *
     * @param langList the lang list
     */
    public void setGlobalLangList(List<GlobalConfigLangInfo> langList) {
        this.globalLangList = langList;
    }
}
