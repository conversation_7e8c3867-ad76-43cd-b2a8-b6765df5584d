/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.dto.audit.log.admin.app;

import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.vo.admin.common.EntityAttributeVo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/19
 */
@Getter
@Setter
public class AppRemarkLog extends BaseLog {

    @Serial
    private static final long serialVersionUID = -6379864257250961069L;
    private List<EntityAttributeVo> entityAttributes;

}
