/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2021. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.dto.rki.authsystem;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/3/31
 */
@NoArgsConstructor
@Getter
@Setter
public class PreDeductionResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String result;
    private String msg;
    @JsonAlias({"extmsg1", "extMsg1"})
    private String extMsg1;
    @JsonAlias({"extmsg2", "extMsg2"})
    private String extMsg2;
    @JsonAlias({"extmsg3", "extMsg3"})
    private String extMsg3;
    @JsonAlias({"platformid", "platformId"})
    private String platformId;
    @JsonAlias({"customerid", "customerId"})
    private String customerId;
    private String url;
    private String sign;
    @JsonAlias({"token", "paymentToken"})
    private String paymentToken;
    private String remarks;
    private boolean needCreateDeduction;

    @JsonIgnore
    public String getJointMsg(){
        StringBuilder sb = new StringBuilder();
        if(StringUtils.isNotEmpty(extMsg1)){
            sb.append(extMsg1);
        }else if(StringUtils.isNotEmpty(extMsg2)){
            sb.append(extMsg2);
        }else if(StringUtils.isNotEmpty(extMsg3)){
            sb.append(extMsg3);
        }else if(StringUtils.isNotEmpty(url)){
            sb.append(url);
        }
        return sb.toString();
    }

    public static PreDeductionResponse of(String result) {
        PreDeductionResponse response = new PreDeductionResponse();
        response.setResult(result);
        return response;
    }
}
