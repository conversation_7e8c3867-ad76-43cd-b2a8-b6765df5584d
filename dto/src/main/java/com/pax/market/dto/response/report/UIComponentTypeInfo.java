/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto.response.report;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/01/22 14:48:12
 */

@Getter
@Setter
public class UIComponentTypeInfo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 3154727813619750036L;

    private Long id;
    private String label;
    private boolean requireDataSource;
    private boolean lazy;
    private boolean supportDependOn;
}
