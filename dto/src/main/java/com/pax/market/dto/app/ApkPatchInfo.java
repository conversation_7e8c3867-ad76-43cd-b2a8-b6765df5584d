/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.app;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

/**
 * The type Apk patch info.
 */
@Builder
@Getter
public class ApkPatchInfo implements Serializable {

    private Long factoryId;
    private Long oldApkId;
    private Long newApkId;
    private String patchUrl;
    private long patchSize;
    private ApkInfo oldApk;

}
