/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.code;

import java.io.Serializable;
import java.util.List;

/**
 * Created by gubin_ on 2016/12/7.
 */
public class CodeTypeTypeConfigInfo implements Serializable {

    private static final long serialVersionUID = -3812783479862042330L;

    private String type;

    private List<CodeTypeConfigInfo> codeTypeConfigInfoList;

    /**
     * Gets type.
     *
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * Sets type.
     *
     * @param type the type
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * Gets code type config info list.
     *
     * @return the code type config info list
     */
    public List<CodeTypeConfigInfo> getCodeTypeConfigInfoList() {
        return codeTypeConfigInfoList;
    }

    /**
     * Sets code type config info list.
     *
     * @param codeTypeConfigInfoList the code type config info list
     */
    public void setCodeTypeConfigInfoList(List<CodeTypeConfigInfo> codeTypeConfigInfoList) {
        this.codeTypeConfigInfoList = codeTypeConfigInfoList;
    }
}
