/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.dto.request.payment;

import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
 * @date May 8, 2017
 */
public class PaypalCheckoutRequest implements Serializable {

    private static final long serialVersionUID = -5255300160220530294L;
    private String nonce;
    private String cardType;
    private String lastTwo;
    private String type;

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getLastTwo() {
        return lastTwo;
    }

    public void setLastTwo(String lastTwo) {
        this.lastTwo = lastTwo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
