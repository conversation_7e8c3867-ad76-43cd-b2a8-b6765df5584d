package com.pax.market.dto.request.activity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 9.1
 **/
@Getter
@Setter
public class AirViewerUsageExportRequestV2 extends BaseExportRequest {
    @Serial
    private static final long serialVersionUID = 6906774896375677984L;
    private Long marketId;
    private Long resellerId;
    private Date startDate;
    private Date endDate;
    private Integer year;
    private Integer month;
    private String period;
    private boolean includeSubReseller;
    private boolean globalExport;
}
