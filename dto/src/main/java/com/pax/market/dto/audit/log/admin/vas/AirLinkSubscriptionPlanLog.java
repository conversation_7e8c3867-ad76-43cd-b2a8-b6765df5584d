package com.pax.market.dto.audit.log.admin.vas;

import com.pax.market.dto.audit.log.BaseLog;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;

@Getter
@Setter
public class AirLinkSubscriptionPlanLog extends BaseLog {
    @Serial
    private static final long serialVersionUID = 6108495719524399899L;

    private String dataPoolName;

    private String packageType;

    private Integer packageLimit;

    private BigDecimal activationFee;

    private BigDecimal packageFee;

    private BigDecimal overagePrice;


}
