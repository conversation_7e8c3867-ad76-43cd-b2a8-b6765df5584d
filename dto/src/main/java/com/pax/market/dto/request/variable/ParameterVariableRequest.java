/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */
package com.pax.market.dto.request.variable;

import com.pax.market.dto.BaseResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The type Parameter variable request.
 */
@Getter
@Setter
@NoArgsConstructor
public class ParameterVariableRequest extends BaseResponse {

    private static final long serialVersionUID = -1671494498973528363L;
    private Long terminalId;
    private Long groupId;
    private Long merchantId;
    private Long appId;
    private String type;
    private String key;
    private String value;
    private String remarks;
}