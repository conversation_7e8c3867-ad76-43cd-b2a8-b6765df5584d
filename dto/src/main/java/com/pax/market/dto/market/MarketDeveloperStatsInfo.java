package com.pax.market.dto.market;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>MarketDeveloperStatsInfo</p>
 * <p>Description:</p>
 *
 * <AUTHOR>
 * @create 2020-10-19 10:24
 * @since 1.0
 */
@Getter
@Setter
@ToString
public class MarketDeveloperStatsInfo implements Serializable {
    private static final long serialVersionUID = 3587709127230137763L;


    private String developerType;
    private String email;
    private String marketName;
    private Long marketId;
    private Date registerDate;

    private int appDownloads;
    private int appNum;
}
