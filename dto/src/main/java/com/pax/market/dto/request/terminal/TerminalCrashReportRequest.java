/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.dto.request.terminal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pax.market.dto.request.BaseRequest;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fanjun on 2018/6/4.
 */
public class TerminalCrashReportRequest extends BaseRequest {
    private static final long serialVersionUID = 4781269006564278600L;

    @JsonProperty(value = "REPORT_ID")
    private String reportId;
    @JsonProperty(value = "APP_VERSION_CODE")
    private int appVersionCode;
    @JsonProperty(value = "APP_VERSION_NAME")
    private String appVersionName;
    @JsonProperty(value = "PACKAGE_NAME")
    private String packageName;
    @JsonProperty(value = "PHONE_MODEL")
    private String phoneModel;
    @JsonProperty(value = "ANDROID_VERSION")
    private String androidVersion;
    @JsonProperty(value = "BUILD")
    private Build build;
    @JsonProperty(value = "TOTAL_MEM_SIZE")
    private long totalMemSize;
    @JsonProperty(value = "AVAILABLE_MEM_SIZE")
    private long availableMemSize;
    @JsonProperty(value = "CUSTOM_DATA")
    private CustomData customData;
    @JsonProperty(value = "STACK_TRACE")
    private List<StackTrace> stacktraces;
    @JsonProperty(value = "USER_APP_START_DATE")
    private String userAppStartDate;
    @JsonProperty(value = "USER_CRASH_DATE")
    private String userCrashDate;
    @JsonProperty(value = "INSTALLATION_ID")
    private String installationId;


    public class Build implements Serializable {
        private static final long serialVersionUID = -3489268127981224535L;
        @JsonProperty(value = "BOARD")
        private String board;
        @JsonProperty(value = "BOOTLOADER")
        private String bootloader;
        @JsonProperty(value = "BRAND")
        private String brand;
        @JsonProperty(value = "CPU_ABI")
        private String cpuAbi;
        @JsonProperty(value = "CPU_ABI2")
        private String cpuAbi2;
        @JsonProperty(value = "DEVICE")
        private String device;
        @JsonProperty(value = "DISPLAY")
        private String display;
        @JsonProperty(value = "FINGERPRINT")
        private String fingerPrint;
        @JsonProperty(value = "HARDWARE")
        private String hardware;
        @JsonProperty(value = "HOST")
        private String host;
        @JsonProperty(value = "TIME")
        private String time;
        @JsonProperty(value = "USER")
        private String user;
        @JsonProperty(value = "ID")
        private String id;
        @JsonProperty(value = "INTER_VERSION")
        private String interVersion;
        @JsonProperty(value = "IS_DEBUGGABLE")
        private boolean debuggable;
        @JsonProperty(value = "IS_EMULATOR")
        private boolean emulator;
        @JsonProperty(value = "MANUFACTURER")
        private String manufacturer;
        @JsonProperty(value = "MODEL")
        private String model;
        @JsonProperty(value = "PERMISSIONS_REVIEW_REQUIRED")
        private boolean permissionsReviewRequired;
        @JsonProperty(value = "PRODUCT")
        private String product;
        @JsonProperty(value = "RADIO")
        private String radio;
        @JsonProperty(value = "SERIAL")
        private String serial;
        @JsonProperty(value = "SUPPORTED_32_BIT_ABIS")
        private String supported32BitAbis;
        @JsonProperty(value = "SUPPORTED_64_BIT_ABIS")
        private String supported64BitAbis;
        @JsonProperty(value = "SUPPORTED_ABIS")
        private String supportedAbis;
        @JsonProperty(value = "TAGS")
        private String tags;
        @JsonProperty(value = "TYPE")
        private String type;
        @JsonProperty(value = "UNKNOWN")
        private String unknown;
        @JsonProperty(value = "VERSION")
        private Version version;

        public class Version implements Serializable {
            private static final long serialVersionUID = 2872905671860895023L;
            @JsonProperty(value = "INCREMENTAL")
            private String incremental;
            @JsonProperty(value = "RELEASE")
            private String release;
            @JsonProperty(value = "BASE_OS")
            private String baseOs;
            @JsonProperty(value = "SECURITY_PATCH")
            private String securityPatch;
            @JsonProperty(value = "SDK")
            private String sdk;
            @JsonProperty(value = "SDK_INT")
            private String sdkInt;
            @JsonProperty(value = "PREVIEW_SDK_INT")
            private String previewSdkInt;
            @JsonProperty(value = "CODENAME")
            private String codename;
            @JsonProperty(value = "RESOURCES_SDK_INT")
            private String resourcesSdkInt;

            public String getIncremental() {
                return incremental;
            }

            public void setIncremental(String incremental) {
                this.incremental = incremental;
            }

            public String getRelease() {
                return release;
            }

            public void setRelease(String release) {
                this.release = release;
            }

            public String getBaseOs() {
                return baseOs;
            }

            public void setBaseOs(String baseOs) {
                this.baseOs = baseOs;
            }

            public String getSecurityPatch() {
                return securityPatch;
            }

            public void setSecurityPatch(String securityPatch) {
                this.securityPatch = securityPatch;
            }

            public String getSdk() {
                return sdk;
            }

            public void setSdk(String sdk) {
                this.sdk = sdk;
            }

            public String getSdkInt() {
                return sdkInt;
            }

            public void setSdkInt(String sdkInt) {
                this.sdkInt = sdkInt;
            }

            public String getPreviewSdkInt() {
                return previewSdkInt;
            }

            public void setPreviewSdkInt(String previewSdkInt) {
                this.previewSdkInt = previewSdkInt;
            }

            public String getCodename() {
                return codename;
            }

            public void setCodename(String codename) {
                this.codename = codename;
            }

            public String getResourcesSdkInt() {
                return resourcesSdkInt;
            }

            public void setResourcesSdkInt(String resourcesSdkInt) {
                this.resourcesSdkInt = resourcesSdkInt;
            }
        }


        public String getBoard() {
            return board;
        }

        public void setBoard(String board) {
            this.board = board;
        }

        public String getBootloader() {
            return bootloader;
        }

        public void setBootloader(String bootloader) {
            this.bootloader = bootloader;
        }

        public String getBrand() {
            return brand;
        }

        public void setBrand(String brand) {
            this.brand = brand;
        }

        public String getCpuAbi() {
            return cpuAbi;
        }

        public void setCpuAbi(String cpuAbi) {
            this.cpuAbi = cpuAbi;
        }

        public String getCpuAbi2() {
            return cpuAbi2;
        }

        public void setCpuAbi2(String cpuAbi2) {
            this.cpuAbi2 = cpuAbi2;
        }

        public String getDevice() {
            return device;
        }

        public void setDevice(String device) {
            this.device = device;
        }

        public String getDisplay() {
            return display;
        }

        public void setDisplay(String display) {
            this.display = display;
        }

        public String getFingerPrint() {
            return fingerPrint;
        }

        public void setFingerPrint(String fingerPrint) {
            this.fingerPrint = fingerPrint;
        }

        public String getHardware() {
            return hardware;
        }

        public void setHardware(String hardware) {
            this.hardware = hardware;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getInterVersion() {
            return interVersion;
        }

        public void setInterVersion(String interVersion) {
            this.interVersion = interVersion;
        }

        public boolean isDebuggable() {
            return debuggable;
        }

        public void setDebuggable(boolean debuggable) {
            this.debuggable = debuggable;
        }

        public boolean isEmulator() {
            return emulator;
        }

        public void setEmulator(boolean emulator) {
            this.emulator = emulator;
        }

        public String getManufacturer() {
            return manufacturer;
        }

        public void setManufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public boolean isPermissionsReviewRequired() {
            return permissionsReviewRequired;
        }

        public void setPermissionsReviewRequired(boolean permissionsReviewRequired) {
            this.permissionsReviewRequired = permissionsReviewRequired;
        }

        public String getProduct() {
            return product;
        }

        public void setProduct(String product) {
            this.product = product;
        }

        public String getRadio() {
            return radio;
        }

        public void setRadio(String radio) {
            this.radio = radio;
        }

        public String getSerial() {
            return serial;
        }

        public void setSerial(String serial) {
            this.serial = serial;
        }

        public String getSupported32BitAbis() {
            return supported32BitAbis;
        }

        public void setSupported32BitAbis(String supported32BitAbis) {
            this.supported32BitAbis = supported32BitAbis;
        }

        public String getSupported64BitAbis() {
            return supported64BitAbis;
        }

        public void setSupported64BitAbis(String supported64BitAbis) {
            this.supported64BitAbis = supported64BitAbis;
        }

        public String getSupportedAbis() {
            return supportedAbis;
        }

        public void setSupportedAbis(String supportedAbis) {
            this.supportedAbis = supportedAbis;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getUnknown() {
            return unknown;
        }

        public void setUnknown(String unknown) {
            this.unknown = unknown;
        }

        public Version getVersion() {
            return version;
        }

        public void setVersion(Version version) {
            this.version = version;
        }
    }

    public class CustomData implements Serializable {
        private static final long serialVersionUID = 807212284436685687L;
        @JsonProperty(value = "DOMAIN")
        private String domain;
        @JsonProperty(value = "SERIAL_NO")
        private String serialNo;
        @JsonProperty(value = "TERMINAL_ID")
        private Long terminalId;
        @JsonProperty(value = "FLAVOR")         //sit, prd, aws, awsstaging, perf, tosan, paxus, usuat
        private String flavor;
        @JsonProperty(value = "MODEL")
        private String model;

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getSerialNo() {
            return serialNo;
        }

        public void setSerialNo(String serialNo) {
            this.serialNo = serialNo;
        }

        public Long getTerminalId() {
            return terminalId;
        }

        public void setTerminalId(Long terminalId) {
            this.terminalId = terminalId;
        }

        public String getFlavor() {
            return flavor;
        }

        public void setFlavor(String flavor) {
            this.flavor = flavor;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }
    }

    public class StackTrace implements Serializable {
        private static final long serialVersionUID = 951830332294042963L;
        @JsonProperty(value = "STACK_TRACE")
        private String stacktrace;
        @JsonProperty(value = "HASH")
        private String hash;
        @JsonProperty(value = "COUNT")
        private long count;

        public String getStacktrace() {
            return stacktrace;
        }

        public void setStacktrace(String stacktrace) {
            this.stacktrace = stacktrace;
        }

        public String getHash() {
            return hash;
        }

        public void setHash(String hash) {
            this.hash = hash;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public int getAppVersionCode() {
        return appVersionCode;
    }

    public void setAppVersionCode(int appVersionCode) {
        this.appVersionCode = appVersionCode;
    }

    public String getAppVersionName() {
        return appVersionName;
    }

    public void setAppVersionName(String appVersionName) {
        this.appVersionName = appVersionName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPhoneModel() {
        return phoneModel;
    }

    public void setPhoneModel(String phoneModel) {
        this.phoneModel = phoneModel;
    }

    public String getAndroidVersion() {
        return androidVersion;
    }

    public void setAndroidVersion(String androidVersion) {
        this.androidVersion = androidVersion;
    }

    public Build getBuild() {
        return build;
    }

    public void setBuild(Build build) {
        this.build = build;
    }

    public long getTotalMemSize() {
        return totalMemSize;
    }

    public void setTotalMemSize(long totalMemSize) {
        this.totalMemSize = totalMemSize;
    }

    public long getAvailableMemSize() {
        return availableMemSize;
    }

    public void setAvailableMemSize(long availableMemSize) {
        this.availableMemSize = availableMemSize;
    }

    public CustomData getCustomData() {
        return customData;
    }

    public void setCustomData(CustomData customData) {
        this.customData = customData;
    }

    public List<StackTrace> getStacktraces() {
        return stacktraces;
    }

    public void setStacktraces(List<StackTrace> stacktraces) {
        this.stacktraces = stacktraces;
    }

    public String getUserAppStartDate() {
        return userAppStartDate;
    }

    public void setUserAppStartDate(String userAppStartDate) {
        this.userAppStartDate = userAppStartDate;
    }

    public String getUserCrashDate() {
        return userCrashDate;
    }

    public void setUserCrashDate(String userCrashDate) {
        this.userCrashDate = userCrashDate;
    }

    public String getInstallationId() {
        return installationId;
    }

    public void setInstallationId(String installationId) {
        this.installationId = installationId;
    }
}
