/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.dto.request.terminal;

import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 终端上送的日志类型
 * <AUTHOR>
 * @date 2022/10/25
 */
@Getter
@Setter
public class TerminalLogCatRequest extends BaseRequest {
    private static final long serialVersionUID = 207654394129553061L;
    private String type;
    private String beginDate;
    private String endDate;

}