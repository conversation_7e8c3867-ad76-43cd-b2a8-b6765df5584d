/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.dto.request.signature;

import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * 应用市场签名配置传输对象 for admin
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MarketSignatureSettingRequest extends BaseRequest {
    private static final long serialVersionUID = -1L;

    private boolean paxPuk;
    private boolean paxPuk4096;
    private boolean allowResellerSignature;
    private String osType;
    private String url;                 //签名URL
    private String accessKey;
    private String accessSecret;
    private String password;
    private String alias;               //别名
    private String certificateName;
    private byte[] certificate;         //p12文件
    private byte[] publicCertificate;   //公共签名证书
    private String publicCertificateName;
    private Long marketId;
    private Long resellerId;
    private Long factoryId;
    private Boolean customSignature;
    private String provider;
    private String signType; //2048/4096
    private boolean deletePuk;     //是否删除Puk
}
