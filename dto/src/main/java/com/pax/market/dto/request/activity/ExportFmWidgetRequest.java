package com.pax.market.dto.request.activity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper=false)
public class ExportFmWidgetRequest extends BaseExportRequest {
    @Serial
    private static final long serialVersionUID = 1227916360178535317L;
    private Integer widgetType;
    private Long currentMarketId;
    private Long currentResellerId;
    private Long userId;
    private String lang;
    private Boolean loadMarketData;
}
