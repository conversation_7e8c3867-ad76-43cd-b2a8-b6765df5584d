/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.constants;

import java.io.Serial;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface TerminalPowerMode {
    String AUTO_ADAPTION = "A";
    String DESKTOP_MODE = "D";
    String MOBILE_MODE = "M";

    String AUTO_ADAPTION_DESC = "Auto-adaption";
    String DESKTOP_MODE_DESC = "Desktop Mode";
    String MOBILE_MODE_DESC = "Mobile Mode";

    Map<String, String> POWER_MODE_MAP = new HashMap<>() {
        @Serial
        private static final long serialVersionUID = 1283662901278474149L;

        {
            put(AUTO_ADAPTION, AUTO_ADAPTION_DESC);
            put(DESKTOP_MODE, DESKTOP_MODE_DESC);
            put(MOBILE_MODE, MOBILE_MODE_DESC);
        }
    };

     List<String> list = List.of(
            AUTO_ADAPTION,
            DESKTOP_MODE,
            MOBILE_MODE
    );
}
