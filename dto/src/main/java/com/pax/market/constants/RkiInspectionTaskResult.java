/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.constants;

/**
 * The interface Rki inspection task response.
 *
 * <AUTHOR>
 * @date 2018 /12/20
 */
public interface RkiInspectionTaskResult {

    /**
     * The constant SUCCESS.
     */
    int SUCCESS = 0;

    /**
     * The constant ERROR_0.
     */
    int ERROR_0 = ApiCodes.RKI_INSPECTION_TASK_ERROR_0;

    /**
     * The constant ERROR_1.
     */
    int ERROR_1 = ApiCodes.RKI_INSPECTION_TASK_ERROR_1;

    /**
     * The constant ERROR_2.
     */
    int ERROR_2 = ApiCodes.RKI_INSPECTION_TASK_ERROR_2;

    /**
     * The constant ERROR_3.
     */
    int ERROR_3 = ApiCodes.RKI_INSPECTION_TASK_ERROR_3;

    /**
     * The constant ERROR_4.
     */
    int ERROR_4 = ApiCodes.RKI_INSPECTION_TASK_ERROR_4;

    /**
     * The constant ERROR_5.
     */
    int ERROR_5 = ApiCodes.RKI_INSPECTION_TASK_ERROR_5;

    /**
     * The constant SUCCESS_EXIST_TASK.
     */
    String SUCCESS_EXIST_TASK = "Success";

    /**
     * The constant SUCCESS_NO_TASK.
     */
    String SUCCESS_NO_TASK = "No Task";
}
