package com.pax.market.constants.emm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/5/11
 */
@Getter
@AllArgsConstructor
public enum EmmAppType {

    GOOGLE("G","PUBLIC_GOOGLE_HOSTED"),
    PRIVATE("P","PRIVATE_GOOGLE_HOSTED");

    private final String sign;

    private final String type;

    public static EmmAppType from(String sign) {
        for(EmmAppType value : EmmAppType.values()) {
            if(value.getSign().equals(sign)) {
                return value;
            }
        }
        return null;
    }

    public static String parse(String sign) {
        for (EmmAppType value : EmmAppType.values()) {
            if (value.getSign().equals(sign)) {
                return value.getType();
            }
        }
        return null;
    }

    public static String getSign(String type) {
        for (EmmAppType value : EmmAppType.values()) {
            if (value.getType().equals(type)) {
                return value.getSign();
            }
        }
        return null;
    }


}
