package com.pax.market.constants;

public enum AppVisualScope {
    All(0),
    Portal(1),
    Client(2),
    Display(3);
    private final int status;

    AppVisualScope(int status) {
        this.status = status;
    }

    public int val() {
        return this.status;
    }

    public static AppVisualScope parse(int val) {
        for (AppVisualScope s : values()) {
            if (s.val() == val) {
                return s;
            }
        }
        return null;
    }
}
