package com.pax.market.constants.emm;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
public interface EmmConstants {

    String PROJECT_PATTERN = "projects/";
    String ENTERPRISE_PATTERN = "enterprises/";
    String DEVICE_PATTERN = "/devices/";
    String POLICY_PATTERN = "/policies/";
    String ENROLLMENT_TOKEN_PATTERN = "/enrollmentTokens/";
    String TOPIC_PATTERN = "/topics/";
    String APPLICATION_PATTERN = "/applications/";
    String OPERATIONS_PATTERN = "/operations/";
    String DPC_PATTERN = "/dpcs/";
    String ZTE_CONFIGURATION_PATTERN = "/configurations/";

    String PARTNER_PATTERN = "partners/";
    String CUSTOMER_PATTERN = "customers/";
    String BATCH_OPERATION_PATTERN = "operations/apibatchoperation/";

    String TOPIC_NAME_PATTERN = PROJECT_PATTERN + "%s" + TOPIC_PATTERN + "%s";
    String POLICY_NAME_PATTERN = ENTERPRISE_PATTERN + "%s" + POLICY_PATTERN + "%s";
    String ENROLLMENT_TOKEN_NAME_PATTERN =  ENTERPRISE_PATTERN + "%s" + ENROLLMENT_TOKEN_PATTERN + "%s";
    String DEVICE_NAME_PATTERN = ENTERPRISE_PATTERN + "%s" + DEVICE_PATTERN + "%s";
    String APPLICATION_NAME_PATTERN = ENTERPRISE_PATTERN + "%s" + APPLICATION_PATTERN + "%s";
    String DPC_NAME_PATTERN = CUSTOMER_PATTERN + "%s" + DPC_PATTERN + "%s";
    String ZTE_CONFIGURATION_NAME_PATTERN = CUSTOMER_PATTERN + "%s" + ZTE_CONFIGURATION_PATTERN + "%s";

    String CERT_POLICY_PREFIX = "cert-specific-";
    String CLIENT_CONFIG_KEY_AUTH = "auth_path_prefix";
    String CLIENT_CONFIG_KEY_TOKEN = "device_token";

    String ENROLLMENT = "ENROLLMENT";
    String COMMAND = "COMMAND";
    String USAGE_LOGS = "USAGE_LOGS";

    String LOCK_NOW = "LOCK_NOW";
    String START_LOST_MODE = "START_LOST_MODE";
    String STOP_LOST_MODE = "STOP_LOST_MODE";

    String REBOOT_ANDROID_VERSION_AT_LEAST = "7";
    String REQUIRED_RESET_PW_ANDROID_VERSION = "14";
    String REQUIRED_LOST_MODE_ANDROID_VERSION = "11";

    int MAX_USER_FACING_LOST_MESSAGE_LENGTH = 64;
    int MAX_USER_FACING_PHONE_NUMBER_MESSAGE_LENGTH = 32;
    int MAX_APP_CONFIG_NAME_LENGTH = 100;

    int ZTE_MAX_QUICK_UPLOAD_NUMBER_SIZE = 200;

    String EMM_POLICY_TYPE_RESELLER = "R";
    String EMM_POLICY_TYPE_MERCHANT = "M";
    String EMM_POLICY_RESTRICTION_TITLE= "r";
    String EMM_POLICY_GENERAL_SETTING_TITLE = "gs";
    String EMM_POLICY_SECURITY_TITLE = "s";
    String EMM_POLICY_COMMUNICATION_NETWORK_TITLE = "cn";
    String EMM_POLICY_APPLICATION_TITLE = "a";
    String EMM_POLICY_APPLICATION_TYPE = "al";
    String EMM_POLICY_ID = "%s_%s";
    String EMM_TYPE_REFERENCE_ID = "%s_%s";
    String EMM_TYPE_REFERENCE_ID_KEY = "%s_%s_%s";
    String EMM_TYPE_REFERENCE_ID_APP_ID = "%s_%s_%s";
    String EMM_POLICY_OVERRIDE_TO_OVERRIDE = "OTO";
    String EMM_POLICY_OVERRIDE_TO_INHERIT = "OTI";
    String EMM_POLICY_INHERIT_TO_OVERRIDE = "ITO";
    String NOTIFICATIONS = "NOTIFICATIONS";
    String UNREDACTED_NOTIFICATIONS = "UNREDACTED_NOTIFICATIONS";
    String TRUST_AGENTS = "TRUST_AGENTS";
    String DISABLE_FINGERPRINT = "DISABLE_FINGERPRINT";
    String FACE = "FACE";
    String BIOMETRICS = "BIOMETRICS";
    String SHORTCUTS = "SHORTCUTS";
    String ALL_FEATURES = "ALL_FEATURES";
    /**
     * The constant EMM_DEVICE_SN_LENGTH.
     */
    int EMM_DEVICE_SN_LENGTH = 32;


    /**
     * The constant PACKAGE_NAME_REGEX.
     */
    String PACKAGE_NAME_REGEX = "^[a-zA-Z][a-zA-Z0-9_]*(\\.[a-zA-Z][a-zA-Z0-9_]*)*$";


    /**
     * PAC_URL.
     */
    String PAC_URL = "^((ht|f)tps?):\\/\\/[\\w\\-]+(\\.[\\w\\-]+)+([\\w\\-\\.,@?^=%&:\\/~\\+#]*[\\w\\-@?^=%&\\/~\\+#])?$";

    /**
     * IP_BASED_HOST_REGEX
     */
    String IP_BASED_HOST_REGEX = "^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}$";

    /**
     * URL_REGEX.
     */
    String URL_REGEX = "^(http|https)://[a-zA-Z0-9.-]+(:[0-9]{1,5})?(/.*)?$";

    String ZTE_NUMBER_SPLIT_REG_EXPR = "[,:;\\s]+";

    /**
     * HOUR_MINUTES_REGEX.
     */
    String HOUR_MINUTES_REGEX = "([01]?\\d|2[0-3]):[0-5]\\d";

    String IMEI_NUMBER_REG_EXPR = "^\\d{15}([\\s,:;]\\d{15})*$";

    String SERIAL_NUMBER_REG_EXPR = "^[\\da-zA-Z]{1,32}([\\s,:;][\\da-zA-Z]{1,32})*$";

    Long PARTNER_ID = 1965494738L;
    String MIGRATED = "M";
    Long DEFAULT_ZTE_CONFIGURATION_MERCHANT_ID = 0L;

    String CUSTOMER_OWNER_EMAIL = "<EMAIL>";
    String ANDROID_DEVICE_POLICY_DPC_ID = "AH6Gbe6zLCPG4gTOUB6j_N8WMDM4njYUiznUHVH3yHChimbLI6aZjvFUg_T9q-vfkFaR94RGu0sdM9oXM-e73Pc_nnW4tm604cOyChbKGEW3xDLw";
    String SECTION_TYPE_ZERO_TOUCH = "SECTION_TYPE_ZERO_TOUCH";
    String SINGLE_DEVICE_STATUS_SUCCESS  = "SINGLE_DEVICE_STATUS_SUCCESS";

    String ZTE_TOKEN_DURATION = 60 * 60 * 24 * 365 * 100L + "s";

    String ZTE_ZOLON_MANUFACTURER = "ZOLON";
    List<String> ZTE_ZOLON_SUPPORTED_MODELS = List.of("L16xx", "M30", "M50", "M9200");
    List<String> SUPPORTED_ENV_CODES = List.of("dev", "sit", "docker86", "usuat", "paxus", "awsstaging", "aws", "ccv");

    /**
     * The constant DEVICE_NOT_AUTHENTICATED_DAY
     */
    double DEVICE_NOT_AUTHENTICATED_DAY = 1;

    int GOOGLE_API_ERROR_CODE_NOT_FOUND = 404;
    int GOOGLE_CALLER_NO_PERMISSIONS = 403;
    int GOOGLE_API_ERROR_CODE_INVALID_ARGUMENT = 400;

    int EMM_DEVICE_PWD_LONGEST_NUM = 16;

    int EMM_APK_MAX_FILE_SIZE = 100; //单位 M

    int APK_NAME_MAX_LENGTH = 30;
    int APK_SHORT_DESC_MAX_LENGTH = 80;
    int APK_DESCRIPTION_MAX_LENGTH = 4000;
    int APK_ACCESS_URL_MAX_LENGTH = 5000;
    long APK_ICON_MAX_SIZE = 1024 * 1024L;
    long APK_SCREENSHOT_MAX_SIZE = 8 * 1024 * 1024L;

    String ENTERPRISE_BIND_NOTIFICATION_EMAIL = "<EMAIL>";

    List<String> SUPPORTED_ICON_SCREENSHOT_MIME_TYPES = List.of("image/png", "image/jpeg");

    String EMM_AIRVIEWER_TRACK_ID = "4698618415212370996";

}
