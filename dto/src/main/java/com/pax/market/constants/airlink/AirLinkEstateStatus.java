package com.pax.market.constants.airlink;

import com.pax.market.StringCodeEnumConvert;

public enum AirLinkEstateStatus implements StringCodeEnumConvert {
    INUSE("U"),
    INSTOCK("S");

    private final String name;

    AirLinkEstateStatus(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return name;
    }

    public static AirLinkEstateStatus from(String value) {
        for (AirLinkEstateStatus type : AirLinkEstateStatus.values()) {
            if (type.name.equals(value)) {
                return type;
            }
        }
        return null;
    }
}
