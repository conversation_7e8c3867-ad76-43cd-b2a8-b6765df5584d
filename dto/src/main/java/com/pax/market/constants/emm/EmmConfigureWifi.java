package com.pax.market.constants.emm;

public enum EmmConfigureWifi {

    UNSPECIFIED("U"),
    ALLOW_CONFIGURING_WIFI("ACWF"),
    DISALLOW_ADD_WIFI_CONFIG("DAWFC"),
    DISALLOW_CONFIGURING_WIFI("DCWF");

    private final String type;

    public String getType() {
        return type;
    }

    EmmConfigureWifi(String type) {
        this.type = type;
    }

    public static EmmConfigureWifi parse(String type) {
        for (EmmConfigureWifi value : EmmConfigureWifi.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNSPECIFIED;
    }
}
