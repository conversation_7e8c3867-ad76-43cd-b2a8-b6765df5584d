package com.pax.market.constants.emm;

public enum EmmMaximumTimeToLock {

    UNSPECIFIED("U",-1L),
    NO_LIMIT("N", 0L),
    FIFTEEN_SECONDS("15S", 15000L),
    THIRTY_SECONDS("30S", 30000L),
    ONE_MINUTES("1M", 60000L),
    TWO_MINUTES("2M", 120000L),
    FIVE_MINUTES("5M", 300000L),
    TEN_MINUTES("10M", 600000L),
    THIRTY_MINUTES("30M", 1800000L);

    private final String type;
    private final Long googleValue;

    public String getType() {
        return type;
    }
    public Long getGoogleValue() {
        return googleValue;
    }

    EmmMaximumTimeToLock(String type, Long googleValue) {
        this.type = type;
        this.googleValue = googleValue;
    }

    public static EmmMaximumTimeToLock parse(String type) {
        for (EmmMaximumTimeToLock value : EmmMaximumTimeToLock.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNSPECIFIED;
    }

    public static EmmMaximumTimeToLock value(Long googleValue) {
        for (EmmMaximumTimeToLock value : EmmMaximumTimeToLock.values()) {
            if (value.getGoogleValue().equals(googleValue)) {
                return value;
            }
        }
        return UNSPECIFIED;
    }


}
