/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.constants;

import java.util.Arrays;
import java.util.List;

/**
 * 签名方式
 */
public interface SignatureType {

    String RSA_2048 = "2048";
    String RSA_4096 = "4096";

    List<String> TYPES = Arrays.asList(
            RSA_2048, RSA_4096
    );
}
