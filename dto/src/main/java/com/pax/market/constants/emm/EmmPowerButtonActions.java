package com.pax.market.constants.emm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/2/19
 */
@Getter
@AllArgsConstructor
public enum EmmPowerButtonActions {

    UNSPECIFIED("U"),
    POWER_BUTTON_AVAILABLE("A"),
    POWER_BUTTON_BLOCKED("B");

    private final String sign;

    public static EmmPowerButtonActions parse(String sign) {
        for (EmmPowerButtonActions value : EmmPowerButtonActions.values()) {
            if (value.getSign().equals(sign)) {
                return value;
            }
        }
        return UNSPECIFIED;
    }

    public static EmmPowerButtonActions toEnum(String sign) {
        EmmPowerButtonActions emmPowerButtonActions;
        try {
            emmPowerButtonActions = Enum.valueOf(EmmPowerButtonActions.class, sign);
        } catch (Exception e) {
            return UNSPECIFIED;
        }
        return emmPowerButtonActions;
    }

}
