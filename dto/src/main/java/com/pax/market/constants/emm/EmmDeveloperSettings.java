package com.pax.market.constants.emm;

public enum EmmDeveloperSettings {

    UNSPECIFIED("U"),
    DEVELOPER_SETTINGS_DISABLED("D"),
    DEVELOPER_SETTINGS_ALLOWED("A");

    private final String type;

    public String getType() {
        return type;
    }

    EmmDeveloperSettings(String type) {
        this.type = type;
    }

    public static EmmDeveloperSettings parse(String type) {
        for (EmmDeveloperSettings value : EmmDeveloperSettings.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNSPECIFIED;
    }

    public static EmmDeveloperSettings toEnum(String value) {
        EmmDeveloperSettings emmDeveloperSettings;
        try {
            emmDeveloperSettings = Enum.valueOf(EmmDeveloperSettings.class, value);
        } catch (Exception e) {
            return UNSPECIFIED;
        }
        return emmDeveloperSettings;
    }
}
