package com.pax.market.constants;

/**
 * <AUTHOR>
 * @date 2022/8/15 14:26
 */
public enum MarketDeveloperServiceStatus {
    PENDING("P"),
    ACTIVE("A"),
    DISABLED("D"),
    DEFAULT("DEFAULT");
    private final String status;

    MarketDeveloperServiceStatus(String status) {
        this.status = status;
    }

    public String val() {
        return this.status;
    }

    public static MarketDeveloperServiceStatus parse(String status) {
        for (MarketDeveloperServiceStatus s : values()) {
            if (s.val().equals(status)) {
                return s;
            }
        }
        return DEFAULT;
    }
}
