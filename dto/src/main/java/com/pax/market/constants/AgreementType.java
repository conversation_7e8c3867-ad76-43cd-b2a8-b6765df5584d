package com.pax.market.constants;


import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 平台协议类型
 * <AUTHOR>
 * @date 2021/10/15
 */
public interface AgreementType {

    /**
     * The constant USER_AGREEMENT_TYPE.
     */
    String USER_AGREEMENT_TYPE = "U";

    /**
     * The constant DEVELOPER_AGREEMENT_TYPE.
     */
    String DEVELOPER_AGREEMENT_TYPE = "D";

    /**
     * The constant USER_PRIVACY_AGREEMENT_TYPE.
     */
    String USER_PRIVACY_AGREEMENT_TYPE = "P";

    /**
     * The constant PLATFORM_AGREEMENT_TYPE.
     */
    String PLATFORM_AGREEMENT_TYPE = "T";

    /**
     * The constant THIRD_PARTY_AGREEMENT_TYPE. 专指腾讯Tencent Provider Agreement
     */
    String THIRD_PARTY_AGREEMENT_TYPE = "H";

    /**
     *THIRD_PARTY_AGREEMENT_TYPE 类型说明，返回给前端，
     */
    String THIRD_PARTY_AGREEMENT_TYPE_NAME = "Tencent Provider Agreement";

    List<String> lists = Arrays.asList(
            USER_AGREEMENT_TYPE,
            USER_PRIVACY_AGREEMENT_TYPE,
            DEVELOPER_AGREEMENT_TYPE,
            PLATFORM_AGREEMENT_TYPE,
            THIRD_PARTY_AGREEMENT_TYPE);

    List<String> onlyGlobalLists = Arrays.asList(
            USER_PRIVACY_AGREEMENT_TYPE,
            PLATFORM_AGREEMENT_TYPE,
            THIRD_PARTY_AGREEMENT_TYPE);

    
    List<String> adminLists = Arrays.asList(
            USER_AGREEMENT_TYPE,
            USER_PRIVACY_AGREEMENT_TYPE,
            PLATFORM_AGREEMENT_TYPE,
            THIRD_PARTY_AGREEMENT_TYPE);

    List<String> developerLists = Arrays.asList(
            USER_AGREEMENT_TYPE,
            USER_PRIVACY_AGREEMENT_TYPE,
            DEVELOPER_AGREEMENT_TYPE);

    List<String> accountPortalMerchantLists = Arrays.asList(
            USER_AGREEMENT_TYPE,
            USER_PRIVACY_AGREEMENT_TYPE);


    Map<String, String> typeNameMap = Map.of(THIRD_PARTY_AGREEMENT_TYPE, THIRD_PARTY_AGREEMENT_TYPE_NAME);
}
