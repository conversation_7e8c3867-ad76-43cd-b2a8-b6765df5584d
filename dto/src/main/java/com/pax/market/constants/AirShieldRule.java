package com.pax.market.constants;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

import static com.pax.market.constants.DetectionSeverity.*;

@Getter
public enum AirShieldRule {
    APP_BLACKLIST("APL","Application Blacklist",MAJOR),
    SYS_FILE_ACCESS("SFA","System File Accessibility",CRITICAL),
    BE_ROOTED("RD","Rooted Device",SEVERE),
    EMULATOR_DETECTION("ED","Emulator Detection",SEVERE),
    BE_HOOKED("BHD","Hooked Device",SEVERE),
    DEV_DEBUG_MODE("DDM","Developer Mode",CRITICAL),
    BE_CLONED("BC","Cloned Device",CRITICAL),
    OS_MODIFICATION("OM","OS Modification",SEVERE),
    DEVICE_ID("DI","Device ID",SEVERE),
    NET_CELL_IP("NCI","Cellular IP",MINOR),
    NET_WIFI_IP("NWI","Wi-Fi IP",MINOR),
    SYS_VERSION("SV","System Version", MAJOR)
    ;

    private final String code;
    private final String name;
    private final DetectionSeverity severity;

    public static AirShieldRule parseByCode(String code){
        return Arrays.stream(AirShieldRule.values()).filter(r -> Objects.equals(r.code, StringUtils.upperCase(code))).findFirst().orElse(null);
    }


    AirShieldRule(String code, String name, DetectionSeverity severity) {
        this.code = code;
        this.name = name;
        this.severity = severity;
    }
}
