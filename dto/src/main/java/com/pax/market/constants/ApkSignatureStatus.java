/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.constants;

/**
 * 应用状态
 *
 * <AUTHOR>
 */
public interface ApkSignatureStatus {
    String NA = "NA";
    String NONE = "N";
    String SUCCESS = "S";
    String FAILED = "F";
    String PENDING = "P";
    String SIGNING = "I";
}
