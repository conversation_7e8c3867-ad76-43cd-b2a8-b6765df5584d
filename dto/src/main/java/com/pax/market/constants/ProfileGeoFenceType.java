/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.constants;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProfileGeoFenceType {

    String DISABLE = "D";

    String CENTER_POINT = "P";

    String CUSTOMIZE_COORDINATE_POINT = "C";

    String BOUNDARIES_GEOFENCING = "B";

    List<String> geoFenceTypes = List.of(
            DISABLE,
            CENTER_POINT,
            CUSTOMIZE_COORDINATE_POINT,
            BOUNDARIES_GEOFENCING
    );
}
