/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.constants;

/**
 * 终端应用状态
 *
 * <AUTHOR>
 */
public interface SandboxTerminalApkStatus {
    /**
     * 初始状态
     */
    String PENDING = "P";

    /**
     * 激活状态
     */
    String ACTIVE = "A";

    /**
     * 挂起状态
     */
    String SUSPEND = "S";
}
