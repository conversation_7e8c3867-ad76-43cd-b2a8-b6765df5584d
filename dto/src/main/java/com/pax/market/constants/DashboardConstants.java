/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.constants;

/**
 * Constants for Dashboard
 *
 * <AUTHOR>
 * @date Mar 16, 2017
 */
public interface DashboardConstants {

    String COLUMN_TYPE_C12 = "C12";
    String COLUMN_TYPE_C8 = "C8";
    String COLUMN_TYPE_C6 = "C6";
    String COLUMN_TYPE_C4 = "C4";

    String WIDGET_ALERT = "W01";
    String WIDGET_PENDING_APPS = "W02";
    String WIDGET_TERNIMAL_STATISTIC = "W03";
    String WIDGET_TERNIMAL_ONLINE_CHART = "W04";
    String WIDGET_TOP_10_APPS = "W05";
    String WIDGET_MAP = "W06";
    String WIDGET_TERMINAL_CRASH_REPORT = "W07";
    String WIDGET_AUDIT_TRAIL_LOG = "W08";
    String WIDGET_TERNIMAL_QUANTITY_SUMMARY = "W09";
    String WIDGET_RESELLER_MERCHANT_SUMMARY = "W10";
    String WIDGET_RESELLER_LEVEL_AUDIT_LIST = "W11";
    String WIDGET_RESELLER_SUMMARY = "W12";
    String WIDGET_FM_TERMINAL_ORGANIZE = "W13";
    String WIDGET_CLIENT_TERMINAL_ORG ="W14";
    String WIDGET_MODEL_TERMINAL="W15";
    String WIDGET_TERMINAL_OFFLINE="W16";
    String WIDGET_HARDWARE_ERROR = "W17";
    String WIDGET_FM_TERMINAL = "W18";
    String WIDGET_CLIENT_TERMINAL = "W19";
    String WIDGET_DIGITAL_DISPLAY = "W20";
    String WIDGET_MERCHANT_TERMINAL_SUMMAY = "W21";
    String WIDGET_PUK_SUMMERY = "W22";
    String WIDGET_ACCESSORY_TYPE_SUMMARY = "W23";
    String WIDGET_ACCESSORY_MODEL_SUMMARY = "W24";
    String WIDGET_APP_DOWNLOAD_COUNT = "W25";

    String[] SUPPORTED_COLUMN_TYPES = new String[]{
            COLUMN_TYPE_C12,
            COLUMN_TYPE_C8,
            COLUMN_TYPE_C6,
            COLUMN_TYPE_C4
    };

    //已按显示name排序
    String[] SUPPORTED_WIDGETS = new String[]{
            WIDGET_TOP_10_APPS, //App Download Top 10
            WIDGET_FM_TERMINAL_ORGANIZE,
            WIDGET_FM_TERMINAL,
            WIDGET_MODEL_TERMINAL,
            WIDGET_PENDING_APPS,
            WIDGET_TERMINAL_OFFLINE,
            WIDGET_PUK_SUMMERY,
            WIDGET_RESELLER_SUMMARY,
            WIDGET_CLIENT_TERMINAL_ORG,
            WIDGET_CLIENT_TERMINAL,
            WIDGET_DIGITAL_DISPLAY,
            WIDGET_TERNIMAL_QUANTITY_SUMMARY,
            WIDGET_AUDIT_TRAIL_LOG,
            WIDGET_FM_TERMINAL_ORGANIZE,
            WIDGET_HARDWARE_ERROR,
            WIDGET_MAP,
            WIDGET_RESELLER_LEVEL_AUDIT_LIST,
            WIDGET_RESELLER_MERCHANT_SUMMARY,
            WIDGET_TERNIMAL_ONLINE_CHART,
            WIDGET_TERNIMAL_QUANTITY_SUMMARY,
            WIDGET_TERNIMAL_STATISTIC,
            WIDGET_TERMINAL_CRASH_REPORT,
            WIDGET_ALERT,
    };

    String[] BASIC_MARKET_SUPPORTED_WIDGETS = new String[]{
            WIDGET_TERNIMAL_QUANTITY_SUMMARY,
            WIDGET_MODEL_TERMINAL,
            WIDGET_RESELLER_SUMMARY
    };

}
