package com.pax.market.constants.airload;

import com.pax.market.StringCodeEnumConvert;

/**
 * <AUTHOR>
 * @date 2025/5/6 14:52
 */
public enum AirLoadTerminalStatus implements StringCodeEnumConvert {
    /**
     * 激活中
     */
    PENDING("P"),

    /**
     * 激活成功
     */
    ACTIVATED("A"),

    /**
     * 激活失败
     */
    FAILED("F");
    private final String code;

    AirLoadTerminalStatus(String name) {
        this.code = name;
    }

    @Override
    public String getCode() {
        return code;
    }
}
