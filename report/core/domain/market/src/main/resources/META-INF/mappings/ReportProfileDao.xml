<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.report.market.domain.dao.organization.ReportProfileDao">
    <select id="findProfileList" resultType="com.pax.market.domain.entity.market.organization.Profile">
        SELECT
            `id` AS "id",
            UPPER(`key`) AS "key",
            `value` AS "value",
            json_value  AS "jsonValue",
            reference_id AS "referenceId",
            `type` AS "type",
            `product_type` AS "productType",
            `created_date` AS "createdDate"
        FROM PAX_PROFILE
        WHERE reference_id=#{referenceId}
        AND `type`=#{type}
        AND product_type=#{productType}
    </select>

    <select id="checkProfileExist" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT id FROM PAX_PROFILE
            WHERE
            reference_id=#{referenceId}
            AND `type`=#{type}
            AND product_type=#{productType}
        )
    </select>
</mapper>