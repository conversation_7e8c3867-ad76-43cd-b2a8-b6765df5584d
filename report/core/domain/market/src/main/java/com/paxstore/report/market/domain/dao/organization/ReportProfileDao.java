package com.paxstore.report.market.domain.dao.organization;

import com.pax.market.domain.entity.market.organization.Profile;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ReportProfileDao extends CrudDao<Profile> {
    List<Profile> findProfileList(@Param("referenceId") Long referenceId,
                                  @Param("type") String type,
                                  @Param("productType") String productType);
    Boolean checkProfileExist(@Param("referenceId") Long referenceId,
                              @Param("type") String type,
                              @Param("productType") String productType);
}
