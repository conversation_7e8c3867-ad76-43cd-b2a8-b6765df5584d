package com.paxstore.report.market.domain.service.pushtask;

import com.pax.market.domain.entity.market.pushtask.TerminalGroupApkParam;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.LongUtils;
import com.paxstore.report.market.domain.dao.pushtask.ReportTerminalGroupApkParamDao;
import org.springframework.stereotype.Service;

@Service
public class ReportTerminalGroupApkParamService extends CrudService<ReportTerminalGroupApkParamDao, TerminalGroupApkParam> {

    public TerminalGroupApkParam getIncludeDeleted(Long id) {
        if (LongUtils.isBlankOrNotPositive(id)) {
            return null;
        }
        return super.get(id);
    }
}
