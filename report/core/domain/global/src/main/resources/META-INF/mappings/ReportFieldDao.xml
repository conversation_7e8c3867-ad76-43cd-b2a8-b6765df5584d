<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.report.global.domain.dao.report.ReportFieldDao">

	<sql id="reportFieldResultColumns">
		a.id AS "id",
		a.report_id AS "reportId",
		a.name AS "name",
		a.display_name AS "displayName",
		a.type AS "type",
		a.support_format AS "supportFormat",
		a.hideable AS "hideable",
		a.description AS "description",
		a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate",
		rff.format AS "defaultFormat"
	</sql>

	<select id="findList" resultType="com.pax.market.domain.entity.global.report.ReportField">
		SELECT 
		<include refid="reportFieldResultColumns"/>
		FROM pax_report_field a
		<include refid="joinFieldFormatForDefaultFormat"/>
		WHERE a.report_id=#{reportId}
		<if test="supportFormat != null">
			AND a.support_format = #{supportFormat}
		</if>
	</select>
	
	<select id="listAllReportFieldByReportId" resultType="com.pax.market.domain.entity.global.report.ReportField">
		SELECT
		<include refid="reportFieldResultColumns"/>
		FROM pax_report_field a
		<include refid="joinFieldFormatForDefaultFormat"/>
		WHERE a.report_id=#{reportId}
	</select>

	<sql id="joinFieldFormatForDefaultFormat">
		LEFT JOIN pax_report_field_format rff ON a.id=rff.field_id AND rff.predefined=1
	</sql>

</mapper>