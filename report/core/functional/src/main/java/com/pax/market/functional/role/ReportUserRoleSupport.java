package com.pax.market.functional.role;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.paxstore.report.global.domain.service.role.ReportRoleService;
import com.paxstore.report.market.domain.service.organization.ReportResellerService;
import lombok.RequiredArgsConstructor;

import java.util.List;

@FunctionalService
@RequiredArgsConstructor
public class ReportUserRoleSupport {
    private final ReportResellerService resellerService;
    private final ReportRoleService roleService;

    /**
     * Find user resellers list.
     *
     * @param userId     the user id
     * @param marketId   the market id
     * @return the list
     */
    public List<Reseller> findUserResellers(Long userId, Long marketId) {
        List<Long> resellerIds = roleService.findUserResellerIds(userId, marketId);
        return resellerService.findByIds(resellerIds);
    }
}
