<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2013 <PERSON><PERSON><PERSON>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License"); you may not
  ~ use this file except in compliance with the License. You may obtain a copy of
  ~ the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations under
  ~ the License.
  -->

 <rootBand name="Root" orientation="H">
        <bands>
            <band name="Main" orientation="H">
                <queries>
                    <query name="Main" type="springbean">
                        <script>
                            com.pax.market.report.common.springbeanloader.DemoSpringBeanBaseInfo.
								getBasicInfo(java.lang.String client, 
									java.lang.Integer invoiceNo)
                        </script>
                    </query>
                </queries>
            </band>
            <!-- <band name="Header" orientation="H">
            
            </band>
            
            <band name="Incomes" orientation="H">
                <queries>
                    <query name="Data_set_1" type="groovy">
                        <script>
                            return [['month':'Jan', 'profit':10000], ['month': 'Feb', 'profit': 12000], ['month': 'March', 'profit': 15000], ['month': 'Apr', 'profit': 12000]]
                        </script>
                    </query>
                </queries>
            </band> -->
            <band name="Title" orientation="H"></band>
			<band name="Header" orientation="H"></band>
            <band name="Items" orientation="H">
                <queries>
                    <query name="dataset_items" type="springbean">
                        <script>
                            com.pax.market.report.common.springbeanloader.DemoSpringBeanItemList.getItems(java.lang.String name)
                        </script>
                    </query>
                    
                </queries>
            </band>
			
			
        </bands>
        <queries/>
    </rootBand>
    

