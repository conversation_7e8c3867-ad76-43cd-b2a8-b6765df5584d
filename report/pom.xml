<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>p-market-backend</artifactId>
        <groupId>com.pax.market</groupId>
        <version>9.8.0-SNAPSHOT</version>
    </parent>

    <artifactId>p-market-report</artifactId>
    <packaging>pom</packaging>
    <name>PAX Market :: Report</name>

    <modules>
        <module>common</module>
        <module>biz</module>
        <module>engine</module>
        <module>config</module>
        <module>core</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pax.market</groupId>
                <artifactId>p-market-framework-web-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pax.market</groupId>
                <artifactId>p-market-mq-producer-kafka</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pax.market</groupId>
                <artifactId>p-market-framework-payment</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pax.market</groupId>
                <artifactId>p-market-framework-signature</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <!-- Define the plugins -->
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
