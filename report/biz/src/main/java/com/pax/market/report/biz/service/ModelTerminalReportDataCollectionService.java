package com.pax.market.report.biz.service;

import com.pax.market.constants.ReportEngineConstants;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.report.ModelTerminalReport;
import com.pax.market.domain.searchcriteria.ModelTerminalReportSearchCriteria;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.report.CommonReportFunctionalService;
import com.pax.market.report.common.utils.ConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 终端机型统计报表
 *
 * <AUTHOR>
 */
@Service("com.pax.market.report.biz.service.ModelTerminalReportDataCollectionService")
public class ModelTerminalReportDataCollectionService extends AbstractReportDataCollectionService<ModelTerminalReport> {
    @Autowired
    private CommonReportFunctionalService reportFunctionalService;


    @Override
    protected List<ModelTerminalReport> searchListData(Map<String, Object> parameters) {
        ModelTerminalReportSearchCriteria searchCriteria = parseParam(parameters);
        return reportFunctionalService.findModelTerminalReportList(searchCriteria);
    }

    private ModelTerminalReportSearchCriteria parseParam(Map<String, Object> parameters) {
        ModelTerminalReportSearchCriteria searchCriteria = new ModelTerminalReportSearchCriteria();
        searchCriteria.setMarketIds(StringUtils.splitToLongSet((String) parameters.get(ReportEngineConstants.SpecialParameter.KEY_MARKET_IDS), ","));
        searchCriteria.setResellerIds(StringUtils.splitToLongSet((String) parameters.get(ReportEngineConstants.SpecialParameter.KEY_RESELLER_IDS), ","));
        searchCriteria.setMerchantIds(StringUtils.splitToLongSet((String) parameters.get(ReportEngineConstants.SpecialParameter.KEY_MERCHANT_IDS), ","));
        searchCriteria.setModelIds(StringUtils.splitToLongSet((String) parameters.get(ReportEngineConstants.SpecialParameter.KEY_MODEL_IDS), ","));
        searchCriteria.setStatus(StringUtils.trim((String) parameters.get(ReportEngineConstants.SpecialParameter.KEY_STATUS)));
        searchCriteria.setCurrentMarketId((Long) parameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID));
        searchCriteria.setCurrentResellerId((Long) parameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID));
        return searchCriteria;
    }

    public Map<String, Object> getBasicInfo(Map<String, Object> parameters) {
        Map<String, Object> result = super.getBasicInfo(parameters);
        String marketIdStr = (String) parameters.get(ReportEngineConstants.SpecialParameter.KEY_MARKET_IDS);
        List<Long> marketIds = ConvertUtils.convertString2LongList(marketIdStr);
        Long currentMarketId = (Long) parameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID);
        if (!SystemConstants.SUPER_MARKET_ID.equals(currentMarketId)) {
            marketIds.add(currentMarketId);
        }
        result.put("marketName", getMarketNamesByMarketIds(marketIds));
        return result;
    }

}
