<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2013 Ha<PERSON><PERSON>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License"); you may not
  ~ use this file except in compliance with the License. You may obtain a copy of
  ~ the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations under
  ~ the License.
  -->

 <rootBand name="Root" orientation="H">
 
	<validateClass>
        com.pax.market.report.validator.ModelTerminalReportParameterValidator
	</validateClass>
    <bands>
		<band name="BasicInfo" orientation="H">
			<queries>
                <query name="BasicInfo" type="SimpleSpringbean">
                    <script>
                        com.pax.market.report.biz.service.ModelTerminalReportDataCollectionService.getBasicInfo
                    </script>
                </query>
            </queries>
		</band>
        <band name="Main" orientation="H">
            <queries>
                <query name="Main" type="SimpleSpringbean">
                    <script>
                        com.pax.market.report.biz.service.ModelTerminalReportDataCollectionService.loadListData
                    </script>
                </query>
            </queries>
        </band>
        
		<band name="Footer" orientation="H"></band>
    </bands>
    <queries/>
</rootBand>
    

