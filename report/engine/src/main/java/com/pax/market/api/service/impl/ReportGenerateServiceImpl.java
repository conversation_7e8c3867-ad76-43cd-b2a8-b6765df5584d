package com.pax.market.api.service.impl;


import com.haulmont.yarg.reporting.ReportOutputDocument;
import com.haulmont.yarg.reporting.ReportingAPI;
import com.haulmont.yarg.reporting.RunParams;
import com.haulmont.yarg.structure.Report;
import com.haulmont.yarg.structure.ReportBand;
import com.haulmont.yarg.structure.ReportOutputType;
import com.haulmont.yarg.structure.ReportParameter;
import com.haulmont.yarg.structure.impl.ReportBuilder;
import com.haulmont.yarg.structure.impl.ReportParameterImpl;
import com.haulmont.yarg.structure.impl.ReportTemplateBuilder;
import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.market.api.service.ReportGenerateService;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.ReportEngineConstants;
import com.pax.market.constants.ReportFormat;
import com.pax.market.constants.ReportTemplateStatus;
import com.pax.market.domain.entity.global.report.*;
import com.pax.market.dto.ReportScheduleType;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.response.report.FieldAlias2DisplayName;
import com.pax.market.framework.common.cron.CronUtils;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.IOUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.market.MarketUserFuncService;
import com.pax.market.mq.contract.report.SendReportFileMessage;
import com.pax.market.mq.producer.gateway.report.SendReportFileGateway;
import com.pax.market.report.common.exception.ReportException;
import com.pax.market.report.biz.service.ReportDynamicFieldService;
import com.pax.market.report.common.utils.ConvertUtils;
import com.pax.market.report.common.utils.ReportFileNameGenerateUtil;
import com.pax.market.report.engine.format.PaxReportFieldFormatImpl;
import com.pax.market.report.engine.utils.LoginProviderUtil;
import com.pax.market.report.engine.utils.POIUtil;
import com.pax.market.report.engine.xml.DefaultBandReader;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.report.global.domain.service.report.ReportFieldFormatService;
import com.paxstore.report.global.domain.service.report.ReportParameterService;
import com.paxstore.report.global.domain.service.report.ReportService;
import com.paxstore.report.global.domain.service.report.ReportTemplateService;
import com.paxstore.report.global.domain.service.report.ReportExecutionContextService;
import com.paxstore.report.global.domain.service.report.ReportJobHistoryService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;


@Service
@RequiredArgsConstructor
public class ReportGenerateServiceImpl implements ReportGenerateService {

    private static final Logger logger = LoggerFactory.getLogger(ReportGenerateServiceImpl.class);

    private final ReportService reportService;
    private final ReportTemplateService reportTemplateService;
    private final ReportFieldFormatService reportFieldFormatService;
    private final ReportParameterService reportParameterService;
    private final ReportExecutionContextService reportExecutionContextService;
    private final ReportJobHistoryService reportJobHistoryService;
    private final ReportingAPI reporting;
    private final ReportDynamicFieldService reportDynamicFieldService;
    private final SendReportFileGateway sendReportFileGateway;
    private final MarketUserFuncService marketUserFuncService;

    private ByteArrayInputStream convertToInputStream(Workbook wb) throws IOException {
        ByteArrayOutputStream bos = null;
        try {
            bos = new ByteArrayOutputStream();
            wb.write(bos);
            return new ByteArrayInputStream(bos.toByteArray());
        } finally {
            IOUtils.closeQuietly(bos);
        }
    }

    @Override
    public Report buildReport(Long reportId, Long userId, Long marketId, Long templateId, Map<String, String> parameters, String exportFields, String hideFields, String currentTz) throws ReportException {

        //get report entity from db
        com.pax.market.domain.entity.global.report.Report reportEntity = reportService.get(reportId);
        if (reportEntity == null) {
            return null;
        } else {
            ReportTemplate template = this.reportTemplateService.get(templateId);
            if (template == null) {
                throw new ReportException("No report template " + templateId);
            }
            template.setReportId(reportId);
            this.canUseTemplate(template, userId);
            ReportBuilder reportBuilder = new ReportBuilder();
            ReportTemplateBuilder reportTemplateBuilder = new ReportTemplateBuilder().
                    documentPath(template.getTemplateFileId()).
                    documentName(template.getDocumentName()).
                    outputType(ReportOutputType.getOutputTypeById(template.getOutputFormat()));

            InputStream templateInputStream = null;
            InputStream updatedTemplateInputStream = null;
            Workbook wb = null;
            try {
                templateInputStream = this.getReportTemplateDoc(template, parameters, exportFields, hideFields);
                reportTemplateBuilder.documentContent(templateInputStream);
            } catch (IOException e) {
                throw new ReportException("Get Report template content from " + template.getTemplateFileId() + " error!", e);
            } finally {
                IOUtils.closeQuietly(templateInputStream, updatedTemplateInputStream, wb);
            }

            reportBuilder.template(reportTemplateBuilder.build());
            ReportBand band;
            try {
                //解析band-xml文件
                band = new DefaultBandReader().parseXml(reportService.getBandFileContent(reportId));
            } catch (IOException e) {
                logger.error("Parse report band encounter exception for report " + reportId, e);
                throw new ReportException("Parse " + reportEntity.getBandFileName() + " error!", e);
            }
            for (ReportBand rb : band.getChildren()) {
                reportBuilder.band(rb);
            }

            //build report field formats
            List<PaxReportFieldFormatImpl> reportFieldFormats = this.getReportFieldFormatsForBuild(reportId, userId,
                    marketId, currentTz);
            if (CollectionUtils.isNotEmpty(reportFieldFormats)) {
                for (PaxReportFieldFormatImpl format : reportFieldFormats) {
                    reportBuilder.format(format);
                }
            }
            //build report parameters
            List<ReportParameter> reportParameters = this.prepareParameters(reportId, parameters);
            if (CollectionUtils.isNotEmpty(reportParameters)) {
                for (ReportParameter parameter : reportParameters) {
                    reportBuilder.parameter(parameter);
                }

                ReportParameter currentTzParameter = new ReportParameterImpl(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ, ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ,
                        Boolean.FALSE, String.class, currentTz);
                reportBuilder.parameter(currentTzParameter);
            }

            //build report
            return reportBuilder.build();
        }
    }

    private void handleSpecialOutputFieldFormat(List<PaxReportFieldFormatImpl> fieldFormatList, String currentTz) {
        if (!CollectionUtils.isEmpty(fieldFormatList)) {
            boolean hasReportGenerateDateFormat = false;
            for (PaxReportFieldFormatImpl fieldFormat : fieldFormatList) {
                if (StringUtils.equals(fieldFormat.getName(), ReportEngineConstants.SpecialOutputField.REPORT_GENERATE_DATE)) {
                    hasReportGenerateDateFormat = true;
                }
            }
            if (!hasReportGenerateDateFormat) {
                PaxReportFieldFormatImpl format = new PaxReportFieldFormatImpl(ReportEngineConstants.SpecialOutputField.REPORT_GENERATE_DATE,
                        ReportEngineConstants.DefaultFormat.FORMAT_REPORT_GENERATE_DATE);
                format.addFormatParameter(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ, currentTz);
                fieldFormatList.add(format);
            }
        } else {
            PaxReportFieldFormatImpl format = new PaxReportFieldFormatImpl(ReportEngineConstants.SpecialOutputField.REPORT_GENERATE_DATE,
                    ReportEngineConstants.DefaultFormat.FORMAT_REPORT_GENERATE_DATE);
            format.addFormatParameter(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ, currentTz);
            fieldFormatList.add(format);
        }
    }


    private boolean canUseTemplate(ReportTemplate template, Long userId) {
        if (template != null && StringUtils.equals(ReportTemplateStatus.Active.val(), template.getStatus())) {
            return true;
        } else {
            throw new BusinessException(ApiCodes.REPORT_TEMPLATE_CANNOT_USE_NOT_ACTIVE);
        }
    }

    @Override
    public List<ReportParameter> prepareParameters(Long reportId, Map<String, String> inputParameters) throws ReportException {
        List<com.pax.market.domain.entity.global.report.ReportParameter> reportParameterEntities =
                reportParameterService.getReportParameters(reportId);//.getReportParametersByUserId(reportId, userId);
        List<ReportParameter> result = new ArrayList<>();
        boolean isCurrentUserIdAdded = false;
        boolean isCurrentMarketIdAdded = false;
        boolean isCurrentResellerIdAdded = false;
        if (CollectionUtils.isNotEmpty(reportParameterEntities)) {
            for (com.pax.market.domain.entity.global.report.ReportParameter parameter : reportParameterEntities) {
                try {
                    if (LongUtils.equals(parameter.getUiComponentType(), ParameterUIComponentType.DATE_TIME_RANGE_PICKER)) {
                        String fromDate = inputParameters.get(parameter.getAliasFrom());
                        if (StringUtils.isNotEmpty(fromDate)) {
                            ReportParameter rp = new ReportParameterImpl(parameter.getName(), parameter.getAliasFrom(),
                                    parameter.isRequired(), ConvertUtils.convertTypeFromString(parameter.getParamType()), fromDate);
                            result.add(rp);
                        }
                        String toDate = inputParameters.get(parameter.getAliasTo());
                        if (StringUtils.isNotEmpty(toDate)) {
                            ReportParameter rp = new ReportParameterImpl(parameter.getName(), parameter.getAliasTo(),
                                    parameter.isRequired(), ConvertUtils.convertTypeFromString(parameter.getParamType()), toDate);
                            result.add(rp);
                        }
                    } else if (StringUtils.isNotEmpty(inputParameters.get(parameter.getAlias()))) {
                        //如果传入的parameters里面有优先使用传入的parameter，否则用默认的
                        ReportParameter rp = new ReportParameterImpl(parameter.getName(), parameter.getAlias(),
                                parameter.isRequired(), ConvertUtils.convertTypeFromString(parameter.getParamType()), inputParameters.get(parameter.getAlias()));

                        if (StringUtils.equals(parameter.getAlias(), ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID)) {
                            isCurrentMarketIdAdded = true;
                        }
                        if (StringUtils.equals(parameter.getAlias(), ReportEngineConstants.SpecialParameter.KEY_CURRENT_USER_ID)) {
                            isCurrentUserIdAdded = true;
                        }
                        if (StringUtils.equals(parameter.getAlias(), ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID)) {
                            isCurrentResellerIdAdded = true;
                        }
                        result.add(rp);
                    }
                } catch (ClassNotFoundException e) {
                    throw new ReportException("Report parameter type " + parameter.getParamType() + " error!", e);
                }
            }
        }
        if (!isCurrentUserIdAdded) {
            if (StringUtils.isNotEmpty(inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_USER_ID))) {
                ReportParameter rp = new ReportParameterImpl(ReportEngineConstants.SpecialParameter.KEY_CURRENT_USER_ID, ReportEngineConstants.SpecialParameter.KEY_CURRENT_USER_ID,
                        Boolean.FALSE, Long.class, inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_USER_ID));
                result.add(rp);
            }
        }
        if (!isCurrentMarketIdAdded) {
            if (StringUtils.isNotEmpty(inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID))) {
                ReportParameter rp = new ReportParameterImpl(ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID, ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID,
                        Boolean.FALSE, Long.class, inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID));
                result.add(rp);
            }
        }
        if (!isCurrentResellerIdAdded) {
            if (StringUtils.isNotEmpty(inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID))) {
                ReportParameter rp = new ReportParameterImpl(ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID, ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID,
                        Boolean.FALSE, Long.class, inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID));
                result.add(rp);
            }
        }
        if (inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_EXPORT_FIELDS) != null) {
            ReportParameter rp = new ReportParameterImpl(
                    ReportEngineConstants.SpecialParameter.KEY_EXPORT_FIELDS,
                    ReportEngineConstants.SpecialParameter.KEY_EXPORT_FIELDS,
                    Boolean.FALSE,
                    String.class,
                    inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_EXPORT_FIELDS));
            result.add(rp);
        }
        if (inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_HIDE_FIELDS) != null) {
            ReportParameter rp = new ReportParameterImpl(
                    ReportEngineConstants.SpecialParameter.KEY_HIDE_FIELDS,
                    ReportEngineConstants.SpecialParameter.KEY_HIDE_FIELDS,
                    Boolean.FALSE,
                    String.class,
                    inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_HIDE_FIELDS));
            result.add(rp);
        }
        if (inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_REPORT_ID) != null) {
            ReportParameter rp = new ReportParameterImpl(ReportEngineConstants.SpecialParameter.KEY_REPORT_ID, ReportEngineConstants.SpecialParameter.KEY_REPORT_ID,
                    Boolean.FALSE, Long.class, inputParameters.get(ReportEngineConstants.SpecialParameter.KEY_REPORT_ID));
            result.add(rp);
        }
        return result;
    }

    @Override
    public List<PaxReportFieldFormatImpl> getReportFieldFormatsForBuild(Long reportId, Long userId, Long marketId, String currentTz) {
        List<ReportFieldFormat> reportFieldFormatEntitys = reportFieldFormatService.getReportFieldFormatsByReportId(reportId, true);
        List<PaxReportFieldFormatImpl> fieldFormat4Build = new ArrayList<PaxReportFieldFormatImpl>();

        for (ReportFieldFormat fieldFormat : reportFieldFormatEntitys) {
            if (!fieldFormat.getField().isSupportFormat() && StringUtils.equalsIgnoreCase(fieldFormat.getField().getType(), "java.util.Date")) {
                PaxReportFieldFormatImpl format =
                        new PaxReportFieldFormatImpl(fieldFormat.getField().getName(), "yyyy-MM-dd HH:mm");
                format.addFormatParameter(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ, currentTz);
                fieldFormat4Build.add(format);
            } else {
                if (fieldFormat.getField().isSupportFormat()) {
                    PaxReportFieldFormatImpl format =
                            new PaxReportFieldFormatImpl(fieldFormat.getField().getName(), fieldFormat.getFormat());
                    format.addFormatParameter(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ, currentTz);
                    fieldFormat4Build.add(format);
                }
            }
        }

        this.handleSpecialOutputFieldFormat(fieldFormat4Build, currentTz);
        return fieldFormat4Build;
    }

    private InputStream getReportTemplateDoc(ReportTemplate template, Map<String, String> parameters, String exportFields, String hideFields) {
        InputStream templateInputStream = reportTemplateService.getTemplateFileStream(template.getId());

        if (StringUtils.equals(ReportFormat.XLSX.val(), template.getOutputFormat())) {
            Workbook wb = null;
            InputStream updatedTemplateInputStream = null;
            try {
                List<FieldAlias2DisplayName> dynamicFieldList = reportDynamicFieldService.getDynamicFieldList(template.getReportId(), parameters);
                if (StringUtils.isNotEmpty(exportFields)) {
                    List<String> exportFieldList = StringUtils.splitToList(exportFields, ",");
                    List<FieldAlias2DisplayName> displayDynamicFieldList = new ArrayList<>(exportFieldList.size());
                    for (FieldAlias2DisplayName field : dynamicFieldList) {
                        if (exportFieldList.contains(field.getAlias())) {
                            displayDynamicFieldList.add(field);
                        }
                    }
                    dynamicFieldList = displayDynamicFieldList;
                } else if (StringUtils.isNotEmpty(hideFields)) {
                    List<String> hideFieldList = StringUtils.splitToList(hideFields, ",");
                    List<FieldAlias2DisplayName> displayDynamicFieldList = new ArrayList<>(hideFieldList.size());
                    for (FieldAlias2DisplayName field : dynamicFieldList) {
                        if (hideFieldList.contains(field.getAlias())) {
                            continue;
                        }
                        displayDynamicFieldList.add(field);
                    }
                    dynamicFieldList = displayDynamicFieldList;
                }
                if (!Collections3.isEmpty(dynamicFieldList)) {
                    wb = POIUtil.getWorkbook(templateInputStream);
                    POIUtil.addColumnsByFieldName(wb, dynamicFieldList);
                    updatedTemplateInputStream = this.convertToInputStream(wb);
                    return updatedTemplateInputStream;
                }
            } catch (Exception ex) {
                logger.warn("Unable to add dynamic field to report template", ex);
            } finally {
                IOUtils.closeQuietly(wb);
                if (updatedTemplateInputStream != null) {
                    IOUtils.closeQuietly(templateInputStream);
                }
            }
        }
        return templateInputStream;
    }

    public void generateReport(String jobName) {
        ReportExecutionContext reportExecutionContext = reportExecutionContextService.getReportExecutionContextByJobName(jobName);
        if (reportExecutionContext == null) {
            logger.error("Failed to generate report by jobName {} cause of cannot find ReportExecutionContext", jobName);
        } else {
            if (BooleanUtils.isTrue(reportExecutionContext.getIsImmediate())) {
                if (ReportExecutionStatus.Inqueue != reportExecutionContext.getStatus()) {
                    return;
                }
            } else {
                if (ReportExecutionStatus.Running != reportExecutionContext.getStatus()) {
                    return;
                }
            }
            com.pax.market.domain.entity.global.report.Report reportEntity = this.reportService.get(reportExecutionContext.getReportId());
            if (reportEntity == null) {
                logger.warn("report is deleted cannot generate report");
                return;
            }
            ReportJobHistory jobHistory = new ReportJobHistory();
            jobHistory.setReportJobId(reportExecutionContext.getId());
            jobHistory.setExecuteTime(new Date());

            jobHistory.setEmailTo(reportExecutionContext.getEmailTo());
            if (reportExecutionContext.getStatus() != ReportExecutionStatus.Running) {
                reportExecutionContextService.updateReportExecutionContextStatus(ReportExecutionStatus.Running, reportExecutionContext);
            }
            try {
                LoginProviderUtil.setLoginProviderProperties(reportExecutionContext.getCurrentMarketId());
                UserInfo userInfo = marketUserFuncService.getUserInfoWithRoles(reportExecutionContext.getCurrentUserId(), reportExecutionContext.getCurrentMarketId(), reportExecutionContext.getCurrentResellerId());

                Map<String, String> paramValueMap = reportExecutionContext.getParameterValue();
                Map<String, String> paramValueMapForReportBuild = new HashMap<>();
                if (paramValueMap != null) {
                    paramValueMapForReportBuild.putAll(paramValueMap);
                }

                if (!reportService.checkReportRuntimePrivilege(reportExecutionContext.getReportId(), userInfo)) {
                    logger.warn("User does not have permission to run this report task, will terminate this task");
                    reportExecutionContextService.updateReportExecutionContextStatus(ReportExecutionStatus.Terminated, reportExecutionContext);
                    return;
                }

                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_REPORT_ID, String.valueOf(reportExecutionContext.getReportId()));
                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_CURRENT_USER_ID, String.valueOf(reportExecutionContext.getCurrentUserId()));
                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_CURRENT_MARKET_ID, String.valueOf(reportExecutionContext.getCurrentMarketId()));
                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_CURRENT_RESELLER_ID, String.valueOf(reportExecutionContext.getCurrentResellerId()));
                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_HIDE_FIELDS, reportExecutionContext.getHideFields());
                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_EXPORT_FIELDS, reportExecutionContext.getExportFields());
                paramValueMapForReportBuild.put(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ, reportExecutionContext.getCurrentTz());

                if (BooleanUtils.isNotTrue(reportExecutionContext.getIsImmediate())) {
                    if (StringUtils.equals(ReportScheduleType.Once.val(), reportExecutionContext.getScheduleType())) {
                        reportExecutionContext.setStatus(ReportExecutionStatus.Completed);
                        reportExecutionContext.setNextExecuteTime(null);
                        reportExecutionContextService.save(reportExecutionContext);
                    } else {
                        Date originalNextExeTime = reportExecutionContext.getNextExecuteTime();
                        Thread.sleep(3000);
                        Date nextExecuteTime = CronUtils.getNextExecuteTimeAfter(reportExecutionContext.getCron(), originalNextExeTime == null ? new Date() : originalNextExeTime);

                        if (nextExecuteTime == null) {
                            reportExecutionContext.setStatus(ReportExecutionStatus.Completed);
                            reportExecutionContext.setNextExecuteTime(null);
                            reportExecutionContextService.save(reportExecutionContext);
                        } else {
                            if (reportExecutionContext.getEndDate() != null
                                    && BooleanUtils.isNotTrue(reportExecutionContext.getIsImmediate())
                                    && reportExecutionContext.getEndDate().before(nextExecuteTime)) {
                                reportExecutionContext.setStatus(ReportExecutionStatus.Completed);
                                reportExecutionContext.setNextExecuteTime(null);
                            } else {
                                reportExecutionContext.setStatus(ReportExecutionStatus.WaitingNextTime);
                                reportExecutionContext.setNextExecuteTime(nextExecuteTime);
                            }

                            reportExecutionContextService.save(reportExecutionContext);
                        }
                    }

                }
                ReportTemplate templateEntity = reportTemplateService.getReportTemplateByReportIdAndExportFormat(reportExecutionContext.getReportId(), reportExecutionContext.getExportFormat());
                if (templateEntity == null) {
                    logger.warn("Report template not found for export format {} for reportId={}", reportExecutionContext.getExportFormat(), reportExecutionContext.getReportId());
                    reportExecutionContextService.updateReportExecutionContextStatus(ReportExecutionStatus.Terminated, reportExecutionContext);
                    return;
                }
                logger.info("buildReport: {}-{}", reportExecutionContext.getId(), reportExecutionContext.getJobName());
                Report report = buildReport(reportExecutionContext.getReportId(),
                        reportExecutionContext.getCurrentUserId(), reportExecutionContext.getCurrentMarketId(), templateEntity.getId(), paramValueMapForReportBuild,
                        reportExecutionContext.getExportFields(), reportExecutionContext.getHideFields(), reportExecutionContext.getCurrentTz());
                logger.info("runReport: {}-{}", reportExecutionContext.getId(), reportExecutionContext.getJobName());
                //开始生成报告文件
                ReportOutputDocument reportOutputdocument = reporting.runReport(new RunParams(report));
                logger.info("uploadReport: {}-{}", reportExecutionContext.getId(), reportExecutionContext.getJobName());
                //上传文件到文件服务器
                String fileId = FileUploader.uploadFile(reportOutputdocument.getContent(), com.pax.market.framework.common.utils.FileUtils.getFileExtension(reportOutputdocument.getDocumentName()), SupportedFileTypes.REPORT);
                if (BooleanUtils.isTrue(reportExecutionContext.getIsImmediate())) {
                    reportExecutionContextService.updateReportExecutionContextStatus(ReportExecutionStatus.Completed, reportExecutionContext);
                }

                jobHistory.setReportFileId(fileId);
                jobHistory.setIsSuccess(Boolean.TRUE);
                String generatedReportFileName = ReportFileNameGenerateUtil.generateReportFileName(reportExecutionContext.getCustomReportName(), templateEntity.getOutputFormat());
                jobHistory.setReportFileName(generatedReportFileName);
                logger.info("finishReport: {}-{}", reportExecutionContext.getId(), reportExecutionContext.getJobName());
            } catch (Exception e) {
                if (BooleanUtils.isTrue(reportExecutionContext.getIsImmediate())) {
                    reportExecutionContextService.updateReportExecutionContextStatus(ReportExecutionStatus.Failed, reportExecutionContext);
                }
                logger.warn("Generate report " + reportExecutionContext.getJobName() + " failed", e);
                jobHistory.setIsSuccess(Boolean.FALSE);
            } finally {
                if (jobHistory.getIsSuccess() == null) {
                    jobHistory.setIsSuccess(Boolean.FALSE);
                }
                this.reportJobHistoryService.save(jobHistory);
                if (BooleanUtils.isTrue(jobHistory.getIsSuccess()) && StringUtils.isNotEmpty(reportExecutionContext.getEmailTo())) {
                    logger.info("Start to send report file by email, report job name is {}", reportExecutionContext.getJobName());
                    SendReportFileMessage message = new SendReportFileMessage();
                    message.setMarketId(reportExecutionContext.getCurrentMarketId());
                    message.setReportMarketId(reportExecutionContext.getCurrentMarketId());
                    message.setSendTo(reportExecutionContext.getEmailTo());
                    message.setReportJobHistoryId(jobHistory.getId());
                    message.setCurrentTz(reportExecutionContext.getCurrentTz());
                    sendReportFileGateway.send(message);
                }
                LoginProviderUtil.clearLoginProviderProperties();
            }
        }
    }
}
