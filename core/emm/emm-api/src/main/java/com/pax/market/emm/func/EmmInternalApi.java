/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.emm.func;

import com.pax.market.dto.emm.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
public interface EmmInternalApi {


    List<PolicyInfo> pageQueryPoliciesFromGoogle(String enterpriseName, Integer pageSize, Integer pageNum);

    PolicyInfo getPolicyByName(String policyName);

    void deletePolicyByName(String policyName);

    List<DeviceInfo> pageQueryDevicesFromGoogle(String enterpriseName, Integer pageSize, Integer pageNum);

    DeviceInfo getDeviceByDeviceName(String deviceName);

    void deleteDeviceByName(String deviceName);

    EnterpriseInfo getEnterpriseByName(String enterpriseName);

    EnterpriseInfo getEnterprise(String enterpriseName);

    List<EnterpriseInfo> pageQueryEnterprise(String projectId, Integer pageSize, Integer pageNum);

    void deleteEnterpriseByName(String enterpriseName);

    void updateEnterpriseByName(String enterpriseName, EnterpriseInfo enterpriseInfo);

    EnrollmentTokenInfo getEnrollmentTokenByName(String tokenName);

    List<EnrollmentTokenInfo> pageQueryEnrollmentToken(String enterpriseName, Integer pageSize, Integer pageNum);

    void deleteEnrollmentToken(String enrollmentTokenName);

    EmmAppInfo getEmmAppInfo(String enterpriseId, String packageName, Boolean onlyTrack);
}
