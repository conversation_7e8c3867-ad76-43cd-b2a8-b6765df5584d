/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.developer.impl;

import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.developer.DevCustomParamTemplate;
import com.pax.market.domain.entity.market.sandbox.SandboxTerminalApk;
import com.paxstore.market.domain.util.ApkParameterUtils;
import com.pax.market.domain.parameter.SchemaProcess;
import com.pax.market.domain.parameter.XmlUtils;
import com.paxstore.global.domain.service.app.ApkParamTemplateService;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.service.developer.DevParamTemplateService;
import com.paxstore.market.domain.service.sandbox.SandboxTerminalApkService;
import com.pax.market.functional.utils.ApkParameterParserUtils;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.apk.ApkDeveloperValidator;
import com.pax.market.functional.validation.validators.app.AppDeveloperValidator;
import com.pax.market.dto.request.developer.app.DownloadApkParamDataRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.NotFoundException;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.developer.DeveloperAppApkDownloadFuncService;
import com.pax.market.functional.developer.DeveloperParamTemplateFuncService;
import com.pax.market.functional.utils.DownloadTaskUtils;
import com.pax.market.vo.developer.customparam.ParamTemplateDetailVo;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/5/16
 */
@FunctionalService
public class DeveloperAppApkDownloadFuncServiceImpl extends AbstractFunctionalService implements DeveloperAppApkDownloadFuncService {

    @Autowired
    private AppService appService;
    @Autowired
    private ApkService apkService;
    @Autowired
    private ApkParamTemplateService apkParamTemplateService;
    @Autowired
    private DevParamTemplateService devParamTemplateService;
    @Autowired
    private SandboxTerminalApkService sandboxTerminalApkService;

    @Autowired
    private DeveloperParamTemplateFuncService paramTemplateFuncService;

    @Override
    public Long createOriginalApkDownloadTask(Long apkId, Long factoryId) {
        Apk apk = apkService.get(apkId);
        Validators.validate(new ApkDeveloperValidator(apk, false));
        apkService.loadApkFile(apk, factoryId);
        return DownloadTaskUtils.createDownloadTaskForApk(apk);
    }


    @Override
    public Long createApkParamTemplateDownloadTask(Long apkId, String paramTemplateName) {
        Validators.validate(new ApkDeveloperValidator(apkService.get(apkId), false));
        ApkParamTemplate apkParamTemplate = apkParamTemplateService.getParamTemplate(apkId, paramTemplateName);
        return DownloadTaskUtils.createDownloadTaskForParamTemplate(apkParamTemplate);
    }


    @Override
    public Long createParameterDataFileDownloadTask(Long apkId, DownloadApkParamDataRequest downloadApkParamDataRequest) {
        Apk apk = apkService.get(apkId);
        Validators.validate(new ApkDeveloperValidator(apk, false));
        return DownloadTaskUtils.createParameterDataFileDownloadTask(apk, downloadApkParamDataRequest.getDownloadToken());
    }

    @Override
    public Long downloadParamTemplatePoFiles(Long paramTemplateId, boolean encrypted) {
        ParamTemplateDetailVo paramTemplate = paramTemplateFuncService.getParamTemplate(paramTemplateId);
        String paramTemplateXml = XmlUtils.fromJson(paramTemplate.getSchema());
        ApkParamTemplate apkParamTemplate = ApkParameterParserUtils.parseParamTemplate(paramTemplate.getName(), paramTemplateXml.getBytes(), false, false);
        SchemaProcess schemaProcess = ApkParameterUtils.getSchemaProcess(apkParamTemplate, null, null, false);
        if (schemaProcess == null) {
            throw new NotFoundException(ApiCodes.APK_PARAM_TEMPLATE_NOT_FOUND);
        }
        String appSecret = null;
        if (encrypted) {
            App app = appService.getWithKeyAndSecret(paramTemplate.getAppId());
            Validators.validate(new AppDeveloperValidator(app, false));
            appSecret = app.getAppSecret();
        }
        return DownloadTaskUtils.createApkParamTemplateDownloadPoFilesTask(schemaProcess, appSecret);
    }

    @Override
    public Long downloadParamTemplate(Long paramTemplateId) {
        DevCustomParamTemplate devCustomParamTemplate = devParamTemplateService.validateAndGetParamTemplate(paramTemplateId);
        String paramTemplateXml = XmlUtils.fromJson(devCustomParamTemplate.getSchema());
        ApkParamTemplate apkParamTemplate = ApkParameterParserUtils.parseParamTemplate(devCustomParamTemplate.getName(), paramTemplateXml.getBytes(), false, false);
        return DownloadTaskUtils.createDownloadTaskForParamTemplate(apkParamTemplate);
    }

    @Override
    public Long createSandboxTerminalApkDataDownloadTask(Long sandboxTerminalApkId, String downloadToken) {
        SandboxTerminalApk terminalApk = sandboxTerminalApkService.get(sandboxTerminalApkId);
        terminalApk.setApk(apkService.get(terminalApk.getApk().getId()));
        checkNotNull(terminalApk, ApiCodes.FILE_NOT_FOUND);
        terminalApk.setApk(apkService.get(terminalApk.getApk()));
        return DownloadTaskUtils.createParameterDataFileDownloadTask(terminalApk.getApk(), downloadToken);
    }
}