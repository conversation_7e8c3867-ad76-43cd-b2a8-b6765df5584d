package com.pax.market.functional.admin.management.emm.impl;

import com.google.common.collect.Lists;
import com.pax.market.audit.remote.AuditLogRemoteService;
import com.pax.market.domain.entity.global.app.IconFileMd5;
import com.pax.market.domain.entity.global.emm.EmmEnterprise;
import com.pax.market.domain.entity.market.emm.EmmDevice;
import com.pax.market.domain.entity.market.emm.EmmDeviceDetail;
import com.pax.market.domain.entity.market.emm.EmmDeviceInstalledApp;
import com.pax.market.domain.entity.market.emm.EmmDeviceLocation;
import com.pax.market.domain.util.FileDownloadUrlGenerator;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.functional.emm.AbstractEmmServiceFunc;
import com.pax.market.functional.admin.management.emm.AdminEmmDeviceBasicFuncService;
import com.pax.market.functional.emm.EmmDeviceFunc;
import com.pax.market.vo.admin.management.emm.*;
import com.pax.market.vo.admin.management.terminal.TerminalDetailPageVo;
import com.pax.market.vo.admin.system.auditlog.AuditLogVoResponse;
import com.paxstore.global.domain.service.app.IconFileMd5Service;
import com.paxstore.market.domain.service.emm.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/6
 */
@FunctionalService
@RequiredArgsConstructor
public class AdminEmmDeviceBasicFuncServiceImpl extends AbstractEmmServiceFunc implements AdminEmmDeviceBasicFuncService {

    private final EmmDeviceFunc deviceFunc;
    private final EmmDeviceDetailService deviceDetailService;
    private final EmmDeviceMonitorService deviceMonitorService;
    private final EmmDeviceTrafficService deviceTrafficService;
    private final EmmDeviceLocationService deviceLocationService;
    private final EmmDeviceInstalledAppService deviceInstalledAppService;
    private final IconFileMd5Service fileMd5Service;

    @Override
    public TerminalDetailPageVo<EmmDeviceDetailVo> findEmmDeviceDetailPage(Long deviceId) {
        EmmDevice emmDevice = deviceFunc.validateEmmDevice(deviceId);
        EmmEnterprise enterprise = validateEmm4ResellerAvailable(getCurrentMarketId(), emmDevice.getResellerId());
        List<EmmDeviceDetail> deviceDetails = deviceDetailService.findByDevice(deviceId);
        deviceFunc.fillDetailsFromGoogleIfEmpty(deviceDetails, emmDevice, enterprise.getEnterpriseId());

        TerminalDetailPageVo<EmmDeviceDetailVo> terminalDetailPageVo = new TerminalDetailPageVo<>();
        terminalDetailPageVo.setList(BeanMapper.mapList(deviceDetails, EmmDeviceDetailVo.class));
        terminalDetailPageVo.setTotalCount(deviceDetails.size());
        return terminalDetailPageVo;
    }

    @Override
    public EmmDeviceMonitorVo getEmmDeviceMonitorVo(Long deviceId) {
        validateDevice(deviceId);
        return BeanMapper.map(deviceMonitorService.getByDevice(deviceId), EmmDeviceMonitorVo.class);
    }

    @Override
    public EmmDeviceTrafficVo getEmmDeviceTrafficVo(Long deviceId) {
        validateDevice(deviceId);
        return BeanMapper.map(deviceTrafficService.getEmmDeviceTraffic(deviceId), EmmDeviceTrafficVo.class);
    }

    @Override
    public EmmDeviceLocationVo getEmmDeviceLocationVo(Long deviceId) {
        validateDevice(deviceId);
        return BeanMapper.map(getLocation(deviceId), EmmDeviceLocationVo.class);
    }

    private EmmDeviceLocation getLocation(Long deviceId) {
        EmmDeviceLocation location = deviceLocationService.getByDevice(deviceId);
        if (location == null) {
            return new EmmDeviceLocation();
        }
        return location;
    }

    @Override
    public PageInfo<EmmDeviceInstalledAppVo> findEmmDeviceInstalledAppPage(Page<EmmDeviceInstalledApp> page, Long deviceId) {
        validateDevice(deviceId);
        Page<EmmDeviceInstalledApp> installedAppPage = deviceInstalledAppService.findPage(page, new EmmDeviceInstalledApp(deviceId));
        List<EmmDeviceInstalledApp> installedApps = installedAppPage.getList();
        if(CollectionUtils.isEmpty(installedApps)) {
            return new PageInfo<>();
        }
        List<EmmDeviceInstalledAppVo> installedAppVos = Lists.newArrayListWithCapacity(installedApps.size());
        for(EmmDeviceInstalledApp installedApp : installedApps) {
            EmmDeviceInstalledAppVo installedAppVo = BeanMapper.map(installedApp, EmmDeviceInstalledAppVo.class);
            if(StringUtils.isNotEmpty(installedApp.getIconMd5())) {
                IconFileMd5 iconFileMd5 = fileMd5Service.getByMd5(installedApp.getIconMd5());
                if(iconFileMd5 != null) {
                    installedAppVo.setIconUrl(FileDownloadUrlGenerator.generateFileUrl(iconFileMd5.getIconFileId()));
                }
            }
            installedAppVos.add(installedAppVo);
        }
        return new PageInfo<>(installedAppVos, installedAppPage.getCount());
    }

    @Override
    public PageInfo<AuditLogVoResponse.OperationLogVo> findEmmDeviceAuditLogPage(Long deviceId, Integer year, String tz) {
        validateDevice(deviceId);
        PageInfo<AuditLogVoResponse.OperationLogVo> emmDeviceAuditTrail =
                SpringContextHolder.getBean(AuditLogRemoteService.class).findTerminalAuditTrail(parsePage(), year, tz, getCurrentMarketId(), deviceId);
        loadTerminalAuditLogBizMessage(emmDeviceAuditTrail.getList());
        return new PageInfo<>(emmDeviceAuditTrail.getList(), emmDeviceAuditTrail.getTotalCount());
    }

    private void loadTerminalAuditLogBizMessage(List<AuditLogVoResponse.OperationLogVo> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(audit -> {
                if (audit.getBizCode() > 0) {
                    Object[] args = null;
                    if (StringUtils.isNotBlank(audit.getMsgArgs())) {
                        args = audit.getMsgArgs().split(",");
                    }
                    audit.setBizMessage(MessageUtils.getLocaleMessage(String.valueOf(audit.getBizCode()), args));
                }
            });
        }
    }

    private void validateDevice(Long deviceId) {
        EmmDevice emmDevice = deviceFunc.validateEmmDevice(deviceId);
        validateEmm4ResellerAvailable(getCurrentMarketId(), emmDevice.getResellerId());
    }

}
