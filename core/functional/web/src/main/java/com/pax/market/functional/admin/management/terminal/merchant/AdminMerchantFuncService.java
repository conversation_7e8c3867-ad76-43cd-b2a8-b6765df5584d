/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.management.terminal.merchant;

import com.pax.market.dto.PageInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.request.activity.MerchantExportRequest;
import com.pax.market.dto.request.organization.CreateMerchantUserRequest;
import com.pax.market.dto.request.organization.MerchantCreateRequest;
import com.pax.market.dto.request.organization.MerchantUpdateRequest;
import com.pax.market.vo.admin.management.terminal.merchant.MerchantCreateVo;
import com.pax.market.vo.admin.management.terminal.merchant.MerchantDetailVo;
import com.pax.market.vo.admin.management.terminal.merchant.MerchantTreeVo;
import com.pax.market.vo.admin.management.terminal.merchant.OrganizationMerchantVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * 终端管理模块---代理商相关接口
 *
 * <AUTHOR>
 * @date 2022/9/21
 */
public interface AdminMerchantFuncService {

    PageInfo<MerchantTreeVo> findMerchants4Tree(long resellerId, int startIndex, String status, String name);

    PageInfo<OrganizationMerchantVo> findMerchantPageForOrganization(Long resellerId, String productType, String status, String name);

    MerchantDetailVo getMerchantDetailVo(long merchantId);

    MerchantCreateVo createMerchant(MerchantCreateRequest request);

    void updateMerchant(long merchantId, MerchantUpdateRequest request);

    void activeMerchant(Long merchantId);

    void disableMerchant(Long merchantId);

    void activeMerchantResendEmail(Long merchantId);

    void replaceMerchantEmail(Long merchantId, CreateMerchantUserRequest createMerchantUserRequest);

    void moveMerchant(Long merchantId, String targetResellerName);

    void deleteMerchant(Long merchantId);

    Long createMerchantImportTemplateDownloadTask(InputStream inputStream) throws IOException;

    ImportExportActivityInfo importMerchant(MultipartFile merchants, String tz) throws IOException;

    ImportExportActivityInfo exportMerchant(MerchantExportRequest request, String tz) throws IOException;


}