package com.pax.market.functional.admin.management.emm.impl;

import com.google.common.collect.Lists;
import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.DownloadTask;
import com.pax.market.domain.entity.market.emm.EmmDevice;
import com.pax.market.domain.entity.market.emm.EmmDeviceVariable;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.domain.util.WebCryptoUtils;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.request.emm.EmmDeviceExportRequest;
import com.pax.market.dto.request.variable.ParameterVariableDeleteRequest;
import com.pax.market.dto.request.variable.ParameterVariableRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.excel.ExportExcel;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.exception.http.NotFoundException;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventAccumulator;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventListener;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.functional.activity.exports.EmmDeviceVariableExportService;
import com.pax.market.functional.activity.imports.variable.EmmDeviceVariableImportService;
import com.pax.market.functional.emm.AbstractEmmServiceFunc;
import com.pax.market.functional.admin.management.emm.AdminEmmDeviceVariableFuncService;
import com.pax.market.functional.emm.EmmDeviceFunc;
import com.pax.market.functional.validation.validators.emm.EmmDeviceExportRequestValidator;
import com.pax.market.functional.validation.validators.reseller.ResellerActiveValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalOperationValidator;
import com.pax.market.vo.admin.common.variable.ParameterVariableVo;
import com.paxstore.global.domain.service.role.OperationService;
import com.paxstore.market.domain.service.DownloadTaskService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.service.variable.EmmDeviceVariableService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import static com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent.EventType.EMM_DEVICE_VARIABLE_CHANGED;

/**
 * <AUTHOR>
 * @Date 2025/2/6
 */
@FunctionalService
@RequiredArgsConstructor
public class AdminEmmDeviceVariableFuncServiceImpl extends AbstractEmmServiceFunc implements AdminEmmDeviceVariableFuncService {

    private final DownloadTaskService downloadTaskService;
    private final EmmDeviceVariableImportService variableImportService;
    private final EmmDeviceVariableExportService variableExportService;
    private final ResellerService resellerService;
    private final EmmDeviceVariableService variableService;
    private final EmmDeviceFunc emmDeviceFunc;
    private final OperationService operationService;

    @Override
    public PageInfo<ParameterVariableVo> findEmmDeviceVariablePage(Page<EmmDeviceVariable> page, Long emmDeviceId, String key) {
        EmmDevice emmDevice = emmDeviceFunc.validateEmmDevice(emmDeviceId);
        EmmDeviceVariable emmDeviceVariable = new EmmDeviceVariable();
        emmDeviceVariable.setEmmDevice(emmDevice);
        emmDeviceVariable.setKey(key);
        Page<EmmDeviceVariable> variablePage = variableService.findPage(page, emmDeviceVariable);
        variablePage.getList().forEach(WebCryptoUtils::maskPasswordVariable);
        return new PageInfo<>(BeanMapper.mapList(variablePage.getList(), ParameterVariableVo.class), variablePage.getCount());
    }

    @Override
    public void createEmmDeviceVariable(ParameterVariableRequest request) {
        validateVariableAccess();
        EmmDevice emmDevice = emmDeviceFunc.validateEmmDevice(request.getTerminalId());
        validateEmm4ResellerAvailable(getCurrentMarketId(), emmDevice.getResellerId());
        WebCryptoUtils.unMaskAndDecryptPasswordVariable(null, request);
        EmmDeviceVariable deviceVariable = BeanMapper.map(request, EmmDeviceVariable.class);
        deviceVariable.setEmmDevice(emmDevice);
        variableService.save(deviceVariable);
    }

    @Override
    public void updateEmmDeviceVariable(Long variableId, ParameterVariableRequest request) {
        EmmDeviceVariable deviceVariable = validateEmmDeviceVariable(variableId);
        WebCryptoUtils.unMaskAndDecryptPasswordVariable(deviceVariable, request);
        BeanMapper.copy(request, deviceVariable);
        variableService.save(deviceVariable);
    }

    @Override
    public void deleteEmmDeviceVariable(Long variableId) {
        EmmDeviceVariable deviceVariable = validateEmmDeviceVariable(variableId);
        variableService.delete(deviceVariable);
        EntityChangedEventAccumulator.add(new EntityChangedEventListener.EntityChangedEvent(EMM_DEVICE_VARIABLE_CHANGED, deviceVariable.getReferenceId()));
    }

    @Override
    public void batchDeleteEmmDeviceVariables(ParameterVariableDeleteRequest request) {
        validateVariableAccess();
        if (request == null || CollectionUtils.isEmpty(request.getVariableIds())) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        EmmDeviceVariable deviceVariable = validateEmmDeviceVariable(request.getVariableIds().get(0));
        variableService.deleteList(request.getVariableIds());
        EntityChangedEventAccumulator.add(new EntityChangedEventListener.EntityChangedEvent(EMM_DEVICE_VARIABLE_CHANGED, deviceVariable.getReferenceId()));
    }

    @Override
    public Long createEmmDeviceVariableImportTemplateDownloadTask() {
        String title = getLocaleMessageForExport("title.emm.device.variables");
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ExportExcel exportExcel = new ExportExcel(title, EmmDeviceVariable.class, 2, true, LocaleUtils.getEnvLocale())) {
            exportExcel.setDataList(generateEmmDeviceVariableList4Template()).write(bos);
            String fileId = FileUploader.uploadFile(bos.toByteArray(), "EmmDeviceVariables", ".xlsx", SupportedFileTypes.TEMP);
            DownloadTask downloadTask = downloadTaskService.createDownloadTask(title + ".xlsx", fileId, DownloadTaskStatus.PENDING, true, DownloadTaskType.TERMINAL_VARIABLE_IMPORT_TEMPLATE);
            return downloadTask.getId();
        } catch (Exception e) {
            logger.error("Error download emm device variable template", e);
            throw new BusinessException(ApiCodes.VARIABLE_TEMPLATE_DOWNLOAD_FAILED);// NOSONAR
        }
    }


    @Override
    public ImportExportActivityInfo importEmmDeviceVariable(MultipartFile file, String tz) throws IOException {
        validateEmm4MarketAvailable(getCurrentMarketId());
        new TerminalOperationValidator(MarketSettingKey.tmParameterVariables).validate();
        if (file == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        Activity activity = variableImportService.importData(file.getOriginalFilename(), file.getBytes(), tz);
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }

    @Override
    public ImportExportActivityInfo createExportEmmDeviceVariableDownloadTask(EmmDeviceExportRequest request, String tz) {
        if (LongUtils.isBlankOrNotPositive(request.getResellerId())) {
            request.setResellerId(getCurrentResellerId());
        } else {
            Reseller reseller = resellerService.get(request.getResellerId());
            new ResellerActiveValidator(reseller).validate();
        }
        request.setOrderBy(parsePage().getOrderBy());
        new EmmDeviceExportRequestValidator(request).validate();
        Activity activity = variableExportService.exportData(request, tz);
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }

    private List<EmmDeviceVariable> generateEmmDeviceVariableList4Template() {
        List<EmmDeviceVariable> list = Lists.newArrayListWithCapacity(3);
        EmmDeviceVariable variable1 = new EmmDeviceVariable();
        String serialNo = "SN123456789";
        variable1.setEmmDevice(new EmmDevice(serialNo));
        variable1.setType(VariableType.TEXT);
        variable1.setKey("RESELLER_NAME");
        variable1.setValue(getCurrentReseller().getName());
        EmmDeviceVariable variable2 = new EmmDeviceVariable();
        variable2.setEmmDevice(new EmmDevice(serialNo));
        variable2.setKey("USER_NAME");
        variable2.setType(VariableType.TEXT);
        variable2.setValue(getCurrentUser().getName());
        EmmDeviceVariable variable3 = new EmmDeviceVariable();
        variable3.setEmmDevice(new EmmDevice(serialNo));
        variable3.setKey("PASSWORD");
        variable3.setType(VariableType.PASSWORD);
        variable3.setValue("123456");
        list.add(variable1);
        list.add(variable2);
        list.add(variable3);
        return list;
    }

    private void validateVariableAccess() {
        if (!operationService.isUserOperationAvailable(MarketSettingKey.tmParameterVariables)) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
    }

    private EmmDeviceVariable validateEmmDeviceVariable(Long variableId) {
        validateVariableAccess();
        EmmDeviceVariable variable = variableService.get(variableId);
        if (variable == null) {
            throw new NotFoundException(ApiCodes.VARIABLE_NOT_FOUND);
        }
        validateEmm4ResellerAvailable(getCurrentMarketId(), emmDeviceFunc.validateEmmDevice(variable.getReferenceId()).getResellerId());
        return variable;
    }

}
