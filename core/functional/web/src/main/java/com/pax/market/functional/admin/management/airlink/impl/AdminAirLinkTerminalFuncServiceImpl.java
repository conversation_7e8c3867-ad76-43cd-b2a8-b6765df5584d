package com.pax.market.functional.admin.management.airlink.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.core.json.JsonMapper;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.DownloadTaskStatus;
import com.pax.market.constants.DownloadTaskType;
import com.pax.market.constants.airlink.AirLinkEstateStatus;
import com.pax.market.constants.airlink.AirLinkTerminalProfileStatus;
import com.pax.market.constants.airlink.AirLinkTerminalStatus;
import com.pax.market.constants.airlink.AirLinkTerminalTaskType;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkEstate;
import com.pax.market.domain.entity.global.vas.airlink.MarketAirLinkSetting;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.DownloadTask;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalActiveHistory;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile;
import com.pax.market.domain.query.AirLinkTerminalActiveHistoryQuery;
import com.pax.market.domain.query.AirLinkTerminalQuery;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.airlink.*;
import com.pax.market.dto.billing.ServiceUsageDetailInfo;
import com.pax.market.dto.request.activity.AirLinkTerminalActivateExportRequest;
import com.pax.market.dto.request.vas.AirLinkDeletedTerminalRequest;
import com.pax.market.dto.request.vas.AirLinkTerminalBatchRequest;
import com.pax.market.dto.request.vas.AirLinkTerminalRequest;
import com.pax.market.dto.vas.AirLinkDataPoolSettingInfo;
import com.pax.market.dto.vas.MarketAirLinkSettingInfo;
import com.pax.market.dto.vas.MarketAirLinkSubscriptionPlanInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.activity.exports.AirLinkTerminalActivateExportService;
import com.pax.market.functional.activity.imports.AirLinkTerminalImportService;
import com.pax.market.functional.admin.management.airlink.AdminAirLinkTerminalFuncService;
import com.pax.market.functional.support.AirLinkTerminalSupport;
import com.pax.market.mq.contract.airlink.AirLinkTerminalActiveMessage;
import com.pax.market.mq.contract.billing.SyncAirLinkUsageDetailMessage;
import com.pax.market.mq.producer.gateway.airlink.AirLinkTerminalActiveGateway;
import com.pax.market.mq.producer.gateway.billing.SyncData2BillingGateway;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.vas.common.VasConstants;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.global.domain.service.vas.Market2ServiceReadonlyService;
import com.paxstore.global.domain.service.vas.ServiceResellerService;
import com.paxstore.global.domain.service.vas.airlink.AirLinkEstateService;
import com.paxstore.global.domain.service.vas.airlink.MarketAirLinkSettingService;
import com.paxstore.market.domain.service.DownloadTaskService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalActiveHistoryService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalProfileService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalTaskService;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.vas.func.platform.VasPlatformFunc;
import com.zolon.saas.vas.func.platform.request.CardsRequest;
import com.zolon.saas.vas.func.platform.request.QueryCardUsageRequest;
import com.zolon.saas.vas.func.platform.request.SwitchCardsRequest;
import com.zolon.saas.vas.func.platform.response.BatchQueryCardDetailResponse;
import com.zolon.saas.vas.func.platform.response.DeleteCardsResponse;
import com.zolon.saas.vas.func.platform.response.QueryCardDetailResponse;
import com.zolon.saas.vas.func.platform.response.QueryCardUsageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author mengxiaoxian
 * Date   2025/3/13 13:20
 */
@Slf4j
@FunctionalService
@RequiredArgsConstructor
public class AdminAirLinkTerminalFuncServiceImpl extends AbstractFunctionalService implements AdminAirLinkTerminalFuncService {
    private static final String TIME_RANGE_TYPE_7D = "7d";
    private static final String TIME_RANGE_TYPE_30D = "30d";
    private static final String TIME_RANGE_TYPE_6M = "6m";

    private final MarketAirLinkSettingService marketAirLinkSettingService;
    private final AirLinkTerminalService airLinkTerminalService;
    private final AirLinkTerminalProfileService airLinkTerminalProfileService;
    private final AirLinkTerminalImportService airLinkTerminalImportService;
    private final TerminalRegistryService terminalRegistryService;
    private final ResellerService resellerService;
    private final MerchantService merchantService;
    private final Market2ServiceReadonlyService market2ServiceReadonlyService;
    private final DownloadTaskService downloadTaskService;
    private final ServiceResellerService serviceResellerService;
    private final AirLinkTerminalSupport airLinkTerminalSupport;
    private final AirLinkTerminalActivateExportService airLinkTerminalActivateExportService;
    private final VasPlatformFunc vasPlatformFunc;
    private final AirLinkTerminalActiveHistoryService airLinkTerminalActiveHistoryService;
    private final AirLinkEstateService airLinkEstateService;
    private final SyncData2BillingGateway syncData2BillingGateway;
    private final AirLinkTerminalTaskService airLinkTerminalTaskService;
    private final ServiceCenterFunc serviceCenterFunc;
    private final AirLinkTerminalActiveGateway airLinkTerminalActiveGateway;
    private final BillingUsageService billingUsageService;

    @Override
    public AirLinkTerminalDetailInfo getAirLinkTerminalDetail(Long id) {
        AirLinkTerminal airLinkTerminal = airLinkTerminalService.get(id);
        if (Objects.isNull(airLinkTerminal)){
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        AirLinkTerminalDetailInfo detailInfo = BeanMapper.map(airLinkTerminal, AirLinkTerminalDetailInfo.class);
        TerminalRegistry terminalRegistry = terminalRegistryService.getBySerialNo(airLinkTerminal.getSerialNo());
        if (Objects.nonNull(terminalRegistry)) {
            List<Long> childResellerIds = resellerService.findChildResellerIds(airLinkTerminal.getResellerId());
            //不在本代理商以及子代理商下的终端信息，不显示
            if (LongUtils.equals(airLinkTerminal.getResellerId(), terminalRegistry.getResellerId()) || childResellerIds.contains(terminalRegistry.getResellerId())) {
                detailInfo.setSerialNo(terminalRegistry.getSerialNo());
                Reseller reseller = resellerService.get(terminalRegistry.getResellerId());
                if (Objects.nonNull(reseller)) {
                    detailInfo.setResellerName(reseller.getName());
                }
                Merchant merchant = merchantService.get(terminalRegistry.getMerchantId());
                if (Objects.nonNull(merchant)) {
                    detailInfo.setMerchantName(merchant.getName());
                }
            }
        }

        MarketAirLinkSubscriptionPlanInfo subscriptionPlanInfo = serviceCenterFunc.getSubscriptionPlanByPeriod(airLinkTerminal.getMarketId(), null);
        if (Objects.nonNull(subscriptionPlanInfo)) {
            detailInfo.setDataLimit(new BigDecimal(subscriptionPlanInfo.getPackageLimit()).multiply(new BigDecimal(1024)));
            BigDecimal currentUsage = billingUsageService.getAirLinkTerminalTotalUsage(id, DateUtils.formatDate(new Date(), "yyyy-MM")).getDataUsage();
            detailInfo.setDataLeft(detailInfo.getDataLimit().subtract(currentUsage));

        }
        QueryCardDetailResponse queryCardDetailResponse = vasPlatformFunc.getCardDetail(airLinkTerminal.getImei()).getData();
        if(Objects.nonNull(queryCardDetailResponse)) {
            detailInfo.setOnlineStatus(Boolean.parseBoolean(queryCardDetailResponse.getOnlineStatus()));
        }


        return detailInfo;
    }

    @Override
    public AirLinkStatisticsInfo getConsumptionStatistics(Long id, String timeRangeType) {
        AirLinkTerminal airLinkTerminal = airLinkTerminalService.get(id);
        if (Objects.isNull(airLinkTerminal)){
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        if (StringUtils.isEmpty(timeRangeType)) {
            timeRangeType = TIME_RANGE_TYPE_30D;
        }

        AirLinkStatisticsInfo airLinkStatisticsInfo = new AirLinkStatisticsInfo();
        AirLinkStatisticsInfo.Column column = new AirLinkStatisticsInfo.Column();
        ArrayList<AirLinkStatisticsInfo.Row> rows = new ArrayList<>();
        QueryCardUsageRequest queryCardUsageRequest = new QueryCardUsageRequest();
        queryCardUsageRequest.setImeiList(List.of(airLinkTerminal.getImei()));
        QueryCardUsageResponse usageResponse;
        switch (timeRangeType.toLowerCase()) {
            case TIME_RANGE_TYPE_7D :
                column.setType("Timestamp");
                column.setFormat("MM-DD");

                queryCardUsageRequest.setMonths(getDaysMonthsForPeriod(7));
                usageResponse = vasPlatformFunc.queryUsage(queryCardUsageRequest).getData();
                if (Objects.nonNull(usageResponse) && CollectionUtils.isNotEmpty(usageResponse.getResults())) {
                    airLinkStatisticsInfo.setRows(List.of(extractUsageRecords(usageResponse.getResults().get(0).getUsage(), 7)));
                }
                break;
            case TIME_RANGE_TYPE_30D:
                column.setType("Timestamp");
                column.setFormat("MM-DD");
                queryCardUsageRequest.setMonths(getDaysMonthsForPeriod(30));
                usageResponse = vasPlatformFunc.queryUsage(queryCardUsageRequest).getData();
                if (Objects.nonNull(usageResponse) && CollectionUtils.isNotEmpty(usageResponse.getResults())) {
                    airLinkStatisticsInfo.setRows(List.of(extractUsageRecords(usageResponse.getResults().get(0).getUsage(), 30)));
                }
                break;
            case TIME_RANGE_TYPE_6M:
                column.setType("Timestamp");
                column.setFormat("YYYY-MM");
                queryCardUsageRequest.setMonths(getMonthsForPeriod(6));
                usageResponse = vasPlatformFunc.queryUsage(queryCardUsageRequest).getData();
                if (Objects.nonNull(usageResponse) && CollectionUtils.isNotEmpty(usageResponse.getResults())) {
                    airLinkStatisticsInfo.setRows(List.of(extractUsageRecordsForMonth(usageResponse.getResults().get(0).getUsage())));
                }
                break;
        }

        airLinkStatisticsInfo.setColumns(List.of(column));
        return airLinkStatisticsInfo;
    }

    // 获取指定天数范围内的月份
    public static List<String> getDaysMonthsForPeriod(int days) {
        LocalDate startDate = LocalDate.now().minusDays(days - 1L);

        List<String> monthsList = new ArrayList<>();

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i);
            String formattedMonth = date.format(DateTimeFormatter.ofPattern("yyyyMM"));
            if (!monthsList.contains(formattedMonth)) {
                monthsList.add(formattedMonth);
            }
        }

        return monthsList;
    }

    // 获取指定月数范围内的月份
    public static List<String> getMonthsForPeriod(int months) {
        LocalDate startDate = LocalDate.now().minusMonths(months - 1L);

        List<String> monthsList = new ArrayList<>();

        for (int i = 0; i < months; i++) {
            LocalDate date = startDate.plusMonths(i);
            String formattedMonth = date.format(DateTimeFormatter.ofPattern("yyyyMM"));
            if (!monthsList.contains(formattedMonth)) {
                monthsList.add(formattedMonth);
            }
        }

        return monthsList;
    }

    public static List<AirLinkStatisticsInfo.Row> extractUsageRecords(List<QueryCardUsageResponse.UsageDetail> usageDataList, int days) {
        List<AirLinkStatisticsInfo.Row> result = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

        // 获取当前日期
        LocalDate today = LocalDate.now();

        for (QueryCardUsageResponse.UsageDetail data : usageDataList) {
            String monthStr = data.getMonth();
            YearMonth yearMonth = YearMonth.parse(monthStr, formatter);
            int[] dailyUsages = data.getDailyUsages();

            // 获取该月的天数
            int daysInMonth = yearMonth.lengthOfMonth();

            // 遍历daily_usages数组，从索引1开始
            for (int i = 1; i <= daysInMonth; i++) {
                // 生成具体日期
                LocalDate date = yearMonth.atDay(i);

                // 检查日期是否在最近days天内
                if (!date.isAfter(today) && !date.isBefore(today.minusDays(days - 1L))) {
                    int usage = dailyUsages[i];
                    long timestamp = date.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
                    AirLinkStatisticsInfo.Row row = new AirLinkStatisticsInfo.Row();
                    row.setColName(String.valueOf(timestamp));
                    row.setValue(String.valueOf(usage));
                    result.add(row);
                }
            }
        }

        // 按时间排序，确保顺序是从最远的一天到当天
        result.sort(Comparator.comparingLong(a -> Long.parseLong(a.getColName())));

        return result;
    }

    public static List<AirLinkStatisticsInfo.Row> extractUsageRecordsForMonth(List<QueryCardUsageResponse.UsageDetail> usageDataList) {
        List<AirLinkStatisticsInfo.Row> result = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

        for (QueryCardUsageResponse.UsageDetail data : usageDataList) {
            String monthStr = data.getMonth();
            YearMonth yearMonth = YearMonth.parse(monthStr, formatter);
            LocalDate firstDayOfMonth = yearMonth.atDay(1);
            long timestamp = firstDayOfMonth.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
            double usage = "-1.0".equals(data.getDataUsage()) ? 0.0 : Double.parseDouble(data.getDataUsage());
            AirLinkStatisticsInfo.Row row = new AirLinkStatisticsInfo.Row();
            row.setColName(String.valueOf(timestamp));
            row.setValue(String.valueOf(usage));
            result.add(row);
        }

        // 按时间排序，确保顺序是从最远的一天到当天
        result.sort(Comparator.comparingLong(a -> Long.parseLong(a.getColName())));

        return result;
    }

    @Override
    public List<AirLinkTerminalProfileInfo> getAirLinkTerminalProfileList(Long id) {
        List<AirLinkTerminalProfile> profileList = airLinkTerminalProfileService.getProfileList(id);
        if (CollectionUtils.isEmpty(profileList)) {
            return Collections.emptyList();
        }
        AirLinkTerminal airLinkTerminal = airLinkTerminalService.get(id);
        QueryCardDetailResponse queryCardDetailResponse = vasPlatformFunc.getCardDetail( airLinkTerminal.getImei()).getData();
        List<AirLinkTerminalProfileInfo> profileInfos = new ArrayList<>(profileList.size());
        for (AirLinkTerminalProfile airLinkTerminalProfile : profileList) {
            AirLinkTerminalProfileInfo profileInfo = BeanMapper.map(airLinkTerminalProfile, AirLinkTerminalProfileInfo.class);
            AirLinkTerminalProfileInfo.NetworkInfo networkInfo = new AirLinkTerminalProfileInfo.NetworkInfo();
            if (Objects.nonNull(queryCardDetailResponse)
                    && StringUtils.equals(airLinkTerminalProfile.getIccid(),queryCardDetailResponse.getCurrentProfile().getIccid())
                    && (Objects.nonNull(queryCardDetailResponse.getNetworkInfo()))
            ) {
                    networkInfo.setDbm(queryCardDetailResponse.getNetworkInfo().getDbm());
                    networkInfo.setSignalLevel(queryCardDetailResponse.getNetworkInfo().getSignalLevel());
                    networkInfo.setType(queryCardDetailResponse.getNetworkInfo().getType());
                    profileInfo.setNetworkInfo(networkInfo);
            }
            profileInfos.add(profileInfo);

        }
        return profileInfos;
    }

    @Override
    public void switchAirLinkTerminalProfile(Long id, String iccid) {
        AirLinkTerminal airLinkTerminal = airLinkTerminalService.get(id);
        if (Objects.isNull(airLinkTerminal)){
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        //只允许终端在用状态才能切换码号
        if (!airLinkTerminal.getStatus().equals(AirLinkTerminalStatus.USE)) {
            throw new BusinessException(ApiCodes.AIRLINK_TERMINAL_STATUS_NOT_USED);
        }
        AirLinkTerminalProfile airLinkTerminalProfile = airLinkTerminalProfileService.getProfileByIccid(iccid);
        if (Objects.isNull(airLinkTerminalProfile)) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        SwitchCardsRequest switchCardsRequest = new SwitchCardsRequest();
        switchCardsRequest.setImeiList(List.of(airLinkTerminal.getImei()));
        switchCardsRequest.setOperator(airLinkTerminalProfile.getOperator());
        vasPlatformFunc.switchCards(switchCardsRequest);

        //更改profile状态
        airLinkTerminalProfileService.switchAirLinkTerminalProfile(id,airLinkTerminalProfile);
    }

    @Override
    public PageInfo<AirLinkDeletedTerminalInfo> getDeletedAirLinkTerminalList(AirLinkDeletedTerminalRequest request) {
        PageInfo<AirLinkDeletedTerminalInfo> pageInfo = airLinkTerminalService.getDeletedAirLinkTerminalList(parsePage(), request);
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            pageInfo.getList().forEach(airLinkDeletedTerminalInfo -> {
                if (StringUtils.isNotEmpty(airLinkDeletedTerminalInfo.getSerialNo())) {
                    TerminalRegistry terminalRegistry = terminalRegistryService.getBySerialNo(airLinkDeletedTerminalInfo.getSerialNo());
                    if (Objects.nonNull(terminalRegistry)) {
                        List<Long> childResellerIds = resellerService.findChildResellerIds(airLinkDeletedTerminalInfo.getResellerId());
                        //不在本代理商以及子代理商下的终端信息，不显示
                        if (LongUtils.equals(airLinkDeletedTerminalInfo.getResellerId(), terminalRegistry.getResellerId()) || childResellerIds.contains(terminalRegistry.getResellerId())) {
                            Reseller reseller = resellerService.get(terminalRegistry.getResellerId());
                            if (Objects.nonNull(reseller)) {
                                airLinkDeletedTerminalInfo.setResellerName(reseller.getName());
                            }
                            Merchant merchant = merchantService.get(terminalRegistry.getMerchantId());
                            if (Objects.nonNull(merchant)) {
                                airLinkDeletedTerminalInfo.setMerchantName(merchant.getName());
                            }
                        }
                    }

                }

            });
        }

        return pageInfo;
    }

    @Override
    public List<String> getAirLinkTerminalOperatorList() {
        return airLinkTerminalService.getAirLinkTerminalOperatorList(getCurrentResellerId());
    }

    @Override
    public Long createAirLinkTerminalImportTemplateDownloadTask(InputStream inputStream) {
        try {
            Workbook wb = new XSSFWorkbook(inputStream);
            String prefix = getLocaleMessageForExport("title.airlink.terminal.import.template");
            String fileName = prefix + ".xlsx";
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            String fileId = FileUploader.uploadFile(bos.toByteArray(), prefix, "xlsx", SupportedFileTypes.TEMP);
            DownloadTask downloadTask = downloadTaskService.createDownloadTask(fileName, fileId, DownloadTaskStatus.PENDING, true, DownloadTaskType.AIRLINK_TERMINAL_IMPORT_TEMPLATE);
            return downloadTask.getId();
        } catch (Exception ex) {
            logger.error("Error create airlink terminal import template", ex);
            throw new BusinessException(ApiCodes.FILE_DOWNLOAD_FAILED);
        }
    }

    @Override
    public ImportExportActivityInfo importAirLinkTerminal(MultipartFile file, String tz) throws IOException {
        if (file == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        checkAuth(getCurrentMarketId(), getCurrentResellerId());
        Activity activity = airLinkTerminalImportService.importData(file.getOriginalFilename(), file.getBytes(), tz);
        return buildInfo(activity);
    }

    @Override
    public void addAirLinkTerminal(String imei) {
        Long marketId = getCurrentMarketId();
        Long resellerId =  getCurrentResellerId();
        MarketAirLinkSetting airLinkSetting = checkAuth(marketId, resellerId);
        MarketAirLinkSubscriptionPlanInfo currentSubscriptionPlan = serviceCenterFunc.getSubscriptionPlanByPeriod(marketId, null);
        BigDecimal terminalFee = currentSubscriptionPlan.getActivationFee().add(currentSubscriptionPlan.getPackageFee());
        BigDecimal occupationFee = airLinkTerminalSupport.getOccupationFee(airLinkSetting, terminalFee, 1);
        AirLinkDataPoolSettingInfo dataPoolSetting = serviceCenterFunc.getAirLinkDataPoolSetting(airLinkSetting.getDataPoolId());
        if (dataPoolSetting == null || dataPoolSetting.getGroupId() == null){
            throw new BusinessException(ApiCodes.AIRLINK_DATA_POOL_GROUP_ID_BLANK);
        }
        if (airLinkTerminalSupport.imeiIsIncorrent(imei)) {
            throw new BusinessException(ApiCodes.AIRLINK_IMEI_NUMBERS_INVALID);
        }
        if (!airLinkEstateService.exists(marketId, imei)){
            throw new BusinessException(ApiCodes.AIRLINK_NO_PERMISSION_ACTIVE_IMEI);
        }
        if (airLinkTerminalService.existImei(imei)){
            throw new BusinessException(ApiCodes.AIRLINK_IMEI_ALREADY_EXIST_PARAM);
        }
        if (airLinkTerminalTaskService.existByImei(imei, AirLinkTerminalTaskType.CANCEL_ACTIVE)){
            throw new BusinessException(ApiCodes.AIRLINK_TERMINAL_DELETEING);
        }
        AirLinkTerminal airLinkTerminal = new AirLinkTerminal();
        airLinkTerminal.setImei(imei);
        airLinkTerminal.setMarketId(marketId);
        airLinkTerminal.setResellerId(resellerId);
        airLinkTerminal.setStatus(AirLinkTerminalStatus.INSERT);
        airLinkTerminal.setCreatedBy(getCurrentUser());
        Date date = new Date();
        airLinkTerminal.setCreatedDate(date);
        airLinkTerminal.setUpdatedBy(getCurrentUser());
        airLinkTerminal.setUpdatedDate(date);
        List<AirLinkEstate> airLinkEstates = airLinkTerminalSupport.getAirLinkEstates(marketId, Collections.singletonList(imei), AirLinkEstateStatus.INUSE);
        Long activeHistoryId = airLinkTerminalSupport.batchCreateAirLinkTerminal(Lists.newArrayList(airLinkTerminal), occupationFee, terminalFee, false, airLinkEstates);
        airLinkTerminalActiveGateway.send(new AirLinkTerminalActiveMessage(dataPoolSetting.getGroupId(), marketId, resellerId, activeHistoryId, Sets.newHashSet(imei)));
    }

    private Map<String, QueryCardDetailResponse> batchGetCardDetail(List<AirLinkTerminal> linkTerminals){
        CardsRequest cardsRequest = new CardsRequest();
        cardsRequest.setImeiList(linkTerminals.stream().map(AirLinkTerminal::getImei).toList());
        Map<String, QueryCardDetailResponse> terminalCardMap = null;
        SingleResponse<BatchQueryCardDetailResponse> batchQueryCardDetailResponseSingleResponse = vasPlatformFunc.batchGetDetail(cardsRequest);
        if (Objects.nonNull(batchQueryCardDetailResponseSingleResponse) && batchQueryCardDetailResponseSingleResponse.getData() != null && CollectionUtils.isNotEmpty(batchQueryCardDetailResponseSingleResponse.getData().getResults())) {
            List<QueryCardDetailResponse> results = batchQueryCardDetailResponseSingleResponse.getData().getResults();
            terminalCardMap = results.stream()
                    .filter(device -> device.getImei() != null)
                    .collect(Collectors.toMap(
                            QueryCardDetailResponse::getImei,
                            device -> device,
                            (existing, replacement) -> existing
                    ));
        }
        return terminalCardMap;
    }

    @Override
    public PageInfo<AirLinkTerminalInfo> getAirLinkTerminalList(Page<AirLinkTerminal> page, AirLinkTerminalRequest request) {
        AirLinkTerminalQuery airLinkTerminalQuery = BeanMapper.map(request, AirLinkTerminalQuery.class);
        Long currentMarketId = getCurrentMarketId();
        Long currentResellerId = getCurrentResellerId();
        airLinkTerminalQuery.setMarketId(currentMarketId);
        airLinkTerminalQuery.setResellerId(currentResellerId);
        Page<AirLinkTerminal> terminalPage = airLinkTerminalService.findAirLinkTerminalList(page, airLinkTerminalQuery);
        List<AirLinkTerminalInfo> resultList = new ArrayList<>();
        if (terminalPage != null && CollectionUtils.isNotEmpty(terminalPage.getList())){
            List<AirLinkTerminal> linkTerminals = terminalPage.getList();
            linkTerminals.forEach(
                    airLinkTerminal -> {
                        AirLinkTerminalInfo airLinkTerminalInfo = convertAirLinkTerminalInfo(airLinkTerminal);
                        resultList.add(airLinkTerminalInfo);
                    }
            );
            Map<String, QueryCardDetailResponse> stringQueryCardDetailResponseMap = batchGetCardDetail(linkTerminals);
            if (Objects.nonNull(stringQueryCardDetailResponseMap)) {
                resultList.forEach(
                        airLinkTerminalInfo -> {
                            QueryCardDetailResponse queryCardDetailResponse = stringQueryCardDetailResponseMap.get(airLinkTerminalInfo.getImei());
                            if (Objects.nonNull(queryCardDetailResponse) && Objects.nonNull(queryCardDetailResponse.getNetworkInfo())){
                                airLinkTerminalInfo.setOnlineStatus(Boolean.parseBoolean(queryCardDetailResponse.getOnlineStatus()));
                                if (Objects.nonNull(airLinkTerminalInfo.getIsUsedAirLinkTerminalProfileInfo())) {
                                    AirLinkTerminalProfileInfo.NetworkInfo networkInfo = new AirLinkTerminalProfileInfo.NetworkInfo();
                                    networkInfo.setDbm(queryCardDetailResponse.getNetworkInfo().getDbm());
                                    networkInfo.setSignalLevel(queryCardDetailResponse.getNetworkInfo().getSignalLevel());
                                    networkInfo.setType(queryCardDetailResponse.getNetworkInfo().getType());
                                    airLinkTerminalInfo.getIsUsedAirLinkTerminalProfileInfo().setNetworkInfo(networkInfo);
                                }
                            }
                        }
                );
            }
        }
        return new PageInfo<>(resultList, page.getCount());
    }

    private AirLinkTerminalInfo convertAirLinkTerminalInfo(AirLinkTerminal airLinkTerminal){
        AirLinkTerminalInfo airLinkTerminalInfo = BeanMapper.map(airLinkTerminal, AirLinkTerminalInfo.class);
        // sn、reseller、merchant
        TerminalRegistry terminalRegistry = terminalRegistryService.getBySerialNo(airLinkTerminal.getSerialNo());
        if (Objects.nonNull(terminalRegistry)) {
            List<Long> childResellerIds = resellerService.findChildResellerIds(airLinkTerminal.getResellerId());
            if (Objects.equals(airLinkTerminal.getResellerId(), terminalRegistry.getResellerId()) || childResellerIds.contains(terminalRegistry.getResellerId())) {
                airLinkTerminalInfo.setSerialNo(terminalRegistry.getSerialNo());
                Reseller reseller = resellerService.get(terminalRegistry.getResellerId());
                if (Objects.nonNull(reseller)) {
                    airLinkTerminalInfo.setResellerName(reseller.getName());
                }
                Merchant merchant = merchantService.get(terminalRegistry.getMerchantId());
                if (Objects.nonNull(merchant)) {
                    airLinkTerminalInfo.setMerchantName(merchant.getName());
                }
            }
        }
        List<AirLinkTerminalProfile> profileList = airLinkTerminalProfileService.getProfileList(airLinkTerminal.getId());
        if (CollectionUtils.isNotEmpty(profileList)){
            for (AirLinkTerminalProfile profile : profileList) {
                if(AirLinkTerminalProfileStatus.USE.equals(profile.getStatus())){
                    AirLinkTerminalProfileInfo profileInfo = BeanMapper.map(profile, AirLinkTerminalProfileInfo.class);
                    airLinkTerminalInfo.setIsUsedAirLinkTerminalProfileInfo(profileInfo);
                    airLinkTerminalInfo.setIccids(profile.getIccid());
                }
            }
        }
        return airLinkTerminalInfo;
    }


    @Override
    public AirLinkDashboardStatisticsInfo getDashboardStatistics() {
        AirLinkDashboardStatisticsInfo airLinkDashboardStatisticsInfo = new AirLinkDashboardStatisticsInfo();
        Long currentResellerId = getCurrentResellerId();
        Date beginOfMonth = DateUtils.beginOfMonth(new Date());
        Long currentMonthUsage = airLinkTerminalService.getCurrentMonthUsage(currentResellerId, beginOfMonth);
        airLinkDashboardStatisticsInfo.setCurrentMonthUsage(currentMonthUsage);
        airLinkDashboardStatisticsInfo.setNewTerminalsToday(airLinkTerminalService.getNewTerminalsTodayCount(currentResellerId));
        airLinkDashboardStatisticsInfo.setTotalTerminalCount(airLinkTerminalService.getTotalTerminalCount(currentResellerId));
        SingleResponse<Integer> integerSingleResponse = vasPlatformFunc.queryOnlineDeviceNumber(currentResellerId);
        airLinkDashboardStatisticsInfo.setOnlineTerminalCount(integerSingleResponse.getData().longValue());
        return airLinkDashboardStatisticsInfo;
    }

    @Override
    public void batchDeleteAirLinkTerminal(AirLinkTerminalBatchRequest request) {
        checkAuth(getCurrentMarketId(), getCurrentResellerId());
        if (CollectionUtils.isNotEmpty(request.getIds())){
            List<AirLinkTerminal> airLinkTerminals = Lists.newArrayListWithExpectedSize(request.getIds().size());
            Long currentResellerId = getCurrentResellerId();
            for (Long id : request.getIds()){
                AirLinkTerminal airLinkTerminal = airLinkTerminalService.getByIdAndResellerId(id, currentResellerId);
                if (Objects.nonNull(airLinkTerminal)) {
                    airLinkTerminals.add(airLinkTerminal);
                }
            }
            if (CollectionUtils.isNotEmpty(airLinkTerminals)){
                List<List<AirLinkTerminal>> partitions = Lists.partition(airLinkTerminals, 1000);
                for (List<AirLinkTerminal> partition : partitions){
                    CardsRequest cardsRequest = new CardsRequest();
                    cardsRequest.setImeiList(partition.stream().map(AirLinkTerminal::getImei).collect(Collectors.toList()));
                    SingleResponse<DeleteCardsResponse> deleteCardsResponseSingleResponse = vasPlatformFunc.deleteCards(cardsRequest);
                    if (Objects.nonNull(deleteCardsResponseSingleResponse) && Objects.nonNull(deleteCardsResponseSingleResponse.getData()) && CollectionUtils.isNotEmpty(deleteCardsResponseSingleResponse.getData().getResults())) {
                        List<String> deletedTerminal = deleteCardsResponseSingleResponse.getData().getResults().stream().map(DeleteCardsResponse.DeleteCardsResultDetails::getImei).toList();
                        List<AirLinkTerminal> toDeleteList = partition.stream().filter(airLinkTerminal -> deletedTerminal.contains(airLinkTerminal.getImei())).toList();
                        batchGetDeletedTerminalTotalUsage(toDeleteList);
                        airLinkTerminalSupport.deleteAirLinkTerminal(getCurrentMarketId(),toDeleteList);
                        //删除的时候去billing更新用量
                        for (AirLinkTerminal airLinkTerminal : toDeleteList) {
                            SyncAirLinkUsageDetailMessage message = SyncAirLinkUsageDetailMessage
                                    .builder()
                                    .airlinkTerminalId(airLinkTerminal.getId())
                                    .marketId(airLinkTerminal.getMarketId())
                                    .dataUsage(airLinkTerminal.getMonthUsage())
                                    .isDelete(true)
                                    .build();
                            syncData2BillingGateway.sendAirLinkTerminalUsage(JsonMapper.toJsonString(message));
                        }
                    }
                }
            }
        }
    }

    private void batchGetDeletedTerminalTotalUsage(List<AirLinkTerminal> airLinkTerminals) {
        Map<String, List<QueryCardUsageResponse.UsageDetail>> stringListMap = batchGetUsage(airLinkTerminals);
        for (AirLinkTerminal airLinkTerminal : airLinkTerminals) {
            List<QueryCardUsageResponse.UsageDetail> usageDetailList = stringListMap.get(airLinkTerminal.getEid());
            BigDecimal currentBalance = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(usageDetailList)) {
                QueryCardUsageResponse.UsageDetail currentMonthUsage = usageDetailList.get(0);
                if (Objects.nonNull(currentMonthUsage) && !"-1.0".equals(currentMonthUsage.getDataUsage())) {
                    currentBalance = new BigDecimal(currentMonthUsage.getDataUsage());
                }

            }

            BigDecimal dataUsage = getAirLinkTerminalOtherImportUsage(airLinkTerminal,DateUtils.formatDate(new Date(), "yyyy-MM"));
            airLinkTerminal.setMonthUsage(currentBalance.subtract(dataUsage));
            airLinkTerminal.setTotalUsage(airLinkTerminal.getTotalUsage() == null
                    ? BigDecimal.ZERO.add(airLinkTerminal.getMonthUsage())
                    :
                    airLinkTerminal.getTotalUsage().add(airLinkTerminal.getMonthUsage()));
        }

    }

    public Map<String, List<QueryCardUsageResponse.UsageDetail>> batchGetUsage(List<AirLinkTerminal> airLinkTerminals) {
        QueryCardUsageRequest queryCardUsageRequest = new QueryCardUsageRequest();
        queryCardUsageRequest.setMonths(List.of(
                DateUtils.formatDate(new Date(), "yyyyMM")
        ));
        queryCardUsageRequest.setImeiList(airLinkTerminals.stream().map(AirLinkTerminal::getImei).toList());
        QueryCardUsageResponse usageResponse = vasPlatformFunc.queryUsage(queryCardUsageRequest).getData();
        Map<String, List<QueryCardUsageResponse.UsageDetail>> map = new HashMap<>();
        if (Objects.nonNull(usageResponse) && CollectionUtils.isNotEmpty(usageResponse.getResults())) {
            for (QueryCardUsageResponse.Usage terminalUsage : usageResponse.getResults()) {
                map.put(terminalUsage.getCardId(), terminalUsage.getUsage());
            }
        }
        return map;
    }

    //获取当月该终端（不包含本次导入）的所有用量
    private BigDecimal getAirLinkTerminalOtherImportUsage(AirLinkTerminal airLinkTerminal,String period) {
        MarketAirLinkSettingInfo marketAirLinkSetting = serviceCenterFunc.getMarketAirLinkSetting(airLinkTerminal.getMarketId());
        if (Objects.isNull(marketAirLinkSetting)) {
            return BigDecimal.ZERO;
        }
        List<Long> marketIdsByPoolId = marketAirLinkSettingService.findMarketIdsByPoolId(marketAirLinkSetting.getDataPoolId());
        String marketIds = String.join(",", Optional.of(marketIdsByPoolId)
                .orElseGet(org.apache.commons.compress.utils.Lists::newArrayList)
                .stream()
                .map(String::valueOf)
                .toList());
        ServiceUsageDetailInfo terminalTotalUsage = billingUsageService.getAirLinkTerminalOtherImportUsage(airLinkTerminal.getImei(),
                airLinkTerminal.getId(),
                marketIds,
                period);
        return terminalTotalUsage.getDataUsage();
    }


    @Override
    public void batchDisableAirLinkTerminal(AirLinkTerminalBatchRequest request) {
        checkAuth(getCurrentMarketId(), getCurrentResellerId());
        if (CollectionUtils.isNotEmpty(request.getIds())){
            List<AirLinkTerminal> airLinkTerminals = Lists.newArrayListWithExpectedSize(request.getIds().size());
            Long currentResellerId = getCurrentResellerId();
            for (Long id : request.getIds()) {
                // 使用状态的终端才能被停用
                AirLinkTerminal airLinkTerminal = airLinkTerminalService.getByIdAndResellerIdAndStatus(id, currentResellerId, AirLinkTerminalStatus.USE);
                if (Objects.nonNull(airLinkTerminal)) {
                    airLinkTerminals.add(airLinkTerminal);
                }
            }
            if (CollectionUtils.isNotEmpty(airLinkTerminals)){
                List<List<AirLinkTerminal>> partitions = Lists.partition(airLinkTerminals, 1000);
                for (List<AirLinkTerminal> partition : partitions){
                    CardsRequest cardsRequest = new CardsRequest();
                    cardsRequest.setImeiList(partition.stream().map(AirLinkTerminal::getImei).toList());
                    vasPlatformFunc.suspendCards(cardsRequest);
                    airLinkTerminalService.batchUpdate(partition, AirLinkTerminalStatus.USE, AirLinkTerminalStatus.DISABLING);
                }
            }
        }
    }

    @Override
    public void batchResumeAirLinkTerminal(AirLinkTerminalBatchRequest request) {
        checkAuth(getCurrentMarketId(), getCurrentResellerId());
        if (CollectionUtils.isNotEmpty(request.getIds())){
            List<AirLinkTerminal> airLinkTerminals = Lists.newArrayListWithExpectedSize(request.getIds().size());
            Long currentResellerId = getCurrentResellerId();
            for (Long id : request.getIds()){
                // 停用状态的终端才能被恢复
                AirLinkTerminal airLinkTerminal = airLinkTerminalService.getByIdAndResellerIdAndStatus(id, currentResellerId, AirLinkTerminalStatus.DISABLED);
                if (Objects.nonNull(airLinkTerminal)) {
                    airLinkTerminals.add(airLinkTerminal);
                }
            }
            if (CollectionUtils.isNotEmpty(airLinkTerminals)) {
                List<List<AirLinkTerminal>> partitions = Lists.partition(airLinkTerminals, 1000);
                for (List<AirLinkTerminal> partition : partitions) {
                    CardsRequest cardsRequest = new CardsRequest();
                    cardsRequest.setImeiList(partition.stream().map(AirLinkTerminal::getImei).collect(Collectors.toList()));
                    vasPlatformFunc.activateCards(cardsRequest);
                    airLinkTerminalService.batchUpdate(partition, AirLinkTerminalStatus.DISABLED, AirLinkTerminalStatus.RESUMING);
                }
            }
        }
    }

    @Override
    public PageInfo<AirLinkTerminalActiveHistoryInfo> getAirLinkTerminalActiveHistoryList(Page<AirLinkTerminalActiveHistory> page) {
        AirLinkTerminalActiveHistoryQuery airLinkTerminalActiveHistoryQuery = new AirLinkTerminalActiveHistoryQuery();
        airLinkTerminalActiveHistoryQuery.setMarketId(getCurrentMarketId());
        airLinkTerminalActiveHistoryQuery.setResellerId(getCurrentResellerId());
        Page<AirLinkTerminalActiveHistory> airLinkTerminalActiveHistoryList = airLinkTerminalService.findAirLinkTerminalActiveHistoryList(page, airLinkTerminalActiveHistoryQuery);
        List<AirLinkTerminalActiveHistoryInfo> resultList = new ArrayList<>();
        if (airLinkTerminalActiveHistoryList != null && CollectionUtils.isNotEmpty(airLinkTerminalActiveHistoryList.getList())){
            resultList = BeanMapper.mapList(airLinkTerminalActiveHistoryList.getList(), AirLinkTerminalActiveHistoryInfo.class);
        }
        return new PageInfo<>(resultList);
    }

    @Override
    public ImportExportActivityInfo exportTerminalActivateDetail(Long id, String tz) {
        AirLinkTerminalActiveHistory airLinkTerminalActiveHistoryById = airLinkTerminalActiveHistoryService.getAirLinkTerminalActiveHistoryById(id, getCurrentResellerId());
        if (Objects.isNull(airLinkTerminalActiveHistoryById)){
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        int terminalListCountByHistoryIdForExport = airLinkTerminalService.getTerminalListCountByHistoryIdForExport(id, SystemPropertyHelper.getAirLinkTerminalActivateExportLimit());
        if (terminalListCountByHistoryIdForExport <= 0){
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        AirLinkTerminalActivateExportRequest airLinkTerminalActivateExportRequest = new AirLinkTerminalActivateExportRequest();
        airLinkTerminalActivateExportRequest.setTaskName(airLinkTerminalActiveHistoryById.getName());
        airLinkTerminalActivateExportRequest.setActiveHistoryId(id);
        airLinkTerminalActivateExportRequest.setTotalCount(terminalListCountByHistoryIdForExport);
        airLinkTerminalActivateExportRequest.setTimeZone(tz);
        Activity activity =  airLinkTerminalActivateExportService.exportData(airLinkTerminalActivateExportRequest,tz);
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }

    private MarketAirLinkSetting checkAuth(Long marketId, Long resellerId){
        if (!market2ServiceReadonlyService.isServiceEnabledActiveAndSubscribed(marketId, VasConstants.ServiceType.AIR_LINK)){
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        if (!serviceResellerService.checkServiceReseller(VasConstants.ServiceType.AIR_LINK, marketId, resellerId)){
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        MarketAirLinkSetting airLinkSetting = marketAirLinkSettingService.getByMarketId(marketId);
        if (airLinkSetting.getOverdueDate() != null){
            throw new BusinessException(ApiCodes.AIRLINK_OVER_DUE);
        }
        return airLinkSetting;
    }

    private ImportExportActivityInfo buildInfo(Activity activity) {
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }


}
