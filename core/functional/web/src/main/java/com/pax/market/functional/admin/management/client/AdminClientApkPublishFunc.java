package com.pax.market.functional.admin.management.client;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.dto.request.client.ClientApkFirmwareCreateRequest;
import com.pax.market.dto.request.client.ClientApkPublishAmountUpdateRequest;
import com.pax.market.dto.request.client.ClientApkPublishRangeUpdateRequest;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.vo.admin.clientapp.ClientApkFirmwareVo;
import com.pax.market.vo.admin.clientapp.ClientApkMarketSummaryVo;
import com.pax.market.vo.admin.clientapp.ClientApkMarketVo;
import com.pax.market.vo.admin.common.specific.SpecificSimpleResellerVo;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AdminClientApkPublishFunc {

    PageInfo<ClientApkMarketVo> findClientMarketPage(Long clientApkId, String name, Boolean limited, Boolean specified);

    ClientApkMarketSummaryVo getClientMarketSummary(Long clientApkId, Long marketId);

    void updateClientApkPublishRange(Long clientApkId, ClientApkPublishRangeUpdateRequest clientApkUpdateRequest);

    void addGlobalApkPublish(Long clientApkId, Long marketId);

    void removeGlobalApkPublish(Long clientApkId, Long marketId);

    void saveClientPublishAmount(Long clientApkId, ClientApkPublishAmountUpdateRequest publishAmountRequest);

    PageInfo<ClientApkFirmwareVo> findClientApkFirmwarePage(Long clientApkId);

    void createClientApkFirmware(Long clientApkId, ClientApkFirmwareCreateRequest createRequest);

    void updateClientApkFirmware(Long clientApkId, Long clientApkFirmwareId, ClientApkFirmwareCreateRequest createRequest);

    void removeClientApkFirmware(Long clientApkId, Long clientApkFirmwareId);

    PageInfo<ResellerInfo> findSpecificClientApkResellerPage(Page<Reseller> page, Long clientApkId);

    PageInfo<SpecificSimpleResellerVo> findSpecificClientApkResellerAllList(Long clientApkId);

    void specificClientApkReseller(Long clientApkId, Set<Long> referenceIds);

    void deleteSpecificClientApkReseller(Long clientApkId, Long referenceId);

}
