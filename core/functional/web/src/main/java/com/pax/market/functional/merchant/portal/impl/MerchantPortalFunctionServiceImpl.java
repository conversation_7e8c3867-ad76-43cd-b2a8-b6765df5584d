package com.pax.market.functional.merchant.portal.impl;

import com.google.common.collect.Lists;
import com.pax.market.constants.DashboardConstants;
import com.pax.market.constants.TerminalOnlineStatus;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.app.PurchasedApp;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.report.ReportForWidget;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.domain.query.TerminalOfflineWidgetQuery;
import com.pax.market.domain.util.WidgetCacheUtils;
import com.pax.market.dto.ListResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.merchant.portal.MerchantPortalFunctionService;
import com.pax.market.functional.model.ModelFunc;
import com.pax.market.functional.support.DashboardStatsSupport;
import com.pax.market.vo.merchant.PurchasedAppVo;
import com.pax.market.vo.merchant.TerminalWidgetSummaryVo;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.service.app.PurchasedAppService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.time.DateUtils.addDays;

@FunctionalService
public class MerchantPortalFunctionServiceImpl extends AbstractFunctionalService implements MerchantPortalFunctionService {

    @Autowired
    private MarketTerminalService marketTerminalService;
    @Autowired
    private ModelFunc modelFunc;
    @Autowired
    private AppService appService;
    @Autowired
    private ApkService apkService;
    @Autowired
    private ResellerService resellerService;
    @Autowired
    private DashboardStatsSupport dashboardStatsSupport;
    @Autowired
    private PurchasedAppService purchasedAppService;

    @Override
    public TerminalWidgetSummaryVo summarizeTerminalQty() {
        TerminalWidgetSummaryVo summary = new TerminalWidgetSummaryVo();
        Terminal terminal = new Terminal();
        terminal.setMerchant(new Merchant(getCurrentMerchantId()));
        terminal.setReseller(resellerService.getWithoutDataScopeCheck(getCurrentMerchant().getReseller().getId()));
        summary.setNumOfTotal(marketTerminalService.getCount(terminal));
        terminal.setOnlineStatuses(Lists.newArrayList(TerminalOnlineStatus.ONLINE));
        summary.setNumOfOnline(marketTerminalService.getCount(terminal));


        summary.setNumOfAppPurchased(purchasedAppService.getMerchantPurchaseAppCount(getCurrentMerchantId()));
        summary.setNumOfOffline(marketTerminalService
                .loadOfflineTerminalWidget(
                        new TerminalOfflineWidgetQuery(
                                null,
                                null,
                                addDays(new Date(), -7),
                                getCurrentMarketId()),
                        null,
                        getCurrentMerchantId())
                .getYKey());
        return summary;
    }

    @Override
    public PageInfo<PurchasedAppVo> findPurchaseAppList(Page<PurchasedApp> page) {
        PurchasedApp appQuery = new PurchasedApp(getCurrentMerchantId());
        appQuery.setPage(page);
        List<PurchasedApp> purchasedAppList = purchasedAppService.findPurchaseAppList(appQuery);
        if (CollectionUtils.isEmpty(purchasedAppList)) return new PageInfo<>();

        List<PurchasedAppVo> purchasedAppVos = Lists.newArrayList();
        purchasedAppList.forEach(purchasedApp -> {
            Terminal terminal = marketTerminalService.getIncludeDeleted(purchasedApp.getTerminalId());
            if (terminal != null) {
                purchasedApp.setTerminalSN(terminal.getSerialNo());
            }
            AppApkQuery appApkQuery = new AppApkQuery();
            appApkQuery.setAppId(purchasedApp.getAppId());
            appApkQuery.setFilterAppReseller(true);
            appApkQuery.setFilterApkPublish(true);
            App onlineApp = appService.getOnlineApp(appApkQuery);
            if (onlineApp == null) {
                onlineApp = appService.getWithDeleted(purchasedApp.getAppId());
                apkService.loadLatestApk(onlineApp);
            }
            if (onlineApp != null) {
                apkService.loadApkDetail(onlineApp.getLatestOnlineApk());
                apkService.generateApkIconScreenshotLink(onlineApp.getLatestOnlineApk());

                purchasedAppVos.add(PurchasedAppVo.builder()
                        .appName(onlineApp.getLatestOnlineApk().getAppName())
                        .apkIconFileId(onlineApp.getLatestOnlineApk().getApkIconFileId())
                        .purchaseDate(purchasedApp.getPurchaseDate())
                        .chargeType(onlineApp.getChargeType())
                        .price(onlineApp.getPrice())
                        .terminalSN(purchasedApp.getTerminalSN())
                        .currency(purchasedApp.getCurrency())
                        .build());
            }
        });

        return new PageInfo<>(purchasedAppVos, page.getCount());
    }

    @Override
    public ListResponse<ReportForWidget> loadOfflineTerminalWidget(Long marketId, Long merchantId) {
        ListResponse<ReportForWidget> response = WidgetCacheUtils.getWidgetFromCache(DashboardConstants.WIDGET_TERMINAL_OFFLINE, -1L, String.valueOf(getCurrentMerchantId()), false);
        if (response == null) {
            List<TerminalOfflineWidgetQuery> queries = dashboardStatsSupport.buildQueryForOfflineWidget(marketId);
            List<ReportForWidget> reportForWidgets = marketTerminalService.loadOfflineTerminalWidget(null, merchantId, queries);
            response = new ListResponse<>(reportForWidgets).setTotal(calculateTotalForPie(reportForWidgets));
            WidgetCacheUtils.putWidgetToCache(DashboardConstants.WIDGET_TERMINAL_OFFLINE, -1L, String.valueOf(getCurrentMerchantId()), response);
        }
        return response;
    }

    @Override
    public ListResponse<ReportForWidget> loadWidgetModelTerminal(Long currentMarketId, Long merchantId) {
        ListResponse<ReportForWidget> response = WidgetCacheUtils.getWidgetFromCache(DashboardConstants.WIDGET_MODEL_TERMINAL, -1L, String.valueOf(getCurrentMerchantId()), false);
        if (response == null) {
            List<ReportForWidget> reportForWidgets = modelFunc.loadWidgetModelTerminal(currentMarketId, null, merchantId, null);
            response = new ListResponse<>(reportForWidgets).setTotal(calculateTotalForPie(reportForWidgets));
            WidgetCacheUtils.putWidgetToCache(DashboardConstants.WIDGET_MODEL_TERMINAL, -1L, String.valueOf(getCurrentMerchantId()), response);
        }
        return response;
    }

    private int calculateTotalForPie(List<ReportForWidget> reportForWidget) {
        int total = 0;
        for (ReportForWidget forWidget : reportForWidget) {
            total += forWidget.getYKey();
        }
        return total;
    }
}
