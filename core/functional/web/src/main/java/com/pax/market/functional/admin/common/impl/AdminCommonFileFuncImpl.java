package com.pax.market.functional.admin.common.impl;

import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.FileUploadStatus;
import com.pax.market.domain.constants.FileTypes;
import com.pax.market.domain.entity.global.file.MultipartFileUpload;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.file.FileUploadVo;
import com.pax.market.dto.file.MultiPartFileUploadVo;
import com.pax.market.dto.request.file.MultipartFileUploadRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.InternalServerErrorException;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.FileUtils;
import com.pax.market.framework.common.utils.RandomUtil;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.admin.common.AdminCommonFileFunc;
import com.pax.market.functional.validation.validators.file.MultipartFileUploadRequestValidator;
import com.paxstore.domain.support.file.ChunkFileUploadService;
import com.paxstore.global.domain.service.file.MultipartFileUploadService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.File;

@FunctionalService
@RequiredArgsConstructor
public class AdminCommonFileFuncImpl extends AbstractFunctionalService implements AdminCommonFileFunc {

    private final MultipartFileUploadService multiPartFileUploadService;
    private final ChunkFileUploadService chunkFileUploadService;

    @Override
    public FileUploadVo upload(HttpServletRequest request) {
        MultipartFileUploadRequest fileRequest = getParameterValue(request, "fileRequest", MultipartFileUploadRequest.class);
        MultipartFile file = null;
        if (request instanceof MultipartHttpServletRequest multipartHttpServletRequest) {
            file = multipartHttpServletRequest.getFile("file");
        }
        if (fileRequest == null || file == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        if (file.getSize() == 0) {
            throw new BusinessException(ApiCodes.FIRMWARE_SIZE_NOT_EMPTY);
        }
        addValidator(new MultipartFileUploadRequestValidator(fileRequest)).validate();
        String cacheKey = String.format("distributionLock:uploadPartFile:%d_%d_%s", getCurrentMarketId(), getCurrentResellerId(), fileRequest.getMd5());
        if (RedisUtils.tryLock(cacheKey)) {
            try {
                MultipartFileUpload multiPartFileUpload = multiPartFileUploadService.getUnusedFile(getCurrentResellerId(), fileRequest.getType(), fileRequest.getMd5());
                if (multiPartFileUpload != null) {
                    if (!multiPartFileUpload.getStatus().equals(FileUploadStatus.IN_PROGRESS)) {
                        throw new BusinessException(ApiCodes.FILE_UPLOAD_COMPLETED);
                    }
                    if (file.getSize() > multiPartFileUpload.getChunkSize()) {
                        throw new BusinessException(ApiCodes.FILE_CURRENT_CHUNK_OVER_TOTAL_CHUNK);
                    }
                } else {
                    if (multiPartFileUploadService.getPendingFileCount(getCurrentResellerId(), fileRequest.getType()) >= SystemPropertyHelper.getChunkFilePendingTotalLimit()) {
                        throw new BusinessException(ApiCodes.FILE_CHUNK_PENDING_TOTAL_LIMIT);
                    }
                    multiPartFileUpload = new MultipartFileUpload();
                    multiPartFileUpload.setMarketId(getCurrentMarketId());
                    multiPartFileUpload.setReferenceId(getCurrentResellerId());
                    multiPartFileUpload.setType(fileRequest.getType());
                    multiPartFileUpload.setFileName(fileRequest.getFileName());
                    multiPartFileUpload.setMd5(fileRequest.getMd5());
                    multiPartFileUpload.setExistPart(false);
                    multiPartFileUpload.setStatus(FileUploadStatus.IN_PROGRESS);
                    multiPartFileUpload.setTotalChunks(fileRequest.getTotalChunks());
                    multiPartFileUpload.setChunkSize(SystemPropertyHelper.getChunkFileSize());
                    multiPartFileUpload.setContentType(file.getContentType());
                }
                File tmpFile = null;
                multiPartFileUpload.setCurrentChunk(fileRequest.getCurrentChunk());
                try {
                    tmpFile = File.createTempFile(RandomUtil.generateMixString(32), "." + FilenameUtils.getExtension(fileRequest.getFileName()));
                    FileUtils.copyInputStreamToFile(file.getInputStream(), tmpFile);
                    chunkFileUploadService.upload(tmpFile, FileTypes.getSupportedFileTypes(fileRequest.getType()), multiPartFileUpload);
                } catch (Exception e) {
                    logger.error("上传文件失败", e);
                    throw new InternalServerErrorException(ApiCodes.UNKNOWN, "上传文件失败");
                } finally {
                    FileUtils.deleteFile(tmpFile);
                }
                if (multiPartFileUpload.getCurrentChunk().equals(multiPartFileUpload.getTotalChunks())) {
                    return FileUploadVo.of(multiPartFileUpload.getId());
                }
            } finally {
                RedisUtils.unlock(cacheKey);
            }
        } else {
            throw new BusinessException(ApiCodes.FILE_IS_UPLOADING);
        }
        return new FileUploadVo();
    }

    @Override
    public MultiPartFileUploadVo checkFile(String md5, String type) {
        SupportedFileTypes supportedFileTypes = FileTypes.getSupportedFileTypes(type);
        if (supportedFileTypes == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        MultiPartFileUploadVo fileUploadVo = BeanMapper.map(multiPartFileUploadService.getUnusedFile(getCurrentResellerId(), type, md5), MultiPartFileUploadVo.class);
        if (fileUploadVo == null) {
            fileUploadVo = new MultiPartFileUploadVo();
            fileUploadVo.setChunkSize(SystemPropertyHelper.getChunkFileSize());
        }
        return fileUploadVo;
    }
}
