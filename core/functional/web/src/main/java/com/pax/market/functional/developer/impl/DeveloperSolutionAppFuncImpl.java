/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.developer.impl;

import com.google.common.collect.Lists;
import com.pax.core.exception.BusinessException;
import com.pax.market.VasAppChargeMode;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.searchcriteria.UserSearchCriteria;
import com.pax.market.dto.DeveloperInfo;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.billing.SolutionAppDetailInfo;
import com.pax.market.dto.vas.VasAppUsageInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.developer.solution.DeveloperSolutionAppFunc;
import com.pax.market.functional.developer.validator.DeveloperAppTypeValidator;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.app.AppDeveloperValidator;
import com.pax.market.functional.vas.App2ServiceFunctionService;
import com.pax.market.notification.DeveloperNotificationService;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.market.vo.developer.app.SolutionUsageSummaryPageVo;
import com.pax.market.vo.developer.app.SolutionUsageSummaryVo;
import com.pax.market.vo.developer.sandbox.SolutionSubscribeVo;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.service.app.AppSettingService;
import com.paxstore.global.domain.service.app.SolutionChargeService;
import com.paxstore.global.domain.service.developer.DeveloperService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.paxstore.global.domain.service.user.UserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/3/2
 */
@FunctionalService
@RequiredArgsConstructor
public class DeveloperSolutionAppFuncImpl extends AbstractFunctionalService implements DeveloperSolutionAppFunc {

    private final DeveloperService developerService;
    private final DeveloperNotificationService developerNotificationService;
    private final RoleService roleService;
    private final UserService userService;
    private final LicenseService licenseService;
    private final AppSettingService appSettingService;
    private final AppService appService;
    private final App2ServiceFunctionService app2ServiceFunctionService;
    private final SolutionChargeService solutionChargeService;
    private final BillingUsageService billingUsageService;
    private final MarketService marketService;
    private final ServiceCenterFunc serviceCenterFunc;

    @Override
    public void applyIndustrySolution() {
        if (!licenseService.getLicenseInfo().isAllowIndustrySolution()) {
            throw new BusinessException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }

        //1.check market developer
        //2.check Industry Solution status
        if (!SystemConstants.SUPER_MARKET_ID.equals(getCurrentMarketId())) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        if (developerService.isAllowIndustrySolution(getCurrentUser().getCurrentDeveloperId())) {
            throw new BusinessException(ApiCodes.DEVELOPER_INDUSTRY_SOLUTION_HAS_ALLOWED);
        }

        DeveloperInfo currentDeveloper = getCurrentUser().getCurrentDeveloper();
        if (BooleanUtils.isFalse(currentDeveloper.getOwner()) && BooleanUtils.isFalse(currentDeveloper.getAdmin())) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        serviceCenterFunc.applyIndustrySolution();
        //3.send email and notify
        String locale = RequestLocaleHolder.getLocale();
        developerNotificationService.sendApplyIndustrySolutionNotification(
                getCurrentUserId(),
                getCurrentMarket(),
                getCurrentUser().getCurrentDeveloper().getEmail(),
                getGlobalMarketAdminReceiverIds(),
                locale
        );
    }


    private List<Long> getGlobalMarketAdminReceiverIds() {
        List<Long> result = new ArrayList<>();
        Set<Long> userIds = roleService.findUserIdByMarketIdAndRoleId(
                SystemConstants.SUPER_MARKET_ID,
                Lists.newArrayList(RoleID.SUPER_ADMIN),
                null);

        if (CollectionUtils.isNotEmpty(userIds)) {
            UserSearchCriteria userSearchCriteria = new UserSearchCriteria();
            userSearchCriteria.setUserIds(new ArrayList<>(userIds));
            userSearchCriteria.setStatus(UserStatus.ACTIVE);
            List<User> users = userService.findUsers(userSearchCriteria);
            if (CollectionUtils.isNotEmpty(users)) {
                result.addAll(users.stream().map(User::getId).toList());
            }
        }
        return result;
    }


    @Override
    public SolutionSubscribeVo isSolutionSandboxSubscribe(Long appId) {
        validateSolutionApp(appId, true);
        return SolutionSubscribeVo.of(appSettingService.isSolutionSandboxSubscribe(appId));
    }

    @Override
    public void updateSolutionSandboxSubscribe(Long appId, boolean isSubscribe) {
        validateSolutionApp(appId, true);
        boolean isSubscribeOld = appSettingService.isSolutionSandboxSubscribe(appId);
        if (isSubscribe == isSubscribeOld) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        appSettingService.updateSolutionSandboxSubscribe(appId, isSubscribe);
    }

    @Override
    public SolutionUsageSummaryPageVo<SolutionUsageSummaryVo> findSolutionAppUsage(Long appId) {
        validateSolutionApp(appId, false);
        Page<VasAppUsageInfo> page = parsePage();
        page.setOrderBy("s.period desc");
        PageInfo<VasAppUsageInfo> pageInfo = app2ServiceFunctionService.findMarketLevUsage(page, appId, null, null);
        List<SolutionUsageSummaryVo> summaryVos = new ArrayList<>();
        SolutionUsageSummaryPageVo<SolutionUsageSummaryVo> pageVo = new SolutionUsageSummaryPageVo<>();
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            Integer chargeMode = solutionChargeService.getChargeModeByAppId(appId);
            pageVo.setChargeMode(chargeMode);
            pageInfo.getList().forEach(e -> {
                SolutionUsageSummaryVo summaryVo = new SolutionUsageSummaryVo();
                summaryVo.setPeriod(e.getPeriod());
                summaryVo.setCount(e.getInstallCount());
                summaryVos.add(summaryVo);
            });
            pageVo.setTotalCount(pageInfo.getTotalCount());
            pageVo.setList(summaryVos);
        }
        return pageVo;
    }

    @Override
    public PageInfo<SolutionAppDetailInfo> findSolutionAppDetail(Long appId, String period, Page page) {
        validateSolutionApp(appId, false);
        PageInfo<SolutionAppDetailInfo> pageInfo = billingUsageService.findSolutionAppDetailList(appId, period, VasAppChargeMode.parse(solutionChargeService.getChargeModeByAppId(appId)).getModeName(), page);
        List<SolutionAppDetailInfo> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(appInfo -> {
                Market market = marketService.get(appInfo.getMarketId());
                if (market != null) {
                    appInfo.setCountry(market.getCountry());
                }
            });
        }
        return pageInfo;
    }


    private void validateSolutionApp(Long appId, boolean isSolutionForbidden) {
        App app = appService.get(appId);
        checkNotNull(app, ApiCodes.APP_NOT_FOUND);
        if (!AppType.SOLUTION.equals(app.getType())) {
            throw new BusinessException(ApiCodes.APP_TYPE_INVALID);
        }
        Validators.validate(new AppDeveloperValidator(app, false),
                new DeveloperAppTypeValidator(app.getType(), isSolutionForbidden));
    }
}