package com.pax.market.functional.admin.platform.market.impl;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.market.domain.entity.global.market.*;
import com.pax.market.domain.entity.global.rki.MarketRkiServerConfig;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.market.setting.Footer;
import com.pax.market.dto.request.market.MarketUpdateAdvanceSettingRequest;
import com.pax.market.dto.request.market.MarketUpdateGeneralSettingRequest;
import com.pax.market.dto.request.market.MarketUpdateMenuSettingRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.admin.platform.market.AdminMarketPermissionsFunc;
import com.pax.market.functional.limit.BizLimitFunc;
import com.pax.market.functional.rki.MarketRkiServerConfigFunctionService;
import com.pax.market.functional.support.UserMarketSupport;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.market.MarketUpdateGeneralRequestValidator;
import com.pax.market.functional.validation.validators.market.MarketUpdateServiceRequestValidator;
import com.pax.market.mq.contract.push.PushProfileTerminalMessage;
import com.pax.market.mq.contract.push.RepairPushTaskMessage;
import com.pax.market.mq.contract.stock.TerminalStockMigrationMessage;
import com.pax.market.mq.producer.gateway.push.PushProfileTerminalGateway;
import com.pax.market.mq.producer.gateway.push.RepairPushTaskGateway;
import com.pax.market.mq.producer.gateway.stock.TerminalStockMigrationGateway;
import com.pax.market.vo.admin.common.IdNameVo;
import com.pax.market.vo.admin.platform.market.MarketAdvancedSettingVo;
import com.pax.market.vo.admin.platform.market.MarketGeneralSettingVo;
import com.pax.market.vo.admin.platform.market.MarketLimitConfigVo;
import com.pax.market.vo.admin.platform.market.MarketMenuSettingVo;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.global.domain.service.market.FooterService;
import com.paxstore.global.domain.service.market.MarketAdvancedSettingService;
import com.paxstore.global.domain.service.market.MarketGeneralSettingService;
import com.paxstore.global.domain.service.market.MarketMenuSettingService;
import com.paxstore.global.domain.service.model.FactoryMarketService;
import com.paxstore.global.domain.service.model.ModelMarketService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.setting.SsoSettingService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.market.domain.service.alarm.AlarmService;
import com.paxstore.market.domain.service.pushtask.PushTaskUtils;
import com.paxstore.market.domain.util.SmartLandingUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@FunctionalService
@AllArgsConstructor
public class AdminMarketPermissionsFuncImpl extends AbstractFunctionalService implements AdminMarketPermissionsFunc {

    private final MarketGeneralSettingService marketGeneralSettingService;
    private final MarketMenuSettingService marketMenuSettingService;
    private final MarketAdvancedSettingService marketAdvancedSettingService;
    private final FooterService footerService;
    private final FactoryMarketService factoryMarketService;
    private final UserService userService;
    private final ModelMarketService modelMarketService;
    private final MarketRkiServerConfigFunctionService marketRkiServerConfigFunctionService;
    private final TerminalStockMigrationGateway terminalStockMigrationGateway;
    private final PushProfileTerminalGateway pushProfileTerminalGateway;
    private final SsoSettingService ssoSettingService;
    private final UserMarketSupport userMarketSupport;
    private final RoleService roleService;
    private final BizLimitFunc bizLimitFunc;
    private final RepairPushTaskGateway repairPushTaskGateway;
    private final AlarmService alarmService;

    /**
     * Init market permissions
     *
     * @param market the market
     */
    @Override
    @Transactional
    public void initMarketPermissions(Market market) {
        MarketGeneralSetting marketGeneralSetting = new MarketGeneralSetting();
        MarketMenuSetting marketMenuSetting = new MarketMenuSetting();
        MarketAdvancedSetting marketAdvancedSetting = new MarketAdvancedSetting();
        marketGeneralSetting.setMarketId(market.getId());
        marketMenuSetting.setMarketId(market.getId());
        marketAdvancedSetting.setMarketId(market.getId());

        loadingMarketSettingForFree(marketGeneralSetting,
                                    marketMenuSetting,
                                    marketAdvancedSetting,
                                    !MarketBillingMode.FREE_MODE.equals(market.getBillingMode()));

        marketGeneralSettingService.save(marketGeneralSetting);
        marketMenuSettingService.save(marketMenuSetting);
        marketAdvancedSettingService.save(marketAdvancedSetting);
    }

    private void loadingMarketSettingForFree(MarketGeneralSetting generalSetting,
                                             MarketMenuSetting menuSetting,
                                             MarketAdvancedSetting advancedSetting,
                                             boolean isNotFree){

        generalSetting.setAllowLogo(isNotFree);
        generalSetting.setAllowAdvertisement(isNotFree);
        generalSetting.setAllowGroupOperation(isNotFree);
        menuSetting.setAllowReportCenter(isNotFree);
        menuSetting.setAllowAlarm(isNotFree);


        advancedSetting.setAllowWebHook(isNotFree);
        advancedSetting.setAllowThirdPartySystem(isNotFree);
        advancedSetting.setAllowTerminalSystemConfig(isNotFree);
        advancedSetting.setAllowIndustrySolution(isNotFree);
        advancedSetting.setAllowMarketPuk(isNotFree);
        advancedSetting.setAllowMpush(isNotFree);
        advancedSetting.setAllowAppFirmwareSpecific(isNotFree);
        advancedSetting.setAllowDynamicGroup(isNotFree);
        advancedSetting.setAllowGeoFence(isNotFree);
        advancedSetting.setAllowGeoLocation(isNotFree);
        advancedSetting.setAllowOperationControl(isNotFree);
        advancedSetting.setAllowAdvanceSetting(isNotFree);
        advancedSetting.setAllowResellerLogin(isNotFree);
        advancedSetting.setAllowSmartAssistant(isNotFree);
        advancedSetting.setAllowGroupPushLimit(isNotFree);
    }

    /**
     * Update market general setting
     *
     * @param market        the market
     * @param updateRequest the update request
     */
    @Override
    @Transactional
    public void updateMarketGeneralSetting(Market market, MarketUpdateGeneralSettingRequest updateRequest) {
        Validators.validate(new MarketUpdateGeneralRequestValidator(market.getId(), updateRequest));
        MarketGeneralSetting marketGeneralSetting = marketGeneralSettingService.getByMarketId(market.getId());
        deleteUserRolesBySsoDomain(market, updateRequest, marketGeneralSetting);
        BeanMapper.copy(updateRequest, marketGeneralSetting);
        marketGeneralSettingService.update(marketGeneralSetting);
        resolveFooter(market, updateRequest);
        if (StringUtils.isNotBlank(updateRequest.getFactoryIds())) {
            List<Long> factoryIds = Collections3.distinct(StringUtils.splitToLongList(StringUtils.trim(updateRequest.getFactoryIds()), SystemConstants.COMMAS));
            List<Factory> factoryList = factoryMarketService.getMarketFactoryList(factoryIds);
            factoryMarketService.updateMarketFactoryList(market, factoryList);
        }
    }

    /**
     * Update market menu setting
     *
     * @param market        the market
     * @param updateRequest the update request
     */
    @Override
    public void updateMarketMenuSetting(Market market, MarketUpdateMenuSettingRequest updateRequest) {
        if (updateRequest == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        boolean isDeleteFirmware = false;
        boolean isDeleteClient = false;
        boolean isTerminalStockMigration = false;
        MarketMenuSetting menuSetting = marketMenuSettingService.getByMarketId(market.getId());
        //When market is allow STORE client/firmware close, delete all data
        if (menuSetting.getAllowStoreClient() && !updateRequest.getAllowStoreClient()) {
            isDeleteClient = true;
        }
        if (menuSetting.getAllowFirmware() && !updateRequest.getAllowFirmware()) {
            isDeleteFirmware = true;
        }
        if (BooleanUtils.isFalse(menuSetting.getAllowTerminalStock())
                && BooleanUtils.isFalse(!updateRequest.getAllowTerminalStock())) {
            isTerminalStockMigration = true;
        }
        BeanMapper.copy(updateRequest, menuSetting);
        marketMenuSettingService.update(menuSetting, isDeleteClient, isDeleteFirmware);
        //just insert terminal table sn not in asset
        if (isTerminalStockMigration)
            terminalStockMigrationGateway.send(new TerminalStockMigrationMessage(market.getId()));
    }

    /**
     * Update market advance setting
     *
     * @param market        the market
     * @param updateRequest the update request
     */
    @Override
    @Transactional
    public void updateMarketAdvanceSetting(Market market, MarketUpdateAdvanceSettingRequest updateRequest) {
        MarketAdvancedSetting advancedSetting = marketAdvancedSettingService.getByMarketId(market.getId());
        boolean isProfileChanged = false;
        Validators.validate(new MarketUpdateServiceRequestValidator(updateRequest));
        if (BooleanUtils.toBoolean(advancedSetting.getAllowIpWhitelist()) != BooleanUtils.toBoolean(updateRequest.getAllowIpWhitelist()) ||
                BooleanUtils.toBoolean(advancedSetting.getAllowPaymentSecurityControl()) != BooleanUtils.toBoolean(updateRequest.getAllowPaymentSecurityControl()) ||
                BooleanUtils.toBoolean(advancedSetting.getAllowUnattendedModelWhitelist()) != BooleanUtils.toBoolean(updateRequest.getAllowUnattendedModelWhitelist()) ||
                BooleanUtils.toBoolean(advancedSetting.getAllowPaymentSecurityControlWhitelist()) != BooleanUtils.toBoolean(updateRequest.getAllowPaymentSecurityControlWhitelist()) ||
                BooleanUtils.toBoolean(advancedSetting.getAllowPrinterWhitelist()) != BooleanUtils.toBoolean(updateRequest.getAllowPrinterWhitelist()) ||
                BooleanUtils.toBoolean(advancedSetting.getAllowStaticIp()) != BooleanUtils.toBoolean(updateRequest.getAllowStaticIp()) ||
                BooleanUtils.toBoolean(advancedSetting.getAllowWifiLanStaticIp()) != BooleanUtils.toBoolean(updateRequest.getAllowWifiLanStaticIp()) ||
                !Objects.equals(advancedSetting.getFirmwareUpdateMinBatteryLevel(), updateRequest.getFirmwareUpdateMinBatteryLevel())) {
            isProfileChanged = true;
        }
        boolean clearPushTaskApproval = false;
        if (BooleanUtils.isTrue(advancedSetting.getAllowPushTaskApproval()) && BooleanUtils.isFalse(updateRequest.getAllowPushTaskApproval())) {
            clearPushTaskApproval = true;
        }
        //激活过的应用市场不允许修改的属性
        if (!StringUtils.equals(market.getStatus(), MarketStatus.REGISTING)) {
            updateRequest.setPaxPuk(advancedSetting.getPaxPuk());
            updateRequest.setPaxPuk4096(advancedSetting.getPaxPuk4096());
            updateRequest.setAllowMultipleApkFiles(advancedSetting.getAllowMultipleApkFiles());
        }
        //设置无人值守机型
        if (!Objects.equals(market.getId(), SystemConstants.SUPER_MARKET_ID)
                && Boolean.FALSE.equals(advancedSetting.getAllowUnattendedModelWhitelist())
                && Boolean.TRUE.equals(updateRequest.getAllowUnattendedModelWhitelist())) {
            modelMarketService.setDefaultUnattendedModels4Market(market.getId());
        }
        Boolean allowMqttDb = advancedSetting.getAllowMqtt();
        if (!allowMqttDb.equals(updateRequest.getAllowMqtt()) && LongUtils.equals(market.getId(), SystemConstants.SUPER_MARKET_ID)) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        if (Objects.equals(market.getId(), SystemConstants.SUPER_MARKET_ID)){
            updateRequest.setAllowAppFirmwareSpecific(true);
        }
        BeanMapper.copy(updateRequest, advancedSetting);
        marketAdvancedSettingService.update(advancedSetting);
        marketRkiServerConfigFunctionService.updateMarketRkiServer(market.getId(), updateRequest.getRkiServerId());
        if (isProfileChanged) {
            pushProfileTerminalGateway.send(PushProfileTerminalMessage.of().marketId(market.getId()));
        }
        if (clearPushTaskApproval) {
            clearPendingApprovalTask(market.getId());
        }
        if (!allowMqttDb.equals(updateRequest.getAllowMqtt())) {
            SmartLandingUtils.sendMarketMqttChanged(market.getId(), updateRequest.getAllowMqtt());
        }

        //清除预警相关mobile推送的配置
        if(!advancedSetting.getAllowMobileApp()){
            PaxDynamicDsThreadLocal.setPreferenceMarketId(market.getId());
            alarmService.clearMobileConfig(market.getId());
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    private void clearPendingApprovalTask(Long marketId) {
        PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
        for (int actionType : TerminalActionType.PENDING_APPROVAL_ACTION_TYPES) {
            Set<Long> pendingApprovalPushTaskIds = PushTaskUtils.getPushTaskService(actionType).findPendingApprovalPushTaskIds(marketId);
            for (Long pendingApprovalPushTaskId : pendingApprovalPushTaskIds) {
                repairPushTaskGateway.send(new RepairPushTaskMessage(marketId, pendingApprovalPushTaskId, actionType));
            }
        }
        PaxDynamicDsThreadLocal.removePreferenceMarketId();
    }

    /**
     * Gets market permissions
     *
     * @param marketId     the market id
     * @param functionType the function type
     * @return the market permissions
     */
    @Override
    public Object getMarketPermissions(Long marketId, Integer functionType) {
        boolean isGlobal = LongUtils.equals(marketId, SystemConstants.SUPER_MARKET_ID);
        MarketUpdateFunctionType type = MarketUpdateFunctionType.from(functionType);
        if (MarketUpdateFunctionType.GENERAL_SETTING == type) {
            return covertMarketGeneralSetting(isGlobal, marketId);
        } else if (MarketUpdateFunctionType.MENU_SETTING == type) {
            return covertMarketMenuSetting(isGlobal, marketId);
        } else if (MarketUpdateFunctionType.ADVANCE_SETTING == type) {
            return covertMarketAdvancedSetting(marketId);
        } else if (MarketUpdateFunctionType.LIMIT_SETTING == type) {
            return convertLimitConfig(marketId);
        }
        throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
    }

    private MarketGeneralSettingVo covertMarketGeneralSetting(boolean isGlobal, Long marketId) {
        MarketGeneralSettingVo setting = BeanMapper.map(marketGeneralSettingService.getByMarketId(marketId), MarketGeneralSettingVo.class);
        if (isGlobal) {
            setting.setAllowDeveloper(Boolean.TRUE);
            setting.setAllowUserAgreement(Boolean.TRUE);
            setting.setAllowDeveloperAgreement(Boolean.TRUE);
        }
        setting.setSubMarketOpenSso(ssoSettingService.getByMarketId(marketId) != null);
        setting.setFactoryInfoList(BeanMapper.mapList(factoryMarketService.findMarketFactoryList(marketId), IdNameVo.class));
        return setting;
    }

    private MarketMenuSettingVo covertMarketMenuSetting(boolean isGlobal, Long marketId) {
        MarketMenuSettingVo setting = BeanMapper.map(marketMenuSettingService.getByMarketId(marketId), MarketMenuSettingVo.class);
        if (isGlobal) {
            setting.setAllowFirmware(Boolean.TRUE);
            setting.setAllowStoreClient(Boolean.TRUE);
        } else {
            setting.setAllowTerminalStock(Boolean.FALSE);
        }
        return setting;
    }

    private MarketAdvancedSettingVo covertMarketAdvancedSetting(Long marketId) {
        MarketAdvancedSettingVo setting = BeanMapper.map(marketAdvancedSettingService.getByMarketId(marketId), MarketAdvancedSettingVo.class);
        MarketRkiServerConfig marketRkiServerConfig = marketRkiServerConfigFunctionService.getMarketRkiServer(marketId);
        if (marketRkiServerConfig != null) {
            setting.setRkiServerId(marketRkiServerConfig.getId());
        }
        return setting;
    }

    private void resolveFooter(Market market, MarketUpdateGeneralSettingRequest updateRequest) {
        Footer query = new Footer();
        query.setMarketId(market.getId());
        List<Footer> footerList = footerService.findList(query);
        if (BooleanUtils.isTrue(updateRequest.getAllowFooterSetting()) && CollectionUtils.isEmpty(footerList)) {
            query.setMarketId(SystemConstants.SUPER_MARKET_ID);
            footerList = footerService.findList(query);
            for (Footer footer : footerList) {
                footer.setMarketId(market.getId());
                footer.setId(null);
                footerService.save(footer);
            }
        }
    }

    private void deleteUserRolesBySsoDomain(Market market, MarketUpdateGeneralSettingRequest updateRequest, MarketGeneralSetting marketGeneralSetting) {
        List<String> ssoDomains = getRemovedSsoDomains(updateRequest, marketGeneralSetting);
        if (CollectionUtils.isEmpty(ssoDomains)) {
            return;
        }
        for (String ssoDomain : ssoDomains) {
            List<User> users = userService.searchByLoginDomain(ssoDomain);
            Set<Long> userIds = users.stream().map(User::getId).filter(id -> !LongUtils.equals(id, market.getAdmin().getId())).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(userIds)) {
                roleService.deleteAllUserRoles(market.getId(), userIds);
                userIds.forEach(userId -> userMarketSupport.deleteAdminUserMarket(userId, market.getId()));
            }
        }
    }

    private List<String> getRemovedSsoDomains(MarketUpdateGeneralSettingRequest marketUpdateGeneralSettingRequest, MarketGeneralSetting marketGeneralSetting) {
        List<String> removedSsoDomains = new ArrayList<>();
        if (BooleanUtils.isTrue(marketGeneralSetting.getAllowSsoSetting()) && marketUpdateGeneralSettingRequest.getAllowSsoSetting()) {
            List<String> ssoDomainsOld = StringUtils.splitToList(marketGeneralSetting.getSsoCompanyDomain(), SystemConstants.SEMICOLON);
            List<String> ssoDomainsNew = StringUtils.splitToList(marketUpdateGeneralSettingRequest.getSsoCompanyDomain(), SystemConstants.SEMICOLON);
            if (CollectionUtils.isNotEmpty(ssoDomainsOld) && CollectionUtils.isNotEmpty(ssoDomainsNew)) {
                List<String> intersectionList = Collections3.intersection(ssoDomainsOld, ssoDomainsNew);
                if (CollectionUtils.isNotEmpty(intersectionList)) {
                    ssoDomainsOld.removeAll(intersectionList);
                }
                removedSsoDomains = ssoDomainsOld;
            }
        }
        return removedSsoDomains;
    }


    public MarketLimitConfigVo convertLimitConfig(Long marketId) {
        return bizLimitFunc.getMarketLimitConfigVo(marketId);
    }
}
