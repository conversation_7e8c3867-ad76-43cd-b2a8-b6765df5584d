package com.pax.market.functional.admin.management.emm;

import com.pax.market.domain.entity.market.emm.EmmDeviceInstalledApp;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.vo.admin.management.emm.*;
import com.pax.market.vo.admin.management.terminal.TerminalDetailPageVo;
import com.pax.market.vo.admin.system.auditlog.AuditLogVoResponse;

/**
 * <AUTHOR>
 * @Date 2024/8/6
 */
public interface AdminEmmDeviceBasicFuncService {

    TerminalDetailPageVo<EmmDeviceDetailVo> findEmmDeviceDetailPage(Long deviceId);

    EmmDeviceMonitorVo getEmmDeviceMonitorVo(Long deviceId);

    PageInfo<EmmDeviceInstalledAppVo> findEmmDeviceInstalledAppPage(Page<EmmDeviceInstalledApp> page, Long deviceId);

    EmmDeviceTrafficVo getEmmDeviceTrafficVo(Long deviceId);

    EmmDeviceLocationVo getEmmDeviceLocationVo(Long deviceId);

    PageInfo<AuditLogVoResponse.OperationLogVo> findEmmDeviceAuditLogPage(Long deviceId, Integer year, String tz);

}
