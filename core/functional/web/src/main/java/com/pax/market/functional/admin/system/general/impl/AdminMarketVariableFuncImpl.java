/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.system.general.impl;

import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.DownloadTask;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.market.variable.MarketVariable;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.variable.VariableAppValidator;
import com.paxstore.global.domain.service.app.SolutionAppService;
import com.paxstore.global.domain.service.role.OperationService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.paxstore.market.domain.service.DownloadTaskService;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.market.domain.service.variable.MarketVariableService;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.domain.util.WebCryptoUtils;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.request.variable.ParameterVariableDeleteRequest;
import com.pax.market.dto.request.variable.ParameterVariableRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.excel.ExportExcel;
import com.pax.market.framework.common.exception.http.NotFoundException;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.activity.imports.variable.MarketVariableImportService;
import com.pax.market.functional.admin.system.general.AdminMarketVariableFunc;
import com.pax.market.vo.admin.common.variable.ParameterVariableVo;
import com.pax.market.vo.admin.common.variable.VariableRefAppVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 应用市场-参数变量设置
 *
 * <AUTHOR>
 * @date 2022/12/6
 */
@FunctionalService
@RequiredArgsConstructor
public class AdminMarketVariableFuncImpl extends AbstractFunctionalService implements AdminMarketVariableFunc {

    private final MarketVariableService marketVariableService;
    private final ApkService apkService;
    private final AppService appService;
    private final SolutionAppService solutionAppService;
    private final DownloadTaskService downloadTaskService;
    private final MarketVariableImportService marketVariableImportService;
    private final LicenseService licenseService;
    private final OperationService operationService;

    @Override
    public PageInfo<ParameterVariableVo> findMarketVariablePage(String key, Long appId) {
        assertCurrentUserIsRootReseller();
        MarketVariable marketVariable = new MarketVariable();
        marketVariable.setKey(StringUtils.trim(key));
        marketVariable.setApp(new App(appId));
        Page<MarketVariable> marketVariablePage = marketVariableService.findPage(parsePage(-1), marketVariable);

        marketVariablePage.getList().forEach(each -> {
            apkService.loadLatestApk(each.getApp());
            if (each.getApp() != null && each.getApp().getLatestOnlineApk()!=null){
                each.getApp().setPackageName(each.getApp().getLatestOnlineApk().getPackageName());
            }
            WebCryptoUtils.maskPasswordVariable(each);
        });
        return new PageInfo<>(BeanMapper.mapList(marketVariablePage.getList(), ParameterVariableVo.class), marketVariablePage.getCount());
    }

    @Override
    public PageInfo<VariableRefAppVo> findMarketVariableUsedAppPage() {
        assertCurrentUserIsRootReseller();
        List<App> appList = marketVariableService.findMarketVariableRelatedAppList();
        return new PageInfo<>(convertAppVoList(appList), appList.size());
    }

    @Override
    public PageInfo<VariableRefAppVo> findMarketVariableSupportedAppPage(String type, String name) {
        assertCurrentUserIsRootReseller();
        AppApkQuery appApkQuery = new AppApkQuery();
        appApkQuery.setBaseType(ApkType.PARAMETER_APP);
        appApkQuery.setKeyWords(StringUtils.trim(name));
        appApkQuery.setCurrentMarketId(getCurrentMarketId());
        appApkQuery.setType(type);
        Page<App> appPage;
        if (AppType.SOLUTION.equals(type)){
            boolean allowIndustrySolution = licenseService.getLicenseInfo().isAllowIndustrySolution();
            if (!allowIndustrySolution){
                return new PageInfo<>();
            }
            appApkQuery.setResellerId(getCurrentResellerId());
            appPage = solutionAppService.findOnlineSolutionAppPageForVariable(parsePage(), appApkQuery);
        }else {
            appApkQuery.setType(AppType.GENERAL);
            appPage  = appService.findOnlineAppPage(parsePage(-1), appApkQuery);
        }
        return new PageInfo<>(convertAppVoList(appPage.getList()), appPage.getCount());
    }

    private List<VariableRefAppVo> convertAppVoList(List<App> appList) {
        List<VariableRefAppVo> appVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appList)) {
            appList.forEach(e -> {
                apkService.loadLatestApk(e);
                Apk latestOnlineApk = ObjectUtils.defaultIfNull(e.getLatestOnlineApk(), new Apk());
                appVos.add(VariableRefAppVo.builder()
                        .id(e.getId())
                        .appName(latestOnlineApk.getAppName())
                        .packageName(latestOnlineApk.getPackageName())
                        .build());
            });
        }
        return appVos;
    }

    @Override
    public void createMarketVariable(ParameterVariableRequest createRequest) {
        validateVariableAccess();
        assertCurrentUserIsRootReseller();
        WebCryptoUtils.unMaskAndDecryptPasswordVariable(null, createRequest);
        MarketVariable marketVariable = BeanMapper.map(createRequest, MarketVariable.class);
        marketVariable.setApp(new App(createRequest.getAppId()));
        Validators.validate(new VariableAppValidator(marketVariable));
        marketVariableService.save(marketVariable);
    }

    @Override
    public void updateMarketVariable(Long marketVariableId, ParameterVariableRequest updateRequest) {
        validateVariableAccess();
        assertCurrentUserIsRootReseller();
        MarketVariable marketVariable = marketVariableService.get(marketVariableId);
        if (marketVariable == null) {
            throw new NotFoundException(ApiCodes.VARIABLE_NOT_FOUND);
        }
        Long originalAppId = marketVariable.getAppId();
        WebCryptoUtils.unMaskAndDecryptPasswordVariable(marketVariable, updateRequest);
        BeanMapper.copy(updateRequest, marketVariable);
        marketVariable.setApp(new App(updateRequest.getAppId()));
        if (!LongUtils.equals(originalAppId, updateRequest.getAppId())){
            Validators.validate(new VariableAppValidator(marketVariable));
        }
        marketVariableService.save(marketVariable);
    }

    @Override
    public void deleteMarketVariable(Long marketVariableId) {
        validateVariableAccess();
        assertCurrentUserIsRootReseller();
        MarketVariable marketVariable = marketVariableService.get(marketVariableId);
        if (marketVariable == null) {
            throw new NotFoundException(ApiCodes.VARIABLE_NOT_FOUND);
        }
        marketVariableService.delete(marketVariable);
    }

    @Override
    public void batchDeleteTerminalVariables(ParameterVariableDeleteRequest deleteRequest) {
        validateVariableAccess();
        assertCurrentUserIsRootReseller();
        marketVariableService.deleteList(deleteRequest.getVariableIds());
    }

    @Override
    public Long createMarketVariableImportTemplateDownloadTask() throws IOException {
        assertCurrentUserIsRootReseller();
        Locale locale = LocaleUtils.getEnvLocale();
        String title = MessageUtils.getLocaleMessage("title.market.variables", locale);
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream(); ExportExcel exportExcel = new ExportExcel(title, MarketVariable.class, 2, true, locale)) {
            String fileName = title + ".xlsx";
            List<MarketVariable> list = new ArrayList<>();
            MarketVariable marketVariable1 = new MarketVariable();
            marketVariable1.setApp(new App("app.package.name"));
            marketVariable1.setKey("MARKET_NAME");
            marketVariable1.setType(VariableType.TEXT);
            marketVariable1.setValue(getCurrentMarket().getName());
            marketVariable1.setRemarks(MessageUtils.getLocaleMessage("title.market.variable.remark1", locale));
            MarketVariable marketVariable2 = new MarketVariable();
            marketVariable2.setKey("MARKET_DOMAIN");
            marketVariable2.setType(VariableType.TEXT);
            marketVariable2.setValue(getCurrentMarket().getDomain());
            marketVariable2.setRemarks(MessageUtils.getLocaleMessage("title.market.variable.remark2", locale));
            MarketVariable marketVariable3 = new MarketVariable();
            marketVariable3.setKey("PASSWORD");
            marketVariable3.setType(VariableType.PASSWORD);
            marketVariable3.setValue("123456");
            marketVariable3.setRemarks(MessageUtils.getLocaleMessage("title.variable.remark.password", locale));
            list.add(marketVariable1);
            list.add(marketVariable2);
            list.add(marketVariable3);
            exportExcel.setDataList(list).write(bos);
            String fileId = FileUploader.uploadFile(bos.toByteArray(), "MarketVariablesImport", "xlsx", SupportedFileTypes.TEMP);
            DownloadTask downloadTask = downloadTaskService.createDownloadTask(fileName, fileId, DownloadTaskStatus.PENDING, true, DownloadTaskType.MARKET_VARIABLE_IMPORT_TEMPLATE);
            return downloadTask.getId();
        } catch (Exception ex) {
            logger.error("Error download market variable template", ex);
            throw new BusinessException(ApiCodes.VARIABLE_TEMPLATE_DOWNLOAD_FAILED);
        }
    }

    @Override
    public ImportExportActivityInfo importMarketVariable(MultipartFile file, String tz) throws IOException {
        validateVariableAccess();
        assertCurrentUserIsRootReseller();
        if (file == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        Activity activity = marketVariableImportService.importData(file.getOriginalFilename(), file.getBytes(), tz);
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }

    private void validateVariableAccess() {
        if (!operationService.isUserOperationAvailable(MarketSettingKey.tmParameterVariables)) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
    }
}