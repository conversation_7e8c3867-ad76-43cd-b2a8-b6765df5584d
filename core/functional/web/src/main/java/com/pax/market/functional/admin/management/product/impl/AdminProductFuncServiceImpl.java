/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.management.product.impl;

import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.admin.management.product.AdminProductFuncService;
import com.pax.market.functional.support.ProductTypeSupport;
import com.pax.market.vo.admin.management.product.ProductDetailVo;
import com.pax.market.vo.admin.management.product.ProductVo;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 机型相关方法
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@FunctionalService
@RequiredArgsConstructor
public class AdminProductFuncServiceImpl extends AbstractFunctionalService implements AdminProductFuncService {
    private final ProductTypeSupport productTypeSupport;
    @Override
    public PageInfo<ProductVo> listProducts() {
        List<ProductVo> productVos = productTypeSupport.getAllProjectTypes();
        return new PageInfo<>(productVos,productVos.size());
    }
    
    @Override
    public ProductDetailVo getProductDetail(String codeValue) {
        return productTypeSupport.getProjectTypeDetail(codeValue);
    }


}