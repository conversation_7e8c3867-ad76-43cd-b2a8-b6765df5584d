/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.developer.validator;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.AppType;
import com.pax.market.constants.SystemConstants;
import com.paxstore.global.domain.service.app.SolutionAppService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.paxstore.global.domain.service.developer.DeveloperService;

/**
 * <AUTHOR>
 * @date 2023/3/8
 */
public class DeveloperAppTypeValidator extends Validator<String> {

    /**
     * 用来区分禁用Solution操作
     * true 则校验当前开发者是否开启solution开关
     * false 检查是否存在solution数据 && 开发者solution开关关闭
     */
    private boolean isSolutionForbidden = false;

    public DeveloperAppTypeValidator(String type, boolean isSolutionForbidden) {
        super(type);
        this.isSolutionForbidden = isSolutionForbidden;
    }

    public DeveloperAppTypeValidator(String type) {
        super(type);
    }

    /**
     * Validate boolean.
     *
     * @return the boolean
     */
    @Override
    public boolean validate() {
        CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);
        DeveloperService developerService = SpringContextHolder.getBean(DeveloperService.class);
        Long currentMarketId = currentLoginProvider.getCurrentMarketInfo().getId();
        Long currentDeveloperId = currentLoginProvider.getCurrentUserInfo() != null
                ? currentLoginProvider.getCurrentUserInfo().getCurrentDeveloperId()
                : currentLoginProvider.getCurrentDeveloperInfo().getId();

        if (currentDeveloperId == null) {
            throw new BusinessException(ApiCodes.DEVELOPER_NOT_FOUND);
        }
        if (!AppType.asList.contains(validateTarget)) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        if (AppType.SOLUTION.equals(validateTarget)) {
            LicenseService licenseService = SpringContextHolder.getBean(LicenseService.class);
            if (!licenseService.getLicenseInfo().isAllowIndustrySolution()) {
                throw new BusinessException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
            }

            if (!SystemConstants.SUPER_MARKET_ID.equals(currentMarketId)) {
                throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
            }
            SolutionAppService solutionAppService = SpringContextHolder.getBean(SolutionAppService.class);
            if (isSolutionForbidden) {
                //当开关关闭固定操作不允许
                if (!developerService.isAllowIndustrySolution(currentDeveloperId)) {
                    throw new BusinessException(ApiCodes.DEVELOPER_INDUSTRY_SOLUTION_NOT_ALLOWED);

                }
            } else if (!solutionAppService.isExistIndustrySolutionApp(currentDeveloperId)
                    && !developerService.isAllowIndustrySolution(currentDeveloperId)) {
                //solution app没有数据且开关关闭
                throw new BusinessException(ApiCodes.DEVELOPER_INDUSTRY_SOLUTION_NOT_ALLOWED);
            }

        }
        return false;
    }
}