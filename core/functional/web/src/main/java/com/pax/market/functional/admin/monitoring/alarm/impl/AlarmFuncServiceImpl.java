package com.pax.market.functional.admin.monitoring.alarm.impl;

import com.google.common.collect.Sets;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.market.MarketAdvancedSetting;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.alarm.Alarm;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.pushtask.TerminalOperation;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalDetail;
import com.pax.market.domain.entity.market.vas.TerminalAttestationAlarm;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.dto.LicenseInfo;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.request.activity.AppSignFailedExportRequest;
import com.pax.market.dto.request.activity.BaseExportRequest;
import com.pax.market.dto.request.activity.TerminalExportRequest;
import com.pax.market.dto.request.admin.monitoring.alarm.AlarmSettingRequest;
import com.pax.market.dto.request.admin.monitoring.alarm.AlarmTerminalQuery;
import com.pax.market.dto.request.admin.monitoring.alarm.LockTerminalRequest;
import com.pax.market.dto.request.terminal.TerminalBatchUpdateRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.activity.exports.*;
import com.pax.market.functional.admin.monitoring.alarm.AlarmFuncService;
import com.pax.market.functional.push.PushStub;
import com.pax.market.functional.support.TerminalRegistrySupport;
import com.pax.market.functional.terminal.TerminalGeoFenceWhiteListFunc;
import com.pax.market.functional.utils.developer.DeveloperUtils;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.alarm.AlarmSettingRequestValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalBatchUpdateRequestBatchSizeValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalBatchUpdateRequestTerminalIdExistsAndOwnerValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalQueryValidator;
import com.pax.market.service.center.cachable.LocalCacheMarket2VasService;
import com.pax.market.vo.admin.monitoring.alarm.*;
import com.pax.market.vo.admin.monitoring.alarm.AlarmSettingVo.AlarmReceiverVo;
import com.pax.vas.common.VasConstants;
import com.paxstore.domain.support.SignatureSupport;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.market.MarketAdvancedSettingService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.global.domain.service.vas.ServiceResellerService;
import com.paxstore.market.domain.service.alarm.AlarmService;
import com.paxstore.market.domain.service.pushtask.TerminalOperationService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.market.domain.service.terminal.TerminalDetailService;
import com.paxstore.market.domain.service.vas.TerminalAttestationAlarmService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 告警相关方法
 *
 * <AUTHOR>
 */
@FunctionalService
@AllArgsConstructor
@Slf4j
public class AlarmFuncServiceImpl extends AbstractFunctionalService implements AlarmFuncService {

    private final AlarmService alarmService;
    private final AppSignFailedExportService appSignFailedExportService;
    private final TerminalOutOfGeoExportService terminalOutOfGeoExportService;
    private final TerminalPrinterOutPaperExportService terminalPrinterOutPaperExportService;
    private final TerminalTamperExportService terminalTamperExportService;
    private final SignatureSupport signatureSupport;
    private final MarketTerminalService marketTerminalService;
    private final ApkService apkService;
    private final UserService userService;
    private final TerminalRegistrySupport terminalRegistrySupport;
    private final TerminalAttestationAlarmService attestationAlarmService;
    private final TerminalAttestationAlarmExportService terminalAttestationAlarmExportService;
    private final TerminalGeoFenceWhiteListFunc terminalGeoFenceWhiteListFunc;
    private final LocalCacheMarket2VasService localCacheMarket2VasService;
    private final ServiceResellerService serviceResellerService;
    private final TerminalLowCoinBatteryExportService terminalLowCoinBatteryExportService;
    private final TerminalBatteryUnHealthyExportService batteryUnHealthyExportService;
    private final TerminalDetailService terminalDetailService;
    private final TerminalOperationService terminalOperationService;
    private final PushStub pushStub;
    private final TerminalRegistryService terminalRegistryService;
    private final ModelService modelService;
    private final LicenseService licenseService;
    private final MarketAdvancedSettingService marketAdvancedSettingService;

    @Override
    public AlarmDigitalVo getAlarmDigitalVo() {
        AlarmDigitalVo alarmDigitalVo = new AlarmDigitalVo();
        Terminal terminal = new Terminal();
        terminal.setStatus(TerminalStatus.ACTIVE);
        terminal.setMarketId(getCurrentMarketId());
        terminal.setReseller(new Reseller(getCurrentResellerId()));
        if (getCurrentMarket().getAllowGeoFence()) {
            alarmDigitalVo.setOutGeoFence(getTerminalCount(AlarmType.OUT_GEO_FENCE, terminal));
        }
        alarmDigitalVo.setPrinterOutPaper(getTerminalCount(AlarmType.PRINTER_OUT_PAPER, terminal));
        alarmDigitalVo.setTamper(getTerminalCount(AlarmType.TAMPER, terminal));
        alarmDigitalVo.setAppSignature(getAppCount());
        alarmDigitalVo.setAttestFailed(getAttestationFailed(terminal));
        alarmDigitalVo.setLowCoinBattery(getTerminalCount(AlarmType.LOW_COIN_BATTERY, terminal));
        alarmDigitalVo.setBatteryUnHealthy(getTerminalCount(AlarmType.BATTERY_UNHEALTHY, terminal));
        return alarmDigitalVo;
    }


    public Integer getAttestationFailed(Terminal terminal) {
        return attestationAlarmService.getAttestationFailed(terminal);
    }


    /**
     * 获取数字卡片中终端相关的数量
     *
     * @param type     告警类型
     * @param terminal 终端
     * @return count
     */
    public Integer getTerminalCount(String type, Terminal terminal) {
        int count = 0;
        switch (type) {
            case AlarmType.OUT_GEO_FENCE -> {
                List<Long> excludeTerminalIds = terminalGeoFenceWhiteListFunc.findCurrentExistTerminalIds();
                terminal.setExcludeTerminalIds(excludeTerminalIds);
                count = marketTerminalService.getTerminalOGFAlarmCount(terminal);
            }
            case AlarmType.PRINTER_OUT_PAPER -> count = marketTerminalService.getTerminalPOPAlarmCount(terminal);
            case AlarmType.TAMPER -> count = marketTerminalService.getTerminalTamperAlarmCount(terminal);
            case AlarmType.LOW_COIN_BATTERY -> count = marketTerminalService.getTerminalLowCoinBatteryAlarmCount(terminal);
            case AlarmType.BATTERY_UNHEALTHY -> count = marketTerminalService.getTerminalBatteryUnHealthAlarmCount(terminal);
            default -> {
            }
        }
        return count;
    }

    /**
     * 获取数字卡片中应用相关的数量
     *
     * @return count
     */
    public Integer getAppCount() {
        int count = 0;
        if (signatureSupport.isResellerCustomSignatureEnabled(getCurrentMarketId(), getCurrentResellerId())) {
            count = apkService.getSignFailedApkCount();
        }
        return count;
    }

    @Override
    public ImportExportActivityInfo createExportAlarmDownloadTask(BaseExportRequest exportRequest, String type, String tz) {
        Activity activity;
        if (!getCurrentMarket().getAllowGeoFence() && AlarmType.OUT_GEO_FENCE.equals(type)) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        if (AlarmType.TERMINAL_ALARM_TYPES.contains(type)) {
            TerminalExportRequest terminalExportRequest = new TerminalExportRequest();
            terminalExportRequest.setResellerId(getCurrentResellerId());
            terminalExportRequest.setDigitalType(type);
            terminalExportRequest.setStatus(TerminalStatus.ACTIVE);
            terminalExportRequest.setTotalCount(exportRequest.getTotalCount());
            activity = switch (type) {
                case AlarmType.OUT_GEO_FENCE ->
                        terminalOutOfGeoExportService.exportData(terminalExportRequest, tz);
                case AlarmType.PRINTER_OUT_PAPER ->
                        terminalPrinterOutPaperExportService.exportData(terminalExportRequest, tz);
                case AlarmType.TAMPER -> terminalTamperExportService.exportData(terminalExportRequest, tz);
                case AlarmType.ATTESTATION_FAILED ->
                        terminalAttestationAlarmExportService.exportData(terminalExportRequest, tz);
                case AlarmType.LOW_COIN_BATTERY ->
                        terminalLowCoinBatteryExportService.exportData(terminalExportRequest, tz);
                case AlarmType.BATTERY_UNHEALTHY ->
                        batteryUnHealthyExportService.exportData(terminalExportRequest, tz);
                default -> throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
            };
        } else if (AlarmType.APP_SIGNATURE.equals(type)) {
            activity = appSignFailedExportService.exportData(BeanMapper.map(exportRequest, AppSignFailedExportRequest.class), tz);
        } else {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }

    @Override
    public PageInfo<AlarmTypeVo> findAlarmTypes() {
        List<AlarmTypeVo> alarmTypeList = new ArrayList<>();
        Long marketId = getCurrentMarketId();
        boolean customSignatureEnabled = signatureSupport.isResellerCustomSignatureEnabled(marketId, getCurrentResellerId());
        if (customSignatureEnabled) {
            alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.APP_SIGNATURE).build());
        }
        if (!SystemConstants.SUPER_MARKET_ID.equals(marketId)) {
            if (getCurrentMarket().getAllowGeoFence()) {
                alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.OUT_GEO_FENCE).build());
            }
            alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.PRINTER_OUT_PAPER).build());
            alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.TAMPER).build());

            UserInfo currentUser = getCurrentUser();
            boolean isRootReseller = currentUser != null && currentUser.getCurrentReseller() != null && LongUtils.isBlankOrNotPositive(currentUser.getCurrentReseller().getParentId());
            if (localCacheMarket2VasService.isServiceEnabledActiveAndSubscribed(marketId, VasConstants.ServiceType.AIR_SHIELD)
                    && (isRootReseller || serviceResellerService.checkServiceReseller(VasConstants.ServiceType.AIR_SHIELD, getCurrentMarketId(), getCurrentResellerId()))) {

                alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.ATTESTATION_FAILED).build());
            }

            alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.LOW_COIN_BATTERY).build());
            alarmTypeList.add(AlarmTypeVo.builder().type(AlarmType.BATTERY_UNHEALTHY).build());
        }

        //接收消息类型
        Alarm alarm = new Alarm(getCurrentResellerId(), marketId, null);
        List<Alarm> alarmList = alarmService.findList(alarm);
        if (CollectionUtils.isNotEmpty(alarmList)) {
            alarmTypeList.forEach(alarmTypeVo -> {
                alarmList.forEach(e -> {
                    if (alarmTypeVo.getType().equals(e.getType())) {
                        alarmTypeVo.setReceiveType(e.getReceiveType());
                    }
                });
            });
        }


        return new PageInfo<>(alarmTypeList, alarmTypeList.size());
    }


    @Override
    public PageInfo<AlarmTerminalVo> findAlarmTerminalPage(Page<Terminal> page, AlarmTerminalQuery query) {
        if (!getCurrentMarket().getAllowGeoFence() && AlarmType.OUT_GEO_FENCE.equals(query.getType())) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        Terminal terminal = new Terminal();
        terminal.setMarketId(terminal.getCurrentMarketId());
        terminal.setStatus(TerminalStatus.ACTIVE);
        terminal.setLockedStatus(query.getLockedStatus());
        Set<Long> queryModelIds = Sets.newHashSet();
        if (StringUtils.isNotEmpty(query.getModelIds())) {
            queryModelIds = StringUtils.splitToLongSet(query.getModelIds(), ",");
        }
        terminal.setModelIdsFilter(queryModelIds);
        Set<Long> queryResellerIds = Sets.newHashSet();
        if (StringUtils.isNotEmpty(query.getTerminalResellerIds())) {
            queryResellerIds = StringUtils.splitToLongSet(query.getTerminalResellerIds(), ",");
        }
        Set<Long> queryMerchantIds = Sets.newHashSet();
        if (StringUtils.isNotEmpty(query.getTerminalMerchantIds())) {
            queryMerchantIds = StringUtils.splitToLongSet(query.getTerminalMerchantIds(), ",");
        }
        Set<String> serialNos = StringUtils.splitToSet(query.getSerialNo(), SystemConstants.COMMAS);
        if (CollectionUtils.isNotEmpty(serialNos) && serialNos.size() > 1) {
            terminal.setSerialNos(serialNos.stream().map(StringUtils::trim).filter(serialNo -> serialNo.length() < 33).collect(Collectors.toSet()));
        } else {
            terminal.setSerialNo(query.getSerialNo());
        }
        Set<String> tids = StringUtils.splitToSet(query.getTid(), SystemConstants.COMMAS);
        if (CollectionUtils.isNotEmpty(tids) && tids.size() > 1) {
            terminal.setTids(tids.stream().map(StringUtils::trim).filter(serialNo -> serialNo.length() < 33).collect(Collectors.toSet()));
        } else {
            terminal.setTID(query.getTid());
        }
        Set<String> names = StringUtils.splitToSet(query.getName(), SystemConstants.COMMAS);
        if (CollectionUtils.isNotEmpty(names) && names.size() > 1) {
            terminal.setNames(names.stream().map(StringUtils::trim).filter(name -> name.length() < 65).collect(Collectors.toSet()));
        } else {
            terminal.setName(query.getName());
        }
        Validators.validate(new TerminalQueryValidator(terminal));
        terminal.setTerminalResellerIds(queryResellerIds);
        terminal.setTerminalMerchantIds(queryMerchantIds);
        terminal.setReseller(new Reseller(getCurrentResellerId()));

        Page<Terminal> terminalPage;
        if (AlarmType.ATTESTATION_FAILED.equals(query.getType())) {
            page.setList(findAttestationAlarmTerminals(page, terminal));
            terminalPage = page;
        } else {
            if (AlarmType.OUT_GEO_FENCE.equals(query.getType())) {
                List<Long> excludeTerminalIds = terminalGeoFenceWhiteListFunc.findCurrentExistTerminalIds();
                terminal.setExcludeTerminalIds(excludeTerminalIds);
            }
            terminalPage = marketTerminalService.findPageForAlarm(page, terminal, query.getType());
        }
        terminalPage.getList().forEach(terminalRegistrySupport::loadTerminalModelResellerMerchantDetails);
        return new PageInfo<>(buildAlarmTerminalVo(terminalPage.getList(), query.getType()), terminalPage.getCount());
    }

    private List<Terminal> findAttestationAlarmTerminals(Page<Terminal> page, Terminal terminal) {
        List<TerminalAttestationAlarm> attestationAlarmList = attestationAlarmService.findAttestationAlarmList(page, terminal);
        List<Terminal> terminals = new ArrayList<>();
        for (TerminalAttestationAlarm alarm : attestationAlarmList) {
            Terminal t = new Terminal();
            t.setSeverity(alarm.getSeverity());
            t.setAlertTime(alarm.getDetectedDate());
            t.setId(alarm.getTerminalId());
            t.setFailedReason(alarm.getFailedItems());
            marketTerminalService.loadDetails(t);
            terminals.add(t);
        }
        return terminals;
    }


    private List<AlarmTerminalVo> buildAlarmTerminalVo(List<Terminal> terminals, String type) {
        List<AlarmTerminalVo> terminalVoList = new ArrayList<>();
        terminals.forEach(e -> {
            AlarmTerminalVo build = AlarmTerminalVo.builder()
                    .id(e.getId())
                    .factoryId(e.getFactoryId())
                    .name(e.getName())
                    .serialNo(e.getSerialNo())
                    .tid(e.getTID())
                    .modelName(e.getModelName())
                    .resellerName(e.getResellerName())
                    .merchantName(e.getMerchantName())
                    .severity(e.getSeverity())
                    .failedItems(e.getFailedReason())
                    .location(e.getLocation())
                    .status(e.getStatus())
                    .alertTime(e.getAlertTime())
                    .batteryStatus(e.getAlertStatus())
                    .batteryVoltage(e.getAlertValue())
                    .isSmart(ProductType.PAYMENT_SMART.equals(e.getProductType()))
                    .build();
            if (AlarmType.OUT_GEO_FENCE.equals(type)) {
                TerminalOperation latestTerminalOperations = terminalOperationService.getLatestTerminalOperations(e.getId(), Set.of(TerminalOperationKeys.GF_LOCK_TM, TerminalOperationKeys.LOCK_TM), null);
                if (latestTerminalOperations != null && latestTerminalOperations.getActionStatus() != TerminalActionStatus.SUCCEED) {
                    build.setOptStatus(latestTerminalOperations.getActionStatus());
                }
                TerminalDetail terminalDetail = terminalDetailService.getByKey(e.getId(), TerminalDetailKeys.LOCK_TM);
                if (terminalDetail != null) {
                    build.setLockStatus(terminalDetail.getValue());
                }
            }
            terminalVoList.add(build);
        });
        return terminalVoList;
    }

    @Override
    public PageInfo<AlarmApkVo> findAlarmApkPage(Page<App> appPage, String name) {
        AppApkQuery appApkQuery = new AppApkQuery();
        appApkQuery.setPage(appPage);
        appApkQuery.setKeyWords(StringUtils.trim(name));
        List<Apk> apkList = new ArrayList<>();
        if (signatureSupport.isResellerCustomSignatureEnabled(getCurrentMarketId(), getCurrentResellerId())) {
            apkList = apkService.findSignFailedApkList(appApkQuery);
            DeveloperUtils.loadDevelopersEmailAndName(apkList.stream().map(Apk::getDeveloper).toList());
        }

        return new PageInfo<>(buildAlarmApkVo(apkList), appApkQuery.getPage().getCount());
    }

    private List<AlarmApkVo> buildAlarmApkVo(List<Apk> apkList) {
        List<AlarmApkVo> apkVoList = new ArrayList<>();
        apkList.forEach(e -> {
            apkVoList.add(AlarmApkVo.builder()
                    .id(e.getId())
                    .appId(e.getAppId())
                    .appName(e.getAppName())
                    .versionName(e.getVersionName())
                    .developerNickname(e.getDeveloperNickName())
                    .factoryName(e.getFactoryName())
                    .updatedDate(e.getUpdatedDate())
                    .packageName(e.getPackageName())
                    .subscribe(e.getSubscribe())
                    .appType(e.getAppType())
                    .signType(e.getSignType())
                    .message(e.getMessage())
                    .build());
        });
        return apkVoList;
    }

    @Override
    public AlarmSettingVo getAlarmSetting(String type) {
        Alarm alarm = alarmService.get(new Alarm(getCurrentResellerId(), getCurrentMarketId(), type));
        AlarmSettingVo alarmSettingVo = new AlarmSettingVo();
        if (alarm != null) {
            alarmSettingVo = BeanMapper.map(alarm, AlarmSettingVo.class);
            if (StringUtils.isNotBlank(alarm.getReceiveEmails())) {
                alarmSettingVo.setReceiverList(findAlarmReceiverList(alarm.getReceiveEmails()));
            }
        }
        return alarmSettingVo;
    }


    @Override
    public void saveAlarmSetting(AlarmSettingRequest request) {
        LicenseInfo licenseInfo = licenseService.getLicenseInfo();
        MarketAdvancedSetting marketAdvancedSetting = marketAdvancedSettingService.getByMarketId(getCurrentMarketId());
        Validators.validate(new AlarmSettingRequestValidator(request, licenseInfo, marketAdvancedSetting));
        Alarm alarm = new Alarm();
        BeanMapper.copy(request, alarm);
        alarm.setMarketId(getCurrentMarketId());
        alarm.setReseller(new Reseller(getCurrentResellerId()));
        alarmService.save(alarm);
    }

    @Override
    public void updateTerminalLockStatus(LockTerminalRequest request) {
        if (!getCurrentUser().getAllowedPrivilegeCodes().contains(PrivilegeCodes.FUNC_TERMINAL_FULL)) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        if (CollectionUtils.isNotEmpty(request.getTerminalIds())) {
            Set<Long> terminalIds = request.getTerminalIds();
            TerminalBatchUpdateRequest terminalBatchUpdateRequest = BeanMapper.map(request, TerminalBatchUpdateRequest.class);
            Validators.validate(new TerminalBatchUpdateRequestBatchSizeValidator(terminalBatchUpdateRequest));
            List<TerminalRegistry> terminalRegistries = new ArrayList<>();
            terminalIds.forEach(terminalId -> {
                TerminalRegistry terminalRegistry = terminalRegistryService.get(terminalId);
                if (terminalRegistry == null) {
                    throw new BusinessException(ApiCodes.TERMINAL_NOT_EXIST);
                }
                modelService.loadDetails(terminalRegistry.getModel());
                if (!ProductType.PAYMENT_SMART.equals(terminalRegistry.getProductType())) {
                    throw new BusinessException(ApiCodes.PROFILE_SETTING_PRODUCT_NOT_SUPPORT);
                }
                TerminalRegistry terminal = new TerminalRegistry();
                terminal.setId(terminalRegistry.getId());
                terminal.setModel(terminalRegistry.getModel());
                terminal.setSerialNo(terminalRegistry.getSerialNo());
                terminalRegistries.add(terminal);
            });
            for (TerminalRegistry terminalRegistry : terminalRegistries) {
                TerminalOperation terminalOperation = new TerminalOperation();
                terminalOperation.setKey(TerminalOperationKeys.GF_LOCK_TM);
                terminalOperation.setTerminalId(terminalRegistry.getId());
                terminalOperation.setValue( request.getValue());
                terminalOperationService.createImmediatelyPushTerminalAction(terminalOperation);
                pushStub.pushTerminalOperationMessage(terminalRegistry);
            }
        }
    }


    /**
     * 查找配置的接收者信息所属来源
     *
     * @param emails the login emails
     * @return the list
     */
    private List<AlarmReceiverVo> findAlarmReceiverList(String emails) {
        List<AlarmReceiverVo> receiverList = new ArrayList<>();
        List<String> emailList = StringUtils.splitToList(emails, SystemConstants.SEMICOLON);
        emailList.forEach(email -> {
            User user = userService.getByLoginName(email);
            if (user != null) {
                receiverList.add(new AlarmReceiverVo(user.getName(), user.getLoginName(), SystemConstants.PLATFORM_USER));
            } else {
                receiverList.add(new AlarmReceiverVo(null, email, SystemConstants.NON_PLATFORM_USER));
            }
        });
        return receiverList;
    }


}
