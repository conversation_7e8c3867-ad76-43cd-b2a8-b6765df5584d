/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.task.activity.impl;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.market.Activity;
import com.paxstore.market.domain.service.ActivityService;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.request.activity.ActivityBatchDeleteRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.admin.task.activity.ActivityFuncService;
import com.pax.market.vo.admin.task.activity.ActivityDetailVo;
import com.pax.market.vo.admin.task.activity.ActivityVo;
import lombok.RequiredArgsConstructor;

/**
 * 我的活动接口
 *
 * <AUTHOR>
 * @date 2022/6/7
 */
@RequiredArgsConstructor
@FunctionalService
public class ActivityFuncServiceImpl extends AbstractFunctionalService implements ActivityFuncService {

    private final ActivityService activityService;

    @Override
    public PageInfo<ActivityVo> findActivityPage(Page<Activity> page, String name, String status, String type) {
        Activity activity = new Activity();
        activity.setName(StringUtils.trim(name));
        activity.setStatus(StringUtils.trim(status));
        activity.setType(StringUtils.trim(type));
        Page<Activity> activityPage = activityService.findPage(page, activity);
        return new PageInfo<>(BeanMapper.mapList(activityPage.getList(), ActivityVo.class), activityPage.getCount());
    }

    @Override
    public ActivityDetailVo getActivityDetailVo(Long activityId) {
        return BeanMapper.map(validateActivity(activityId), ActivityDetailVo.class);
    }

    @Override
    public void deleteActivity(Long activityId) {
        activityService.delete(validateActivity(activityId));
    }

    @Override
    public void batchDeleteActivities(ActivityBatchDeleteRequest activityBatchDeleteRequest) {
        activityService.deleteList(activityBatchDeleteRequest.getActivityIds());
    }

    private Activity validateActivity(Long activityId) {
        Activity activity = activityService.get(activityId);
        if (activity == null) {
            throw new BusinessException(ApiCodes.ACTIVITY_NOT_FOUNT);
        } else if (!LongUtils.equals(getCurrentUserId(), activity.getCreatedBy().getId())) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        return activity;
    }
}