/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.management.group.impl;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.app.SolutionApp;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.app.ApkParameter;
import com.pax.market.domain.entity.market.pushtask.TerminalGroupApk;
import com.pax.market.domain.entity.market.terminal.TerminalGroup;
import com.pax.market.domain.query.SolutionAppQuery;
import com.pax.market.domain.util.FileDownloadUrlGenerator;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.parameter.ParameterInfo;
import com.pax.market.dto.pushtask.SimpleTerminalGroupApkInfo;
import com.pax.market.dto.pushtask.TerminalGroupApkInfo;
import com.pax.market.dto.request.download.DownloadTokenRequest;
import com.pax.market.dto.request.market.RejectRequest;
import com.pax.market.dto.request.pushtask.GroupResumePushRequest;
import com.pax.market.dto.request.terminal.*;
import com.pax.market.dto.terminal.GroupTerminalInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.admin.management.group.TerminalGroupSolutionFunc;
import com.pax.market.functional.admin.task.approval.GroupPushTaskApprovalService;
import com.pax.market.functional.role.PrivilegeSupportFunctionService;
import com.pax.market.functional.support.SolutionVerifySupport;
import com.pax.market.functional.utils.DownloadTaskUtils;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.terminal.TerminalGroupSolutionCreateRequestValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalGroupSolutionPackageExistValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalOperationValidator;
import com.pax.market.functional.vas.App2ServiceFunctionService;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.vo.admin.common.apk.ApkDetailPopupVo;
import com.pax.market.vo.admin.management.group.GroupTerminalDetailVo;
import com.pax.market.vo.admin.management.group.apk.TerminalGroupApkParamVo;
import com.pax.market.vo.admin.management.group.solution.SolutionApkParameterVo;
import com.pax.market.vo.admin.management.group.solution.SolutionAppVo;
import com.pax.market.vo.admin.management.group.solution.TerminalGroupSolutionDetailVo;
import com.pax.market.vo.admin.management.group.solution.TerminalGroupSolutionVo;
import com.paxstore.global.domain.service.app.SolutionAppService;
import com.paxstore.global.domain.service.app.SolutionChargeService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.global.domain.service.solution.SolutionResellerService;
import com.paxstore.market.domain.service.app.ApkParameterService;
import com.paxstore.market.domain.service.pushtask.BaseGroupApkService;
import com.paxstore.market.domain.service.pushtask.TerminalGroupSolutionService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.pax.market.constants.ProductTypeUtils.isTraditional;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
@FunctionalService
@RequiredArgsConstructor
public class TerminalGroupSolutionFuncImpl extends BaseTerminalGroupApkFuncService implements TerminalGroupSolutionFunc, GroupPushTaskApprovalService<TerminalGroupSolutionDetailVo> {


    private final TerminalGroupSolutionService groupSolutionService;
    private final ApkParameterService apkParameterService;
    private final ModelService modelService;
    private final PrivilegeSupportFunctionService privilegeSupportFunctionService;
    private final SolutionAppService solutionAppService;
    private final App2ServiceFunctionService app2ServiceFunctionService;
    private final SolutionVerifySupport solutionVerifySupport;
    private final SolutionResellerService solutionResellerService;
    private final SolutionChargeService solutionChargeService;

    /**
     * Gets group apk service.
     *
     * @return the group apk service
     */
    @Override
    BaseGroupApkService getGroupApkService() {
        return groupSolutionService;
    }


    @Override
    public Apk getAvailableApk(TerminalGroup group, Apk apk) {
        if (apkService.isSolutionApkAvailable(apk.getId(), group.getModel().getId())) {
            return apk;
        }
        return null;
    }


    @Override
    void validateApkParamTemplate(Apk apk, String paramTemplateName) {
        apkParamTemplateService.checkParamTemplateExist(apk, paramTemplateName);
    }

    @Override
    public PageInfo<SolutionAppVo> findOnlineSolutionAppPage(Long groupId, Long modelId, String name) {
        //1.在线可见 2.机型适配 3.分组的时候是传统机型否有推送app权限 4.获取最新在线版本
        Model model = modelService.getIncludeDeleted(modelId);
        if (modelId != null && model == null) {
            return new PageInfo<>();
        }

        TerminalGroup group = terminalGroupService.get(groupId);
        if (group == null) {
            return new PageInfo<>();
        }
        String apkType = null;
        if (isTraditional(model.getProductType())
                && !privilegeSupportFunctionService.currentUserHasPrivilege(PrivilegeCodes.FUNC_GROUP_APP_PARAM)) {
            apkType = ApkType.NORMAL_APP;
        }
        SolutionAppQuery solutionAppQuery = new SolutionAppQuery();
        solutionAppQuery.setCurrentMarketId(getCurrentMarketId());
        solutionAppQuery.setModelId(model.getId());
        solutionAppQuery.setBaseType(apkType);
        solutionAppQuery.setKeyWords(StringUtils.trim(name));
        solutionAppQuery.setResellerId(group.getResellerId());
        Page<SolutionApp> page = solutionAppService.findOnlineSolutionAppsForGroupPush(parsePage(), solutionAppQuery);
        return buildAppPageVo(page);
    }


    private PageInfo<SolutionAppVo> buildAppPageVo(Page<SolutionApp> page) {
        List<SolutionAppVo> appVoList = new ArrayList<>();
        page.getList().forEach(app ->
                appVoList.add(SolutionAppVo.builder()
                        .id(app.getId())
                        .name(app.getLatestApk().getApkDetail().getAppName())
                        .status(app.getStatus())
                        .price(isCurrentUserInRootReseller() ? app2ServiceFunctionService.getCurrentPrice(getCurrentMarketId(), app.getId()) : null)
                        .packageName(app.getPackageName())
                        .type(app.getType())
                        .apkId(app.getLatestApk().getId())
                        .apkIconFileId(FileDownloadUrlGenerator.generateFileUrl(app.getLatestApk().getApkIconFileId()))
                        .versionName(app.getLatestApk().getVersionName())
                        .apkType(app.getLatestApk().getApkType())
                        .displayFileSize(app.getDisplayFileSize())
                        .updatedDate(app.getLatestApk().getUpdatedDate())
                        .chargeMode(solutionChargeService.getChargeModeByAppId(app.getId()))
                        .build())

        );
        return new PageInfo<>(appVoList, page.getCount());
    }

    @Override
    public PageInfo<SolutionApkParameterVo> findSolutionApkParameterPage(Long groupId, Long modelId, String name) {
        Model model = modelService.getIncludeDeleted(modelId);
        if (modelId != null && model == null) {
            return new PageInfo<>();
        }
        TerminalGroup group = terminalGroupService.get(groupId);
        if (group == null) {
            return new PageInfo<>();
        }
        ApkParameter apkParameter = new ApkParameter();
        apkParameter.setName(StringUtils.trim(name));
        apkParameter.setModelId(modelId);
        apkParameter.setResellerId(group.getResellerId());
        apkParameter.setAppType(AppType.SOLUTION);
        apkParameter.setApkIdList(apkParameterSupport.findAvailableApkIdList(apkParameter));
        if (CollectionUtils.isEmpty(apkParameter.getApkIdList())) {
            return new PageInfo<>();
        }
        Page<ApkParameter> page = apkParameterService.findApkParameterPageForSolution(parsePage(), apkParameter);
        for (ApkParameter each : page.getList()) {
            each.setApk(apkService.get(each.getApk()));
        }
        List<SolutionApkParameterVo> apkParameterVos = BeanMapper.mapList(page.getList(), SolutionApkParameterVo.class);
        return new PageInfo<>(apkParameterVos, page.getCount());

    }

    @Override
    public PageInfo<TerminalGroupSolutionVo> findPushTasks(Long groupId, Boolean pendingOnly, Boolean historyOnly, String keyWords) {
        PageInfo<SimpleTerminalGroupApkInfo> groupApkInfoPageInfo = searchPushTasks(groupId, pendingOnly, historyOnly, keyWords);
        return new PageInfo<>(BeanMapper.mapList(groupApkInfoPageInfo.getList(), TerminalGroupSolutionVo.class), groupApkInfoPageInfo.getTotalCount());
    }

    /**
     * 创建推送任务，如果只有一条记录，返回ID.
     *
     * @return the long
     */
    @Override
    @Transactional //FIXME YH
    public IdVo createTerminalGroupSolutions(TerminalGroupSolutionCreateRequest groupSolutionCreateRequest) {
        TerminalGroup group = terminalGroupService.get(groupSolutionCreateRequest.getGroupId());
        checkNotNull(group, ApiCodes.TERMINAL_GROUP_NOT_FOUND);
        Validators.validate(new TerminalGroupSolutionCreateRequestValidator(groupSolutionCreateRequest, group.getModel().getId(), group.getResellerId(), getCurrentMarketId()));
        return IdVo.of(createGroupApks(group, groupSolutionCreateRequest.getApkIdList(), groupSolutionCreateRequest.getApkParameterIdList()));
    }

    @Override
    public TerminalGroupSolutionDetailVo getTaskDetails(Long groupApkId) {
        TerminalGroupApkInfo details = getPushTaskDetails(groupApkId);
        details.setApkAvailable(solutionVerifySupport.isSolutionApkAvailableContainSpecificReseller(groupApkId));
        TerminalGroupSolutionDetailVo groupSolutionDetailVo = BeanMapper.map(details, TerminalGroupSolutionDetailVo.class);
        return reloadActivateInfo(groupSolutionDetailVo);
    }


    @Override
    public void activateTerminalGroupSolution(Long groupSolutionApkId, ActivateRequest groupApkActivateRequest) {
        TerminalGroupApk terminalGroupApk =  getPushTask(groupSolutionApkId);
        if (terminalGroupApk.getApk() != null){
            App app = solutionAppService.get(terminalGroupApk.getApk().getAppId());
            if (app != null){
                Validators.validate(new TerminalGroupSolutionPackageExistValidator(app.getPackageName(), getCurrentMarketId()));
                TerminalGroup terminalGroup = terminalGroupApk.getGroup();
                boolean isSpecificReseller = solutionResellerService.isSolutionSpecificReseller(app.getId(), terminalGroup.getMarketId(), terminalGroup.getResellerId());
                if (!isSpecificReseller){
                    Apk apk = terminalGroupApk.getApk();
                    throw new BusinessException(ApiCodes.INDUSTRY_SOLUTION_APP_NOT_ALLOWED, null,  StringUtils.isNotEmpty(apk.getAppName()) ? apk.getAppName() : app.getPackageName());
                }
            }
        }
        activatePushTask(groupSolutionApkId, groupApkActivateRequest);
    }

    @Override
    public void submitTerminalGroupSolution(Long groupSolutionApkId, SubmitPushTaskRequest groupApkSubmitRequest) {
        if (BooleanUtils.isFalse(getCurrentMarket().getAllowPushTaskApproval())) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        addValidator(new TerminalOperationValidator(MarketSettingKey.tmPushApp)).validate();
        submitPushTask(groupSolutionApkId, getActionType(), groupApkSubmitRequest);
    }

    @Override
    public TerminalGroupSolutionDetailVo resetTerminalGroupSolution(Long groupSolutionApkId) {
        solutionVerifySupport.validateSolutionApkAvailableContainSpecificReseller(groupSolutionApkId);
        TerminalGroupApkInfo groupApkInfo = resetPushTask(groupSolutionApkId);
        return BeanMapper.map(groupApkInfo, TerminalGroupSolutionDetailVo.class);
    }

    @Override
    public void suspendTerminalGroupSolution(Long groupSolutionApkId) {
        suspendPushTask(groupSolutionApkId);
    }

    @Override
    public void deleteTerminalGroupSolution(Long groupSolutionApkId) {
        deletePushTask(groupSolutionApkId);
    }

    @Override
    public void updateTerminalGroupSolutionApkParam(Long groupSolutionApkId, String paramTemplateName, Long apkParameterId, Boolean resetToDefault) {
        solutionVerifySupport.validateSolutionApkAvailableContainSpecificReseller(groupSolutionApkId);
        updateTerminalGroupApkParamTask(groupSolutionApkId, paramTemplateName, apkParameterId, resetToDefault);
    }

    @Override
    public void updateTerminalGroupSolutionApkParamFormData(Long groupSolutionApkId, GroupApkParamUpdateRequest request) {
        solutionVerifySupport.validateSolutionApkAvailableContainSpecificReseller(groupSolutionApkId);
        updateTerminalGroupApkParamTaskFormData(groupSolutionApkId, request);
    }

    @Override
    public TerminalGroupApkParamVo getTerminalGroupSolutionParam(Long groupSolutionApkId, Long terminalActionId) {
        return getTerminalGroupApkParamTask(groupSolutionApkId, terminalActionId);
    }

    @Override
    public Long createGroupSolutionDataFileDownloadTask(Long groupSolutionApkId, DownloadTokenRequest downloadTokenRequest) {
        TerminalGroupApk groupApk = getGroupApkService().getWithoutCheck(groupSolutionApkId);
        checkNotNull(groupApk, getGroupApkService().getGroupApkNotFoundErrorCode());
        return DownloadTaskUtils.createParameterDataFileDownloadTask(apkService.get(groupApk.getApk()), downloadTokenRequest.getDownloadToken());
    }

    @Override
    public PageInfo<ParameterInfo> getGroupSolutionParamVariables(Long groupSolutionApkId) {
        return getGroupApkParamTaskVariables(groupSolutionApkId);
    }

    @Override
    public void saveGroupSolutionParamVariables(Long groupSolutionApkId, List<ParameterInfo> parameterInfoList) {
        saveGroupApkParamTaskVariables(groupSolutionApkId, parameterInfoList);
    }

    @Override
    public PageInfo<GroupTerminalDetailVo> searchGroupSolutionTerminals(Long groupSolutionApkId, String serialNo, String tid, String name, Integer actionStatus, Integer filterType, String errorCodesFilter) {
        PageInfo<GroupTerminalInfo> terminalInfoPageInfo = searchPushTaskTerminals(groupSolutionApkId, serialNo, tid, name, actionStatus, filterType, StringUtils.splitToIntList(errorCodesFilter, SystemConstants.COMMAS));
        return new PageInfo<>(BeanMapper.mapList(terminalInfoPageInfo.getList(), GroupTerminalDetailVo.class), terminalInfoPageInfo.getTotalCount());
    }

    @Override
    public PageInfo<GroupTerminalDetailVo> searchGroupSolutionParamTerminals(Long groupSolutionApkId, String serialNo, String tid, String name, Integer actionStatus, Integer filterType, String errorCodesFilter) {
        PageInfo<GroupTerminalInfo> terminalInfoPageInfo = searchPushTaskParamTerminals(groupSolutionApkId, serialNo, tid, name, actionStatus, filterType, StringUtils.splitToIntList(errorCodesFilter, SystemConstants.COMMAS));
        return new PageInfo<>(BeanMapper.mapList(terminalInfoPageInfo.getList(), GroupTerminalDetailVo.class), terminalInfoPageInfo.getTotalCount());
    }

    @Override
    public Activity createGroupSolutionTerminalsExportTasks(Long groupSolutionApkId, String tz) {
        return createPushTaskExportTask(groupSolutionApkId, tz);
    }

    @Override
    public PageInfo<ParameterInfo> getGroupTerminalSolutionParamVariables(Long groupSolutionApkId, Long terminalActionId) {
        return getGroupTerminalApkParamTaskVariables(groupSolutionApkId, terminalActionId);
    }

    @Override
    public void saveGroupTerminalSolutionParamVariables(Long groupSolutionApkId, Long terminalActionId, List<ParameterInfo> parameterInfoList) {
        saveGroupTerminalApkParamTaskVariables(groupSolutionApkId, terminalActionId, parameterInfoList);
    }

    @Override
    public void resumeGroupTerminalSolution(Long groupSolutionApkId, GroupResumePushRequest pushRequest) {
        resumeGroupApk(groupSolutionApkId, pushRequest);
    }

    @Override
    public void resumeGroupTerminalSolutionParam(Long groupSolutionApkParamId, GroupResumePushRequest pushRequest) {
        resumeGroupApkParam(groupSolutionApkParamId, pushRequest);
    }

    @Override
    public void createGroupSolutionFilter(Long groupSolutionApkId, GroupPushFilterCreateRequest createRequest) {
        createPushTaskFilter(groupSolutionApkId, createRequest);
    }

    @Override
    public void updateGroupSolutionFilter(Long filterId, GroupPushFilterCreateRequest request) {
        updatePushTaskFilter(filterId, request);
    }

    @Override
    public void deleteGroupSolutionFilter(Long filterId) {
        deletePushTaskFilter(filterId);
    }

    @Override
    public void updateGroupSolutionPushLimit(Long groupSolutionApkId, GroupPushLimitRequest request) {
        updatePushLimit(groupSolutionApkId, request);
    }

    @Override
    public ApkDetailPopupVo getSolutionApkDetailVo(Long groupSolutionApkId) {
        ApkDetailPopupVo apkDetailPopupVo = getApkDetailForGroupApkPop(groupSolutionApkId);
        apkDetailPopupVo.setDeveloperNickName(null);
        return apkDetailPopupVo;
    }

    @Override
    public int getActionType() {
        return DOWNLOAD_GROUP_APP;
    }

    @Override
    public String getType() {
        return PushApkType.SOLUTION;
    }

    @Override
    public TerminalGroupSolutionDetailVo getTaskDetail(Long referenceId) {
        TerminalGroupApkInfo details = getPushTaskDetails(referenceId);
        details.setApkAvailable(solutionVerifySupport.isSolutionApkAvailableContainSpecificReseller(referenceId));
        return BeanMapper.map(details, TerminalGroupSolutionDetailVo.class);
    }

    @Override
    public TerminalGroupSolutionDetailVo approvePushTask(Long referenceId) {
        super.approveGroupApk(referenceId);
        return null;
    }

    @Override
    public void rejectPushTask(Long referenceId, RejectRequest request) {
        super.rejectPushTask(referenceId, request);
    }
}