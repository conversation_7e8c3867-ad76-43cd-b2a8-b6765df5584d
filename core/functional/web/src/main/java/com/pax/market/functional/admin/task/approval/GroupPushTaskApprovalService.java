package com.pax.market.functional.admin.task.approval;

import com.pax.market.constants.TerminalActionType;
import com.pax.market.dto.request.market.RejectRequest;
import com.pax.market.vo.admin.management.group.BaseGroupPushTaskDetailVo;

public interface GroupPushTaskApprovalService<T extends  BaseGroupPushTaskDetailVo> extends TerminalActionType {
    int getActionType();
    String getType();
    T getTaskDetail(Long referenceId);
    T approvePushTask(Long referenceId);

    void rejectPushTask(Long referenceId, RejectRequest request);
}
