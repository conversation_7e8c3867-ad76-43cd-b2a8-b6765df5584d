/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.developer.validator;

import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.developer.Developer;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.paxstore.global.domain.service.developer.DeveloperService;

/**
 * The type Developer create id card no not registered validator.
 */
public class DeveloperIdCardNoNotRegisteredValidator extends Validator<String> {

    private Developer developer;

    /**
     * Instantiates a new Developer create id card no not registered validator.
     *
     * @param idCardNo the developer update request ---idCardNo
     * @param developer              the developer
     */
    public DeveloperIdCardNoNotRegisteredValidator(String idCardNo, Developer developer) {
        super(idCardNo);
        this.developer = developer;
    }

    @Override
    public boolean validate() {

        DeveloperService developerService = SpringContextHolder.getBean(DeveloperService.class);
        //the card no is not registered by others
        if (StringUtils.isNotBlank(validateTarget) && developerService.isIdCardNoRegistered(developer.getMarketId(), validateTarget, developer.getId())) {
            throw new BusinessException(ApiCodes.DEVELOPER_ID_CARD_NO_REGISTERED);
        }

        return true;
    }
}
