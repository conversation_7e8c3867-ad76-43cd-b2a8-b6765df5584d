package com.pax.market.functional.admin.platform.migration.impl;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.migration.ResellerMigrateHistory;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.request.organization.ResellerMigrationRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.admin.platform.migration.ResellerMigrationFunc;
import com.pax.market.functional.support.ResellerMigrateSupport;
import com.pax.market.mq.contract.migration.ResellerMigrateMessage;
import com.pax.market.mq.producer.gateway.migration.ResellerMigrateGateway;
import com.pax.market.vo.admin.platform.migration.ResellerMigrateHistoryVo;
import com.pax.vas.common.VasConstants;
import com.paxstore.global.domain.service.emm.EmmEnterpriseService;
import com.paxstore.global.domain.service.migration.ResellerMigrateHistoryService;
import com.paxstore.global.domain.service.vas.ServiceResellerService;
import com.paxstore.global.domain.utils.UserUtils;
import com.paxstore.market.domain.service.emm.EmmDeviceService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@FunctionalService
@RequiredArgsConstructor
public class ResellerMigrationFuncImpl extends AbstractFunctionalService implements ResellerMigrationFunc {
    private final ResellerMigrateHistoryService resellerMigrateHistoryService;
    private final ResellerMigrateGateway resellerMigrateGateway;
    private final ResellerMigrateSupport resellerMigrateSupport;
    private final EmmEnterpriseService emmEnterpriseService;
    private final EmmDeviceService emmDeviceService;
    private final ResellerService resellerService;
    private final ServiceResellerService serviceResellerService;

    @Override
    public PageInfo<ResellerMigrateHistoryVo> findResellerMigrationPage(String sourceResellerName) {
        ResellerMigrateHistory resellerMigrateHistory = new ResellerMigrateHistory();
        resellerMigrateHistory.setSourceResellerName(StringUtils.trim(sourceResellerName));
        Page<ResellerMigrateHistory> resellerMigrateHistoryPage = resellerMigrateHistoryService.findPage(parsePage(), resellerMigrateHistory);
        List<ResellerMigrateHistoryVo> resellerMigrateHistoryInfoList = BeanMapper.mapList(resellerMigrateHistoryPage.getList(), ResellerMigrateHistoryVo.class);
        UserUtils.loadUserNameAndEmail(resellerMigrateHistoryInfoList.stream().map(ResellerMigrateHistoryVo::getCreatedBy).collect(Collectors.toList()));
        return new PageInfo<>(resellerMigrateHistoryInfoList, resellerMigrateHistoryPage.getCount());
    }

    @Override
    public ResellerMigrateHistoryVo getResellerMigration(Long resellerMigrationId) {
        ResellerMigrateHistory resellerMigrateHistory = resellerMigrateHistoryService.get(resellerMigrationId);
        if (resellerMigrateHistory == null) {
            throw new BusinessException(ApiCodes.BAD_REQUEST, "Reseller migration history not fount.");
        }
        ResellerMigrateHistoryVo resellerMigrateHistoryVo = BeanMapper.map(resellerMigrateHistory, ResellerMigrateHistoryVo.class);
        UserUtils.loadUserNameAndEmail(resellerMigrateHistoryVo.getCreatedBy());
        return resellerMigrateHistoryVo;
    }

    @Override
    public ResellerMigrateHistoryVo createResellerMigration(ResellerMigrationRequest resellerMigrationRequest) {
        if (resellerMigrateHistoryService.isUnfinishedMigrationExist()) {
            throw new BusinessException(ApiCodes.BAD_REQUEST, "Unfinished migration task exists, unable to create task.");
        }

        Market sourceMarket = resellerMigrateSupport.validateAndGetSourceMarket(resellerMigrationRequest.getSourceMarketName());
        Reseller sourceReseller = resellerMigrateSupport.validateAndGetSourceReseller(sourceMarket, resellerMigrationRequest.getSourceResellerName());
        Market targetMarket = resellerMigrateSupport.validateAndGetTargetMarket(sourceMarket, resellerMigrationRequest.getTargetMarketName());
        Reseller targetReseller = resellerMigrateSupport.validateAndGetTargetReseller(targetMarket, sourceReseller, resellerMigrationRequest.isReplaceRootReseller());
        ensureEmptyEmmDevices(sourceReseller, targetMarket, resellerMigrationRequest.isReplaceRootReseller());

        ResellerMigrateHistory resellerMigrateHistory = resellerMigrateHistoryService.createResellerMigrate(resellerMigrationRequest.getSourceMarketName(), resellerMigrationRequest.getSourceResellerName(), resellerMigrationRequest.getTargetMarketName(), targetReseller == null ? null : targetReseller.getName());
        resellerMigrateGateway.send(ResellerMigrateMessage.of(sourceMarket.getId(), resellerMigrateHistory.getId()));
        return BeanMapper.map(resellerMigrateHistory, ResellerMigrateHistoryVo.class);
    }
    
    private void ensureEmptyEmmDevices(Reseller sourceReseller, Market targetMarket, boolean replaceRootReseller) {
        if(Objects.isNull(emmEnterpriseService.getIncludeDeletedEnterprise(sourceReseller.getMarketId()))) {
            return;
        }
        List<Long> childResellerIds = resellerService.findChildResellerIdsIncludeSelf(sourceReseller, Boolean.FALSE);
        List<Long> serviceResellerIds = serviceResellerService.findServiceResellerIds(sourceReseller.getMarketId(), VasConstants.ServiceType.EMM);
        List<Long> distributedEmmChildResellerIds = childResellerIds.stream().filter(serviceResellerIds::contains).toList();
        if(emmDeviceService.isExist4Resellers(distributedEmmChildResellerIds)) {
            throw new BusinessException(ApiCodes.RESELLER_HAS_EMM_DEVICES);
        }
        if(replaceRootReseller
                && Objects.nonNull(emmEnterpriseService.getIncludeDeletedEnterprise(targetMarket.getId()))
                && emmDeviceService.isExist4Market(targetMarket.getId())) {
            throw new BusinessException(ApiCodes.TARGET_MARKET_HAS_EMM_DEVICES);
        }
    }
}
