/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.account.impl;

import com.pax.market.constants.AgreementType;
import com.pax.market.domain.entity.global.agreement.extend.AgreementMapExtend;
import com.pax.market.domain.entity.global.agreement.extend.VasAgreementMapExtend;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.account.AccountAgreementFunc;
import com.pax.market.vo.account.agreement.AgreementMapExtendVo;
import com.pax.market.vo.account.agreement.VasAgreementMapExtendVo;
import com.paxstore.global.domain.service.agreement.AgreementMapService;
import com.paxstore.global.domain.service.agreement.AgreementService;
import com.paxstore.global.domain.service.agreement.VasAgreementMapService;
import com.paxstore.global.domain.service.agreement.VasAgreementService;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
@FunctionalService
@RequiredArgsConstructor
public class AccountAgreementFuncImpl extends AbstractFunctionalService implements AccountAgreementFunc {

    private final AgreementService agreementService;
    private final AgreementMapService agreementMapService;
    private final VasAgreementService vasAgreementService;
    private final VasAgreementMapService vasAgreementMapService;

    @Override
    public PageInfo<AgreementMapExtendVo> findSystemAgreementList(Page<AgreementMapExtend> page, Long userId) {
        AgreementMapExtend agreementMap = new AgreementMapExtend();
        agreementMap.setUserId(userId);
        //删除THIRD_PARTY_AGREEMENT_TYPE，THIRD_PARTY_AGREEMENT_TYPE在 findServiceAgreementList 服务协议中显示，系统协议中不显示
        List<String> searchList = AgreementType.lists.stream().filter(each -> !StringUtils.equals(AgreementType.THIRD_PARTY_AGREEMENT_TYPE, each))
                .collect(Collectors.toList());
        agreementMap.setTypes(searchList);
        Page<AgreementMapExtend> resultPage = agreementMapService.findPage(page, agreementMap);
        return new PageInfo<>(BeanMapper.mapList(resultPage.getList(), AgreementMapExtendVo.class), resultPage.getCount());
    }

    @Override
    public PageInfo<VasAgreementMapExtendVo> findServiceAgreementList(Page<VasAgreementMapExtend> page, Long userId) {
        VasAgreementMapExtend vasAgreementMap = new VasAgreementMapExtend();
        vasAgreementMap.setUserId(userId);
        Page<VasAgreementMapExtend> resultPage = vasAgreementMapService.findPage(page, vasAgreementMap);
        return new PageInfo<>(BeanMapper.mapList(resultPage.getList(), VasAgreementMapExtendVo.class), resultPage.getCount());
    }

}