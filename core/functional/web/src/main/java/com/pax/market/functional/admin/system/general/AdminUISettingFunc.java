/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.system.general;

import com.pax.market.dto.PageInfo;
import com.pax.market.vo.admin.system.general.ResellerSettingVo;
import com.pax.market.vo.admin.system.general.app.AppVo;
import com.pax.market.vo.admin.system.general.app.FeaturedAppVo;

/**
 * ui setting模块下市场和代理商共用的方法
 *
 * <AUTHOR>
 * @date 2022/12/23
 */
public interface AdminUISettingFunc {

    ResellerSettingVo getUiSetting();

    PageInfo<AppVo> findOnlineAppPageForFeatured(String keyWords);

    PageInfo<FeaturedAppVo> findFeaturedAppPageForPicture();

    void addFeaturedApp(Long appId);

    void updateFeaturedAppSort(Long featuredAppId, String action);

    void deleteFeaturedApp(Long appId);

    void updateFeaturedApp(Long appId, Long featuredAppId);
}