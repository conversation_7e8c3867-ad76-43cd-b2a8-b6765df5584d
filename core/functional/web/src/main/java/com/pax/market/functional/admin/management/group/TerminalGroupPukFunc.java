package com.pax.market.functional.admin.management.group;

import com.pax.market.domain.entity.market.Activity;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.request.admin.management.TerminalGroupQueryRequest;
import com.pax.market.dto.request.pushtask.GroupResumePushRequest;
import com.pax.market.dto.request.terminal.ActivateRequest;
import com.pax.market.dto.request.terminal.GroupOperationCreateRequest;
import com.pax.market.dto.request.terminal.GroupPushLimitRequest;
import com.pax.market.dto.request.terminal.SubmitPushTaskRequest;
import com.pax.market.vo.admin.management.group.GroupTerminalDetailVo;
import com.pax.market.vo.admin.management.group.puk.TerminalGroupFactoryPukVo;
import com.pax.market.vo.admin.management.group.puk.TerminalGroupPukDetailVo;
import com.pax.market.vo.admin.management.group.puk.TerminalGroupPukVo;

public interface TerminalGroupPukFunc {
    PageInfo<TerminalGroupPukVo> searchGroupPuk(Long groupId, Boolean pendingOnly, Boolean historyOnly, String key, String keyWords);

    TerminalGroupPukDetailVo getGroupPuk(Long groupOptId);

    TerminalGroupPukDetailVo createGroupPuk(GroupOperationCreateRequest groupOperationCreateRequest);

    void activateGroupPuk(Long groupOptId, ActivateRequest groupOptActivateRequest);
    void submitGroupPuk(Long groupOptId, SubmitPushTaskRequest groupOptSubmitRequest);
    TerminalGroupPukDetailVo resetGroupPuk(Long groupOptId);

    void suspendGroupPuk(Long groupOptId);

    void deleteGroupPuk(Long groupOptId);

    PageInfo<GroupTerminalDetailVo> searchGroupPukTerminals(TerminalGroupQueryRequest queryRequest);

    Activity createGroupPukTerminalsExportTasks(Long groupOptId, String tz);

    void resumeGroupTerminalPuk(Long groupOptId, GroupResumePushRequest pushRequest);

    void updateGroupPukPushLimit(Long groupOptId, GroupPushLimitRequest request);

    PageInfo<TerminalGroupFactoryPukVo> findSignaturePukPage(Long groupId);
}
