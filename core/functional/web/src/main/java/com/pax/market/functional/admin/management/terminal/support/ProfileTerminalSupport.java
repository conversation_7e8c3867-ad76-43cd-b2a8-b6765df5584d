package com.pax.market.functional.admin.management.terminal.support;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.functional.profile.ProfileFunc;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.pax.market.dto.profile.ProfileInfo;
import com.pax.market.functional.AbstractFunctionalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProfileTerminalSupport extends AbstractFunctionalService {

    private final MarketTerminalService marketTerminalService;
    private final ModelService modelService;
    private final ProfileFunc profileFunc;

    /**
     * Gets terminal profile.
     *
     * @param terminalId the terminal id
     * @return the terminal profile
     */
    public ProfileInfo getTerminalProfile(Long terminalId) {
        Terminal terminal = marketTerminalService.get(terminalId);
        if (terminal == null) {
            throw new BusinessException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        modelService.loadDetails(terminal.getModel());
        return profileFunc.getTerminalProfileByTerminal(terminal);
    }
}
