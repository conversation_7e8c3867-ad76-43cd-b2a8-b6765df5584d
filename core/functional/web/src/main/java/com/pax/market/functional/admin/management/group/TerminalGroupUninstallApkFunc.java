/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.admin.management.group;

import com.pax.market.domain.entity.market.Activity;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.request.admin.management.TerminalGroupQueryRequest;
import com.pax.market.dto.request.pushtask.GroupResumePushRequest;
import com.pax.market.dto.request.terminal.ActivateRequest;
import com.pax.market.dto.request.terminal.GroupPushLimitRequest;
import com.pax.market.dto.request.terminal.GroupUninstallApkCreateRequest;
import com.pax.market.dto.request.terminal.SubmitPushTaskRequest;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.vo.admin.management.group.GroupTerminalDetailVo;
import com.pax.market.vo.admin.management.group.uninstallapk.TerminalGroupUninstallApkDetailVo;
import com.pax.market.vo.admin.management.group.uninstallapk.TerminalGroupUninstallApkVo;

public interface TerminalGroupUninstallApkFunc {

    PageInfo<TerminalGroupUninstallApkVo> searchGroupUninstallApks(Long groupId, Boolean pendingOnly, Boolean historyOnly, String keyWords);

    TerminalGroupUninstallApkDetailVo getGroupUninstallApk(Long groupUninstallApkId);

    IdVo createGroupUninstallApk(GroupUninstallApkCreateRequest groupUninstallApkCreateRequest);

    void activateGroupUninstallApk(Long groupUninstallApkId, ActivateRequest groupUninstallApkActivateRequest);
    void submitGroupUninstallApk(Long groupUninstallApkId, SubmitPushTaskRequest groupUninstallApkSubmitRequest);

    TerminalGroupUninstallApkDetailVo resetGroupUninstallApk(Long groupUninstallApkId);

    void suspendGroupUninstallApk(Long groupUninstallApkId);

    void deleteGroupUninstallApk(Long groupUninstallApkId);

    PageInfo<GroupTerminalDetailVo> searchGroupUninstallApkTerminals(TerminalGroupQueryRequest queryRequest);

    Activity createGroupUninstallApkTerminalsExportTasks(Long groupUninstallApkId, String tz);

    void resumeGroupTerminalUninstallApk(Long groupUninstallApkId, GroupResumePushRequest groupResumePushRequest);

    void updateGroupUninstallApkPushLimit(Long groupUninstallApkId, GroupPushLimitRequest request);
}