package com.pax.market.functional.internal.impl;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.market.MarketGeneralSetting;
import com.pax.market.dto.sso.SsoMarketRoleInfo;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.global.domain.service.market.MarketGeneralSettingService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.setting.SsoSettingService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */

public abstract class BaseUserRoleFuncInternalImpl  {
    @Autowired
    private SsoSettingService ssoSettingService;
    @Autowired
    private MarketService marketService;
    @Autowired
    private MarketGeneralSettingService marketGeneralSettingService;

    public int validateExternalUserRole(Long marketId, String resellerName, String roleName, String email,
                                        List<SsoMarketRoleInfo> marketRoleInfos){

        if (Objects.isNull(marketService.get(marketId))){
            return ApiCodes.MARKET_NOT_FOUND;
        }
        if (CollectionUtils.isNotEmpty(marketRoleInfos)){
            if (StringUtils.isNotBlank(resellerName) || StringUtils.isNotBlank(roleName)){
                return  ApiCodes.SSO_MARKET_ROLE_INVALID;
            }
            if(marketRoleInfos.size() > 5){
                return ApiCodes.SSO_MULTI_MARKET_OVER_LIMIT;
            }
            return ssoSettingService.validateAndLoadMultiMarketSsoByEmail(email, marketRoleInfos);
        } else {
            //validate and load target market - only one market setting sso
            String emailDomain = StringUtils.substringAfterLast(email.toLowerCase(), "@");
            List<Market> marketList  = ssoSettingService.findMarketAllowSsoByDomain(emailDomain);
            //需要找到唯一配置的sso市场
            if (CollectionUtils.isEmpty(marketList) || marketList.size() != 1){
                //多市场配置了该sso domain需要更新openid server user 属性
                return ApiCodes.SSO_MARKET_ROLE_INVALID;
            }
            MarketGeneralSetting generalSetting = marketGeneralSettingService.getByMarketId(marketId);
            boolean targetMarketEnableSso = false;
            if (StringUtils.isNotBlank(generalSetting.getSsoCompanyDomain())){
                List<String> ssoDomains = StringUtils.splitToList(generalSetting.getSsoCompanyDomain(), SystemConstants.SEMICOLON);
                if (StringUtils.containsIgnoreCase(ssoDomains, emailDomain)){
                    targetMarketEnableSso = true;
                }
            }
            SsoMarketRoleInfo marketRole = new SsoMarketRoleInfo();
            if (targetMarketEnableSso) {
                //匹配直接加载到marketRoleInfos
                marketRole.setMarketId(marketId);
            }else {
                //匹配直接加载到marketRoleInfos
                marketRole.setMarketId(marketList.get(0).getId());
            }
            marketRole.setResellerName(resellerName);
            marketRole.setRole(roleName);
            marketRoleInfos.add(marketRole);
        }

        return ApiCodes.SUCCESS;
    }
}
