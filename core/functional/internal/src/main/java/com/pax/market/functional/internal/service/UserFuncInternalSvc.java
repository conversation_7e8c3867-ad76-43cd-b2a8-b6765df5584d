package com.pax.market.functional.internal.service;

import com.zolon.saas.api.common.response.MultiResponse;
import com.zolon.saas.api.common.response.PagedResponse;
import com.zolon.saas.api.common.response.SimpleResponse;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.store.func.marketplace.RoleFunc.UpdateExternalUserRoleRequest;
import com.zolon.saas.store.func.marketplace.dto.user.UserBasicDto;
import com.zolon.saas.store.func.marketplace.dto.user.UserDto;
import lombok.Data;

import java.util.Date;

public interface UserFuncInternalSvc {
    SingleResponse<UserDto> getUser(long userId);

    SingleResponse<UserDto> getUser(String loginName);

    SingleResponse<UserBasicDto> getUserBasic(long userId);

    MultiResponse<Long> findActiveUserIds(FindActiveUserIdRequest request);

    /**
     * Check if user email account enabled external authentication (e.g. OpenID)
     *
     * @param email User email account
     * @return true if email account enabled external authentication
     */
    SingleResponse<Boolean> isExternalUser(String email);
    
    SimpleResponse lock(long userId);
    SimpleResponse activateUser(ActivateUserRequest request);
    SingleResponse<UserDto> createExternalUser(CreateExternalUserRequest request);
    PagedResponse<UserDto> findUserByMarketResellerRole(FindUserByMarketResellerRoleRequest request);

    SingleResponse<UserDto> isValidAuthLog(String loginName, Long marketId, String clientId, Boolean actionSuccess);

    @Data
    public static class ActivateUserRequest {
    	private long userId;
    	private String userName;
    	private String password;
    	private Date passwordChangeTime;
    }
    @Data
    public static class FindActiveUserIdRequest {
    	private Date activeCheckDate; 
    	private int startIndex; 
    	private int limit;
    }
    @Data
    public static class CreateExternalUserRequest {
    	private String email;
    	private String name;
    	private String firstName;
    	private String lastName;
    	private String birthday;
    	//for role sync
    	private UpdateExternalUserRoleRequest updateExternalUserRoleRequest;
    }
    @Data
    public static class CreateUserLoginLogRequest {
        private Long id;
    	private long marketId;
    	private String loginName;
        private String clientId;
        private String remoteAddr;
        private String sessionId;
        private String actionType;
        private Boolean actionSuccess;
        private Integer errorCode;
        private String msgArgs;
        private String remarks;
        private Date createdDate;
        private String username;
    }

    @Data
    public static class FindUserByMarketResellerRoleRequest {
        private long marketId;
        private long resellerId;
        private long roleId;
        private String keyWords;
        private int pageNo;
        private int pageSize;
    }
}