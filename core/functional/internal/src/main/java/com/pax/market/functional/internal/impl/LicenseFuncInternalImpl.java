package com.pax.market.functional.internal.impl;

import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.functional.internal.service.LicenseFuncInternalSvc;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.zolon.saas.api.common.response.SingleResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;

@FunctionalService
@Primary
@RequiredArgsConstructor
public class LicenseFuncInternalImpl implements LicenseFuncInternalSvc {

	private final LicenseService licenseService;

	@Override
	public SingleResponse<Boolean> isWebPageFooterCustomizationAllowed() {
		return SingleResponse.of(licenseService.getLicenseInfo().isAllowFooter());
	}

	@Override
	public boolean isConnectionFeeViolationPolicyAllowed() {
		return licenseService.getLicenseInfo().isAllowConnectionFeeViolationPolicy();
	}

}