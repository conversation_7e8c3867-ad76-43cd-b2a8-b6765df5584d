package com.pax.market.functional.internal.service;

import com.zolon.saas.api.common.response.MultiResponse;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.store.func.marketplace.dto.developer.DeveloperDto;
import com.zolon.saas.store.func.marketplace.dto.user.UserDto;

public interface DeveloperFuncInternalSvc {
	SingleResponse<DeveloperDto> getByLoginName(Long marketId, String loginName);

	/**
	 * 注意，此接口和上面的接口不同的是会返回 admin, owner字段
	 *
	 * @param marketId
	 * @param userId
	 * @return
	 */
	SingleResponse<DeveloperDto> getByUserId(Long marketId, Long userId);

	MultiResponse<UserDto> findEnterpriseDeveloperUsers(Long developerId);

}