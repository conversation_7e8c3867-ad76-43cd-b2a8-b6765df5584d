package com.pax.market.functional.internal.service;

import com.zolon.saas.api.common.response.MultiResponse;
import com.zolon.saas.api.common.response.SimpleResponse;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.store.func.marketplace.dto.user.RoleDto;
import com.zolon.saas.store.func.marketplace.dto.user.auth.DocCenterAccessInfoDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

public interface RoleFuncInternalSvc {


    SingleResponse<RoleDto> findUserRole(Long marketId, Long userId, Long roleId);

    SingleResponse<Boolean> isPlatformAdmin(Long userId);

    SingleResponse<Boolean> isMarketplaceAdmin(Long marketId, Long userId);

    SingleResponse<Boolean> isUserNotGlobalReadonly(Long marketId, String loginName);

    MultiResponse<RoleDto> findUserRoles(Long marketId, Long userId);

    SingleResponse<DocCenterAccessInfoDto> getAuthAccessInfoForDocCenter(Long marketId, Long userId);
    SingleResponse<DocCenterAccessInfoDto> getAuthAccessInfoForDocCenter(Long userId);


    SimpleResponse updateExternalUserRole(UpdateExternalUserRoleRequest request);

    @Data
    class UpdateExternalUserRoleRequest {
    	private String externalUserEmail;
    	private String resellerName;
    	private String roleName;
        private Long marketId;
        private List<MultiMarketRole> multiMarketRoles;
    }

    @Getter
    @Setter
    class  MultiMarketRole {
        private String marketDomain;
        private String role;
        private String resellerName;
    }
}