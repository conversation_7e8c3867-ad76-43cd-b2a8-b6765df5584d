package com.pax.market.functional.vas.validator;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.app.App;
import com.paxstore.global.domain.service.app.AppService;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

import java.util.Objects;

public class CloudMsgAppIdValidator extends Validator<Long> {
    private Long currentDeveloperId;

    public CloudMsgAppIdValidator(Long appId, Long currentDeveloperId) {
        this.validateTarget = appId;
        this.currentDeveloperId = currentDeveloperId;
    }
    @Override
    public boolean validate() {
        App app = SpringContextHolder.getBean(AppService.class).get(validateTarget);
        if(Objects.isNull(app)) {
            throw new BusinessException(ApiCodes.APP_NOT_FOUND);
        }
        if(Objects.isNull(currentDeveloperId) || !LongUtils.equals(app.getDeveloperId(), currentDeveloperId)) {
            throw new BusinessException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        return false;
    }
}
