package com.pax.market.functional.validation.validators.apk;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.service.center.cachable.LocalCacheMarket2VasService;
import com.pax.vas.common.VasConstants;
import com.zolon.saas.vas.func.platform.VasPlatformFunc;
import com.zolon.saas.vas.func.platform.dto.ThirdpartyAppInfo;

public class ApkStacklyAccessKeyValidator extends Validator<Apk> {

    private App app;
    private Long marketId;

    /**
     * Instantiates a new Validator.
     *
     * @param app
     * @param apk
     * @param marketId
     */
    public ApkStacklyAccessKeyValidator(App app, Apk apk, Long marketId) {
        super(apk);
        this.app = app;
        this.marketId = marketId;
    }

    @Override
    public boolean validate() {
        if (validateTarget == null) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }
        // PAXStore client和AirViewer client不验证
        if (SystemConstants.PAC_NAME_PAXSTORE.equals(validateTarget.getPackageName())
                || SystemConstants.AIR_VIEWER_PACKAGE_NAME.equals(validateTarget.getPackageName())) {
            return false;
        }
        if (validateTarget.isSupportStackly() && StringUtils.isEmpty(validateTarget.getStacklyAccessKey())) {
            throw new BusinessException(ApiCodes.APK_STACKLY_ACCESS_KEY_MISMATCH);
        }
        LocalCacheMarket2VasService localCacheMarket2VasService = SpringContextHolder.getBean(LocalCacheMarket2VasService.class);
        if (!localCacheMarket2VasService.isServiceEnabledActiveAndSubscribed(marketId, VasConstants.ServiceType.STACKLYTICS)) {
            throw new BusinessException(ApiCodes.APK_STACKLY_SDK_NOT_ALLOW_IN_CURRENT_MARKET);
        }
        VasPlatformFunc vasPlatformFunc = SpringContextHolder.getBean(VasPlatformFunc.class);
        String accessKey = validateTarget.getStacklyAccessKey();
        ThirdpartyAppInfo thirdpartyAppInfo = vasPlatformFunc.getThirdpartyApp(marketId, app.getId(), null, VasConstants.ServiceType.STACKLYTICS).getData();
        if (thirdpartyAppInfo == null || accessKey != null && !accessKey.equals(thirdpartyAppInfo.getAccessKey())) {
            throw new BusinessException(ApiCodes.APK_STACKLY_ACCESS_KEY_MISMATCH);
        }
        return false;
    }
}
