/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.reseller;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.ResellerStatus;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

/**
 * Created by liukai on 2016/11/4.
 */
public class ResellerActiveValidator extends Validator<Reseller> {

    /**
     * Instantiates a new Reseller active validator.
     *
     * @param reseller the reseller
     */
    public ResellerActiveValidator(Reseller reseller) {
        super(reseller);
    }

    @Override
    public boolean validate() {
        CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);

        if (validateTarget == null || validateTarget.getMarketId().longValue() != currentLoginProvider.getCurrentMarketInfo().getId()) {
            throw new BusinessException(ApiCodes.RESELLER_NOT_EXIST);
        }

        if (!ResellerStatus.ACTIVE.equals(validateTarget.getStatus())) {
            throw new BusinessException(ApiCodes.RESELLER_NOT_ACTIVE);
        }

        return true;
    }
}
