/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.terminalstock;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.global.terminal.BaseTerminalEntity;
import com.pax.market.domain.entity.global.terminal.TerminalStock;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.terminal.TerminalStockService;
import com.pax.market.domain.util.SerialNoUtils;
import com.pax.market.functional.utils.TerminalUtils;
import com.pax.market.dto.request.terminalstock.TerminalStockBatchCreateRequest;
import com.pax.market.dto.request.terminalstock.TerminalStockCreateRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.NotFoundException;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The type Default terminal stock function service.
 *
 * <AUTHOR>
 * @date 2018 /9/4
 */
@FunctionalService
public class DefaultTerminalStockFunctionServiceImpl extends AbstractFunctionalService implements TerminalStockFunctionService {

    @Autowired
    private MarketService marketService;

    /**
     * The Terminal stock service.
     */
    @Autowired
    protected TerminalStockService terminalStockService;


    @Override
    public TerminalStock validateTerminalStock(Long terminalStockId) {
        if (LongUtils.isBlankOrNotPositive(terminalStockId)) {
            throw new NotFoundException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        TerminalStock terminalStock = terminalStockService.get(terminalStockId);
        if (terminalStock == null) {
            throw new NotFoundException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        return terminalStock;
    }

    @Override
    public TerminalStock searchBySN(String serialNo) {
        return terminalStockService.getBySerialNo(serialNo);
    }

    @Override
    public void globalTerminalStockCheck(BaseTerminalEntity<?> terminal) {
        Integer errorCode = terminalStockService.globalTerminalStockCheck(terminal);
        if (errorCode != null) {
            throw new BusinessException(errorCode, null, terminal.getSerialNo());
        }
    }

    @Override
    public void marketTerminalWhiteListCheck(BaseTerminalEntity<?> terminal) {
        Integer errorCode = terminalStockService.marketTerminalWhiteListCheck(terminal);
        if (errorCode != null) {
            throw new BusinessException(errorCode, null, terminal.getSerialNo());
        }
    }


    public void loadBatchCreate(TerminalStockBatchCreateRequest batchCreateRequest,
                                List<TerminalStock> terminalListToSave,
                                Model model) {
        int prefixEndIndex = SerialNoUtils.getPrefixEndIndex(batchCreateRequest.getSerialNo(), batchCreateRequest.getSerialNoEnd());
        String prefix = batchCreateRequest.getSerialNo().substring(0, prefixEndIndex);

        String newSerialStart = batchCreateRequest.getSerialNo().substring(prefixEndIndex);
        String newSerialEnd = batchCreateRequest.getSerialNoEnd().substring(prefixEndIndex);

        Pattern pattern = Pattern.compile(SystemConstants.TERMINAL_BATCH_CREATE_SERIAL_NO_REGULAR);
        Matcher matcherStart = pattern.matcher(newSerialStart);
        Matcher matcherEnd = pattern.matcher(newSerialEnd);

        matcherStart.matches();
        matcherEnd.matches();

        String start = matcherStart.group(2);
        int startI = Integer.parseInt(start);
        String appendixStart = matcherStart.group(3);

        String end = matcherEnd.group(2);
        int endI = Integer.parseInt(end);

        int middle = (startI + endI) / 2;
        List<String> serialNos = new ArrayList<>();
        for (int i = startI; i < middle; i++) {
            serialNos.add(prefix + TerminalUtils.getSerialNo(start.length(), i) + appendixStart);
        }
        checkExistSns(serialNos);

        List<String> serialNos1 = new ArrayList<>();
        for (int i = middle; i < endI + 1; i++) {
            serialNos1.add(prefix + TerminalUtils.getSerialNo(start.length(), i) + appendixStart);
        }
        checkExistSns(serialNos1);

        for (int i = startI; i < endI + 1; i++) {
            TerminalStockCreateRequest terminalCreateRequestI = new TerminalStockCreateRequest();
            BeanMapper.copy(batchCreateRequest, terminalCreateRequestI);
            terminalCreateRequestI.setSerialNo(prefix + TerminalUtils.getSerialNo(start.length(), i) + appendixStart);
            TerminalStock terminal = BeanMapper.map(terminalCreateRequestI, TerminalStock.class);
            initTerminal(terminal, model);
            terminalListToSave.add(terminal);
        }
    }

    public void loadSingleCreate(TerminalStockBatchCreateRequest batchCreateRequest,
                                 List<TerminalStock> terminalListToSave,
                                 Model model) {
        TerminalUtils.checkTerminalSerialNo(batchCreateRequest.getSerialNo());
        TerminalUtils.checkTerminalSerialNo(batchCreateRequest.getSerialNoEnd());
        List<String> serialNos = new ArrayList<>();
        serialNos.add(batchCreateRequest.getSerialNo());
        checkExistSns(serialNos);

        Long number = batchCreateRequest.getNumber();
        if (serialNos.size() != number
                && StringUtils.isNotEmpty(batchCreateRequest.getSerialNo())
                && StringUtils.isNotEmpty(batchCreateRequest.getSerialNoEnd())) {
            throw new BusinessException(ApiCodes.TERMINAL_SN_NUMBER_NOT_MATCH);
        }
        for (int i = 0; i < number; i++) {
            TerminalStock terminal = BeanMapper.map(batchCreateRequest, TerminalStock.class);
            initTerminal(terminal, model);
            if (batchCreateRequest.getSerialNo() == null
                    || batchCreateRequest.getSerialNo().isEmpty()
                    || batchCreateRequest.getSerialNoEnd() == null
                    || batchCreateRequest.getSerialNoEnd().isEmpty()) {
                throw new BusinessException(ApiCodes.TERMINAL_SERIAL_NO_MANDATORY);
            }
            terminalListToSave.add(terminal);
        }
    }

    private void initTerminal(TerminalStock terminal, Model model) {
        terminal.setMarketId(-2L);
        terminal.setModel(model);
    }

    private void checkExistSns(List<String> serialNos) {
        TerminalStock existsTerminal;
        Market existsTerminalMarket;
        if (CollectionUtils.isNotEmpty(serialNos)) {
            //Can not exist in db:1.Global market. 2.Normal market and Global market no terminal stock management
            List<TerminalStock> existsTerminalSns = terminalStockService.getExistsTerminalDevicesStockSns(serialNos);
            if (existsTerminalSns != null && !existsTerminalSns.isEmpty()) {
                existsTerminal = existsTerminalSns.get(0);
                existsTerminalMarket = marketService.get(existsTerminal.getMarketId());
                if (existsTerminalMarket != null) {
                    throw new BusinessException(ApiCodes.TERMINAL_SN_ALREADY_EXIST_PARAM, null, existsTerminal.getSerialNo(), existsTerminalMarket.getName());
                } else {
                    throw new BusinessException(ApiCodes.TERMINAL_SN_ALREADY_EXIST, null, existsTerminal.getSerialNo());
                }

            }
        }
    }

}
