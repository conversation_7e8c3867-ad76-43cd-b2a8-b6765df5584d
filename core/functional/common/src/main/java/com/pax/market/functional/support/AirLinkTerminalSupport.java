package com.pax.market.functional.support;

import com.google.common.collect.Lists;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.airlink.*;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkEstate;
import com.pax.market.domain.entity.global.vas.airlink.MarketAirLinkSetting;
import com.pax.market.domain.entity.market.vas.airlink.*;
import com.pax.market.dto.vas.MarketAirLinkSubscriptionPlanInfo;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.mq.contract.airlink.AirLinkTerminalActiveHistoryDeductMessage;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.support.dynamic.datasource.aspectj.XATransactional;
import com.paxstore.global.domain.service.vas.airlink.AirLinkEstateService;
import com.paxstore.global.domain.service.vas.airlink.AirLinkOrderService;
import com.paxstore.global.domain.service.vas.airlink.MarketAirLinkSettingService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalActiveHistoryService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalMonthDeductDetailService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalService;
import com.paxstore.market.domain.service.airlink.AirLinkTerminalTaskService;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.vas.func.platform.VasPlatformFunc;
import com.zolon.saas.vas.func.platform.request.CardsRequest;
import com.zolon.saas.vas.func.platform.response.DeleteCardsResponse;
import com.zolon.saas.vas.func.platform.response.QueryCardProfilesResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * author mengxiaoxian
 * Date   2025/3/25 15:25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AirLinkTerminalSupport extends AbstractFunctionalService {

    private final ServiceCenterFunc serviceCenterFunc;
    private final AirLinkTerminalActiveHistoryService airLinkTerminalActiveHistoryService;
    private final AirLinkTerminalService airLinkTerminalService;
    private final AirLinkOrderService airLinkOrderService;
    private final AirLinkTerminalMonthDeductDetailService airLinkTerminalMonthDeductDetailService;
    private final AirLinkEstateService airLinkEstateService;
    private final AirLinkTerminalTaskService airLinkTerminalTaskService;
    private final VasPlatformFunc vasPlatformFunc;
    private final MarketAirLinkSettingService marketAirLinkSettingService;

    @XATransactional
    public boolean batchResumeAirLinkTerminal(Long currentMarketId, Date deductTime, List<AirLinkTerminal> airLinkTerminalList) {
        MarketAirLinkSetting marketAirLinkSetting = marketAirLinkSettingService.getByMarketId(currentMarketId);
        MarketAirLinkSubscriptionPlanInfo currentSubscriptionPlan = serviceCenterFunc.getSubscriptionPlanByPeriod(currentMarketId, null);
        List<AirLinkTerminalMonthDeductDetail> shouldMonthlyDeductTerminal = Lists.newArrayListWithExpectedSize(airLinkTerminalList.size());
        String period = DateUtils.formatDate(deductTime, DateUtils.PERIOD_DATE_FORMAT);
        if (Objects.nonNull(currentSubscriptionPlan) && Objects.nonNull(marketAirLinkSetting) && Objects.nonNull(marketAirLinkSetting.getChargeDate())) {
            // 查询那些终端当月没扣过包月费。没扣款的恢复需要扣款，并且记录扣款详情
            airLinkTerminalList.stream()
                    .filter(airLinkTerminal -> !DateUtils.isThisMonth(airLinkTerminal.getActiveTime()))
                    .filter(airLinkTerminal -> airLinkTerminalMonthDeductDetailService.notExist(airLinkTerminal.getId(),period))
                    .map(
                            airLinkTerminal -> {
                                AirLinkTerminalMonthDeductDetail airLinkTerminalMonthDeductDetail = new AirLinkTerminalMonthDeductDetail();
                                airLinkTerminalMonthDeductDetail.setAirLinkTerminalId(airLinkTerminal.getId());
                                airLinkTerminalMonthDeductDetail.setPeriod(period);
                                return airLinkTerminalMonthDeductDetail;
                            }
                    ).forEach(shouldMonthlyDeductTerminal::add);
        }
        List<AirLinkTerminal> airLinkTerminals = airLinkTerminalList.stream().filter(linkTerminal -> linkTerminal.getStatus() == AirLinkTerminalStatus.RESUMING).toList();
        if (CollectionUtils.isNotEmpty(airLinkTerminals)) {
            airLinkTerminalService.batchUpdate(airLinkTerminals, AirLinkTerminalStatus.RESUMING, AirLinkTerminalStatus.USE);
        }
        List<AirLinkTerminal> overdueList = airLinkTerminalList.stream().filter(linkTerminal -> linkTerminal.getOverdueStatus() == AirLinkTerminalOverdueStatus.RESUMING).toList();
        if (CollectionUtils.isNotEmpty(overdueList)) {
            airLinkTerminalService.batchUpdateOverdueStatus(overdueList, AirLinkTerminalOverdueStatus.RESUMING,AirLinkTerminalOverdueStatus.USE);
        }
        if (CollectionUtils.isNotEmpty(shouldMonthlyDeductTerminal) && Objects.nonNull(currentSubscriptionPlan)) {
            airLinkTerminalMonthDeductDetailService.batchCreateAirLinkTerminalDeductDetails(shouldMonthlyDeductTerminal);
            return airLinkOrderService.deductCurrentMonthlyFee(marketAirLinkSetting, currentSubscriptionPlan.getPackageFee(), period, (long) shouldMonthlyDeductTerminal.size(), AirLinkDeductType.RESUME);
        }
        return false;
    }

    @XATransactional
    public boolean deductActiveFee(AirLinkTerminalActiveHistory activeHistory, List<AirLinkTerminalMonthDeductDetail> terminalMonthDeductDetails,
                                   MarketAirLinkSetting airLinkSetting, MarketAirLinkSubscriptionPlanInfo subscriptionPlan,
                                   String period, boolean isExpire, boolean isRealActiveAll,
                                   AirLinkTerminalActiveHistoryDeductMessage message) {
        boolean isBalanceInsufficient = false;
        int deductTerminalNum = terminalMonthDeductDetails.size();
        if (CollectionUtils.isNotEmpty(terminalMonthDeductDetails)){
            //添加扣款订单
            isBalanceInsufficient = airLinkOrderService.deductTerminalActiveFee(airLinkSetting, subscriptionPlan.getPackageFee(),
                    subscriptionPlan.getActivationFee(), period, (long) deductTerminalNum, AirLinkDeductType.IMPORT);
            //添加扣款详情
            airLinkTerminalMonthDeductDetailService.batchCreateAirLinkTerminalDeductDetails(terminalMonthDeductDetails);
        }
        //全部激活/过期完成激活任务
        if ((isRealActiveAll || isExpire || message.getCloseActiveHistory()) && !AirLinkTerminalActiveHistoryStatus.COMPLETED.equals(activeHistory.getStatus())){
            completeActiveHistory(activeHistory, deductTerminalNum);
        }
        //如果是扣除的上月激活费用,将上月扣除标识置为true
        if (message.getDeductLastMonth()){
            successLastMonthBilled(activeHistory, deductTerminalNum);
        }
        return isBalanceInsufficient;
    }

    @XATransactional
    public boolean deductMonthlyFee(MarketAirLinkSetting marketAirLinkSetting, BigDecimal price, String period,
                                    List<AirLinkTerminalMonthDeductDetail> terminalDeductDetails, AirLinkDeductType deductType){
        boolean isBalanceInsufficient = airLinkOrderService.deductCurrentMonthlyFee(marketAirLinkSetting, price, period,
                (long) terminalDeductDetails.size(), deductType);
        airLinkTerminalMonthDeductDetailService.batchCreateAirLinkTerminalDeductDetails(terminalDeductDetails);
        return isBalanceInsufficient;
    }

    @XATransactional
    public Long batchCreateAirLinkTerminal(List<AirLinkTerminal> saveTerminals, BigDecimal occupationFee, BigDecimal terminalFee,
                                           boolean isBatch, List<AirLinkEstate> airLinkEstateList){
        Long activeHistoryId = airLinkTerminalService.batchCreateAirLinkTerminals(saveTerminals, occupationFee, terminalFee, isBatch);
        airLinkEstateService.batchUpdateStatus(airLinkEstateList);
        return activeHistoryId;
    }

    @XATransactional
    public void cancelActiveTerminal(List<AirLinkTerminal> airLinkTerminalList, List<AirLinkEstate> airLinkEstates,
                                     List<AirLinkTerminalTask> airLinkTerminalTasks, List<String> cancelSuccessImeiList, Long activeHistoryId){
        //修改终端状态，更新激活失败个数
        airLinkTerminalService.cancelActiveTerminal(airLinkTerminalList, activeHistoryId, airLinkTerminalList.size());
        //取消激活后，修改资产状态
        if (CollectionUtils.isNotEmpty(airLinkEstates)){
            airLinkEstateService.batchUpdateStatus(airLinkEstates);
        }
        //取消成功的imei可能一直没有激活成功，还存在task表中，需要删除
        if (CollectionUtils.isNotEmpty(cancelSuccessImeiList)){
            airLinkTerminalTaskService.batchDeleteTask(new ArrayList<>(cancelSuccessImeiList));
        }
        //取消失败的imei需加入任务，等待再次执行
        if (CollectionUtils.isNotEmpty(airLinkTerminalTasks)){
            airLinkTerminalTaskService.batchCreateTask(airLinkTerminalTasks);
        }
    }

    @XATransactional
    public void deleteMarketAirLinkTerminal(Long marketId){
        airLinkTerminalService.updateByMarketOrReseller(marketId, null);
        airLinkEstateService.deleteByMarketId(marketId);
    }

    @XATransactional
    public void deleteAirLinkTerminal(Long marketId,List<AirLinkTerminal> airLinkTerminalList){
        if (CollectionUtils.isNotEmpty(airLinkTerminalList)){
            airLinkTerminalService.batchDeleteAirLinkTerminal(airLinkTerminalList);
            airLinkEstateService.batchUpdateStatus(marketId, airLinkTerminalList.stream().map(AirLinkTerminal::getImei).toList());
        }
    }

    @XATransactional
    public void deleteResellerTerminal(Long marketId, Long resellerId, List<AirLinkEstate> airLinkEstates){
        airLinkTerminalService.updateByMarketOrReseller(marketId, resellerId);
        if (CollectionUtils.isNotEmpty(airLinkEstates)){
            airLinkEstateService.batchUpdateStatus(airLinkEstates);
        }
    }

    @XATransactional
    public void updateTerminalImportResult(List<AirLinkTerminal> airLinkTerminalList, List<Long> estateIdList){
        airLinkTerminalService.update2PendingStatusById(airLinkTerminalList);
        if (CollectionUtils.isNotEmpty(estateIdList)){
            airLinkEstateService.batchStock(estateIdList);
        }
    }

    public List<AirLinkTerminalProfile> getCardProfiles(Long airLinkTerminalId, String imei){
        QueryCardProfilesResponse queryCardProfilesResponse;
        try {
            queryCardProfilesResponse = vasPlatformFunc.getCardProfiles(imei).getData();
            if (Objects.nonNull(queryCardProfilesResponse)) {
                List<AirLinkTerminalProfile> resultProfiles = new ArrayList<>();
                for (QueryCardProfilesResponse.InstalledProfile installedProfile : queryCardProfilesResponse.getInstalledProfiles()) {
                    AirLinkTerminalProfile airLinkTerminalProfile = new AirLinkTerminalProfile();
                    airLinkTerminalProfile.setAirlinkTerminalId(airLinkTerminalId);
                    airLinkTerminalProfile.setIccid(installedProfile.getIccid());
                    airLinkTerminalProfile.setMsisdn(installedProfile.getMsisdn());
                    airLinkTerminalProfile.setOperator(installedProfile.getOperator());
                    airLinkTerminalProfile.setStatus(installedProfile.getStatus().equals(AirLinkConstants.CARD_ENABLED_STATUS) ? AirLinkTerminalProfileStatus.USE : AirLinkTerminalProfileStatus.NOT_USE);
                    airLinkTerminalProfile.setCreatedDate(new Date());
                    resultProfiles.add(airLinkTerminalProfile);
                }
                return resultProfiles;
            }
        }catch (Exception e){
            log.error("call red tea get card profile api failure!", e);
        }
        return Collections.emptyList();
    }

    public List<String> deleteCards(List<String> imeis){
        List<String> cancelFailImeiList = new ArrayList<>();
        CardsRequest request = new CardsRequest();
        request.setImeiList(imeis);
        try {
            SingleResponse<DeleteCardsResponse> response = vasPlatformFunc.deleteCards(request);
            if (response.isSuccess()){
                List<DeleteCardsResponse.DeleteCardsResultDetails> results = response.getData().getResults();
                for (DeleteCardsResponse.DeleteCardsResultDetails detail : results){
                    if (!AirLinkConstants.AIRLINK_DELETE_CARD_SUCCESS_CODES.contains(detail.getStatus())){
                        cancelFailImeiList.add(detail.getImei());
                    }
                }
            }
        }catch (Exception e){
            log.warn("call redtea delete card api failure", e);
            cancelFailImeiList.addAll(imeis);
        }
        return cancelFailImeiList;
    }


    public BigDecimal getOccupationFee(MarketAirLinkSetting airLinkSetting, BigDecimal terminalFee, int terminalCount){
        Long marketId = airLinkSetting.getMarketId();
        BigDecimal totalFee = BigDecimal.valueOf(terminalCount).multiply(terminalFee);
        BigDecimal marketTotalOccupationFee = airLinkTerminalActiveHistoryService.getMarketTotalOccupationFee(marketId);
        if (airLinkSetting.getTrialBalance().add(airLinkSetting.getBalance()).subtract(marketTotalOccupationFee).compareTo(totalFee) < 0){
            throw new BusinessException(ApiCodes.AIRLINK_BALANCE_INSUFFICIENT);
        }
        return totalFee;
    }

    public boolean imeiIsIncorrent(String imei){
        return !StringUtils.matches(imei, AirLinkConstants.IMEI_REG_EXPR);
    }

    public List<AirLinkTerminalMonthDeductDetail> getTerminalDeductDetails(List<Long> terminalIds, String period){
        List<AirLinkTerminalMonthDeductDetail> deductDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(terminalIds)){
            AirLinkTerminalMonthDeductDetail deductDetail;
            for (Long airlinkTerminalId : terminalIds){
                deductDetail = new AirLinkTerminalMonthDeductDetail();
                deductDetail.setAirLinkTerminalId(airlinkTerminalId);
                deductDetail.setPeriod(period);
                deductDetailList.add(deductDetail);
            }
        }
        return deductDetailList;
    }

    public List<AirLinkEstate> getAirLinkEstates(Long marketId, List<String> imeis, AirLinkEstateStatus status){
        List<AirLinkEstate> batchList = new ArrayList<>();
        Date currentDate = new Date();
        if (CollectionUtils.isNotEmpty(imeis)) {
            for (String imei : imeis) {
                AirLinkEstate estate = new AirLinkEstate();
                estate.setMarketId(marketId);
                estate.setImei(imei);
                estate.setStatus(status);
                estate.setUpdatedDate(currentDate);
                batchList.add(estate);
            }
        }
        return batchList;
    }

    public List<AirLinkTerminalTask> getAirLinkTerminalTasks(Long marketId, Long resellerId,
                                                             String type, Set<String> imeis){
        List<AirLinkTerminalTask> saveList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(imeis)){
            for (String imei : imeis){
                AirLinkTerminalTask task = new AirLinkTerminalTask();
                task.setMarketId(marketId);
                task.setResellerId(resellerId);
                task.setImei(imei);
                task.setType(type);
                saveList.add(task);
            }
        }
        return saveList;
    }

    private void completeActiveHistory(AirLinkTerminalActiveHistory activeHistory, int activeTerminalNum){
        int deductNum;
        BigDecimal occupationFee = BigDecimal.ZERO;
        if (activeHistory.isLastMonthBilled()){
            deductNum = activeHistory.getActiveNum();
        }else{
            deductNum = activeTerminalNum;
            occupationFee = activeHistory.getTerminalFee().multiply(new BigDecimal(activeHistory.getActiveNum() - activeTerminalNum));
        }
        activeHistory.setStatus(AirLinkTerminalActiveHistoryStatus.COMPLETED);
        activeHistory.setCompletedTime(new Date());
        activeHistory.setDeductNum(deductNum);
        activeHistory.setOccupationFee(occupationFee);
        airLinkTerminalActiveHistoryService.completeActiveHistory(activeHistory);
    }

    private void successLastMonthBilled(AirLinkTerminalActiveHistory activeHistory, int activeTerminalNum){
        int deductNum;
        BigDecimal occupationFee = BigDecimal.ZERO;
        if (AirLinkTerminalActiveHistoryStatus.COMPLETED == activeHistory.getStatus()){
            deductNum = activeHistory.getActiveNum();
        }else{
            deductNum = activeTerminalNum;
            int occupationNum = activeHistory.getActiveNum() - activeTerminalNum + activeHistory.getPendingNum();
            occupationFee = activeHistory.getTerminalFee().multiply(new BigDecimal(occupationNum));
        }
        activeHistory.setDeductNum(deductNum);
        activeHistory.setLastMonthBilled(true);
        activeHistory.setOccupationFee(occupationFee);
        airLinkTerminalActiveHistoryService.successLastMonthBilled(activeHistory);
    }
}
