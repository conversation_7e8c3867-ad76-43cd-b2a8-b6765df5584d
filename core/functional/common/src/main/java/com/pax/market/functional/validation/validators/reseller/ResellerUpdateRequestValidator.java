/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.functional.validation.validators.reseller;

import com.pax.market.constants.ApiCodes;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.request.organization.ResellerUpdateRequest;
import com.pax.market.framework.common.utils.StringUtils;

/**
 * Created by liukai on 2016/10/19.
 */
public class ResellerUpdateRequestValidator extends Validator<ResellerUpdateRequest> {

    /**
     * Instantiates a new Reseller update request validator.
     *
     * @param resellerUpdateRequest the reseller update request
     */
    public ResellerUpdateRequestValidator(ResellerUpdateRequest resellerUpdateRequest) {
        super(resellerUpdateRequest);
    }

    @Override
    public boolean validate() {

        if (StringUtils.isEmpty(validateTarget.getName())) {
            throw new BusinessException(ApiCodes.RESELLER_NAME_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getPhone())) {
            throw new BusinessException(ApiCodes.RESELLER_PHONE_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getCountry())) {
            throw new BusinessException(ApiCodes.FACTORY_COUNTRY_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getContact())) {
            throw new BusinessException(ApiCodes.RESELLER_CONTACT_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getEmail())) {
            throw new BusinessException(ApiCodes.RESELLER_EMAIL_MANDATORY);
        }

        if (validateTarget.getName().trim().length() > 64) {
            throw new BusinessException(ApiCodes.RESELLER_NAME_TOO_LONG);
        }

        if (validateTarget.getPhone().trim().length() > 32) {
            throw new BusinessException(ApiCodes.RESELLER_PHONE_TOO_LONG);
        }

        if (validateTarget.getContact().trim().length() > 64) {
            throw new BusinessException(ApiCodes.RESELLER_CONTACT_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getPostcode()) && validateTarget.getPostcode().trim().length() > 16) {
            throw new BusinessException(ApiCodes.POSTCODE_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getCity()) && validateTarget.getCity().trim().length() > 255) {
            throw new BusinessException(ApiCodes.FACTORY_CITY_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getAddress()) && validateTarget.getAddress().trim().length() > 255) {
            throw new BusinessException(ApiCodes.ADDRESS_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getCompany()) && validateTarget.getCompany().trim().length() > 255) {
            throw new BusinessException(ApiCodes.RESELLER_COMPANY_TOO_LONG);
        }

        if (validateTarget.getEmail().trim().length() > 255) {
            throw new BusinessException(ApiCodes.RESELLER_EMAIL_TOO_LONG);
        }

        if (!StringUtils.isValidEmailAddress(validateTarget.getEmail())) {
            throw new BusinessException(ApiCodes.USER_EMAIL_INVALID);
        }

        if (StringUtils.isInvalidPhoneNumber(validateTarget.getPhone())) {
            throw new BusinessException(ApiCodes.PHONE_INVALID);
        }

        if (StringUtils.isInvalidResellerName(validateTarget.getName())) {
            throw new BusinessException(ApiCodes.ENTITY_NAME_INVALID);
        }

        return true;
    }
}
