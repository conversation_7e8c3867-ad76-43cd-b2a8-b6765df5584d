/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: PzIniParser.
 *
 * Revision History:
 * Date	                	Author	            	Action
 * 2019/7/3  	        	rongsheng          		Create
 * ===========================================================================================
 */
package com.pax.market.functional.app.parser.pz;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Data
@EqualsAndHashCode
public abstract class SystemIni implements Serializable {
    private static final long serialVersionUID = -7825263394606400328L;

    protected OS os;
    protected String name;
    protected String version;
    private Map<String, List<String>> paramTemplateFileMap;

    public int getTotalTaskSize() {
        return countElements(os.getOsFile());
    }

    public static SystemIni newInstance(OSType osType) {
        switch (osType) {
            case MONITOR:
                return new SystemIni.MonitorIni();
            case PROLIN:
                return new SystemIni.ProlinIni();
            case RUNTHOS:
                return new SystemIni.RunthosIni();
            case NONE:
            default:
                return null;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProlinIni extends SystemIni {
        private static final long serialVersionUID = 7600307035751106898L;
        private List<String> uspuks;
        private List<String> opts;
        private List<String> fwps;
        private List<String> aips;
        private List<String> aups;
        private Map<String, List<String>> appDataFileMap;

        @Override
        public int getTotalTaskSize() {
            return super.getTotalTaskSize() + SystemIni.countElements(uspuks, opts, fwps, aips, aups, appDataFileMap);
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class MonitorIni extends SystemIni {
        private static final long serialVersionUID = 2096777792368605973L;
        private List<String> fontLibs;
        private String uspuk;
        private String uapuk;
        private String baseDriver;
        private String baseDriverName;
        private String baseDriverVersion;
        private List<String> publicFiles;
        private List<MonitorApp> apps;

        @Override
        public int getTotalTaskSize() {
            int total = super.getTotalTaskSize() + countElements(fontLibs, uspuk, uapuk, baseDriver, publicFiles);
            if (!CollectionUtils.isEmpty(apps)) {
                total += apps.stream().mapToInt(MonitorApp::size).sum();
            }
            return total;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class RunthosIni extends MonitorIni {
        @Override
        public int getTotalTaskSize() {
            int total = countElements(getOs().getOsFile(), getFontLibs(), getUspuk(), getUapuk(), getBaseDriver(), getPublicFiles());
            if (!CollectionUtils.isEmpty(getApps())) {
                total += getApps().stream().mapToInt(app -> {
                    int size = 0;
                    if (nonNull(app.getBinFile())) {
                        size += 1;
                    }
                    if (nonNull(app.getAppDataFiles())) {
                        size += app.getAppDataFiles().size();
                    }
                    return size;
                }).sum();
            }
            return total;
        }
    }

    private static int countElements(Object... objects) {
        int total = 0;
        for (Object obj : objects) {
            if (isNull(obj)) {
                continue;
            }
            if (obj instanceof Collection) {
                total += ((Collection<?>) obj).size();
            } else if (obj instanceof Map) {
                total += ((Map<?, ?>) obj).size();
            } else {
                total += 1;
            }
        }
        return total;
    }

    @Data
    @EqualsAndHashCode
    public static class MonitorApp implements Serializable {
        private static final long serialVersionUID = 3590663029100088525L;
        private String binFile;
        private String appName;
        private String appVersion;
        private List<String> appDataFiles;
        private String comment;

        public int size() {
            int total = 0;
            if (nonNull(binFile)) {
                total += 1;
            }
            if (nonNull(appDataFiles)) {
                total += 1;
            }
            return total;
        }
    }

    @Data
    @EqualsAndHashCode
    public static class OS implements Serializable {
        private static final long serialVersionUID = -4886145843312844829L;
        private OSType osType;
        private String iniVersion;
        private String osFile;
        private String osVersion;
    }

    public enum OSType {
        MONITOR("0"),
        PROLIN("1"),
        ANDROID("2"),
        RUNTHOS("3"),
        NONE("null");

        private final String value;

        OSType(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public static OSType parse(String value) {
            for (OSType t : values()) {
                if (t.getValue().equals(value)) {
                    return t;
                }
            }
            return NONE;
        }
    }
}