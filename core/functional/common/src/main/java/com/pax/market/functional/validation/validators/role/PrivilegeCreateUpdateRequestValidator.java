/*
 * *******************************************************************************
 * COPYRIGHT      
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION     
 *   This software is supplied under the terms of a license agreement or      
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied     
 *   or disclosed except in accordance with the terms in that agreement.
 *         
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.role;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.PrivilegeType;
import com.pax.market.domain.entity.global.role.Privilege;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.request.role.PrivilegeCreateUpdateRequest;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.paxstore.global.domain.service.role.PrivilegeService;

/**
 * The type Privilege create request validator.
 */
public class PrivilegeCreateUpdateRequestValidator extends Validator<PrivilegeCreateUpdateRequest> {
    private Privilege privilege;

    /**
     * Instantiates a new Privilege create or update request validator.
     *
     * @param privilegeCreateUpdateRequest the privilege create or update request
     * @param privilege                    the privilege
     */
    public PrivilegeCreateUpdateRequestValidator(PrivilegeCreateUpdateRequest privilegeCreateUpdateRequest, Privilege privilege) {
        super(privilegeCreateUpdateRequest);
        this.privilege = privilege;
    }

    @Override
    public boolean validate() {
        PrivilegeService privilegeService = SpringContextHolder.getBean(PrivilegeService.class);

        if (StringUtils.isBlank(validateTarget.getCode())) {
            throw new BusinessException(ApiCodes.PRIVILEGE_CODE_MANDATORY);
        }

        if (StringUtils.isBlank(validateTarget.getType())) {
            throw new BusinessException(ApiCodes.PRIVILEGE_TYPE_MANDATORY);
        }

        if (StringUtils.isBlank(validateTarget.getName())) {
            throw new BusinessException(ApiCodes.PRIVILEGE_NAME_MANDATORY);
        }

        if (validateTarget.getName().trim().length() > 64) {
            throw new BusinessException(ApiCodes.PRIVILEGE_NAME_TOO_LONG);
        }

        if (!StringUtils.equals(validateTarget.getType().trim(), PrivilegeType.FUNCTION)
                && !StringUtils.equals(validateTarget.getType().trim(), PrivilegeType.MENU)) {
            throw new BusinessException(ApiCodes.PRIVILEGE_TYPE_INVALID);
        }

        if (validateTarget.getCode().trim().length() > 32) {
            throw new BusinessException(ApiCodes.PRIVILEGE_CODE_TOO_LONG);
        }

        if (validateTarget.getHref() != null && validateTarget.getHref().trim().length() > 255) {
            throw new BusinessException(ApiCodes.PRIVILEGE_HREF_TOO_LONG);
        }

        if (validateTarget.getStyle() != null && validateTarget.getStyle().trim().length() > 255) {
            throw new BusinessException(ApiCodes.PRIVILEGE_STYLE_TOO_LONG);
        }

        if (validateTarget.getTitle() != null && validateTarget.getTitle().trim().length() > 64) {
            throw new BusinessException(ApiCodes.PRIVILEGE_TITLE_TOO_LONG);
        }

        if (validateTarget.getRemarks() != null && validateTarget.getRemarks().trim().length() > 255) {
            throw new BusinessException(ApiCodes.PRIVILEGE_REMARKS_TOO_LONG);
        }

        if ((privilege == null || !StringUtils.equals(privilege.getCode(), validateTarget.getCode())) &&
                privilegeService.getByCode(validateTarget.getCode()) != null) {
            throw new BusinessException(ApiCodes.PRIVILEGE_CODE_EXIST);
        }

        if ((privilege == null || !LongUtils.equals(privilege.getParentId(), validateTarget.getParentId()) || !StringUtils.equals(privilege.getName(), validateTarget.getName())) &&
                privilegeService.getByName(validateTarget.getParentId(), validateTarget.getName()) != null) {
            throw new BusinessException(ApiCodes.PRIVILEGE_NAME_EXIST);
        }

        return false;
    }

}
