package com.pax.market.functional.vas.usage;

import com.google.common.collect.Lists;
import com.pax.core.json.JsonMapper;
import com.pax.core.utils.LongUtils;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.constants.ActivityType;
import com.pax.market.constants.DownloadTaskStatus;
import com.pax.market.constants.DownloadTaskType;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.excel.vas.AirShieldUsageForGlobalExport;
import com.pax.market.domain.entity.excel.vas.AirShieldUsageForMarketExport;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.billing.ServiceUsageDetailInfo;
import com.pax.market.dto.request.activity.AirShieldUsageExportRequest;
import com.pax.market.dto.request.vas.VasUsageSummaryRequest;
import com.pax.market.dto.vas.AirShieldUsageInfo;
import com.pax.market.dto.vas.VasExportInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.FileUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.activity.exports.BaseExportService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.terminal.TerminalAirShieldUsageService;
import com.paxstore.market.domain.service.DownloadTaskService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AirShieldUsageSummaryService extends BaseExportService<AirShieldUsageExportRequest> implements VasUsageSummaryService<AirShieldUsageInfo> {
    private final TerminalAirShieldUsageService terminalAirShieldUsageService;

    private final DownloadTaskService downloadTaskService;

    private final MarketService marketService;
    private final ResellerService resellerService;

    private final BillingUsageService billingUsageService;

    @Override
    public String serviceType() {
        return AIR_SHIELD;
    }

    @Override
    public AirShieldUsageInfo buildDashboardUsage(VasUsageSummaryRequest request) {
        String period = DateUtils.formatDate(request.getStartDate(), DateUtils.PERIOD_DATE_FORMAT);
        AirShieldUsageInfo usageInfo = new AirShieldUsageInfo();
        String marketIds = null;
        Long marketId = request.getMarketId();
        if (LongUtils.equals(SystemConstants.SUPER_MARKET_ID, getCurrentMarketId())) {
            List<Long> availableMarketIds = findCurrentMonthAvailableMarketIds();
            if (CollectionUtils.isEmpty(availableMarketIds)) {
                return usageInfo;
            }
            marketId = SystemConstants.SUPER_MARKET_ID;
            marketIds = String.join(",", Optional.of(availableMarketIds)
                    .orElseGet(org.apache.commons.compress.utils.Lists::newArrayList)
                    .stream()
                    .map(String::valueOf)
                    .toList());
        }
        usageInfo.setAmount(billingUsageService.getTotalUsage(marketId, marketIds, serviceType(), period));
        return usageInfo;
    }

    @Override
    public List<AirShieldUsageInfo> buildHistoryUsage(Page<AirShieldUsageInfo> page, VasUsageSummaryRequest request) {
        String period = request.getPeriod();
        String name = request.getName();
        Long marketId = getCurrentMarketId();
        Long resellerId = request.getResellerId();
        boolean includeSubReseller = request.isIncludeSubReseller();
        if (StringUtils.isBlank(period)) {
            return terminalAirShieldUsageService.findHistoryUsageByPeriod(page, marketId, period);
        }
        if (LongUtils.equals(marketId, SystemConstants.SUPER_MARKET_ID)) {
            appendOrderByParam(page, MARKET_ID);
            return terminalAirShieldUsageService.findMarketHistoryUsage(page, period, name);
        }

        return this.findMonthUsage(page, marketId, resellerId, period, includeSubReseller, name, false);
    }

    public List<AirShieldUsageInfo> findMonthUsage(Page<AirShieldUsageInfo> page, Long marketId,
                                                   Long resellerId, String period, boolean includeSubReseller,
                                                   String name, boolean isCurrentMonth) {
        List<Long> marketIds = null;
        String resellerIds = null;
        if (StringUtils.isNotBlank(name)) {
            if (LongUtils.equals(marketId, SystemConstants.SUPER_MARKET_ID)) {
                marketIds = marketService.findMarketIdsByName(name);
                if (CollectionUtils.isEmpty(marketIds)) {
                    return Lists.newArrayList();
                }
            } else {
                resellerIds = resellerService.findResellerIdsByName(name, resellerId, marketId).stream().map(String::valueOf).collect(Collectors.joining(","));
                if (StringUtils.isBlank(resellerIds)) {
                    return List.of();
                }
            }
        }
        if (isCurrentMonth) {
            if (LongUtils.equals(marketId, SystemConstants.SUPER_MARKET_ID)) {
                if (CollectionUtils.isEmpty(marketIds)) {
                    marketIds = findCurrentMonthAvailableMarketIds();
                } else {
                    marketIds.retainAll(findCurrentMonthAvailableMarketIds());
                }
                if (CollectionUtils.isEmpty(marketIds)) {
                    return List.of();
                }
            } else if (!isCurrentMonthAvailable(marketId)) {
                return List.of();
            }
        }

        PageInfo<ServiceUsageDetailInfo> serviceUsageDetailPage = billingUsageService.findServiceUsageDetailPageV2(
                serviceType(), marketId, resellerId, resellerIds,
                period, includeSubReseller, true, null, null,
                String.join(",", Optional.ofNullable(marketIds).orElseGet(Lists::newArrayList).stream().map(String::valueOf).toList()), page);
        page.setCount(serviceUsageDetailPage.getTotalCount());
        return BeanMapper.mapList(getServiceUsageDetailInfos(serviceUsageDetailPage, isCurrentMonth), AirShieldUsageInfo.class);
    }


    @Override
    public List<AirShieldUsageInfo> buildCurrentMonthUsage(Page<AirShieldUsageInfo> page, VasUsageSummaryRequest request) {
        String period = DateUtils.formatDate(request.getStartDate(), DateUtils.PERIOD_DATE_FORMAT);
        String name = request.getName();
        Long marketId = getCurrentMarketId();
        Long resellerId = request.getResellerId();
        boolean includeSubReseller = request.isIncludeSubReseller();
        return findMonthUsage(page, marketId, resellerId, period, includeSubReseller, name, true);
    }

    @Override
    public void loadCurrentUsageForExport(VasUsageSummaryRequest request, VasExportInfo vasExportInfo) {
        Long resellerId = request.getResellerId();
        if (Objects.isNull(resellerId)) {
            BeanMapper.copy(exportHistoryUsage(request), vasExportInfo);
        }
    }

    @Override
    public VasExportInfo exportHistoryUsage(VasUsageSummaryRequest request) {
        String fileId = this.getAirShieldFileId(request.getMarketId(), request.getPeriod());

        if (StringUtils.isBlank(fileId)) {
            AirShieldUsageExportRequest exportRequest = new AirShieldUsageExportRequest();
            exportRequest.setMarketId(request.getMarketId());
            exportRequest.setPeriod(request.getPeriod());
            exportRequest.setGlobalOperation(LongUtils.equals(SystemConstants.SUPER_MARKET_ID, getCurrentMarketId()));
            Activity activity = this.exportData(exportRequest, request.getTimeZone());
            return new VasExportInfo(ImportExportActivityInfo.builder()
                    .id(activity.getId())
                    .status(activity.getStatus())
                    .resultMessage(activity.getResultMessage())
                    .build());
        }
        String excelFileName = FileUtils.generateFileNameWithDateSuffix(String.format("%s_%s_%s.xlsx",
                        getLocaleMessageForExport("label.airshield"),
                        getLocaleMessageForExport("excel.usage.details"),
                        formatPeriod(request.getPeriod()).replace('/', '_')),
                request.getTimeZone());
        return new VasExportInfo(downloadTaskService.createDownloadTask(
                excelFileName,
                fileId,
                DownloadTaskStatus.PENDING,
                false,
                DownloadTaskType.AIRSHIELD_USAGE_REPORT).getId());
    }

    public void writeDataInExcel(Long marketId, String period, Consumer<List<?>> consumer) {
        List<Terminal> terminals;
        Long terminalId = null;
        do {
            terminals = billingUsageService.findServiceUsageForExport(null, null, marketId, serviceType(), period, terminalId);
            if (CollectionUtils.isNotEmpty(terminals)) {
                consumer.accept(terminals);
                terminalId = terminals.get(terminals.size() - 1).getId();
            }
        } while (CollectionUtils.isNotEmpty(terminals));
    }

    private String getAirShieldFileId(Long marketId, String period) {
        if (StringUtils.isBlank(period)) {
            return "";
        }
        return terminalAirShieldUsageService.getAirShieldFileId(marketId, period, LongUtils.equals(SystemConstants.SUPER_MARKET_ID, getCurrentMarketId()));
    }

    private List<ServiceUsageDetailInfo> getServiceUsageDetailInfos(PageInfo<ServiceUsageDetailInfo> serviceUsageDetailPage, boolean isCurrentMonth) {
        List<ServiceUsageDetailInfo> list = serviceUsageDetailPage.getList();
        list.forEach(detail -> {
            if (detail.getResellerId() != null) {
                Reseller reseller = resellerService.getIncludeDeletedWithoutDataScopeCheck(detail.getResellerId());
                if (reseller != null) {
                    detail.setResellerName(reseller.getName());
                }

            } else if (detail.getMarketId() != null) {
                Market market = marketService.getWithDeleted(detail.getMarketId());
                if (market != null) {
                    detail.setMarketName(market.getName());
                    detail.setCountry(market.getCountry());
                    detail.setRegion(market.getRegion());
                }
            }
        });
        return list;
    }

    @Override
    public void exportData(Activity activity) {
        AirShieldUsageExportRequest exportRequest = JsonMapper.fromJsonString(activity.getRequestData(), AirShieldUsageExportRequest.class);
        Date startDate = DateUtils.parseDate(exportRequest.getPeriod());
        Long marketId = exportRequest.getMarketId();
        Market market = marketService.getWithDeleted(marketId);
        if (Objects.isNull(market)) {
            return;
        }

        String label = getLocaleMessageForExport("label.airshield");
        String excelTitle = String.format("%s %s %s - %s/%s", market.getName(),
                getLocaleMessageForExport("label.airshield"),
                getLocaleMessageForExport("title.usage.details"),
                DateUtils.getMonth(startDate), DateUtils.getYear(startDate));
        String excelFileName = FileUtils.generateFileNameWithDateSuffix(String.format("%s_%s_%s.xlsx", label,
                        getLocaleMessageForExport("excel.usage.details"),
                        formatPeriod(exportRequest.getPeriod()).replace('/', '_')),
                activity.getTimezone());

        String sheetName = label + " " + getLocaleMessageForExport("title.usage.details");


        this.doExport(activity, setDataList -> writeDataInExcel(marketId, exportRequest.getPeriod(), setDataList),
                exportRequest.isGlobalOperation() ? AirShieldUsageForGlobalExport.class : AirShieldUsageForMarketExport.class,
                excelTitle,
                sheetName,
                excelFileName
        );
    }

    @Override
    public String getActivityType() {
        return ActivityType.EXPORT_AIRSHIELD_USAGE;
    }

    @Override
    public String getActivityName(AirShieldUsageExportRequest request) {
        return String.format("%s %s(%d)", getLocaleMessageForExport("label.airshield"),
                getLocaleMessageForExport("title.usage.details"),
                this.getTotalCount(request));
    }

    @Override
    public int getTotalCount(AirShieldUsageExportRequest request) {
        Long marketId = request.getMarketId();
        if (request.isCurrentMonth() && !isCurrentMonthAvailable(marketId)) {
            return 0;
        }
        return (int) billingUsageService.getTotalCount4Export(marketId, serviceType(), request.getPeriod());
    }

    @Override
    public int getExportLimit() {
        return 0;
    }

    public List<AirShieldUsageInfo> findFileList(String period) {
        return terminalAirShieldUsageService.findFileList(period);
    }
}
