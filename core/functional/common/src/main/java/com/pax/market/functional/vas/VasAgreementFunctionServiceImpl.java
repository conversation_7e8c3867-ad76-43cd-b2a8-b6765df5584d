package com.pax.market.functional.vas;

import com.google.common.collect.Lists;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.VasAgreementType;
import com.pax.market.domain.entity.global.agreement.VasAgreement;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.DeveloperInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.request.activity.VasAgreementExportRequest;
import com.pax.market.dto.request.vas.VasAgreementRequest;
import com.pax.market.dto.vas.VasAgreementInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.activity.exports.VasAgreementExportService;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.vas.validator.MarketVasAgreementValidator;
import com.pax.market.vo.developer.user.VasAgreementDetailVo;
import com.pax.vas.common.VasConstants;
import com.paxstore.global.domain.service.agreement.VasAgreementService;
import com.paxstore.global.domain.service.app.AppVasSupportService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.global.domain.service.vas.VasInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@FunctionalService
public class VasAgreementFunctionServiceImpl extends AbstractFunctionalService implements VasAgreementFunctionService {

    private final VasInfoService vasInfoService;
    private final VasAgreementService vasAgreementService;
    private final UserService userService;
    private final VasAgreementExportService vasAgreementExportService;
    private final AppVasSupportService appVasSupportService;
    private final VasRemoteInvokeFunc vasRemoteInvokeFunc;

    public VasAgreementFunctionServiceImpl(VasInfoService vasInfoService, VasAgreementService vasAgreementService,
                                           UserService userService, VasAgreementExportService vasAgreementExportService,
                                           AppVasSupportService appVasSupportService, @Lazy VasRemoteInvokeFunc vasRemoteInvokeFunc) {
        this.vasInfoService = vasInfoService;
        this.vasAgreementService = vasAgreementService;
        this.userService = userService;
        this.vasAgreementExportService = vasAgreementExportService;
        this.appVasSupportService = appVasSupportService;
        this.vasRemoteInvokeFunc = vasRemoteInvokeFunc;
    }


    @Override
    public ImportExportActivityInfo downloadServiceAgreement(VasAgreementRequest request) {
        if (!SystemPropertyHelper.getShowServiceAgreement()) {
            throw new BusinessException(ApiCodes.BAD_REQUEST);
        }
        VasAgreement vasAgreement = vasAgreementService.get(request.getAgreementId());
        if (vasAgreement == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        VasInfo vasInfo = vasInfoService.getByServiceType(vasAgreement.getServiceType());
        VasAgreementExportRequest exportRequest = new VasAgreementExportRequest();
        exportRequest.setAgreementId(request.getAgreementId());
        exportRequest.setTotalCount(request.getTotalCount());
        exportRequest.setServiceName(vasInfo.getServiceName());
        exportRequest.setName(vasAgreement.getName());
        exportRequest.setServiceType(vasAgreement.getServiceType());
        exportRequest.setType(vasAgreement.getType());
        Activity activity = vasAgreementExportService.exportData(exportRequest, request.getTimeZone());
        return ImportExportActivityInfo.builder()
                .id(activity.getId())
                .status(activity.getStatus())
                .resultMessage(activity.getResultMessage())
                .build();
    }

    @Override
    //FIXME Albert.Zuo
    public void agreeServiceAgreement(Long agreementId, String serviceType, String type) {
        Long currentMarketId = getCurrentMarketId();
        Long currentDeveloperId = getCurrentUser().getCurrentDeveloperId();
        if (StringUtils.equals(type, VasAgreementType.MARKET)) {
            addValidator(new MarketVasAgreementValidator(currentMarketId, serviceType, type)).validate();
        } else {
            if (StringUtils.equals(type, VasAgreementType.DEVELOPER) && !vasRemoteInvokeFunc.isEndpointAvailable(serviceType,currentMarketId, currentDeveloperId )) {
                throw new BusinessException(ApiCodes.DEV_SERVICE_NOT_ENABLED);
            }
            Validators.validate(new MarketVasAgreementValidator(currentMarketId, serviceType, type));
            DeveloperInfo currentDeveloper = getCurrentUser().getCurrentDeveloper();
            if (!Boolean.TRUE.equals(currentDeveloper.getOwner()) && !Boolean.TRUE.equals(currentDeveloper.getAdmin())) {
                throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
            }
        }
        vasAgreementService.updateAgreedVasAgreement(agreementId, serviceType, type, StringUtils.equals(type, VasAgreementType.MARKET) ? currentMarketId : currentDeveloperId);
    }

    @Override
    //FIXME Albert.Zuo
    public VasAgreementInfo getServiceAgreement(String serviceType, Boolean agreed, String type) {
        if (!SystemPropertyHelper.getShowServiceAgreement()) {
            throw new BusinessException(ApiCodes.BAD_REQUEST);
        }
        VasAgreement vasAgreement = new VasAgreement();
        if (StringUtils.equals(type, VasAgreementType.MARKET) && BooleanUtils.isTrue(agreed)) {
            Validators.validate(new MarketVasAgreementValidator(getCurrentMarketId(), serviceType, VasAgreementType.MARKET));
            vasAgreement = vasAgreementService.getAgreedLatestAgreement(serviceType, VasAgreementType.MARKET, getCurrentMarketId());
        } else if (StringUtils.equals(type, VasAgreementType.MARKET) && BooleanUtils.isFalse(agreed)) {
            vasAgreement = vasAgreementService.getLatestCompleteAgreement(serviceType, VasAgreementType.MARKET);
        } else if (!StringUtils.equals(type, VasAgreementType.MARKET) && BooleanUtils.isTrue(agreed)) {
            Validators.validate(new MarketVasAgreementValidator(getCurrentMarketId(), serviceType, type));
            vasAgreement = vasAgreementService.getAgreedLatestAgreement(serviceType, type, getCurrentUser().getCurrentDeveloperId());
        } else if (!StringUtils.equals(type, VasAgreementType.MARKET) && BooleanUtils.isFalse(agreed)) {
            vasAgreement = vasAgreementService.getLatestCompleteAgreement(serviceType, type);
        }
        if (Objects.isNull(vasAgreement)) {
            throw new BusinessException(ApiCodes.VAS_SERVICE_AGREEMENT_NOT_FOUND);
        }
        return new VasAgreementInfo()
                .setId(vasAgreement.getId())
                .setName(vasAgreement.getName())
                .setContent(vasAgreement.getContent());
    }

    @Override
    public List<VasAgreementDetailVo> findServiceAgreements(Boolean agreed) {
        if (!SystemPropertyHelper.getShowServiceAgreement()) {
            throw new BusinessException(ApiCodes.BAD_REQUEST);
        }
        List<VasAgreementDetailVo> details = Lists.newArrayList();
        if (Boolean.TRUE.equals(agreed)) {
            List<VasAgreement> vasAgreedList = vasAgreementService.findVasAgreedList(getCurrentMarketId(), getCurrentUser().getCurrentDeveloperId(), LongUtils.equals(getCurrentMarketId(), SystemConstants.SUPER_MARKET_ID) ? null : VasAgreementType.EXTRA);
            if (CollectionUtils.isNotEmpty(vasAgreedList)) {
                Map<String, List<VasAgreement>> collect = vasAgreedList.stream().collect(Collectors.groupingBy(VasAgreement::getServiceType));
                collect.forEach((serviceType, infos) -> {
                    VasAgreement vasAgreement = infos.stream().max(Comparator.comparing(VasAgreement::getAgreeTime)).orElseGet(VasAgreement::new);
                    details.add(VasAgreementDetailVo.builder()
                            .name(vasAgreement.getName())
                            .serviceType(vasAgreement.getServiceType())
                            .content(vasAgreement.getContent())
                            .agreeTime(vasAgreement.getAgreeTime())
                            .assentor(userService.get(vasAgreement.getUserId()).getEmail()).build());
                });
            }
            return details;
        }

        Map<String, String> agreementNeeded = new HashMap<>();

        agreementNeeded.put(VasConstants.ServiceType.INSIGHT, VasAgreementType.EXTRA);
        agreementNeeded.forEach((serviceType, agreementType) -> {
            VasAgreement vasAgreement = vasAgreementService.getLatestCompleteAgreement(serviceType, agreementType);
            if (vasAgreement != null && appVasSupportService.isExistsVasSupported(getCurrentMarketId(), getCurrentUser().getCurrentDeveloperId(), serviceType)
                    && vasAgreementService.getVasAgreementMap(getCurrentMarketId(), vasAgreement.getId(), getCurrentUser().getCurrentDeveloperId()) == null) {
                details.add(VasAgreementDetailVo.builder()
                        .id(vasAgreement.getId())
                        .name(vasAgreement.getName())
                        .serviceType(serviceType)
                        .type(agreementType)
                        .content(vasAgreement.getContent())
                        .build());
            }
        });
        return details;
    }


}
