package com.pax.market.functional.vas;

import com.pax.market.domain.entity.market.Activity;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.mq.contract.airshield.TerminalDetectResult;
import com.pax.market.vo.admin.service.airshield.DetectionHistoryVo;
import com.pax.market.vo.admin.service.airshield.TerminalDetectionDataInfo;


public interface AirShieldAttestationFuncService {

    PageInfo<DetectionHistoryVo> findHistoryPage(Page<DetectionHistoryVo> page, Long terminalId, Boolean result, String severity, Long startTime, Long endTime, String tz, String ip);

    Activity exportAirShieldAttestationHistory(Long terminalId, Boolean result, String severity, Long startTime, Long endTime, String timezone);

    void clearHistory(Long terminalId);

    void deleteAlarm(Long terminalId);

    TerminalDetectResult detectData(TerminalDetectionDataInfo info);

    boolean existedHistory(Long marketId, Long terminalId);

    boolean hasSendUsage(Long marketId, Long terminalId);
}
