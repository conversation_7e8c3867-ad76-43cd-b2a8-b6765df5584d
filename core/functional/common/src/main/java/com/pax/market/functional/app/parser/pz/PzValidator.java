package com.pax.market.functional.app.parser.pz;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.framework.common.io.UploadedFileStream;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.ZipUtils;
import com.pax.market.functional.app.parser.AipAppInfoParser;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.ini4j.Ini;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.StringReader;
import java.nio.file.Paths;
import java.util.List;
import java.util.regex.Pattern;

public class PzValidator {

    protected static void verifyInstallableUnitCount(SystemIni systemIni) {
        if (systemIni.getTotalTaskSize() > SystemConstants.PZ_INSTALLABLE_ITEM_MAX_NUM) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Total count of installable unit must be <=%s", SystemConstants.PZ_INSTALLABLE_ITEM_MAX_NUM));
        }
    }

    protected static void verifyInstallableFiles(UploadedFileStream apkFile, List<String> filePathList) throws IOException {
        verifyInstallableFiles(apkFile, filePathList, false);
    }

    protected static void verifyInstallableFiles(UploadedFileStream apkFile, List<String> filePathList, boolean isMonitorFile) throws IOException {
        if (filePathList == null || filePathList.isEmpty()) {
            return;
        }

        for (String path : filePathList) {
            verifyInstallableFile(apkFile, path, isMonitorFile);
        }
    }

    protected static void verifyInstallableFile(UploadedFileStream apkFile, String filePath) throws IOException {
        verifyInstallableFile(apkFile, filePath, false);
    }

    protected static void verifyInstallableFile(UploadedFileStream apkFile, String filePath, boolean isMonitorFile) throws IOException {
        if (StringUtils.isEmpty(filePath)) {
            return;
        }

        String fileName = Paths.get(filePath).getFileName().toString(); //NOSONAR

        byte[] fileBytes;
        if ((fileBytes = ZipUtils.getEntryBytes(apkFile.getApkFile().getInputStream(), filePath)) == null) {
            throw new BusinessException(ApiCodes.APK_PARSER_ERROR, null, String.format("File[%s] not found in package", filePath));
        }

        if (fileBytes.length == 0) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Empty file[%s] found in package", fileName));
        }

        if (fileName.length() > SystemConstants.PZ_INSTALLABLE_ITEM_NAME_MAX_LENGTH) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Name length of file[%s] must be <=%s", filePath, SystemConstants.PZ_INSTALLABLE_ITEM_NAME_MAX_LENGTH));
        }

        if (isMonitorFile) {
            verifyMonitorFile(fileBytes, fileName);
        }
    }

    protected static void verifyProlinOS(UploadedFileStream apkFile, String filePath, boolean hasPackageKey) throws IOException {
        if (!hasPackageKey) {
            return;
        }

        if (StringUtils.isEmpty(filePath)) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Invalid file path set for Section[%s], Key[%s]", PzIniParser.Attrs.Prolin.OS, PzIniParser.Attrs.Prolin.PACKAGE));
        }

        verifyInstallableFile(apkFile, filePath);

        byte[] osBytes = ZipUtils.getEntryBytes(apkFile.getApkFile().getInputStream(), filePath);
        String manifestXmlStr = ZipUtils.getEntryAsString(osBytes, "manifest.xml");

        if (StringUtils.isEmpty(manifestXmlStr)) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("manifest.xml not found in OS file[%s] or it's empty", filePath));
        }

        Document doc;
        try {
            SAXReader reader = new SAXReader();
            reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true); // Compliant
            doc = reader.read(new StringReader(manifestXmlStr));
        } catch (DocumentException | SAXException e) {//NOSONAR
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Invalid manifest.xml found in OS file[%s]", filePath));
        }

        Element root = doc.getRootElement();
        String osVersion = root.attributeValue("version");
        if (StringUtils.isEmpty(osVersion)) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Invalid value set for attribute \"version\" in manifest.xml in OS file[%s]", filePath));
        }
    }

    protected static void verifyFwpPackages(UploadedFileStream apkFile, List<String> filePathList) throws IOException {
        if (filePathList == null || filePathList.isEmpty()) {
            return;
        }

        verifyInstallableFiles(apkFile, filePathList);

        byte[] fileBytes;
        for (String filePath : filePathList) {
            fileBytes = ZipUtils.getEntryBytes(apkFile.getApkFile().getInputStream(), filePath);
            String fwpinfoStr = ZipUtils.getEntryAsString(fileBytes, "fwpinfo");
            if (StringUtils.isEmpty(fwpinfoStr)) {
                throw new BusinessException(
                        ApiCodes.APK_PARSER_ERROR,
                        null,
                        String.format("fwpinfo not found in FWP package [%s]", filePath));
            }

            Ini ini = new Ini();
            try {
                ini.load(new StringReader(fwpinfoStr));
            } catch (IOException e) {//NOSONAR
                throw new BusinessException(
                        ApiCodes.APK_PARSER_ERROR,
                        null,
                        String.format("Invalid fwpinfo found in FWP package [%s]", filePath));
            }

            String fwpName = ini.get("fwp", "name");
            String version = ini.get("fwp", "version");
            if (StringUtils.isEmpty(fwpName) || StringUtils.isEmpty(version)) {
                throw new BusinessException(
                        ApiCodes.APK_PARSER_ERROR,
                        null,
                        String.format("Invalid value set for [name] or [version] for FWP package [%s]", filePath));
            }

        }
    }

    protected static void verifyAipPackages(UploadedFileStream apkFile, List<String> aipNameList) throws IOException {
        if (aipNameList == null || aipNameList.isEmpty()) {
            return;
        }

        verifyInstallableFiles(apkFile, aipNameList);

        String mainAipName = null;
        for (String aipName : aipNameList) {
            AipAppInfoParser aipAppInfoParser = new AipAppInfoParser();
            aipAppInfoParser.parse(ZipUtils.getEntryBytes(apkFile.getApkFile().getInputStream(), aipName), aipName);
            String aipId = aipAppInfoParser.getValue("app", "id");

            if ("MAINAPP".equals(aipId)) {
                if (mainAipName != null) {
                    throw new BusinessException(
                            ApiCodes.APK_PARSER_ERROR,
                            null,
                            String.format("Duplicate app id [MAINAPP] in aip file %s and %s", mainAipName, aipName));
                } else {
                    mainAipName = aipName;
                }
            }
        }
    }

    protected static void verifyAupPackages(UploadedFileStream apkFile, List<String> aupNameList) throws IOException {
        if (aupNameList == null || aupNameList.isEmpty()) {
            return;
        }

        verifyInstallableFiles(apkFile, aupNameList);

        for (String aupName : aupNameList) {
            new AipAppInfoParser().parse(ZipUtils.getEntryBytes(apkFile.getApkFile().getInputStream(), aupName), aupName);
        }
    }


    private static final String REGEXP_MONITOR_APP_NAME = "[A-Za-z0-9]+";
    private static final String REGEXP_RUNTHOS_APP_VERSION = "[1-9][0-9]*";
    private static final Pattern PATTERN_MONITOR_APP_NAME = Pattern.compile(REGEXP_MONITOR_APP_NAME);
    private static final Pattern PATTERN_RUNTHOS_APP_VERSION = Pattern.compile(REGEXP_RUNTHOS_APP_VERSION);

    protected static void verifyMonitorFile(byte[] fileBytes, String fileName) {
        if (fileBytes != null && fileBytes.length > SystemConstants.PZ_INSTALLABLE_ITEM_MONITOR_MAX_SIZE) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Size of file[%s] must be <=%d bytes", fileName, SystemConstants.PZ_INSTALLABLE_ITEM_MONITOR_MAX_SIZE));
        }

        if (fileName.length() > SystemConstants.PZ_INSTALLABLE_MONITOR_FILE_NAME_MAX_LENGTH) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Name length of file[%s] must be <=%d", fileName, SystemConstants.PZ_INSTALLABLE_MONITOR_FILE_NAME_MAX_LENGTH));
        }
    }

    protected static void verifyAppName(String appName, boolean isMonitor) {
        if (appName == null || appName.length() == 0) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    "APPName can't be empty");
        }

        int appNameMaxLength = isMonitor ? SystemConstants.PZ_MONITOR_APP_NAME_MAX_LENGTH : SystemConstants.PZ_RUNTHOS_APP_NAME_MAX_LENGTH;
        if (appName.length() > appNameMaxLength) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Length of APPName[%s] must be <=%d", appName, appNameMaxLength));
        }

        if (isMonitor && !PATTERN_MONITOR_APP_NAME.matcher(appName).matches()) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("APPName[%s] is invalid, only numeric and alphabetic characters are allowed", appName));
        }
    }

    protected static void verifyMonitorAttribute(String attrName, String attrValue) {
        if (StringUtils.isEmpty(attrValue)) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Attribute[%s] can't be empty", attrName));
        }

        if (StringUtils.isNotEmpty(attrValue) && attrValue.length() > SystemConstants.PZ_MONITOR_ATTRIBUTE_VALUE_MAX_LENGTH) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("Length of [%s] must be <=%d", attrName, SystemConstants.PZ_MONITOR_ATTRIBUTE_VALUE_MAX_LENGTH));
        }
    }

    protected static void verifyVersion4Runthos(String ver) {
        if (!PATTERN_RUNTHOS_APP_VERSION.matcher(ver).matches()) {
            throw new BusinessException(
                    ApiCodes.APK_PARSER_ERROR,
                    null,
                    String.format("APPVersion[%s] is invalid, only numeric characters are allowed", ver));
        }
    }


    protected static void verifyMonitorAppDuplicate(List<SystemIni.MonitorApp> appList,
                                                    SystemIni.MonitorApp monitorApp,
                                                    boolean hasDataFiles) {
        for (SystemIni.MonitorApp app : appList) {
            // if only app bin file set, but no data file, then compare bin file name
            // if only data file set, then compare app_name & app_version
            // if both bin file and data file set, then compare app_name & app_version
            if (StringUtils.isNotEmpty(monitorApp.getBinFile()) && !hasDataFiles) {
                if (monitorApp.getBinFile().equals(app.getBinFile())) {
                    throw new BusinessException(
                            ApiCodes.APK_PARSER_ERROR,
                            null,
                            String.format("Duplicate app bin file [%s] found in package", monitorApp.getBinFile()));
                }
            } else {
                if (monitorApp.getAppName().equals(app.getAppName()) && monitorApp.getAppVersion().equals(app.getAppVersion())) {
                    throw new BusinessException(
                            ApiCodes.APK_PARSER_ERROR,
                            null,
                            String.format("Duplicate %s section[AppName=%s, AppVersion=%s] found in package",
                                    PzIniParser.Attrs.Monitor.APP, monitorApp.getAppName(), monitorApp.getAppVersion()));
                }
            }
        }
    }

}
