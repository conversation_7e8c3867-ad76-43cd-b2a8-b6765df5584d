package com.pax.market.functional.utils.vas;

import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.vas.VasServiceStatusInfo;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.functional.vas.VasAdminFunc;
import com.pax.vas.common.VasConstants;
import com.paxstore.global.domain.service.vas.VasConfigEntityService;
import com.paxstore.global.domain.service.vas.VasInfoService;

import java.util.List;

public class VasUtils {

    private VasUtils() {
    }

    public static void loadMarketPermissions(MarketInfo market) {
        if (market == null) {
            return;
        }
        //vas service
        VasInfoService vasInfoService = SpringContextHolder.getBean(VasInfoService.class);
        VasAdminFunc vasAdminFunc = SpringContextHolder.getBean(VasAdminFunc.class);
        VasConfigEntityService vasConfigEntityService = SpringContextHolder.getBean(VasConfigEntityService.class);
        market.setVasEnabled(vasConfigEntityService.isVasEnabledGlobally());
        market.setCloudMsgEnabledBefore(vasInfoService.isServiceEnabledBefore(VasConstants.ServiceType.CLOUD_MSG));
        List<VasServiceStatusInfo> vasServiceStatusInfos = vasAdminFunc.findEnabledVasServiceStatusInfos(market.getId());
        market.setVasEnabledServiceInfos(vasServiceStatusInfos);
    }
}
