/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.market;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
public interface MarketAdvanceWhiteListIpFunc {

    boolean validateAccessIpInWhiteList(String requestIp, Long marketId);

}