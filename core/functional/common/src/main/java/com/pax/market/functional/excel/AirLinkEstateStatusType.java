package com.pax.market.functional.excel;

import com.pax.market.constants.airlink.AirLinkEstateStatus;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.framework.common.excel.ObjectFieldTypeConvertor;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * <AUTHOR>
 * @Date 2025/4/23 15:02
 */
@Component("airLinkEstateStatusType")
public class AirLinkEstateStatusType implements ObjectFieldTypeConvertor<AirLinkEstateStatus> {
    @Override
    public AirLinkEstateStatus convertToObject(String val) {
        return null;
    }

    @Override
    public String convertToString(AirLinkEstateStatus val) {
        Locale envLocale = LocaleUtils.getEnvLocale();
        if (val == AirLinkEstateStatus.INUSE){
            return MessageUtils.getLocaleMessage("title.status.in.use", envLocale);
        }
        if (val == AirLinkEstateStatus.INSTOCK){
            return MessageUtils.getLocaleMessage("title.status.in.stock", envLocale);
        }
        return val.getCode();
    }
}
