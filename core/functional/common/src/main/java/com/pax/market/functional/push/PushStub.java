/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.functional.push;

import com.google.common.collect.Sets;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.OsType;
import com.pax.market.domain.entity.global.terminal.BaseTerminalEntity;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.mq.contract.billing.SyncActiveTerminalMessage;
import com.pax.market.mq.contract.push.*;
import com.pax.market.mq.producer.gateway.billing.SyncData2BillingGateway;
import com.pax.market.mq.producer.gateway.push.PushCommandGateway;
import com.pax.market.push.common.protocol.BizCommand;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.market.domain.util.SmartLandingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.pax.market.constants.ProductTypeUtils.isTraditional;

/**
 * The type Push stub.
 *
 * <AUTHOR>
 * @date Apr 12, 2017
 */
@Component
public class PushStub {
    /**
     * The constant logger.
     */
    protected static Logger logger = LoggerFactory.getLogger(PushStub.class);

    /**
     * Push app download command.
     *
     * @param appDownloadTerminalIdSet the app download terminal id set
     */
    public void pushAppDownloadCommand(Set<Long> appDownloadTerminalIdSet) {
        if (appDownloadTerminalIdSet == null || appDownloadTerminalIdSet.isEmpty()) {
            return;
        }
        pushMessage(appDownloadTerminalIdSet, AppDownloadPushMessage.class);
    }

    /**
     * Push disable pos command.
     *
     * @param terminal the terminal
     */
    public void pushDisablePosCommand(Terminal terminal) {
        if (terminal == null) {
            return;
        }
        ModelService modelService = SpringContextHolder.getBean(ModelService.class);
        modelService.loadDetails(terminal.getModel());
        if (isTraditional(terminal.getProductType())) {
            SmartLandingUtils.sendTerminalCommands(Collections.singletonList(terminal), BizCommand.DISABLE_POS);
        } else {
            pushCommandMessage(new DisablePosPushMessage(terminal.getId()));
        }
    }

    /**
     * Push disable pos command.
     *
     * @param terminalList the terminal list
     */
    public void pushDisablePosCommand(List<Terminal> terminalList) {
        for (Terminal terminal : terminalList) {
            pushDisablePosCommand(terminal);
        }
    }

    /**
     * Push disable pos command.
     *
     * @param terminalId the terminal id
     */
    public void pushDisablePosCommand(Long terminalId) {
        if (terminalId == null || LongUtils.isBlankOrNotPositive(terminalId)) {
            return;
        }
        pushCommandMessage(new DisablePosPushMessage(terminalId));
    }

    /**
     * Push parameter download command.
     *
     * @param terminalId the terminal id
     */
    public void pushParameterDownloadCommand(Long terminalId) {
        if (terminalId == null || LongUtils.isBlankOrNotPositive(terminalId)) {
            return;
        }
        pushMessage(Sets.newHashSet(terminalId), ParameterDownloadPushMessage.class);
    }

    /**
     * Push terminal profile command.
     *
     * @param terminal the terminal
     */
    public void pushTerminalProfileCommand(BaseTerminalEntity<?> terminal) {
        if (terminal == null) {
            return;
        }
        ModelService modelService = SpringContextHolder.getBean(ModelService.class);
        modelService.loadDetails(terminal.getModel());
        if (isTraditional(terminal.getProductType())) {
            SmartLandingUtils.sendTerminalCommands(Collections.singletonList(terminal), BizCommand.TERMINAL_PROFILE);
        } else {
            pushMessage(Sets.newHashSet(terminal.getId()), TerminalProfilePushMessage.class);
        }
    }

    /**
     * Push terminalprofile command.
     *
     * @param terminalProfileTerminalIdSet the terminal profile terminal id set
     */
    public void pushTerminalProfileCommand(Set<Long> terminalProfileTerminalIdSet) {
        if (terminalProfileTerminalIdSet == null || terminalProfileTerminalIdSet.isEmpty()) {
            return;
        }
        pushMessage(terminalProfileTerminalIdSet, TerminalProfilePushMessage.class);
    }


    /**
     * Push terminal accessory operation message.
     *
     * @param terminalId the terminal id
     */
    public void pushTerminalAccessoryOperationMessage(Long terminalId) {
        if (terminalId == null || LongUtils.isBlankOrNotPositive(terminalId)) {
            return;
        }
        pushMessage(Sets.newHashSet(terminalId), TerminalAccessoryOperationMessage.class);
    }

    /**
     * Push collect logcat message.
     *
     * @param terminal the terminal
     */
    public void pushCollectLogcatMessage(Terminal terminal) {
        if (terminal == null) {
            return;
        }
        ModelService modelService = SpringContextHolder.getBean(ModelService.class);
        modelService.loadDetails(terminal.getModel());
        if (isTraditional(terminal.getProductType())) {
            SmartLandingUtils.sendTerminalCommands(Collections.singletonList(terminal), BizCommand.COLLECT_LOGCAT);
        } else {
            pushMessage(Sets.newHashSet(terminal.getId()), CollectLogcatMessage.class);
        }
    }

    /**
     * Push collect logcat detail message.
     *
     * @param terminal the terminal
     */
    public void pushCollectLogcatDetailMessage(Terminal terminal) {
        if (terminal == null) {
            return;
        }
        pushMessage(Sets.newHashSet(terminal.getId()), CollectLogcatDetailMessage.class);

    }

    /**
     * Push collect skyhook geo message.
     *
     * @param terminal the terminal
     */
    public void pushCollectSkyhookGeoMessage(Terminal terminal) {
        if (terminal == null) {
            return;
        }
        pushMessage(Sets.newHashSet(terminal.getId()), CollectSkyhookGeoMessage.class);
    }

    /**
     * Push terminal refresh map message
     *
     * @param refreshMapTerminalIdSet the terminal ids
     */
    public void pushRefreshMapCommand(Set<Long> refreshMapTerminalIdSet) {
        if (refreshMapTerminalIdSet == null || refreshMapTerminalIdSet.isEmpty()) {
            return;
        }
        pushMessage(refreshMapTerminalIdSet, RefreshTerminalMapMessage.class);
    }

    /**
     * Push terminal operation message.
     *
     * @param terminal the terminal
     */
    public void pushTerminalOperationMessage(BaseTerminalEntity<?> terminal) {
        if (terminal == null) {
            return;
        }
        ModelService modelService = SpringContextHolder.getBean(ModelService.class);
        modelService.loadDetails(terminal.getModel());
        if (isTraditional(terminal.getProductType())) {
            SmartLandingUtils.sendTerminalCommands(Collections.singletonList(terminal), BizCommand.TERMINAL_OPERATION);
        } else {
            pushMessage(Sets.newHashSet(terminal.getId()), TerminalOperationMessage.class);
        }
    }

    /**
     * Push refresh terminal detail message.
     *
     * @param terminal the terminal
     */
    public void pushRefreshTerminalDetailMessage(BaseTerminalEntity<?> terminal) {
        if (terminal == null) {
            return;
        }
        ModelService modelService = SpringContextHolder.getBean(ModelService.class);
        modelService.loadDetails(terminal.getModel());
        if (isTraditional(terminal.getProductType())) {
            SmartLandingUtils.sendTerminalCommands(Collections.singletonList(terminal), BizCommand.REFRESH_POS_DETAIL);
        } else {
            pushMessage(Sets.newHashSet(terminal.getId()), RefreshTerminalDetailMessage.class);
        }
    }

    /**
     * Push terminal activation message.
     *
     * @param terminal the terminal
     */
    public void pushTerminalActivationMessage(BaseTerminalEntity<?> terminal) {
        if (terminal == null) {
            return;
        }
        if (StringUtils.isEmpty(terminal.getSerialNo())) {
            return;
        }
        ModelService modelService = SpringContextHolder.getBean(ModelService.class);
        modelService.loadDetails(terminal.getModel());
        if (isTraditional(terminal.getProductType())) {
            SmartLandingUtils.sendTerminalCommands(Collections.singletonList(terminal), BizCommand.TERMINAL_ACTIVATION);
        } else {
            PushCommandGateway pushGateway = SpringContextHolder.getBean(PushCommandGateway.class);
            pushGateway.send(new TerminalActivationMessage(terminal.getSerialNo()));
        }

        //发送消息到billing
        SyncData2BillingGateway syncData2BillingGateway = SpringContextHolder.getBean(SyncData2BillingGateway.class);
        SyncActiveTerminalMessage message = SyncActiveTerminalMessage.builder()
                .marketId(terminal.getMarketId())
                .serialNo(terminal.getSerialNo()).build();
        syncData2BillingGateway.sendTerminalActiveMessage(JsonMapper.toJsonString(message));
    }

    /**
     * Push terminal activation message.
     *
     * @param terminalList the terminal list
     */
    public void pushTerminalActivationMessage(List<Terminal> terminalList) {
        for (Terminal terminal : terminalList) {
            pushTerminalActivationMessage(terminal);
        }
    }



    public void pushAirviwerAppointmentMessage(Long terminalId) {
        if (terminalId == null || LongUtils.isBlankOrNotPositive(terminalId)) {
            return;
        }
        pushCommandMessage(new AirviewerAppointmentPushMessage(terminalId));
    }


    private void pushMessage(Set<Long> terminalIds, Class<? extends PushCommandMessage> pushMessageClass) {
        PushCommandGateway pushGateway = SpringContextHolder.getBean(PushCommandGateway.class);
        try {
            for (Long terminalId : terminalIds) {
                PushCommandMessage msg = pushMessageClass.getDeclaredConstructor().newInstance();
                msg.setTerminalId(terminalId);
                pushGateway.send(msg);
            }
        } catch (Exception e) {
            logger.error("PushThread Exception...", e);
        }
    }

    /**
     * push PushCommandMessage
     *
     * @param pushCommandMessage the push command message
     */
    public void pushCommandMessage(PushCommandMessage pushCommandMessage) {
        PushCommandGateway pushGateway = SpringContextHolder.getBean(PushCommandGateway.class);
        pushGateway.send(pushCommandMessage);
    }


}
