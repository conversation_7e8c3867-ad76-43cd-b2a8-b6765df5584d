/*
 *  *******************************************************************************
 *  COPYRIGHT
 *                PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *    This software is supplied under the terms of a license agreement or
 *    nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *    or disclosed except in accordance with the terms in that agreement.
 *
 *       Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  *******************************************************************************
 */

package com.pax.market.functional.validation.validators.apk;

import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.core.exception.BusinessException;
import com.paxstore.global.domain.service.app.ApkService;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

public class ApkTypeNoChangeValidator extends Validator<Apk> {

    private App app;

    /**
     * Instantiates a new Validator.
     *
     * @param apk the apk
     */
    public ApkTypeNoChangeValidator(Apk apk, App app) {
        super(apk);
        this.app = app;
    }

    @Override
    public boolean validate() {

        if (validateTarget == null) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }

        if (app == null) {
            throw new BusinessException(ApiCodes.APP_NOT_FOUND);
        }

        ApkService apkService = SpringContextHolder.getBean(ApkService.class);
        Apk apk = apkService.getLatestOnlineOfflineApkByAppId(app.getId());
        if (apk != null && !StringUtils.equals(validateTarget.getApkType(), apk.getApkType()) && !apk.isAllowUpdateParamTemplate()) {
            throw new BusinessException(ApiCodes.APK_BASE_TYPE_NOT_UPDATE);
        }

        return false;
    }
}
