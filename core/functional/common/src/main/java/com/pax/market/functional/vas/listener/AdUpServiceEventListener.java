package com.pax.market.functional.vas.listener;

import com.pax.market.constants.SystemConstants;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 9.3
 */
@Component
public class AdUpServiceEventListener extends AbstractVasSvrEventListener {


    @Override
    public String serviceType() {
        return AD_UP;
    }

    @Override
    protected String packageName() {
        return SystemConstants.AD_UP_PACKAGE_NAME;
    }


}
