package com.pax.market.functional.validation.validators.emm;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.emm.EmmConstants;
import com.pax.market.domain.entity.market.emm.EmmApk;
import com.pax.market.domain.entity.market.emm.EmmApkDetail;
import com.pax.market.functional.validation.Validator;
import org.apache.commons.lang3.StringUtils;

public class EmmApkDetailMaxLengthValidator extends Validator<EmmApk> {
    /**
     * Instantiates a new Apk lang validator.
     *
     * @param apk the apk
     */
    public EmmApkDetailMaxLengthValidator(EmmApk apk) {
        super(apk);
    }

    @Override
    public boolean validate() {
        EmmApkDetail apkDetail = validateTarget.getApkDetail();
        if (apkDetail != null) {
            if (!StringUtils.isBlank(apkDetail.getAppName()) && apkDetail.getAppName().trim().length() > EmmConstants.APK_NAME_MAX_LENGTH) {
                throw new BusinessException(ApiCodes.APP_NAME_TOO_LONG_PARAM);
            }
            if (!StringUtils.isBlank(apkDetail.getShortDesc()) && apkDetail.getShortDesc().trim().length() > EmmConstants.APK_SHORT_DESC_MAX_LENGTH) {
                throw new BusinessException(ApiCodes.SHORT_DESC_TOO_LONG_PARAM);
            }
            if (!StringUtils.isBlank(apkDetail.getDescription()) && apkDetail.getDescription().trim().length() > EmmConstants.APK_DESCRIPTION_MAX_LENGTH) {
                throw new BusinessException(ApiCodes.DESCRIPTION_TOO_LONG_PARAM);
            }
            if (StringUtils.isNotBlank(apkDetail.getAccessUrl()) && apkDetail.getAccessUrl().length() > EmmConstants.APK_ACCESS_URL_MAX_LENGTH) {
                throw new BusinessException(ApiCodes.APP_ACCESS_URL_TOO_LARGE);
            }
        }
        return true;
    }
}