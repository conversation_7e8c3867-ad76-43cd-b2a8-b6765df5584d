package com.pax.market.functional.utils;

import com.pax.core.json.JsonMapper;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.market.role.Role;
import com.pax.market.domain.parameter.ParameterUtils;
import com.pax.market.domain.parameter.SchemaProcess;
import com.pax.market.domain.util.DownloadTokenHelper;
import com.pax.market.domain.util.WebCryptoUtils;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.parameter.GroupInfo;
import com.pax.market.dto.parameter.HeaderInfo;
import com.pax.market.dto.parameter.ParameterInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.DummyCurrentLoginProvider;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.vo.common.parameter.GroupVo;
import com.pax.market.vo.common.parameter.HeaderVo;
import com.pax.market.vo.common.parameter.ParameterVo;
import com.pax.market.vo.common.parameter.SchemaVo;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.market.domain.util.ApkParameterUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SchemaViewUtils {

    public static void resolveUserParameters(String paramTemplate, String originalParam, Map<String, String> defaultValues) {
        SchemaProcess schemaProcess = SchemaProcess.create().defaultValues(JsonMapper.fromJsonString(originalParam)).parseXml(paramTemplate);
        resolveUserReadOnlyParameters(schemaProcess, defaultValues);
        WebCryptoUtils.unMaskAndDecryptDefaultValues(schemaProcess, defaultValues);
    }

    /**
     * 对当前用户只读的参数值，恢复到上一次的数据
     *
     * @param defaultValues the default values
     */
    private static void resolveUserReadOnlyParameters(SchemaProcess schemaProcess, Map<String, String> defaultValues) {
        if (Collections3.isEmpty(defaultValues)) {
            return;
        }
        List<Role> currentUserRoles = getCurrentUserRoles();
        if (Collections3.isEmpty(currentUserRoles)) {
            return;
        }
        final Map<String, String> originalParamValues = schemaProcess.getDefaultValues();
        schemaProcess.getUsedParameterInfoList().forEach(parameterInfo -> {
            if (isUserReadOnlyParameter(parameterInfo, currentUserRoles)) {
                if (originalParamValues.containsKey(parameterInfo.getFormPidWithFileId())) {
                    defaultValues.put(parameterInfo.getFormPidWithFileId(), originalParamValues.get(parameterInfo.getFormPidWithFileId()));
                }
                String pidUrlKey = parameterInfo.getFormPidWithFileId() + SystemConstants.URL_PID_SUFFIX;
                if (originalParamValues.containsKey(pidUrlKey)) {
                    defaultValues.put(pidUrlKey, originalParamValues.get(pidUrlKey));
                }
            }
        });
    }


    private static boolean isUserReadOnlyParameter(ParameterInfo parameterInfo, List<Role> userRoleList) {
        if (StringUtils.isEmpty(parameterInfo.getEditableRoles()) || Collections3.isEmpty(userRoleList)) {
            return false;
        }
        List<String> editableRoles = StringUtils.splitToList(parameterInfo.getEditableRoles(), ",");
        return userRoleList.stream().noneMatch(role -> editableRoles.contains(role.getName()));
    }


    /**
     * Gets schema.
     *
     * @param apkParamTemplate   the apk param template
     * @param currentParamValues the current param values
     * @param paramVariables     the param variables
     * @return the schema
     */
    public static SchemaVo getSchemaView(ApkParamTemplate apkParamTemplate, String currentParamValues, Map<String, String> paramVariables) {
        SchemaProcess schemaProcess = ApkParameterUtils.getSchemaProcess(apkParamTemplate, currentParamValues, paramVariables, false);
        if (schemaProcess != null) {
            SchemaVo schemaView = getSchemaView(schemaProcess);
            schemaView.setParamTemplateName(apkParamTemplate.getName());
            return schemaView;
        }
        return null;
    }

    /**
     * Gets display schema info, password is masked.
     *
     * @param schemaProcess the schema process
     * @return the display schema info
     */
    public static SchemaVo getSchemaView(SchemaProcess schemaProcess) {
        List<Role> currentUserRoles = getCurrentUserRoles();
        SchemaVo schemaView = new SchemaVo();
        for (GroupInfo groupInfo : schemaProcess.getSchemaInfo().getGroupList()) {
            GroupVo groupView = new GroupVo();
            groupView.setTitle(groupInfo.getTitle());
            groupInfo.getParameterList().stream().filter(SchemaViewUtils::isParameterDisplay).filter(parameterInfo -> SchemaViewUtils.isUserVisibleParameter(parameterInfo, currentUserRoles)).forEach(
                    parameterInfo -> groupView.addParameter(getParameterInfoView(parameterInfo, currentUserRoles)));
            groupInfo.getHeaderList().stream().filter(HeaderInfo::isDisplay).forEach(headerInfo -> {
                HeaderVo headerView = new HeaderVo();
                headerView.setTitle(headerInfo.getTitle());
                headerView.setDefaultStyle(headerInfo.getDefaultStyle());
                headerView.setDisplayStyle(headerInfo.getDisplayStyle());
                headerInfo.getParameterList().stream().filter(SchemaViewUtils::isParameterDisplay)
                        .filter(parameterInfo -> SchemaViewUtils.isUserVisibleParameter(parameterInfo, currentUserRoles)).forEach(
                        parameterInfo -> headerView.addParameter(getParameterInfoView(parameterInfo, currentUserRoles)));
                if (!headerView.getParameterList().isEmpty()) {
                    groupView.addHeader(headerView);
                }
            });

            if (!groupView.getHeaderList().isEmpty() || !groupView.getParameterList().isEmpty()) {
                schemaView.addGroup(groupView);
            }
        }
        return schemaView;
    }

    private static boolean isParameterDisplay(ParameterInfo parameterInfo) {
        List<ParameterInfo> dependentParameterList = parameterInfo.getDependentParameterList();
        if (CollectionUtils.isEmpty(dependentParameterList)) {
            return parameterInfo.isDisplay();
        }
        for (int i = 0; i < dependentParameterList.size(); i++) {
            ParameterInfo dependentParameter = dependentParameterList.get(i);
            if (StringUtils.equals(dependentParameter.getWebFormPid(), parameterInfo.getWebFormPid()) || isParameterDisplay(dependentParameter)) {
                continue;
            }
            //如果依赖的参数是不显示的，那么如果依赖的参数值不匹配，该参数也不显示
            Set<String> dependsOnValues = parameterInfo.getDependentValueList().get(i);
            if (!dependsOnValues.contains(dependentParameter.getDefaultValue())) {
                return false;
            }
        }
        return parameterInfo.isDisplay();
    }

    public static boolean isUserVisibleParameter(ParameterInfo parameterInfo, List<Role> userRoleList) {
        if (StringUtils.isEmpty(parameterInfo.getViewableRoles()) || Collections3.isEmpty(userRoleList)) {
            return true;
        }
        List<String> viewableRoles = StringUtils.splitToList(parameterInfo.getViewableRoles(), ",");
        return userRoleList.stream().anyMatch(role -> viewableRoles.contains(role.getName()));
    }

    private static ParameterVo getParameterInfoView(ParameterInfo parameterInfo, List<Role> userRoles) {
        ParameterVo parameterView = new ParameterVo();
        parameterView.setPid(parameterInfo.getWebFormPid());
        parameterView.setDataType(StringUtils.lowerCase(parameterInfo.getDataType()));
        parameterView.setType(StringUtils.lowerCase(parameterInfo.getType()));
        parameterView.setInputType(StringUtils.lowerCase(parameterInfo.getInputType()));
        parameterView.setTitle(parameterInfo.getTitle());
        parameterView.setLength(parameterInfo.getLength());
        parameterView.setMinLength(parameterInfo.getMinLength());
        parameterView.setRequired(parameterInfo.isRequired());
        parameterView.setReadOnly(parameterInfo.isReadOnly() || isUserReadOnlyParameter(parameterInfo, userRoles));
        parameterView.setMaxInclusive(parameterInfo.getMaxInclusive());
        parameterView.setMaxExclusive(parameterInfo.getMaxExclusive());
        parameterView.setMinInclusive(parameterInfo.getMinInclusive());
        parameterView.setMinExclusive(parameterInfo.getMinExclusive());
        parameterView.setFractionDigits(parameterInfo.getFractionDigits());
        parameterView.setFileSuffix(parameterInfo.getFileSuffix());
        parameterView.setSelect(parameterInfo.getSelect());
        parameterView.setUrl(parameterInfo.getUrl());
        parameterView.setVariableKey(parameterInfo.getVariableKey());
        if (StringUtils.isNotBlank(parameterInfo.getUrl())) {
            //downloadToken用于页面下载用，有效时间30分钟，用户如果在页面上停留超过30分钟，下载会失败
            parameterView.setDownloadToken(DownloadTokenHelper.generateDownloadToken(parameterInfo.getUrl(), parameterInfo.getDefaultValue(), parameterInfo.isUseOriginalValue(), 1800));
        }
        parameterView.setUrlPid(parameterView.getPid() + SystemConstants.URL_PID_SUFFIX);
        parameterView.setDescription(parameterInfo.getDescription());
        if (StringUtils.equalsIgnoreCase(parameterInfo.getInputType(), "password") && StringUtils.isNotEmpty(parameterInfo.getDefaultValue())) {
            parameterView.setDefaultValue(StringUtils.mask(parameterInfo.getDefaultValue()));
        } else {
            parameterView.setDefaultValue(parameterInfo.getDefaultValue());
        }
        setDependentParameterList(parameterView, parameterInfo);
        reverseAmountValue(parameterView);
        return parameterView;
    }

    private static void setDependentParameterList(ParameterVo parameterView, ParameterInfo parameterInfo) {
        List<ParameterInfo> dependentParameterList = parameterInfo.getDependentParameterList();
        if (CollectionUtils.isEmpty(dependentParameterList)) {
            return;
        }
        List<ParameterVo> dependentParameterViewList = Lists.newArrayList();
        for (int i = 0; i < dependentParameterList.size(); i++) {
            ParameterVo dependentParameterView = new ParameterVo();
            ParameterInfo dependentParameter = dependentParameterList.get(i);
            dependentParameterView.setPid(dependentParameterList.get(i).getWebFormPid());
            dependentParameterView.setDependentValue(parameterInfo.getDependentValueList().get(i));
            //如果依赖的参数是amount类型，需要将amount类型转换，否则页面无法匹配
            if (dependentParameter.isAmountDataType()) {
                Set<String> reversedDependentValue = dependentParameterView.getDependentValue().stream().map(each -> ParameterUtils.reverseAmountValue(each, dependentParameter.getFractionDigits())).collect(Collectors.toSet());
                dependentParameterView.setDependentValue(reversedDependentValue);
            }
            dependentParameterViewList.add(dependentParameterView);

        }
        parameterView.setDependentParameterList(dependentParameterViewList);
    }

    private static void reverseAmountValue(ParameterVo parameterView) {
        if (parameterView.isAmountDataType()) {
            parameterView.setDefaultValue(ParameterUtils.reverseAmountValue(parameterView.getDefaultValue(), parameterView.getFractionDigits()));
            parameterView.setMinExclusive(ParameterUtils.reverseMinMaxValue(parameterView.getMinExclusive(), parameterView.getFractionDigits()));
            parameterView.setMinInclusive(ParameterUtils.reverseMinMaxValue(parameterView.getMinInclusive(), parameterView.getFractionDigits()));
            parameterView.setMaxExclusive(ParameterUtils.reverseMinMaxValue(parameterView.getMaxExclusive(), parameterView.getFractionDigits()));
            parameterView.setMaxInclusive(ParameterUtils.reverseMinMaxValue(parameterView.getMaxInclusive(), parameterView.getFractionDigits()));
        }
    }

    /**
     * Gets current user role list.
     *
     * @return the current user role list
     */
    public static List<Role> getCurrentUserRoles() {
        List<Role> currentUserRoles = com.google.common.collect.Lists.newArrayList();
        CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);
        if (currentLoginProvider instanceof DummyCurrentLoginProvider) {
            return currentUserRoles;
        }
        UserInfo currentUser = currentLoginProvider.getCurrentUserInfo();
        if (currentUser == null) {
            return currentUserRoles;
        }
        ResellerInfo currentReseller = currentUser.getCurrentReseller();
        if (currentReseller != null) {
            RoleService roleService = SpringContextHolder.getBean(RoleService.class);
            currentUserRoles = roleService.findUserRoles(currentUser.getId(), currentReseller.getMarketId(), currentReseller.getId());
        }
        return currentUserRoles;
    }
}
