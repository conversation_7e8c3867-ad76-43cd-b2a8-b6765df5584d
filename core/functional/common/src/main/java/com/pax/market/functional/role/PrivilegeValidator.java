/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.role;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.role.Privilege;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

import java.util.Set;

/**
 * The type Role create request validator.
 */
public class PrivilegeValidator extends Validator<Set<Long>> {

    public PrivilegeValidator(Set<Long> privilegeIds) {
        super(privilegeIds);
    }

    @Override
    public boolean validate() {
        CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);
        MarketInfo currentMarketInfo = currentLoginProvider.getCurrentMarketInfo();
        UserInfo currentUserInfo = currentLoginProvider.getCurrentUserInfo();

        Set<Privilege> currentUserPrivileges = SpringContextHolder
                .getBean(PrivilegeSupportFunctionService.class)
                .findByCurrentUser(currentUserInfo, currentMarketInfo);
        for (Long privilegeId : validateTarget) {
            boolean privilegeAvailable = false;
            for (Privilege currentPrivilege : currentUserPrivileges) {
                if (LongUtils.equals(privilegeId, currentPrivilege.getId()) && currentPrivilege.isShow()) {
                    privilegeAvailable = true;
                    break;
                }
            }

            if (!privilegeAvailable) {
                throw new BusinessException(ApiCodes.ROLE_PRIVILEGE_INVALID);
            }
        }

        return false;
    }

}
