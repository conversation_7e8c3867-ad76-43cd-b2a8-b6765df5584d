package com.pax.market.functional.activity.imports;

import com.google.common.collect.Maps;
import com.pax.market.constants.ActivityStatus;
import com.pax.market.constants.ActivityType;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.airlink.AirLinkEstateStatus;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkEstate;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.StopWatch;
import com.paxstore.global.domain.service.vas.airlink.AirLinkEstateService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.pax.market.functional.activity.util.TerminalImportDataValidator.*;

/**
 * <AUTHOR>
 * @Date 2025/4/21 17:52
 */
@Component
@RequiredArgsConstructor
public class AirLinkEstateImportService extends BaseImportService<AirLinkEstate>{
    private final AirLinkTerminalImportService airLinkTerminalImportService;
    private final AirLinkEstateService airLinkEstateService;

    @Override
    public void importData(Activity activity, List<AirLinkEstate> dataList) {

    }

    @Override
    public void importData(Activity activity, List<AirLinkEstate> dataList, byte[] importFileData) {
        StringBuilder sbForInput = new StringBuilder();
        Map<Integer, String> importErrorsForInput = Maps.newHashMapWithExpectedSize(dataList.size());
        Set<String> existImeis = new HashSet<>();
        List<AirLinkEstate> saveEstates = new ArrayList<>();
        AirLinkEstate airLinkEstate;
        boolean saveFlag = true;
        StopWatch sw = new StopWatch(String.format("Import %d imeis", dataList.size()));
        sw.start("Validate imeis");
        for (int i = 0; i < dataList.size(); i++){
            boolean hasError = false;
            int lineNo = i + 3;
            airLinkEstate = dataList.get(i);
            String imei = airLinkEstate.getImei();
            if (StringUtils.isNotEmpty(imei)) {
                if (airLinkTerminalImportService.imeiIsIllegal(imei, sbForInput, activity.getLocale())) {
                    hasError = true;
                } else {
                    if (existImeis.contains(imei)) {
                        hasError = true;
                        loadStringBuilderContentWithoutLine(sbForInput, activity.getLocale(), ApiCodes.AIRLINK_IMEI_DUPLICATE_PARAM, imei);
                    } else {
                        if (airLinkEstateService.existImei(imei)) {
                            hasError = true;
                            loadStringBuilderContentWithoutLine(sbForInput, activity.getLocale(), ApiCodes.AIRLINK_IMEI_DUPLICATE_PARAM, imei);
                        }
                        existImeis.add(imei);
                    }
                }
            }
            if (hasError && saveFlag){
                saveFlag = false;
            }
            if (hasError){
                loadImportError(sbForInput, lineNo, importErrorsForInput);
            }else {
                if (saveFlag){
                    airLinkEstate.setStatus(AirLinkEstateStatus.INSTOCK);
                    airLinkEstate.setMarketId(activity.getReferenceId());
                    saveEstates.add(airLinkEstate);
                }
            }
        }
        sw.stop();
        if (!importErrorsForInput.isEmpty()) {
            importErrorsForInput = repairErrorLineNumber(importFileData, importErrorsForInput);
            activity.setFileId(getFileIdWithError(activity.getFileId(), importFileData, importErrorsForInput));
            String msg = StringUtils.join(convertImportErrorForView(importErrorsForInput, activity.getLocale()), "\n");
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, msg);
        } else if (saveEstates.isEmpty()) {
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, MessageUtils.getErrorMessage(ApiCodes.NO_VALID_IMPORT_DATA, activity.getLocale()));
        } else {
            sw.start("batchCreateAirLinkEstates");
            airLinkEstateService.batchInsert(saveEstates);
            sw.stop();
            activityService.updateActivityStatus(activity, ActivityStatus.SUCCESS, MessageUtils.getErrorMessage(ApiCodes.AIRLINK_ESTATE_CREATED_SUCCESS_MSG, activity.getLocale(), saveEstates.size()));
        }
        if (sw.getTotalTimeMillis() > 2000) {
            logger.info(sw.prettyPrint());
        }
    }

    @Override
    public String getActivityType() {
        return ActivityType.IMPORT_AIR_LINK_ESTATE;
    }

    @Override
    public String getActivityName(List<AirLinkEstate> dataList, Long referenceId) {
        return String.format(getLocaleMessageForImport("title.import.airlink.estates") + "(%s)", dataList.size());
    }

    @Override
    public Class getClazz() {
        return AirLinkEstate.class;
    }

    @Override
    public int getImportLimit() {
        return SystemPropertyHelper.getAirLinkEstateImportLimit();
    }
}
