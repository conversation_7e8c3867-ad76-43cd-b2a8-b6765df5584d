/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.functional.activity.imports.variable;

import com.pax.market.audit.biz.utils.AuditLogChangeRecordUtils;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.variable.MerchantVariable;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.variable.MerchantVariableService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.audit.AuditTrailContext;
import com.pax.market.framework.common.audit.AuditTrailContextHolder;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * The type Merchant variable import service.
 */
@Component
public class MerchantVariableImportService extends BaseVariableImportService<MerchantVariable> {
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantVariableService merchantVariableService;

    @Override
    public void importData(Activity activity, List<MerchantVariable> merchantVariableList) {
        Merchant merchant = merchantService.get(activity.getReferenceId());
        if (merchant == null) {
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, MessageUtils.getErrorMessage(ApiCodes.MERCHANT_NOT_EXIST, activity.getLocale()));
            return;
        }

        StringBuilder failureMsg = new StringBuilder();
        int errorLines = 0;
        int lineNo = 3; //模板行数从3开始
        List<MerchantVariable> finalList = new ArrayList<>();
        merchantVariableList.forEach(each -> each.setMerchant(merchant));
        for (MerchantVariable merchantVariable : merchantVariableList) {
            if (StringUtils.isNotEmpty(merchantVariable.getKey()) ||
                    StringUtils.isNotEmpty(merchantVariable.getValue()) ||
                    StringUtils.isNotEmpty(merchantVariable.getAppPackageName()) ||
                    StringUtils.isNotEmpty(merchantVariable.getRemarks()) ||
                    StringUtils.isNotEmpty(merchantVariable.getStatus())) {
                //密码类型的变量，导入，如果是maskString，也不是删除变量操作，就认为没有改过，直接忽略
                if (StringUtils.equals(VariableType.PASSWORD, merchantVariable.getType())
                        && StringUtils.isMaskString(merchantVariable.getValue())
                        && !VariableStatus.DELETED.equals(merchantVariable.getStatus())) {
                    lineNo++;
                    continue;
                }
                boolean hasError = validateVariableForImport(merchantVariable, failureMsg, lineNo, activity.getLocale(), finalList);
                if (hasError) {
                    errorLines++;
                }
                if (errorLines >= 10) { //如果错误的行出超过十行，不继续执行
                    failureMsg.append("...");
                    break;
                }
                finalList.add(merchantVariable);
            }
            lineNo++;
        }
        completeVariableImportActivity(activity, finalList, failureMsg);
    }

    /**
     * Import data list.
     *
     * @param activity       the activity
     * @param dataList       the data list
     * @param importFileData the file data
     * @return the list
     */
    @Override
    public void importData(Activity activity, List<MerchantVariable> dataList, byte[] importFileData) {
        // nothing need to to
    }

    @Override
    public String getActivityType() {
        return ActivityType.IMPORT_MERCHANT_VARIABLE;
    }

    @Override
    public String getActivityName(List<MerchantVariable> merchantVariableList, Long referenceId) {
        Merchant merchant = merchantService.get(referenceId);
        if (merchant == null) {
            return String.format(getLocaleMessageForImport("title.import.merchant.variables") + "(%s)", merchantVariableList.size());
        }

        return String.format(getLocaleMessageForImport("excel.import.merchant.variables"), merchant.getName(), merchantVariableList.size());
    }

    @Override
    public Class<MerchantVariable> getClazz() {
        return MerchantVariable.class;
    }

    @Override
    public int getImportLimit() {
        return SystemPropertyHelper.getMerchantVariableImportLimit();
    }

    @Override
    MerchantVariableService getVariableService() {
        return merchantVariableService;
    }
}
