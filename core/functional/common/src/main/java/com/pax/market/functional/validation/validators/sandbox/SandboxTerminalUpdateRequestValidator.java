/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.sandbox;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.utils.TerminalUtils;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.request.sandbox.SandboxTerminalUpdateRequest;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2018/5/2
 */
public class SandboxTerminalUpdateRequestValidator extends Validator<SandboxTerminalUpdateRequest> {

    public SandboxTerminalUpdateRequestValidator(SandboxTerminalUpdateRequest sandboxTerminalUpdateRequest){
        super(sandboxTerminalUpdateRequest);
    }

    public boolean validate(){

        if (StringUtils.isEmpty(validateTarget.getName())) {
            throw new BusinessException(ApiCodes.TERMINAL_NAME_MANDATORY);
        }

        if (validateTarget.getModelId() == null || LongUtils.isBlankOrNotPositive(validateTarget.getModelId())) {
            throw new BusinessException(ApiCodes.TERMINAL_MODEL_MANDATORY);
        }

        if (validateTarget.getName().trim().length() > SystemConstants.TERMINAL_NAME_LENGTH) {
            throw new BusinessException(ApiCodes.TERMINAL_NAME_TOO_LONG);
        }

        if (validateTarget.getModelId().toString().trim().length() > 20) {
            throw new BusinessException(ApiCodes.TERMINAL_MODEL_TOO_LONG);
        }

        if (StringUtils.isEmpty(validateTarget.getSerialNo())) {
            throw new BusinessException(ApiCodes.TERMINAL_SERIAL_NO_MANDATORY);
        }

        TerminalUtils.checkTerminalSerialNo(validateTarget.getSerialNo());

        return true;
    }
}
