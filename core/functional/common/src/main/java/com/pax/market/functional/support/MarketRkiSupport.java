/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.functional.support;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.paxstore.market.domain.service.rki.RkiKeyService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MarketRkiSupport {

    private final RkiKeyService rkiKeyService;
    private final ResellerService resellerService;

    public boolean isKeyAlreadyExistForReseller(String key, Long resellerId) {
        Reseller reseller = resellerService.getWithoutDataScopeCheck(resellerId);
        if (reseller == null) {
            return false;
        }
        return rkiKeyService.isKeyAlreadyExistForReseller(key, resellerId);
    }
}
