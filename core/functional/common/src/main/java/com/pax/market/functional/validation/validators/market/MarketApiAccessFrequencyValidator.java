/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.market;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.dto.request.market.access.frequency.MarketApiAccFreqRequest;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.functional.validation.Validator;


public class MarketApiAccessFrequencyValidator extends Validator<MarketApiAccFreqRequest> {

    private final static Integer MAX_FREQUENCY_TIME = 999999;

    public MarketApiAccessFrequencyValidator(MarketApiAccFreqRequest request) {
        super(request);
    }

    @Override
    public boolean validate() {

        if (validateTarget == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }

        if (IntegerUtils.isBlankOrNotPositive(validateTarget.getFrequency())
                || IntegerUtils.getValue(validateTarget.getFrequency()) > MAX_FREQUENCY_TIME){
            throw new BusinessException(ApiCodes.MARKET_API_ACCESS_FREQUENCY_INVALID);
        }

        if (IntegerUtils.isBlankOrNotPositive(validateTarget.getTime())
                || IntegerUtils.getValue(validateTarget.getTime()) > MAX_FREQUENCY_TIME){
            throw new BusinessException(ApiCodes.MARKET_API_ACCESS_TIME_INVALID);
        }

        return true;
    }
}
