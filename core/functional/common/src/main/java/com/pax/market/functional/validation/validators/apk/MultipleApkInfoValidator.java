/*
 *  *******************************************************************************
 *  COPYRIGHT
 *                PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *    This software is supplied under the terms of a license agreement or
 *    nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *    or disclosed except in accordance with the terms in that agreement.
 *
 *       Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  *******************************************************************************
 */

package com.pax.market.functional.validation.validators.apk;

import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;

/**
 * The type Multiple apk info validator.
 */
public class MultipleApkInfoValidator extends Validator<Apk> {

    private final Apk originalApk;

    /**
     * Instantiates a new Multiple apk info validator.
     *
     * @param originalApk the original apk
     * @param apk         the apk
     */
    public MultipleApkInfoValidator(Apk originalApk, Apk apk) {
        super(apk);
        this.originalApk = originalApk;
    }

    @Override
    public boolean validate() {
        if (originalApk == null || validateTarget == null) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }

        if (!StringUtils.equals(originalApk.getPackageName(), validateTarget.getPackageName()) ||
                !StringUtils.equals(originalApk.getVersionName(), validateTarget.getVersionName()) ||
                !LongUtils.equals(originalApk.getVersionCode(), validateTarget.getVersionCode())) {
            throw new BusinessException(ApiCodes.MULTIPLE_APK_FILE_INFO_NOT_MATCH);
        }

        return false;
    }
}
