package com.pax.market.functional.activity.util;

import com.pax.market.constants.*;
import com.pax.market.constants.emm.EmmConstants;
import com.pax.market.domain.entity.market.emm.EmmDevice;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class EmmDeviceImportDataValidator {

    /**
     * 记录导入的错误信息(不包含行号)
     *
     * @param sbForInput the each row of error messages will eventually modify the original excel sheet
     * @param locale     the locale
     * @param errorCode  the error code
     * @param args       the params
     */
    public static void loadEmmStringBuilderContentWithoutLine(StringBuilder sbForInput, String locale, int errorCode, Object... args) {
        //sbForInput without line
        sbForInput.append(MessageUtils.getErrorMessage(
                errorCode, locale, args
        )).append("\n");
    }

    /**
     * 加载导入验证的错误
     *
     * @param sbForInput           the sb for input
     * @param lineNo               the line no
     * @param importErrorsForInput the import errors for input
     */
    public static void loadEmmImportError(StringBuilder sbForInput, int lineNo, Map<Integer, String> importErrorsForInput) {
        importErrorsForInput.put(lineNo, StringUtils.substring(sbForInput.toString(), 0, sbForInput.lastIndexOf("\n")));
        sbForInput.setLength(0);
    }


    /**
     * 判断 sn 是否合法
     *
     * @param sn         the sn
     * @param sbForInput the sb for input
     * @param locale     the locale
     * @return the boolean
     */
    public static boolean emmSerialNoIsIllegal(String sn, StringBuilder sbForInput, String locale) {
        boolean hasError = false;
        if (StringUtils.isEmpty(sn)) {
            return false;
        }
        if (sn.length() > EmmConstants.EMM_DEVICE_SN_LENGTH) {
            hasError = true;
            loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_SERIAL_NO_TOO_LONG_PARAM);
        }
        if (!sn.matches(SystemConstants.ALPHA_NUMERIC) || StringUtils.inStringIgnoreCase(sn.trim(), SystemConstants.INVALID_TERMINAL_SNS)) {
            hasError = true;
            loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_SERIAL_NO_INVALID_PARAM);
        }
        return hasError;
    }

    /**
     * 判断 IMEI 是否合法
     *
     * @param imei       the imei
     * @param sbForInput the sb for input
     * @param locale     the locale
     * @return the boolean
     */
    public static boolean emmImeiIsIllegal(String imei, StringBuilder sbForInput, String locale) {
        boolean hasError = false;
        if (StringUtils.isEmpty(imei)) {
            return false;
        }
        if (!imei.matches(EmmConstants.IMEI_NUMBER_REG_EXPR)) {
            hasError = true;
            loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_ZTE_IMEI_NUMBERS_INVALID);
        }
        return hasError;
    }

    /**
     * 检验SN是否匹配EMM设备
     * For importBatch
     *
     * @param emmDevice    the emm device
     * @param emmDeviceDB  the emm device in db
     * @param locale       the locale
     * @param sbForInput   the sb for input
     * @return the boolean
     */
    public static boolean validateEmmSerialNoMatch(EmmDevice emmDevice, EmmDevice emmDeviceDB, String locale, StringBuilder sbForInput) {

        boolean hasError = false;
        if (StringUtils.isNotBlank(emmDevice.getSerialNo()) && StringUtils.isNotBlank(emmDevice.getImei())
                && (!StringUtils.equals(emmDevice.getSerialNo(), emmDeviceDB.getSerialNo()) || !StringUtils.equals(emmDevice.getImei(), emmDeviceDB.getImei()))) {
            loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_SN_IMEI_MISMATCH);
            hasError = true;
        }
        emmDevice.setId(emmDeviceDB.getId());
        emmDevice.setStatus(emmDeviceDB.getStatus());

        return hasError;
    }

    /**
     * Validate reseller and merchant for move boolean.
     *
     * @param emmDevice  the emm device
     * @param locale     the locale
     * @param sbForInput the sb for input
     * @return the boolean
     */
    public static boolean validateEmmResellerAndMerchantForMove(EmmDevice emmDevice, String locale, StringBuilder sbForInput) {
        boolean hasError = validateResellerAndMerchant(emmDevice, sbForInput, locale);
        //EMM设备必须要有merchant
        if (StringUtils.isBlank(emmDevice.getMerchantName())) {
            loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.MERCHANT_NOT_EXIST_PARAM);
            hasError = true;
        }
        return hasError;
    }


    /**
     * Validate reseller and merchant boolean.
     *
     * @param emmDevice   the emm device
     * @param sbForInput the sb for input
     * @param locale     the locale
     * @return the boolean
     */
    public static boolean validateResellerAndMerchant(EmmDevice emmDevice, StringBuilder sbForInput, String locale) {
        ResellerService resellerService = SpringContextHolder.getBean(ResellerService.class);
        MerchantService merchantService = SpringContextHolder.getBean(MerchantService.class);
        boolean hasError = false;
        if (Objects.isNull(emmDevice.getReseller()) || StringUtils.isEmpty(emmDevice.getReseller().getName())) {
            hasError = true;
            loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_RESELLER_MANDATORY_PARAM);
        } else {
            Reseller reseller = resellerService.getByNameWithDataScopeCheck(StringUtils.trim(emmDevice.getReseller().getName()));
            if (Objects.isNull(reseller)) {
                hasError = true;
                loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.RESELLER_NOT_EXIST_PARAM);
            } else if (!StringUtils.equalsIgnoreCase(ResellerStatus.ACTIVE, reseller.getStatus())) {
                hasError = true;
                loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_SERIAL_RESELLER_NOT_ACTIVE_PARAM);
            }

            emmDevice.setReseller(reseller);
        }

        if (Objects.nonNull(emmDevice.getReseller()) && Objects.nonNull(emmDevice.getMerchant()) && StringUtils.isNotBlank(emmDevice.getMerchant().getName())) {
            Merchant merchant = merchantService.getByName(emmDevice.getReseller().getId(), StringUtils.trim(emmDevice.getMerchant().getName()));
            if (Objects.isNull(merchant)) {
                hasError = true;
                loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.MERCHANT_NOT_EXIST_PARAM);
            } else if (MerchantStatus.MIGRATING.equals(merchant.getStatus())) {
                hasError = true;
                loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.MERCHANT_IN_MIGRATING_PARAM);
            } else if (!MerchantStatus.ACTIVE.equals(merchant.getStatus())) {
                hasError = true;
                loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.MERCHANT_NOT_ACTIVE_PARAM);
            }

            if (Objects.nonNull(merchant)) {
                emmDevice.setMerchant(merchant);
                if (StringUtils.isEmpty(emmDevice.getReseller().getName())) {
                    hasError = true;
                    loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_RESELLER_MANDATORY_PARAM);
                } else {
                    if (!StringUtils.equals(emmDevice.getReseller().getName(), merchant.getReseller().getName())) {
                        hasError = true;
                        loadEmmStringBuilderContentWithoutLine(sbForInput, locale, ApiCodes.EMM_DEVICE_RESELLER_MERCHANT_MISMATCH);
                    }
                }
            }
        }

        if (Objects.isNull(emmDevice.getMerchant()) || StringUtils.isEmpty(emmDevice.getMerchant().getName())) {
            emmDevice.setMerchant(null);
        }
        return hasError;
    }


    /**
     * 转换同步可见错误消息/Remark
     *
     * @param importErrorsForInput the import errors for input
     * @param locale               the locale
     * @return the list
     */
    public static List<String> convertEmmImportErrorForView(Map<Integer, String> importErrorsForInput, String locale) {
        List<String> importErrorsForView = new ArrayList<>();
        int syncLimit = SystemPropertyHelper.getSyncImportDataLimit();
        if (MapUtils.isNotEmpty(importErrorsForInput)) {
            importErrorsForInput = Collections3.sortByKey(importErrorsForInput, false);
            for (Map.Entry<Integer, String> entry : importErrorsForInput.entrySet()) {
                if (importErrorsForView.size() < syncLimit) {
                    String message = StringUtils.replaceAll(entry.getValue(), "\n", SystemConstants.COMMAS);
                    importErrorsForView.add(
                            MessageUtils.getErrorMessage(ApiCodes.EMM_DEVICE_ERROR_LINE, locale, String.valueOf(entry.getKey())) + message
                    );
                } else {
                    break;
                }
            }
            if (CollectionUtils.isNotEmpty(importErrorsForView) && importErrorsForInput.size() > importErrorsForView.size()) {
                importErrorsForView.add("...");
            }
        }
        return importErrorsForView;
    }

}
