package com.pax.market.functional.support;

import com.pax.api.cache.CacheService;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.AppGlobalSubscription;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.market.domain.entity.global.market.MarketAdvancedSetting;
import com.pax.market.domain.entity.global.market.MarketApkSignature;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalGroup;
import com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventAccumulator;
import com.paxstore.domain.support.SignatureSupport;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppSubscriptionService;
import com.paxstore.global.domain.service.market.MarketAdvancedSettingService;
import com.paxstore.global.domain.service.market.MarketApkSignatureService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.market.domain.service.organization.ResellerApkSignatureService;
import com.pax.market.dto.app.ApkSignatureInfo;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.signature.SignatureProviderName;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.paxstore.global.domain.service.app.AppWhiteListService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MarketApkSignatureSupport extends AbstractFunctionalService {

    private final SignatureSupport signatureSupport;
    private final ApkService apkService;
    private final MarketApkSignatureService marketApkSignatureService;
    private final AppWhiteListService appWhiteListService;
    private final AppSubscriptionService appSubscriptionService;
    private final ResellerApkSignatureService resellerApkSignatureService;
    private final ResellerService resellerService;
    private final CacheService cacheService;
    private final MarketAdvancedSettingService marketAdvancedSettingService;
    private final ResellerApkSignatureSupport resellerApkSignatureSupport;
    private final ModelService modelService;


    /**
     * 应用详情页显示应用签名状态
     *
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @param apk        the apk
     * @return the market apk signature info list
     */
    public List<ApkSignatureInfo> getMarketApkSignatureInfoList(Long marketId, Long resellerId, Apk apk) {
        List<ApkSignatureInfo> apkSignatureInfoList = Lists.newArrayList();
        //非安卓应用, 不支持签名  不显示
        if (!StringUtils.equals(OsType.ANDROID, apk.getOsType()) || !signatureSupport.isSignatureSupport(marketId)) {
            return apkSignatureInfoList;
        }
        apkService.findApkSupportFactoryList(apk.getId()).forEach(factory -> {
            apkService.loadApkFile(apk, factory.getId());
            if (apk.getApkFile() == null) {
                apkService.loadApkFile(apk);
            }

            Stream<String> signatureTypes = BooleanUtils.isTrue(getCurrentMarket().getAllowPci7Signature())
                    ? SignatureType.TYPES.stream()
                    : Stream.of(SignatureType.RSA_2048);

            signatureTypes.forEach(signType -> {
                if (!SignatureProviderName.PAX.val().equals(factory.getSignatureProvider()) && signType.equals(SignatureType.RSA_4096)){
                    return;
                }
                ApkSignatureInfo apkSignatureInfo = getMarketApkSignatureInfo(marketId, resellerId, factory, apk, signType);
                apkSignatureInfo.setResignable(isApkReSignable(getCurrentMarketId(), getCurrentResellerId(), factory.getId(), apk, signType));
                if (SignatureProviderName.SUNMI.val().equals(factory.getSignatureProvider()) && signType.equals(SignatureType.RSA_2048)){
                  apkSignatureInfo.setSignType("-");
                }
                apkSignatureInfoList.add(apkSignatureInfo);
            });
        });
        return apkSignatureInfoList;
    }

    /**
     * Is apk resignable boolean.
     *
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @param factoryId  the factory id
     * @param apk        the apk
     * @return the boolean
     */
    public boolean isApkReSignable(Long marketId, Long resellerId, Long factoryId, Apk apk, String signType) {
        //代理商不需要签名，不允许重新签名
        boolean packageInSignatureWhiteList = appWhiteListService.isPackageInSignatureWhiteList(apk.getOriginalMarketId(), apk.getPackageName());
        if (!signatureSupport.isMarketApkSignatureEnabledBySignType(marketId, resellerId, factoryId, apk, packageInSignatureWhiteList, signType)) {
            return false;
        }

        MarketApkSignature marketApkSignature = marketApkSignatureService.getMarketApkSignature(marketId, resellerId, factoryId, apk.getId(), signType);
        if (marketApkSignature != null && StringUtils.inString(marketApkSignature.getStatus(), ApkSignatureStatus.PENDING, ApkSignatureStatus.SIGNING)) {
            return (System.currentTimeMillis() - marketApkSignature.getUpdatedDate().getTime()) >= 60000L;
        }

        if (LongUtils.equals(SystemConstants.SUPER_MARKET_ID, apk.getOriginalMarketId()) && !LongUtils.equals(SystemConstants.SUPER_MARKET_ID, marketId)) {
            //白名单自动订阅签名，solution app也一样
            if (appWhiteListService.isAutoSubscriptionInWhiteList(apk.getOriginalMarketId(), apk.getPackageName())
                    || AppType.SOLUTION.equals(apk.getAppType())) {
                return true;
            }
            AppGlobalSubscription appGlobalSubscription = appSubscriptionService.getGlobalAppSubscribe(apk.getAppId(), marketId);
            return appGlobalSubscription != null && !StringUtils.equals(AppSubscribeStatus.UNSUBSCRIBE, appGlobalSubscription.getStatus());
        }
        return true;
    }

    private ApkSignatureInfo getMarketApkSignatureInfo(Long marketId, Long resellerId, Factory factory, Apk apk, String signType) {
        ApkSignatureInfo apkSignatureInfo = new ApkSignatureInfo();
        apkSignatureInfo.setFactoryId(factory.getId());
        apkSignatureInfo.setFactoryName(factory.getName());
        apkSignatureInfo.setSignType(signType);

        //若上传为已签名的包，验签得到是哪个厂商，在签名结果列表中对该厂商的状态显示为成功，remark标注‘This version has already signed when uploaded’，其余都为N/A
        //区分判断是否是pci7签名
        SignatureProviderName signatureProviderName = SignatureProviderName.fromValue(apk.getApkFile().getSignatureProvider());
        if (signatureProviderName != SignatureProviderName.NONE) {
            if (signatureProviderName == SignatureProviderName.fromValue(factory.getSignatureProvider())) {
                if(SignatureType.RSA_2048.equals(signType) || (SignatureType.RSA_4096.equals(signType) && BooleanUtils.isTrue(apk.getApkFile().getPci7Signed()))){
                    apkSignatureInfo.setSignatureStatus(ApkSignatureStatus.SUCCESS);
                    apkSignatureInfo.setMessage(MessageUtils.getLocaleMessage("msg.signature.message1"));
                    return apkSignatureInfo;
                }
            } else {
                apkSignatureInfo.setMessage(MessageUtils.getLocaleMessage("msg.signature.message2"));
                apkSignatureInfo.setSignatureStatus(ApkSignatureStatus.NA);
                return apkSignatureInfo;
            }
        }

        //白名单应用，正常应该是已签名包，如果不是已签名包显示N/A
        if (appWhiteListService.isPackageInSignatureWhiteList(apk.getOriginalMarketId(), apk.getPackageName())) {
            apkSignatureInfo.setMessage(MessageUtils.getLocaleMessage("msg.signature.message3"));
            apkSignatureInfo.setSignatureStatus(ApkSignatureStatus.NA);
            return apkSignatureInfo;
        }

        //1.应用市场未开启签名功能         显示N/A
        //2.使用Global签名，本市场的应用   显示N/A
        if (!signatureSupport.isSignatureEnabledBySignType(marketId, factory.getId(), signType)
                || LongUtils.equals(apk.getOriginalMarketId(), marketId)
                    && signatureSupport.isUseGlobalSignature(marketId, resellerId, factory.getId(), signType)) {
            apkSignatureInfo.setSignatureStatus(ApkSignatureStatus.NA);
            apkSignatureInfo.setMessage(MessageUtils.getLocaleMessage("msg.signature.message4"));
            return apkSignatureInfo;
        }
        MarketApkSignature marketApkSignature = getAvailableMarketApkSignature(marketId, resellerId, factory.getId(), apk, signType);
        //3.无签名                 	显示未签名
        if (marketApkSignature == null) {
            apkSignatureInfo.setSignatureStatus(ApkSignatureStatus.NONE);
            return apkSignatureInfo;
        }
        //4.等待签名                	显示等待签名
        //5.正在签名                	显示正在签名
        //6.签名失败               	显示签名失败
        //7.签名成功               	显示已签名
        apkSignatureInfo.setMessage(marketApkSignature.getMessage());
        apkSignatureInfo.setSignatureStatus(marketApkSignature.getStatus());
        return apkSignatureInfo;
    }

    /**
     * 推送任务页面显示应用签名状态
     *
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @param model      the model
     * @param apk        the apk
     */
    public void setPushTaskApkSignatureStatus(Long marketId,
                                              Long resellerId,
                                              Model model,
                                              Apk apk) {
        setPushTaskApkSignatureStatus(marketId, resellerId, model, apk, null);
    }

    public void setPushTaskApkSignatureStatus(Long marketId,
                                              Long resellerId,
                                              Model model,
                                              Apk apk,
                                              String signType) {
        //1.非PayDroid不显示，允许推送
        if (!ProductType.PAYMENT_SMART.equals(model.getProductType())) {
            return;
        }
        //2.非安卓应用               	不显示，允许推送
        //4.白名单应用               	不显示，允许推送
        //5.应用市场未开启签名功能（2048 409都关闭）         不显示，允许推送
        //6.使用Global签名，本市场的应用   不显示，允许推送
        if (!StringUtils.equals(OsType.ANDROID, apk.getOsType())
                || isApkOriginFileSignedForPushTask(apk,signType)
                || appWhiteListService.isPackageInSignatureWhiteList(apk.getOriginalMarketId(), apk.getPackageName())
                || !signatureSupport.isSignatureEnabled(marketId, model.getFactoryId())
                || isUseGlobalSignatureForPushTask(marketId, resellerId, model.getFactoryId(), apk, signType)) {
            return;
        }
        //根据三个入口分别区分如何设置签名状态
        String apkSignatureStatus = ApkSignatureStatus.NONE;
        String message = null;
        if (StringUtils.isNotBlank(signType)) {
            MarketApkSignature marketApkSignature = getAvailableMarketApkSignature(marketId, resellerId, model.getFactoryId(), apk, signType);
            if (Objects.nonNull(marketApkSignature)) {
                apkSignatureStatus = marketApkSignature.getStatus();
                message = marketApkSignature.getMessage();
            }
        } else {
            //过滤显示两种签名状态（如有）
            MarketApkSignature marketApkSignature4096 = null;
            boolean allowPci7Signature = BooleanUtils.isTrue(getCurrentMarket().getAllowPci7Signature());
            MarketApkSignature marketApkSignature = getAvailableMarketApkSignature(marketId, resellerId, model.getFactoryId(), apk, SignatureType.RSA_2048);
            //1.市场级别开启了pci7 目前必须要两个都配置 必须有对应签名数据
            //2.市场级别关闭pci7, 只需要验证2048 加载对应签名状态
            if (allowPci7Signature){
                marketApkSignature4096 = getAvailableMarketApkSignature(marketId, resellerId, model.getFactoryId(), apk, SignatureType.RSA_4096);
                if (Objects.isNull(marketApkSignature4096) || Objects.isNull(marketApkSignature)){
                    apkSignatureStatus = ApkSignatureStatus.NONE;
                } else {
                        List<String> statusList = Arrays.asList(marketApkSignature4096.getStatus(), marketApkSignature.getStatus());
                        if (statusList.stream().allMatch(status -> StringUtils.equals(status, ApkSignatureStatus.SUCCESS))) {
                            apkSignatureStatus = ApkSignatureStatus.SUCCESS;
                        } else if (statusList.contains(ApkSignatureStatus.FAILED)) {
                            apkSignatureStatus = ApkSignatureStatus.FAILED;
                            message = Objects.nonNull(marketApkSignature.getMessage()) ? marketApkSignature.getMessage() : marketApkSignature4096.getMessage(); // 优先取2048
                        } else if (statusList.contains(ApkSignatureStatus.SIGNING)) {
                            apkSignatureStatus = ApkSignatureStatus.SIGNING;
                        } else if (statusList.contains(ApkSignatureStatus.PENDING)) {
                            apkSignatureStatus = ApkSignatureStatus.PENDING;
                        }
                    }
            } else {
                if (Objects.isNull(marketApkSignature)){
                    apkSignatureStatus = ApkSignatureStatus.NONE;
                } else {
                    apkSignatureStatus = marketApkSignature.getStatus();
                    message = marketApkSignature.getMessage();
                }
            }
        }
        //7.无签名                 	显示未签名，不允许推送
        //8.等待签名                	显示等待签名，不允许推送
        //9.正在签名                	显示正在签名，不允许推送
        //10.签名失败               	显示签名失败，不允许推送
        //11.签名成功               	不显示，允许推送
        apk.setApkSignatureStatus(apkSignatureStatus);
        apk.setMessage(message);
    }


    /**
     * 判断是否使用 Global 签名，推送任务的签名状态判断
     */
    private boolean isUseGlobalSignatureForPushTask(Long marketId, Long resellerId, Long  factoryId, Apk apk, String signType) {
        if (!LongUtils.equals(apk.getOriginalMarketId(), marketId)) {
            return false;
        }

        if (!getCurrentMarket().getAllowPci7Signature()) {
            return signatureSupport.isUseGlobalSignature(marketId, resellerId, factoryId, SignatureType.RSA_2048);
        }

        if (StringUtils.isBlank(signType)) {
            return signatureSupport.isUseGlobalSignature(marketId, resellerId, factoryId, SignatureType.RSA_2048)
                    && signatureSupport.isUseGlobalSignature(marketId, resellerId, factoryId, SignatureType.RSA_4096);
        } else {
            return signatureSupport.isUseGlobalSignature(marketId, resellerId, factoryId, signType);
        }
    }
    /**
     * 原包已签名需要根据signType区分
     * 若组推送入口则需要判断2048+4096同时已签名才允许推送
     * pci7若关闭 则只需要判断2048
     */
    private boolean isApkOriginFileSignedForPushTask(Apk apk, String signType) {
        if (!getCurrentMarket().getAllowPci7Signature()) {
           signType = SignatureType.RSA_2048;
        }
        return apkService.isApkOriginFileSigned(apk, signType);
    }


    /**
     * Generate apk signature.
     *
     * @param terminal the terminal
     * @param apk      the apk
     */
    public void generateApkSignature(Terminal terminal, Apk apk) {
        if (!ProductType.PAYMENT_SMART.equals(terminal.getProductType())) {
            return;
        }
        boolean packageInSignatureWhiteList = appWhiteListService.isPackageInSignatureWhiteList(apk.getOriginalMarketId(), apk.getPackageName());
        String signType = null;
        if (!Objects.isNull(terminal.getPci7())){
            signType = terminal.getPci7() ? SignatureType.RSA_4096 : SignatureType.RSA_2048;
        }
        validateAndGetAvailableMarketApkSignature(terminal.getMarketId(),
                terminal.getResellerId(),
                terminal.getFactoryId(),
                apk,
                packageInSignatureWhiteList,
                signType);
        resellerApkSignatureSupport.generateResellerApkSignature(terminal.getMarketId(), terminal.getResellerId(), terminal.getFactoryId(), apk, false, packageInSignatureWhiteList);
    }

    /**
     * Generate apk signature.
     *
     * @param group the group
     * @param apk   the apk
     */
    public void generateApkSignature(TerminalGroup group, Apk apk) {
        modelService.loadDetails(group.getModel());
        if (!ProductType.PAYMENT_SMART.equals(group.getModel().getProductType())) {
            return;
        }
        boolean packageInSignatureWhiteList = appWhiteListService.isPackageInSignatureWhiteList(apk.getOriginalMarketId(), apk.getPackageName());
        Long factoryId = group.getModel().getFactoryId();
        validateAndGetAvailableMarketApkSignature(group.getMarketId(), group.getResellerId(), factoryId, apk, packageInSignatureWhiteList);
        resellerApkSignatureSupport.generateResellerApkSignature(group.getMarketId(), group.getResellerId(), factoryId, apk, true, packageInSignatureWhiteList);
    }

    /**
     * 获取根代理商的应用签名
     *
     * @param marketId  the market id
     * @param factoryId the factory id
     * @param apk       the apk
     * @return the market apk signature
     */
    public MarketApkSignature getAvailableMarketApkSignature(Long marketId, Long factoryId, Apk apk, String signType) {
        return getAvailableMarketApkSignature(marketId, null, factoryId, apk, signType);
    }

    /**
     * 获取代理商可用的应用签名，会一层一层往父级代理商查找
     *
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @param factoryId  the factory id
     * @param apk        the apk
     * @return the available market apk signature
     */
    public MarketApkSignature getAvailableMarketApkSignature(Long marketId, Long resellerId, Long factoryId, Apk apk, String signType) {
        Reseller reseller = LongUtils.isBlankOrNotPositive(resellerId) ? resellerService.getMarketRootReseller(marketId) : resellerService.getWithoutDataScopeCheck(resellerId);
        if (reseller == null) {
            //代理商已经被删除了，不需要签名数据
            return null;
        }
        boolean isRootReseller = reseller.getParent() == null;
        boolean isCustomSignatureEnabled = signatureSupport.isResellerCustomSignatureEnabledByType(marketId, reseller.getId(), factoryId, signType);
        //如果不是根代理商并且没有开启自定义签名，获取父代理商的签名
        if (!isRootReseller && !isCustomSignatureEnabled) {
            return getAvailableMarketApkSignature(marketId, reseller.getParentId(), factoryId, apk, signType);
        }
        //有自定义签名，直接返回自定义签名
        if (isCustomSignatureEnabled) {
            return marketApkSignatureService.getMarketApkSignature(marketId, reseller.getId(), factoryId, apk.getId(), signType);
        }
        //订阅应用，使用Global签名
        MarketAdvancedSetting advancedSetting = marketAdvancedSettingService.getByMarketId(marketId);
        if (LongUtils.equals(apk.getOriginalMarketId(), SystemConstants.SUPER_MARKET_ID)
                && !LongUtils.equals(SystemConstants.SUPER_MARKET_ID, marketId)
                && (SignatureType.RSA_4096.equals(signType) ? advancedSetting.getPaxPuk4096() : advancedSetting.getPaxPuk())) {
            return getAvailableMarketApkSignature(SystemConstants.SUPER_MARKET_ID, factoryId, apk, signType);
        }
        //不是订阅应用，没有自定义签名，无签名数据
        return null;
    }

    public void validateAndGetAvailableMarketApkSignature(Long marketId,
                                                          Long resellerId,
                                                          Long factoryId,
                                                          Apk apk,
                                                          boolean isPackageInSignatureWhiteList) {
        validateAndGetAvailableMarketApkSignature(marketId, resellerId, factoryId, apk, isPackageInSignatureWhiteList, null);
    }

    /**
     * 获取代理商可用的应用签名，会一层一层往父级代理商查找，如果找不到或者签名失败会报错
     *
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @param factoryId  the factory id
     * @param apk        the apk
     */
    public void validateAndGetAvailableMarketApkSignature(Long marketId,
                                                          Long resellerId,
                                                          Long factoryId,
                                                          Apk apk,
                                                          boolean isPackageInSignatureWhiteList,
                                                          String signType) {
        //应用市场无需签名的不检查，返回null
        if (!signatureSupport.isMarketApkSignatureEnabledForMarket(marketId, factoryId, apk, isPackageInSignatureWhiteList, signType)) {
            return;
        }
        //本市场的应用，使用Global签名，无需签名，返回null
        if (isUseGlobalSignatureForPushTask(marketId, resellerId, factoryId, apk, signType)) {
            return;
        }
        //根据终端推送和组推送 分别验证签名状态
        MarketApkSignature marketApkSignature = null;
        if (StringUtils.isNotBlank(signType) && SignatureType.TYPES.contains(signType)){
            marketApkSignature = getAvailableMarketApkSignature(marketId, resellerId, factoryId, apk, signType);
            validateMarketApkSignature(marketApkSignature);
        } else {
            for (String type : SignatureType.TYPES) {
                if (SignatureType.RSA_4096.equals(type) && BooleanUtils.isFalse(getCurrentMarket().getAllowPci7Signature())){
                     //4096 市场未开启pci7 无需验证
                    continue;
                }
                marketApkSignature = getAvailableMarketApkSignature(marketId, resellerId, factoryId, apk, type);
                validateMarketApkSignature(marketApkSignature);
            }
        }

    }

    private void validateMarketApkSignature(MarketApkSignature marketApkSignature){
        if (Objects.isNull(marketApkSignature)) {
            throw new BusinessException(ApiCodes.SIGNATURE_NOT_EXIST);
        } else if (StringUtils.equals(marketApkSignature.getStatus(), ApkSignatureStatus.PENDING) || StringUtils.equals(marketApkSignature.getStatus(), ApkSignatureStatus.SIGNING)) {
            throw new BusinessException(ApiCodes.SIGNATURE_NOT_FINISHED);
        } else if (StringUtils.equals(marketApkSignature.getStatus(), ApkSignatureStatus.FAILED)) {
            throw new BusinessException(ApiCodes.SIGNATURE_FAILED_NOARGS);
        }
    }

    /**
     * Clear apk signature data.
     *
     * @param apkId the apk id
     */
    public void clearApkSignatureData(Long apkId) {
        MarketApkSignature marketApkSignature = new MarketApkSignature();
        marketApkSignature.setApkId(apkId);
        //global库
        marketApkSignatureService.delete(marketApkSignature);
        cacheService.removeAll(CacheNames.MARKET_APK_SIGNATURE);
        //market库
        resellerApkSignatureService.deleteResellerApkSignature(null, apkId);
        EntityChangedEventAccumulator.add(PublishEntityChangedEvent.EventType.APK_MARKET_SIGNATURE_CHANGED, apkId);
    }
}
