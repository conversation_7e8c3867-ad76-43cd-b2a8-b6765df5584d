package com.pax.market.functional.vas.goinsight;

import com.pax.market.domain.insight.InsightTerminalDeviceInfo;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @create 2023/7/6
 */
public interface GoInsightTerminalBasicDataSyncFunc {
    String sendTerminalRealTimeDataToGoInsight(List<Long> resellerIds);
    String sendTerminalRealTimeDataToGoInsight(Long merchantId);

    String sendTerminalRealTimeDataToGoInsight(String marketIds, Boolean sendTerminalBasic,
                                               Boolean sendTerminalApp, Boolean sendTerminalFm, Boolean syncPending);

    String sendTerminalRealTimeDataToGoInsight(String marketIds,
                                               List<Long> resellerIds, Long merchantId,
                                               Boolean sendTerminalBasic, Boolean sendTerminalApp,
                                               Boolean sendTerminalFm, Boolean syncPending);


    void sendTerminalRealTimeDataToGoInsight(Long marketId, String terminalIds);

    void sendTerminalInfoMessage(InsightTerminalDeviceInfo insightTerminalDeviceInfo,
                                 Boolean sendTerminalBasic, Boolean sendTerminalApp, Boolean sendTerminalFm, Boolean isFormInsightReSyncReq);

    void sendMarketAllTerminalDataToGoInsight(Long marketId);

}



