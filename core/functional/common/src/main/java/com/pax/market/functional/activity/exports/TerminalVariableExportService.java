/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.functional.activity.exports;

import com.pax.core.json.JsonMapper;
import com.pax.market.constants.ActivityType;
import com.pax.market.constants.VariableType;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.variable.TerminalVariable;
import com.pax.market.functional.support.ProductTypeSupport;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.market.domain.service.variable.TerminalVariableService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.request.activity.TerminalExportRequest;
import com.pax.market.framework.common.utils.FileUtils;
import com.pax.market.framework.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.pax.market.functional.activity.util.TerminalExportRequestConverter.convertTerminalExportRequest;


/**
 * The type Terminal variable export service.
 */
@Component
public class TerminalVariableExportService extends BaseExportService<TerminalExportRequest> {
    @Autowired
    private TerminalVariableService terminalVariableService;
    @Autowired
    private AppService appService;
    @Autowired
    private ProductTypeSupport productTypeSupport;

    /**
     * Export terminal variables long.
     *
     * @param activity the task
     * @return the long
     */
    public void exportData(Activity activity) {
        String excelTitle = getLocaleMessageForExport("title.export.terminal.variables");
        String excelFileName = FileUtils.generateFileNameWithDateSuffix(getLocaleMessageForExport("excel.export.terminal.variables") + ".xlsx", activity.getTimezone());

        TerminalExportRequest terminalExportRequest = JsonMapper.fromJsonString(activity.getRequestData(), TerminalExportRequest.class);
        Terminal terminal = convertTerminalExportRequest(terminalExportRequest);
        terminal.getPage().setLimit(getExportLimit());
        terminal.getPage().setOrderBy(terminalExportRequest.getOrderBy());
        if (StringUtils.isBlank(terminalExportRequest.getModelIds()) && StringUtils.isNotBlank(terminalExportRequest.getProductType())) {
            terminal.setModelIdsFilter(productTypeSupport.findModelIdsByProductType(terminalExportRequest.getProductType()));
        }
        List<TerminalVariable> terminalVariableList = terminalVariableService.findVariableListForExport(terminal);
        for (TerminalVariable terminalVariable : terminalVariableList) {
            App app = appService.getWithDeleted(terminalVariable.getAppId());
            if (app != null){
                terminalVariable.getApp().setPackageName(app.getPackageName());
            }
            if (StringUtils.equals(VariableType.PASSWORD, terminalVariable.getType())) {
                terminalVariable.setValue(StringUtils.mask(terminalVariable.getValue()));
            }
        }

        doExport(activity, terminalVariableList, TerminalVariable.class, excelTitle, excelTitle, excelFileName);
    }

    @Override
    public int getTotalCount(TerminalExportRequest request) {
        Terminal terminal = convertTerminalExportRequest(request);
        terminal.getPage().setLimit(getExportLimit());
        return terminalVariableService.getVariableCountForExport(terminal);
    }

    @Override
    public int getExportLimit() {
        return SystemPropertyHelper.getVariableExportLimit();
    }

    @Override
    public String getActivityType() {
        return ActivityType.EXPORT_TERMINAL_VARIABLE;
    }

    @Override
    public String getActivityName(TerminalExportRequest request) {
        return String.format(getLocaleMessageForExport("title.export.terminal.variables") + "(%s)", request.getTotalCount());
    }
}
