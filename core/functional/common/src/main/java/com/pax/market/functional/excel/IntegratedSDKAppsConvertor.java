package com.pax.market.functional.excel;

import com.pax.market.framework.common.excel.ObjectFieldTypeConvertor;
import com.pax.market.framework.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
@Component("integratedSDKAppsConvertor")
public class IntegratedSDKAppsConvertor implements ObjectFieldTypeConvertor<List<String>> {

    @Override
    public List<String> convertToObject(String val) {
        if (StringUtils.isEmpty(val)) {
            return Lists.newArrayList();
        }
        String[] integratedSDKAppNames = val.split(",");
        List<String> integratedSDKAppNameList = Lists.newArrayList();
        integratedSDKAppNameList.addAll(Arrays.asList(integratedSDKAppNames));
        return integratedSDKAppNameList;

    }

    @Override
    public String convertToString(List<String> val) {
        return CollectionUtils.isEmpty(val) ? "" : StringUtils.join(val, "\n");

    }

}
