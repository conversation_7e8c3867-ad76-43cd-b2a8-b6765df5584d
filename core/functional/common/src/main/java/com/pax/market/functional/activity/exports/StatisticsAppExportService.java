/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.activity.exports;

import com.pax.core.json.JsonMapper;
import com.pax.market.constants.ActivityType;
import com.pax.market.domain.constants.MarketSummaryStatsCategory;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.excel.StatisticsAppExport;
import com.pax.market.domain.query.MarketAppStatsQuery;
import com.paxstore.global.domain.service.market.MarketAppStatsService;
import com.paxstore.global.domain.service.market.MarketSummaryStatsService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.market.MarketAppStatsInfo;
import com.pax.market.dto.request.activity.StatisticsExportRequest;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/12
 */
@Component
public class StatisticsAppExportService extends BaseExportService<StatisticsExportRequest> {

    @Autowired
    private MarketAppStatsService marketAppStatsService;

    @Autowired
    private MarketSummaryStatsService marketSummaryStatsService;

    /**
     * Export data.
     *
     * @param activity the activity
     */
    @Override
    public void exportData(Activity activity) {
        String excelTitle = getLocaleMessageForExport("title.export.apps.statistics");
        String excelFileName = FileUtils.generateFileNameWithDateSuffix(getLocaleMessageForExport("excel.export.apps.statistics") + ".xlsx", activity.getTimezone());
        String sheetTitle = getLocaleMessageForExport("title.apps");

        StatisticsExportRequest exportRequest = JsonMapper.fromJsonString(activity.getRequestData(), StatisticsExportRequest.class);
        Page<MarketAppStatsInfo> page = new Page<>();
        page.setLimit(getExportLimit());
        page.setOrderBy(exportRequest.getOrderBy());
        MarketAppStatsQuery query = MarketAppStatsQuery.create(page);
        if (exportRequest.isOnline()) {
            excelTitle = getLocaleMessageForExport("title.export.online.apps.statistics");
            excelFileName = FileUtils.generateFileNameWithDateSuffix(getLocaleMessageForExport("excel.export.online.apps.statistics") + ".xlsx", activity.getTimezone());
            sheetTitle = getLocaleMessageForExport("title.online.apps");
            query.setOnlineFlag(true);
        }
        List<MarketAppStatsInfo> marketAppStatsInfoList = marketAppStatsService.findAppStatsDetailList(query);
        doExport(activity, marketAppStatsInfoList, StatisticsAppExport.class, excelTitle, sheetTitle, excelFileName);
    }


    /**
     * Gets activity type.
     *
     * @return the activity type
     */
    @Override
    public String getActivityType() {
        return ActivityType.EXPORT_GLOBAL_STATISTICS_APP_NO;
    }

    /**
     * Gets activity name.
     *
     * @param request the request
     * @return the activity name
     */
    @Override
    public String getActivityName(StatisticsExportRequest request) {
        if (request.isOnline()) {
            return String.format(getLocaleMessageForExport("title.export.online.apps.statistics") + "(%s)", request.getTotalCount());
        }
        return String.format(getLocaleMessageForExport("title.export.apps.statistics") + "(%s)", request.getTotalCount());
    }

    /**
     * Gets total count.
     *
     * @param request the request
     * @return the total count
     */
    @Override
    public int getTotalCount(StatisticsExportRequest request) {
        if (request.getTotalCount() != null) {
            return request.getTotalCount();
        }

        int totalCount;
        if (request.isOnline()) {
            totalCount = marketSummaryStatsService.calcSumByCategory(MarketSummaryStatsCategory.ONLINE_APPS);
        } else {
            totalCount = marketSummaryStatsService.calcSumByCategory(MarketSummaryStatsCategory.TOTAL_APPS);
        }
        return totalCount;
    }

    /**
     * Gets export limit.
     *
     * @return the export limit
     */
    @Override
    public int getExportLimit() {
        return SystemPropertyHelper.getGlobalStatisticsAppExportLimit();
    }

}
