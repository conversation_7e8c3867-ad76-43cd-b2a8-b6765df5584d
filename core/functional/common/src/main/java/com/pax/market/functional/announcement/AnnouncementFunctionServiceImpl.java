/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.functional.announcement;

import com.google.common.collect.Lists;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.market.setting.AnnouncementContext;
import com.pax.market.domain.searchcriteria.UserSearchCriteria;
import com.pax.market.dto.request.announcement.AnnouncementContextRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.notification.AnnouncementNotificationService;
import com.paxstore.global.domain.service.developer.DeveloperService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.setting.AnnouncementContextService;
import com.paxstore.global.domain.service.user.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/5 15:24
 */
@FunctionalService
public class AnnouncementFunctionServiceImpl extends AbstractFunctionalService implements AnnouncementFunctionService {

    @Autowired
    private AnnouncementNotificationService announcementNotificationService;
    @Autowired
    private UserService userService;
    @Autowired
    private DeveloperService developerService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private AnnouncementContextService announcementContextService;


    @Override
    public AnnouncementContext validateAnnouncementContext(Long announcementId) {
        if (LongUtils.isBlankOrNotPositive(announcementId)) {
            throw new BusinessException(ApiCodes.ANNOUNCEMENT_NOT_FOUND);
        }
        AnnouncementContext announcementContext = announcementContextService.get(announcementId);
        if (announcementContext == null) {
            throw new BusinessException(ApiCodes.ANNOUNCEMENT_NOT_FOUND);
        }
        return announcementContext;
    }

    /**
     * publish announcement notification
     *
     * @param request the request
     */
    @Override
    public void publishAnnouncementNotification(AnnouncementContextRequest request, String status) {
        announcementContextService.createAnnouncement(request, status);
        if (BooleanUtils.isTrue(request.getImmediately()) && StringUtils.equals(AnnouncementStatus.COMPLETED, status)) {
            announcementNotificationService.sendAnnouncementNotification(
                    request.getUserId(),
                    String.valueOf(request.getReferenceId()),
                    request.getTitle(),
                    request.getContent(),
                    getReceiverIds(request.getMarketId(), request.getReceiverType()),
                    RequestLocaleHolder.getLocale());
        }

    }

    public List<Long> getReceiverIds(Long marketId, String receiverType) {
        List<Integer> receivers = StringUtils.splitToIntList(receiverType, ",");
        List<Long> result = new ArrayList<>();
        Set<Long> userIds = new HashSet<>();
        for (Integer type : receivers) {
            if (IntegerUtils.equals(type, AnnouncementType.DEVELOPER)) {
                userIds.addAll(developerService.findAllDevelopersByMarketId(marketId));
            } else if (IntegerUtils.equals(type, AnnouncementType.MARKET_ADMIN) && !receivers.contains(AnnouncementType.MARKET_USER)) {
                userIds.addAll(roleService.findUserIdForAnnouncement(marketId, Lists.newArrayList(RoleID.MARKET_ADMIN)));
            } else if (IntegerUtils.equals(type, AnnouncementType.MARKET_USER)) {
                userIds.addAll(roleService.findUserIdForAnnouncement(marketId, null));
            }
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            UserSearchCriteria userSearchCriteria = new UserSearchCriteria();
            userSearchCriteria.setUserIds(new ArrayList<>(userIds));
            userSearchCriteria.setStatus(Status.A);
            List<User> users = userService.findUsers(userSearchCriteria);
            if (CollectionUtils.isNotEmpty(users)) {
                result.addAll(users.stream().map(User::getId).collect(Collectors.toList()));
            }
        }
        return result;
    }

}
