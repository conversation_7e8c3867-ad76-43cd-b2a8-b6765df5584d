package com.pax.market.functional.terminal;

import com.pax.market.domain.entity.market.pushtask.TerminalFirmware;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalInstalledFirmware;
import com.pax.market.framework.common.persistence.Page;


/**
* <AUTHOR>
* @create 2023/9/13
*/
public interface TerminalFirmwareFunc {
    Long createTerminalFirmware(Terminal terminal, Long firmwareId);

    TerminalFirmware validateAndGetTerminalFirmware(Long terminalFirmwareId);

    TerminalFirmware validateAndGetTerminalFirmware(Long terminalFirmwareId, boolean includeDeleted);

    Page<TerminalFirmware> findPendingTerminalFirmwarePage(Page<TerminalFirmware> page, TerminalFirmware terminalFirmware);

    Page<TerminalFirmware> findHistoryTerminalFirmwarePage(Page<TerminalFirmware> page, TerminalFirmware terminalFirmware);
    Page<TerminalFirmware> findTerminalFirmwarePageFor3rd(Page<TerminalFirmware> page, TerminalFirmware terminalFirmware);

    void createTerminalFirmwareForChangeSN(Terminal terminal, TerminalInstalledFirmware terminalInstalledFirmware);
}
