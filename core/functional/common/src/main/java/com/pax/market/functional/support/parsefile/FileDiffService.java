/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.functional.support.parsefile;

import com.pax.market.mq.contract.filediff.ClientApkDiffMessage;
import com.pax.market.mq.producer.gateway.filediff.FileDiffGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * File binary difference service
 *
 * <AUTHOR>
 * @date 2017/4/21
 */
@Service
public class FileDiffService {

    @Autowired
    private FileDiffGateway fileDiffGateway;

    /**
     * Compute Client Apk binary difference
     *
     * @param clientApkId the client apk id
     */
    public void computeClientApkDiff(Long clientApkId) {
        ClientApkDiffMessage message = new ClientApkDiffMessage(clientApkId);
        fileDiffGateway.send(message);
    }
}
