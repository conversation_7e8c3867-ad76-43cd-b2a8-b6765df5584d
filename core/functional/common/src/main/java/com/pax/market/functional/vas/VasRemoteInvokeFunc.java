package com.pax.market.functional.vas;

import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.dto.request.vas.*;
import com.pax.market.dto.vas.MarketAirLinkSettingInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 9.2
 */
public interface VasRemoteInvokeFunc {

    boolean isServiceEnabled(String serviceType);

    List<VasInfo> findVasList(boolean filterEnabled);

    VasInfo getVasInfoByServiceType(String serviceType);

    VasInfo getVasInfoByServiceTypeWithoutCheck(String serviceType);

    void saveVasInfo(VasServiceRequest request);

    void syncVasInfo(VasInfo vasInfo);

    void changeFreeTill(String serviceType, boolean freeTrial, Long freeTill);

    boolean isEndpointAvailable(String serviceType, Long marketId, Long endpointId);

    void closeService(String serviceType);

    void updateServiceByList(VasStatusBatchUpdateRequest request);

    void unsubscribeAllService(Long marketId);

    void disableAllService(Long marketId);

    void saveDefaultStatusInServices(Long marketId, String fromType);

    void onVasServiceSuspendEvent(String serviceType);

    void onVasServiceActiveEvent(String serviceType);

    void disabledAllDeveloperServices(Long developerId, String fromType);

    void setFreeTrialInfo(FreeTrialRequest freeTrialRequest);

    boolean checkServiceEnabled4Reseller(String serviceType, Long marketId, Long resellerId, boolean isRoot);

    MarketAirLinkSettingInfo getMarketAirLinkSetting(Long marketId);

    void updateMarketAirLinkSetting(Long marketId, VasStatusRequest request);

}
