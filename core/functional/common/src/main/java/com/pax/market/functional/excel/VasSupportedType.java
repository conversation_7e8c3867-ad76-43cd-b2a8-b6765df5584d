package com.pax.market.functional.excel;/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.framework.common.excel.ObjectFieldTypeConvertor;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component("vasSupportedType")
public class VasSupportedType implements ObjectFieldTypeConvertor<Boolean> {
    @Override
    public Boolean convertToObject(String val) {
        if (StringUtils.equals(val.toUpperCase(), "YES")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public String convertToString(Boolean val) {
        Locale envLocale = LocaleUtils.getEnvLocale();
        if (Boolean.TRUE.equals(val)) {
            return MessageUtils.getLocaleMessage("title.yes", envLocale);
        }
        return MessageUtils.getLocaleMessage("title.no", envLocale);
    }
}
