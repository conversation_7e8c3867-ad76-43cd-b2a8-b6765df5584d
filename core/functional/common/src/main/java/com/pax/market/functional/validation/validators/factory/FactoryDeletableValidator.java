/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.factory;

import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.core.exception.BusinessException;
import com.paxstore.global.domain.service.model.FactoryService;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

public class FactoryDeletableValidator extends Validator<Factory> {

    /**
     * Instantiates a new Factory delete able validator.
     *
     * @param factory the factory
     */
    public FactoryDeletableValidator(Factory factory) {
        super(factory);
    }

    @Override
    public boolean validate() {
        FactoryService factoryService = SpringContextHolder.getBean(FactoryService.class);
        if (factoryService.isModelExist(validateTarget.getId())) {
            throw new BusinessException(ApiCodes.FACTORY_HAS_MODELS);
        }
        if (factoryService.isClientAppExist(validateTarget.getId())){
            throw new BusinessException(ApiCodes.FACTORY_HAS_CLIENT_APPS);
        }
        return true;
    }
}
