/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.functional.support;

import com.google.common.collect.Maps;
import com.pax.market.constants.CodeTypes;
import com.pax.market.constants.ProductType;
import com.pax.market.constants.ProductTypeUtils;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.global.setting.Code;
import com.pax.market.dto.product.ProductTypeStat;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.vo.admin.common.ValueTypeItemVo;
import com.pax.market.vo.admin.management.product.ProductDetailVo;
import com.pax.market.vo.admin.management.product.ProductVo;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.global.domain.service.setting.CodeService;
import com.paxstore.global.domain.service.setting.LicenseService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProductTypeSupport extends AbstractFunctionalService {

    private final CodeService codeService;
    private final ModelService modelService;
    private final LicenseService licenseService;
    private final MarketService marketService;

    /**
     * 过滤models，返回市场支持的产品类型 models
     *
     * @param marketId
     * @param models
     * @return
     */
    public List<Model> filterModelsOfProductType(Long marketId, List<Model> models) {
        return models.stream().filter(model -> {
                    String productType = model.getProductType();
                    String parentProductType = ProductTypeUtils.getParentType(productType);
                    if (StringUtils.isNotBlank(parentProductType)) {
                        return fetchProductTypes(marketId).contains(parentProductType);
                    }
                    return true;
                }

        ).collect(Collectors.toList());
    }

    /**
     *
     * 过滤factory models，返回市场支持的产品类型 models,当models为空，factory也一同过滤掉。
     *
     * @param marketId
     * @param factories
     * @return
     */
    public List<Factory> filterFactoryModelsOfProductType(Long marketId, List<Factory> factories) {
        return factories.stream().filter(each -> {
            List<Model> models = filterModelsOfProductType(marketId, each.getModelList());
            if (CollectionUtils.isNotEmpty(models)) {
                each.setModelList(models);
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }


    public List<ProductVo> getAllProjectTypes() {
        List<Code> codes = filterByLicense(codeService.getListByTypeAndLang(CodeTypes.PRODUCT_TYPE, RequestLocaleHolder.getLocale()));
        List<ProductTypeStat> modelStats = modelService.getModelCountByProductType(ProductType.PRODUCT_SUB_TYPES);
        Map<String, Integer> modelCountMap = aggregateParentProductCount(modelStats);
        return codes.stream().filter(each -> StringUtils.isEmpty(each.getParentCode())).map(each -> {
            ProductVo vo = new ProductVo();
            BeanUtils.copyProperties(each, vo);
            //初始化，默认0
            vo.setModelNum(0);
            vo.setManufacturerNum(0);

            //制造商数量直接查询,因为有重复的factoryId，所以不能使用 modelService.getFacCountByProductType 方法。
            Set<String> subTypes = ProductTypeUtils.getProductSubtypes(each.getValue());
            //增加判空，防止sql查询出错。
            if (CollectionUtils.isNotEmpty(subTypes)) {
                vo.setManufacturerNum(modelService.getFacCountOfParentProductType(subTypes.stream().toList()));
            }

            if (modelCountMap.get(each.getValue()) != null) {
                vo.setModelNum(modelCountMap.get(each.getValue()));
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 市场支持的产品类型
     *
     * @param marketId
     * @param codes
     * @return
     */
    public List<Code> filterMarketProductTypes(Long marketId, List<Code> codes) {
        return codes.stream().filter(each -> fetchProductTypes(marketId).contains(each.getValue())).toList();
    }

    public ProductDetailVo getProjectTypeDetail(String codeValue) {
        Code code = codeService.getCode(CodeTypes.PRODUCT_TYPE, codeValue, RequestLocaleHolder.getLocale());
        ProductDetailVo vo = new ProductDetailVo();
        if (Objects.isNull(code)) {
            return vo;
        }
        BeanUtils.copyProperties(code, vo);
        //初始化，默认0,空数组
        vo.setManufacturerNum(0);
        vo.setModelNum(0);
        vo.setSubProducts(Lists.newArrayList());

        //查不到，返回空，加个保护，防止sql出错
        List<String> subTypes = ProductTypeUtils.getProductSubtypes(codeValue).stream().toList();
        if (CollectionUtils.isEmpty(subTypes)) {
            return vo;
        }

        List<ProductTypeStat> fmStats = modelService.getFacCountByProductType(subTypes);
        List<ProductTypeStat> modelStats = modelService.getModelCountByProductType(subTypes);
        vo.setManufacturerNum(modelService.getFacCountOfParentProductType(subTypes));
        vo.setModelNum(modelStats.stream().mapToInt(ProductTypeStat::getCount).sum());
        List<Code> codes = code.getChildList();
        if (CollectionUtils.isNotEmpty(codes)) {
            List<ProductVo> subProjects = codes.stream().map(each -> {
                ProductVo cvo = new ProductVo();
                BeanUtils.copyProperties(each, cvo);
                cvo.setDescription(MessageUtils.getLocaleMessage(each.getRemarks()));
                //初始化，默认0
                cvo.setManufacturerNum(0);
                cvo.setModelNum(0);
                fmStats.forEach(each2 -> {
                    if (each2.getProductType().equals(each.getValue())) {
                        cvo.setManufacturerNum(each2.getCount());
                    }
                });
                modelStats.forEach(each2 -> {
                    if (each2.getProductType().equals(each.getValue())) {
                        cvo.setModelNum(each2.getCount());
                    }
                });
                return cvo;
            }).collect(Collectors.toList());
            vo.setSubProducts(subProjects);
        }
        return vo;
    }

    private Map<String, Integer> aggregateParentProductCount(List<ProductTypeStat> stats) {
        Map<String, Integer> countMap = Maps.newConcurrentMap();
        stats.forEach(each -> {
            String parentType = ProductTypeUtils.getParentType(each.getProductType());
            countMap.merge(parentType, each.getCount(), Integer::sum);
        });
        return countMap;
    }

    public List<ValueTypeItemVo> getProductTypesInfoByValues(String productTypeValue) {
        List<String> pTypes = Arrays.stream(productTypeValue.split(SystemConstants.COMMAS)).toList();
        List<Code> codes = codeService.getListByTypeAndLang(CodeTypes.PRODUCT_TYPE, RequestLocaleHolder.getLocale());
        return codes.stream().filter(each -> pTypes.contains(each.getValue())).map(each -> BeanMapper.map(each, ValueTypeItemVo.class)).toList();
    }

    public String getProductTypeLabelByValue(String productTypeValue) {
        Code product = codeService.getCode(CodeTypes.PRODUCT_TYPE, productTypeValue, RequestLocaleHolder.getLocale());
        if (Objects.nonNull(product)) {
            return product.getLabel();
        }
        return "";
    }


    public List<Code> filterByLicense(List<Code> productVos) {
        return productVos.stream().filter(each -> getGlobalProductTypes().contains(each.getValue())).toList();
    }

    /**
     * 获取市场支持的产品类型value
     *
     * @param marketId
     * @return
     */
    public List<String> fetchProductTypes(Long marketId) {
        if (SystemConstants.SUPER_MARKET_ID.equals(marketId)) {
            return getGlobalProductTypes();
        }
        return getMarketProductTypes(marketId);

    }

    private List<String> getMarketProductTypes(Long marketId) {
        Market market = marketService.get(marketId);
        String productTypes = Objects.nonNull(market) ? market.getProductTypes() : "";
        List<String> productValues = Arrays.stream(productTypes.split(SystemConstants.COMMAS)).collect(Collectors.toList());
        //附属设备不受限制
        productValues.add(ProductType.ACCESSORY);
        return productValues;
    }

    private List<String> getGlobalProductTypes() {
        String licenseProductType = licenseService.getLicenseInfo().getProductType();
        //第一上线时productType为空
        if (StringUtils.isEmpty(licenseProductType)) {
            licenseProductType = ProductType.PAYMENT;
        }
        List<String> productValues = Arrays.stream(licenseProductType.split(SystemConstants.COMMAS)).collect(Collectors.toList());
        //附属设备不受licence控制
        productValues.add(ProductType.ACCESSORY);
        return productValues;
    }

    /**
     * 判断市场是否允许使用productType
     * 有终端使用对应productType,则忽略市场检查
     *
     */
    public boolean isNotAllowProductType(String productType) {
        List<String> allMarketProductList = findCurrentMarketSubAllProductTypeList();
        if (CollectionUtils.isEmpty(allMarketProductList)) return true;
        return !allMarketProductList.contains(productType);
    }


    /**
     * 获取市场所有支持的productType 包含child
     * 如果没有child就是parent
     * 过滤license
     */
    public List<String> findCurrentMarketSubAllProductTypeList(){
        List<Code> supportProductList = findRootProductContainChildList();
        supportProductList = filterMarketProductTypes(getCurrentMarketId(), supportProductList);
        if (CollectionUtils.isEmpty(supportProductList)){
            return Lists.newArrayList();
        }
        return findSubProductTypeWithParentList(supportProductList);
    }

    public List<String> getAllSubProductTypeList(){
        List<String> productValues = Arrays.stream(licenseService.getLicenseInfo().getProductType().split(",")).toList();
        if (CollectionUtils.isEmpty(productValues)){
            return Lists.newArrayList();
        }
        return findSubProductTypeWithParentList(findRootProductContainChildList());
    }


    /**
     * 获取当前childList所有子产品类型，若没有子则直接用父产品类型
     */
    public List<String> findSubProductTypeWithParentList(List<Code> productTypeList) {
        if (CollectionUtils.isNotEmpty(productTypeList)){
            return productTypeList.stream()
                    .flatMap(code -> {
                        if (CollectionUtils.isEmpty(code.getChildList())) {
                            return Stream.of(code.getValue());
                        } else {
                            return code.getChildList().stream().map(Code::getValue);
                        }
                    })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<Code> findRootProductContainChildList(){
        List<Code> codeList = codeService.getListByTypeAndLang(CodeTypes.PRODUCT_TYPE, SystemConstants.DEFAULT_LOCALE);
        if (CollectionUtils.isEmpty(codeList)) return codeList;
        codeList = codeList.stream()
                .filter(code -> code.getParent() == null || CollectionUtils.isNotEmpty(code.getChildList()))
                .toList();

        return filterByLicense(codeList);
    }

    public Set<Long> findModelIdsByProductType(String productType) {
        return modelService.findModelIdsByProductType(ProductTypeUtils.getProductSubtypes(productType));
    }
}
