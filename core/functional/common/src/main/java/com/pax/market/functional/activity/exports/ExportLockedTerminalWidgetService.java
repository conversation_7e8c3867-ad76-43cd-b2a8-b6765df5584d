/*
 *
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *       Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 *
 */

package com.pax.market.functional.activity.exports;

import com.pax.market.constants.ActivityType;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.entity.excel.TerminalForExport;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.functional.terminal.TerminalExportFunc;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.request.activity.TerminalExportRequest;
import com.pax.market.framework.common.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/4/8 15:26
 */
@Service
@RequiredArgsConstructor
public class ExportLockedTerminalWidgetService extends BaseExportService<TerminalExportRequest> {


    private final MarketTerminalService marketTerminalService;
    private final TerminalExportFunc terminalExportFunc;

    @Override
    public void exportData(Activity activity) {
        String excelTitle = getLocaleMessageForExport("title.export.locked.terminals");
        String excelFileName = FileUtils.generateFileNameWithDateSuffix(getLocaleMessageForExport("excel.export.locked.terminals") + ".xlsx", activity.getTimezone());
        String sheetTitle = getLocaleMessageForExport("title.locked.terminals");
        Terminal terminal = new Terminal();
        terminal.setLimit(getExportLimit());
        List<Terminal> terminalList = terminalExportFunc.findLockedTerminalListForExport(terminal);
        doExport(activity, terminalList, TerminalForExport.class, excelTitle, sheetTitle, excelFileName);
    }

    @Override
    public String getActivityType() {
        return ActivityType.EXPORT_LOCKED_TERMINAL;
    }

    @Override
    public String getActivityName(TerminalExportRequest request) {
        return String.format(getLocaleMessageForExport("title.export.locked.terminals") + "(%s)", request.getTotalCount());
    }

    @Override
    public int getTotalCount(TerminalExportRequest request) {
        if (request.getTotalCount() != null) {
            return request.getTotalCount();
        }
        return marketTerminalService.getTerminalLockedCount();
    }

    @Override
    public int getExportLimit() {
        return SystemPropertyHelper.getTerminalExportLimit();
    }
}
