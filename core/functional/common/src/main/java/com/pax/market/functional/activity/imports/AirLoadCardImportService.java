package com.pax.market.functional.activity.imports;

import com.google.common.collect.Maps;
import com.pax.market.constants.ActivityStatus;
import com.pax.market.constants.ActivityType;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.vas.airload.AirLoadCard;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.request.vas.AirLoadCardPoolRequest;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.StopWatch;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.service.center.ServiceCenterFunc;
import com.paxstore.global.domain.service.vas.airload.AirLoadCardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.pax.market.functional.activity.util.TerminalImportDataValidator.*;

@Component
@RequiredArgsConstructor
public class AirLoadCardImportService extends BaseImportService<AirLoadCard> {
    private final AirLoadCardService airLoadCardService;
    private final ServiceCenterFunc serviceCenterFunc;

    @Override
    public void importData(Activity activity, List<AirLoadCard> dataList) {
    }

    @Override
    public void importData(Activity activity, List<AirLoadCard> dataList, byte[] importFileData) {
        int dataSize = dataList.size();
        StringBuilder sbForInput = new StringBuilder();
        Map<Integer, String> importErrorsForInput = Maps.newHashMapWithExpectedSize(dataSize);
        List<AirLoadCard> saveList = new ArrayList<>();
        AirLoadCard card;
        StopWatch sw = new StopWatch(String.format("Import %d cards", dataSize));
        sw.start("Validate cards");
        Long marketId = getCurrentMarketId();
        Long resellerId = getCurrentResellerId();
        Long cardPoolId = activity.getReferenceId();
        for (int i = 0; i < dataSize; i++) {
            boolean hasError = false;
            int lineNo = i + 3;
            card = dataList.get(i);
            card.setMarketId(marketId);
            card.setResellerId(resellerId);
            card.setCardPoolId(cardPoolId);
            // activationCode 合法性的校验
            String activationCode = card.getActivationCode();
            if (serialNoIsIllegal(activationCode, sbForInput, activity.getLocale())) {
                hasError = true;
            } else if (StringUtils.isNotEmpty(activationCode)) {
                //  唯一性校验
                Integer snErrorCode = uniqueCheck(activationCode);
                if (snErrorCode != null) {
                    loadStringBuilderContentWithoutLine(sbForInput, activity.getLocale(), snErrorCode, activationCode);
                    hasError = true;
                }
            }

            if (hasError) {
                loadImportError(sbForInput, lineNo, importErrorsForInput);
            } else {
                card.setPrefixCode(activationCode.substring(0, 6));
                card.setSuffixCode(activationCode.substring(activationCode.length() - 6));
                saveList.add(card);
            }
        }

        sw.stop();

        if (importErrorsForInput.size() > 0) {
            // 本地校验未通过 标记为失败
            activity.setFileId(getFileIdWithError(activity.getFileId(), importFileData, importErrorsForInput));

            String msg = StringUtils.join(convertImportErrorForView(importErrorsForInput, activity.getLocale()), "\n");
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, msg);
            return;
        } else if (saveList.isEmpty()) {
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, MessageUtils.getErrorMessage(ApiCodes.NO_VALID_IMPORT_DATA, activity.getLocale()));
        }
        if (importErrorsForInput.size() > 0) {
            importErrorsForInput = repairErrorLineNumber(importFileData, importErrorsForInput);
            activity.setFileId(getFileIdWithError(activity.getFileId(), importFileData, importErrorsForInput));

            String msg = StringUtils.join(convertImportErrorForView(importErrorsForInput, activity.getLocale()), "\n");
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, msg);
        } else if (saveList.isEmpty()) {
            activityService.updateActivityStatus(activity, ActivityStatus.FAILED, MessageUtils.getErrorMessage(ApiCodes.NO_VALID_IMPORT_DATA, activity.getLocale()));
        } else {
            sw.start("batchCreateCards");
            airLoadCardService.batchCreateCards(saveList);
            AirLoadCardPoolRequest airLoadCardPoolRequest = new AirLoadCardPoolRequest();
            airLoadCardPoolRequest.setAddQuantity(saveList.size());
            serviceCenterFunc.updateAirLoadCardPool(cardPoolId, airLoadCardPoolRequest);
            sw.stop();
            activityService.updateActivityStatus(activity, ActivityStatus.SUCCESS, MessageUtils.getErrorMessage(ApiCodes.AIR_LOAD_CARD_CREATED_SUCCESS_MSG, activity.getLocale(), saveList.size()));
        }

        if (sw.getTotalTimeMillis() > 2000) {
            logger.info(sw.prettyPrint());
        }
    }


    private Integer uniqueCheck(String activationCode) {
        if (airLoadCardService.existsSameCode(activationCode)) {
            return ApiCodes.AIR_LOAD_CARD_EXISTED;
        }
        return null;
    }

    @Override
    public String getActivityType() {
        return ActivityType.IMPORT_AIR_LOAD_CARD;
    }

    @Override
    public String getActivityName(List<AirLoadCard> dataList, Long referenceId) {
        return String.format(getLocaleMessageForImport("Import eSIM Card") + "(%s)", dataList.size());
    }

    @Override
    public Class getClazz() {
        return AirLoadCard.class;
    }

    @Override
    public int getImportLimit() {
        return SystemPropertyHelper.getTerminalImportLimit();
    }
}
