package com.pax.market.functional.support;

import com.google.common.collect.Maps;
import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.MarketSettingKey;
import com.pax.market.constants.MarketSettingKeys;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.organization.ResellerSetting;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.request.market.ResellerSettingRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.market.setting.ResellerSettingValidator;
import com.pax.market.mq.contract.file.FileDeletionMessage;
import com.pax.market.mq.producer.gateway.file.FileDeletionGateway;
import com.paxstore.domain.support.ResellerSettingSupport;
import com.paxstore.global.domain.service.market.MarketSettingService;
import com.paxstore.global.domain.service.organization.ResellerSettingService;
import com.paxstore.global.domain.service.setting.LangService;
import com.paxstore.market.domain.service.organization.ResellerService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@FunctionalService
public class MarketSettingSupport extends AbstractFunctionalService {

    private final ResellerSettingService resellerSettingService;
    private final LangService langService;
    private final ResellerService resellerService;
    private final MarketSettingService marketSettingService;
    private final ResellerSettingSupport resellerSettingSupport;


    @Transactional//多处调用
    public void updateResellerSettings(HttpServletRequest request, ResellerSettingRequest resellerSettingRequest, ResellerSettingRequest resellerSettingDeleteRequest, MarketInfo market, Long resellerId) {
        List<String> uploadedIds = new ArrayList<>();
        List<ResellerSetting> resellerSettingListAsFile = new ArrayList<>();
        List<String> fileIdsToBeDelete = new ArrayList<>();
        try {
            if (request instanceof MultipartHttpServletRequest) {
                Map<String, MultipartFile> fileMap = ((MultipartHttpServletRequest) request).getFileMap();
                if (!Collections3.isEmpty(fileMap)) {
                    loadFileMapRequest(resellerSettingListAsFile, fileMap, uploadedIds, market);
                }
            }

            List<ResellerSetting> resellerSettingListAsDelete = new ArrayList<>();
            if (resellerSettingDeleteRequest != null && CollectionUtils.isNotEmpty(resellerSettingDeleteRequest.getResellerSettingInfos())) {
                List<ResellerSetting> resellerSettingList = BeanMapper.mapList(resellerSettingDeleteRequest.getResellerSettingInfos(), ResellerSetting.class);
                for (ResellerSetting resellerSetting : resellerSettingList) {
                    if (StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement1.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement2.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement3.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement4.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement5.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement6.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.portalAdvertisement7.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement1.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement2.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement3.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement4.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement5.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement6.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalAdvertisement7.getValue())) {
                        if (!market.getAllowAdvertisement()) {
                            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
                        }
                        resellerSettingListAsDelete.add(resellerSetting);
                    } else if (StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.colorFullLogo.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.whiteLogo.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.favicon.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.terminalLogo.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.lockedScreenRemarks.getValue())
                            || StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.lockedScreenLogo.getValue())) {
                        if (!market.getAllowLogo()) {
                            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
                        }
                        resellerSettingListAsDelete.add(resellerSetting);
                    } else if (StringUtils.equals(resellerSetting.getKey(), MarketSettingKey.welcomePage.getValue())) {
                        resellerSettingListAsDelete.add(resellerSetting);
                    } else {
                        throw new BusinessException(ApiCodes.MARKET_SETTING_KEY_CAN_NOT_DELETE);
                    }
                }
                resellerSettingSupport.deleteResellerSettingList(resellerId, resellerSettingListAsDelete);
                loadFileIdsToBeDelete(resellerSettingListAsDelete, fileIdsToBeDelete, resellerId);

            }
            loadFileIdsToBeDelete(resellerSettingListAsFile, fileIdsToBeDelete, resellerId);

            List<ResellerSetting> newResellerSettingList = new ArrayList<>();
            if (resellerSettingRequest != null) {
                List<ResellerSetting> resellerSettingList = BeanMapper.mapList(resellerSettingRequest.getResellerSettingInfos(), ResellerSetting.class);
                for (ResellerSetting marketSetting : resellerSettingList) {
                    //文件如果没改的话，不重新存。也就是说目前文件不能清空
                    if (!isIgnoreFileDataCase(marketSetting.getKey())) {
                        //过滤无法找到的key
                        if (MarketSettingKey.getMarketSettingKey(marketSetting.getKey()) != null) {
                            // 如果下面文件未更改，文件不删除，不重新存
                            if (MarketSettingKey.portalTitleName.getValue().equals(marketSetting.getKey())
                                    || MarketSettingKey.storeName.getValue().equals(marketSetting.getKey())
                                    || MarketSettingKey.terminalTheme.getValue().equals(marketSetting.getKey())
                                    || MarketSettingKey.theme.getValue().equals(marketSetting.getKey())
                                    || MarketSettingKey.lockedScreenLogo.getValue().equals(marketSetting.getKey())
                                    || MarketSettingKey.lockedScreenRemarks.getValue().equals(marketSetting.getKey())
                                    || MarketSettingKey.welcomePage.getValue().equals(marketSetting.getKey())) {
                                String result = resellerSettingService.getResellerSettingString(resellerId, MarketSettingKey.getMarketSettingKey(marketSetting.getKey()));
                                if ((result == null && marketSetting.getValue() != null) || (result != null && !result.equals(marketSetting.getValue()))) {
                                    newResellerSettingList.add(marketSetting);
                                }
                            } else {
                                newResellerSettingList.add(marketSetting);
                            }
                        }
                    }
                }
            }

            newResellerSettingList.addAll(resellerSettingListAsFile);
            Validators.validate(new ResellerSettingValidator(newResellerSettingList));
            resellerSettingSupport.save(resellerId, newResellerSettingList);
            SpringContextHolder.getBean(FileDeletionGateway.class).send(new FileDeletionMessage(fileIdsToBeDelete));
        } catch (Exception e) {
            SpringContextHolder.getBean(FileDeletionGateway.class).send(new FileDeletionMessage(uploadedIds));
            throw e;
        }
    }

    private boolean isIgnoreFileDataCase(String key) {
        return (StringUtils.equals(key, MarketSettingKey.colorFullLogo.getValue())
                || StringUtils.equals(key, MarketSettingKey.whiteLogo.getValue())
                || StringUtils.equals(key, MarketSettingKey.welcomePage.getValue())
                || StringUtils.equals(key, MarketSettingKey.lockedScreenLogo.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement1.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement2.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement3.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement4.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement5.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement6.getValue())
                || StringUtils.equals(key, MarketSettingKey.portalAdvertisement7.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement1.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement2.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement3.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement4.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement5.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement6.getValue())
                || StringUtils.equals(key, MarketSettingKey.terminalAdvertisement7.getValue())
        );
    }

    private void loadFileIdsToBeDelete(List<ResellerSetting> resellerSettingList, List<String> fileIdsToBeDelete, Long resellerId) {
        for (ResellerSetting marketSetting : resellerSettingList) {
            String valueInDb = null;
            if (StringUtils.isNotEmpty(marketSetting.getLang())) {
                valueInDb = resellerSettingService.getResellerSettingString(resellerId, MarketSettingKey.getMarketSettingKey(marketSetting.getKey()), marketSetting.getLang());
            } else {
                valueInDb = resellerSettingService.getResellerSettingString(resellerId, MarketSettingKey.getMarketSettingKey(marketSetting.getKey()));
            }
            if (StringUtils.isNotEmpty(valueInDb)) {
                fileIdsToBeDelete.add(valueInDb);
            }
        }
    }

    /**
     * load fileMap Request.
     */
    private void loadFileMapRequest(List<ResellerSetting> resellerSettingListAsFile, Map<String, MultipartFile> fileMap, List<String> uploadedIds, MarketInfo market) {
        for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
            if (!StringUtils.startsWith(entry.getValue().getContentType(), SystemConstants.CONTENT_TYPE_IMAGE_PREFIX)) {
                throw new BusinessException(ApiCodes.INVALID_FILE_CONTENT_TYPE);
            }
            String key = entry.getKey();
            if (key.indexOf("#") > 0) {
                if (!market.getAllowAdvertisement()) {
                    throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
                }
                if (entry.getValue().getSize() > SystemConstants.IMAGE_FILE_MAXIMUM_SIZE) {
                    throw new BusinessException(ApiCodes.UI_SETTING_IMAGE_TOO_LARGE, null, SystemConstants.IMAGE_FILE_MAXIMUM_SIZE_MB);
                }
                String realKey = key.substring(0, key.indexOf("#"));
                String lang = "en";
                if (key.startsWith("terminal")) {
                    lang = key.substring(key.indexOf("#") + 1, key.length());
                }
                MarketSettingKey marketSettingKey = MarketSettingKey.getMarketSettingKey(realKey);
                if (marketSettingKey != null && langService.isLangCodeActive(lang)) {
                    String fileId = FileUploader.uploadImage(entry.getValue());
                    uploadedIds.add(fileId);
                    resellerSettingListAsFile.add(new ResellerSetting(marketSettingKey.getValue(), fileId, lang));
                }
            } else {
                if (!market.getAllowLogo()) {
                    throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
                }
                MarketSettingKey marketSettingKey = MarketSettingKey.getMarketSettingKey(entry.getKey());
                if (marketSettingKey != null) {
                    String fileId;
                    if (MarketSettingKey.favicon.getValue().equals(marketSettingKey.getValue())) {
                        if (entry.getValue().getSize() > SystemConstants.FAVICON_FILE_MAX_SIZE) {
                            throw new BusinessException(ApiCodes.MARKET_FAVICON_FILE_SIZE_TOO_LARGE);
                        }
                        fileId = FileUploader.uploadFile(entry.getValue(), ".ico", SupportedFileTypes.IMG);
                    } else if (MarketSettingKey.terminalLogo.getValue().equals(marketSettingKey.getValue())) {
                        if (entry.getValue().getSize() > SystemConstants.FAVICON_FILE_MAX_SIZE) {
                            throw new BusinessException(ApiCodes.TERMINAL_LOGO_FILE_SIZE_TOO_LARGE);
                        }
                        fileId = FileUploader.uploadImage(entry.getValue());
                    } else if (MarketSettingKey.welcomePage.getValue().equals(marketSettingKey.getValue())) {
                        if (entry.getValue().getSize() > SystemConstants.WELCOME_PAGE_FILE_MAXIMUM_SIZE) {
                            throw new BusinessException(ApiCodes.UI_SETTING_IMAGE_TOO_LARGE, null, SystemConstants.WELCOME_PAGE_FILE_MAXIMUM_SIZE_MB);
                        }
                        fileId = FileUploader.uploadImage(entry.getValue());
                    } else if (MarketSettingKeys.LOCKED_SCREEN_LOGO.equals(entry.getKey())) {
                        if (entry.getValue().getSize() > SystemConstants.LOCKED_SCREEN_LOGO) {
                            throw new BusinessException(ApiCodes.MARKET_FAVICON_FILE_SIZE_TOO_LARGE);
                        }
                        fileId = FileUploader.uploadImage(entry.getValue());
                    } else {
                        if (entry.getValue().getSize() > SystemConstants.IMAGE_FILE_MAXIMUM_SIZE) {
                            throw new BusinessException(ApiCodes.UI_SETTING_IMAGE_TOO_LARGE, null, SystemConstants.IMAGE_FILE_MAXIMUM_SIZE_MB);
                        }
                        fileId = FileUploader.uploadImage(entry.getValue());
                    }
                    uploadedIds.add(fileId);
                    resellerSettingListAsFile.add(new ResellerSetting(marketSettingKey.getValue(), fileId));
                }
            }
        }
    }

    public Map<String, String> getCustomizedSettings4LoginPage(Long marketId) {
        Map<String, String> result = Maps.newHashMap();
        Reseller reseller = resellerService.getMarketRootReseller(marketId);
        String logo = resellerSettingSupport.getResellerImageResourceUrl(reseller.getId(), MarketSettingKey.whiteLogo);
        String favicon = resellerSettingSupport.getResellerImageResourceUrl(reseller.getId(), MarketSettingKey.favicon);
        String storeName = resellerSettingSupport.getResellerSettingOrParentReseller(reseller.getId(), MarketSettingKey.storeName);
        String supportEmail = marketSettingService.getMarketSettingAsString(marketId, MarketSettingKey.supportEmail);
        String address = marketSettingService.getMarketSettingAsString(marketId, MarketSettingKey.address);
        if (StringUtils.isNotEmpty(logo)) {
            result.put(MarketSettingKey.whiteLogo.getValue(), logo);
        }
        if (StringUtils.isNotEmpty(favicon)) {
            result.put(MarketSettingKey.favicon.getValue(), favicon);
        }
        result.put(MarketSettingKey.storeName.getValue(), storeName);
        result.put(MarketSettingKey.supportEmail.getValue(), supportEmail);
        result.put(MarketSettingKey.address.getValue(), address);

        return result;
    }

    public void deleteUIAdvanceSetting() {
        List<ResellerSetting> resellerSettingList = new ArrayList<>();
        resellerSettingList.add(new ResellerSetting(MarketSettingKey.lockedScreenLogo.getValue(), null));
        resellerSettingList.add(new ResellerSetting(MarketSettingKey.lockedScreenRemarks.getValue(), null));
        String fileId = resellerSettingService.getResellerSettingString(getCurrentResellerId(), MarketSettingKey.lockedScreenLogo);
        resellerSettingSupport.deleteResellerSettingList(getCurrentResellerId(), resellerSettingList);
        if (StringUtils.isNotBlank(fileId)) {
            SpringContextHolder.getBean(FileDeletionGateway.class).send(new FileDeletionMessage(fileId));
        }
    }
}
