/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.app;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.AppSwitchStatus;
import com.pax.market.domain.entity.global.app.App;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;

/**
 * Created by gubin_ on 2016/11/1.
 */
public class AppStatusOfflineValidator extends Validator<App> {
    /**
     * Instantiates a new App status offline validator.
     *
     * @param app the app
     */
    public AppStatusOfflineValidator(App app) {
        super(app);
    }

    @Override
    public boolean validate() {

        if (validateTarget == null) {
            throw new BusinessException(ApiCodes.APP_NOT_FOUND);
        }

        if (!AppSwitchStatus.SUSPEND.equals(validateTarget.getStatus())) {
            throw new BusinessException(ApiCodes.APP_NOT_SUSPEND);
        }

        return false;
    }
}
