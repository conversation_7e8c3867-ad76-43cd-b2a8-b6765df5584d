/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.market;

import com.pax.market.constants.ApiCodes;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.request.market.FeedbackCreateRequest;

/**
 * The type Feedback create request validator.
 */
public class FeedbackCreateRequestValidator extends Validator<FeedbackCreateRequest> {

    /**
     * Instantiates a new Feedback create request validator.
     *
     * @param feedbackCreateRequest the feedback create request
     */
    public FeedbackCreateRequestValidator(FeedbackCreateRequest feedbackCreateRequest) {
        super(feedbackCreateRequest);
    }

    @Override
    public boolean validate() {

        if (validateTarget.getTitle() == null) {
            throw new BusinessException(ApiCodes.FEEDBACK_TITLE_INVALID);
        }

        if (validateTarget.getContent() == null) {
            throw new BusinessException(ApiCodes.FEEDBACK_CONTENT_INVALID);
        }

        return false;
    }

}
