/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.app;

import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.app.App;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;


/**
 * The type App owner validator.
 */
public class AppOwnerValidator extends Validator<App> {

    /**
     * Instantiates a new App owner validator.
     *
     * @param app the app
     */
    public AppOwnerValidator(App app) {
        super(app);
    }

    @Override
    public boolean validate() {
        if (validateTarget == null) {
            throw new BusinessException(ApiCodes.APP_NOT_FOUND);
        }

        CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);
        MarketInfo currentMarket = currentLoginProvider.getCurrentMarketInfo();
        if (currentMarket == null || !LongUtils.equals(validateTarget.getMarketId(), currentMarket.getId())) {
            throw new BusinessException(ApiCodes.APP_NOT_FOUND);
        }
        return false;
    }
}
