package com.pax.market.functional.launcher;

import com.google.common.collect.Maps;
import com.pax.core.exception.BusinessException;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.market.app.LauncherTemplate;
import com.pax.market.domain.query.AppApkQuery;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.market.domain.service.app.LauncherTemplateService;
import com.pax.market.functional.support.AppEntitySupport;
import com.pax.market.functional.utils.developer.DeveloperUtils;
import com.paxstore.global.domain.utils.EntityUtils;
import com.pax.market.domain.util.WebCryptoUtils;
import com.pax.market.dto.LauncherTemplateInfo;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.vo.admin.management.group.launcher.ApkNameIconVo;
import com.pax.market.vo.admin.management.template.ResellerAppVo;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@FunctionalService
@RequiredArgsConstructor
public class LauncherTemplateFuncImpl extends AbstractFunctionalService implements LauncherTemplateFunc {

    private final LauncherTemplateService launcherTemplateService;
    private final AppService appService;
    private final ApkService apkService;
    private final AppEntitySupport appEntitySupport;


    @Override
    public PageInfo<LauncherTemplateInfo> findLauncherTemplatePage(String name) {
        LauncherTemplate launcherTemplate = new LauncherTemplate();
        launcherTemplate.setName(StringUtils.trim(name));
        Page<LauncherTemplate> launcherTemplatePage = launcherTemplateService.findPage(parsePage(-1), launcherTemplate);
        List<LauncherTemplateInfo> resultList = Lists.newArrayList();
        for (LauncherTemplate each : launcherTemplatePage.getList()) {
            LauncherTemplateInfo launcherTemplateInfo = BeanMapper.map(each, LauncherTemplateInfo.class);
            launcherTemplateInfo.setSchema(getTypeEnabledMap(each.getParam()));
            resultList.add(launcherTemplateInfo);
        }
        return new PageInfo<>(resultList, launcherTemplatePage.getCount());
    }

    @Override
    public LauncherTemplateInfo getLauncherTemplate(Long launcherTemplateId) {
        LauncherTemplate launcherTemplate = launcherTemplateService.get(launcherTemplateId);
        checkNotNull(launcherTemplate, ApiCodes.LAUNCHER_TEMPLATE_NOT_FOUND);
        launcherTemplate.setParam(WebCryptoUtils.appendFileDownloadUrl(getCurrentMarketId(), launcherTemplate.getParam()));
        LauncherTemplateInfo launcherTemplateInfo = BeanMapper.map(launcherTemplate, LauncherTemplateInfo.class);
        Map map = JsonMapper.fromJsonString(launcherTemplate.getParam(), Map.class);
        WebCryptoUtils.maskPasswordAirLauncher(map);
        launcherTemplateInfo.setSchema(map);
        return launcherTemplateInfo;
    }


    @Override
    public PageInfo<ResellerAppVo> findResellerOnlineAppPage(String name) {
        AppApkQuery appListQuery = new AppApkQuery();
        appListQuery.setKeyWords(StringUtils.trim(name));
        appListQuery.setDeveloperIds(DeveloperUtils.getDeveloperIdsByKeyWords(appListQuery.getKeyWords()));
        appListQuery.setStatus(AppStatus.ONLINE);
        appListQuery.setResellerId(getCurrentResellerId());
        appListQuery.setExcludePackageNames(SystemConstants.VAS_APP_PACKAGE_NAMES);
        if (!isSuperMarket() && BooleanUtils.isTrue(getCurrentMarket().getAllowAppOfflinePurchase())) {
            appListQuery.setFilterGlobalAppCharge(true);
        }
        Page<App> appPage = appService.findPageForReseller(parsePage(), appListQuery);
        DeveloperUtils.loadDevelopersEmailAndName(appPage.getList().stream().map(App::getDeveloper).collect(Collectors.toList()));
        List<ResellerAppVo> resellerAppVos = appPage.getList().stream().map(app -> {
            appEntitySupport.loadAppSpecificInfo(app, true);
            EntityUtils.loadLatestOnlineApk(getCurrentMarketId(), app);
            return ResellerAppVo.builder()
                    .apkIconFileId(Objects.isNull(app.getLatestOnlineApk()) ? null : app.getLatestOnlineApk().getApkIconFileId())
                    .apkType(Objects.isNull(app.getLatestOnlineApk()) ? null : app.getLatestOnlineApk().getApkType())
                    .appName(Objects.isNull(app.getLatestOnlineApk()) ? null : app.getLatestOnlineApk().getAppName())
                    .id(app.getId())
                    .developerNickName(app.getDeveloper().getNickname())
                    .packageName(app.getPackageName())
                    .build();
        }).collect(Collectors.toList());
        return new PageInfo<>(resellerAppVos, appPage.getCount());
    }

    @Override
    public ApkNameIconVo getResellerOnlineApkNameAndIcon(HttpServletRequest request) {
        String packageName = extractPath(request);
        AppApkQuery appApkQuery = new AppApkQuery();
        appApkQuery.setFilterAppSubscribe(true);
        EntityUtils.setApkSpecificFilters(appApkQuery);
        appApkQuery.setAppStatus(AppSwitchStatus.ACTIVE);
        appApkQuery.setPackageName(packageName);
        Long apkId = apkService.getLatestApkId(getCurrentMarketId(), null, AppStatus.ONLINE, appApkQuery);
        if (LongUtils.isBlankOrNotPositive(apkId)) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }
        Apk apk = apkService.get(apkId);
        checkNotNull(apk, ApiCodes.APK_NOT_FOUND);
        apkService.generateApkIconScreenshotLink(apk);
        return BeanMapper.map(apk, ApkNameIconVo.class);
    }

    private Map<String, Map<String, String>> getTypeEnabledMap(String param) {
        Map<String, Object> paramMap = JsonMapper.fromJsonString(param, Map.class);
        if (paramMap == null) {
            return null;
        }
        Map<String, Map<String, String>> typeEnabledMap = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if (entry.getValue() instanceof Map) {
                Map<String, String> enabledMap = new HashMap<>();
                enabledMap.put("enable", (String) ((Map) entry.getValue()).get("enable"));
                typeEnabledMap.put(entry.getKey(), enabledMap);
            }
        }
        return typeEnabledMap;
    }
}
