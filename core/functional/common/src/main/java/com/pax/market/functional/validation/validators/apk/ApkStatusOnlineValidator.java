/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.apk;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.AppStatus;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;

/**
 * Created by liukai on 2017/05/17.
 */
public class ApkStatusOnlineValidator extends Validator<Apk> {
    /**
     * Instantiates a new App status online validator.
     *
     * @param apk the apk
     */
    public ApkStatusOnlineValidator(Apk apk) {
        super(apk);
    }

    @Override
    public boolean validate() {

        if (validateTarget == null) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }

        if (!AppStatus.ONLINE.equals(validateTarget.getStatus())) {
            throw new BusinessException(ApiCodes.APP_NOT_ONLINE);
        }

        return false;
    }
}
