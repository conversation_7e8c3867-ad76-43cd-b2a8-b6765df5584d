package com.pax.market.functional.constants;

/**
 * HTTP Header 请求头key
 */
public interface ApiHeader {
    String MARKET = "X-Market-Domain";

    String MARKET_ID = "X-Market-ID";

    String TERMINAL = "X-Terminal-SN";

    String MARKET_HOST = "X-Market-Host";


    String RESELLER = "X-Reseller-ID";

    String TOKEN_HEADER = "Authorization";

    String CLIENT_ID = "X-Client-ID";

    String MERCHANT = "X-Merchant-ID";

    String SIGNATURE = "Signature";

    String ACCEPT = "Accept";

    String CONTENT_TYPE = "Content-Type";


    /**
     * webHook header
     **/
    String REQUEST_URL = "Request-Url";

    String REQUEST_METHOD = "Request-Method";

    String USER_AGENT = "User-Agent";

    String WEBHOOK_TOKEN = "X-Webhook-Token";

    String WEBHOOK_TIMESTAMP = "X-Webhook-Timestamp";

    String WEBHOOK_EVENT = "X-Webhook-Event";

    String CAPTCHA_HEADER = "X-Captcha";

    String MOBILE_TOKEN = "X-Mobile-Token";
    String MOBILE_VERSION_NAME = "X-Mobile-VersionName";
    String MOBILE_VERSION_CODE = "X-Mobile-VersionCode";
    //未使用，先保留,Mobile APP端已传
    String MOBILE_PLATFORM = "X-Mobile-Platform";
    String MOBILE_AUTH = "X-Mobile-Auth";//Username,Biometrics,Gesture
}
