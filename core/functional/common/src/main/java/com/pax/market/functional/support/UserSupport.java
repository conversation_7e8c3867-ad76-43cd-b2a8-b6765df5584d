package com.pax.market.functional.support;

import com.google.common.collect.Lists;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.RoleID;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.UserStatus;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.searchcriteria.UserSearchCriteria;
import com.pax.market.dto.UserInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.exception.http.NotFoundException;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.role.UserRoleSupport;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.setting.SsoSettingService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@FunctionalService
@RequiredArgsConstructor
public class UserSupport extends AbstractFunctionalService {

    private final MerchantService merchantService;
    private final ResellerService resellerService;
    private final UserRoleSupport userRoleSupport;
    private final MarketService marketService;
    private final SsoSettingService ssoSettingService;
    private final RoleService roleService;
    private final UserService userService;

    /**
     * 代理商级别，此代理商是否可见该user，不可见则不允许相关操作
     *
     * @param currentUser       the current user
     * @param user              the user
     * @param currentResellerId the current reseller id
     * @param currentMarketId   the current market id
     */
    public void assertUserHasOperationPermission(UserInfo currentUser, User user, Long currentResellerId, Long currentMarketId) {
        if (user == null) {
            throw new BusinessException(ApiCodes.USER_NOT_FOUND);
        }
        if (currentResellerId != null) {
            if (currentUser == null || !LongUtils.equals(SystemConstants.SUPER_MARKET_ID, currentMarketId)) {
                //代理商检查角色匹配 在代理商的用户列表的,只能是有子代理商或者当前代理商的角色,有一个就可见
                List<Reseller> resellerList = userRoleSupport.findUserResellers(user.getId(), Collections.singletonList(currentMarketId), false);
                boolean allowView = false;
                if (CollectionUtils.isNotEmpty(resellerList)) {
                    for (Reseller reseller : resellerList) {
                        if (reseller.getId().equals(currentResellerId) || reseller.getParentIds().contains(currentResellerId.toString())) {
                            allowView = true;
                            break;
                        }
                    }
                }
                if (CollectionUtils.isEmpty(resellerList) || !allowView) {
                    throw new BusinessException(ApiCodes.USER_NOT_FOUND);
                }
            }
        } else {
            //开发者check用户是否属于自己
            if (currentUser.getCurrentDeveloper() != null && !user.getId().equals(currentUser.getId())) {
                throw new BusinessException(ApiCodes.USER_NOT_FOUND);
            }
        }
    }


    public void assertUserHasOperationPermission(User user) {
        if (user == null) {
            throw new BusinessException(ApiCodes.USER_NOT_FOUND);
        }
        List<Long> childResellerIdsIncludeSelf = resellerService.findChildResellerIdsIncludeSelf(BeanMapper.map(getCurrentReseller(), Reseller.class), false);
        if (!merchantService.checkMerchantUserExist(childResellerIdsIncludeSelf, user.getId())) {
            throw new BusinessException(ApiCodes.USER_NOT_FOUND);
        }
    }

    public boolean isExternalUser(String email){
        //1.检查当前user是否是任意激活市场的rootAdmin 2.检查domain是否配置有sso市场配置
        email = StringUtils.trim(email);
        if (marketService.isRootAdminInMarket(email)){
            return false;
        }else {
            List<Market> markets = ssoSettingService.findMarketAllowSsoByEmail(email);
            return CollectionUtils.isNotEmpty(markets);
        }
    }


    /**
     * 获取市场管理员用户id列表
     */
    public Set<Long> getMarketAdminUserIds(Long marketId) {
        Set<Long> result = new HashSet<>();
        Reseller rootReseller = resellerService.getMarketRootReseller(marketId);
        if (rootReseller == null) {
            throw new NotFoundException(ApiCodes.RESELLER_NOT_EXIST);
        }
        Set<Long> userIds =
                roleService.findUserIdByMarketIdAndRoleId(
                        marketId,
                        Lists.newArrayList(RoleID.MARKET_ADMIN),
                        rootReseller.getId());

        if (CollectionUtils.isNotEmpty(userIds)) {
            UserSearchCriteria userSearchCriteria = new UserSearchCriteria();
            userSearchCriteria.setUserIds(new ArrayList<>(userIds));
            userSearchCriteria.setStatus(UserStatus.ACTIVE);
            List<User> users = userService.findUsers(userSearchCriteria);
            if (CollectionUtils.isNotEmpty(users)) {
                result.addAll(users.stream().map(User::getId).toList());
            }
        }
        return result;
    }

}
