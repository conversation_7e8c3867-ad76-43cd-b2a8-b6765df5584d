/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.functional.utils;

import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.parameter.ParameterUtils;
import com.pax.market.domain.parameter.SchemaProcess;
import com.pax.market.dto.parameter.ParameterInfo;
import com.pax.market.framework.common.exception.ServiceException;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.ZipUtils;
import com.pax.market.framework.common.utils.FileUtils;
import com.paxstore.global.domain.utils.ApkParamTemplateUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Utils for APK parameter
 *
 * <AUTHOR>
 * @date Apr 9, 2018
 */
@Slf4j
public class ApkParameterParserUtils {

    private ApkParameterParserUtils() {
    }

    /**
     * Sets param template.
     *
     * @param apk               the apk
     * @param paramTemplateName the param template name
     * @param fileBytes         the file bytes
     */
    public static void setParamTemplate(Apk apk, String paramTemplateName, byte[] fileBytes) {
        ApkParamTemplate apkParamTemplate = parseParamTemplate(paramTemplateName, fileBytes, false, true);
        setParamTemplate(apk, apkParamTemplate);
    }

    /**
     * Parse param template apk param template.
     *
     * @param paramTemplateName the param template name
     * @param fileBytes         the file bytes
     * @return the apk param template
     */
    public static ApkParamTemplate parseParamTemplate(String paramTemplateName, byte[] fileBytes, boolean allowEmptyGroups, boolean uploadZipFile) {
        if (fileBytes.length > SystemConstants.APK_PARAM_TEMPLATE_MAXIMUM_SIZE) {
            throw new BusinessException(ApiCodes.APK_PARAM_TEMPLATE_TOO_LARGE);
        }
        if (!Pattern.compile(SystemConstants.FILE_NAME_REG_EXPR).matcher(paramTemplateName).matches()) {
            throw new BusinessException(ApiCodes.APK_PARAMETER_NAME_INVALID);
        }

        List<String> entryNames;
        String schemaXml;
        try {
            entryNames = ZipUtils.getEntryNames(fileBytes);
            schemaXml = getParamTemplateXml(paramTemplateName, fileBytes, entryNames);
        } catch (Exception ex) {
            if (ex instanceof BusinessException) {
                throw (BusinessException) ex;
            } else {
                log.warn("Error retrieving parameter xml from parameter zip file", ex);
                throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, MessageUtils.getLocaleMessage("msg.invalid.parameter.template", paramTemplateName, "\n" + MessageUtils.getLocaleMessage("msg.invalid.parameter.parse.error")));
            }
        }

        SchemaProcess schemaProcess = SchemaProcess.create().allowEmptyGroups(allowEmptyGroups).validateInputType(true).parseXml(schemaXml);
        if (StringUtils.isNotBlank(schemaProcess.getInvalidMsg())) {
            log.warn("Invalid param template file: {}", schemaProcess.getInvalidMsg());
            throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, MessageUtils.getLocaleMessage("msg.invalid.parameter.template", paramTemplateName, schemaProcess.getInvalidMsg()));
        }

        Map<String, String> fileParamMap = getFileParamMap(schemaProcess.getAllParameterMap(), fileBytes, entryNames);
        //对于无法编辑的值，需要验证其有效性，否则推送的时候用户无法修改成正确的
        validateReadonlyParams(paramTemplateName, schemaProcess, fileParamMap);

        String zipFileUrl = null;
        if (!entryNames.isEmpty() && uploadZipFile) {
            zipFileUrl = FileUploader.uploadFile(fileBytes, "paramtpl", ".zip", SupportedFileTypes.APP_PARAM);
        }

        ApkParamTemplate apkParamTemplate = new ApkParamTemplate();
        apkParamTemplate.setName(paramTemplateName);
        apkParamTemplate.setParamTemplate(schemaXml);
        apkParamTemplate.setParam(JsonMapper.toJsonString(fileParamMap));
        apkParamTemplate.setZipFileUrl(zipFileUrl);
        apkParamTemplate.setAllRelatedFileNames(schemaProcess.getAllRelatedFileNames());

        return apkParamTemplate;
    }

    private static void setParamTemplate(Apk apk, ApkParamTemplate apkParamTemplate) {
        if (apk.getParamTemplateList() == null) {
            return;
        }
        for (ApkParamTemplate each : apk.getParamTemplateList()) {
            if (StringUtils.equals(each.getName(), apkParamTemplate.getName())) {
                each.setParamTemplate(apkParamTemplate.getParamTemplate());
                each.setParam(apkParamTemplate.getParam());
                each.setZipFileUrl(apkParamTemplate.getZipFileUrl());
                break;
            }
        }
    }

    /**
     * 获取参数模板文件
     */
    private static String getParamTemplateXml(String paramTemplateName, byte[] fileBytes, List<String> entryNames) throws UnsupportedEncodingException {
        if (!entryNames.isEmpty()) {
            List<String> paramTemplateNameList = entryNames.stream().filter(each -> each.endsWith(".xml") || each.endsWith(".ps")).collect(Collectors.toList());
            List<byte[]> paramTemplateXmlBytesList = ZipUtils.getEntryBytes(fileBytes, paramTemplateNameList);
            List<String> paramTemplateXmlList = new ArrayList<>();
            for (byte[] paramTemplateXmlBytes : paramTemplateXmlBytesList) {
                if (paramTemplateXmlBytes != null && StringUtils.contains(new String(paramTemplateXmlBytes, StandardCharsets.UTF_8), "<Schema>")) {
                    paramTemplateXmlList.add(new String(paramTemplateXmlBytes, FileUtils.getXmlFileCharset(paramTemplateXmlBytes)));
                }
            }
            if (paramTemplateXmlList.isEmpty()) {
                log.warn("No param template xml file found in the zip file: {}", paramTemplateName);
                throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, MessageUtils.getLocaleMessage("msg.invalid.parameter.template", paramTemplateName, "\n" + MessageUtils.getLocaleMessage("msg.invalid.parameter.xml")));
            }

            return ApkParamTemplateUtils.combineParamTemplate(paramTemplateXmlList);
        } else {
            return new String(fileBytes, FileUtils.getXmlFileCharset(fileBytes));
        }
    }

    /**
     * 获取参数模板中关联的文件的URL
     */
    private static Map<String, String> getFileParamMap(Map<String, ParameterInfo> allParameterMap, byte[] fileBytes, List<String> entryNames) {
        Map<String, String> fileParamMap = new HashMap<>();
        if (!entryNames.isEmpty()) {
            for (Map.Entry<String, ParameterInfo> entry : allParameterMap.entrySet()) {
                if (!StringUtils.equalsIgnoreCase(entry.getValue().getInputType(), "upload")) {
                    continue;
                }
                for (String entryName : entryNames) {
                    if (entryName.equals(entry.getValue().getDefaultValue()) || entryName.endsWith("/" + entry.getValue().getDefaultValue())) {
                        byte[] entryBytes = ZipUtils.getEntryBytes(fileBytes, entryName);
                        String fileUrl = FileUploader.uploadFile(entryBytes, "paramdata", ".data", SupportedFileTypes.APP_PARAM);
                        fileParamMap.put(entry.getValue().getWebFormPid() + SystemConstants.URL_PID_SUFFIX, fileUrl);
                    }
                }
            }
        }

        return fileParamMap;
    }

    private static void validateReadonlyParams(String paramTemplateName, SchemaProcess schemaProcess, Map<String, String> fileParamMap) {
        StringBuilder invalidMsg = new StringBuilder();
        for (ParameterInfo parameterInfo : schemaProcess.getUsedParameterInfoList()) {
            if (parameterInfo.isEditable() && StringUtils.isBlank(parameterInfo.getViewableRoles())) {
                continue;
            }
            if (StringUtils.equalsIgnoreCase(parameterInfo.getInputType(), "upload")) {
                parameterInfo.setUrl(StringUtils.trimToEmpty(fileParamMap.get(parameterInfo.getFormPidWithFileId() + SystemConstants.URL_PID_SUFFIX)));
            }
            try {
                ParameterUtils.validateParameterValue(parameterInfo, true, false);
            } catch (ServiceException ex) {
                log.warn("validateReadonlyParams error", ex);
                invalidMsg.append("\n").append(parameterInfo.getGroupTitle()).append(" - ").append(parameterInfo.getTitle()).append(" : ")
                        .append(MessageUtils.getLocaleMessage(ex.getMessage(), ex.getArgObjects()));
            }
        }
        if (StringUtils.isNotBlank(invalidMsg)) {
            throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, MessageUtils.getLocaleMessage("msg.invalid.parameter.template", paramTemplateName, invalidMsg));
        }
    }
}
