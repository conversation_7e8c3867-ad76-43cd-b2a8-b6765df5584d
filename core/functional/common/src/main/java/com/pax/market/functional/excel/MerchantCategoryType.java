/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.functional.excel;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.pax.market.domain.entity.market.setting.MerchantCategory;
import com.pax.market.framework.common.excel.ObjectFieldTypeConvertor;
import com.pax.market.framework.common.utils.StringUtils;

@Component("merchantCategoryType")
public class MerchantCategoryType implements ObjectFieldTypeConvertor<List<MerchantCategory>> {
    @Override
    public List<MerchantCategory> convertToObject(String val) {
        if (StringUtils.isEmpty(val)) {
            return new ArrayList<>();
        }

        String[] categoryNames = val.split(",");

        List<MerchantCategory> merchantCategoryList = new ArrayList<>();

        for (String name : categoryNames) {
            MerchantCategory merchantCategory = new MerchantCategory();
            merchantCategory.setName(name);

            merchantCategoryList.add(merchantCategory);
        }

        return merchantCategoryList;

    }

    @Override
    public String convertToString(List<MerchantCategory> val) {

        if (CollectionUtils.isEmpty(val)) {
            return "";
        }

        StringBuilder categoryStr = new StringBuilder();

        if (val.get(0) != null && StringUtils.isNotEmpty(val.get(0).getName())) {
            categoryStr = new StringBuilder(val.get(0).getName());
        }

        for (int i = 1; i < val.size(); i++) {
            MerchantCategory merchantCategory = val.get(i);
            if (StringUtils.isNotEmpty(merchantCategory.getName())) {
                categoryStr.append(",").append(merchantCategory.getName());
            }
        }

        return categoryStr.toString();
    }
}
