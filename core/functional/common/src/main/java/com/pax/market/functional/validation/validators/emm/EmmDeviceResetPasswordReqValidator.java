package com.pax.market.functional.validation.validators.emm;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.util.PasswordUtils;
import com.pax.market.dto.request.emm.EmmDeviceResetPasswordRequest;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.validation.Validator;

import static com.pax.market.constants.emm.EmmConstants.REQUIRED_RESET_PW_ANDROID_VERSION;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
public class EmmDeviceResetPasswordReqValidator extends Validator<EmmDeviceResetPasswordRequest> {

    public EmmDeviceResetPasswordReqValidator(EmmDeviceResetPasswordRequest emmDeviceResetPasswordRequest) {
        super(emmDeviceResetPasswordRequest);
    }

    @Override
    public boolean validate() {
        if(validateTarget.getAndroidVersion() == null) {
            throw new BusinessException(ApiCodes.BAD_REQUEST);
        }
        int minPasswordLength = StringUtils.versionNotLessThan(validateTarget.getAndroidVersion(), REQUIRED_RESET_PW_ANDROID_VERSION) ? 6 : 4;
        if(StringUtils.isEmpty(validateTarget.getPassword()) || StringUtils.isEmpty(validateTarget.getConfirmPassword())) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        String decryptPassword = PasswordUtils.decryptWebPassword(validateTarget.getPassword());
        String decryptConfirmPassword = PasswordUtils.decryptWebPassword(validateTarget.getConfirmPassword());
        if (decryptPassword.trim().length() < minPasswordLength) {
            throw new BusinessException(ApiCodes.EMM_DEVICE_PASSWORD_TOO_SHORT, null, String.valueOf(minPasswordLength));
        }
        if(!StringUtils.equals(decryptPassword, decryptConfirmPassword)) {
            throw new BusinessException(ApiCodes.CONFIRM_PASSWORD_MISMATCH);
        }
        return true;
    }
}
