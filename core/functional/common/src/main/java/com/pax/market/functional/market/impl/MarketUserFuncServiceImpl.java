/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.market.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.developer.Developer;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.global.user.UserMarket;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.role.Role;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.sso.SsoMarketRoleInfo;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.market.MarketUserFuncService;
import com.pax.market.functional.role.UserRoleSupport;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.domain.support.SignatureSupport;
import com.paxstore.global.domain.cachable.LocalCacheMarketInfoService;
import com.paxstore.global.domain.service.developer.DeveloperPaymentService;
import com.paxstore.global.domain.service.developer.DeveloperService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.paxstore.global.domain.service.setting.SsoSettingService;
import com.paxstore.global.domain.service.user.UserMarketService;
import com.paxstore.global.domain.service.user.UserRoleService;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarketUserFuncServiceImpl extends AbstractFunctionalService implements MarketUserFuncService {

    private final DeveloperService developerService;
    private final DeveloperPaymentService developerPaymentService;
    private final LicenseService licenseService;
    private final ResellerService resellerService;
    private final MerchantService merchantService;
    private final SignatureSupport signatureSupport;
    private final MarketService marketService;
    private final RoleService roleService;
    private final UserRoleSupport userRoleSupport;
    private final UserRoleService userRoleService;
    private final UserMarketService userMarketService;
    private final SsoSettingService ssoSettingService;
    private final LocalCacheMarketInfoService marketInfoService;

    public Set<Long> getCurrentAvailableResellerIds(Long marketId, boolean includeMerchantRelatedResellers) {
        Set<Long> queryResellerIds = Sets.newHashSet();
        if (getCurrentUser() != null) {
            queryResellerIds.addAll(userRoleSupport.findUserResellerIds(getCurrentUserId(), marketId));

            if (includeMerchantRelatedResellers) {
                List<Merchant> merchantList = merchantService.findMerchantListByUserId(marketId, getCurrentUserId());
                for (Merchant merchant : merchantList) {
                    if (merchant != null && merchant.getReseller() != null
                            && LongUtils.isNotBlankAndPositive(merchant.getReseller().getId())) {
                        queryResellerIds.add(merchant.getReseller().getId());
                    }
                }
            }
        }

        return queryResellerIds;
    }


    public void loadUserDeveloperInfo(User user, MarketInfo marketInfo) {
        if (user == null || marketInfo == null) return;
        user.setDeveloper(developerService.isUserDeveloperExist(user.getId(), marketInfo.getId()));
        user.setHasDeveloper(developerService.isUserDeveloperExist(user.getId(), null));
        Developer developer = developerService.getByUser(user.getId(), marketInfo.getId());
        if (developer != null && licenseService.getLicenseInfo().isAllowPaidDeveloper()) {
            developer.setDeveloperPayment(developerPaymentService.getByDeveloperId(developer.getId()));
        }
        if (developer != null && developer.getUser() != null && DeveloperType.ENTERPRISE.equals(developer.getDeveloperType()) && developer.getUser().getId().equals(user.getId())) {
            developer.setOwner(true);
        }
        user.setCurrentDeveloper(developer);
    }

    @Override
    public List<Reseller> loadUserResellerRoleInfo(User user, MarketInfo marketInfo, String resellerHeader) {
        if (user == null || marketInfo == null) return Lists.newArrayList();
        Long resellerId = -1L;
        List<Reseller> userResellerList = userRoleSupport.findUserResellers(user.getId(), Collections.singletonList(marketInfo.getId()), true);
        //如果是Global Viewer用户，需要添加代理商
        if (roleService.isUserRoleExist(user.getId(), SystemConstants.SUPER_MARKET_ID, null, RoleID.GLOBAL_VIEWER)) {
            Reseller currentRootReseller = resellerService.getMarketRootReseller(marketInfo.getId());
            if (!userResellerList.contains(currentRootReseller)) {
                userResellerList.add(0, currentRootReseller);
            }
        }
        //根据前端传入的代理商header设置用户当前代理商
        if (CollectionUtils.isNotEmpty(userResellerList)) {
            marketInfo = marketInfoService.getMarketInfo(marketInfo.getId());
            if (marketInfo.getAllowResellerLogin() && StringUtils.isNotEmpty(resellerHeader)) {
                for (Reseller reseller : userResellerList) {
                    if (StringUtils.equalsIgnoreCase(reseller.getIdString(), resellerHeader)) {
                        user.setCurrentReseller(reseller);
                        resellerId = reseller.getId();
                        break;
                    }
                }
            }
            if (user.getCurrentReseller() == null) {
                user.setCurrentReseller(userResellerList.get(0));
                resellerId = userResellerList.get(0).getId();
            }
            Reseller currentReseller = user.getCurrentReseller();
            currentReseller.setCustomSignatureEnabled(signatureSupport.isResellerCustomSignatureEnabled(marketInfo.getId(), currentReseller.getId()));
            user.setCurrentReseller(currentReseller);
        }
        user.setSuperAdmin(roleService.isUserRoleExist(user.getId(), marketInfo.getId(), resellerId, RoleID.SUPER_ADMIN));
        user.setMarketAdmin(roleService.isUserRoleExist(user.getId(), marketInfo.getId(), resellerId, RoleID.MARKET_ADMIN));
        user.setHasMarketAdmin(roleService.isUserRoleExist(user.getId(), null, null, RoleID.MARKET_ADMIN));
        user.setResellerAdmin(roleService.isUserRoleExist(user.getId(), marketInfo.getId(), resellerId, RoleID.RESELLER_ADMIN));
        user.setOperator(roleService.isUserRoleExist(user.getId(), marketInfo.getId(), null, RoleID.OPERATOR));
        return userResellerList;
    }

    /**
     * Load user merchant info.
     *
     * @param user           the user
     * @param marketInfo     the market info
     * @param merchantHeader the merchant header
     */
    public void loadUserMerchantInfo(User user, MarketInfo marketInfo, String merchantHeader) {
        if (user == null || marketInfo == null) return;
        user.setMerchantAdmin(merchantService.isUserMerchantExist(user.getId(), marketInfo.getId()));

        List<Merchant> merchantList = merchantService.findMerchantListByUserId(marketInfo.getId(), user.getId());
        if (CollectionUtils.isNotEmpty(merchantList)) {
            if (StringUtils.isNotBlank(merchantHeader)) {
                for (Merchant merchant : merchantList) {
                    if (StringUtils.equalsIgnoreCase(merchant.getIdString(), merchantHeader)) {
                        user.setCurrentMerchant(merchant);
                        break;
                    }
                }
            }
            if (user.getCurrentMerchant() == null) {
                user.setCurrentMerchant(merchantList.get(0));
            }
        }

        //为了显示跳转到Admin的菜单
        List<Reseller> userResellerList = userRoleSupport.findUserResellers(user.getId(), Collections.singletonList(marketInfo.getId()), true);
        if (CollectionUtils.isNotEmpty(userResellerList)) {
            user.setCurrentReseller(new Reseller(userResellerList.get(0).getId()));
        }
        user.setMerchants(merchantList);
    }

    @Override
    public List<Market> findUserResellerMarketList(Long userId) {
        Market market = new Market();
        market.setUserId(userId);
        return Lists.newArrayList(marketService.findUserResellerMarketList(market));
    }

    @Override
    public List<Long> findMarketUserIdsForSearchUsers(User user) {
        if (LongUtils.isNotBlankAndPositive(user.getResellerId())) {
            user.setMarketIds(Sets.newHashSet(user.getMarketId()));
            user.setResellerIds(resellerService.findAvailableResellerIds(user.getResellerId()));
        } else {
            if (CollectionUtils.isEmpty(user.getMarketIds())) {
                Set<Long> marketIds = new HashSet<>();
                if (LongUtils.equals(user.getMarketId(), SystemConstants.SUPER_MARKET_ID)) {
                    Market market = new Market();
                    market.setStatus(MarketStatus.ACTIVE);
                    List<Market> marketList = marketService.findList(market);

                    if (!CollectionUtils.isEmpty(marketList)) {
                        for (Market marketDB : marketList) {
                            marketIds.add(marketDB.getId());
                        }
                    }
                    marketIds.add(SystemConstants.SUPER_MARKET_ID);
                } else {
                    marketIds.add(user.getMarketId());
                }
                user.setMarketIds(marketIds);
            }
        }
        return roleService.findMarketUserIds(user);
    }

    @Override
    public List<Long> findMerchantUserIdsForSearchUsers(User user) {
        if (LongUtils.isNotBlankAndPositive(user.getResellerId())) {
            user.setResellerIds(resellerService.findAvailableResellerIds(user.getResellerId()));
        }
        return merchantService.findMerchantUserIds(user);
    }

    @Override
    public List<Merchant> getUserMerchants(Long marketId, Long userId) {
        return merchantService.findMerchantListByUserId(marketId, userId);
    }

    public List<Long> findRoleUserIdList(Long marketId, Long resellerId, Long roleId, Long privilegeId) {
        return roleService.findRoleUserIdList(marketId, resellerId, roleId, privilegeId);
    }

    /**
     * 同步外部系统角色
     *
     * @param user       the user
     */
    public void synchronizeUserRoles(User user, List<SsoMarketRoleInfo> multiMarketRoles) {
        if (CollectionUtils.isNotEmpty(multiMarketRoles)){
            loadMultiMarketUserRoleDetail(user.getId(), multiMarketRoles);
            userRoleService.updateMarketUserRolesForExternalUser(user.getId(), multiMarketRoles);
            deleteOtherSsoMarketUserRoles(user, multiMarketRoles);
            //同步userMarket
            for (SsoMarketRoleInfo marketRole : multiMarketRoles) {
                syncUserSsoMarket(user.getId(), marketRole.getMarketId());
            }
        }
    }

    public void deleteOtherSsoMarketUserRoles(User user, List<SsoMarketRoleInfo> multiMarketRoles){
        //1.查出当前用户所有配置的sso市场，对比传递配置的市场
        List<Market> markets = ssoSettingService.findMarketAllowSsoByEmail(user.getLoginName());
        //2.找出此次未配置的SSO市场，删除对应用户所有角色、userMarket
        List<String> marketDomainList = multiMarketRoles.stream().map(SsoMarketRoleInfo::getMarketDomain).toList();
        List<String> marketDomainNotIn = markets.stream().map(Market::getDomain).filter(domain -> !marketDomainList.contains(domain)).toList();
        if (CollectionUtils.isNotEmpty(marketDomainNotIn)){
            List<Long> marketIdList = markets.stream().filter(market -> marketDomainNotIn.contains(market.getDomain())).map(Market::getId).toList();
            for (Long delMarketId : marketIdList) {
                List<Role> oldRoleList = roleService.findUserRoles(user.getId(), delMarketId, Collections.emptyList());
                userRoleService.updateUserRoles(user.getId(), oldRoleList, null);
                userMarketService.delete(user.getId(), delMarketId);
            }
        }
    }


    public void syncUserSsoMarket(Long userId, Long marketId){
        UserMarket userMarket = userMarketService.get(userId, marketId);
        if (checkUserRoleForSyncUserMarket(marketId, userId)) {
            userMarketService.saveAdmin(userId, marketId, true);
        } else if (userMarket != null && (userMarket.isDeveloper() || userMarket.isMerchant())) {
            userMarketService.saveAdmin(userId, marketId, false);
        } else {
            userMarketService.delete(userId, marketId);
        }
    }

    private void loadMultiMarketUserRoleDetail(Long userId, List<SsoMarketRoleInfo> marketRoles){
        for (SsoMarketRoleInfo marketRole : marketRoles) {
            //检查从openId登录,是否是该市场的rootAdmin，如果是则不同步角色
            Market market = null;
            if (marketRole.getMarketId() != null){
                 market = marketService.get(marketRole.getMarketId());
            }else {
                 market = marketService.findMarketByDomain(marketRole.getMarketDomain());
            }
            if (market != null){
                if (StringUtils.isBlank(marketRole.getMarketDomain())){
                    marketRole.setMarketDomain(market.getDomain());
                }
                if ( market.getAdmin().getId().equals(userId)) {
                    marketRole.setRootMarketAdmin(true);
                }
                Reseller reseller = null;
                try {
                    PaxDynamicDsThreadLocal.setPreferenceMarketId(market.getId());
                    if (StringUtils.isBlank(marketRole.getResellerName())) {
                        reseller = resellerService.findMarketRootReseller(market.getId());
                    } else {
                        reseller = resellerService.getByName(market.getId(), marketRole.getResellerName());
                    }
                } catch (Exception e) {
                    log.error("get reseller error", e);
                } finally {
                    PaxDynamicDsThreadLocal.removePreferenceMarketId();
                }
                if (reseller != null){
                    SsoMarketRoleInfo.ResellerInfo resellerInfo = new SsoMarketRoleInfo.ResellerInfo();
                    resellerInfo.setId(reseller.getId());
                    resellerInfo.setParentId(reseller.getParentId());
                    resellerInfo.setMarketId(market.getId());
                    resellerInfo.setName(reseller.getName());
                    marketRole.setReseller(resellerInfo);
                }
            }
        }
    }


    public boolean checkUserRoleForSyncUserMarket(Long marketId, Long userId) {
        List<Role> roles = roleService.findUserRoles(userId, marketId, 0L);
        if (!Collections3.isEmpty(roles)) {
            try {
                PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
                return resellerService.findByIds(roles.stream().map(Role::getResellerId).collect(Collectors.toList()))
                        .stream().anyMatch(each->ResellerStatus.ACTIVE.equals(each.getStatus()));
            } catch (Exception e) {
                log.error("checkUserRoleForSyncUserMarket error", e);
            } finally {
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
        return false;
    }


}
