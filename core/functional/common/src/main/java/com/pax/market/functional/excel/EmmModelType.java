package com.pax.market.functional.excel;

import com.pax.market.domain.entity.global.emm.EmmModel;
import com.pax.market.framework.common.excel.ObjectFieldTypeConvertor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/11/13
 */
@Component("emmModelType")
public class EmmModelType implements ObjectFieldTypeConvertor<EmmModel> {
    @Override
    public EmmModel convertToObject(String val) {
        EmmModel query = new EmmModel();
        query.setName(val);
        return query;
    }

    @Override
    public String convertToString(EmmModel val) {
        if (val != null) {
            return val.getName();
        } else {
            return "";
        }
    }
}
