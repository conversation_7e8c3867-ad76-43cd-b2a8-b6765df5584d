/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2021. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.functional.vas.goinsight.impl;

import com.google.common.collect.Lists;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.functional.vas.VasRemoteInvokeFunc;
import com.pax.market.functional.vas.goinsight.GoInsightDictionaryFunc;
import com.pax.market.mq.contract.goinsight.GoInsightDictionarySyncMessage;
import com.pax.market.mq.producer.gateway.insight.GoInsightDictionaryGateway;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.vas.common.VasConstants;
import com.paxstore.global.domain.service.vas.VasConfigEntityService;
import com.paxstore.global.domain.service.vas.VasInfoService;
import com.paxstore.market.domain.service.organization.ResellerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/16
 */
@FunctionalService
public class GoInsightDictionaryFuncImpl implements GoInsightDictionaryFunc {

    @Autowired
    private GoInsightDictionaryGateway insightDictionaryGateway;
    @Autowired
    private VasConfigEntityService vasConfigEntityService;
    @Autowired
    private VasInfoService vasInfoService;
    @Autowired
    private VasRemoteInvokeFunc vasRemoteInvokeFunc;
    @Autowired
    private ResellerService resellerService;

    @Override
    public void syncTerminalInfo(Terminal terminal) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(terminal.getId(), GoInsightDictionarySyncMessage.DictionaryType.TERMINAL, terminal.getMarketId());
        syncMessage.setSn(terminal.getSerialNo());
        syncMessage.setTid(terminal.getTID());
        syncMessage.setName(terminal.getName());
        sendDataToGoInsight(syncMessage);
    }

    @Override
    public void syncMarketInfo(Market market) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(market.getId(), GoInsightDictionarySyncMessage.DictionaryType.MARKET, market.getId());
        syncMessage.setName(market.getName());
        syncMessage.setDomain(market.getDomain());
        syncMessage.setRegion(market.getRegion());
        syncMessage.setCountry(market.getCountry());
        syncMessage.setAgent(market.getAgent());
        sendDataToGoInsight(syncMessage);
    }

    @Override
    public void syncResellerInfo(Reseller reseller) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(reseller.getId(), GoInsightDictionarySyncMessage.DictionaryType.RESELLER, reseller.getMarketId());
        syncMessage.setName(reseller.getName());
        sendDataToGoInsight(syncMessage);
    }

    @Override
    public void syncResellerTreeInfo(Reseller reseller) {
        syncResellerTreeList(Collections.singletonList(reseller));
    }

    @Override
    public void syncResellerTreeInfo(Long marketId) {
        PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
        List<Reseller> resellers = resellerService.findResellerByMarketId(marketId, false);
        if(CollectionUtils.isNotEmpty(resellers)){
            Lists.partition(resellers, 1000).forEach(this::syncResellerTreeList);
        }
    }

    @Override
    public void syncResellerTreeInfo(List<Long> resellerIds) {
        List<Reseller> resellers = resellerService.findTreeInfoByIds(resellerIds);
        syncResellerTreeList(resellers);
    }

    private void syncResellerTreeList(List<Reseller> resellers) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(GoInsightDictionarySyncMessage.DictionaryType.RESELLER_TREE);
        syncMessage.setResellerTreeList(resellers.stream().map(covertInsightResellerTree).collect(Collectors.toList()));
        sendDataToGoInsight(syncMessage);
    }

    private final Function<Reseller, GoInsightDictionarySyncMessage.InsightResellerTree> covertInsightResellerTree = info -> {
        GoInsightDictionarySyncMessage.InsightResellerTree insightResellerTree = new GoInsightDictionarySyncMessage.InsightResellerTree();
        insightResellerTree.setName(info.getName());
        insightResellerTree.setMarketId(info.getMarketId());
        insightResellerTree.setResellerId(info.getId());
        insightResellerTree.setParentId(info.getParentId());
        insightResellerTree.setParentIds(info.getParentIds());
        return insightResellerTree;
    };

    @Override
    public void syncMerchantInfo(Long merchantId, String name, Long marketId) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(merchantId, GoInsightDictionarySyncMessage.DictionaryType.MERCHANT, marketId);
        syncMessage.setName(name);
        sendDataToGoInsight(syncMessage);
    }

    @Override
    public void syncFactoryInfo(Long factoryId, String name) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(factoryId, GoInsightDictionarySyncMessage.DictionaryType.FACTORY, null);
        syncMessage.setName(name);
        sendDataToGoInsight(syncMessage);
    }

    @Override
    public void syncModelInfo(Long modelId, String name, String productType) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(modelId, GoInsightDictionarySyncMessage.DictionaryType.MODEL, null);
        syncMessage.setName(name);
        syncMessage.setModelProductType(productType);
        sendDataToGoInsight(syncMessage);
    }

    @Override
    public void syncDeveloperInfo(Long developerId, String name, Long marketId) {
        GoInsightDictionarySyncMessage syncMessage = new GoInsightDictionarySyncMessage(developerId, GoInsightDictionarySyncMessage.DictionaryType.DEVELOPER, marketId);
        syncMessage.setName(name);
        sendDataToGoInsight(syncMessage);
    }

    private void sendDataToGoInsight(GoInsightDictionarySyncMessage syncMessage) {
        if (!vasConfigEntityService.isVasEnabledGlobally() || !vasRemoteInvokeFunc.isServiceEnabled(VasConstants.ServiceType.INSIGHT)) {
            return;
        }
        insightDictionaryGateway.send(syncMessage);
    }
}
