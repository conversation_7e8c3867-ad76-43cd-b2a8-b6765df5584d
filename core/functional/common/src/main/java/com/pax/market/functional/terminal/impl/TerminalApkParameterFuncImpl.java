/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.functional.terminal.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.PushApkType;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.market.pushtask.TerminalApk;
import com.pax.market.domain.entity.market.pushtask.TerminalApkParam;
import com.pax.market.domain.entity.market.pushtask.TerminalGroupApk;
import com.pax.market.domain.entity.market.pushtask.TerminalGroupApkParam;
import com.pax.market.domain.entity.market.role.Role;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.domain.parameter.ParameterUtils;
import com.pax.market.domain.parameter.SchemaProcess;
import com.pax.market.dto.app.ApkParamCompareInfo;
import com.pax.market.dto.parameter.ConfiguredParameterInfo;
import com.pax.market.dto.parameter.ParameterInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.terminal.TerminalApkParameterFunc;
import com.pax.market.functional.support.TerminalGroupApkSupport;
import com.pax.market.functional.utils.SchemaViewUtils;
import com.paxstore.global.domain.service.app.ApkParamTemplateService;
import com.paxstore.market.domain.service.pushtask.TerminalApkService;
import com.paxstore.market.domain.service.pushtask.TerminalGroupApkParamService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.market.domain.service.terminal.TerminalActionHistoryService;
import com.paxstore.market.domain.util.ApkParameterUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/10/22
 */
@FunctionalService
@RequiredArgsConstructor
public class TerminalApkParameterFuncImpl extends AbstractFunctionalService implements TerminalApkParameterFunc {

    private final TerminalGroupApkSupport groupApkSupport;
    private final TerminalGroupApkParamService terminalGroupApkParamService;
    private final TerminalActionHistoryService terminalActionHistoryService;
    private final MarketTerminalService marketTerminalService;
    private final ApkParamTemplateService apkParamTemplateService;
    private final TerminalApkService terminalApkService;

    /**
     * 获取终端推送Apk参数模板内容配置的参数值
     *
     * @param terminalApkId the terminalApkId
     * @return the pid-value parameters map
     */
    public List<ConfiguredParameterInfo> getTerminalApkConfiguredParameters(Long terminalApkId) {
        TerminalApk terminalApk = terminalApkService.getIncludeDeleted(terminalApkId);
        if (terminalApk == null || !LongUtils.equals(getCurrentMarketId(), terminalApk.getTerminal().getMarketId())) {
            return Collections.emptyList();
        }

        TerminalApkParam terminalApkParam = terminalApkService.getTerminalApkParam(terminalApk);
        if (terminalApkParam == null) {
            return Collections.emptyList();
        }
        return getConfiguredParameters(terminalApk.getApk(), terminalApkParam.getParamTemplateName(), terminalApkParam.getParamTemplateId(), terminalApkParam.getParam(), terminalApkParam.getParamVariables());
    }


    /**
     * 获取分组推送Apk参数模板内容配置的参数值
     *
     * @param terminalGroupApkId    the terminalGroupApkId
     * @param terminalGroupActionId the terminalGroupActionId
     * @return the pid-value parameters
     */
    public List<ConfiguredParameterInfo> getGroupApkConfiguredParameters(Long terminalGroupApkId, Long terminalGroupActionId) {
        TerminalGroupApk terminalGroupApk = groupApkSupport.getGroupApkWithDetails(terminalGroupApkId, PushApkType.NORMAL);
        if (terminalGroupApk == null) {
            return Collections.emptyList();
        }
        TerminalGroupApkParam terminalGroupApkParam = terminalGroupApk.getGroupApkParam();
        if (terminalGroupApkParam == null) {
            return Collections.emptyList();
        }
        if (StringUtils.isEmpty(terminalGroupApkParam.getParamTemplateName())) {
            terminalGroupApkParam = terminalGroupApkParamService.get(terminalGroupApkParam);
        }

        TerminalAction terminalAction = terminalActionHistoryService.getTerminalActionHistoryById(terminalGroupActionId);
        if (terminalAction == null) {
            return Collections.emptyList();
        }
        Terminal terminal = marketTerminalService.get(terminalAction.getTerminalId());
        if (terminal == null) {
            return Collections.emptyList();
        }

        return getConfiguredParameters(terminalGroupApk.getApk(), terminalGroupApkParam.getParamTemplateName(), terminalGroupApkParam.getParamTemplateId(), terminalGroupApkParam.getParam(), terminalAction.getParamVariables());
    }

    /**
     * 获取参数模板内容配置的参数值
     *
     * @param apk               the apk
     * @param paramTemplateName the  paramTemplateName
     * @param param             the param
     * @param paramVariables    the paramVariables
     * @return the configured parameters
     */
    public List<ConfiguredParameterInfo> getConfiguredParameters(Apk apk, String paramTemplateName, String paramTemplateIds,  String param, String paramVariables) {
        ApkParamTemplate apkParamTemplate = apkParamTemplateService.getParamTemplate(apk.getId(), paramTemplateName, paramTemplateIds);
        SchemaProcess schemaProcess = ApkParameterUtils.getSchemaProcess(apkParamTemplate, param, JsonMapper.fromJsonString(paramVariables), false);
        List<ConfiguredParameterInfo> configuredParameters = new ArrayList<>();
        List<Role> userRoleList = SchemaViewUtils.getCurrentUserRoles();
        for (ParameterInfo parameterInfo : schemaProcess.getUsedParameterInfoList()) {
            if (!SchemaViewUtils.isUserVisibleParameter(parameterInfo, userRoleList)) {
                continue;
            }
            ParameterInfo cloneParameterInfo = BeanMapper.clone(parameterInfo, ParameterInfo.class);
            if (ParameterUtils.replaceParamVariables(cloneParameterInfo, paramVariables, schemaProcess.getComboParametersMap().get(parameterInfo.getPid()))) {
                //因为变量是没有经过转换的，所以如果替换了变量值，需要进行转换
                ParameterUtils.convertAmountValue(cloneParameterInfo, false);
            }
            Set<String> passwordPids = schemaProcess.getPasswordPids();
            configuredParameters.add(ConfiguredParameterInfo.builder()
                    .pid(cloneParameterInfo.getPidWithArrayIndexAndFileId())
                    .title(cloneParameterInfo.getTitle())
                    .paramValue(cloneParameterInfo.getDefaultValue())
                    .isPassword(CollectionUtils.isNotEmpty(passwordPids) && passwordPids.contains(cloneParameterInfo.getFormPidWithFileId()))
                    .build());
        }
        return configuredParameters;
    }


    /**
     * 获取推送参数的值比较列表
     *
     * @param firstPidParamList  the firstPidParamList
     * @param secondPidParamList the secondPidParamList
     * @return the apkParamPidCompareInfo list
     */
    public List<ApkParamCompareInfo> findCompareParamList(List<ConfiguredParameterInfo> firstPidParamList, List<ConfiguredParameterInfo> secondPidParamList) {
        List<ApkParamCompareInfo> paramPidCompareList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstPidParamList) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(secondPidParamList)) {
            List<String> firstPidList = firstPidParamList.stream().map(ConfiguredParameterInfo::getPid).collect(Collectors.toList());
            List<String> secondPidList = secondPidParamList.stream().map(ConfiguredParameterInfo::getPid).collect(Collectors.toList());
            List<String> intersectionKeys = Collections3.intersection(firstPidList, secondPidList);

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(intersectionKeys)) {
                loadApkParamPidInfoList(intersectionKeys, firstPidParamList, secondPidParamList, paramPidCompareList);
                List<String> firstDiffPidList = Collections3.subtract(firstPidList, intersectionKeys);
                List<String> secondDiffPidList = Collections3.subtract(secondPidList, intersectionKeys);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstDiffPidList)) {
                    loadApkParamPidInfoList(firstDiffPidList, firstPidParamList, secondPidParamList, paramPidCompareList);
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(secondDiffPidList)) {
                    loadApkParamPidInfoList(secondDiffPidList, firstPidParamList, secondPidParamList, paramPidCompareList);
                }
            } else {
                List<String> unionKeys = new ArrayList<>();
                unionKeys.addAll(firstPidList);
                unionKeys.addAll(secondPidList);
                loadApkParamPidInfoList(unionKeys, firstPidParamList, secondPidParamList, paramPidCompareList);
            }
        } else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstPidParamList) && org.apache.commons.collections4.CollectionUtils.isEmpty(secondPidParamList)) {
            List<String> firstPidList = firstPidParamList.stream().map(ConfiguredParameterInfo::getPid).collect(Collectors.toList());
            loadApkParamPidInfoList(firstPidList, firstPidParamList, secondPidParamList, paramPidCompareList);
        } else {
            List<String> secondPidList = secondPidParamList.stream().map(ConfiguredParameterInfo::getPid).collect(Collectors.toList());
            loadApkParamPidInfoList(secondPidList, firstPidParamList, secondPidParamList, paramPidCompareList);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(paramPidCompareList)) {
            paramPidCompareList.sort(Comparator.comparing(ApkParamCompareInfo::getPid));
        }
        return paramPidCompareList;
    }

    /**
     * 更新获取参数信息列表
     *
     * @param pidList             the pid list
     * @param firstPidParamList   the firstPidParamList
     * @param secondPidParamList  the secondPidParamList
     * @param paramPidCompareList the paramList
     */
    private void loadApkParamPidInfoList(List<String> pidList, List<ConfiguredParameterInfo> firstPidParamList, List<ConfiguredParameterInfo> secondPidParamList, List<ApkParamCompareInfo> paramPidCompareList) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pidList) && paramPidCompareList != null) {
            for (String pid : pidList) {
                ConfiguredParameterInfo firstInfo = getConfiguredParameter(pid, firstPidParamList);
                ConfiguredParameterInfo secondInfo = getConfiguredParameter(pid, secondPidParamList);
                if (!StringUtils.equals(firstInfo.getParamValue(), secondInfo.getParamValue())) {
                    String valueA = BooleanUtils.toBoolean(firstInfo.getIsPassword()) ? StringUtils.mask(firstInfo.getParamValue()) : firstInfo.getParamValue();
                    String valueB = BooleanUtils.toBoolean(secondInfo.getIsPassword()) ? StringUtils.mask(secondInfo.getParamValue()) : secondInfo.getParamValue();

                    paramPidCompareList.add(ApkParamCompareInfo.builder()
                            .pid(pid)
                            .nameA(firstInfo.getTitle())
                            .nameB(secondInfo.getTitle())
                            .paramValueA(valueA)
                            .paramValueB(valueB)
                            .build());
                }
            }
        }
    }

    /**
     * 根据pid获取参数模板内容配置的具体参数值
     *
     * @param pid              the pid
     * @param conParamInfoList the conParamInfoList
     * @return the ConfiguredParameterInfo
     */
    private ConfiguredParameterInfo getConfiguredParameter(String pid, List<ConfiguredParameterInfo> conParamInfoList) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(conParamInfoList)) {
            Optional<ConfiguredParameterInfo> configuredParameterInfo = conParamInfoList.stream().filter(e -> e.getPid().equals(pid)).findFirst();
            if (configuredParameterInfo.isPresent()) {
                return configuredParameterInfo.get();
            }
        }
        return new ConfiguredParameterInfo();
    }
}
