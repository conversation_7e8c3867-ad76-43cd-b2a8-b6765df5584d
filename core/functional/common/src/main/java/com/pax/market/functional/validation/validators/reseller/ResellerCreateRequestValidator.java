/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.functional.validation.validators.reseller;

import com.pax.market.dto.market.MarketInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.ResellerStatus;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.core.exception.BusinessException;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.pax.market.functional.validation.Validator;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.request.organization.ResellerCreateRequest;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.TreeEntity;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

/**
 * Created by liukai on 2016/10/19.
 */
public class ResellerCreateRequestValidator extends Validator<ResellerCreateRequest> {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * Instantiates a new Reseller create request validator.
     *
     * @param resellerCreateRequest the reseller create request
     */
    public ResellerCreateRequestValidator(ResellerCreateRequest resellerCreateRequest) {
        super(resellerCreateRequest);
    }

    @Override
    public boolean validate() {
        Reseller parentReseller = null;
        if (LongUtils.isBlankOrNotPositive(validateTarget.getParentId())) {
        	parentReseller = resolveCurrentReseller();
        } else {
        	ResellerService resellerService = SpringContextHolder.getBean(ResellerService.class);
            parentReseller = resellerService.get(validateTarget.getParentId());
        }
        if (parentReseller == null) {
        	throw new BusinessException(ApiCodes.PARENT_RESELLER_NOT_FOUND);
        }
        if (!StringUtils.equals(parentReseller.getStatus(), ResellerStatus.ACTIVE)) {
        	throw new BusinessException(ApiCodes.PARENT_RESELLER_NOT_ACTIVE);
        }

        checkLimit(parentReseller);

        if (StringUtils.isEmpty(validateTarget.getName())) {
            throw new BusinessException(ApiCodes.RESELLER_NAME_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getContact())) {
            throw new BusinessException(ApiCodes.RESELLER_CONTACT_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getPhone())) {
            throw new BusinessException(ApiCodes.RESELLER_PHONE_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getEmail())) {
            throw new BusinessException(ApiCodes.RESELLER_EMAIL_MANDATORY);
        }

        if (StringUtils.isEmpty(validateTarget.getCountry())) {
            throw new BusinessException(ApiCodes.FACTORY_COUNTRY_MANDATORY);
        }

        if (validateTarget.getName().trim().length() > 64) {
            throw new BusinessException(ApiCodes.RESELLER_NAME_TOO_LONG);
        }

        if (validateTarget.getContact().trim().length() > 64) {
            throw new BusinessException(ApiCodes.RESELLER_CONTACT_TOO_LONG);
        }

        if (validateTarget.getPhone().trim().length() > 32) {
            throw new BusinessException(ApiCodes.RESELLER_PHONE_TOO_LONG);
        }

        if (validateTarget.getEmail().trim().length() > 255) {
            throw new BusinessException(ApiCodes.RESELLER_EMAIL_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getPostcode()) && validateTarget.getPostcode().trim().length() > 16) {
            throw new BusinessException(ApiCodes.POSTCODE_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getCity()) && validateTarget.getCity().trim().length() > 255) {
            throw new BusinessException(ApiCodes.FACTORY_CITY_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getAddress()) && validateTarget.getAddress().trim().length() > 255) {
            throw new BusinessException(ApiCodes.ADDRESS_TOO_LONG);
        }

        if (!StringUtils.isEmpty(validateTarget.getCompany()) && validateTarget.getCompany().trim().length() > 255) {
            throw new BusinessException(ApiCodes.RESELLER_COMPANY_TOO_LONG);
        }

        if (!StringUtils.isValidEmailAddress(validateTarget.getEmail())) {
            throw new BusinessException(ApiCodes.USER_EMAIL_INVALID);
        }

        if (StringUtils.isInvalidPhoneNumber(validateTarget.getPhone())) {
            throw new BusinessException(ApiCodes.PHONE_INVALID);
        }

        if(StringUtils.isInvalidResellerName(validateTarget.getName())){
            throw new BusinessException(ApiCodes.ENTITY_NAME_INVALID);
        }
        return true;
    }
    
    private Reseller resolveCurrentReseller() {
    	CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);
    	UserInfo currentUserInfo = currentLoginProvider.getCurrentUserInfo();
        if(currentUserInfo != null && currentUserInfo.getCurrentReseller() != null) {//from web
        	return BeanMapper.map(currentUserInfo.getCurrentReseller(), Reseller.class);
        } else if (currentLoginProvider.getCurrentThirdPartySysInfo() != null) {//from 3rd system API call
        	ResellerService resellerService = SpringContextHolder.getBean(ResellerService.class);
        	return resellerService.get(currentLoginProvider.getCurrentThirdPartySysInfo().getReseller().getId());
        }
        return null;
    }

    private void checkLimit(Reseller parentReseller){
        //校验代理商层级数量和当前层子代理商级数
        CurrentLoginProvider currentLoginProvider = SpringContextHolder.getBean(CurrentLoginProvider.class);
        MarketInfo currentMarket = currentLoginProvider.getCurrentMarketInfo();
        int maxParentNode = currentMarket.getResellerLevelLimit();
        if (StringUtils.isNotEmpty(parentReseller.getParentIds())) {
            String parentIds = parentReseller.getParentIds();
            //根代理商为0 默认需要+1
            String[] parentIdArray = parentIds.split(TreeEntity.PARENT_IDS_DELEMETER);
            int currentLevel;
            if (parentIdArray.length == 0){
                currentLevel = 1;
            }else {
                currentLevel = parentIdArray.length;
            }
            if (currentLevel >= maxParentNode) {
                //已超过最大代理商级数，拒绝创建
                throw new BusinessException(ApiCodes.RESELLER_MAX_LEVEL_CAN_NOT_CREATE);
            }
        }
        //校验代理商当前子级代理商数量
        ResellerService resellerService = SpringContextHolder.getBean(ResellerService.class);
        int count = resellerService.getNextLevelResellerCount(parentReseller.getId());
        int limitCount = currentMarket.getSubResellerLimit();
        if (limitCount > 0  && count >= limitCount){
            throw new BusinessException(ApiCodes.RESELLER_MAX_COUNT_CAN_NOT_CREATE);
        }

    }
}
