/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.validation.validators.developer;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.DeveloperType;
import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.request.admin.task.developer.DeveloperUpdateRequest;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.validation.Validator;

/**
 * The type Developer update validator - by admin.
 */
public class AdminDeveloperUpdateValidator extends Validator<DeveloperUpdateRequest> {

    /**
     * Instantiates a new Developer update validator.
     *
     * @param request the developer update request
     */
    public AdminDeveloperUpdateValidator(DeveloperUpdateRequest request) {
        super(request);
    }

    @Override
    public boolean validate() {

        if (StringUtils.isEmpty(validateTarget.getDeveloperType())||
                (!DeveloperType.INDIVIDUAL.equals(validateTarget.getDeveloperType())&&!DeveloperType.ENTERPRISE.equals(validateTarget.getDeveloperType()))) {
            throw new BusinessException(ApiCodes.DEV_TYPE_INVALID);
        }

        //Email
        if(StringUtils.isEmpty(validateTarget.getEmail())) {
            throw new BusinessException(ApiCodes.DEV_EMAIL_MANDATORY);
        }
        if (validateTarget.getEmail().trim().length() > SystemConstants.USER_EMAIL_MAX_LENGTH) {
            throw new BusinessException(ApiCodes.USER_EMAIL_TOO_LONG);
        }
        if (!StringUtils.isValidEmailAddress(validateTarget.getEmail())) {
            throw new BusinessException(ApiCodes.USER_EMAIL_INVALID);
        }

        //Real Name
        if(StringUtils.isEmpty(validateTarget.getRealName()) ){
            throw new BusinessException(ApiCodes.DEV_REAL_NAME_MANDATORY);
        }
        if (validateTarget.getRealName().trim().length() > SystemConstants.REAL_NAME_MAX_LENGH) {
            throw new BusinessException(ApiCodes.REAL_NAME_TOO_LONG);
        }
        if (validateTarget.getRealName().trim().length() <= SystemConstants.REAL_NAME_MIN_LENGH) {
            throw new BusinessException(ApiCodes.REAL_NAME_TOO_SHORT);
        }
        if (StringUtils.isEmpty(validateTarget.getCountry())) {
            throw new BusinessException(ApiCodes.FACTORY_COUNTRY_MANDATORY);
        }

        if (validateTarget.getCountry().trim().length() > SystemConstants.COUNTRY_MAX_LENGTH) {
            throw new BusinessException(ApiCodes.FACTORY_COUNTRY_TOO_LONG);
        }

        if (DeveloperType.INDIVIDUAL.equals(validateTarget.getDeveloperType())) {
            if(StringUtils.isEmpty(validateTarget.getNickname()) ){
                throw new BusinessException(ApiCodes.DEV_NICK_NAME_I_MANDATORY);
            }
            if(validateTarget.getNickname().trim().length()>SystemConstants.DEV_NICK_MAX_LENGTH){
                throw new BusinessException(ApiCodes.DEV_NICK_NAME_TOO_LONG);
            }

            //idType
            if(StringUtils.isEmpty(validateTarget.getIdType()) ){
                throw new BusinessException(ApiCodes.DEV_ID_TYPE_MANDATORY);
            }

            if(validateTarget.getIdType().trim().length()>1){
                throw new BusinessException(ApiCodes.DEV_ID_TYPE_TOO_LONG);
            }

            //idNo
            if(StringUtils.isEmpty(validateTarget.getIdCardNo()) ){
                throw new BusinessException(ApiCodes.DEV_ID_NO_MANDATORY);
            }

            if(validateTarget.getIdCardNo().trim().length()>64){
                throw new BusinessException(ApiCodes.DEV_ID_NO_TOO_LONG);
            }

            //img
            if(StringUtils.isEmpty(validateTarget.getIdCardFrontImg()) ){
                throw new BusinessException(ApiCodes.DEV_ID_FRONT_IMG_MANDATORY);
            }
        }
        if (DeveloperType.ENTERPRISE.equals(validateTarget.getDeveloperType())){

            if(StringUtils.isEmpty(validateTarget.getNickname()) ){
                throw new BusinessException(ApiCodes.DEV_NICK_NAME_E_MANDATORY);
            }
            if(validateTarget.getNickname().trim().length()>SystemConstants.DEV_NICK_MAX_LENGTH){
                throw new BusinessException(ApiCodes.COM_NICK_NAME_TOO_LONG);
            }

            //Legal Company Name
            if(StringUtils.isEmpty(validateTarget.getCompanyName()) ){
                throw new BusinessException(ApiCodes.DEV_COM_NAME_MANDATORY);
            }

            if(validateTarget.getCompanyName().trim().length()>SystemConstants.LEGAL_COMPANY_MAX_LENGTH){
                throw new BusinessException(ApiCodes.LEGAL_COMPANY_NAME_TOO_LONG);
            }

            //Company Address
            if(StringUtils.isEmpty(validateTarget.getCompanyAddr()) ){
                throw new BusinessException(ApiCodes.DEV_COM_ADDR_MANDATORY);
            }

            if(validateTarget.getCompanyAddr().trim().length()>SystemConstants.COMPANY_ADDR_MAX_LENGTH){
                throw new BusinessException(ApiCodes.DEV_COM_ADDR_TOO_LONG);
            }

            //img
            if(StringUtils.isEmpty(validateTarget.getIdCardFrontImg()) ){
                throw new BusinessException(ApiCodes.DEV_COM_FRONT_IMG_MANDATORY);
            }

        }

        //Register Reason

        if(StringUtils.isEmpty(validateTarget.getReason()) ){
            throw new BusinessException(ApiCodes.DEV_REASON_MANDATORY);
        }
        if (validateTarget.getReason().trim().length() > SystemConstants.REG_REASON_MAX_LENGTH) {
            throw new BusinessException(ApiCodes.DEV_REASON_TOO_LONG);
        }

        //Phone

        if(StringUtils.isEmpty(validateTarget.getPhone()) ){
            throw new BusinessException(ApiCodes.DEV_PHONE_MANDATORY);
        }

        if (StringUtils.isInvalidPhoneNumber(validateTarget.getPhone())) {
            throw new BusinessException(ApiCodes.PHONE_INVALID);
        }

        if(validateTarget.getPhone().trim().length() > SystemConstants.MERCHANT_PHONE_NO_MAX_LENGTH){
            throw new BusinessException(ApiCodes.DEV_PHONE_TOO_LONG);
        }

        if(validateTarget.getCompanyWebsite() != null && validateTarget.getCompanyWebsite().length() > SystemConstants.LEGAL_COMPANY_MAX_LENGTH){
            throw new BusinessException(ApiCodes.DEV_WEBSITE_TOO_LONG);
        }

        if (StringUtils.isEmpty(validateTarget.getCompanyRegistrationNumber())) {
            throw new BusinessException(ApiCodes.DEV_COM_REG_NUMBER_MANDATORY);
        }
        if (validateTarget.getCompanyRegistrationNumber().trim().length() > SystemConstants.COMP_REG_NUMBER_MAX_LENGTH) {
            throw new BusinessException(ApiCodes.DEV_COM_REG_NUMBER_LONG);
        }



        return true;
    }
}
