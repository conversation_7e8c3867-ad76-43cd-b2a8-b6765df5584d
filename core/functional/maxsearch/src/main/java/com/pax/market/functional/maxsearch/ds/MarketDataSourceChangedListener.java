package com.pax.market.functional.maxsearch.ds;

import com.pax.api.pubsub.MessageListenerDelegate;
import com.pax.support.dynamic.datasource.constants.CommonConstant;
import com.pax.support.dynamic.datasource.pubsub.DataSourceMarketChangedMessage;
import com.pax.support.dynamic.datasource.tools.MasterDsSupport;
import com.zolon.saas.maxsearch.maxstore.query.api.QueryDataSourceChangedListener;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MarketDataSourceChangedListener implements MessageListenerDelegate {
    private final QueryDataSourceChangedListener queryDataSourceChangedListener;

    public MarketDataSourceChangedListener(QueryDataSourceChangedListener queryDataSourceChangedListener) {
        this.queryDataSourceChangedListener = queryDataSourceChangedListener;
    }

    @Override
    public void onReceiveMessage(Object message) {
        if (message instanceof DataSourceMarketChangedMessage changedMessage) {
            log.info("received MarketDataSourceChangedMessage[id={}, dsId={}, database={}, type={}]",
                    changedMessage.getId(), changedMessage.getDataSourceId(), changedMessage.getDbInstanceName(), changedMessage.getChangeType());
            boolean active = CommonConstant.MESSAGE_CREATE_MARKET_DS_MAPPING.equals(changedMessage.getChangeType());

            MasterDsSupport.invoker(() -> queryDataSourceChangedListener.onChange(changedMessage.getDataSourceId().intValue(), changedMessage.getId().intValue(), active));
        }
    }

}
