package com.pax.market.functional.maxsearch.query;

import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.functional.maxsearch.query.exception.QueryException;
import com.pax.market.functional.maxsearch.query.impl.TerminalMysqlSearchFunc;
import com.pax.market.functional.maxsearch.query.impl.TerminalOsSearchFunc;
import com.pax.market.functional.terminal.TerminalFunctionService;
import com.zolon.saas.maxsearch.query.FallbackExecutor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
@Primary
public class DelegatedTerminalSearchFunc implements TerminalSearchFunc {
    private final FallbackExecutor fallbackExecutor;
    private final TerminalMysqlSearchFunc terminalMysqlSearchFunc;
    private final TerminalOsSearchFunc terminalOsSearchFunc;

    public DelegatedTerminalSearchFunc(ApplicationContext applicationContext,
                                       FallbackExecutor fallbackExecutor,
                                       TerminalFunctionService terminalFunctionService) {
        this.fallbackExecutor = fallbackExecutor;
        this.terminalMysqlSearchFunc = new TerminalMysqlSearchFunc(terminalFunctionService);
        this.terminalOsSearchFunc = applicationContext.getBeansOfType(TerminalOsSearchFunc.class)
                .values().stream().findFirst().orElse(null);
    }

    @Override
    public Page<Terminal> findPagedTerminals(Page<Terminal> page, long marketId, Terminal terminal) throws QueryException {
        return (Page<Terminal>) query(f -> f.findPagedTerminals(page, marketId, terminal), marketId);
    }

    private Object query(Function<TerminalSearchFunc, Object> func, long marketId) {
        if (SystemPropertyHelper.isTerminalFuzzyQueryDisabled()) {
            return func.apply(terminalMysqlSearchFunc);
        } else {
            return fallbackExecutor.query(func, terminalOsSearchFunc, terminalMysqlSearchFunc, marketId);
        }
    }
}
