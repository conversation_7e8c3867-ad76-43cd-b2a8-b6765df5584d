package com.pax.market.functional.maxsearch.export;

import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.functional.activity.exports.MerchantExportService;
import com.pax.market.functional.maxsearch.export.impl.MerchantMysqlExportFunc;
import com.pax.market.functional.maxsearch.export.impl.MerchantOsExportFunc;
import com.paxstore.global.domain.service.setting.MerchantCategoryService;
import com.paxstore.market.domain.service.attribute.EntityAttributeService;
import com.paxstore.market.domain.service.organization.MerchantCategoryMapService;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.zolon.saas.maxsearch.query.FallbackExecutor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
public class DelegatedMerchantExportService extends MerchantExportService {
    private final FallbackExecutor fallbackExecutor;
    private final MerchantMysqlExportFunc merchantMysqlExportFunc;
    private final MerchantOsExportFunc merchantOsExportFunc;

    public DelegatedMerchantExportService(ApplicationContext applicationContext,
                                          FallbackExecutor fallbackExecutor,
                                          ResellerService resellerService,
                                          MerchantService merchantService,
                                          MerchantCategoryMapService merchantCategoryMapService,
                                          MerchantCategoryService merchantCategoryService,
                                          EntityAttributeService entityAttributeService) {
        super(resellerService, merchantService, merchantCategoryMapService, merchantCategoryService, entityAttributeService);
        this.fallbackExecutor = fallbackExecutor;
        this.merchantMysqlExportFunc = new MerchantMysqlExportFunc(resellerService, merchantService, merchantCategoryMapService, merchantCategoryService, entityAttributeService);
        this.merchantOsExportFunc = applicationContext.getBeansOfType(MerchantOsExportFunc.class)
                .values().stream().findFirst().orElse(null);
    }

    @Override
    public List<Merchant> findMerchants4WidgetExport(Merchant merchant) {
        return (List<Merchant>) execute(f -> f.findMerchants4WidgetExport(merchant.getPage(), getCurrentMarketId(), merchant));
    }

    @Override
    public List<Merchant> findMerchants4OrgExport(Merchant merchant) {
        return (List<Merchant>) execute(f -> f.findMerchants4OrgExport(merchant.getPage(), getCurrentMarketId(), merchant));
    }

    @Override
    public int countMerchants4WidgetExport(List<Long> childResellerIds) {
        return (int) execute(f -> f.countMerchants4WidgetExport(getCurrentMarketId(), childResellerIds));
    }

    @Override
    public int countMerchants4OrgExport(Merchant merchant) {
        return (int) execute(f -> f.countMerchants4OrgExport(getCurrentMarketId(), merchant));
    }

    private Object execute(Function<MerchantExportFunc, Object> func) {
        return fallbackExecutor.query(func, merchantOsExportFunc, merchantMysqlExportFunc, getCurrentMarketId());
    }
}
