package com.pax.market.functional.maxsearch.query;

import com.pax.market.functional.maxsearch.query.support.TerminalQueryControl;
import com.paxstore.domain.support.TerminalChangedCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MaxseachTerminalChangedCallback implements TerminalChangedCallback, DisposableBean {
    private final TerminalQueryControl terminalQueryControl;
    private final ExecutorService executor;


    public MaxseachTerminalChangedCallback(TerminalQueryControl terminalQueryControl) {
        this.terminalQueryControl = terminalQueryControl;
        this.executor = new ThreadPoolExecutor(2, 4,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(1024)
        );
    }

    @Override
    public void onChanged(int effectElements) {
        try {
            executor.submit((() -> {
                log.debug("{} terminal changed", effectElements);
                terminalQueryControl.updateOpensearchQueryableLazyTime(effectElements);
            }));
        } catch (Throwable t) {
            log.warn("terminal changed callback execute error", t);
        }
    }


    @Override
    public void destroy() throws Exception {
        executor.shutdown();
    }
}
