package com.pax.market.functional.maxsearch.export.impl;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.functional.maxsearch.export.ResellerExportFunc;
import com.pax.market.functional.maxsearch.query.impl.ResellerOsSearchFunc;

import java.util.List;

public class ResellerOsExportFunc implements ResellerExportFunc {
    private final ResellerOsSearchFunc resellerOsSearchFunc;

    public ResellerOsExportFunc(ResellerOsSearchFunc resellerOsSearchFunc) {
        this.resellerOsSearchFunc = resellerOsSearchFunc;
    }

    @Override
    public List<Reseller> findResellers(long marketId, Reseller reseller) {
        return this.resellerOsSearchFunc.findResellers4OrgExport(reseller.getPage(), marketId, reseller);
    }

    @Override
    public int countResellers(long marketId, Reseller reseller) {
        return this.resellerOsSearchFunc.countResellers4OrgExport(reseller.getPage(), marketId, reseller);
    }
}
