package com.pax.market.functional.maxsearch.api;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class FullSyncTaskDTO implements Serializable {
    private Map<String, Object> payload = new HashMap<>();
    private String taskId;
    private int mdsBundleId;
    private List<String> domains;
    private boolean clean;
    private String status;
    private String remark;
    private String submitDt;
    private String startDt;
    private String finishDt;
    private int spentMs;

    public FullSyncTaskDTO addPayload(String key, Object val) {
        payload.put(key, val);
        return this;
    }
}