package com.pax.market.functional.mobile.admin.impl;

import com.google.common.collect.Lists;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.user.UserMarket;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.domain.util.WidgetCacheUtils;
import com.pax.market.dto.PrivilegeInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.terminal.TerminalQtySummary;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.mobile.admin.MobileAdminMarketUserFunc;
import com.pax.market.functional.mobile.common.MobileCommonUserFunc;
import com.pax.market.functional.role.PrivilegeSupportFunctionService;
import com.pax.market.functional.role.UserRoleSupport;
import com.pax.market.vo.mobile.*;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.vas.common.VasConstants;
import com.paxstore.domain.support.ResellerSettingSupport;
import com.paxstore.global.domain.cachable.LocalCacheMarketInfoService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.service.developer.DeveloperService;
import com.paxstore.global.domain.service.role.OperationService;
import com.paxstore.global.domain.service.user.UserMarketService;
import com.paxstore.global.domain.service.user.UserService;
import com.paxstore.global.domain.service.vas.Market2ServiceReadonlyService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/3 15:32
 */
@FunctionalService
@RequiredArgsConstructor
@Slf4j
public class MobileAdminMarketUserFuncImpl extends AbstractFunctionalService implements MobileAdminMarketUserFunc {


    private final UserMarketService userMarketService;
    private final LocalCacheMarketInfoService marketInfoService;
    private final ResellerSettingSupport resellerSettingSupport;
    private final AppService appService;
    private final DeveloperService developerService;
    private final UserService userService;
    private final MarketTerminalService marketTerminalService;
    private final MobileCommonUserFunc mobileCommonUserFunc;
    private final Market2ServiceReadonlyService market2ServiceReadonlyService;
    private final UserRoleSupport userRoleSupport;
    private final PrivilegeSupportFunctionService privilegeSupportFunctionService;
    private final OperationService operationService;

    @Override
    public MobileUserVo getMarketCurrentUser(HttpServletRequest servletRequest, String remarks) {
        UserInfo userInfo = getCurrentUser();
        MobileUserVo mobileUserVo = BeanMapper.map(userInfo, MobileUserVo.class);
        mobileUserVo.setMobileAppAdmin(true);
        mobileUserVo.setMarketName(getCurrentMarket().getName());
        mobileUserVo.setMarketId(getCurrentMarketId());
        mobileUserVo.setPrivileges(getCurrentPrivilegeInfoVo());
        mobileUserVo.setInsightEnabledMarketLevel(market2ServiceReadonlyService.isServiceEnabledActiveAndSubscribed(getCurrentMarketId(), VasConstants.ServiceType.INSIGHT));
        mobileUserVo.setAllowRemoteLockTerminal(operationService.isUserOperationAvailable(MarketSettingKey.tmRemoteLock));
        mobileUserVo.setAllowDeleteTerminal(operationService.isUserOperationAvailable(MarketSettingKey.tmDelete));
        mobileUserVo.setResellerLogin(userInfo.isResellerAdmin() || (userInfo.getCurrentReseller() != null && LongUtils.isNotBlankAndPositive(userInfo.getCurrentReseller().getParentId())));
        mobileUserVo.setMarketLogin(userInfo.isMarketAdmin() || (userInfo.getCurrentReseller() != null && LongUtils.isBlankOrNotPositive(userInfo.getCurrentReseller().getParentId())));
        mobileUserVo.setAllowPci7Signature(getCurrentMarket().getAllowPci7Signature());
        mobileCommonUserFunc.saveAuditLog(true, null, AuthLogActionTypes.LOGIN, servletRequest.getRequestedSessionId(), userInfo, remarks, servletRequest);

        mobileUserVo.setMarketLogo(resellerSettingSupport.getRootResellerImageResourceUrl(getCurrentMarketId(), MarketSettingKey.favicon));
        return mobileUserVo;
    }
    @Override
    public List<MobileUserMarketsVo> getUserMarkets(String name) {
        List<UserMarket> userMarkets = userMarketService.findUserMarketByUserId(getCurrentUserId());
        userMarkets = userMarkets.stream().filter(market -> {
            MarketInfo marketInfo = marketInfoService.getMarketInfo(market.getMarketId());
            return marketInfo.getAllowMobileApp() && market.isAdmin() && StringUtils.equals(MarketStatus.ACTIVE, market.getStatus());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userMarkets)) {
            throw new BusinessException(ApiCodes.AUTH_INVALID_USER_ACCOUNT);
        }
        if (StringUtils.isNotBlank(name)) {
            userMarkets = userMarkets.stream().filter(market -> market.getName().toLowerCase().contains(name.toLowerCase())).collect(Collectors.toList());
        }
        List<MobileUserMarketsVo> mobileUserMarketsVos = BeanMapper.mapList(userMarkets, MobileUserMarketsVo.class);

        //add logo,optEnabled
        mobileUserMarketsVos.forEach(each -> {
            //登录后就要取得权限这个optEnable是用户级别的
            each.setOtpEnabled(Objects.nonNull(getCurrentUser()) ? getCurrentUser().getOtpEnabled() : false);
            each.setLogo(resellerSettingSupport.getRootResellerImageResourceUrl(each.getId(), MarketSettingKey.favicon));
        });
        mobileUserMarketsVos.sort((o1, o2) -> o2.getName().compareToIgnoreCase(o1.getName()));
        return mobileUserMarketsVos;
    }

    @Override
    public MobileWorkspaceVo getWorkSpaces() {
        AppApkQuery appApkQuery = new AppApkQuery();
        int onlineDevelopersCount = 0;
        appApkQuery.setStatus(AppStatus.PENDING);
        appApkQuery.setType(AppType.GENERAL);
        MobileWorkspaceVo vo = new MobileWorkspaceVo();
        int pendingAppCount = appService.getAppCountForAdmin(appApkQuery);

        List<Long> userIds = developerService.getPendingDeveloperUserIds();
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<Long> pendingUserIds = userService.findPendingUserIncludeDelete(userIds);
            onlineDevelopersCount = Optional.ofNullable(userIds.stream().filter(userId -> !pendingUserIds.contains(userId)).count()).orElse(0L).intValue();
        }

        TerminalQtySummary terminalQtySummary = WidgetCacheUtils.getWidgetFromCache(DashboardConstants.WIDGET_TERNIMAL_QUANTITY_SUMMARY, getCurrentResellerId(), null, false);
        if (terminalQtySummary == null) {
            terminalQtySummary = marketTerminalService.summarizeTerminalQty(getCurrentResellerId(), null, null);
            WidgetCacheUtils.putWidgetToCache(DashboardConstants.WIDGET_TERNIMAL_QUANTITY_SUMMARY, getCurrentResellerId(), null, terminalQtySummary);
        }
        
        vo.setPendingAppsCount(pendingAppCount);
        vo.setPendingDevelopersCount(onlineDevelopersCount);
        vo.setOnlineTerminalsCount(Integer.parseInt(String.valueOf(terminalQtySummary.getNumOfOnline())));
        return vo;
    }

    @Override
    public List<MobileUserMarketResellerVo> getUserMarketResellers(String name) {
        UserInfo userInfo = getCurrentUser();
        try {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(getCurrentMarketId());
            List<Reseller> resellers = userRoleSupport.findUserResellers(userInfo.getId(), Collections.singletonList(getCurrentMarketId()), true);
            if (CollectionUtils.isEmpty(resellers)) {
                return Lists.newArrayList();
            }
            if (StringUtils.isNotBlank(name)) {
                resellers = resellers.stream().filter(each -> each.getName().toLowerCase().contains(name.toLowerCase())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(resellers)) {
                    return Lists.newArrayList();
                }
            }
            resellers.remove(new Reseller(getCurrentResellerId()));
            resellers.sort((o1, o2) -> o1.getName().compareToIgnoreCase(o2.getName()));
            List<MobileUserMarketResellerVo> resellerVos = BeanMapper.mapList(resellers, MobileUserMarketResellerVo.class);
            resellerVos.add(0, MobileUserMarketResellerVo.builder().id(getCurrentResellerId()).parentId(getCurrentReseller().getParentId()).name(getCurrentReseller().getName()).build());
            return resellerVos;
        } finally {
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    private List<MobilePrivilegeInfoVo> getCurrentPrivilegeInfoVo() {
        List<PrivilegeInfo> privileges = privilegeSupportFunctionService.findCurrentUserPrivilegesTree(getCurrentUser());
        List<MobilePrivilegeInfoVo> mobilePrivileges = Lists.newArrayList();
        if (!LongUtils.equals(getCurrentMarketId(), SystemConstants.SUPER_MARKET_ID)) {
            //terminal
            addMobilePrivilegeInfoVo(mobilePrivileges, privileges, PrivilegeCodes.MENU_MANAGEMENT, PrivilegeCodes.MENU_TERMINAL);
        }
        //alert
        addMobilePrivilegeInfoVo(mobilePrivileges, privileges, PrivilegeCodes.MENU_MONITOR, PrivilegeCodes.MENU_ALARM);
        //app
        addMobilePrivilegeInfoVo(mobilePrivileges, privileges, PrivilegeCodes.MENU_TASK, PrivilegeCodes.MENU_APP_MANAGEMENT);
        //developer
        addMobilePrivilegeInfoVo(mobilePrivileges, privileges, PrivilegeCodes.MENU_TASK, PrivilegeCodes.MENU_DEVELOPER_APPROVAL);
        //dashboard
        addMobilePrivilegeInfoVo(mobilePrivileges, privileges, PrivilegeCodes.MENU_MONITOR, PrivilegeCodes.MENU_DASHBOARD);
        return mobilePrivileges;
    }

    /**
     * @param mobilePrivileges
     * @param privileges
     * @param menuCode
     * @param funcCode
     */
    private void addMobilePrivilegeInfoVo(List<MobilePrivilegeInfoVo> mobilePrivileges, List<PrivilegeInfo> privileges, String menuCode, String funcCode) {
        if (CollectionUtils.isEmpty(privileges)) {
            return;
        }
        privileges.forEach(each -> {
            if (StringUtils.equals(each.getCode(), menuCode) && CollectionUtils.isNotEmpty(each.getChildren())) {
                each.getChildren().forEach(child -> {
                    if (StringUtils.equals(child.getCode(), funcCode)) {
                        MobilePrivilegeInfoVo mobilePrivilegeInfoVo = BeanMapper.map(child, MobilePrivilegeInfoVo.class);
                        mobilePrivileges.add(mobilePrivilegeInfoVo);
                    }
                });
            }
        });
    }
}
