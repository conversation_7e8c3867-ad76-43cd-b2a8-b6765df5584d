package com.pax.market.functional.mobile.admin;


import com.pax.market.domain.entity.global.app.App;
import com.pax.market.dto.mobile.MobileApkOfflineRequest;
import com.pax.market.dto.mobile.MobileAppApproveRequest;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.vo.mobile.MobileApkDetailVo;
import com.pax.market.vo.mobile.MobileApkPageVo;
import com.pax.market.vo.mobile.MobileAppDetailVo;
import com.pax.market.vo.mobile.MobileAppVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @description
 * @date 2024/6/3 15:35
 */

public interface MobileAdminAppFunc {

    List<MobileAppVo> getApps(Page page, String name, String status);

    MobileAppDetailVo getAppDetail(Long appId);

    void approveApp(Long appId, Long apkId);

    void rejectApp(Long appId, Long apkId, MobileAppApproveRequest appApproveRequest);

    void offlineApp(Long appId);

    void onlineApp(Long appId);

    void onlineApk(Long appId, Long apkId);

    void offlineApk(Long appId, Long apkId, MobileApkOfflineRequest apkOfflineRequest);
    
    void deleteApk(Long appId, Long apkId);

    void deleteApp(Long appId);

    Long createApkDownloadTask(Long apkId, Long factoryId);

    Long createApkParamTemplateDownloadTask(Long apkId, String paramTemplateName);

    void deleteApkParamTemplate(Long apkId, String paramTemplateName, Boolean ignoreUsed);

    MobileApkPageVo searchApk(Page<App> page, Long appId, String status);

    MobileApkDetailVo getApkDetail(Long apkId);
}
