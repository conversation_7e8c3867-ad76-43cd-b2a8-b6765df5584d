/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.functional.mobile.terminal.impl;

import com.google.gson.Gson;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.config.MobileApiBizConfig;
import com.pax.market.domain.entity.global.setting.Code;
import com.pax.market.domain.entity.market.pushtask.TerminalOperation;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalDetail;
import com.pax.market.domain.entity.market.terminal.TerminalPedInfo;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.maxsearch.query.TerminalSearchFunc;
import com.pax.market.functional.mobile.terminal.MobileAdminTerminalFuncService;
import com.pax.market.functional.support.ResellerApkSignatureSupport;
import com.pax.market.functional.support.TerminalOperationSupport;
import com.pax.market.functional.support.TerminalRegistrySupport;
import com.pax.market.functional.terminal.TerminalFunctionService;
import com.pax.market.functional.utils.TerminalOnlineChecker;
import com.pax.market.functional.validation.Validators;
import com.pax.market.functional.validation.validators.terminal.TerminalOperationValidator;
import com.pax.market.functional.validation.validators.terminal.TerminalQueryValidator;
import com.pax.market.vo.admin.management.terminal.TerminalDetailPageVo;
import com.pax.market.vo.admin.management.terminal.TerminalDetailVo;
import com.pax.market.vo.mobile.MobileTerminalBasicVo;
import com.pax.market.vo.mobile.MobileTerminalDetailVo;
import com.pax.market.vo.mobile.MobileTerminalPedStatusVo;
import com.paxstore.global.domain.service.market.MarketGeneralSettingService;
import com.paxstore.global.domain.service.market.MarketSettingService;
import com.paxstore.global.domain.service.setting.CodeService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.market.domain.service.terminal.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/9
 */
@FunctionalService
@RequiredArgsConstructor
public class MobileAdminTerminalFuncServiceImpl extends AbstractFunctionalService implements MobileAdminTerminalFuncService {

    private final TerminalFunctionService terminalFunctionService;
    private final TerminalRegistrySupport terminalRegistrySupport;
    private final TerminalAccessTimeService terminalAccessTimeService;
    private final MarketTerminalService marketTerminalService;
    private final CodeService codeService;
    private final TerminalDetailService terminalDetailService;
    private final TerminalOnlineStatusService terminalOnlineStatusService;
    private final MobileApiBizConfig mobileApiBizConfig;
    private final TerminalOperationSupport terminalOperationSupport;
    private final TerminalPedInfoService terminalPedInfoService;
    private final DeviceInfoService deviceInfoService;
    private final ResellerApkSignatureSupport resellerApkSignatureSupport;
    private final TerminalSearchFunc terminalSearchFunc;
    private final MarketSettingService marketSettingService;
    private String defaultOrderBy = "a.id desc";

    @Override
    public List<MobileTerminalBasicVo> searchTerminals(Page page, String tidOrSn) {
        if (StringUtils.isEmpty(page.getOrderBy())) {
            //默认按照id倒序
            page.setOrderBy(defaultOrderBy);
        }
        Terminal query = new Terminal();
        query.setSerialNo(tidOrSn);
        Validators.validate(new TerminalQueryValidator(query));
        Page<Terminal> terminalPage = terminalSearchFunc.findPagedTerminals(page, getCurrentMarketId(), query);
        if (CollectionUtils.isEmpty(terminalPage.getList())) {
            query = new Terminal();
            query.setTID(tidOrSn);
            terminalPage = terminalSearchFunc.findPagedTerminals(page, getCurrentMarketId(), query);
        }
        List<MobileTerminalBasicVo> res = terminalPage.getList().stream().map(terminal -> {
            terminalRegistrySupport.loadTerminalModelResellerMerchantDetails(terminal);
            terminalAccessTimeService.loadTerminalLastAccessTime(terminal);
            MobileTerminalBasicVo vo = convert2MobileTerminalVo(terminal);
            return vo;
        }).collect(Collectors.toList());

        return res;
    }

    @Override
    public MobileTerminalDetailVo getTerminalById(Long terminalId) {
        Terminal terminal = terminalFunctionService.getTerminal(terminalId);
        if (terminal==null) {
            throw new BusinessException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        terminalRegistrySupport.loadTerminalResellerMerchantDetails(terminal);
        terminalAccessTimeService.loadTerminalLastAccessTime(terminal);
        List<TerminalDetail> terminalDetails = terminalDetailService.findByTerminal(terminalId);

        //需要把一些固定的信息添加进去
        boolean isContainTerminalStatus = false;
        boolean isContainResetTerminal = false;
        boolean isContainRestartTerminal = false;
        boolean isContainLockTerminal = false;
        for (TerminalDetail terminalDetail : terminalDetails) {
            if (StringUtils.equals(TerminalDetailKeys.TERMINAL_STATUS, terminalDetail.getKey())) {
                isContainTerminalStatus = true;
            } else if (StringUtils.equals(TerminalDetailKeys.RESET_TM, terminalDetail.getKey())) {
                isContainResetTerminal = true;
            } else if (StringUtils.equals(TerminalDetailKeys.RESTART_TM, terminalDetail.getKey())) {
                isContainRestartTerminal = true;
            } else if (StringUtils.equals(TerminalDetailKeys.LOCK_TM, terminalDetail.getKey())) {
                isContainLockTerminal = true;
            }
        }
        if (!isContainTerminalStatus) {
            terminalDetails.add(new TerminalDetail(TerminalDetailKeys.TERMINAL_STATUS));
        }
        if (!isContainResetTerminal) {
            terminalDetails.add(new TerminalDetail(TerminalDetailKeys.RESET_TM));
        }
        if (!isContainRestartTerminal) {
            terminalDetails.add(new TerminalDetail(TerminalDetailKeys.RESTART_TM));
        }
        if (!isContainLockTerminal) {
            terminalDetails.add(new TerminalDetail(TerminalDetailKeys.LOCK_TM));
        }
        boolean containPendingOperation = terminalDetailService.loadOperationStatus(terminalId, terminalDetails);

        for (TerminalDetail terminalDetail : terminalDetails) {
            if (StringUtils.equals(terminalDetail.getKey(), TerminalDetailKeys.TERMINAL_STATUS)) {
                terminalDetail.setValue(TerminalOnlineChecker.isTerminalOnline(terminalId) ? "AVL" : "");
                break;
            }
        }

        //代理商签名关闭+没签名，不返回，和web端一致
        boolean allowResellerSignature = resellerApkSignatureSupport.isResellerSignatureEnabled(getCurrentMarketId(), terminal.getReseller().getId(), terminal.getFactoryId());
        if (!allowResellerSignature) {
            terminalDetails.removeIf(each -> StringUtils.equals(TerminalDetailKeys.HAS_RESELLER_CERTIFICATE, each.getKey()) && StringUtils.equals("false", each.getValue()));
        }


        Terminal masterTerminal = deviceInfoService.getMasterTerminal(terminal.getSerialNo());
        if (masterTerminal != null) {
            TerminalDetail terminalDetail = new TerminalDetail();
            terminalDetail.setKey("masterTerminal");
            Map<String, String> terminalInstanceMap = new LinkedHashMap<>();
            terminalInstanceMap.put("id", String.valueOf(masterTerminal.getId()));
            terminalInstanceMap.put("serialNo", masterTerminal.getSerialNo());
            Gson gson = new Gson();
            terminalDetail.setValue(gson.toJson(terminalInstanceMap));
            terminalDetails.add(terminalDetail);
        }

        TerminalDetailPageVo<TerminalDetailVo> terminalDetailPageVo = new TerminalDetailPageVo<>();
        terminalDetailPageVo.setList(BeanMapper.mapList(terminalDetails, TerminalDetailVo.class));
        terminalDetailPageVo.setTotalCount(terminalDetails.size());
        if (TerminalOnlineChecker.isTerminalOnline(terminalId)) {
            terminalDetailPageVo.setContainPendingOperation(containPendingOperation);
        }

        MobileTerminalBasicVo mobileTerminalBasicVo = convert2MobileTerminalVo(terminal);
        MobileTerminalDetailVo detailVo = BeanMapper.map(mobileTerminalBasicVo, MobileTerminalDetailVo.class);
        detailVo.setDetailList(BeanMapper.mapList(terminalDetails, MobileTerminalDetailVo.KeyValueVo.class));

        return detailVo;
    }

    @Override
    public List<MobileTerminalBasicVo> getTerminalByIdList(List<Long> terminalIdList) {
        if (CollectionUtils.isEmpty(terminalIdList)) {
            return Lists.newArrayList();
        }
        if(terminalIdList.size() > mobileApiBizConfig.getTerminalMaxSearchSize()) {
            throw new BusinessException(ApiCodes.EXCEED_MAX_FETCH_SIZE, null, String.valueOf(mobileApiBizConfig.getTerminalMaxSearchSize()));
        }

        List<Terminal> terminalList = marketTerminalService.findTerminalList(terminalIdList);
        if (CollectionUtils.isEmpty(terminalList)) {
            return Lists.newArrayList();
        }

        return terminalList.stream().map(terminal -> {
            terminalRegistrySupport.loadTerminalModelResellerMerchantDetails(terminal);
            terminalAccessTimeService.loadTerminalLastAccessTime(terminal);
            return convert2MobileTerminalVo(terminal);
        }).toList();

    }

    private MobileTerminalBasicVo convert2MobileTerminalVo(Terminal terminal){

        MobileTerminalBasicVo terminalBasicVo = new MobileTerminalBasicVo();
        terminalBasicVo.setId(terminal.getId());
        terminalBasicVo.setSerialNo(terminal.getSerialNo());
        terminalBasicVo.setTID(terminal.getTID());
        terminalBasicVo.setResellerName(terminal.getReseller()==null?null:terminal.getReseller().getName());
        terminalBasicVo.setMerchantName(terminal.getMerchant()==null?null:terminal.getMerchant().getName());
        terminalBasicVo.setFactoryName(terminal.getModel()==null?null:(terminal.getModel().getFactory()==null?null:terminal.getModel().getFactory().getName()));
        terminalBasicVo.setModelName(terminal.getModel()==null?null:terminal.getModel().getName());
        terminalBasicVo.setProductType(terminal.getModel()==null?null:terminal.getModel().getProductType());
        Code code = codeService.getCode(CodeTypes.PRODUCT_TYPE, terminalBasicVo.getProductType(), RequestLocaleHolder.getLocale());
        if (code!=null) {
            terminalBasicVo.setProductTypeLabel(String.format("%s - %s", code.getParent() != null ? code.getParent().getLabel() : "", code.getLabel()));
        }

        terminalOnlineStatusService.loadTerminalOnlineStatusInfo(terminal);
        terminalBasicVo.setOnlineStatus(terminal.getOnlineStatus());
        terminalBasicVo.setOnlineStatusChangeDate(terminal.getOnlineStatusChangeDate());
        terminalAccessTimeService.loadTerminalLastAccessTime(terminal);
        terminalBasicVo.setLastAccessTime(terminal.getLastAccessTime());
        terminalBasicVo.setLastActiveTime(terminal.getLastActiveTime());
        terminalBasicVo.setLastDisableTime(terminal.getLastDisableTime());
        terminalBasicVo.setCreatedDate(terminal.getCreatedDate());
        terminalBasicVo.setStatus(terminal.getStatus());
        terminalBasicVo.setLocation(terminal.getLocation());
        terminalBasicVo.setRemark(terminal.getRemark());
        terminalBasicVo.setPedKeyStatusEnable(SystemPropertyHelper.isPedKeyStatusEnabled());
        terminalBasicVo.setAllowActiveTerminal(getCurrentMarket().getAllowActiveTerminal() && marketSettingService.getMarketSettingAsBool(getCurrentMarketId(), MarketSettingKey.allowActiveTerminal, true));
        return terminalBasicVo;
    }


    @Override
    public void activeTerminal(Long terminalId) {
        terminalFunctionService.validateAndGetTerminal(terminalId);
        terminalFunctionService.activeTerminal(terminalId, getCurrentMarket());
    }

    @Override
    public void disableTerminal(Long terminalId) {
        terminalFunctionService.validateAndGetTerminal(terminalId);
        terminalFunctionService.disableTerminal(terminalId, getCurrentMarket());
    }

    @Override
    public void deleteTerminal(Long terminalId) {
        Validators.validate(new TerminalOperationValidator(MarketSettingKey.tmDelete));
        terminalFunctionService.validateAndGetTerminal(terminalId);
        Terminal deletedTerminal = terminalFunctionService.deleteTerminal(terminalId);
        MarketTerminalService.clearTerminalCache(deletedTerminal);
        TerminalRegistryService.clearTerminalCache(deletedTerminal);
    }

    @Override
    public void lockTerminal(Long terminalId) {
        TerminalOperation terminalOperation = new TerminalOperation();
        terminalOperation.setTerminalId(terminalId);
        terminalOperation.setKey(TerminalDetailKeys.LOCK_TM);
        terminalOperation.setValue(SystemConstants.OPT_VALUE_LOCK_TM);
        terminalOperationSupport.pushOperationMsg(terminalId, terminalOperation);
    }

    @Override
    public void unlockTerminal(Long terminalId) {
        TerminalOperation terminalOperation = new TerminalOperation();
        terminalOperation.setTerminalId(terminalId);
        terminalOperation.setKey(TerminalDetailKeys.LOCK_TM);
        terminalOperation.setValue(SystemConstants.OPT_VALUE_UNLOCK_TM);
        terminalOperationSupport.pushOperationMsg(terminalId, terminalOperation);
    }


    @Override
    public MobileTerminalPedStatusVo getTerminalPedStatusVo(Long terminalId) {
        terminalFunctionService.validateAndGetTerminal(terminalId);
        MobileTerminalPedStatusVo.MobileTerminalPedStatusVoBuilder builder = MobileTerminalPedStatusVo.builder();
        if (!SystemPropertyHelper.isPedKeyStatusEnabled()) {
            return builder.build();
        }
        TerminalPedInfo terminalPedInfo = terminalPedInfoService.getByTerminal(terminalId);
        if (terminalPedInfo == null) {
            return builder.build();
        }
        return builder.info(terminalPedInfo.getInfo()).build();
    }

}