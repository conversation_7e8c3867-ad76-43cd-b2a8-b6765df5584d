package com.pax.market.functional.mobile.admin.impl;

import com.pax.market.constants.AirShieldRule;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.mobile.admin.MobileAirShieldAttestationFunc;
import com.pax.market.mq.contract.airshield.TerminalDetectResult;
import com.pax.market.vo.admin.service.airshield.DetectionItem;
import com.pax.market.vo.mobile.MobileDetectionVo;
import com.paxstore.market.domain.service.vas.TerminalAttestationInitialAttributeService;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.vas.func.goinsight.DataQueryFunc;
import com.zolon.saas.vas.func.goinsight.dto.ApiQueryAuth;
import com.zolon.saas.vas.func.goinsight.dto.DataQueryRequest;
import com.zolon.saas.vas.func.goinsight.dto.DataQueryResult;
import com.zolon.saas.vas.func.goinsight.dto.Row;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FunctionalService
@RequiredArgsConstructor
@Slf4j
public class MobileAttestationFuncServiceImpl extends AbstractFunctionalService implements MobileAirShieldAttestationFunc {

    private final DataQueryFunc dataQueryFunc;
    private final TerminalAttestationInitialAttributeService attributeService;

    @Override
    public List<MobileDetectionVo> findHistoryList(Long terminalId, String tz, String ip) {
        if (!existedHistory(getCurrentMarketId(), terminalId)) {
            return new ArrayList<>();
        }
        List<TerminalDetectResult> detectResults = getFromInsight(getCurrentMarketId(), terminalId, ip, tz);
        if (CollectionUtils.isEmpty(detectResults)) {
            return new ArrayList<>();
        }
        return generateDetectHistoryVo(detectResults.get(0));
    }

    private boolean existedHistory(Long marketId, Long terminalId) {
        return attributeService.existTabTime(marketId, terminalId);
    }

    /**
     * goinsight 查询 转化
     *
     * @param result
     * @return
     */
    private List<MobileDetectionVo> generateDetectHistoryVo(TerminalDetectResult result) {
        return resolveItems(result);
    }

    private List<MobileDetectionVo> resolveItems(TerminalDetectResult result) {
        List<DetectionItem> list = Stream.of(
                        new DetectionItem().setRule(AirShieldRule.BE_HOOKED).setSeverity(AirShieldRule.BE_HOOKED.getSeverity()).setResult(result.isBeHooked()),
                        new DetectionItem().setRule(AirShieldRule.EMULATOR_DETECTION).setSeverity(AirShieldRule.EMULATOR_DETECTION.getSeverity()).setResult(result.isEmulator()),
                        new DetectionItem().setRule(AirShieldRule.APP_BLACKLIST).setSeverity(AirShieldRule.APP_BLACKLIST.getSeverity()).setResult(result.isAppBlacklist()),
                        new DetectionItem().setRule(AirShieldRule.SYS_FILE_ACCESS).setSeverity(AirShieldRule.SYS_FILE_ACCESS.getSeverity()).setResult(result.isSysFileAccess()),
                        new DetectionItem().setRule(AirShieldRule.DEV_DEBUG_MODE).setSeverity(AirShieldRule.DEV_DEBUG_MODE.getSeverity()).setResult(result.isDevDebugMode()),
                        new DetectionItem().setRule(AirShieldRule.NET_CELL_IP).setSeverity(AirShieldRule.NET_CELL_IP.getSeverity()).setResult(result.isNetCellIp()),
                        new DetectionItem().setRule(AirShieldRule.NET_WIFI_IP).setSeverity(AirShieldRule.NET_WIFI_IP.getSeverity()).setResult(result.isNetWifiIp()),
                        new DetectionItem().setRule(AirShieldRule.DEVICE_ID).setSeverity(AirShieldRule.DEVICE_ID.getSeverity()).setResult(result.isDeviceId()),
                        new DetectionItem().setRule(AirShieldRule.BE_ROOTED).setSeverity(AirShieldRule.BE_ROOTED.getSeverity()).setResult(result.isBeRooted()),
                        new DetectionItem().setRule(AirShieldRule.OS_MODIFICATION).setSeverity(AirShieldRule.OS_MODIFICATION.getSeverity()).setResult(result.isOs()),
                        new DetectionItem().setRule(AirShieldRule.SYS_VERSION).setSeverity(AirShieldRule.SYS_VERSION.getSeverity()).setResult(result.isSysVersion())
                ).sorted((Comparator.comparing(DetectionItem::isResult)
                        .thenComparing((x, y) -> y.getSeverity().getWeight() - x.getSeverity().getWeight())))
                .toList();
        return list.stream().map(it ->
                MobileDetectionVo.builder().severity(it.getSeverity().getLabel())
                        .result(it.isResult())
                        .rule(it.getRule()).build()
        ).collect(Collectors.toList());
    }


    List<TerminalDetectResult> getFromInsight(Long marketId, Long terminalId, String ip, String tz) {
        ApiQueryAuth auth = new ApiQueryAuth()
                .setMarketId(marketId)
                .setTerminalId(terminalId);
        DataQueryRequest dataQueryRequest = new DataQueryRequest(auth, SystemPropertyHelper.getAirShieldDatasetQueryCode(), "");
        dataQueryRequest.setPageNo(1).setPageSize(1000);
        SingleResponse<DataQueryResult> bizData = dataQueryFunc.getAppBizDataFromGoInsight(dataQueryRequest, tz, ip);
        if (bizData == null || !bizData.isSuccess() || CollectionUtils.isEmpty(bizData.getData().getRows())) {
            return new ArrayList<>();
        }
        List<List<Row>> rows = bizData.getData().getRows();
        return rows.stream().map(r -> toResult(r, marketId, terminalId)).collect(Collectors.toList());

    }

    private TerminalDetectResult toResult(List<Row> rows, Long marketId, Long terminalId) {
        TerminalDetectResult result = new TerminalDetectResult().setMarketId(marketId).setTerminalId(terminalId);
        rows.forEach(r -> row2Result(r, result));
        return result;
    }

    private void row2Result(Row row, TerminalDetectResult result) {
        switch (row.getColName()) {
            case "id":
                result.setId(Long.parseLong(row.getValue()));
                break;
            case "_eventtime":
                result.setDetectTime(DateUtils.parseDate(row.getValue()));
                break;
            case "appBlacklist":
                result.setAppBlacklist(Boolean.parseBoolean(row.getValue()));
                break;
            case "sysFileAccess":
                result.setSysFileAccess(Boolean.parseBoolean(row.getValue()));
                break;
            case "beRooted":
                result.setBeRooted(Boolean.parseBoolean(row.getValue()));
                break;
            case "isEmulator":
                result.setEmulator(Boolean.parseBoolean(row.getValue()));
                break;
            case "beHooked":
                result.setBeHooked(Boolean.parseBoolean(row.getValue()));
                break;
            case "devDebugMode":
                result.setDevDebugMode(Boolean.parseBoolean(row.getValue()));
                break;
            case "os":
                result.setOs(Boolean.parseBoolean(row.getValue()));
                break;
            case "deviceId":
                result.setDeviceId(Boolean.parseBoolean(row.getValue()));
                break;
            case "netCellIp":
                result.setNetCellIp(Boolean.parseBoolean(row.getValue()));
                break;
            case "netWifiIp":
                result.setNetWifiIp(Boolean.parseBoolean(row.getValue()));
                break;
            case "sysVersion":
                result.setSysVersion(Boolean.parseBoolean(row.getValue()));
                break;
            default:
        }
    }
}
