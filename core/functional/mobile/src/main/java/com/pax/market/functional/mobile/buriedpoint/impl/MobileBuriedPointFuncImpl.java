package com.pax.market.functional.mobile.buriedpoint.impl;

import com.pax.core.utils.BuildVersions;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.mobile.MobileBuriedPointRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.functional.AbstractFunctionalService;
import com.pax.market.functional.mobile.buriedpoint.MobileBuriedPointFunc;
import com.pax.market.mq.contract.buriedpoint.NavigoBuriedPointMessage;
import com.pax.market.mq.producer.gateway.buriedpoint.NavigoBuriedPointGateway;
import com.paxstore.global.domain.service.vas.VasConfigEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Map;

@FunctionalService
@RequiredArgsConstructor
@Slf4j
public class MobileBuriedPointFuncImpl extends AbstractFunctionalService implements MobileBuriedPointFunc {

    private final NavigoBuriedPointGateway navigoBuriedPointGateway;
    private final VasConfigEntityService vasConfigEntityService;


    @Override
    public void createBuriedPoints(MobileBuriedPointRequest buriedPointRequest) {
        boolean userSendUsageData = getCurrentUser() != null && BooleanUtils.toBoolean(getCurrentUser().getSendUsageData());
        boolean allowBuriedPoint = vasConfigEntityService.isVasEnabledGlobally() && SystemPropertyHelper.getAllowBuriedPointDataCollection() && userSendUsageData;
        if (null != buriedPointRequest.getBuriedPointList() && !buriedPointRequest.getBuriedPointList().isEmpty() && allowBuriedPoint) {
            for (Map buriedPointMap : buriedPointRequest.getBuriedPointList()) {
                NavigoBuriedPointMessage navigoBuriedPointMessage = new NavigoBuriedPointMessage();
                navigoBuriedPointMessage.setUserId(String.valueOf(getCurrentUserId()));
                navigoBuriedPointMessage.setMarketId(getCurrentMarketId());
                navigoBuriedPointMessage.setPaxstoreVersion(BuildVersions.get());
                if (null != getCurrentReseller()) {
                    navigoBuriedPointMessage.setResellerId(getCurrentResellerId());
                    if (null != getCurrentReseller().getParentId()) {
                        navigoBuriedPointMessage.setRoot(0);
                    } else {
                        navigoBuriedPointMessage.setRoot(1);
                    }
                }
                navigoBuriedPointMessage.setEventTime((Long) buriedPointMap.get("eventTime"));
                navigoBuriedPointMessage.setEventCode(String.valueOf(buriedPointMap.get("eventName")));
                navigoBuriedPointMessage.setEventParameter(String.valueOf(buriedPointMap.get("eventParam")));
                navigoBuriedPointMessage.setSourcePage(String.valueOf(buriedPointMap.get("fromPage")));
                navigoBuriedPointMessage.setTargetPage(String.valueOf(buriedPointMap.get("toPage")));
                navigoBuriedPointMessage.setFromTab(String.valueOf(buriedPointMap.get("fromTab")));
                navigoBuriedPointMessage.setToTab(String.valueOf(buriedPointMap.get("toTab")));
                navigoBuriedPointMessage.setOperationType(String.valueOf(buriedPointMap.get("operationType")));
                navigoBuriedPointMessage.setOperationParam(String.valueOf(buriedPointMap.get("operationParam")));
                navigoBuriedPointMessage.setEntity(String.valueOf(buriedPointMap.get("entity")));
                navigoBuriedPointMessage.setNetworkEnv(buriedPointRequest.getNetworkEnv());
                navigoBuriedPointMessage.setBrowserVersion(buriedPointRequest.getBrowserVersion());
                navigoBuriedPointMessage.setAgent(buriedPointRequest.getAgent());
                navigoBuriedPointMessage.setOperationSystem(buriedPointRequest.getOperationSystem());
                navigoBuriedPointGateway.send(navigoBuriedPointMessage);
            }
        }
    }
}
