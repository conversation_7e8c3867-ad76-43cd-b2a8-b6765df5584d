package com.pax.market.notification;

import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrder;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.zolon.saas.notification.func.dto.NotificationRequest;
import com.zolon.saas.notification.func.dto.NotificationRequestBuilder;
import com.zolon.saas.notification.func.enums.MessageCategory;
import com.zolon.saas.notification.func.enums.MessageType;
import com.zolon.saas.notification.func.enums.Operations;
import com.zolon.saas.notification.func.enums.ReceiverType;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;

/**
 * author mengxiaoxian
 * Date   2025/1/16 15:28
 */
@Component
@AllArgsConstructor
public class AirLinkNotificationService extends BaseNotificationService{

    private static final String MARKET_NAME = "marketName";
    private static final String REASON = "reason";
    private static final String ORDER_NUMBER = "orderNumber";


    public void sendApproveMarketChargeNotification(List<Long> receiveIds, Market market, AirLinkOrder airLinkOrder, String locale){
        Map<String, Serializable> map = new HashMap<>();
        map.put(MARKET_NAME, market.getName());
        map.put(ORDER_NUMBER, airLinkOrder.getOrderNumber());

        NotificationRequest notification = NotificationRequestBuilder.newInstance()
                .setOperation(Operations.AIR_LINK_APPROVE_CHARGE)
                .setSender(SYSTEM_USERID_PAXSTORE)
                .setCategory(MessageCategory.SYSTEM)
                .setType(MessageType.AIR_LINK_CHARGE_AUDIT)
                .setReceiverType(ReceiverType.GENERAL_GROUP)
                .setReceivers(receiveIds)
                .setUseTemplate(true)
                .setContentParamMap(map)
                .setTitleArguments(new String[]{airLinkOrder.getOrderNumber()})
                .setLocale(locale)
                .create();
        sendNotification(notification);
    }

    public void sendRejectMarketChargeNotification(List<Long> receiveIds, Market market, AirLinkOrder airLinkOrder, String locale){
        Map<String, Serializable> map = new HashMap<>();
        map.put(MARKET_NAME, market.getName());
        map.put(ORDER_NUMBER, airLinkOrder.getOrderNumber());
        map.put(REASON, airLinkOrder.getRemarks());

        NotificationRequest notification = NotificationRequestBuilder.newInstance()
                .setOperation(Operations.AIR_LINK_REJECT_CHARGE)
                .setSender(SYSTEM_USERID_PAXSTORE)
                .setCategory(MessageCategory.SYSTEM)
                .setType(MessageType.AIR_LINK_CHARGE_AUDIT)
                .setReceiverType(ReceiverType.GENERAL_GROUP)
                .setReceivers(receiveIds)
                .setUseTemplate(true)
                .setContentParamMap(map)
                .setTitleArguments(new String[]{airLinkOrder.getOrderNumber()})
                .setLocale(locale)
                .create();
        sendNotification(notification);
    }

    public void sendRechargeGlobalNotification(Long senderId, List<Long> receiveIds, Market market, String  orderNumber, String locale){
        Map<String, Serializable> map = new HashMap<>();
        map.put(MARKET_NAME, market.getName());
        map.put(ORDER_NUMBER, orderNumber);

        NotificationRequest notification = NotificationRequestBuilder.newInstance()
                .setOperation(Operations.AIR_LINK_MARKET_CHARGE)
                .setSender(senderId)
                .setCategory(MessageCategory.ACTIVITY)
                .setType(MessageType.AIR_LINK_MARKET_CHARGE)
                .setReceiverType(ReceiverType.GENERAL_GROUP)
                .setReceivers(receiveIds)
                .setUseTemplate(true)
                .setContentParamMap(map)
                .setTitleArguments(new String[]{orderNumber, market.getName()})
                .setLocale(locale)
                .create();
        sendNotification(notification);
    }

    public void sendMarketMonthDeductFailNotification(List<Long> receiveIds, String marketName){
        Map<String, Serializable> map = new HashMap<>();
        map.put(MARKET_NAME, marketName);

        NotificationRequest notification = NotificationRequestBuilder.newInstance()
                .setOperation(Operations.AIR_LINK_MONTHLY_DEDUCT_FAILURE)
                .setSender(SYSTEM_USERID_PAXSTORE)
                .setCategory(MessageCategory.SYSTEM)
                .setType(MessageType.AIR_LINK_MONTHLY_DEDUCT)
                .setReceiverType(ReceiverType.GENERAL_GROUP)
                .setReceivers(receiveIds)
                .setUseTemplate(true)
                .setContentParamMap(map)
                .setLocale(RequestLocaleHolder.getLocale())
                .create();
        sendNotification(notification);
    }

}
