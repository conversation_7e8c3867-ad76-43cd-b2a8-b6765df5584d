/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: FirmwareNotificationService.
 *
 * Revision History:
 * Date	                	Author	            	Action
 * 20190310  	        	jianrong           		Create
 * ===========================================================================================
 */
package com.pax.market.notification;

import com.pax.market.constants.PrivilegeCodes;
import com.pax.market.dto.market.MarketInfo;
import com.zolon.saas.notification.func.enums.MessageCategory;
import com.zolon.saas.notification.func.enums.MessageType;
import com.zolon.saas.notification.func.enums.Operations;
import com.zolon.saas.notification.func.enums.ReceiverType;
import com.zolon.saas.notification.func.dto.NotificationRequest;
import com.zolon.saas.notification.func.dto.NotificationRequestBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class FirmwareNotificationService extends BaseNotificationService {
    private static final Logger logger = LoggerFactory.getLogger(FirmwareNotificationService.class);

    private static final String KEY_COMMENT = "comment";
    private static final String KEY_FIRMWARE_NAME = "firmwareName";
    private static final String KEY_FIRMWARE_COMMITTER = "fmCommitter";
	private static final String KEY_PORTAL_MARKETPLACE_URL = "portalMarketplaceUrl";
    private static final String KEY_ADMIN_MARKETPLACE_URL = "adminMarketplaceUrl";
	private static final String KEY_MARKET = "market";

	public void sendSubmitFirmwareNotification(Long senderId, String firmwareName, String committer, MarketInfo market,
                                               String locale) {
        Map<String, Serializable> map = new HashMap<>();
        map.put(KEY_MARKET, market);
        map.put(KEY_PORTAL_MARKETPLACE_URL, deployInfoConfigProps.getPortalRedirectUrlNoDomain());
        map.put(KEY_ADMIN_MARKETPLACE_URL, deployInfoConfigProps.getAdminRedirectUrlNoDomain());
        map.put(KEY_FIRMWARE_NAME, firmwareName);
        map.put(KEY_FIRMWARE_COMMITTER, committer);

        List<Long> receiverIdList = getUserIdListByPrivilegeCode(PrivilegeCodes.MENU_FIRMWARE_APPROVAL);
        NotificationRequest notification = NotificationRequestBuilder.newInstance()
                .setOperation(Operations.SUBMIT_FIRMWARE)
                .setSender(senderId)
                .setCategory(MessageCategory.ACTIVITY)
                .setType(MessageType.SYSTEM_TASK_AUDIT_FIRMWARE)
                .setReceiverType(ReceiverType.GENERAL_GROUP)
                .setReceivers(receiverIdList)
                .setUseTemplate(true)
                .setContentParamMap(map)
                .setTitleArguments(new String[] { firmwareName })
                .setLocale(locale)
                .create();
        sendNotification(notification);
    }

    public void sendApproveFirmwareNotification(Long senderId, Long receiverId, String firmwareName, String committer,
                                                MarketInfo market, String locale) {
        if (this.hasFirmwareManagementPrivilege(receiverId)) {
            this.doSendFirmwareOperationNtfMessage(
                    Operations.APPROVE_FIRMWARE,
                    senderId,
                    MessageCategory.SYSTEM,
                    MessageType.SYSTEM_FIRMWARE_STATUS_UPDATE,
                    receiverId,
                    null,
                    firmwareName,
                    committer,
                    market,
                    new String[]{firmwareName},
                    locale);
        }
    }

    public void sendRejectFirmwareNotification(Long senderId, Long receiverId, String firmwareName, String committer,
                                               String comment, MarketInfo market, String locale) {
        if (this.hasFirmwareManagementPrivilege(receiverId)) {
            NotificationRequest notification = this.createFirmwareOperationNtfMessage(
                    Operations.REJECT_FIRMWARE,
                    senderId,
                    MessageCategory.SYSTEM,
                    MessageType.SYSTEM_FIRMWARE_STATUS_UPDATE,
                    receiverId,
                    null,
                    firmwareName,
                    committer,
                    market,
                    new String[]{firmwareName, market.getName()},
                    locale,
                    comment);
            sendNotification(notification);
        }
    }

    public void sendOnlineFirmwareNotification(Long senderId, Long receiverId, String firmwareName, String committer,
                                               MarketInfo market, String locale) {
        if (this.hasFirmwareManagementPrivilege(receiverId)) {
            this.doSendFirmwareOperationNtfMessage(
                    Operations.ONLINE_FIRMWARE,
                    senderId,
                    MessageCategory.SYSTEM,
                    MessageType.SYSTEM_FIRMWARE_STATUS_UPDATE,
                    receiverId,
                    null,
                    firmwareName,
                    committer,
                    market,
                    new String[]{firmwareName, market.getName()},
                    locale);
        }
    }

    public void sendOfflineFirmwareNotification(Long senderId, Long receiverId, String firmwareName, String committer,
                                                MarketInfo market, String locale, String comment) {
        if (this.hasFirmwareManagementPrivilege(receiverId)) {
            this.doSendFirmwareOperationNtfMessage(
                    Operations.OFFLINE_FIRMWARE,
                    senderId,
                    MessageCategory.SYSTEM,
                    MessageType.SYSTEM_FIRMWARE_STATUS_UPDATE,
                    receiverId,
                    null,
                    firmwareName,
                    committer,
                    market,
                    new String[]{firmwareName, market.getName()},
                    locale,
                    comment);
        }
    }

    public void sendDeleteFirmwareNotification(Long senderId, Long receiverId, String firmwareName, String committer,
                                               MarketInfo market, String locale) {
        if (this.hasFirmwareManagementPrivilege(receiverId)) {
            this.doSendFirmwareOperationNtfMessage(
                    Operations.DELETE_FIRMWARE,
                    senderId,
                    MessageCategory.SYSTEM,
                    MessageType.SYSTEM_FIRMWARE_STATUS_UPDATE,
                    receiverId,
                    null,
                    firmwareName,
                    committer,
                    market,
                    new String[]{firmwareName, market.getName()},
                    locale);
        }
    }

    private boolean hasFirmwareManagementPrivilege(Long userId) {
        List<Long> userIdList = getUserIdListByPrivilegeCode(PrivilegeCodes.MENU_FIRMWARE_MANAGEMENT);
        if (userIdList == null || userIdList.isEmpty()) {
            return false;
        }
        for (Long id : userIdList) {
            if (id.equals(userId)) {
                return true;
            }
        }
        return false;
    }

    public void sendOnlineFirmwareSubscriptionNotification(Long senderId, List<Long> receiverIds, String firmwareName,
                                               MarketInfo market, String locale) {
        this.doSendFirmwareOperationNtfMessage(
                Operations.ONLINE_FIRMWARE_SUBSCRIPTION,
                senderId,
                MessageCategory.SUBSCRIPTION,
                MessageType.SUBSCRIPTION_FIRMWARE_STATUS_UPDATE,
                null,
                receiverIds,
                firmwareName,
                null,
                market,
                new String[]{firmwareName, market.getName()},
                locale);
    }

    public void sendOfflineFirmwareSubscriptionNotification(Long senderId, List<Long> receiverIds, String firmwareName,
                                                MarketInfo market, String locale, String comment) {
        this.doSendFirmwareOperationNtfMessage(
                Operations.OFFLINE_FIRMWARE_SUBSCRIPTION,
                senderId,
                MessageCategory.SUBSCRIPTION,
                MessageType.SUBSCRIPTION_FIRMWARE_STATUS_UPDATE,
                null,
                receiverIds,
                firmwareName,
                null,
                market,
                new String[]{firmwareName, market.getName()},
                locale,
                comment);
    }


    public void sendOfflineFwSubsMktNotification(Long senderId, List<Long> receiverIds, String firmwareName,
                                                MarketInfo market, String locale, String comment) {
        this.doSendFirmwareOperationNtfMessage(
                Operations.OFFLINE_FIRMWARE_SUBSCRIPTION_MARKET,
                senderId,
                MessageCategory.SUBSCRIPTION,
                MessageType.SYSTEM_PLATFORM_NOTIFICATION,
                null,
                receiverIds,
                firmwareName,
                null,
                market,
                new String[]{firmwareName},
                locale,
                comment);
    }

    public void sendDeleteFirmwareSubscriptionNotification(Long senderId, List<Long> receiverIds, String firmwareName,
                                               MarketInfo market, String locale) {
        this.doSendFirmwareOperationNtfMessage(
                Operations.DELETE_FIRMWARE_SUBSCRIPTION,
                senderId,
                MessageCategory.SUBSCRIPTION,
                MessageType.SUBSCRIPTION_FIRMWARE_STATUS_UPDATE,
                null,
                receiverIds,
                firmwareName,
                null,
                market,
                new String[]{firmwareName, market.getName()},
                locale);
    }

    private NotificationRequest createFirmwareOperationNtfMessage(Operations operation,
                                                                  Long senderId,
                                                                  MessageCategory messageCategory,
                                                                  MessageType messageType,
                                                                  Long receiverId,
                                                                  List<Long> receiverIdList,
                                                                  String firmwareName,
                                                                  String committer,
                                                                  MarketInfo market,
                                                                  String[] titleArgs,
                                                                  String locale,
                                                                  String comment) {
        Map<String, Serializable> map = new HashMap<>();
        map.put(KEY_MARKET, market);
        map.put(KEY_PORTAL_MARKETPLACE_URL, deployInfoConfigProps.getPortalRedirectUrlNoDomain());
        map.put(KEY_ADMIN_MARKETPLACE_URL, deployInfoConfigProps.getAdminRedirectUrlNoDomain());
        map.put(KEY_FIRMWARE_NAME, firmwareName);
        map.put(KEY_FIRMWARE_COMMITTER, committer);
        map.put(KEY_COMMENT, comment);

        NotificationRequestBuilder builder = NotificationRequestBuilder.newInstance()
                .setOperation(operation)
                .setSender(senderId)
                .setCategory(messageCategory)
                .setType(messageType)
                .setUseTemplate(true)
                .setContentParamMap(map)
                .setTitleArguments(titleArgs)
                .setLocale(locale);

        if (receiverIdList != null) {
            builder.setReceiverType(ReceiverType.GENERAL_GROUP);
            builder.setReceivers(receiverIdList);
        } else {
            builder.setReceiverType(ReceiverType.PERSONAL);
            builder.setReceiver(receiverId);
        }

        return builder.create();
    }

    private void doSendFirmwareOperationNtfMessage(Operations operation,
                                                   Long senderId,
                                                   MessageCategory messageCategory,
                                                   MessageType messageType,
                                                   Long receiverId,
                                                   List<Long> receiverIdList,
                                                   String firmwareName,
                                                   String committer,
                                                   MarketInfo market,
                                                   String[] titleArgs,
                                                   String locale) {
        doSendFirmwareOperationNtfMessage(operation,
                senderId,
                messageCategory,
                messageType,
                receiverId,
                receiverIdList,
                firmwareName,
                committer,
                market,
                titleArgs,
                locale,
                null);
    }

    private void doSendFirmwareOperationNtfMessage(Operations operation,
                                                   Long senderId,
                                                   MessageCategory messageCategory,
                                                   MessageType messageType,
                                                   Long receiverId,
                                                   List<Long> receiverIdList,
                                                   String firmwareName,
                                                   String committer,
                                                   MarketInfo market,
                                                   String[] titleArgs,
                                                   String locale,
                                                   String comment) {
        NotificationRequest notification = this.createFirmwareOperationNtfMessage(
                operation,
                senderId,
                messageCategory,
                messageType,
                receiverId,
                receiverIdList,
                firmwareName,
                committer,
                market,
                titleArgs,
                locale,
                comment
        );
        sendNotification(notification);
    }
}
