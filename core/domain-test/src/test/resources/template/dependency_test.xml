<?xml version="1.0" encoding="gb2312"?>
<!-- edited with XMLSpy v2013 (http://www.altova.com) by  () -->
<Schema>
    <Groups>
        <Group>
            <ID>sys_G0</ID>
            <Title>Industry</Title>
            <Order>1</Order>
            <Description/>
        </Group>
    </Groups>
    <!-- Files Definition -->
    <Files>
        <File>
            <ID>sys_F1</ID>
            <FileName>sys_param.p</FileName>
            <Description/>
        </File>
    </Files>
    <!-- Parameter Definition -->
    <Parameters>
        <Header>
            <Title>Industry Features</Title>
            <DisplayStyle>foldable</DisplayStyle>
            <DefaultStyle>open</DefaultStyle>
            <Display>true</Display>
            <Parameter>
                <Type>single</Type>
                <InputType>select</InputType>
                <PID>sys.cap.dLanguage</PID>
                <Title>Language Setting</Title>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Select>{"English":"English","ChineseSimplified":"Chinese Simplified"}</Select>
                <Defaultvalue>English</Defaultvalue>
                <Description>Multi-Language</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>select</InputType>
                <PID>sys.cap.currencyCode</PID>
                <Title>Currency Code</Title>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Select>{"US":"US","CN":"CN"}</Select>
                <Defaultvalue>US</Defaultvalue>
                <Description>Multi-Language</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Float</DataType>
                <PID>sys.cap.currency</PID>
                <Title>English US Currency</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Defaultvalue>9.99</Defaultvalue>
                <FractionDigits>2</FractionDigits>
                <Description>English US Currency</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
                <DependsOn>sys.cap.dLanguage=English,Japan</DependsOn>
                <DependsOn>sys.cap.currencyCode=US</DependsOn>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Float</DataType>
                <PID>sys.cap.currency</PID>
                <Title>ChineseSimplified CN Currency</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Defaultvalue>9.999</Defaultvalue>
                <FractionDigits>3</FractionDigits>
                <Description>ChineseSimplified CN Currency</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
                <DependsOn>sys.cap.dLanguage=ChineseSimplified</DependsOn>
                <DependsOn>sys.cap.currencyCode=CN,JP</DependsOn>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Float</DataType>
                <PID>sys.cap.dependent.lang</PID>
                <Title>Dependent English</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Defaultvalue>123.00</Defaultvalue>
                <Description>Dependent English</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
                <DependsOn>sys.cap.dLanguage=English</DependsOn>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Float</DataType>
                <PID>sys.cap.dependent.lang</PID>
                <Title>Dependent ChineseSimplified</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Defaultvalue>456.00</Defaultvalue>
                <Description>Dependent ChineseSimplified</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
                <DependsOn>sys.cap.dLanguage=ChineseSimplified</DependsOn>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Amount</DataType>
                <PID>sys.cap.amount</PID>
                <Title>Amount</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Select/>
                <Defaultvalue>99</Defaultvalue>
                <Description>Amount</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Amount</DataType>
                <PID>sys.cap.amount</PID>
                <Title>Amount</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Select/>
                <Defaultvalue>99</Defaultvalue>
                <Description>Amount</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Amount</DataType>
                <PID>sys.cap.amount</PID>
                <Title>Amount</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Select/>
                <Defaultvalue>99</Defaultvalue>
                <Description>Amount</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Float</DataType>
                <PID>sys.cap.dependent.amount</PID>
                <Title>Dependent Amount1</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Defaultvalue>100.00</Defaultvalue>
                <Description>Table Number Prompt</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
                <DependsOn>sys.cap.amount=100</DependsOn>
            </Parameter>
            <Parameter>
                <Type>single</Type>
                <InputType>text</InputType>
                <DataType>Float</DataType>
                <PID>sys.cap.dependent.amount</PID>
                <Title>Dependent Amount2</Title>
                <Length>20</Length>
                <Required>true</Required>
                <Readonly>false</Readonly>
                <Defaultvalue>200.00</Defaultvalue>
                <Description>Dependent Amount2</Description>
                <Display>true</Display>
                <GroupID>sys_G0</GroupID>
                <FileID>sys_F1</FileID>
                <DependsOn>sys.cap.amount=99</DependsOn>
            </Parameter>
        </Header>
    </Parameters>
</Schema>
