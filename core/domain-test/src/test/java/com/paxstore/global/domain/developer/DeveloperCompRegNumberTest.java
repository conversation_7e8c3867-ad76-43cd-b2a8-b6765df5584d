package com.paxstore.global.domain.developer;

import com.pax.core.json.JsonMapper;
import com.pax.market.domain.BaseDomainTest;
import com.pax.market.domain.entity.global.developer.Developer;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.request.developer.DeveloperCreateRequest;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.functional.admin.task.developer.AdminDeveloperFuncService;
import com.pax.market.functional.common.MarketAndUserFunc;
import com.pax.market.functional.developer.impl.DeveloperUserFuncServiceImpl;
import com.pax.market.vo.developer.user.UserVo;
import com.paxstore.global.domain.sandbox.SpyLoginProvider;
import com.paxstore.global.domain.service.developer.DeveloperService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.SQLException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/22 17:57
 */
//@RunWith(SpringJUnit4ClassRunner.class)
//@EnableAspectJAutoProxy(proxyTargetClass = true)
//@ContextConfiguration(classes = {SpyLoginProvider.class})
public class DeveloperCompRegNumberTest extends BaseDomainTest {

    //story 1 增：注册开发者/更新，返回新字段companyRegistrationNumber POST https://api.sit.whatspos.cn/p-market-web/v1/developer
    //story 2 查：开发者查看自己的信息，返回companyRegistrationNumber，开发者详情 GET https://api.sit.whatspos.cn/p-market-web/v1/developer/current-user
    //story 3 开发者审核，返回开发者信息companyRegistrationNumber

    @Autowired
    private DeveloperUserFuncServiceImpl developerUserFuncServiceImpl;

    @Autowired
    private DeveloperService developerService;

    @Autowired
    private CurrentLoginProvider currentLoginProvider;
    @Autowired
    private AdminDeveloperFuncService adminDeveloperFuncService;

    @Autowired
    @Qualifier("developerMarketAndUserFuncImpl")
    private MarketAndUserFunc developerMarketAndUserFunc;

    //story 1
    @Before
    public void prepare() throws SQLException {
        executeSql("delete from PAX_DEVELOPER where email='<EMAIL>'");
        executeSql("delete from PAX_USER where email='<EMAIL>'");
        PowerMockito.when(currentLoginProvider.getCurrentMarketInfo()).thenReturn(getCurrentMarketInfo());
        SpyLoginProvider.setHttpRequest();
    }


    //story 2
    @Test
    public void testRegisterDeveloper() {
        PowerMockito.when(currentLoginProvider.getCurrentUserInfo()).thenReturn(null);
        PowerMockito.when(currentLoginProvider.getCurrentMarketInfo()).thenReturn(getCurrentMarketInfo());
        HttpServletRequest req = SpyLoginProvider.getDummyHttpServletRequest();
        developerUserFuncServiceImpl.createDeveloper(req, getDeveloperReq());
        Set<Long> developerIds = developerService.getDeveloperIdsByKeyWords("<EMAIL>");
        Assert.assertTrue(developerIds.size() > 0);
        Long developerId = developerIds.stream().collect(Collectors.toList()).get(0);
        Developer developer = developerService.get(developerId);
        Assert.assertNotNull(developer);
        Assert.assertEquals("228876512SBD", developer.getCompanyRegistrationNumber());

    }

    @Test
    public void testGetCurrentUser() {
        PowerMockito.when(currentLoginProvider.getCurrentMarketInfo()).thenReturn(getCurrentMarketInfo());
        UserVo vo = developerMarketAndUserFunc.getCurrentUserVo();
        Assert.assertNotNull(vo);
        Assert.assertNotNull(vo.getCurrentDeveloper());
        Assert.assertTrue(vo.getCurrentDeveloper().getCompanyRegistrationNumber().length() > 0);
        PowerMockito.when(currentLoginProvider.getCurrentMarketInfo()).thenReturn(getPAXCurrentMarketInfo());
        Assert.assertTrue(adminDeveloperFuncService.getDeveloperDetailVo(1000017382L).getCompanyRegistrationNumber().length() > 0);
    }


    private DeveloperCreateRequest getDeveloperReq() {
        String req = "{\"email\":\"<EMAIL>\",\"realName\":\"testregister\",\"phone\":\"***********\",\"nickname\":\"register\",\"companyName\":\"universe\",\"companyAddr\":\"universe road 111\",\"companyWebsite\":\"univers.com\",\"reason\":\"for test\",\"country\":\"test123456\",\"accountEmail\":\"<EMAIL>\",\"companyRegistrationNumber\":\"228876512SBD\"}";
        DeveloperCreateRequest depReq = JsonMapper.fromJsonString(req, DeveloperCreateRequest.class);
        depReq.setIdCardFrontImg("abcd.img");
        return depReq;
    }

    public MarketInfo getCurrentMarketInfo() {
        MarketInfo marketInfo = new MarketInfo();
        marketInfo.setId(-1L);
        marketInfo.setDomain("www");
        marketInfo.setName("Global");
        marketInfo.setTerminalLimit(Integer.MAX_VALUE);
        marketInfo.setAllowTerminalManagement(true);
        marketInfo.setAllowAdvertisement(true);
        marketInfo.setAllowAppOfflinePurchase(false);
        marketInfo.setAllowDeveloper(true);
        marketInfo.setAllowTerminalDownload(true);
        marketInfo.setMarketPlaceLimit(Integer.MAX_VALUE);
        marketInfo.setAppLimit(Integer.MAX_VALUE);
        marketInfo.setAllowTerminalStock(false);
        marketInfo.setAllowStoreClient(true);
        marketInfo.setAllowReportCenter(true);
        marketInfo.setAllowFirmware(true);
        marketInfo.setAllowRKI(true);
        marketInfo.setDeveloperLimit(Integer.MAX_VALUE);
        marketInfo.setAllowRKI(true);
        marketInfo.setAllowPaymentSecurityControl(true);
        marketInfo.setAllowIpWhitelist(true);
        marketInfo.setAllowVas(true);
        marketInfo.setSsoCompanyDomain("test.com");
        marketInfo.setAllowSsoSetting(true);
        marketInfo.setAllowUnattendedModelWhitelist(Boolean.FALSE);
        marketInfo.setAllowGroupPushLimit(Boolean.TRUE);
        return marketInfo;
    }

    public MarketInfo getPAXCurrentMarketInfo() {
        MarketInfo marketInfo = getCurrentMarketInfo();
        marketInfo.setId(1L);
        marketInfo.setDomain("PAX");
        marketInfo.setName("Admin");
        return marketInfo;
    }
}
