/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain;

import com.pax.market.constants.VariableSource;
import com.pax.market.constants.VariableType;
import com.pax.market.domain.entity.market.variable.TerminalVariable;
import com.pax.market.domain.parameter.SchemaProcess;
import com.paxstore.market.domain.service.variable.TerminalVariableService;
import com.pax.market.framework.common.utils.FileUtils;
import com.pax.market.framework.common.utils.IdGen;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.*;

public class ComboParametersTest {
    private final TerminalVariableService terminalVariableService = new TerminalVariableService();

    @Test
    public void test() throws IOException, URISyntaxException, ParseException {
        File schemaFile = new File(ComboParametersTest.class.getResource("/template/Template_combo.xml").toURI());
        String schemaXml = FileUtils.readFileToString(schemaFile);

        Map<String, String> defaultValues = new HashMap<>();
        defaultValues.put("sys_test_combo_2", "#{test}");

        List<TerminalVariable> variableList = Lists.newArrayList();
        variableList.add(createVariable("sys.test.combo", "7,8,9"));
        variableList.add(createVariable("sys.test.combo.1", "5"));
        variableList.add(createVariable("test", "10"));

        SchemaProcess schemaProcess = SchemaProcess.create().defaultValues(defaultValues).parseXml(schemaXml);
        Map<String, String> paramVariables = terminalVariableService.getParamVariables(schemaProcess, variableList, false);
        System.out.println(paramVariables);

        schemaProcess = SchemaProcess.create().defaultValues(defaultValues).paramVariables(paramVariables).parseXml(schemaXml);
        System.out.println(schemaProcess.getFileMap());
    }

    TerminalVariable createVariable(String key, String value) {
        TerminalVariable terminalVariable = new TerminalVariable();
        terminalVariable.setId(IdGen.randomLong());
        terminalVariable.setKey(key);
        terminalVariable.setValue(value);
        terminalVariable.setType(VariableType.TEXT);
        terminalVariable.setSource(VariableSource.TERMINAL);
        return terminalVariable;
    }
}
