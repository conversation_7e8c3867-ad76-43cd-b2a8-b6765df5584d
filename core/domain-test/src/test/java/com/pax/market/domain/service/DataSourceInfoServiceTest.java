package com.pax.market.domain.service;

import com.pax.api.pubsub.PubSubService;
import com.pax.market.constants.DataSourceInfoStatus;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.BaseDomainTest;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.market.attribute.EntityAttributeLabel;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.thirdparty.MarketWhiteListAccessIp;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.request.admin.platform.datasourceinfo.DataSourceInfoRequest;
import com.pax.market.dto.request.admin.platform.datasourceinfo.DataSourceMarketRequest;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.domain.entity.market.attribute.EntityAttribute;
import com.pax.market.func.DataSourceInfoFuncTestService;
import com.pax.support.dynamic.datasource.config.PaxDataSourceConfig;
import com.pax.support.dynamic.datasource.constants.CommonConstant;
import com.pax.support.dynamic.datasource.pubsub.DataSourceInfoChangedMessage;
import com.pax.support.dynamic.datasource.pubsub.DataSourceMarketChangedMessage;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.global.domain.entity.DataSourceInfo;
import com.paxstore.global.domain.entity.DataSourceMarket;
import com.paxstore.global.domain.service.datasourceinfo.DataSourceInfoService;
import com.paxstore.global.domain.service.datasourceinfo.DataSourceMarketService;
import com.paxstore.global.domain.service.market.MarketWhiteListAccessIpService;
import com.paxstore.market.domain.service.attribute.EntityAttributeService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
public class DataSourceInfoServiceTest extends BaseDomainTest {

    @Autowired
    private DataSourceInfoService dataSourceInfoService;

    @Autowired
    private DataSourceMarketService dataSourceMarketService;

    @Autowired
    private MarketWhiteListAccessIpService marketWhiteListAccessIpService;

    @Autowired
    private PaxDataSourceConfig paxDataSourceConfig;

    @Autowired
    private PubSubService pubSubService;

    @Autowired
    private DataSourceInfoFuncTestService dataSourceInfoFuncService;
    
    @Autowired
    private CurrentLoginProvider currentLoginProvider;

    @Autowired
    private ResellerService resellerService;

    @Autowired
    private EntityAttributeService entityAttributeService;

    //创建数据源
    @Test
    @Order(1)
    public void createDataSourceInfo(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("tx_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("腾讯云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");

        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);

        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertNotNull("查不到数据源信息", dataSourceInfo);
//        dataSourceInfoService.delete(dataSourceInfo);
    }

    //获得数据源
    //@Test
    @Order(2)
    public void findDataSourceInfo(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("tx_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("腾讯云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");

        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);

        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertNotNull("查不到数据源信息", dataSourceInfo);


        dataSourceInfoService.delete(dataSourceInfo);
        dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertNull("删除失败", dataSourceInfo);

    }

    //修改数据源
    //@Test
    @Order(3)
    public void updateDataSourceInfo(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("tx_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("腾讯云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");

        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);

        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertNotNull("查不到数据源信息", dataSourceInfo);


        dataSourceInfo.setRemark("腾讯云修改");
        dataSourceInfoService.save(dataSourceInfo);

        dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertTrue("修改失败", "腾讯云修改".equals(dataSourceInfo.getRemark()));

    }

    //删除数据源
    //@Test
    @Order(4)
    public void deleteDataSourceInfo(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("tx_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("腾讯云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");

        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);

        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertNotNull("查不到数据源信息", dataSourceInfo);


        dataSourceInfoService.delete(dataSourceInfo);
        dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        Assert.assertNull("删除失败", dataSourceInfo);

    }

    //市场配置数据源
    //@Test
    @Order(5)
    public void createMarketConfig(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("tx_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("腾讯云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSourceInfoRequest.setStatus(1);

        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);

        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        DataSourceMarketRequest marketRequest = new DataSourceMarketRequest();
        marketRequest.setMarketId(10L);
        marketRequest.setDataSourceId(dataSourceInfo.getId());
        marketRequest.setDbInstanceName("p_market_api_91");
        dataSourceMarketService.saveDataSourceMarket(marketRequest);

        DataSourceMarket dataSourceMarket = dataSourceMarketService.selectByMarketId(10L);
        dataSourceInfoService.delete(dataSourceInfo);
        dataSourceMarketService.delete(dataSourceMarket);

    }

    //删除市场数据源配置
    //@Test
    @Order(6)
    public void deleteMarketConfig(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("tx_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("腾讯云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");

        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);

        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        DataSourceMarketRequest marketRequest = new DataSourceMarketRequest();
        marketRequest.setMarketId(10L);
        marketRequest.setDataSourceId(dataSourceInfo.getId());
        marketRequest.setDbInstanceName("p_market_api_91");
        dataSourceMarketService.saveDataSourceMarket(marketRequest);

        DataSourceMarket dataSourceMarket = dataSourceMarketService.selectByMarketId(10L);
        dataSourceInfoService.delete(dataSourceInfo);
        dataSourceMarketService.delete(dataSourceMarket);

        dataSourceMarket = dataSourceMarketService.selectByMarketId(10L);

        Assert.assertNull("删除失败",dataSourceMarket);

    }

    //多数据源查询
    @Test
    @Order(7)
    public void dynamicDsSearch(){
        //查询global库
        DataSourceInfo searchCriteria = new DataSourceInfo();
        searchCriteria.setStatus(1);
        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("tx_cloud");
        List<DataSourceInfo> dataSourceInfoList = dataSourceInfoService.findAllList(searchCriteria);
        //查询market库
        PaxDynamicDsThreadLocal.setPreferenceMarketId(1L);
        MarketWhiteListAccessIp whiteListAccessIp = new MarketWhiteListAccessIp();
        whiteListAccessIp.setMarketId(1L);
        whiteListAccessIp.setIp(1000L);
        whiteListAccessIp.setDescription("test");
        whiteListAccessIp.setId(1000L);
        whiteListAccessIp.setIsNewRecord(true);
        marketWhiteListAccessIpService.save(whiteListAccessIp);

        int count = marketWhiteListAccessIpService.getCount(whiteListAccessIp);
        log.info("********count={}******", count);
        Assert.assertTrue("查询市场白名单访问ip地址失败", count>0);
    }

    //@Test
    @Transactional
    @Rollback(false)
    /**
     * 模拟动态跨库事务
     * 测试通过2023/11/29
     * */
    @Order(8)
    public void dynamic3DsTransactionSuccess(){
        dataSourceInfoFuncService.dynamic3DsTransactionSuccess();
    }

    //@Test
    /**
     * 模拟动态跨库操作
     * 测试通过2023/11/29
     * */
    @Order(9)
    public void dynamic3DsTransactionFail(){
        try {
            dataSourceInfoFuncService.testDynamic3DsTransactionFail();
        } catch (Exception e) {
            Assert.assertNotNull("运行失败", e);
        }
    }

    @Test
    @Transactional
    @Rollback(false)
    /**
     * 模拟动态创建一个数据源，然后切到对应的数据源进行查询
     * 测试通过2023/11/29
     * **/
    @Order(10)
    public void dynamicCreateDsAndUseIt(){
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("jd_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("rooT#123");
        dataSourceInfoRequest.setUrl("***************************************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("京东云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSourceInfoService.saveDataSourceInfo(dataSourceInfoRequest);
        DataSourceInfo dataSourceInfo = dataSourceInfoService.selectByName("jd_cloud");
        dataSourceInfo.setStatus(DataSourceInfoStatus.ENABLED.getStatus());
        dataSourceInfoService.save(dataSourceInfo);

        //发送数据源创建消息
        DataSourceInfoChangedMessage infoChangedMessage = new DataSourceInfoChangedMessage();
        BeanMapper.copy(dataSourceInfo, infoChangedMessage);
        infoChangedMessage.setChangeType(CommonConstant.MESSAGE_CREATE_DATASOURCE);
        infoChangedMessage.setEnableEncrypt(paxDataSourceConfig.getEnableEncrypt());

        new Thread(()->{
            pubSubService.pubMessage(SystemConstants.PAX_DYNAMIC_DATASOURCE_PUBSUB_TOPIC, infoChangedMessage);
        }).start();

        try{
            Thread.sleep(5000);
        } catch (Exception e) {

        }

        DataSourceMarketRequest marketRequest = new DataSourceMarketRequest();
        marketRequest.setMarketId(10L);
        marketRequest.setDataSourceId(dataSourceInfo.getId());
        marketRequest.setDbInstanceName("");
        dataSourceMarketService.saveDataSourceMarket(marketRequest);

        //发送市场与数据源绑定消息
        DataSourceMarket dataSourceMarket = dataSourceMarketService.selectByMarketId(10L);
        DataSourceMarketChangedMessage marketChangedMessage = new DataSourceMarketChangedMessage();
        BeanMapper.copy(dataSourceMarket, marketChangedMessage);
        marketChangedMessage.setDataSourceName(dataSourceInfo.getName());
        marketChangedMessage.setDbInstanceName("");
        marketChangedMessage.setChangeType(CommonConstant.MESSAGE_CREATE_MARKET_DS_MAPPING);

        new Thread(()->{
            pubSubService.pubMessage(SystemConstants.PAX_DYNAMIC_DATASOURCE_PUBSUB_TOPIC, marketChangedMessage);
        }).start();

        try{
            Thread.sleep(5000);
        } catch (Exception e) {

        }

        //构建两个属性
        EntityAttribute entityAttribute = new EntityAttribute();
        entityAttribute.setMarketId(1L);
        entityAttribute.setKey("myName");
        entityAttribute.setEntityType("Reseller");
        entityAttribute.setInputType("TEXT");
        UserInfo userInfo = new UserInfo();
        userInfo.setId(getCurrentUser().getId());
        entityAttribute.setCreatedBy(userInfo);
        entityAttribute.setCreatedDate(new Date());
        entityAttribute.setUpdatedBy(userInfo);
        entityAttribute.setUpdatedDate(new Date());

        List<EntityAttributeLabel> labelList = new ArrayList<>();
        EntityAttributeLabel label = new EntityAttributeLabel();
        label.setLabel("myName");
        label.setCreatedBy(userInfo);
        label.setCreatedDate(new Date());
        label.setUpdatedBy(userInfo);
        label.setUpdatedDate(new Date());
        label.setCurrentMarketId(1L);
        label.setLocale(LocaleUtils.getEnvLocale().getCountry());
        labelList.add(label);

        entityAttribute.setEntityAttributeLabelList(labelList);
        entityAttributeService.save(entityAttribute);

        //模拟查询
        PaxDynamicDsThreadLocal.setPreferenceMarketId(1L);

        Reseller reseller = new Reseller();
        reseller.setAddress("SIP");
        reseller.setCity("Suzhou");
        reseller.setCountry("CN");
        User user = new User();
        user.setId(getCurrentUser().getId());
        reseller.setUser(user);
        reseller.setPhone("18136066703");
        reseller.setStatus("A");
        reseller.setCompany("Pax");
        reseller.setContact("Owens/Mao");
        reseller.setIsNewRecord(true);
        reseller.setId(System.currentTimeMillis()/1000);
        reseller.setName("testReseller-jd-cloud-"+reseller.getId());

        LinkedHashMap<String, String> entityAttributeValues = new LinkedHashMap<>();
        entityAttributeValues.put("myName", "owens");
        reseller.setEntityAttributeValues(entityAttributeValues);

        resellerService.save(reseller);

        Reseller resellerTemp = resellerService.getByName(reseller.getName());
        PaxDynamicDsThreadLocal.removePreferenceMarketId();

        Assert.assertNotNull("按照名称查询失败", resellerTemp);
        Assert.assertTrue("失败", resellerTemp.getName().equals(reseller.getName()));

    }

    //@Test
    @Transactional
    @Rollback(false)
    @Order(11)
    public void funcTestDeleteDataSource() {
        dataSourceInfoFuncService.testDeleteDataSource();
    }

    //@Test
    @Transactional
    @Rollback(false)
    @Order(12)
    public void funcTestDeleteDataSourceExistReference() {
        dataSourceInfoFuncService.testDeleteDataSourceExistReference();
    }

    //@Test
    @Transactional
    @Rollback(false)
    @Order(13)
    public void funcTestDeleteMarketConfig() {
        dataSourceInfoFuncService.testDeleteMarketConfig();
    }

}
