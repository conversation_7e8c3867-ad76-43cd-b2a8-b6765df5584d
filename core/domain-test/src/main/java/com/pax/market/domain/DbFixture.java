/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain;

import com.pax.support.dynamic.datasource.atomikos.PaxDynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;

import javax.sql.DataSource;
import java.io.File;

/**
 * <AUTHOR>
 * @date Jun 1, 2016
 */
@Slf4j
public class DbFixture {

  public DbFixture(DataSource dataSource) {

    File folder;
    try {
      if (dataSource.getConnection().getMetaData().getURL().contains("mysql")) {
        return;
      }
      String path = DbFixture.class.getResource("/").toURI().getPath();
      String newPath =
          path.substring(0, path.indexOf("core/domain-test"))
              + "api-servers/api-test/src/test/resources/";
      folder = new File(newPath);
    } catch (Exception e) {
      throw new IllegalStateException("failed to resolve migration sql scripts", e);
    }

    String prefix = "filesystem:" + folder.getAbsolutePath();
    if (dataSource instanceof PaxDynamicDataSource) {
      log.warn("<<<<<数据库脚本将会在全部数据源中执行，请耐心等待！！！>>>>");
      PaxDynamicDataSource dynamicRoutingDataSource = (PaxDynamicDataSource)(dataSource);
      dynamicRoutingDataSource.getResolvedDataSources().forEach((key, value)->{
        String dsName = key.toString();
        if (!dsName.endsWith("_xa")) {//排除XA数据源
          log.info("****migration for DynamicRoutingDataSource datasource:{}*****", key);
          Flyway.configure()
              .dataSource(value)
              .locations(prefix + "/db/migration/test")
              .load()
              .migrate();
        }
      });
    } else {
      Flyway.configure()
              .dataSource(dataSource)
              .locations(prefix + "/db/migration/test")
              .load()
              .migrate();
    }
  }

}
