package com.pax.market.config;

import com.zolon.saas.vas.func.airviewer.AirViewerFunc;
import com.zolon.saas.vas.func.airviewer.mock.MockAirviewerFunc;
import com.zolon.saas.vas.func.appscan.AppScanFunc;
import com.zolon.saas.vas.func.appscan.mock.MockAppScanFunc;
import com.zolon.saas.vas.func.cloudmsg.CloudMsgFunc;
import com.zolon.saas.vas.func.cloudmsg.mock.MockCloudMsgFunc;
import com.zolon.saas.vas.func.cyberlab.CyberlabFunc;
import com.zolon.saas.vas.func.cyberlab.mock.MockCyberlabFunc;
import com.zolon.saas.vas.func.goinsight.DataIngestionFunc;
import com.zolon.saas.vas.func.goinsight.DataQueryFunc;
import com.zolon.saas.vas.func.goinsight.DatasetMgtFunc;
import com.zolon.saas.vas.func.goinsight.SystemOperationNotifyFunc;
import com.zolon.saas.vas.func.goinsight.mock.MockDataIngestionFunc;
import com.zolon.saas.vas.func.goinsight.mock.MockDataQueryFunc;
import com.zolon.saas.vas.func.goinsight.mock.MockDatasetMgtFunc;
import com.zolon.saas.vas.func.goinsight.mock.MockSystemOperationNotifyFunc;
import com.zolon.saas.vas.func.platform.ServiceUsageQueryFunc;
import com.zolon.saas.vas.func.platform.VasPlatformFunc;
import com.zolon.saas.vas.func.platform.mock.MockServiceUsageQueryFunc;
import com.zolon.saas.vas.func.platform.mock.MockVasPlatformFunc;
import com.zolon.saas.vas.func.stackly.StacklyFunc;
import com.zolon.saas.vas.func.stackly.mock.MockStacklyFunc;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DummySaasApiConfig {
    @Bean
    public VasPlatformFunc vasPlatformFunc(){
        return new MockVasPlatformFunc();
    }

    @Bean
    public CloudMsgFunc cloudMsgFunc(){
        return new MockCloudMsgFunc();
    }

    @Bean
    public AirViewerFunc airViewerFunc(){
        return new MockAirviewerFunc();
    }

    @Bean
    public AppScanFunc appScanFunc(){
        return new MockAppScanFunc();
    }

    @Bean
    public CyberlabFunc cyberlabFunc(){
        return new MockCyberlabFunc();
    }

    @Bean
    public ServiceUsageQueryFunc serviceUsageQueryFunc(){
        return new MockServiceUsageQueryFunc();
    }

    @Bean
    public DataIngestionFunc dataIngestionFunc(){
        return new MockDataIngestionFunc();
    }

    @Bean
    public DataQueryFunc dataQueryFunc(){
        return new MockDataQueryFunc();
    }

    @Bean
    public DatasetMgtFunc datasetMgtFunc(){
        return new MockDatasetMgtFunc();
    }

    @Bean
    public SystemOperationNotifyFunc systemOperationNotifyFunc(){
        return new MockSystemOperationNotifyFunc();
    }

    @Bean
    public StacklyFunc stacklyFunc(){
        return new MockStacklyFunc();
    }
}
