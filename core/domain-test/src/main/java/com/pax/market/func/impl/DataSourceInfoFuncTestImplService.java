package com.pax.market.func.impl;

import com.pax.market.domain.entity.global.developer.Developer;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.market.domain.entity.market.thirdparty.MarketWhiteListAccessIp;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.market.sandbox.SandboxTerminal;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.dto.request.admin.platform.datasourceinfo.DataSourceInfoRequest;
import com.pax.market.dto.request.admin.platform.datasourceinfo.DataSourceMarketRequest;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.func.DataSourceInfoFuncTestService;
import com.pax.market.functional.admin.platform.datasourceinfo.DataSourceInfoFuncService;
import com.pax.market.functional.admin.platform.datasourceinfo.DataSourceMarketFuncService;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.vo.admin.platform.datasourceinfo.DataSourceInfoVo;
import com.pax.support.dynamic.datasource.aspectj.XATransactional;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.global.domain.entity.DataSourceInfo;
import com.paxstore.global.domain.service.datasourceinfo.DataSourceInfoService;
import com.paxstore.global.domain.service.developer.DeveloperService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.market.MarketWhiteListAccessIpService;
import com.paxstore.market.domain.service.sandbox.SandboxTerminalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@FunctionalService
@RequiredArgsConstructor
@Slf4j
public class DataSourceInfoFuncTestImplService implements DataSourceInfoFuncTestService {

    private final DataSourceInfoService dataSourceInfoService;
    private final MarketService marketService;
    private final SandboxTerminalService sandboxTerminalService;
    private final MarketWhiteListAccessIpService marketWhiteListAccessIpService;
    private final DataSourceInfoFuncService dataSourceInfoFuncService;
    private final DataSourceMarketFuncService dataSourceMarketFuncService;
    private final DeveloperService developerService;

    @Override
//    @GlobalTransactional
//    @Transactional
    public void testDynamicTransactionSuccess() {
        DataSourceInfo dummyDataSourceInfo = createDummyDataSourceInfo();
        dataSourceInfoService.save(dummyDataSourceInfo);
        //测试动态数据源
        SandboxTerminal sandbox = createSandbox();
        sandboxTerminalService.save(sandbox);
    }

    @Override
//    @GlobalTransactional
//    @Transactional
    public void testDynamicTransactionFail() {
        DataSourceInfo dummyDataSourceInfo = createDummyDataSourceInfo();
        dataSourceInfoService.save(dummyDataSourceInfo);
        //测试动态数据源
        SandboxTerminal sandbox = createSandbox();
        sandboxTerminalService.save(sandbox);
        int i=1/0;
    }

    @Override
//    @GlobalTransactional
    @Transactional
    @XATransactional
    public void testDynamic3DsTransactionFail() {
        DataSourceInfo dummyDataSourceInfo = createDummyDataSourceInfo();
        dataSourceInfoService.save(dummyDataSourceInfo);

        MarketWhiteListAccessIp whiteListAccessIp = new MarketWhiteListAccessIp();
        whiteListAccessIp = new MarketWhiteListAccessIp();
        whiteListAccessIp.setDescription("阿里测试");
        whiteListAccessIp.setMarketId(1L);
        whiteListAccessIp.setIp(3020L);
        PaxDynamicDsThreadLocal.setPreferenceMarketId(2L);
        marketWhiteListAccessIpService.save(whiteListAccessIp);

        //测试动态数据源
        SandboxTerminal sandbox = createSandbox();
        PaxDynamicDsThreadLocal.removePreferenceMarketId();
        sandboxTerminalService.save(sandbox);
        int i=1/0;

    }

    @Override
    @Transactional
    @XATransactional
    public void dynamic3DsTransactionSuccess() {
        //保存market库
        MarketWhiteListAccessIp whiteListAccessIp = new MarketWhiteListAccessIp();
        whiteListAccessIp.setDescription("腾讯测试");
        whiteListAccessIp.setMarketId(1L);
        whiteListAccessIp.setIp(2020L);
        whiteListAccessIp.setIsNewRecord(true);
        whiteListAccessIp.setId(1001L);
        PaxDynamicDsThreadLocal.setPreferenceMarketId(1L);
        marketWhiteListAccessIpService.save(whiteListAccessIp);
        int count = marketWhiteListAccessIpService.getCount(whiteListAccessIp);

        whiteListAccessIp = new MarketWhiteListAccessIp();
        whiteListAccessIp.setDescription("阿里测试");
        whiteListAccessIp.setMarketId(2L);
        whiteListAccessIp.setIp(3020L);
        whiteListAccessIp.setIsNewRecord(true);
        whiteListAccessIp.setId(1002L);
        PaxDynamicDsThreadLocal.setPreferenceMarketId(2L);
        marketWhiteListAccessIpService.save(whiteListAccessIp);

        Developer developer = new Developer();
        developer.setId(1L);
        developer.setIsNewRecord(true);
        developer.setMarketId(1L);
        developer.setRealName("owens");
        User user = new User();
        user.setId(1L);
        developer.setUser(user);
        developer.setDeveloperType("I");

        developerService.save(developer);

        //测试动态数据源
        SandboxTerminal sandbox = createSandbox();
        sandboxTerminalService.save(sandbox);

        count = marketWhiteListAccessIpService.getCount(whiteListAccessIp);
    }

    private DataSourceInfo createDummyDataSourceInfo(){
        DataSourceInfo dataSourceInfo = new DataSourceInfo();
        dataSourceInfo.setUsername("nCzemXNOVrr3vWY2uelD3svFvfWhOCbWradgGUTGOhqcG5CZRsC1oU44tuZVBOosHSzADekIQcPlgxJaOrW47Q==");
        dataSourceInfo.setPassword("cvXVSPQeIhaAEXIKFxglW7aVFwdrPHqWMN97rnbO4l5HyqQtTsYeImuid2CLNEzSa2vdcTe7w/XT30Ef1s4hHQ==");
        dataSourceInfo.setDriverClassName("com.mysql.jdbc.Driver");
        dataSourceInfo.setUrl("********************************************************************************************************************");
        dataSourceInfo.setStatus(0);
        dataSourceInfo.setMarketId(1L);

        return dataSourceInfo;
    }

    private SandboxTerminal createSandbox(){
        SandboxTerminal sandboxTerminal = new SandboxTerminal();
        sandboxTerminal.setDevId(1L);

        User user = new User();
        user.setId(1L);
        sandboxTerminal.setUser(user);
        sandboxTerminal.setDevId(1L);

//        Developer developer = new Developer();
//        developer.setId(1L);
//        sandboxTerminal.setDeveloper(developer);

        sandboxTerminal.setName("test");
        sandboxTerminal.setSerialNo("202300001");

        Model model = new Model();
        model.setId(1L);
        Factory factory = new Factory();
        factory.setId(1L);
        model.setFactory(factory);
        sandboxTerminal.setModel(model);

        sandboxTerminal.setStatus("Y");
        return sandboxTerminal;
    }

    @Override
    public void testDeleteDataSource() {
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("jd_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("京东云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");
        IdVo dataSourceInfo = dataSourceInfoFuncService.createDataSourceInfo(dataSourceInfoRequest);

        try{
            Thread.sleep(5000);
        } catch (Exception e) {

        }

        boolean resultFlag = true;
        try{
            Thread.sleep(5000);
            dataSourceInfoFuncService.deleteDataSourceInfo(dataSourceInfo.getId());
        } catch (Exception e) {
            resultFlag = false;
        }

        Assert.isTrue(resultFlag, "测试删除数据源失败!");

    }

    @Override
    public void testDeleteDataSourceExistReference() {
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("jd_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("京东云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");
        IdVo dataSourceInfo = dataSourceInfoFuncService.createDataSourceInfo(dataSourceInfoRequest);

        try{
            Thread.sleep(5000);
        } catch (Exception e) {

        }

        DataSourceMarketRequest marketRequest = new DataSourceMarketRequest();
        marketRequest.setMarketId(1L);
        marketRequest.setDataSourceId(dataSourceInfo.getId());
        marketRequest.setDbInstanceName("p_market_api_91");
        dataSourceMarketFuncService.createDataSourceMarket(marketRequest);

        boolean resultFlag = true;
        try{
            Thread.sleep(5000);
            dataSourceInfoFuncService.deleteDataSourceInfo(dataSourceInfo.getId());
        } catch (Exception e) {
            resultFlag = false;
        }

        Assert.isTrue(!resultFlag, "测试删除一个已被引用的数据源失败!");

    }

    @Override
    public void testDeleteMarketConfig() {
        DataSourceInfoRequest dataSourceInfoRequest = new DataSourceInfoRequest();
        dataSourceInfoRequest.setName("jd_cloud");
        dataSourceInfoRequest.setUsername("root");
        dataSourceInfoRequest.setPassword("root123");
        dataSourceInfoRequest.setUrl("************************************************************************************************************************************");
        dataSourceInfoRequest.setRemark("京东云");
        dataSourceInfoRequest.setDriverClassName("com.mysql.cj.jdbc.Driver");
        IdVo dataSourceInfo = dataSourceInfoFuncService.createDataSourceInfo(dataSourceInfoRequest);

        try{
            Thread.sleep(5000);
        } catch (Exception e) {
        }

        DataSourceInfoVo dataSourceInfoVo = dataSourceInfoFuncService.getDataSourceInfoVo(dataSourceInfo.getId());

        DataSourceMarketRequest marketRequest = new DataSourceMarketRequest();
        marketRequest.setMarketId(1L);
        marketRequest.setDataSourceId(dataSourceInfo.getId());
        marketRequest.setDbInstanceName("p_market_api_91");
        IdVo dataSourceMarket = dataSourceMarketFuncService.createDataSourceMarket(marketRequest);
        boolean resultFlag = true;
        try{
            Thread.sleep(5000);
            dataSourceMarketFuncService.deleteDataSourceMarket(dataSourceMarket.getId());
        } catch (Exception e) {
            resultFlag = false;
        }

        Assert.isTrue(resultFlag, "测试删除市场配置信息失败!");
    }


}
