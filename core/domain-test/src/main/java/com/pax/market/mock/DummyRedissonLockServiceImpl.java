package com.pax.market.mock;

import com.pax.market.framework.common.redisson.RedissonLockService;

import java.util.concurrent.TimeUnit;

public class DummyRedissonLockServiceImpl implements RedissonLockService {

    @Override
    public String processKey(String key) {
        return "";
    }

    @Override
    public void lock(String lockKey) {

    }

    @Override
    public void lock(String lockKey, long timeout) {

    }

    @Override
    public void lock(String lockKey, long timeout, TimeUnit unit) {

    }

    @Override
    public boolean tryLock(String lockKey, long waitTime, long timeout) {
        return true;
    }

    @Override
    public boolean tryLock(String lockKey, long waitTime, long timeout, TimeUnit unit) {
        return true;
    }

    @Override
    public Object getBucket(String lockKey) {
        return null;
    }

    @Override
    public void setBucket(String lockKey, Object o, long timeout) {

    }

    @Override
    public void delBucket(String lockKey) {

    }

    @Override
    public void unlock(String lockKey) {

    }

    @Override
    public boolean isLocked(String lockKey) {
        return false;
    }
}
