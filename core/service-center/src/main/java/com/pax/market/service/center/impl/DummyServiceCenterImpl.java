package com.pax.market.service.center.impl;

import com.pax.market.domain.entity.global.developer.DeveloperSolutionApply;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.domain.entity.market.solution.ResellerSolutionApply;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.dto.request.admin.common.specific.SpecificResellerRequest;
import com.pax.market.dto.request.admin.service.adup.*;
import com.pax.market.dto.request.market.MarketDeveloperRequest;
import com.pax.market.dto.request.market.ServiceSpecificRequest;
import com.pax.market.dto.request.market.VasStatusUpdateRequest;
import com.pax.market.dto.request.vas.*;
import com.pax.market.dto.vas.*;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.market.vo.admin.common.IdLabelVo;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.vo.admin.service.DeveloperSolutionApplyCountVo;
import com.pax.market.vo.admin.service.DeveloperSolutionApplyVo;
import com.pax.market.vo.admin.service.SolutionAppTrialVo;
import com.pax.market.vo.admin.service.SolutionAppUsageVo;
import com.pax.market.vo.admin.service.adup.*;
import com.pax.market.vo.admin.service.airshield.AirShieldDetectRules;
import com.pax.market.vo.admin.service.airshield.BlackAppInfo;
import com.pax.market.vo.admin.service.airshield.DetectionIntervalInfo;
import com.pax.market.vo.admin.service.airshield.RiskFileInfo;
import com.pax.market.vo.solution.ResellerSolutionApplyCountVo;
import com.pax.market.vo.solution.ResellerSolutionApplyVo;
import com.pax.market.vo.vas.InsightMarketSettingVo;
import com.pax.market.vo.vas.common.MarketAvailableServicesVo;
import com.pax.market.vo.vas.common.ServiceInfoVo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class DummyServiceCenterImpl implements ServiceCenterFunc {
    @Override
    public void triggerServiceCenterJob(String jobMethod) {

    }

    @Override
    public void closeService(String serviceType) {

    }

    @Override
    public MarketAvailableServicesVo findVasServices() {
        return null;
    }

    @Override
    public ServiceInfoVo getResellerVasByReseller(String serviceType) {
        return null;
    }

    @Override
    public List<VasInfo> findVasList(boolean filterEnabled) {
        return List.of();
    }


    @Override
    public void updateActiveStatus(Long marketId, VasStatusRequest vasStatusRequest) {

    }

    @Override
    public <T extends Serializable> PageInfo<VasOperationHistoryInfo> findSubscribeHistoryPage(Long marketId, String serviceType, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public void createVasAgreement(VasAgreementRequest request) {

    }

    @Override
    public void updateVasAgreement(Long agreementId, UpdateVasAgreementRequest request) {

    }

    @Override
    public void publishVasAgreement(VasAgreementRequest request) {

    }

    @Override
    public <T extends Serializable> PageInfo<VasAgreementInfo> findVasAgreements(String serviceType, String type, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public void deleteVasAgreement(Long agreementId) {

    }

    @Override
    public <T extends Serializable> PageInfo<Developer2ServiceInfo> findDeveloper2ServicePage(String serviceType, String status, String searchParam, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public MarketAirLinkSettingInfo getMarketAirLinkSetting(Long marketId) {
        return null;
    }

    @Override
    public List<AirLinkDataPoolSettingInfo> findAirLinkDataPoolSettings() {
        return List.of();
    }


    @Override
    public void updateMarketAirLinkSetting(Long marketId, VasStatusRequest request) {

    }

    @Override
    public void subscribeService(String serviceType, VasStatusUpdateRequest request) {

    }

    @Override
    public void unsubscribeService(String serviceType, VasStatusUpdateRequest request) {

    }

    @Override
    public void updateAirLinkDataPoolSetting(Long id, AirLinkDataPoolSettingRequest request) {

    }

    @Override
    public <T extends Serializable> PageInfo<ResellerInfo> findServiceDistributionPage(String serviceType, boolean includeAllData, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public void distributeService(String serviceType, ServiceSpecificRequest serviceSpecificRequest) {

    }

    @Override
    public void deleteServiceDistribution(String serviceType, Long resellerId) {

    }

    @Override
    public MarketAirLinkSubscriptionPlanInfo getSubscriptionPlanByPeriod(Long marketId, String period) {
        return null;
    }

    @Override
    public List<MarketAirLinkSubscriptionPlanInfo> findSubscriptionPlans(Long marketId) {
        return List.of();
    }

    @Override
    public AirLinkDataPoolSettingInfo getAirLinkDataPoolSetting(Long id) {
        return null;
    }

    @Override
    public void agreeVasAgreement(Long agreementId) {

    }

    @Override
    public List<Long> findMarketIdsWithSamePool(Long marketId, String period) {
        return List.of();
    }

    @Override
    public VasAgreementInfo getVasAgreement2Market(String serviceType, String type, boolean agreed) {
        return null;
    }

    @Override
    public void updateRechargeConfig(Integer minRecharge) {

    }

    @Override
    public RechargeConfigInfo getRechargeConfig() {
        return null;
    }

    @Override
    public void readPlanById(Long id) {

    }

    @Override
    public void updateMarketDevServiceStatus(Long developerId, MarketDeveloperRequest marketDeveloperRequest) {

    }

    @Override
    public void applyMarketDevService(String serviceType, Long agreementId) {

    }

    @Override
    public VasInfo getVasInfo(String serviceType) {
        return null;
    }

    @Override
    public void saveVasInfo(VasServiceRequest request) {

    }

    @Override
    public void syncVasInfo(VasServiceRequest request) {

    }

    @Override
    public void changeFreeTill(String serviceType, boolean freeTrial, Long freeTill) {

    }

    @Override
    public Boolean isEndpointAvailable(String serviceType, Long marketId, Long endpointId) {
        return false;
    }

    @Override
    public Boolean isMarket2VasCharged(String serviceType, Long marketId, Integer year, Integer month) {
        return false;
    }


    @Override
    public void setFreeTrialInfo(FreeTrialRequest freeTrialRequest) {

    }

    @Override
    public void vasServiceSuspendDisabledEvent(String serviceType, boolean isSuspend) {

    }

    @Override
    public void batchUpdateVasStatus(VasStatusBatchUpdateRequest request) {

    }

    @Override
    public void saveDefaultStatusInServices(Long marketId, String fromType) {

    }

    @Override
    public void disableAllDevServices(Long developerId, String fromType) {

    }

    @Override
    public List<Long> findMonthAvailableMarketIdsByServiceType(String serviceType) {
        return List.of();
    }

    @Override
    public List<Market2VasInfo> findVasEnabledListByServiceType(String serviceType) {
        return List.of();
    }

    @Override
    public <T extends Serializable> PageInfo<AdSlotVo> findAdSlotPage(Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public IdVo createAdSlot(AdSlotCreateRequest request) {
        return null;
    }

    @Override
    public AdSlotVo getAdSlot(Long adSlotId) {
        return null;
    }

    @Override
    public void disableAdSlot(Long adSlotId) {

    }

    @Override
    public void activateAdSlot(Long adSlotId) {

    }

    @Override
    public void deleteAdSlot(Long adSlotId) {

    }

    @Override
    public List<SimpleAdSlotSpecVo> findAdSlotSpecList() {
        return List.of();
    }

    @Override
    public <T extends Serializable> PageInfo<AdVisualVo> findAdVisualPage(String name, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public AdVisualVo getAdVisual(Long adVisualId) {
        return null;
    }

    @Override
    public IdVo createAdVisual(AdVisualCreateRequest request) {
        return null;
    }

    @Override
    public List<IdLabelVo> findModels(String adSlotType) {
        return List.of();
    }

    @Override
    public IdVo createAdGroup(AdGroupCreateUpdateRequest request) {
        return null;
    }

    @Override
    public void updateAdGroup(AdGroupCreateUpdateRequest request, Long marketId) {

    }

    @Override
    public <T extends Serializable> PageInfo<AdGroupVo> searchAdGroup(String status, Long resellerId, String adSlotType, String adName, Date startTime, Date endTime, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public AdGroupDetailVo getAdGroupDetail(Long adGroupId) {
        return null;
    }

    @Override
    public void deleteAdGroup(Long adGroupId) {

    }

    @Override
    public void updateAdGroupStatus(AdGroupUpdateStatusRequest request, Long adGroupId) {

    }

    @Override
    public void updateAdGroupVisual(AdGroupVisualUpdateRequest request, Long adGroupId) {

    }

    @Override
    public <T extends Serializable> PageInfo<AdGroupSlotVo> findAdGroupSlotPage(Long adGroupId, String adSlotType, String modelId, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public <T extends Serializable> PageInfo<AdVisualVo> findAdGroupVisualPage(Long adSlotId, String adVisualName, Boolean choiceFlag, Page<T> page) {
        return new PageInfo<>();
    }

    @Override
    public <T extends Serializable> PageInfo<BlackAppInfo> findAppBlackListPage(Page<T> page) {
        return null;
    }

    @Override
    public <T extends Serializable> PageInfo<RiskFileInfo> findSysFileAccessPage(Page<T> page) {
        return null;
    }

    @Override
    public DetectionIntervalInfo getAttestationInterval() {
        return null;
    }

    @Override
    public Integer changeInterval(Integer interval) {
        return null;
    }

    @Override
    public void addAppBlackList(BlackAppInfo blackAppInfo) {

    }

    @Override
    public void updateAppBlackList(BlackAppInfo blackAppInfo) {

    }

    @Override
    public void deleteBlackApp(Long id) {

    }

    @Override
    public BlackAppInfo getBlackApp(Long id) {
        return null;
    }

    @Override
    public void addRiskFileInfo(RiskFileInfo riskFileInfo) {

    }

    @Override
    public void updateRiskFileInfo(RiskFileInfo riskFileInfo) {

    }

    @Override
    public void deleteRiskFileInfo(Long id) {

    }

    @Override
    public RiskFileInfo getRiskFileInfo(Long id) {
        return null;
    }

    @Override
    public AirShieldDetectRules getAttestationRules(Long marketId) {
        return null;
    }

    @Override
    public InsightMarketSettingVo getInsightChargeVersion(Long marketId) {
        return null;
    }

    @Override
    public <T extends Serializable> PageInfo<VasAppInfo> findVasAppPage(String name, String appIds, Page<T> page) {
        return null;
    }

    @Override
    public VasAppInfo getAvailableVasApp(Long appId) {
        return null;
    }

    @Override
    public List<VasAppInfo> findMarketAvailableApps(String name, Boolean supportCloudData, Boolean supportCloudMsg, Boolean supportStackly) {
        return List.of();
    }

    @Override
    public void updateAppServiceStatus(Long appId, Long marketId, VasAppRequest vasAppRequest) {

    }

    @Override
    public void updateAppServiceSetting(Long appId, Long marketId, VasAppRequest vasAppRequest) {

    }

    @Override
    public void applyIndustrySolutionAppForSpecific(Long appId) {

    }

    @Override
    public void deleteSpecificSolutionReseller(Long appId, Long resellerId) {

    }

    @Override
    public ResellerSolutionApplyVo getResellerApplySolutionById(Long applyId) {
        return null;
    }

    @Override
    public void updateResellerApplySolution(ResellerSolutionApplyVo vo) {

    }

    @Override
    public ResellerSolutionApplyCountVo getResellerSolutionApplyPendingCount() {
        return null;
    }

    @Override
    public <T extends Serializable> PageInfo<ResellerSolutionApply> getResellerApplySolutions(Page<ResellerSolutionApply> page) {
        return null;
    }


    @Override
    public <T extends Serializable> PageInfo<SolutionAppTrialVo> getSolutionTrialPage(Long appId, Page<T> page) {
        return null;
    }

    @Override
    public List<Long> findSpecificSolutionResellerIdList(Long appId, Integer limit) {
        return List.of();
    }

    @Override
    public void specificSolutionReseller(Long appId, SpecificResellerRequest specificResellerRequest) {

    }

    @Override
    public <T extends Serializable> PageInfo<VasAppOperationHistoryInfo> findVasAppServiceHistory(Long appId, Long marketId, Page<T> page) {
        return null;
    }

    @Override
    public List<App2ServiceInfo> findVasAppMarkets(Long appId, String status) {
        return List.of();
    }

    @Override
    public void applyIndustrySolution() {

    }

    @Override
    public SolutionAppUsageVo getSolutionAppUsage(Long marketId, Long appId, String date) {
        return null;
    }

    @Override
    public PageInfo<DeveloperSolutionApplyVo> findDeveloperSolutionApplyPage(Page<DeveloperSolutionApply> page, boolean approved, String email) {
        return null;
    }

    @Override
    public DeveloperSolutionApplyCountVo getDeveloperSolutionApplyCount(boolean approved) {
        return null;
    }

    @Override
    public void updateIndustrySolutionApply(Long developerId) {

    }

    @Override
    public void syncSolutionTrial(Long marketId, Long appId, String sn) {

    }

    @Override
    public SolutionAppTrialVo getLastTrialBySn(Long marketId, Long appId, String sn) {
        return null;
    }

    @Override
    public Long getTrialAllNum(Long marketId, Long appId) {
        return 0L;
    }

    @Override
    public void saveTrial(SolutionAppTrialVo solutionAppTrialVo) {

    }

    @Override
    public List<Long> findAirLinkOverdueMarketIds() {
        return List.of();
    }

    @Override
    public AirLoadCardPoolMaximumInfo getAirLoadCardPoolMaximumInfo() {
        return null;
    }

    @Override
    public void updateAirLoadCardPoolMaximumInfo(Integer maximum) {

    }

    @Override
    public AirLoadCardPoolInfo getAirLoadCardPool(Long id) {
        return null;
    }

    @Override
    public void createAirLoadCardPool(AirLoadCardPoolRequest request) {

    }

    @Override
    public void updateAirLoadCardPool(Long id, AirLoadCardPoolRequest request) {

    }

    @Override
    public void deleteAirLoadCardPool(Long id) {

    }

    @Override
    public <T extends Serializable> PageInfo<AirLoadCardPoolInfo> findAirLoadCardPools(Page<T> page) {
        return null;
    }

    @Override
    public <T extends Serializable> PageInfo<ResellerAirLoadCardPoolInfo> findAirLoadCardPoolResellers(Page<T> page, Long cardPoolId, Boolean includeAllData) {
        return null;
    }

    @Override
    public <T extends Serializable> PageInfo<AirLoadCardInfo> findAirLoadCardsByPoolId(Page<T> page, Long cardPoolId, String status, String iccid, String serialNo) {
        return null;
    }

    @Override
    public void deleteAirLoadCard(Long id) {

    }

    @Override
    public void removeAirLoadCard(Long id) {

    }

    @Override
    public void batchDeleteAirLoadCards(AirLoadCardBatchRequest request) {

    }

    @Override
    public void batchRemoveAirLoadCards(AirLoadCardBatchRequest request) {

    }
}
