package com.pax.market.service.center.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.pax.market.domain.entity.global.developer.DeveloperSolutionApply;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.domain.entity.market.solution.ResellerSolutionApply;
import com.pax.market.dto.DeveloperInfo;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.base.NumResponse;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.request.admin.common.specific.SpecificResellerRequest;
import com.pax.market.dto.request.admin.service.adup.*;
import com.pax.market.dto.request.market.MarketDeveloperRequest;
import com.pax.market.dto.request.market.ServiceSpecificRequest;
import com.pax.market.dto.request.market.VasStatusUpdateRequest;
import com.pax.market.dto.request.vas.*;
import com.pax.market.dto.vas.*;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.audit.AuditTrailContext;
import com.pax.market.framework.common.audit.AuditTrailContextHolder;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.market.vo.admin.common.IdLabelVo;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.vo.admin.service.DeveloperSolutionApplyCountVo;
import com.pax.market.vo.admin.service.DeveloperSolutionApplyVo;
import com.pax.market.vo.admin.service.SolutionAppTrialVo;
import com.pax.market.vo.admin.service.SolutionAppUsageVo;
import com.pax.market.vo.admin.service.adup.*;
import com.pax.market.vo.admin.service.airshield.AirShieldDetectRules;
import com.pax.market.vo.admin.service.airshield.BlackAppInfo;
import com.pax.market.vo.admin.service.airshield.DetectionIntervalInfo;
import com.pax.market.vo.admin.service.airshield.RiskFileInfo;
import com.pax.market.vo.solution.ResellerSolutionApplyCountVo;
import com.pax.market.vo.solution.ResellerSolutionApplyVo;
import com.pax.market.vo.vas.InsightMarketSettingVo;
import com.pax.market.vo.vas.common.MarketAvailableServicesVo;
import com.pax.market.vo.vas.common.ServiceInfoVo;
import com.pax.support.http.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.io.Serializable;
import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR> Zuo
 * @since 9.2
 */
@RequiredArgsConstructor
public class ServiceCenterFuncHttpImpl implements ServiceCenterFunc {

    private final HttpClient httpClient;
    private final CurrentLoginProvider currentLoginProvider;
    private final String apiBaseUrl;
    private final Consumer<HttpResponse> failedRequestHandler;
    private final Consumer<Throwable> exceptionHandler;
    private static final String MARKET_ID_PARAM = "marketId";
    private static final String APP_ID_PARAM = "appId";
    private static final String STATUS_PARAM = "status";
    private static final String AD_SLOT_TYPE_PARAM = "adSlotType";
    private static final String GLOBAL_SERVICE_CENTER_API = "/v1/global/service-center/";
    private static final String GLOBAL_INDUSTRY_SOLUTION_API = "v1/global/industry-solution/";
    private static final String MARKET_SERVICE_CENTER_API = "/v1/market/service-center/";
    private static final String MARKET_INDUSTRY_SOLUTION_API = "v1/market/industry-solution/";
    private static final String CLOSE_ENDPOINT_SERVICE_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "close/{serviceType}";
    private static final String FIND_VAS_SERVICES_ENDPOINT = MARKET_SERVICE_CENTER_API + "services";
    private static final String GET_RESELLER_VAS_BY_SERVICE_TYPE_ENDPOINT = MARKET_SERVICE_CENTER_API + "reseller/{serviceType}";
    private static final String FIND_VAS_LIST_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "services";
    private static final String FIND_VAS_ENABLED_MARKETS_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}/enabled/markets";
    private static final String FIND_CURRENT_AVAILABLE_MARKETS_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}/show/current/markets";
    private static final String GET_VAS_INFO_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}";
    private static final String BATCH_UPDATE_VAS_STATUS_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "batch/update/status";
    private static final String SAVE_VAS_INFO_ENDPOINT = GLOBAL_SERVICE_CENTER_API;
    private static final String SYNC_VAS_INFO_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "sync";
    private static final String VAS_EVENT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}/event";
    private static final String SAVE_DEFAULT_STATUS_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{marketId}/default/status";
    private static final String UPDATE_ACTIVE_STATUS_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{marketId}/status";
    private static final String UPDATE_FREE_TILL_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}/free/time";
    private static final String FIND_SUBSCRIBE_HISTORY_PAGE_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}/history/{marketId}";
    private static final String CREATE_VAS_AGREEMENT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "agreement";
    private static final String VAS_AGREEMENT_ENDPOINT = "agreement/{agreementId}";
    private static final String UPDATE_VAS_AGREEMENT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + VAS_AGREEMENT_ENDPOINT;
    private static final String PUBLISH_VAS_AGREEMENT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "publish/agreement";
    private static final String FIND_VAS_AGREEMENTS_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "{serviceType}/agreements/{type}";
    private static final String DELETE_VAS_AGREEMENT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + VAS_AGREEMENT_ENDPOINT;
    private static final String IS_MARKET_TO_SERVICE_CHARGED = GLOBAL_SERVICE_CENTER_API + "{marketId}/charged";
    private static final String FIND_DEVELOPER_TO_SERVICE_PAGE_ENDPOINT = MARKET_SERVICE_CENTER_API + "{serviceType}/developers";
    private static final String SUBSCRIBE_SERVICE_ENDPOINT = MARKET_SERVICE_CENTER_API + "subscribe/{serviceType}";
    private static final String IS_AVAILABLE_ENDPOINT = MARKET_SERVICE_CENTER_API + "{serviceType}/available";
    private static final String APPLY_DEVELOPER_TO_SERVICE = MARKET_SERVICE_CENTER_API + "apply/{serviceType}";
    private static final String UNSUBSCRIBE_SERVICE_ENDPOINT = MARKET_SERVICE_CENTER_API + "unsubscribe/{serviceType}";
    private static final String FIND_SERVICE_DISTRIBUTION_PAGE_ENDPOINT = MARKET_SERVICE_CENTER_API + "{serviceType}/distributions";
    private static final String DISTRIBUTE_SERVICE_ENDPOINT = MARKET_SERVICE_CENTER_API + "distribute/{serviceType}";
    private static final String DELETE_SERVICE_DISTRIBUTION_ENDPOINT = MARKET_SERVICE_CENTER_API + "{serviceType}/distribution/{resellerId}";
    private static final String AGREE_VAS_AGREEMENT_ENDPOINT = MARKET_SERVICE_CENTER_API + VAS_AGREEMENT_ENDPOINT;
    private static final String GET_VAS_AGREEMENT_ENDPOINT = MARKET_SERVICE_CENTER_API + "{serviceType}/agreement";
    private static final String UPDATE_MARKET_DEV_SERVICE_STATUS_ENDPOINT = MARKET_SERVICE_CENTER_API + "{developerId}/status";
    private static final String SAVE_FREE_INFO_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "free/info";
    private static final String DISABLE_ALL_DEVELOPER_SERVICES = MARKET_SERVICE_CENTER_API + "{developerId}/disable/all/services";

    private static final String FIND_AD_SLOT_PAGE_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slots";
    private static final String CREATE_AD_SLOT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slot";
    public static final String GET_AD_SLOT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slot/{adSlotId}";
    public static final String DISABLE_AD_SLOT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slot/{adSlotId}/disable";
    public static final String ACTIVATE_AD_SLOT_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slot/{adSlotId}/activate";
    public static final String DELETE_AD_SLOT_ENDPOINT = GET_AD_SLOT_ENDPOINT;
    public static final String FIND_AD_SLOT_SPEC_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slot/specs";
    public static final String FIND_AD_SLOT_MODEL_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "adup/ad-slot/models";

    public static final String FIND_VAS_APP_PAGE = GLOBAL_INDUSTRY_SOLUTION_API + "apps";
    public static final String FIND_VAS_APP_SERVICE_HISTORY = GLOBAL_INDUSTRY_SOLUTION_API + "apps/{appId}/services/{marketId}/history";
    public static final String FIND_VAS_APP_MARKETS = GLOBAL_INDUSTRY_SOLUTION_API + "apps/{appId}/markets";
    public static final String GET_SOLUTION_APP_DETAIL = GLOBAL_INDUSTRY_SOLUTION_API + "apps/{appId}";
    public static final String GET_SOLUTION_TRIAL_USAGES = GLOBAL_INDUSTRY_SOLUTION_API + "apps/trial";
    public static final String UPDATE_INDUSTRY_SOLUTION_APP_STATUS = GLOBAL_INDUSTRY_SOLUTION_API + "apps/{appId}/services/{marketId}/status";
    public static final String UPDATE_INDUSTRY_SOLUTION_APP = GLOBAL_INDUSTRY_SOLUTION_API + "apps/{appId}/services/{marketId}";
    public static final String SYNC_TRIAL_TIME = GLOBAL_INDUSTRY_SOLUTION_API + "sync";
    public static final String GET_SOLUTION_TRIAL_BY_SN = GLOBAL_INDUSTRY_SOLUTION_API + "getLastTrialBySn";
    public static final String GET_TRIAL_NUM = GLOBAL_INDUSTRY_SOLUTION_API + "trialNum";
    public static final String SAVE_TRIAL = GLOBAL_INDUSTRY_SOLUTION_API;


    public static final String FIND_MARKET_AVAILABLE_APPS = MARKET_INDUSTRY_SOLUTION_API + "available/apps";
    public static final String GET_SOLUTION_TRIAL_TERMINALS = MARKET_INDUSTRY_SOLUTION_API + "apps/{appId}/trial/terminals";
    public static final String APPLY_SOLUTION_APP_FOR_SPECIFIC = MARKET_INDUSTRY_SOLUTION_API + "apps/{appId}/apply";
    public static final String FIND_SPECIFIC_SOLUTION_RESELLER_ID_LIST = MARKET_INDUSTRY_SOLUTION_API + "apps/{appId}/reseller/specific/ids";
    public static final String SPECIFIC_SOLUTION_RESELLER = MARKET_INDUSTRY_SOLUTION_API + "apps/{appId}/reseller/specific";
    public static final String DELETE_SOLUTION_RESELLER = MARKET_INDUSTRY_SOLUTION_API + "apps/{appId}/reseller/{resellerId}";


    public static final String GET_RESELLER_APPLY_SOLUTION_BY_ID = MARKET_INDUSTRY_SOLUTION_API + "apps/reseller/apply/{applyId}";
    public static final String UPDATE_RESELLER_APPLY_SOLUTION = MARKET_INDUSTRY_SOLUTION_API + "apps/reseller/apply";
    public static final String GET_RESELLER_APPLY_SOLUTION_PENDING_COUNT = MARKET_INDUSTRY_SOLUTION_API + "apps/reseller/apply/pending/count";
    public static final String GET_RESELLER_APPLY_SOLUTION = MARKET_INDUSTRY_SOLUTION_API + "apps/reseller/apply";


    public static final String APPLY_INDUSTRY_SOLUTION = MARKET_INDUSTRY_SOLUTION_API + "developer/apply";
    public static final String FIND_DEVELOPER_SOLUTION_APPLY_PAGE = MARKET_INDUSTRY_SOLUTION_API + "developer/apply";
    public static final String UPDATE_INDUSTRY_SOLUTION_APPLY = MARKET_INDUSTRY_SOLUTION_API + "developer/apply/{developerId}";
    public static final String GET_DEVELOPER_SOLUTION_APPLY_COUNT = MARKET_INDUSTRY_SOLUTION_API + "developer/apply/pending-count";

    public static final String FIND_AD_VISUAL_PAGE_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-visuals";
    public static final String GET_AD_VISUAL_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-visuals/{adVisualId}";
    public static final String CREATE_AD_VISUAL_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-visual";

    public static final String CREATE_AD_GROUP_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-group";
    public static final String UPDATE_AD_GROUP_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-group/{adGroupId}";
    public static final String FIND_AD_GROUP_PAGE_ENDPOINT = CREATE_AD_GROUP_ENDPOINT;
    public static final String GET_AD_GROUP_DETAIL_ENDPOINT = UPDATE_AD_GROUP_ENDPOINT;
    public static final String DELETE_AD_GROUP_ENDPOINT = UPDATE_AD_GROUP_ENDPOINT;
    public static final String UPDATE_AD_GROUP_STATUS_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-group/{adGroupId}/status";
    public static final String UPDATE_AD_GROUP_VISUAL_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-group/ad-visual/{adGroupId}";
    public static final String GET_AD_GROUP_SLOT_PAGE_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-group/ad-slot";
    public static final String GET_AD_GROUP_VISUAL_PAGE_ENDPOINT = MARKET_SERVICE_CENTER_API + "adup/ad-group/ad-visual/{adSlotId}";
    public static final String GET_APP_BLACK_LIST_ENDPOINT = MARKET_SERVICE_CENTER_API + "airShield/app-black-list";
    public static final String UPDATE_APP_BLACK_LIST_ENDPOINT = MARKET_SERVICE_CENTER_API + "airShield/app-black-list/{id}";
    public static final String GET_SYS_FILE_ACCESS_ENDPOINT = MARKET_SERVICE_CENTER_API + "airShield/sys-file-access";
    public static final String UPDATE_SYS_FILE_ACCESS_ENDPOINT = MARKET_SERVICE_CENTER_API + "airShield/sys-file-access/{id}";
    public static final String GET_INTERVAL_ENDPOINT = MARKET_SERVICE_CENTER_API + "airShield/interval";
    public static final String UPDATE_INTERVAL_ENDPOINT = MARKET_SERVICE_CENTER_API + "airShield/interval";
    private static final String GET_AIR_SHIELD_DETECTION_RULES = MARKET_SERVICE_CENTER_API + "airShield/detection/{marketId}/rules";
    private static final String GET_GO_INSIGHT_CHARGE_VERSION = MARKET_SERVICE_CENTER_API + "insight/{marketId}/chargeVersion";

    public static final String MARKET_AIR_LINK_SETTING_URL = GLOBAL_SERVICE_CENTER_API + "air-link/setting/{marketId}";
    public static final String AIR_LINK_DATA_POOL_SETTINGS_URL = GLOBAL_SERVICE_CENTER_API + "air-link/data/pool/settings";
    public static final String AIR_LINK_DATA_POOL_SETTING_ENDPOINT = GLOBAL_SERVICE_CENTER_API + "air-link/data/pool/setting/{id}";
    public static final String AIR_LINK_SAME_POOL_MARKETS_URL = GLOBAL_SERVICE_CENTER_API + "air-link/same/pool/markets/{marketId}";
    public static final String MARKET_AIR_LINK_PLANS_URL = MARKET_SERVICE_CENTER_API + "air-link/plans/{marketId}";
    public static final String MARKET_AIR_LINK_PLAN_URL = MARKET_SERVICE_CENTER_API + "air-link/plan/{id}";
    public static final String MARKET_AIR_LINK_CURRENT_PLAN_URL = MARKET_SERVICE_CENTER_API + "air-link/plan/{marketId}";


    public static final String TRIGGER_SCHEDULE_JOB_END_POINT = "v1/schedule-job";

    public static final String RECHARGE_CONFIG_END_POINT = GLOBAL_SERVICE_CENTER_API + "air-link/recharge/config";
    public static final String UPDATE_CONFIG_END_POINT = GLOBAL_SERVICE_CENTER_API + "air-link/recharge/config/{minRecharge}";
    public static final String FIND_OVERDUE_MARKETS_END_POINT = GLOBAL_SERVICE_CENTER_API + "air-link/overdue/markets";
    public static final String MAX_POOL_NUM_END_POINT = GLOBAL_SERVICE_CENTER_API + "air-load/card/pool/maximum";
    public static final String UPDATE_MAX_POOL_NUM_END_POINT = GLOBAL_SERVICE_CENTER_API + "air-load/card/pool/{maximum}";

    public static final String CREATE_AIR_LOAD_CARD_POOL_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/pool/info";
    public static final String GET_AIR_LOAD_CARD_POOL_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/pool/info/{id}";
    public static final String UPDATE_AIR_LOAD_CARD_POOL_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/pool/info/{id}";
    public static final String DELETE_AIR_LOAD_CARD_POOL_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/pool/info/{id}";

    public static final String FIND_AIR_LOAD_CARD_POOL_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/pools";
    public static final String FIND_RESELLER_AIR_LOAD_CARD_POOL_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/pool/resellers";
    public static final String FIND_AIR_LOAD_CARDS_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/cards/{cardPoolId}";
    public static final String DELETE_AIR_LOAD_CARD_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/{id}";
    public static final String REMOVE_AIR_LOAD_CARD_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/card/{id}/remove";
    public static final String BATCH_DELETE_AIR_LOAD_CARD_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/cards/batch/delete";
    public static final String BATCH_REMOVE_AIR_LOAD_CARD_ENDPOINT = MARKET_SERVICE_CENTER_API + "air-load/cards/batch/remove";


    @Override
    public void triggerServiceCenterJob(String jobMethod) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("jobMethod", jobMethod);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(TRIGGER_SCHEDULE_JOB_END_POINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void closeService(String serviceType) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(CLOSE_ENDPOINT_SERVICE_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public MarketAvailableServicesVo findVasServices() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_SERVICES_ENDPOINT))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public ServiceInfoVo getResellerVasByReseller(String serviceType) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_RESELLER_VAS_BY_SERVICE_TYPE_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public List<VasInfo> findVasList(boolean filterEnabled) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("filterEnabled", filterEnabled);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_LIST_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<VasOperationHistoryInfo> findSubscribeHistoryPage(Long marketId, String serviceType, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_SUBSCRIBE_HISTORY_PAGE_ENDPOINT)
                        .setPathVariableValues(serviceType, marketId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateActiveStatus(Long marketId, VasStatusRequest vasStatusRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_ACTIVE_STATUS_ENDPOINT)
                        .setPathVariableValues(marketId))
                .setRequestBody(vasStatusRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public void createVasAgreement(VasAgreementRequest request) {
        IdVo idVo = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(CREATE_VAS_AGREEMENT_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(idVo);
    }

    @Override
    public void updateVasAgreement(Long agreementId, UpdateVasAgreementRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_VAS_AGREEMENT_ENDPOINT)
                        .setPathVariableValues(agreementId))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void publishVasAgreement(VasAgreementRequest request) {
        IdVo idVo = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(PUBLISH_VAS_AGREEMENT_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(idVo);
    }

    @Override
    public <T extends Serializable> PageInfo<VasAgreementInfo> findVasAgreements(String serviceType, String type, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_AGREEMENTS_ENDPOINT)
                        .setPathVariableValues(serviceType, type)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void deleteVasAgreement(Long agreementId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_VAS_AGREEMENT_ENDPOINT)
                        .setPathVariableValues(agreementId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public <T extends Serializable> PageInfo<Developer2ServiceInfo> findDeveloper2ServicePage(String serviceType, String status, String searchParam, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(STATUS_PARAM, status);
        paramMap.put("searchParam", searchParam);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_DEVELOPER_TO_SERVICE_PAGE_ENDPOINT)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void subscribeService(String serviceType, VasStatusUpdateRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SUBSCRIBE_SERVICE_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void unsubscribeService(String serviceType, VasStatusUpdateRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UNSUBSCRIBE_SERVICE_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public <T extends Serializable> PageInfo<ResellerInfo> findServiceDistributionPage(String serviceType, boolean includeAllData, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("includeAllData", includeAllData);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_SERVICE_DISTRIBUTION_PAGE_ENDPOINT)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void distributeService(String serviceType, ServiceSpecificRequest serviceSpecificRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DISTRIBUTE_SERVICE_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .setRequestBody(serviceSpecificRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void deleteServiceDistribution(String serviceType, Long resellerId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_SERVICE_DISTRIBUTION_ENDPOINT)
                        .setPathVariableValues(serviceType, resellerId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void agreeVasAgreement(Long agreementId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(AGREE_VAS_AGREEMENT_ENDPOINT)
                        .setPathVariableValues(agreementId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public VasAgreementInfo getVasAgreement2Market(String serviceType, String type, boolean agreed) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("agreed", agreed);
        paramMap.put("type", type);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_VAS_AGREEMENT_ENDPOINT)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateMarketDevServiceStatus(Long developerId, MarketDeveloperRequest marketDeveloperRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_MARKET_DEV_SERVICE_STATUS_ENDPOINT)
                        .setPathVariableValues(developerId))
                .setRequestBody(marketDeveloperRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void applyMarketDevService(String serviceType, Long agreementId) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("agreementId", agreementId);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(APPLY_DEVELOPER_TO_SERVICE)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public VasInfo getVasInfo(String serviceType) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_VAS_INFO_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void saveVasInfo(VasServiceRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SAVE_VAS_INFO_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void syncVasInfo(VasServiceRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SYNC_VAS_INFO_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void changeFreeTill(String serviceType, boolean freeTrial, Long freeTill) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("freeTrial", freeTrial);
        paramMap.put("freeTill", freeTill);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_FREE_TILL_ENDPOINT)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public Boolean isEndpointAvailable(String serviceType, Long marketId, Long endpointId) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(MARKET_ID_PARAM, marketId);
        paramMap.put("endpointId", endpointId);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(IS_AVAILABLE_ENDPOINT)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getSimpleResult(Boolean.class);
    }

    @Override
    public Boolean isMarket2VasCharged(String serviceType, Long marketId, Integer year, Integer month) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("serviceType", serviceType);
        paramMap.put("year", year);
        paramMap.put("month", month);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(IS_MARKET_TO_SERVICE_CHARGED)
                        .setPathVariableValues(marketId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getSimpleResult(Boolean.class);
    }

    private <T extends Serializable> void fillPageParams(TreeMap<String, Object> paramMap, Page<T> page) {
        if (Objects.nonNull(page)) {
            paramMap.put("pageNo", page.getPageNo());
            paramMap.put("pageSize", page.getPageSize());
            paramMap.put("orderBy", page.getOrderBy());
        }
    }

    @Override
    public void vasServiceSuspendDisabledEvent(String serviceType, boolean isSuspend) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("isSuspend", isSuspend);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(VAS_EVENT_ENDPOINT)
                        .setPathVariableValues(serviceType)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void batchUpdateVasStatus(VasStatusBatchUpdateRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(BATCH_UPDATE_VAS_STATUS_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void saveDefaultStatusInServices(Long marketId, String fromType) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("fromType", fromType);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SAVE_DEFAULT_STATUS_ENDPOINT)
                        .setPathVariableValues(marketId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void setFreeTrialInfo(FreeTrialRequest freeTrialRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SAVE_FREE_INFO_ENDPOINT))
                .setRequestBody(freeTrialRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();

    }

    @Override
    public void disableAllDevServices(Long developerId, String fromType) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("fromType", fromType);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DISABLE_ALL_DEVELOPER_SERVICES)
                        .setPathVariableValues(developerId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public List<Market2VasInfo> findVasEnabledListByServiceType(String serviceType) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_ENABLED_MARKETS_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public List<Long> findMonthAvailableMarketIdsByServiceType(String serviceType) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_CURRENT_AVAILABLE_MARKETS_ENDPOINT)
                        .setPathVariableValues(serviceType))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<AdSlotVo> findAdSlotPage(Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AD_SLOT_PAGE_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public IdVo createAdSlot(AdSlotCreateRequest request) {
        IdVo complexResult = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(CREATE_AD_SLOT_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(complexResult);
        return complexResult;
    }

    @Override
    public void disableAdSlot(Long adSlotId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DISABLE_AD_SLOT_ENDPOINT)
                        .setPathVariableValues(adSlotId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void activateAdSlot(Long adSlotId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(ACTIVATE_AD_SLOT_ENDPOINT)
                        .setPathVariableValues(adSlotId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void deleteAdSlot(Long adSlotId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_AD_SLOT_ENDPOINT)
                        .setPathVariableValues(adSlotId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public List<SimpleAdSlotSpecVo> findAdSlotSpecList() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AD_SLOT_SPEC_ENDPOINT))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }


    public void setEntityId(IdVo idVo) {
        AuditTrailContext current = AuditTrailContextHolder.getInstance().getCurrent();
        if (current != null) {
            current.addEntityIdIfMissing(idVo.getId());
        }

    }

    @Override
    public AdSlotVo getAdSlot(Long adSlotId) {
        AdSlotVo adSlotVo = getHttpInvoker().setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AD_SLOT_ENDPOINT)
                        .setPathVariableValues(adSlotId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        return Objects.isNull(adSlotVo.getId()) ? null : adSlotVo;
    }

    @Override
    public <T extends Serializable> PageInfo<AdVisualVo> findAdVisualPage(String name, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("name", name);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AD_VISUAL_PAGE_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public AdVisualVo getAdVisual(Long adVisualId) {
        AdVisualVo adVisualVo = getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AD_VISUAL_ENDPOINT)
                        .setPathVariableValues(adVisualId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        return Objects.isNull(adVisualVo.getId()) ? null : adVisualVo;
    }

    @Override
    public IdVo createAdVisual(AdVisualCreateRequest request) {
        IdVo complexResult = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(CREATE_AD_VISUAL_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(complexResult);
        return complexResult;
    }

    @Override
    public List<IdLabelVo> findModels(String adSlotType) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(AD_SLOT_TYPE_PARAM, adSlotType);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AD_SLOT_MODEL_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });

    }

    @Override
    public IdVo createAdGroup(AdGroupCreateUpdateRequest request) {
        IdVo complexResult = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(CREATE_AD_GROUP_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(complexResult);
        return complexResult;
    }

    @Override
    public void updateAdGroup(AdGroupCreateUpdateRequest request, Long adGroupId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_AD_GROUP_ENDPOINT)
                        .setPathVariableValues(adGroupId))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public <T extends Serializable> PageInfo<AdGroupVo> searchAdGroup(String status, Long resellerId, String adSlotType, String adName, Date startTime, Date endTime, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(STATUS_PARAM, status);
        paramMap.put("resellerId", resellerId);
        paramMap.put(AD_SLOT_TYPE_PARAM, adSlotType);
        paramMap.put("adName", adName);
        paramMap.put("startTime", Objects.nonNull(startTime) ? DateUtils.formatDate(startTime, "yyyy-MM-dd") : null);
        paramMap.put("endTime", Objects.nonNull(endTime) ? DateUtils.formatDate(endTime, "yyyy-MM-dd") : null);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AD_GROUP_PAGE_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public AdGroupDetailVo getAdGroupDetail(Long adGroupId) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AD_GROUP_DETAIL_ENDPOINT)
                        .setPathVariableValues(adGroupId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void deleteAdGroup(Long adGroupId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_AD_GROUP_ENDPOINT)
                        .setPathVariableValues(adGroupId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public void updateAdGroupStatus(AdGroupUpdateStatusRequest request, Long adGroupId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_AD_GROUP_STATUS_ENDPOINT)
                        .setPathVariableValues(adGroupId))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void updateAdGroupVisual(AdGroupVisualUpdateRequest request, Long adGroupId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_AD_GROUP_VISUAL_ENDPOINT)
                        .setPathVariableValues(adGroupId))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public <T extends Serializable> PageInfo<AdGroupSlotVo> findAdGroupSlotPage(Long adGroupId, String adSlotType, String modelId, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("adGroupId", adGroupId);
        paramMap.put(AD_SLOT_TYPE_PARAM, adSlotType);
        paramMap.put("modelId", modelId);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AD_GROUP_SLOT_PAGE_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<AdVisualVo> findAdGroupVisualPage(Long adSlotId, String adVisualName, Boolean choiceFlag, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("adVisualName", adVisualName);
        paramMap.put("choiceFlag", choiceFlag);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AD_GROUP_VISUAL_PAGE_ENDPOINT)
                        .setPathVariableValues(adSlotId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<BlackAppInfo> findAppBlackListPage(Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_APP_BLACK_LIST_ENDPOINT)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<RiskFileInfo> findSysFileAccessPage(Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_SYS_FILE_ACCESS_ENDPOINT)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public DetectionIntervalInfo getAttestationInterval() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_INTERVAL_ENDPOINT)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public Integer changeInterval(Integer interval) {
        TreeMap<String, Object> map = Maps.newTreeMap();
        map.put("interval", interval);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_INTERVAL_ENDPOINT)
                        .setQueryParams(map)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void addAppBlackList(BlackAppInfo blackAppInfo) {
        IdVo result = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_APP_BLACK_LIST_ENDPOINT)
                ).setRequestBody(blackAppInfo)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(result);

    }

    @Override
    public void updateAppBlackList(BlackAppInfo blackAppInfo) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_APP_BLACK_LIST_ENDPOINT)
                        .setPathVariableValues(blackAppInfo.getId())
                ).setRequestBody(blackAppInfo)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void deleteBlackApp(Long id) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_APP_BLACK_LIST_ENDPOINT)
                        .setPathVariableValues(id)
                )
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public BlackAppInfo getBlackApp(Long id) {
        BlackAppInfo blackAppInfo = getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_APP_BLACK_LIST_ENDPOINT)
                        .setPathVariableValues(id)
                )
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        return Objects.isNull(blackAppInfo.getId()) ? null : blackAppInfo;


    }

    @Override
    public void addRiskFileInfo(RiskFileInfo riskFileInfo) {
        IdVo result = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_SYS_FILE_ACCESS_ENDPOINT)
                ).setRequestBody(riskFileInfo)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(result);
    }

    @Override
    public void updateRiskFileInfo(RiskFileInfo riskFileInfo) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_SYS_FILE_ACCESS_ENDPOINT)
                        .setPathVariableValues(riskFileInfo.getId())
                ).setRequestBody(riskFileInfo)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void deleteRiskFileInfo(Long id) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_SYS_FILE_ACCESS_ENDPOINT)
                        .setPathVariableValues(id)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public RiskFileInfo getRiskFileInfo(Long id) {
        RiskFileInfo complexResult = getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_SYS_FILE_ACCESS_ENDPOINT)
                        .setPathVariableValues(id)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        return Objects.isNull(complexResult.getId()) ? null : complexResult;
    }

    private HttpInvoker getHttpInvoker() {
        Preconditions.checkNotNull(currentLoginProvider, "currentLoginProvider is null");
        MarketInfo currentMarketInfo = currentLoginProvider.getCurrentMarketInfo();
        Preconditions.checkNotNull(currentMarketInfo, "currentMarketInfo is null");
        UserInfo currentUserInfo = currentLoginProvider.getCurrentUserInfo();
        Map<String, String> headerMap = getStringStringMap(currentMarketInfo, currentUserInfo);
        return HttpInvoker.newInstance(httpClient).addHeaders(headerMap);
    }

    private Map<String, String> getStringStringMap(MarketInfo currentMarketInfo, UserInfo currentUserInfo) {
        Map<String, String> headerMap = Maps.newTreeMap();
        headerMap.put(HttpHeaders.CONTENT_LANGUAGE, MessageUtils.getRequestLocaleResolver().get().toString());
        headerMap.put("currentMarketId", String.valueOf(currentMarketInfo.getId()));
        if (Objects.nonNull(currentUserInfo)) {
            headerMap.put("currentUserId", String.valueOf(currentUserInfo.getId()));
            DeveloperInfo currentDeveloper = currentUserInfo.getCurrentDeveloper();
            if (Objects.nonNull(currentDeveloper)) {
                headerMap.put("currentDeveloperId", String.valueOf(currentDeveloper.getId()));
            }
            ResellerInfo currentReseller = currentUserInfo.getCurrentReseller();
            if (Objects.nonNull(currentReseller)) {
                headerMap.put("currentResellerId", String.valueOf(currentReseller.getId()));
            }
        }
        return headerMap;
    }

    @Override
    public AirShieldDetectRules getAttestationRules(Long marketId) {
        return getHttpInvoker().setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AIR_SHIELD_DETECTION_RULES)
                        .setPathVariableValues(marketId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public InsightMarketSettingVo getInsightChargeVersion(Long marketId) {
        return getHttpInvoker().setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_GO_INSIGHT_CHARGE_VERSION)
                        .setPathVariableValues(marketId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<VasAppInfo> findVasAppPage(String name, String appIds, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        paramMap.put("name", name);
        if (StringUtils.isNotBlank(appIds)) {
            paramMap.put("appIds", appIds);
        }
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_APP_PAGE)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public VasAppInfo getAvailableVasApp(Long appId) {
        return getHttpInvoker().setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_SOLUTION_APP_DETAIL)
                        .setPathVariableValues(appId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public List<VasAppInfo> findMarketAvailableApps(String name,
                                                    Boolean supportCloudData,
                                                    Boolean supportCloudMsg,
                                                    Boolean supportStackly) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("name", name);
        paramMap.put("supportCloudData", supportCloudData);
        paramMap.put("supportCloudMsg", supportCloudMsg);
        paramMap.put("supportStackly", supportStackly);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_MARKET_AVAILABLE_APPS)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }


    @Override
    public void updateAppServiceStatus(Long appId, Long marketId, VasAppRequest vasAppRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_INDUSTRY_SOLUTION_APP_STATUS)
                        .setPathVariableValues(appId, marketId)
                ).setRequestBody(vasAppRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public void updateAppServiceSetting(Long appId, Long marketId, VasAppRequest vasAppRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_INDUSTRY_SOLUTION_APP)
                        .setPathVariableValues(appId, marketId)
                ).setRequestBody(vasAppRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void applyIndustrySolutionAppForSpecific(Long appId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(APPLY_SOLUTION_APP_FOR_SPECIFIC)
                        .setPathVariableValues(appId)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void deleteSpecificSolutionReseller(Long appId, Long resellerId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_SOLUTION_RESELLER)
                        .setPathVariableValues(appId, resellerId)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public ResellerSolutionApplyVo getResellerApplySolutionById(Long applyId) {
        return getHttpInvoker().setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_RESELLER_APPLY_SOLUTION_BY_ID)
                        .setPathVariableValues(applyId))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateResellerApplySolution(ResellerSolutionApplyVo vo) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_RESELLER_APPLY_SOLUTION)
                ).setRequestBody(vo)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public ResellerSolutionApplyCountVo getResellerSolutionApplyPendingCount() {
        return getHttpInvoker().setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_RESELLER_APPLY_SOLUTION_PENDING_COUNT)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public <T extends Serializable> PageInfo<ResellerSolutionApply> getResellerApplySolutions(Page<ResellerSolutionApply> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_RESELLER_APPLY_SOLUTION)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }


    @Override
    public <T extends Serializable> PageInfo<SolutionAppTrialVo> getSolutionTrialPage(Long appId, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_SOLUTION_TRIAL_TERMINALS)
                        .setPathVariableValues(appId)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });

    }

    @Override
    public List<Long> findSpecificSolutionResellerIdList(Long appId, Integer limit) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        if (Objects.nonNull(limit)) {
            paramMap.put("limit", limit);
        }
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_SPECIFIC_SOLUTION_RESELLER_ID_LIST)
                        .setPathVariableValues(appId)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void specificSolutionReseller(Long appId, SpecificResellerRequest specificResellerRequest) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SPECIFIC_SOLUTION_RESELLER)
                        .setPathVariableValues(appId)
                ).setRequestBody(specificResellerRequest)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public <T extends Serializable> PageInfo<VasAppOperationHistoryInfo> findVasAppServiceHistory(Long appId, Long marketId, Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_APP_SERVICE_HISTORY)
                        .setPathVariableValues(appId, marketId)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public List<App2ServiceInfo> findVasAppMarkets(Long appId, String status) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        if (StringUtils.isNotBlank(status)) {
            paramMap.put(STATUS_PARAM, status);
        }
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_VAS_APP_MARKETS)
                        .setPathVariableValues(appId)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void applyIndustrySolution() {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(APPLY_INDUSTRY_SOLUTION)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public SolutionAppUsageVo getSolutionAppUsage(Long marketId, Long appId, String date) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(MARKET_ID_PARAM, marketId);
        paramMap.put(APP_ID_PARAM, appId);
        paramMap.put("date", date);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_SOLUTION_TRIAL_USAGES)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });

    }

    @Override
    public PageInfo<DeveloperSolutionApplyVo> findDeveloperSolutionApplyPage(Page<DeveloperSolutionApply> page, boolean approved, String email) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        paramMap.put("approved", approved);
        if (StringUtils.isNotBlank(email)) {
            paramMap.put("email", email);
        }
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_DEVELOPER_SOLUTION_APPLY_PAGE)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public DeveloperSolutionApplyCountVo getDeveloperSolutionApplyCount(boolean approved) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("approved", approved);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_DEVELOPER_SOLUTION_APPLY_COUNT)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateIndustrySolutionApply(Long developerId) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_INDUSTRY_SOLUTION_APPLY)
                        .setPathVariableValues(developerId)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void syncSolutionTrial(Long marketId, Long appId, String sn) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(MARKET_ID_PARAM, marketId);
        paramMap.put(APP_ID_PARAM, appId);
        paramMap.put("sn", sn);
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SYNC_TRIAL_TIME)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public SolutionAppTrialVo getLastTrialBySn(Long marketId, Long appId, String sn) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(MARKET_ID_PARAM, marketId);
        paramMap.put(APP_ID_PARAM, appId);
        paramMap.put("sn", sn);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_SOLUTION_TRIAL_BY_SN)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public Long getTrialAllNum(Long marketId, Long appId) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(MARKET_ID_PARAM, marketId);
        paramMap.put(APP_ID_PARAM, appId);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_TRIAL_NUM)
                        .setQueryParams(paramMap)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<NumResponse>() {
                }).getNum();
    }

    @Override
    public void saveTrial(SolutionAppTrialVo solutionAppTrialVo) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(SAVE_TRIAL)
                ).setRequestBody(solutionAppTrialVo)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public MarketAirLinkSettingInfo getMarketAirLinkSetting(Long marketId) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(MARKET_AIR_LINK_SETTING_URL)
                        .setPathVariableValues(marketId)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateMarketAirLinkSetting(Long marketId, VasStatusRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(MARKET_AIR_LINK_SETTING_URL)
                        .setPathVariableValues(marketId)
                ).setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public List<AirLinkDataPoolSettingInfo> findAirLinkDataPoolSettings() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(AIR_LINK_DATA_POOL_SETTINGS_URL)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }


    @Override
    public void updateAirLinkDataPoolSetting(Long id, AirLinkDataPoolSettingRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(AIR_LINK_DATA_POOL_SETTING_ENDPOINT)
                        .setPathVariableValues(id)
                ).setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public AirLinkDataPoolSettingInfo getAirLinkDataPoolSetting(Long id) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(AIR_LINK_DATA_POOL_SETTING_ENDPOINT)
                        .setPathVariableValues(id)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }


    @Override
    public MarketAirLinkSubscriptionPlanInfo getSubscriptionPlanByPeriod(Long marketId, String period) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        if (StringUtils.isNotBlank(period)) {
            paramMap.put("period", period);
        }
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(MARKET_AIR_LINK_CURRENT_PLAN_URL)
                        .setPathVariableValues(marketId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public List<MarketAirLinkSubscriptionPlanInfo> findSubscriptionPlans(Long marketId) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(MARKET_AIR_LINK_PLANS_URL)
                        .setPathVariableValues(marketId)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void readPlanById(Long id) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(MARKET_AIR_LINK_PLAN_URL)
                        .setPathVariableValues(id)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public RechargeConfigInfo getRechargeConfig() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(RECHARGE_CONFIG_END_POINT)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateRechargeConfig(Integer minRecharge) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_CONFIG_END_POINT)
                        .setPathVariableValues(minRecharge)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public List<Long> findAirLinkOverdueMarketIds() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_OVERDUE_MARKETS_END_POINT)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public List<Long> findMarketIdsWithSamePool(Long marketId, String period) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        if (StringUtils.isNotBlank(period)) {
            paramMap.put("period", period);
        }
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(AIR_LINK_SAME_POOL_MARKETS_URL)
                        .setPathVariableValues(marketId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public AirLoadCardPoolMaximumInfo getAirLoadCardPoolMaximumInfo() {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(MAX_POOL_NUM_END_POINT)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void updateAirLoadCardPoolMaximumInfo(Integer maximum) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_MAX_POOL_NUM_END_POINT)
                        .setPathVariableValues(maximum)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }


    @Override
    public AirLoadCardPoolInfo getAirLoadCardPool(Long id) {
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(GET_AIR_LOAD_CARD_POOL_ENDPOINT)
                        .setPathVariableValues(id))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void createAirLoadCardPool(AirLoadCardPoolRequest request) {
        IdVo idVo = getHttpInvoker()
                .setHttpMethod(HttpMethod.POST)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(CREATE_AIR_LOAD_CARD_POOL_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
        setEntityId(idVo);
    }

    @Override
    public void updateAirLoadCardPool(Long id, AirLoadCardPoolRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(UPDATE_AIR_LOAD_CARD_POOL_ENDPOINT)
                        .setPathVariableValues(id))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void deleteAirLoadCardPool(Long id) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_AIR_LOAD_CARD_POOL_ENDPOINT)
                        .setPathVariableValues(id))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public <T extends Serializable> PageInfo<AirLoadCardPoolInfo> findAirLoadCardPools(Page<T> page) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AIR_LOAD_CARD_POOL_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }


    @Override
    public <T extends Serializable> PageInfo<ResellerAirLoadCardPoolInfo> findAirLoadCardPoolResellers(Page<T> page, Long cardPoolId, Boolean includeAllData) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put("cardPoolId", cardPoolId);
        paramMap.put("includeAllData", includeAllData);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_RESELLER_AIR_LOAD_CARD_POOL_ENDPOINT)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });

    }

    @Override
    public <T extends Serializable> PageInfo<AirLoadCardInfo> findAirLoadCardsByPoolId(Page<T> page, Long cardPoolId, String status, String iccid, String serialNo) {
        TreeMap<String, Object> paramMap = Maps.newTreeMap();
        paramMap.put(STATUS_PARAM, status);
        paramMap.put("iccid", iccid);
        paramMap.put("serialNo", serialNo);
        fillPageParams(paramMap, page);
        return getHttpInvoker()
                .setHttpMethod(HttpMethod.GET)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(FIND_AIR_LOAD_CARDS_ENDPOINT)
                        .setPathVariableValues(cardPoolId)
                        .setQueryParams(paramMap))
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getComplexResult(new TypeReference<>() {
                });
    }

    @Override
    public void deleteAirLoadCard(Long id) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(DELETE_AIR_LOAD_CARD_ENDPOINT)
                        .setPathVariableValues(id)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void removeAirLoadCard(Long id) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(REMOVE_AIR_LOAD_CARD_ENDPOINT)
                        .setPathVariableValues(id)
                ).onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void batchDeleteAirLoadCards(AirLoadCardBatchRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.DELETE)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(BATCH_DELETE_AIR_LOAD_CARD_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }

    @Override
    public void batchRemoveAirLoadCards(AirLoadCardBatchRequest request) {
        getHttpInvoker()
                .setHttpMethod(HttpMethod.PUT)
                .setUrlBuilder(UrlBuilder.create()
                        .setHostUri(apiBaseUrl)
                        .setSubPaths(BATCH_REMOVE_AIR_LOAD_CARD_ENDPOINT))
                .setRequestBody(request)
                .onFailure(failedRequestHandler)
                .onError(exceptionHandler)
                .getVoidResult();
    }
}

