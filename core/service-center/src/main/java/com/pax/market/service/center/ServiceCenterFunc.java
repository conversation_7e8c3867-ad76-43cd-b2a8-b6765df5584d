package com.pax.market.service.center;

import com.pax.market.domain.entity.global.developer.DeveloperSolutionApply;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.domain.entity.market.solution.ResellerSolutionApply;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.dto.request.admin.common.specific.SpecificResellerRequest;
import com.pax.market.dto.request.admin.service.adup.*;
import com.pax.market.dto.request.market.MarketDeveloperRequest;
import com.pax.market.dto.request.market.ServiceSpecificRequest;
import com.pax.market.dto.request.market.VasStatusUpdateRequest;
import com.pax.market.dto.request.vas.*;
import com.pax.market.dto.vas.*;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.vo.admin.common.IdLabelVo;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.vo.admin.service.DeveloperSolutionApplyCountVo;
import com.pax.market.vo.admin.service.DeveloperSolutionApplyVo;
import com.pax.market.vo.admin.service.SolutionAppTrialVo;
import com.pax.market.vo.admin.service.SolutionAppUsageVo;
import com.pax.market.vo.admin.service.adup.*;
import com.pax.market.vo.admin.service.airshield.AirShieldDetectRules;
import com.pax.market.vo.admin.service.airshield.BlackAppInfo;
import com.pax.market.vo.admin.service.airshield.DetectionIntervalInfo;
import com.pax.market.vo.admin.service.airshield.RiskFileInfo;
import com.pax.market.vo.solution.ResellerSolutionApplyCountVo;
import com.pax.market.vo.solution.ResellerSolutionApplyVo;
import com.pax.market.vo.vas.InsightMarketSettingVo;
import com.pax.market.vo.vas.common.MarketAvailableServicesVo;
import com.pax.market.vo.vas.common.ServiceInfoVo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Zuo
 * @since 9.2
 */
public interface ServiceCenterFunc {

    void triggerServiceCenterJob(String jobMethod);

    void closeService(String serviceType);

    MarketAvailableServicesVo findVasServices();

    ServiceInfoVo getResellerVasByReseller(String serviceType);

    List<VasInfo> findVasList(boolean filterEnabled);


    void updateActiveStatus(Long marketId, VasStatusRequest vasStatusRequest);

    <T extends Serializable> PageInfo<VasOperationHistoryInfo> findSubscribeHistoryPage(Long marketId, String serviceType, Page<T> page);


    void createVasAgreement(VasAgreementRequest request);

    void updateVasAgreement(Long agreementId, UpdateVasAgreementRequest request);

    void publishVasAgreement(VasAgreementRequest request);

    <T extends Serializable> PageInfo<VasAgreementInfo> findVasAgreements(String serviceType, String type, Page<T> page);

    void deleteVasAgreement(Long agreementId);

    <T extends Serializable> PageInfo<Developer2ServiceInfo> findDeveloper2ServicePage(String serviceType,
                                                                                       String status,
                                                                                       String searchParam,
                                                                                       Page<T> page);

    void subscribeService(String serviceType, VasStatusUpdateRequest request);

    void unsubscribeService(String serviceType, VasStatusUpdateRequest request);


    <T extends Serializable> PageInfo<ResellerInfo> findServiceDistributionPage(String serviceType, boolean includeAllData, Page<T> page);

    void distributeService(String serviceType, ServiceSpecificRequest serviceSpecificRequest);

    void deleteServiceDistribution(String serviceType, Long resellerId);

    void agreeVasAgreement(Long agreementId);

    VasAgreementInfo getVasAgreement2Market(String serviceType, String type, boolean agreed);

    void updateMarketDevServiceStatus(Long developerId, MarketDeveloperRequest marketDeveloperRequest);

    void applyMarketDevService(String serviceType, Long agreementId);

    VasInfo getVasInfo(String serviceType);

    void saveVasInfo(VasServiceRequest request);

    void syncVasInfo(VasServiceRequest request);

    void changeFreeTill(String serviceType, boolean freeTrial, Long freeTill);

    Boolean isEndpointAvailable(String serviceType, Long marketId, Long endpointId);

    Boolean isMarket2VasCharged(String serviceType, Long marketId, Integer year, Integer month);

    void setFreeTrialInfo(FreeTrialRequest freeTrialRequest);

    void vasServiceSuspendDisabledEvent(String serviceType, boolean isSuspend);

    void batchUpdateVasStatus(VasStatusBatchUpdateRequest request);

    void saveDefaultStatusInServices(Long marketId, String fromType);

    void disableAllDevServices(Long developerId, String fromType);

    List<Market2VasInfo> findVasEnabledListByServiceType(String serviceType);

    List<Long> findMonthAvailableMarketIdsByServiceType(String serviceType);

    <T extends Serializable> PageInfo<AdSlotVo> findAdSlotPage(Page<T> page);

    IdVo createAdSlot(AdSlotCreateRequest request);

    AdSlotVo getAdSlot(Long adSlotId);

    void disableAdSlot(Long adSlotId);

    void activateAdSlot(Long adSlotId);

    void deleteAdSlot(Long adSlotId);

    List<SimpleAdSlotSpecVo> findAdSlotSpecList();

    <T extends Serializable> PageInfo<AdVisualVo> findAdVisualPage(String name, Page<T> page);

    AdVisualVo getAdVisual(Long adVisualId);

    IdVo createAdVisual(AdVisualCreateRequest request);

    List<IdLabelVo> findModels(String adSlotType);

    IdVo createAdGroup(AdGroupCreateUpdateRequest request);

    void updateAdGroup(AdGroupCreateUpdateRequest request, Long adGroupId);

    <T extends Serializable> PageInfo<AdGroupVo> searchAdGroup(String status, Long resellerId, String adSlotType, String adName, Date startTime, Date endTime, Page<T> page);

    AdGroupDetailVo getAdGroupDetail(Long adGroupId);

    void deleteAdGroup(Long adGroupId);

    void updateAdGroupStatus(AdGroupUpdateStatusRequest request, Long adGroupId);

    void updateAdGroupVisual(AdGroupVisualUpdateRequest request, Long adGroupId);

    <T extends Serializable> PageInfo<AdGroupSlotVo> findAdGroupSlotPage(Long adGroupId, String adSlotType, String modelId, Page<T> page);

    <T extends Serializable> PageInfo<AdVisualVo> findAdGroupVisualPage(Long adSlotId, String adVisualName, Boolean choiceFlag, Page<T> page);

    <T extends Serializable> PageInfo<BlackAppInfo> findAppBlackListPage(Page<T> page);

    <T extends Serializable> PageInfo<RiskFileInfo> findSysFileAccessPage(Page<T> page);

    DetectionIntervalInfo getAttestationInterval();

    Integer changeInterval(Integer interval);

    void addAppBlackList(BlackAppInfo blackAppInfo);

    void updateAppBlackList(BlackAppInfo blackAppInfo);

    void deleteBlackApp(Long id);

    BlackAppInfo getBlackApp(Long id);

    void addRiskFileInfo(RiskFileInfo riskFileInfo);

    void updateRiskFileInfo(RiskFileInfo riskFileInfo);

    void deleteRiskFileInfo(Long id);

    RiskFileInfo getRiskFileInfo(Long id);

    AirShieldDetectRules getAttestationRules(Long marketId);

    InsightMarketSettingVo getInsightChargeVersion(Long marketId);

    <T extends Serializable> PageInfo<VasAppInfo> findVasAppPage(String name, String appIds, Page<T> page);

    VasAppInfo getAvailableVasApp(Long appId);

    List<VasAppInfo> findMarketAvailableApps(String name,
                                             Boolean supportCloudData,
                                             Boolean supportCloudMsg,
                                             Boolean supportStackly);


    void updateAppServiceStatus(Long appId, Long marketId, VasAppRequest vasAppRequest);

    void updateAppServiceSetting(Long appId, Long marketId, VasAppRequest vasAppRequest);

    void applyIndustrySolutionAppForSpecific(Long appId);

    void deleteSpecificSolutionReseller(Long appId, Long resellerId);

    ResellerSolutionApplyVo getResellerApplySolutionById(Long applyId);

    void updateResellerApplySolution(ResellerSolutionApplyVo vo);

    ResellerSolutionApplyCountVo getResellerSolutionApplyPendingCount();

    <T extends Serializable> PageInfo<ResellerSolutionApply> getResellerApplySolutions(Page<ResellerSolutionApply> page);

    <T extends Serializable> PageInfo<SolutionAppTrialVo> getSolutionTrialPage(Long appId, Page<T> page);

    List<Long> findSpecificSolutionResellerIdList(Long appId, Integer limit);

    void specificSolutionReseller(Long appId, SpecificResellerRequest specificResellerRequest);

    <T extends Serializable> PageInfo<VasAppOperationHistoryInfo> findVasAppServiceHistory(Long appId, Long marketId, Page<T> page);

    List<App2ServiceInfo> findVasAppMarkets(Long appId, String status);

    void applyIndustrySolution();

    SolutionAppUsageVo getSolutionAppUsage(Long marketId, Long appId, String date);

    PageInfo<DeveloperSolutionApplyVo> findDeveloperSolutionApplyPage(Page<DeveloperSolutionApply> page, boolean approved, String email);

    DeveloperSolutionApplyCountVo getDeveloperSolutionApplyCount(boolean approved);

    void updateIndustrySolutionApply(Long developerId);

    void syncSolutionTrial(Long marketId, Long appId, String sn);

    SolutionAppTrialVo getLastTrialBySn(Long marketId, Long appId, String sn);

    Long getTrialAllNum(Long marketId, Long appId);

    void saveTrial(SolutionAppTrialVo solutionAppTrialVo);

    MarketAirLinkSettingInfo getMarketAirLinkSetting(Long marketId);

    void updateMarketAirLinkSetting(Long marketId, VasStatusRequest request);

    List<AirLinkDataPoolSettingInfo> findAirLinkDataPoolSettings();

    void updateAirLinkDataPoolSetting(Long id, AirLinkDataPoolSettingRequest request);

    AirLinkDataPoolSettingInfo getAirLinkDataPoolSetting(Long id);

    List<MarketAirLinkSubscriptionPlanInfo> findSubscriptionPlans(Long marketId);

    MarketAirLinkSubscriptionPlanInfo getSubscriptionPlanByPeriod(Long marketId, String period);

    void readPlanById(Long id);

    RechargeConfigInfo getRechargeConfig();

    void updateRechargeConfig(Integer minRecharge);

    List<Long> findAirLinkOverdueMarketIds();

    List<Long> findMarketIdsWithSamePool(Long marketId, String period);

    AirLoadCardPoolMaximumInfo getAirLoadCardPoolMaximumInfo();

    void updateAirLoadCardPoolMaximumInfo(Integer maximum);

    AirLoadCardPoolInfo getAirLoadCardPool(Long id);

    void createAirLoadCardPool(AirLoadCardPoolRequest request);

    void updateAirLoadCardPool(Long id, AirLoadCardPoolRequest request);

    void deleteAirLoadCardPool(Long id);

    <T extends Serializable> PageInfo<AirLoadCardPoolInfo> findAirLoadCardPools(Page<T> page);

    <T extends Serializable> PageInfo<ResellerAirLoadCardPoolInfo> findAirLoadCardPoolResellers(Page<T> page, Long cardPoolId, Boolean includeAllData);

    <T extends Serializable> PageInfo<AirLoadCardInfo> findAirLoadCardsByPoolId(Page<T> page, Long cardPoolId, String status, String iccid, String serialNo);

    void deleteAirLoadCard(Long id);

    void removeAirLoadCard(Long id);

    void batchDeleteAirLoadCards(AirLoadCardBatchRequest request);

    void batchRemoveAirLoadCards(AirLoadCardBatchRequest request);
}


