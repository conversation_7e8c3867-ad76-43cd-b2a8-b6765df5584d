/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.paxstore.global.domain.service.report;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.report.JobStatusInScheduleCenter;
import com.pax.market.domain.entity.global.report.ReportExecutionContext;
import com.pax.market.domain.entity.global.report.ReportExecutionStatus;
import com.pax.market.domain.entity.global.report.ReportTask;
import com.pax.market.domain.searchcriteria.ReportTaskSearchCriteria;
import com.pax.market.dto.ReportScheduleType;
import com.pax.market.dto.response.report.ReportRuntimeInfo;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.report.ReportExecutionContextDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * The type Report execution context service.
 *
 * <AUTHOR>
 * @date 2018 /12/18 18:01:36
 */
@Service
@RequiredArgsConstructor
public class ReportExecutionContextService extends CrudService<ReportExecutionContextDao, ReportExecutionContext> {

    private static String getDayOfMonthOrWeek(String cron, ReportScheduleType type) {
        if (StringUtils.isEmpty(cron)) {
            return "";
        } else {
            String[] arr = cron.split(" ");
            if (arr.length == 6 || arr.length == 7) {
                if (ReportScheduleType.Monthly == type) {
                    return arr[3];
                } else {
                    return arr[5];
                }
            } else {
                return "";
            }
        }
    }

    /**
     * Gets report execution context by job name.
     *
     * @param jobName the job name
     * @return the report execution context by job name
     */
    public ReportExecutionContext getReportExecutionContextByJobName(String jobName) {
        return dao.getByJobName(jobName);
    }

    /**
     * Update report execution context status.
     *
     * @param status the status
     * @param ids    the ids
     */
    @Transactional
    public void updateReportExecutionContextStatus(ReportExecutionStatus status, Long... ids) {
        if (ids != null && ids.length > 0) {
            dao.updateStatus(new HashSet<>(Arrays.asList(ids)), status);
            for (int i = 0; i < ids.length; i++) {
                ReportExecutionContext context = dao.get(ids[i]);
                if (ReportExecutionStatus.Paused == status) {
                    context.setStatusInScheduleCenter(JobStatusInScheduleCenter.Paused);
                } else {
                    context.setStatusInScheduleCenter(null);
                }
                context.setNextExecuteTime(null);
                dao.update(context);
            }
        }
    }

    /**
     * Gets report task by page.
     *
     * @param searchCriteria the search criteria
     * @return the report task by page
     */
    public Page<ReportTask> getReportTaskByPage(ReportTaskSearchCriteria searchCriteria) {
        List<ReportTask> reportTasks = dao.getReportTaskByPage(searchCriteria);
        return searchCriteria.getPage().setList(reportTasks);
    }

    /**
     * Delete scheduled job.
     *
     * @param id the id
     */
    @MasterDs
    public void deleteScheduledJob(Long id, Long updateById, Date updatedDate) {
        dao.deleteScheduledJob(id, updateById, updatedDate);
    }

    @MasterDs
    public void delete(Long id, Long updateById, Date updatedDate) {
        dao.delete(id, updateById, updatedDate);
    }

    /**
     * Gets by market id and user id and id.
     *
     * @param marketId the market id
     * @param userId   the user id
     * @param id       the id
     * @return the by market id and user id and id
     */
    public ReportExecutionContext getByMarketIdAndUserIdAndId(Long marketId, Long userId, Long id) {
        return dao.getByMarketIdAndUserIdAndId(marketId, userId, id);
    }

    /**
     * Has scheduled report task incomplete boolean.
     *
     * @param reportId   the report id
     * @param userId     the user id
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @return the boolean
     */
    public int getScheduledReportTaskIncompleteCount(Long reportId, Long userId, Long marketId, Long resellerId) {
        return dao.getScheduledReportTaskIncompleteCount(reportId, userId, marketId, resellerId);
    }

    /**
     * Batch suspend report task for markets.
     *
     * @param marketIds the market ids
     */
    @MasterDs
    public void batchSuspendReportTaskForMarkets(List<Long> marketIds) {
        if (CollectionUtils.isNotEmpty(marketIds)) {
            dao.batchSuspendTaskByMarketIds(marketIds);
        }
    }

    /**
     * Batch terminate report task for markets.
     *
     * @param marketIds the market ids
     */
    @MasterDs
    public void batchTerminateReportTaskForMarkets(List<Long> marketIds) {
        if (CollectionUtils.isNotEmpty(marketIds)) {
            dao.batchTerminateTaskByMarketIds(marketIds);
        }
    }

    /**
     * Batch terminate report task for all markets.
     */
    @MasterDs
    public void batchTerminateReportTaskForAllMarkets() {
        dao.batchTerminateTaskByMarketIds(null);
    }

    /**
     * Batch recover report task for markets.
     *
     * @param marketIds the market ids
     */
    @MasterDs
    public void batchRecoverReportTaskForMarkets(List<Long> marketIds) {
        if (CollectionUtils.isNotEmpty(marketIds)) {
            dao.batchRecoverTaskByMarketIds(marketIds);
        }
    }

    /**
     * Get task will fire in seconds list.
     *
     * @param fireInSeconds the fire in seconds
     * @return the list
     */
    public List<ReportExecutionContext> getTaskWillFireInSeconds(long fireInSeconds) {
        if (fireInSeconds <= 0) {
            logger.warn("Parameter fireInSeconds must grate than 0.");
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        Date start = new Date(System.currentTimeMillis() - fireInSeconds * 1000);
        Date end = new Date(System.currentTimeMillis() + fireInSeconds * 1000);
        return dao.findTaskWillFireBetweenStartAndEnd(start, end);
    }

    /**
     * Complete task.
     *
     * @param reportTask the report task
     */
    @MasterDs
    public void completeTask(ReportExecutionContext reportTask) {
        if (reportTask != null) {
            reportTask.setStatus(ReportExecutionStatus.Completed);
            reportTask.setNextExecuteTime(null);
            save(reportTask);
        }
    }

    /**
     * Terminate task.
     *
     * @param reportTask the report task
     */
    @MasterDs
    public void terminateTask(ReportExecutionContext reportTask) {
        if (reportTask != null) {
            reportTask.setStatus(ReportExecutionStatus.Terminated);
            reportTask.setNextExecuteTime(null);
            save(reportTask);
        }
    }

    /**
     * Suspend task.
     *
     * @param reportTask the report task
     */
    @MasterDs
    public void suspendTask(ReportExecutionContext reportTask) {
        if (reportTask != null) {
            reportTask.setStatus(ReportExecutionStatus.Suspend);
            reportTask.setNextExecuteTime(null);
            save(reportTask);
        }
    }

    /**
     * Fail task.
     *
     * @param reportTask the report task
     */
    @MasterDs
    public void failTask(ReportExecutionContext reportTask) {
        if (reportTask != null) {
            reportTask.setStatus(ReportExecutionStatus.Failed);
            reportTask.setNextExecuteTime(null);
            save(reportTask);
        }
    }

    /**
     * Repair report execution context status int.
     *
     * @return the int
     */
    @MasterDs
    public int repairReportExecutionContextStatus() {
        return dao.repairImmediateReportExecutionContextStatus(DateUtils.subHours(new Date(), 6)) + dao.repairScheduleReportExecutionContextStatus(DateUtils.subHours(new Date(), 6));
    }

    public ReportRuntimeInfo convertToReportRuntimeInfo(ReportExecutionContext reportExecutionContext) {
        if (Objects.isNull(reportExecutionContext)) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        ReportRuntimeInfo reportRuntimeInfo = new ReportRuntimeInfo();

        reportRuntimeInfo.setId(reportExecutionContext.getId());
        reportRuntimeInfo.setCustomReportName(reportExecutionContext.getCustomReportName());
        reportRuntimeInfo.setExportFormat(reportExecutionContext.getExportFormat());
        reportRuntimeInfo.setScheduleType(reportExecutionContext.getScheduleType());
        if (reportExecutionContext.getEndDate() != null) {
            reportRuntimeInfo.setEndTime(this.formatDate(reportExecutionContext.getEndDate()));
        }
        reportRuntimeInfo.setReportId(reportExecutionContext.getReportId());
        reportRuntimeInfo.setEmailTo(reportExecutionContext.getEmailTo());
        reportRuntimeInfo.setTemplateId(reportExecutionContext.getTemplateId());
        reportRuntimeInfo.setTimeZone(reportExecutionContext.getTimeZone());
        if (StringUtils.equals(ReportScheduleType.Once.val(), reportExecutionContext.getScheduleType())) {
            reportRuntimeInfo.setScheduleTime(getScheduleTimeStr(reportExecutionContext.getCron(), ReportScheduleType.Once));
        } else if (StringUtils.equals(ReportScheduleType.Daily.val(), reportExecutionContext.getScheduleType())) {
            reportRuntimeInfo.setScheduleTime(getScheduleTimeStr(reportExecutionContext.getCron(), ReportScheduleType.Daily));
        } else if (StringUtils.equals(ReportScheduleType.Weekly.val(), reportExecutionContext.getScheduleType())) {
            reportRuntimeInfo.setScheduleDayOfWeek(getDayOfMonthOrWeek(reportExecutionContext.getCron(), ReportScheduleType.Weekly));
            reportRuntimeInfo.setScheduleTime(getScheduleTimeStr(reportExecutionContext.getCron(), ReportScheduleType.Weekly));
        } else if (StringUtils.equals(ReportScheduleType.Monthly.val(), reportExecutionContext.getScheduleType())) {
            reportRuntimeInfo.setScheduleDayOfMonth(getDayOfMonthOrWeek(reportExecutionContext.getCron(), ReportScheduleType.Monthly));
            reportRuntimeInfo.setScheduleTime(getScheduleTimeStr(reportExecutionContext.getCron(), ReportScheduleType.Monthly));
        }
        return reportRuntimeInfo;
    }

    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        return sdf.format(date);
    }

    private String getScheduleTimeStr(String cron, ReportScheduleType type) {
        if (StringUtils.isEmpty(cron)) {
            return "";
        } else {
            String[] arr = cron.split(" ");
            if (arr.length == 6 || arr.length == 7) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.SECOND, Integer.parseInt(arr[0]));
                calendar.set(Calendar.MINUTE, Integer.parseInt(arr[1]));
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(arr[2]));
                if (ReportScheduleType.Once == type) {
                    calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(arr[3]));
                    calendar.set(Calendar.MONTH, Integer.parseInt(arr[4]) - 1);
                    if (arr.length == 7) {
                        calendar.set(Calendar.YEAR, Integer.parseInt(arr[6]));
                    }
                    Date date = calendar.getTime();
                    return formatDate(date);
                } else if (ReportScheduleType.Daily == type) {
                    Date date = calendar.getTime();
                    return formatDate(date);
                } else if (ReportScheduleType.Weekly == type) {
                    Date date = calendar.getTime();
                    return formatDate(date);
                } else if (ReportScheduleType.Monthly == type) {
                    Date date = calendar.getTime();
                    return formatDate(date);
                } else {
                    return "";
                }
            } else {
                return "";
            }
        }
    }
}
