package com.paxstore.global.domain.dao.app;

import com.pax.market.domain.entity.global.app.AppUninstallWhiteList;
import com.pax.market.domain.entity.global.app.WhiteList;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;


@MyBatisDao
public interface AppUninstallWhiteListDao extends CrudDao<AppUninstallWhiteList>{

    Boolean isPackageInWhiteList(@Param("marketId") Long marketId,
                                 @Param("packageName")String packageName);

    Integer getCount(WhiteList whiteList);

    void deleteWhitelist(AppUninstallWhiteList appUninstallWhiteList);
}
