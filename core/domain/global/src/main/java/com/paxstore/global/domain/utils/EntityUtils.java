/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.utils;

import com.google.common.collect.Sets;
import com.pax.market.constants.AppStatus;
import com.pax.market.constants.CodeTypes;
import com.pax.market.constants.SignatureType;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.global.setting.Code;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.persistence.BaseEntity;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.global.domain.service.setting.CodeService;
import com.paxstore.global.domain.support.signature.SignatureSupportService;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * File Description
 *
 * <AUTHOR>
 */
public class EntityUtils {

    /**
     * Load first 5 apks info app.
     *
     * @param app the app
     */
    public static void loadApkList(App app) {
        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);

        if (app == null || LongUtils.isBlankOrNotPositive(app.getId())) {
            return;
        }

        AppApkQuery appApkQuery = new AppApkQuery();
        appApkQuery.setPage(new Page<>());
        appApkQuery.getPage().setPageNo(1);
        appApkQuery.getPage().setPageSize(5);

        app.setApkList(apkService.findList(app.getId(), appApkQuery));
    }

    /**
     * Load first 5 apks that matches appApkQuery info app.
     *
     * @param app         the app
     * @param appApkQuery the app apk query
     */
    public static void loadApkList(App app, AppApkQuery appApkQuery) {

        if (app == null || LongUtils.isBlankOrNotPositive(app.getId())) {
            return;
        }
        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);
        List<Apk> apkListResult = apkService.findList(app.getId(), appApkQuery);

        for (Apk apk : apkListResult) {
            apk.setAppId(app.getId());
            apkService.generateApkIconScreenshotLink(apk);
        }

        app.setApkList(apkListResult);
    }

    /**
     * Sets apk specific filters.
     *
     * @param appApkQuery the app apk query
     */
    public static void setApkSpecificFilters(AppApkQuery appApkQuery) {
        //全局应用市场根代理商不需要过滤应用市场定向发布
        CurrentLoginProvider loginProvider = SpringContextHolder.getApplicationContext().getBean(CurrentLoginProvider.class);
        boolean isCurrentUserInRootReseller = false;
        if (loginProvider.getCurrentUserInfo() != null && loginProvider.getCurrentUserInfo().getCurrentReseller() != null) {
            isCurrentUserInRootReseller = LongUtils.isBlankOrNotPositive(loginProvider.getCurrentUserInfo().getCurrentReseller().getParentId());
        } else if (loginProvider.getCurrentThirdPartySysInfo() != null && loginProvider.getCurrentThirdPartySysInfo().getReseller() != null) {
            isCurrentUserInRootReseller = LongUtils.isBlankOrNotPositive(loginProvider.getCurrentThirdPartySysInfo().getReseller().getParentId());
        }
        //全局应用市场的根代理商不需要检查子应用市场的定向发布
        if (!LongUtils.equals(SystemConstants.SUPER_MARKET_ID, loginProvider.getCurrentMarketInfo().getId()) || !isCurrentUserInRootReseller) {
            appApkQuery.setFilterApkPublish(true);
        }
        //根代理商不需要验证代理商定向发布
        if (!isCurrentUserInRootReseller) {
            appApkQuery.setFilterApkReseller(true);
            Long resellerId = loginProvider.getCurrentUserInfo() != null ? loginProvider.getCurrentUserInfo().getCurrentReseller().getId() :
                    loginProvider.getCurrentThirdPartySysInfo() != null ? loginProvider.getCurrentThirdPartySysInfo().getReseller().getId() : null;
            appApkQuery.setResellerId(resellerId);
        }
    }

    /**
     * Load latest apk info.
     *
     * @param marketId the market id
     * @param app      the app
     */
    public static void loadLatestNotDraftApk(Long marketId, App app) {
        AppApkQuery appApkQuery = new AppApkQuery();
        setApkSpecificFilters(appApkQuery);
        appApkQuery.addExcludeApkStatus(AppStatus.DRAFT);

        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);
        setLatestApk(app, apkService.get(apkService.getLatestApkId(marketId, app.getId(), null, appApkQuery)));
    }

    /**
     * Load pending apk info.
     *
     * @param marketId the market id
     * @param app      the app
     */
    public static void loadPendingApk(Long marketId, App app) {
        AppApkQuery appApkQuery = new AppApkQuery();
        setApkSpecificFilters(appApkQuery);
        appApkQuery.addExcludeApkStatus(AppStatus.DRAFT);
        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);
        setLatestApk(app, apkService.get(apkService.getLatestPendingApkId(marketId, app.getId(), AppStatus.PENDING, appApkQuery)));
    }

    /**
     * Load reseller latest apk.
     *
     * @param marketId the market id
     * @param app      the app
     */
    public static void loadLatestOnlineApk(Long marketId, App app) {
        AppApkQuery appApkQuery = new AppApkQuery();
        setApkSpecificFilters(appApkQuery);

        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);
        setLatestApk(app, apkService.get(apkService.getLatestApkId(marketId, app.getId(), AppStatus.ONLINE, appApkQuery)));
    }

    /**
     * Load developer latest apk.
     *
     * @param marketId the market id
     * @param app      the app
     */
    public static void loadLatestApk(Long marketId, App app) {
        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);
        setLatestApk(app, apkService.get(apkService.getLatestApkId(marketId, app.getId(), null, null)));
    }

    private static void setLatestApk(App app, Apk latestApk) {
        if (latestApk == null) {
            return;
        }
        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);
        apkService.generateApkIconScreenshotLink(latestApk);
        switch (latestApk.getStatus()) {
            case AppStatus.ONLINE -> app.setLatestOnlineApk(latestApk);
            case AppStatus.UNAVAILABLE -> app.setLatestOfflineApk(latestApk);
            case AppStatus.REJECTED -> app.setLatestRejectedApk(latestApk);
            case AppStatus.PENDING -> app.setLatestPendingApk(latestApk);
            case AppStatus.DRAFT -> app.setLatestDraftApk(latestApk);
            default -> {
            }
        }
    }

    /**
     * Load featured apk info app.
     *
     * @param app      the app
     * @param marketId the marketId
     */
    public static void loadFeaturedApk(App app, Long marketId, Set<Long> resellerIds) {
        if (app == null || LongUtils.isBlankOrNotPositive(app.getId())) {
            return;
        }
        ApkService apkService = SpringContextHolder.getApplicationContext().getBean(ApkService.class);

        Apk featuredApk = null;
        if (app.getFeaturedApk() == null
                || app.getFeaturedApk().getSpecificMarket()
                || app.getFeaturedApk().getSpecificReseller()
                || AppStatus.UNAVAILABLE.equals(app.getFeaturedApk().getStatus())) {
            AppApkQuery appApkQuery = new AppApkQuery();
            appApkQuery.setFilterApkReseller(true);
            appApkQuery.setResellerIds(resellerIds);
            appApkQuery.setFilterApkPublish(true);
            Long latestOnlineApkId = apkService.getLatestApkId(marketId, app.getId(), AppStatus.ONLINE, appApkQuery);
            if (LongUtils.isNotBlankAndPositive(latestOnlineApkId)) {
                featuredApk = apkService.get(latestOnlineApkId);
                featuredApk.setSpecificMarket(false);
                featuredApk.setSpecificReseller(false);
            } else {
                Long latestOfflineApkId = apkService.getLatestApkId(marketId, app.getId(), AppStatus.UNAVAILABLE, appApkQuery);
                if (LongUtils.isNotBlankAndPositive(latestOfflineApkId)) {
                    featuredApk = apkService.get(latestOfflineApkId);
                    featuredApk.setSpecificMarket(false);
                    featuredApk.setSpecificReseller(false);
                } else {
                    appApkQuery.setFilterApkPublish(false);
                    appApkQuery.setFilterApkReseller(false);
                    Long latestPublishOnlineApkId = apkService.getLatestApkId(marketId, app.getId(), AppStatus.ONLINE, appApkQuery);
                    if (LongUtils.isNotBlankAndPositive(latestPublishOnlineApkId)) {
                        featuredApk = apkService.get(latestPublishOnlineApkId);
                        featuredApk.setSpecificMarket(true);
                        featuredApk.setSpecificReseller(true);
                    } else {
                        Long latestPublishOfflineApkId = apkService.getLatestApkId(marketId, app.getId(), AppStatus.UNAVAILABLE, appApkQuery);
                        if (LongUtils.isNotBlankAndPositive(latestPublishOfflineApkId)) {
                            featuredApk = apkService.get(latestPublishOfflineApkId);
                            featuredApk.setSpecificMarket(true);
                            featuredApk.setSpecificReseller(true);
                        }
                    }
                }
            }
        } else {
            featuredApk = apkService.get(app.getFeaturedApk().getId());
            featuredApk.setSpecificMarket(false);
            featuredApk.setSpecificReseller(false);
        }

        if (featuredApk != null) {
            app.setFeaturedApk(featuredApk);
        }
    }

    /**
     * Load code info apk.
     *
     * @param apk the apk
     */
    public static void loadApkCategory(Apk apk) {
        if (apk == null || LongUtils.isBlankOrNotPositive(apk.getId())) {
            return;
        }
        apk.setApkCategoryList(loadApkCategory(apk.getApkCategoryList()));
    }

    private static List<Code> loadApkCategory(List<Code> apkCategoryList) {
        if (apkCategoryList == null || apkCategoryList.isEmpty()) {
            return apkCategoryList;
        }

        CodeService codeService = SpringContextHolder.getApplicationContext().getBean(CodeService.class);
        List<Code> apkCategoryList_ = new ArrayList<>();
        for (Code apkCategory : apkCategoryList) {
            apkCategory = codeService.getCodeForDisplay(CodeTypes.APP_CATEGORY, apkCategory.getValue());
            if (apkCategory != null) {
                apkCategoryList_.add(apkCategory);
            }
        }
        return apkCategoryList_;
    }

    public static void loadResellerSignatureSetting(MarketInfo market) {
        if (market == null) {
            return;
        }
        //代理商签名是否开启需要检查签名服务器的配置
        //新增4096 需要增加返回区分
        if ((market.getAllowResellerSignature() || market.getAllowResellerPuk())) {
            SignatureSupportService signatureSupportService = SpringContextHolder.getBean(SignatureSupportService.class);
            if (!signatureSupportService.isSignatureEnabled(market.getId(), market.getRootResellerId(), null)) {
                market.setAllowResellerPuk(Boolean.FALSE);
                market.setAllowResellerSignature(Boolean.FALSE);
            }
        }
    }

    /**
     * Gets limit.
     *
     * @param currentLimit the current limit
     * @param globalLimit  the global limit
     * @return the limit
     */
    public static int getLimit(int currentLimit, int globalLimit) {
        if (globalLimit == 0 || currentLimit == 0) {
            return 0;
        } else if (currentLimit < 0) {
            return globalLimit;
        } else if (globalLimit < 0) {
            return currentLimit;
        } else {
            return Math.min(currentLimit, globalLimit);
        }
    }

    /**
     * Load models list.
     *
     * @param factoryList the factory list
     */
    public static void loadModels(List<Factory> factoryList) {
        factoryList.forEach(EntityUtils::loadModel);
    }

    /**
     * Load model factory.
     *
     * @param factory the factory
     */
    private static void loadModel(Factory factory) {
        loadModel(factory, null, null);
    }

    public static void loadModel(Factory factory, String modelName, String productType) {

        ModelService modelService = SpringContextHolder.getBean(ModelService.class);

        Model model = new Model();
        model.setFactory(new Factory(factory.getId()));
        Set<String> queryFactoryIds = Sets.newHashSet();
        if (LongUtils.isNotBlankAndPositive(factory.getId()) && factory.getId() > 0) {
            queryFactoryIds.add(String.valueOf(factory.getId()));
        }
        model.setFactoryIds(queryFactoryIds);
        if (StringUtils.isNotBlank(modelName)){
            model.setName(StringUtils.trim(modelName));
        }
        if (StringUtils.isNotBlank(productType)){
            model.setProductType(StringUtils.trim(productType));
        }
        factory.setModelList(modelService.findList(model));
    }

    /**
     * Gets entity id.
     *
     * @param entity the entity
     * @return the entity id
     */
    public static Long getEntityId(BaseEntity<?> entity) {
        if (entity == null) {
            return -1L;
        } else {
            return entity.getId();
        }
    }
}
