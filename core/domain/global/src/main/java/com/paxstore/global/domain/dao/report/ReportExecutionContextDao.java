/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.paxstore.global.domain.dao.report;

import com.pax.market.domain.entity.global.report.ReportExecutionContext;
import com.pax.market.domain.entity.global.report.ReportExecutionStatus;
import com.pax.market.domain.entity.global.report.ReportTask;
import com.pax.market.domain.searchcriteria.ReportTaskSearchCriteria;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/18 17:59:12
 */

@MyBatisDao
public interface ReportExecutionContextDao extends CrudDao<ReportExecutionContext> {
    ReportExecutionContext getByJobName(@Param("jobName") String jobName);

    void updateStatus(@Param("contextIds") Collection<Long> id, @Param("status") ReportExecutionStatus status);

    List<ReportTask> getReportTaskByPage(ReportTaskSearchCriteria searchCriteria);

    List<ReportExecutionContext> getExecutionContextNeed2UpdateStatusInScheduleCenter();

    void updateReportJobStatusInScheduleCenter(@Param("id") Long id, @Param("statusInScheduleCenter") String status);

    void deleteScheduledJob(@Param("id") Long id, Long updateById, Date updatedDate);

    void delete(@Param("id") Long id, Long updateById, Date updatedDate);

    ReportExecutionContext getByMarketIdAndUserIdAndId(@Param("marketId") Long marketId, @Param("userId") Long userId, @Param("id") Long id);

    int getScheduledReportTaskIncompleteCount(@Param("reportId") Long reportId, @Param("userId") Long userId, @Param("marketId") Long marketId, @Param("resellerId") Long resellerId);

    void batchSuspendTaskByMarketIds(@Param("marketIds") List<Long> marketIds);

    void batchTerminateTaskByMarketIds(@Param("marketIds") List<Long> marketIds);

    void batchRecoverTaskByMarketIds(@Param("marketIds") List<Long> marketIds);

    List<ReportExecutionContext> findTaskWillFireBetweenStartAndEnd(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    int repairImmediateReportExecutionContextStatus(@Param("updatedDate") Date updatedDate);

    int repairScheduleReportExecutionContextStatus(@Param("updatedDate") Date updatedDate);
}
