/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.dao.setting;

import java.util.List;

import com.pax.market.domain.entity.global.setting.Code;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

/**
 * Created by gubin_ on 2016/11/29.
 */
@MyBatisDao
public interface LangDao {

    /**
     * Get langs
     *
     * @return langs langs
     */
    List<Code> getLangs();

    /**
     * Save lang
     *
     * @param code the code
     */
    void saveLang(Code code);

}
