package com.paxstore.global.domain.service.vas.airlink;

import com.pax.market.billing.MarketBillingForZolonService;
import com.pax.market.constants.airlink.*;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrder;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrderItem;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkVoucher;
import com.pax.market.domain.entity.global.vas.airlink.MarketAirLinkSetting;
import com.pax.market.domain.query.AirLinkOrderQuery;
import com.pax.market.dto.request.market.billing.ZolonBillingAirLinkOrderRequest;
import com.pax.market.dto.vas.AirLinkUsageInfo;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.vas.airlink.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @Author: 李志祥
 * @Date: 2025/1/8 14:42
 * @Since: 2.0
 **/
@Service
@RequiredArgsConstructor
public class AirLinkOrderService extends CrudService<AirLinkOrderDao, AirLinkOrder> {

    private final MarketAirLinkSettingDao airLinkMarketSettingDao;
    private final AirLinkVoucherDao airLinkVoucherDao;
    private final AirLinkWarningDao airLinkWarningDao;
    private final AirLinkOrderItemDao airLinkOrderItemDao;
    private final AirLinkOrderNumberCacheService airLinkOrderNumberCacheService;
    private final MarketBillingForZolonService marketBillingForZolonService;

    private static final String DATE_FORMAT = "yyyyMMdd";

    public AirLinkOrder getByOrderType(Long orderId,AirLinkOrderType orderType) {
        return dao.getByOrderType(orderId,orderType);
    }
    @MasterDs
    public void deleteHistoryWarningByMarketIdAndPeriod(Long marketId,  String period){
        airLinkWarningDao.deleteByMarketIdAndPeriod(marketId,period);
    }

    @Transactional
    public void  recharge(AirLinkOrder airLinkOrder, ZolonBillingAirLinkOrderRequest zolonBillingAirLinkOrderRequest, List<AirLinkVoucher> airLinkVouchers){
        String currentOrderNumber = getCurrentOrderNumber(AirLinkOrderType.PREPAID_RECHARGE);
        airLinkOrder.setOrderNumber(currentOrderNumber);
        zolonBillingAirLinkOrderRequest.setOrderNumber(currentOrderNumber);
        marketBillingForZolonService.createAirLinkOrder(zolonBillingAirLinkOrderRequest);
        save(airLinkOrder);
        airLinkVouchers.forEach(
                airLinkVoucher -> airLinkVoucher.setOrderId(airLinkOrder.getId())
        );
        airLinkVoucherDao.batchInsertVoucherList(airLinkVouchers);
    }

    public Page<AirLinkOrder> findOrderList(Page<AirLinkOrder> page, AirLinkOrderQuery airLinkOrderQuery)  {
        airLinkOrderQuery.setPage(page);
        page.setList(dao.findOrderList(airLinkOrderQuery));
        return page;
    }

    public boolean firstRecharge(Long marketId){
        AirLinkOrder pendingOrder = dao.getChargeOrderByMarketIdAndStatus(marketId, AirLinkOrderType.PREPAID_RECHARGE, AirLinkOrderStatus.COMPLETED);
        return pendingOrder == null;
    }

    public boolean existedPendingChargeOrder(Long marketId){
        AirLinkOrder pendingOrder = dao.getChargeOrderByMarketIdAndStatus(marketId, AirLinkOrderType.PREPAID_RECHARGE, AirLinkOrderStatus.PENDING);
        return pendingOrder != null;
    }

    public AirLinkOrder getByOrderNumber(String orderNumber){
        return dao.getByOrderNumber(orderNumber);
    }

    @Transactional
    public void approveAirLinkCharge(AirLinkOrder airLinkOrder) {
        MarketAirLinkSetting marketAirLinkSetting = airLinkMarketSettingDao.getByMarketId(airLinkOrder.getMarketId());
        BigDecimal marketBalance = marketAirLinkSetting.getBalance();
        if (marketBalance == null){
            marketBalance = airLinkOrder.getAmount();
        }else{
            marketBalance = marketBalance.add(airLinkOrder.getAmount());
        }
        airLinkOrder.setRemainBalance(marketBalance);
        //修改审核状态
        dao.auditCharge(airLinkOrder);

        //更新余额和首次充值时间
        Date chargeDate = marketAirLinkSetting.getChargeDate();
        if (chargeDate == null){
            chargeDate = new Date();
        }
        airLinkMarketSettingDao.updateMarketBalanceAndChargeDate(marketAirLinkSetting.getId(), airLinkOrder.getAmount(), chargeDate);
    }

    @Transactional
    public boolean deductCurrentMonthlyFee(MarketAirLinkSetting marketAirLinkSetting, BigDecimal price, String period,
                                           Long currentActiveTerminalCount, AirLinkDeductType deductType){
        AirLinkOrder airLinkOrder = deductFee(marketAirLinkSetting, currentActiveTerminalCount, price, period, deductType);
        AirLinkOrderItem orderItem = new AirLinkOrderItem();
        orderItem.setOrderId(airLinkOrder.getId());
        orderItem.setType(AirLinkOrderItemType.MONTHLY);
        orderItem.setQuantity(currentActiveTerminalCount);
        orderItem.setPrice(price);
        orderItem.setAmount(airLinkOrder.getAmount());
        airLinkOrderItemDao.insert(orderItem);
        airLinkMarketSettingDao.updateMarketBalance(marketAirLinkSetting.getId(),
                airLinkOrder.getAmount().subtract(airLinkOrder.getDiscountAmount()), airLinkOrder.getDiscountAmount());
        return airLinkOrder.getRemainBalance().compareTo(BigDecimal.ZERO) < 0;
    }

    @Transactional
    public boolean deductOverageTrafficFee(MarketAirLinkSetting marketAirLinkSetting, BigDecimal number, String period, AirLinkUsageInfo usageInfo){
        long roundNumber = number.setScale(0, RoundingMode.CEILING).longValue();
        AirLinkOrder airLinkOrder = deductFee(marketAirLinkSetting, roundNumber, usageInfo.getOveragePrice(), period, AirLinkDeductType.TRAFFIC_OVER);
        AirLinkOrderItem orderItem = new AirLinkOrderItem();
        orderItem.setOrderId(airLinkOrder.getId());
        orderItem.setType(AirLinkOrderItemType.TRAFFIC_OVER);
        orderItem.setQuantity(roundNumber);
        orderItem.setTrafficOverage(number);
        orderItem.setPrice(usageInfo.getOveragePrice());
        orderItem.setAmount(airLinkOrder.getAmount());
        airLinkOrderItemDao.insert(orderItem);
        airLinkMarketSettingDao.updateMarketBalance(marketAirLinkSetting.getId(),
                airLinkOrder.getAmount().subtract(airLinkOrder.getDiscountAmount()), airLinkOrder.getDiscountAmount());
        return airLinkOrder.getRemainBalance().compareTo(BigDecimal.ZERO) < 0;
    }

    private AirLinkOrder deductFee(MarketAirLinkSetting marketAirLinkSetting, long number, BigDecimal price,
                                   String period, AirLinkDeductType deductType){
        BigDecimal marketBalance = marketAirLinkSetting.getBalance() == null ? BigDecimal.ZERO : marketAirLinkSetting.getBalance();
        BigDecimal trialBalance = marketAirLinkSetting.getTrialBalance() == null ? BigDecimal.ZERO : marketAirLinkSetting.getTrialBalance();
        BigDecimal fee = BigDecimal.valueOf(number).multiply(price).setScale(2, RoundingMode.HALF_UP);
        BigDecimal remainBalance = marketBalance.add(trialBalance).subtract(fee);
        //计算优惠金额
        BigDecimal discountAmount;
        if (trialBalance.compareTo(BigDecimal.ZERO) <= 0){
            discountAmount = BigDecimal.ZERO;
        }else{
            if (trialBalance.compareTo(fee) >= 0){
                discountAmount = fee;
            }else{
                discountAmount = trialBalance;
            }
        }

        Date nowDate = new Date();
        AirLinkOrder saveOrder = new AirLinkOrder();
        saveOrder.setMarketId(marketAirLinkSetting.getMarketId());
        saveOrder.setAmount(fee);
        saveOrder.setDiscountAmount(discountAmount);
        saveOrder.setRemainBalance(remainBalance);
        saveOrder.setOrderNumber(getDeductOrderNumber(nowDate));
        saveOrder.setOrderType(AirLinkOrderType.DEDUCTION);
        saveOrder.setDeductType(deductType);
        saveOrder.setPaymentMethod(AirLinkPaymentMethod.BALANCE);
        saveOrder.setPaymentTime(nowDate);
        saveOrder.setPeriod(period);
        saveOrder.setStatus(AirLinkOrderStatus.COMPLETED);
        saveOrder.setCreatedDate(nowDate);
        saveOrder.setCreatedBy(getCurrentUser());
        saveOrder.setUpdatedDate(nowDate);
        saveOrder.setUpdatedBy(getCurrentUser());
        dao.insert(saveOrder);
        return saveOrder;
    }

    @MasterDs
    public void rejectAirLinkCharge(AirLinkOrder airLinkOrder){
        dao.auditCharge(airLinkOrder);
    }

    @MasterDs
    public String getCurrentOrderNumber(AirLinkOrderType type){
        String lastOrderNumber = dao.getLastOrderNumber(type);
        int sortNo = 1;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String nowDate = dateFormat.format(new Date());
        if (lastOrderNumber != null){
            int lastSortNo = Integer.parseInt(lastOrderNumber.substring(lastOrderNumber.length() - 6));
            if (lastSortNo < 999999){
                sortNo = lastSortNo + 1;
            }
        }
        return String.format("%s%s%06d", type.getCode(), nowDate, sortNo);
    }

    public String getLastChargeCode(Long marketId){
        return dao.getCurrentIdentifyCode(marketId, AirLinkOrderType.PREPAID_RECHARGE, AirLinkOrderStatus.COMPLETED);
    }

    private String getDeductOrderNumber(Date date){
        Long airlinkOrderNumber = airLinkOrderNumberCacheService.getAirlinkDeductOrderNumber();
        String nowDate = DateUtils.formatDate(date, DATE_FORMAT);
        return String.format("%s%s%06d", AirLinkOrderType.DEDUCTION.getCode(), nowDate, airlinkOrderNumber);
    }

    @MasterDs
    public String getCurrentChargeCode(Long marketId, String domain){
        String currentIdentifyCode = dao.getCurrentIdentifyCode(marketId, AirLinkOrderType.PREPAID_RECHARGE, AirLinkOrderStatus.COMPLETED);
        int sortNo = 1;
        String year = DateUtils.getYear(new Date());
        if (currentIdentifyCode != null){
            int lastSortNo = Integer.parseInt(currentIdentifyCode.substring(currentIdentifyCode.length() - 6));
            if (lastSortNo < 999999){
                sortNo = lastSortNo + 1;
            }
        }
        return String.format("%s%s%s%06d", "AL", domain, year, sortNo);
    }


    @Transactional
    public boolean deductTerminalActiveFee(MarketAirLinkSetting marketAirLinkSetting, BigDecimal monthPrice, BigDecimal activeFee,
                                        String period, Long currentActiveTerminalCount, AirLinkDeductType deductType) {
        AirLinkOrder airLinkOrder = deductFee(marketAirLinkSetting, currentActiveTerminalCount, monthPrice.add(activeFee), period, deductType);
        List<AirLinkOrderItem> itemList = new ArrayList<>();
        BigDecimal decimalTerminalCount = BigDecimal.valueOf(currentActiveTerminalCount);
        BigDecimal totalMonthFee = decimalTerminalCount.multiply(monthPrice);
        AirLinkOrderItem monthItem = new AirLinkOrderItem();
        monthItem.setOrderId(airLinkOrder.getId());
        monthItem.setType(AirLinkOrderItemType.MONTHLY);
        monthItem.setQuantity(currentActiveTerminalCount);
        monthItem.setPrice(monthPrice);
        monthItem.setAmount(totalMonthFee);
        BigDecimal totalActiveFee = decimalTerminalCount.multiply(activeFee);
        AirLinkOrderItem activeItem = new AirLinkOrderItem();
        activeItem.setOrderId(airLinkOrder.getId());
        activeItem.setType(AirLinkOrderItemType.PROFILE_DOWNLOAD);
        activeItem.setQuantity(currentActiveTerminalCount);
        activeItem.setPrice(activeFee);
        activeItem.setAmount(totalActiveFee);
        itemList.add(monthItem);
        itemList.add(activeItem);
        airLinkOrderItemDao.insertList(itemList);
        airLinkMarketSettingDao.updateMarketBalance(marketAirLinkSetting.getId(),
                airLinkOrder.getAmount().subtract(airLinkOrder.getDiscountAmount()), airLinkOrder.getDiscountAmount());
        return airLinkOrder.getRemainBalance().compareTo(BigDecimal.ZERO) < 0;
    }

    public AirLinkOrder getDeductOrderByPeriod(Long marketId, String deductType, String period) {
        return dao.getDeductOrderByPeriod(marketId, deductType, period);
    }
}
