package com.paxstore.global.domain.service.vas.airlink;

import com.pax.market.domain.entity.global.vas.airlink.AirLinkVoucher;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.vas.airlink.AirLinkVoucherDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Author: 李志祥
 * @Date: 2025/1/20 14:25
 **/
@Service
@RequiredArgsConstructor
public class AirLinkVoucherService extends CrudService<AirLinkVoucherDao, AirLinkVoucher> {
    private final AirLinkVoucherDao airLinkVoucherDao;

    public List<AirLinkVoucher> getAirLinkVoucherList(Long orderId) {
        return dao.getVoucherList(orderId);
    }
    @MasterDs
    public void batchInsertVoucherList(List<AirLinkVoucher> airLinkVouchers) {
        if (CollectionUtils.isNotEmpty(airLinkVouchers)) {
            airLinkVoucherDao.batchInsertVoucherList(airLinkVouchers);
        }
    }

}
