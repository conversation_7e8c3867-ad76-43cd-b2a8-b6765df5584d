package com.paxstore.global.domain.dao.vas.appscan;

import com.pax.market.domain.entity.global.vas.appscan.Market2EngineSetting;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2021/1/19 13:55
 */
@MyBatisDao
public interface Market2EngineSettingDao extends CrudDao<Market2EngineSetting> {

    Market2EngineSetting getSettingByMarketId(@Param("marketId") Long marketId);
}
