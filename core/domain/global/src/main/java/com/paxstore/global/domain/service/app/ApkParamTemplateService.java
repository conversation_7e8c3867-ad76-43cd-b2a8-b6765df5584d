/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.service.app;

import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.query.DeleteApkParamTemplateRequest;
import com.pax.market.domain.util.BizLimitCacheHelper;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventAccumulator;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.app.ApkParamTemplateDao;
import com.paxstore.global.domain.utils.ApkParamTemplateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent.EventType.APK_PARAMETER_TEMPLATE_CHANGED;

/**
 * The type Apk param template service.
 */
@Service
public class ApkParamTemplateService extends CrudService<ApkParamTemplateDao, ApkParamTemplate> {
    //一次最多允许选择多少个参数模板
    private static final int MAX_APK_PARAM_TEMPLATE = 10;
    @Autowired
    private ApkService apkService;

    /**
     * Add param templates.
     *
     * @param apkId             the apk id
     * @param apkParamTemplates the apk param templates
     * @param checkLimit        the check limit
     */
    @MasterDs
    public void insertParamTemplates(Long apkId, List<ApkParamTemplate> apkParamTemplates,boolean checkLimit) {
        if (apkParamTemplates == null || apkParamTemplates.isEmpty()) {
            return;
        }
        if(checkLimit) {
            apkParamTemplates.get(0).setApkId(apkId);
            BizLimitCacheHelper.checkBizLimit(this, apkParamTemplates.get(0), apkParamTemplates.size());
        }

        apkParamTemplates.forEach(each -> {
            if (StringUtils.isNotEmpty(each.getParamTemplate())) {
                each.setParamTemplateUrl(FileUploader.uploadFile(each.getParamTemplate().getBytes(), "paramtpl", ".xml", SupportedFileTypes.APP_PARAM));
            }
            if (StringUtils.isEmpty(each.getParamTemplateUrl())) {
                throw new BusinessException(ApiCodes.APK_PARAM_DATA_FILE_NOT_FOUND);
            }
        });
        Collections.reverse(apkParamTemplates);
        dao.insertParamTemplates(apkId, apkParamTemplates);
        if(checkLimit) {
            //更新缓存
            BizLimitCacheHelper.updateBizCount(apkParamTemplates.get(0), apkParamTemplates.size());
        }
        EntityChangedEventAccumulator.add(APK_PARAMETER_TEMPLATE_CHANGED, apkId);

    }

    /**
     * Update param template.
     *
     * @param apkParamTemplate the apk param template
     */
    @MasterDs
    public void updateParamTemplate(ApkParamTemplate apkParamTemplate) {
        dao.updateParamTemplateParam(apkParamTemplate);
    }

    /**
     * Gets param template list.
     *
     * @param apkIdList the apkIdList
     * @return the param template list
     */
    public List<String> findParamTemplateNameList(List<Long> apkIdList) {
        List<ApkParamTemplate> paramTemplateNameList = dao.findParamTemplateNameList(apkIdList);
        if (CollectionUtils.isNotEmpty(paramTemplateNameList)) {
            return paramTemplateNameList.stream().map(ApkParamTemplate::getName).toList();
        }
        return List.of();
    }

    /**
     * Find param template list
     *
     * @param apkIdList the apk id list
     * @return the list
     */
    public List<ApkParamTemplate> findParamTemplateList(List<Long> apkIdList) {
        return dao.findParamTemplateNameList(apkIdList);
    }

    /**
     * Find param template list by ids
     *
     * @param ids the ids
     * @return the list
     */
    public List<ApkParamTemplate> findParamTemplateListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return dao.findParamTemplateListByIds(ids);
    }

    @MasterDs
    public List<String> findParamTemplateNameListWithMasterDs(List<Long> apkIdList) {
        return findParamTemplateNameList(apkIdList);
    }

    /**
     * Find param template  list.
     *
     * @param apkId the apk id
     * @return the list
     */
    public List<ApkParamTemplate> findParamTemplateListIncludeDelete(Long apkId) {
        return dao.findParamTemplateList(apkId, false,null, null,null);
    }

    public List<ApkParamTemplate> findParamTemplateList(Long apkId, Boolean excludeUpdate, List<String> paramTemplateNames) {
        return dao.findParamTemplateList(apkId, true, excludeUpdate, paramTemplateNames, null);
    }

    public List<Long> findParamTemplateIdList(Long apkId, String paramTemplateName) {
        return dao.findParamTemplateIdList(apkId, StringUtils.splitToList(paramTemplateName, SystemConstants.FILE_NAME_DELIMITER));
    }

    /**
     * Gets param template.
     *
     * @param apkId             the apk id
     * @param paramTemplateName the param template name
     * @return the combined param template
     */
    public ApkParamTemplate getParamTemplate(Long apkId, String paramTemplateName) {
        List<ApkParamTemplate> apkParamTemplateList = getApkParamTemplateList(apkId, paramTemplateName, null);
        return ApkParamTemplateUtils.combineApkParamTemplates(apkParamTemplateList, apkId, paramTemplateName);
    }

    /**
     * Gets param template by id
     *
     * @param apkId             the apk id
     * @param paramTemplateName the param template name
     * @return the combined param template
     */
    public ApkParamTemplate getParamTemplate(Long apkId, String paramTemplateName, String paramTemplateIds) {
        List<ApkParamTemplate> apkParamTemplateList = getApkParamTemplateList(apkId, paramTemplateName, StringUtils.splitToLongList(paramTemplateIds, ","));
        return ApkParamTemplateUtils.combineApkParamTemplates(apkParamTemplateList, apkId, paramTemplateName);
    }

    /**
     * Gets param template exclude delete
     *
     * @param apkId             the apk id
     * @param paramTemplateName the param template name
     * @return the combined param template
     */
    public ApkParamTemplate getParamTemplateExcludeDelete(Long apkId, String paramTemplateName) {
        if (StringUtils.isEmpty(paramTemplateName)) {
            throw new BusinessException(ApiCodes.APK_PARAM_TEMPLATE_NOT_FOUND);
        }
        //如果选择的是多个模板，需要合并多个模板的数据
        List<String> paramTemplateNameList = StringUtils.splitToList(paramTemplateName, SystemConstants.FILE_NAME_DELIMITER);
        List<ApkParamTemplate> apkParamTemplateList = dao.findParamTemplateList(apkId, true, false, paramTemplateNameList, null);
        validateParamTemplate(apkId, paramTemplateName, apkParamTemplateList, paramTemplateNameList);
        return ApkParamTemplateUtils.combineApkParamTemplates(apkParamTemplateList, apkId, paramTemplateName);
    }

    /**
     * 获取单个模板数据，可能返回为NULL
     *
     * @param apkId             the apk id
     * @param paramTemplateName 单个模板名字
     * @return the param template
     */
    public ApkParamTemplate getSingleParamTemplate(Long apkId, String paramTemplateName) {
        ApkParamTemplate apkParamTemplate = dao.getParamTemplate(apkId, paramTemplateName);
        ApkParamTemplateUtils.loadApkParamTemplateData(apkParamTemplate, apkId, paramTemplateName);
        return apkParamTemplate;
    }

    /**
     * 获取合并后的单个模板数据，可能返回为NULL
     *
     * @param apkIdList         the apk id list
     * @param paramTemplateName 单个模板名字
     * @return the param template
     */
    public ApkParamTemplate getSingleParamTemplate(List<Long> apkIdList, String paramTemplateName) {
        List<ApkParamTemplate> apkParamTemplateList = new ArrayList<>();
        for (Long apkId : apkIdList) {
            ApkParamTemplate apkParamTemplate = dao.getParamTemplate(apkId, paramTemplateName);
            if (apkParamTemplate != null) {
                ApkParamTemplateUtils.loadApkParamTemplateData(apkParamTemplate, apkId, paramTemplateName);
                apkParamTemplateList.add(apkParamTemplate);
            }
        }
        if (apkParamTemplateList.isEmpty()) {
            return null;
        }
        return ApkParamTemplateUtils.combineApkParamTemplates(apkParamTemplateList, -1L, paramTemplateName);
    }

    /**
     * Gets apk param template list.
     *
     * @param apkId             the apk id
     * @param paramTemplateName the param template name
     * @return the apk param template list
     */
    public List<ApkParamTemplate> getApkParamTemplateList(Long apkId, String paramTemplateName, List<Long> paramTemplateIds) {
        if (StringUtils.isEmpty(paramTemplateName)) {
            throw new BusinessException(ApiCodes.APK_PARAM_TEMPLATE_NOT_FOUND);
        }

        //如果选择的是多个模板，需要合并多个模板的数据
        List<String> paramTemplateNameList = StringUtils.splitToList(paramTemplateName, SystemConstants.FILE_NAME_DELIMITER);
        List<ApkParamTemplate> apkParamTemplateList = dao.findParamTemplateList(apkId, false, false, paramTemplateNameList, paramTemplateIds);

        validateParamTemplate(apkId, paramTemplateName, apkParamTemplateList, paramTemplateNameList);

        return apkParamTemplateList;
    }

    private void validateParamTemplate(Long apkId, String paramTemplateName, List<ApkParamTemplate> apkParamTemplateList, List<String> paramTemplateNameList) {
        if (apkParamTemplateList.size() < paramTemplateNameList.size()) {
            Apk apk = apkService.get(apkId);
            throw new BusinessException(ApiCodes.APK_PARAM_TEMPLATE_NOT_FOUND_IN_APP, null, paramTemplateName, apk.getAppName(), apk.getVersionName());
        } else if (apkParamTemplateList.size() > MAX_APK_PARAM_TEMPLATE) {
            throw new BusinessException(ApiCodes.MAX_PARAM_TEMPLATE_EXCEEDED);
        }
    }

    /**
     * Is param template exist boolean.
     *
     * @param apk               the apk
     * @param paramTemplateName the param template name
     * @return the boolean
     */
    public void checkParamTemplateExist(Apk apk, String paramTemplateName) {
        List<String> paramTemplateNameList = StringUtils.splitToList(paramTemplateName, SystemConstants.FILE_NAME_DELIMITER);
        for (String each : paramTemplateNameList) {
            Boolean isParamTemplateExist = dao.isParamTemplateExist(apk.getId(), each);
            if (isParamTemplateExist == null || !isParamTemplateExist) {
                throw new BusinessException(ApiCodes.APK_PARAM_TEMPLATE_NAME_NOT_FOUND, null, each);
            }
        }
    }

    /**
     * Delete param template.
     *
     * @param apk               the apk
     * @param apkParamTemplate the apk param template
     * @param alreadyUsed       the already used
     */
    @MasterDs
    public void deleteParamTemplate(Apk apk, ApkParamTemplate apkParamTemplate, boolean alreadyUsed) {
        if (dao.getParamTemplateCount(apk.getId()) <= 1) {
            throw new BusinessException(ApiCodes.APK_BASE_TYPE_PAYMENT_PARAM_TEMPLATE_MANDATORY);
        }
        DeleteApkParamTemplateRequest request = DeleteApkParamTemplateRequest.builder()
                .apkId(apk.getId())
                .paramTemplateId(Long.valueOf(apkParamTemplate.getParamTemplateId()))
                .delFlag(1L)
                .build();
        if (alreadyUsed) {
            dao.deleteParamTemplate(request);
        } else {
            dao.physicalDeleteParamTemplate(request);
        }
        BizLimitCacheHelper.updateBizCount(apkParamTemplate, -1);
    }

    @MasterDs
    public void deleteParamTemplate(Long apkId, Long paramTemplateId) {
        DeleteApkParamTemplateRequest request = DeleteApkParamTemplateRequest.builder()
                .apkId(apkId)
                .delFlag(paramTemplateId)
                .paramTemplateId(paramTemplateId)
                .build();
        dao.deleteParamTemplate(request);
    }

    /**
     * Delete param template by apk id.
     *
     * @param apkId the apk id
     */
    @MasterDs
    public void deleteParamTemplateByApkId(Long apkId) {
        //同版本更新-删除需要包括已删除的
        List<ApkParamTemplate> apkParamTemplateList = findParamTemplateListIncludeDelete(apkId);
        if (CollectionUtils.isEmpty(apkParamTemplateList)) {
            return;
        }
        dao.deleteParamTemplateByApkId(apkId);
        BizLimitCacheHelper.updateBizCount(apkParamTemplateList.get(0), -apkParamTemplateList.size());
    }

    @MasterDs
    public void physicalDeleteParamTemplate(Long apkId, String paramTemplateName) {
        ApkParamTemplate apkParamTemplate = dao.getParamTemplate(apkId, paramTemplateName);
        if (apkParamTemplate == null) return;

        DeleteApkParamTemplateRequest request = DeleteApkParamTemplateRequest.builder()
                .apkId(apkId)
                .paramTemplateId(apkParamTemplate.getId())
                .build();
        dao.physicalDeleteParamTemplate(request);

        BizLimitCacheHelper.updateBizCount(apkParamTemplate, -1);
    }

    public ApkParamTemplate paramTemplateExist(Long apkId, String paramTemplateName) {
        List<ApkParamTemplate> apkParamTemplateList = getApkParamTemplateList(apkId, paramTemplateName, null);
        return ApkParamTemplateUtils.combinedApkParamTemplates(apkParamTemplateList, apkId, paramTemplateName);
    }
}