/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.dao.market;

import com.pax.market.domain.entity.market.thirdparty.MarketThirdPartySystemIp;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;


/**
 * The interface Market third party system ip dao.
 *
 * <AUTHOR>
 */
@MyBatisDao
public interface MarketThirdPartySystemIpDao extends CrudDao<MarketThirdPartySystemIp> {

    Integer getCount(MarketThirdPartySystemIp systemIp);
}
