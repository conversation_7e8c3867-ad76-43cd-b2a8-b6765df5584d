/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.dao.agreement;

import com.pax.market.domain.entity.global.agreement.extend.VasAgreementMapExtend;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

/**
 * <AUTHOR>
 * @date 2021/8/23
 */
@MyBatisDao
public interface VasAgreementMapDao extends CrudDao<VasAgreementMapExtend> {


}
