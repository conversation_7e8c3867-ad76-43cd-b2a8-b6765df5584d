package com.paxstore.global.domain.dao.vas;

import com.pax.market.domain.entity.global.vas.CyberLabTerminalBlacklist;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 9.2
 */
@MyBatisDao
public interface CyberLabTerminalBlacklistDao extends CrudDao<CyberLabTerminalBlacklist> {

    String existsSerialNo(@Param("serialNos") List<String> serialNos);

    List<String> findSerialNos();

    int getCount();
}
