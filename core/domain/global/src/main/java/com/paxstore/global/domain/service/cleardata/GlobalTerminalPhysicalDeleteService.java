package com.paxstore.global.domain.service.cleardata;

import com.google.common.collect.Lists;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.cleardata.GlobalTerminalPhysicalDeleteDao;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GlobalTerminalPhysicalDeleteService extends CrudService<GlobalTerminalPhysicalDeleteDao, Terminal> {

    private final static List<String> terminalRelatedTables = Lists.newArrayList(
            "pax_client_apk_download_history",
            "pax_package_terminal_mapping",
            "pax_purchased_app",
            "pax_push_history",
            "pax_subscribed_app"
    );

    @MasterDs
    public void deleteTerminal(Long terminalId) {
        terminalRelatedTables.forEach(relatedTableName -> dao.deleteFromTerminalRelatedTable(relatedTableName, terminalId));
    }
}
