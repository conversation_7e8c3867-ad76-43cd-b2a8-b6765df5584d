package com.paxstore.global.domain.service.emm;

import com.pax.api.cache.CacheService;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.entity.global.emm.EmmModel;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.emm.EmmModelDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/11
 */
@Service
@RequiredArgsConstructor
public class EmmModelService extends CrudService<EmmModelDao, EmmModel> {

    private static final String MODEL_CACHE_ID_ = "id_";

    private final CacheService cacheService;

    public EmmModel getByNameAndMfrName(String name, String mfrName) {
        return dao.getByNameAndMfrName(name, mfrName);
    }

    public List<EmmModel> findListByIds(List<Long> ids){
        if(CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        EmmModel emmModel = new EmmModel();
        emmModel.setIdsFilter(ids);
        return dao.findListByIds(emmModel);
    }

    @MasterDs
    public EmmModel get(Long id) {
        return get(new EmmModel(id));
    }

    @MasterDs
    public EmmModel get(EmmModel model) {
        if (model == null || LongUtils.isBlankOrNotPositive(model.getId())) {
            return null;
        }

        EmmModel result = (EmmModel) cacheService.get(CacheNames.EMM_MODEL_CACHE, MODEL_CACHE_ID_ + model.getId());
        if (result == null) {
            result = dao.get(new EmmModel(model.getId()));
            if (result == null) {
                return null;
            }
            cacheService.put(CacheNames.EMM_MODEL_CACHE, MODEL_CACHE_ID_ + model.getId(), result);
        }

        return result;
    }

}
