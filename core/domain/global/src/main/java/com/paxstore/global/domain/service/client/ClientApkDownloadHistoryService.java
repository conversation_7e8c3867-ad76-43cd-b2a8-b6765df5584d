/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.service.client;

import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.client.ClientApkDownloadHistoryDao;
import com.pax.market.domain.entity.global.client.ClientApkDownloadHistory;
import com.pax.market.framework.common.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * The type Client apk download history service.
 */
@Service
public class ClientApkDownloadHistoryService extends BaseService {

    @Autowired
    private ClientApkDownloadHistoryDao dao;

    /**
     * Save client download history.
     *
     * @param clientApkId  the client apk id
     * @param marketId     the market id
     * @param terminalId   the terminal id
     * @param downloadTime the download time
     */
    @MasterDs
    public void saveClientDownloadHistory(Long clientApkId, Long marketId, Long terminalId, Date downloadTime) {
        ClientApkDownloadHistory clientApkDownloadHistory = dao.getClientDownloadHistory(clientApkId, marketId, terminalId);
        if (clientApkDownloadHistory == null) {
            clientApkDownloadHistory = new ClientApkDownloadHistory();
            clientApkDownloadHistory.setApkId(clientApkId);
            clientApkDownloadHistory.setMarketId(marketId);
            clientApkDownloadHistory.setTerminalId(terminalId);
            clientApkDownloadHistory.setDownloadTime(downloadTime);
            dao.createClientDownloadHistory(clientApkDownloadHistory);
        } else {
            clientApkDownloadHistory.setDownloadTime(downloadTime);
            dao.updateClientDownloadHistory(clientApkDownloadHistory);
        }
    }

    /**
     * Gets client downloaded terminal count.
     *
     * @param clientApkId the client apk id
     * @param marketId    the market id
     * @return the client downloaded terminal count
     */
    public int getClientDownloadedTerminalCount(Long clientApkId, Long marketId) {
        return dao.getClientDownloadedTerminalCount(clientApkId, marketId);
    }
}
