/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.dao.app;

import com.pax.market.domain.entity.global.app.*;
import com.pax.market.domain.entity.market.app.ApkParameter;
import com.pax.market.domain.entity.report.ApkVersionOfTerminalReport;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;
import java.util.Set;

/**
 * 应用APK DAO接口
 *
 * <AUTHOR>
 * @version 2016 -05-17
 */
@MyBatisDao
public interface ApkDao extends CrudDao<Apk> {

    /**
     * Find all list.
     *
     * @param apk     the apk
     * @param handler the handler
     */
    void findAllList(Apk apk, ResultHandler<Apk> handler);

    /**
     * Gets latest apk id by app id.
     *
     * @param appId the app id
     * @return the latest apk id by app id
     */
    Long getLatestOnlineOfflineApkIdByAppId(@Param("appId") Long appId);


    /**
     * Gets latest deleted apk id by app id.
     *
     * @param appId the app id
     * @return the latest deleted apk id by app id
     */
    Long getLatestDeletedOnlineOfflineApkIdByAppId(@Param("appId") Long appId);

    /**
     * Gets latest apk id by app id.
     *
     * @param appId the app id
     * @return the latest apk id by app id
     */
    Long getLatestApkIdByAppId(@Param("appId") Long appId);

    /**
     * Gets latest apk id.
     *
     * @param appApkQuery the app apk query
     * @return the latest apk id
     */
    Long getLatestApkId(AppApkQuery appApkQuery);

    /**
     * Gets pending apk id.
     *
     * @param appApkQuery the app apk query
     * @return the latest apk id
     */
    Long getLatestPendingApkId(AppApkQuery appApkQuery);

    /**
     * get latest offline apk id
     *
     * @param appApkQuery the appApkQuery
     * @return the latest offline apk id
     */
    Long getLatestOfflineApkId(AppApkQuery appApkQuery);

    /**
     * Gets latest apk id for vas.
     *
     * @param appId    the app id
     * @param marketId the market id
     * @return the latest apk id for vas
     */
    Long getLatestApkIdForVas(@Param("appId") Long appId, @Param("marketId") Long marketId);

    /**
     * Gets latest deleted apk id
     *
     * @param appApkQuery the app apk query
     * @return latest deleted apk id
     */
    Long getLatestDeletedApkId(AppApkQuery appApkQuery);

    /**
     * Find apk list list.
     *
     * @param appApkQuery the app apk query
     * @return the list
     */
    List<Apk> findList(AppApkQuery appApkQuery);


    /**
     * Gets count.
     *
     * @param appApkQuery the app apk query
     * @return the count
     */
    Integer getCount(AppApkQuery appApkQuery);

    /**
     * Find apk list list.
     *
     * @param appApkQuery the app apk query
     * @return the list
     */
    List<Apk> findSignFailedApkList(AppApkQuery appApkQuery);


    /**
     * Gets count.
     *
     * @param appApkQuery the app apk query
     * @return the count
     */
    Integer getSignFailedApkCount(AppApkQuery appApkQuery);

    /**
     * Is file id used in app boolean.
     *
     * @param app          the app
     * @param fileId       the file id
     * @param excludeApkId the exclude apk id
     * @return the boolean
     */
    Boolean isFileIdUsedAsIconScreenshotInApk(@Param("appId") Long app, @Param("fileId") String fileId, @Param("excludeApkId") Long excludeApkId);

    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_STATUS_CHANGED)
    int update(Apk apk);

    /**
     * Update version code.
     *
     * @param apk the apk
     */
    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_STATUS_CHANGED)
    void updateApkFileInfo(Apk apk);

    /**
     * Update status request info
     *
     * @param apk the apk
     */
    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_STATUS_CHANGED)
    void updateStatusRequestInfo(Apk apk);

    /**
     * Update apk type and param file map.
     *
     * @param apk the apk
     */
    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_STATUS_CHANGED)
    void updateApkTypeAndParamFileMap(Apk apk);

    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_STATUS_CHANGED)
    int delete(Long id);

    /**
     * Is apk visible boolean.
     *
     * @param appApkQuery the app apk query
     * @return the boolean
     */
    Boolean isApkVisibleToCurrentUser(AppApkQuery appApkQuery);

    /**
     * Is apk available boolean.
     *
     * @param appApkQuery the app apk query
     * @return the boolean
     */
    Boolean isApkAvailable(AppApkQuery appApkQuery);

    /**
     * Is apk available for notification verification.
     *
     * @param appApkQuery the app apk query
     * @return the boolean
     */
    Boolean isApkAvailable4Ntf(AppApkQuery appApkQuery);

    /**
     * Update market apk.
     *
     * @param apk the apk
     */
    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_STATUS_CHANGED)
    void updateApkStatus(Apk apk);

    /**
     * find apkList
     *
     * @param appId the app id
     * @return apk list
     */
    List<Apk> getApkList(@Param("appId") Long appId);

    /**
     * Get complex apk(join pax_apk_file)
     *
     * @param apkId the apk id
     * @return the ComplexApk
     */
    ComplexApk getComplexApk(@Param("apkId") Long apkId);

    /**
     * Gets latest apk version code.
     *
     * @param app the app
     * @return the latest apk version code
     */
    Long getLatestApkVersionCode(App app);

    /**
     * Find signed market apk list list.
     *
     * @return the list
     */
    List<Apk> findAllAndroidApkList();

    /**
     * Get latest apk icon info by appId
     *
     * @param appId the app id
     * @return latest apk icon url info
     */
    String getLatestApkIconInfo(@Param("appId") Long appId);


    /**
     * get apk by package name and version code
     *
     * @param apk the apk
     * @return long long
     */
    Long getLatestOnlineOfflineApkIdByPackageNameAndVersion(Apk apk);

    /**
     * get app online apk count
     *
     * @param app the app
     * @return online apk count
     */
    Integer getOnlineApkCount(App app);

    /**
     * Gets max version online apk id.
     *
     * @param appId the app id
     * @return the max version online apk id
     */
    Long getMaxVersionOnlineApkId(@Param("appId") Long appId);


    /**
     * Find apk id list need online signature list.
     *
     * @param currentMarketId the current market id
     * @return the list
     */
    List<Long> findApkIdListNeedOnlineSignature(@Param("currentMarketId") Long currentMarketId);

    /**
     * Find all market apk list list.
     *
     * @param currentMarketId the current market id
     * @return the list
     */
    List<Apk> findAllMarketApkList(@Param("currentMarketId") Long currentMarketId);

    /**
     * Gets apk id by package name and version.
     *
     * @param currentMarketId the current market id
     * @param packageName     the package name
     * @param versionName     the version name
     * @return the apk id by package name and version
     */
    Long getApkIdByPackageNameAndVersion(@Param("currentMarketId") Long currentMarketId, @Param("packageName") String packageName, @Param("versionName") String versionName);

    /**
     * Is solution apk available boolean.
     *
     * @param id              the id
     * @param currentMarketId the current market id
     * @param modelId         the model id
     * @param onlyWhiteList   the only white list
     * @return the boolean
     */
    Boolean isSolutionApkAvailable(@Param("id") Long id,
                                   @Param("currentMarketId") Long currentMarketId,
                                   @Param("modelId") Long modelId,
                                   @Param("onlyWhiteList") Boolean onlyWhiteList);

    /**
     * Find all apk ids by package name and version name list.
     *
     * @param packageName the package name
     * @param versionName the version name
     * @return the list
     */
    List<Long> findApkIdsByPackageNameAndVersionName(@Param("marketId") Long marketId, @Param("packageName") String packageName, @Param("versionName") String versionName, @Param("excludeDeletedApp") boolean excludeDeletedApp);

    /**
     * Find app ids by app name list.
     *
     * @param apkParameter the apk parameter
     * @return the list
     */
    List<Long> findAppIdsByAppName(ApkParameter apkParameter);

    /**
     * Find available apk id list list.
     *
     * @param apkParameter the apk parameter
     * @return the list
     */
    List<Long> findAvailableApkIdList(ApkParameter apkParameter);

    /**
     * Is app of apk available boolean.
     *
     * @param apkId the apk id
     * @return the boolean
     */
    Boolean isAppOfApkAvailable(@Param("apkId") Long apkId);

    /**
     * Is apk available 4 param tpl boolean.
     *
     * @param apkParameter the apk parameter
     * @return the boolean
     */
    Boolean isApkAvailable4ApkParameter(ApkParameter apkParameter);

    /**
     * Find apk and app info by id apk.
     *
     * @param apkId the apk id
     * @return the apk
     */
    Apk findApkAndAppInfoById(@Param("id") Long apkId);

    /**
     * Find available solution apk id list list.
     *
     * @param apkParameter the apk parameter
     * @return the list
     */
    List<Long> findAvailableSolutionApkIdList(ApkParameter apkParameter);

    /**
     * Is apk available 4 solution boolean.
     *
     * @param apkParameter the apk parameter
     * @return the boolean
     */
    Boolean isApkAvailable4Solution(ApkParameter apkParameter);

    /**
     * Find apk ids by fuzzy package name set.
     *
     * @param packageName the package name
     * @return the set
     */
    Set<Long> findApkIdsByFuzzyPackageName(@Param("packageName") String packageName);

    /**
     * Find apk ids by fuzzy app name set.
     *
     * @param appName the app name
     * @return the set
     */
    Set<Long> findApkIdsByFuzzyAppName(@Param("appName") String appName);

    /**
     * Find apk ids by app id list.
     *
     * @param appId         the app id
     * @param includeDelete the include delete
     * @param versionCodes  the version codes
     * @return the list
     */
    List<Long> findApkIdsByAppId(@Param("appId") Long appId, @Param("includeDelete") Boolean includeDelete, @Param("versionCodes") Set<Long> versionCodes);

    /**
     * Find pushed parameter apk version list list.
     *
     * @param apkIdList the apk id list
     * @return the list
     */
    List<ApkVersionOfTerminalReport> findPushedParameterApkVersionList(@Param("apkIdList") List<Long> apkIdList);

    /**
     * Find pushed latest parameter apk list list.
     *
     * @param appApkQuery the app apk query
     * @return the list
     */
    List<Apk> findPushedLatestParameterApkList(AppApkQuery appApkQuery);

    /**
     * Gets apk id of max version.
     *
     * @param apkIdList the apk id list
     * @return the apk id of max version
     */
    Long getApkIdOfMaxVersion(Set<Long> apkIdList);

    /**
     * find apkVersion List
     *
     * @param appId the app id
     * @return apk
     */
    List<Apk> getApkVersionList(@Param("appId") Long appId);
}