/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.service.market;

import com.pax.market.domain.entity.market.thirdparty.MarketWhiteListAccessIp;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.paxstore.global.domain.dao.market.MarketWhiteListAccessIpDao;
import org.springframework.stereotype.Service;

/**
 * 市场配置白名单访问ip
 * <AUTHOR>
 * @date 2022/12/28
 */
@Service
public class MarketWhiteListAccessIpService extends CrudService<MarketWhiteListAccessIpDao, MarketWhiteListAccessIp>{


    public int getCount(MarketWhiteListAccessIp accessIp){
        return IntegerUtils.getValue(dao.getCount(accessIp));
    }

}