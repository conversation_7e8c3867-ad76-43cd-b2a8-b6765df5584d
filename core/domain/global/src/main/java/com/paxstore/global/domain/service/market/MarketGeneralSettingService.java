package com.paxstore.global.domain.service.market;

import com.pax.api.cache.CacheService;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.entity.global.market.MarketGeneralSetting;
import com.pax.market.domain.pushsub.MarketInfoChangedMessage;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.market.MarketGeneralSettingDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class MarketGeneralSettingService extends CrudService<MarketGeneralSettingDao, MarketGeneralSetting> {

    private static final String MARKET_CACHE_ID_ = "marketId_";

    private final CacheService cacheService;

    @MasterDs
    public MarketGeneralSetting getByMarketId(Long marketId) {
        MarketGeneralSetting marketGeneralSetting = (MarketGeneralSetting) cacheService.get(CacheNames.MARKET_GENERAL_SETTING_CACHE, MARKET_CACHE_ID_ + marketId);
        if (marketGeneralSetting == null) {
            marketGeneralSetting = dao.getByMarketId(marketId);
            if (marketGeneralSetting == null) {
                marketGeneralSetting = new MarketGeneralSetting();
            }
            cacheService.put(CacheNames.MARKET_GENERAL_SETTING_CACHE, MARKET_CACHE_ID_ + marketId, marketGeneralSetting);
        }
        return marketGeneralSetting;
    }

    @MasterDs
    public void update(MarketGeneralSetting marketGeneralSetting) {
        dao.update(marketGeneralSetting);
        cacheService.remove(CacheNames.MARKET_GENERAL_SETTING_CACHE, MARKET_CACHE_ID_ + marketGeneralSetting.getMarketId());
        pubMessage(new MarketInfoChangedMessage(marketGeneralSetting.getMarketId()));
    }

    public List<MarketGeneralSetting> findMarketSsoDomain(Long excludeMarketId) {
        return dao.findMarketSsoDomain(excludeMarketId);
    }
}
