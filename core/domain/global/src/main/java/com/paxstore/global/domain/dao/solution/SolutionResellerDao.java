package com.paxstore.global.domain.dao.solution;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.solution.SolutionReseller;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * solution app 定向发布代理商
 * <AUTHOR>
 * @create 2023/6/27
 */
@MyBatisDao
public interface SolutionResellerDao extends CrudDao<SolutionReseller> {



    List<Long> findSolutionResellerIdAllList(Reseller reseller);
    void insertSolutionResellerList(@Param("appId") Long appId,
                                    @Param("marketId") Long marketId,
                                    @Param("resellerIdList") List<Long> resellerIdList);
    void insertResellerSolutionList(@Param("marketId") Long marketId, @Param("resellerId") Long resellerId,@Param("appIds")List<Long> appIds);
    void deleteSolutionResellerList(@Param("appId") Long appId, @Param("resellerIds") List<Long> resellerIds);
    boolean isSolutionSpecificReseller(@Param("appId") Long appId, @Param("marketId") Long marketId, @Param("resellerId") Long resellerId);
    List<Long> findSolutionAppIdListBySpecificReseller(@Param("marketId") Long marketId, @Param("resellerId") Long resellerId);

}
