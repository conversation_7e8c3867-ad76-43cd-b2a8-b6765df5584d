/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.service.vas.airlauncher;

import com.pax.market.dto.vas.AirLauncherUsageInfo;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.service.BaseService;
import com.pax.market.framework.common.utils.LongUtils;
import com.paxstore.global.domain.dao.vas.airlauncher.TerminalInstalledLauncherDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * The type Terminal installed launcher service.
 */
@Service
public class TerminalInstalledLauncherService extends BaseService {
    @Autowired
    private TerminalInstalledLauncherDao dao;

    public List<AirLauncherUsageInfo> findFileList(String period) {
        return dao.findFileList(period);
    }

    /**
     * Find market history usage list.
     *
     * @param page
     * @param period
     * @param name
     * @return
     */

    public List<AirLauncherUsageInfo> findMarketHistoryUsage(Page page, String period, String name) {
        return dao.findMarketHistoryUsage(page, period, name);
    }

    /**
     * Find history usage by period list.
     *
     * @param page     the page
     * @param marketId the market id
     * @return the list
     */

    public List<AirLauncherUsageInfo> findHistoryUsageByPeriod(Page page, Long marketId) {
        return dao.findHistoryUsageByPeriod(page, marketId);
    }

    /**
     * Get launcher up file id string.
     *
     * @param marketId
     * @param period
     * @param globalOperation
     * @return
     */

    public String getLauncherUpFileId(Long marketId, String period, boolean globalOperation) {
        if (LongUtils.isBlankOrNotPositive(marketId) || StringUtils.isBlank(period)) {
            return "";
        }
        return dao.getLauncherUpFileId(marketId, period, globalOperation);
    }


}
