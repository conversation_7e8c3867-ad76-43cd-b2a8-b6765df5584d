/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.paxstore.global.domain.service.sandbox;


import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.sandbox.SandboxTerminalRegistry;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.sandbox.SandboxTerminalRegistryDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * The type Sandbox terminal registry service.
 *
 * <AUTHOR>
 * @date 2023 /7/17
 */
@Service
public class SandboxTerminalRegistryService extends CrudService<SandboxTerminalRegistryDao, SandboxTerminalRegistry> {
    @Override
    public SandboxTerminalRegistry get(Long id) {
        return get(new SandboxTerminalRegistry(id));
    }

    /**
     * Gets by serial no.
     *
     * @param sn the sn
     * @return the by serial no
     */
    public SandboxTerminalRegistry getBySerialNo(String sn) {
        return dao.getBySerialNo(sn);
    }

    /**
     * Update status.
     *
     * @param sandboxTerminalRegistry the sandbox terminal registry
     */
    @MasterDs
    public void updateStatus(SandboxTerminalRegistry sandboxTerminalRegistry) {
        dao.updateStatus(sandboxTerminalRegistry);
    }

    /**
     * Delete by market.
     *
     * @param marketId the market id
     */
    @MasterDs
    public void deleteByMarket(Long marketId) {
        dao.deleteByMarket(marketId);
    }

    /**
     * Delete by developer id.
     *
     * @param developerId the developer id
     */
    @MasterDs
    public void deleteByDeveloperId(Long developerId) {
        dao.deleteByDeveloperId(developerId);
    }

    /**
     * Find by developer id list.
     *
     * @param developerId the developer id
     * @return the list
     */
    public List<SandboxTerminalRegistry> findByDeveloperId(Long developerId) {
        return dao.findByDeveloperId(developerId);
    }

    /**
     * Gets exists terminal serial nos.
     *
     * @param terminalSns the terminal sns
     * @return the exists terminal serial nos
     */
    public List<SandboxTerminalRegistry> getExistsTerminalSerialNos(List<String> terminalSns) {
        if (CollectionUtils.isEmpty(terminalSns)) {
            return Collections.emptyList();
        }
        return dao.getExistsTerminalSns(terminalSns);
    }


    /**
     * Check limit.
     *
     * @param addCount the add count
     */
    public void checkLimit(int addCount) {
        int currentMarketLimit = getCurrentMarket().getDevTerminalLimit();
        if (currentMarketLimit >= 0 && (getTotalCount(getCurrentMarketId(), getCurrentUser().getCurrentDeveloperId()) + addCount) > currentMarketLimit) {
            throw new BusinessException(ApiCodes.TERMINAL_LIMIT_EXCEED);
        }
    }
    
    public int getTotalCount(Long marketId, Long devId) {
        Integer totalCount = dao.getTotalCount(marketId, devId);
        if (totalCount == null) {
            return 0;
        }
        return totalCount;
    }
}
