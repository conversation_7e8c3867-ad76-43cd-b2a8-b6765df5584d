package com.paxstore.global.domain.service.vas.airlink;

import com.pax.market.constants.airlink.AirLinkOrderType;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.paxstore.global.domain.dao.vas.airlink.AirLinkOrderDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * author mengxia<PERSON>ian
 * Date   2025/4/2 13:28
 */
@Component
@Lazy
@Slf4j
public class AirLinkOrderNumberCacheService implements InitializingBean {

    @Autowired
    private AirLinkOrderDao airLinkOrderDao;

    @Autowired(required = false)
    private JedisConnectionFactory jedisConnectionFactory;

    private static final RedisSerializer<String> redisSerializer = new StringRedisSerializer();
    private RedisTemplate<String, Object> template;
    private static final String INIT_AIRLINK_ORDER_NUMBER = "distributionLock:initAirLinkOrderNumber";
    private static final String AIRLINK_ORDER_NUMBER = "airlink_order_number";
    private static RedisScript<Long> INCR_NUMBER = null;
    private static final long MAX_ORDER_SORT_NO = 999999;

    static {
        String script = """
                    local val, err = redis.pcall('INCR', KEYS[1]) if err then return err end
                    if val == 1 then redis.call('SET', KEYS[1], 0, 'EX', ARGV[1]) return nil end
                    if val > %d then redis.call('SET', KEYS[1], 1, 'EX', ARGV[1]) return 1 end return val
                """.formatted(MAX_ORDER_SORT_NO);
        INCR_NUMBER = new DefaultRedisScript(script, Long.class);
    }

    public Long getAirlinkDeductOrderNumber(){
        Long val = template.execute(INCR_NUMBER, Collections.singletonList(AIRLINK_ORDER_NUMBER), String.valueOf(3600));
        if (val == null){
            boolean lock = RedisUtils.tryLock(INIT_AIRLINK_ORDER_NUMBER, 5);
            if (lock){
                try {
                    val = 1L;
                    Date maxDate = airLinkOrderDao.getMaxCreatedDate(AirLinkOrderType.DEDUCTION.getCode());
                    if (maxDate != null){
                        Date startTime = DateUtils.beginOfDate(maxDate);
                        Date endTime = DateUtils.endOfDate(maxDate);
                        String lastOrderNumber = airLinkOrderDao.getMaxOrderNumber(AirLinkOrderType.DEDUCTION.getCode(), startTime, endTime);
                        long lastOrder = Long.parseLong(lastOrderNumber.substring(lastOrderNumber.length() - 6));
                        if (lastOrder < MAX_ORDER_SORT_NO){
                            val = lastOrder + 1;
                        }
                    }
                    template.opsForValue().set(AIRLINK_ORDER_NUMBER, String.valueOf(val), 3600, TimeUnit.SECONDS);
                }catch (Exception e){
                    log.error("get airlink order number error: ", e);
                }finally {
                    RedisUtils.unlock(INIT_AIRLINK_ORDER_NUMBER);
                }
            }else {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("get airLink order number thread interrupted", e);
                }
                return getAirlinkDeductOrderNumber();
            }
        }
        return val;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (jedisConnectionFactory != null) {
            template = new RedisTemplate<>();
            template.setConnectionFactory(jedisConnectionFactory);
            template.setKeySerializer(redisSerializer);
            template.setHashKeySerializer(redisSerializer);
            template.setValueSerializer(redisSerializer);
            template.setHashValueSerializer(redisSerializer);
            template.afterPropertiesSet();
        }else{
            log.warn("init airlink order number cache, jedis connect factory is null!");
        }
    }
}
