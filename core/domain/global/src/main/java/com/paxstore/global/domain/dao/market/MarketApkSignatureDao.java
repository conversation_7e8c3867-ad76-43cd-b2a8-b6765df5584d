/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.dao.market;

import com.pax.market.domain.entity.global.market.MarketApkSignature;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent;
import org.apache.ibatis.annotations.Param;

/**
 * The interface Market apk signature dao.
 */
@MyBatisDao
public interface MarketApkSignatureDao extends CrudDao<MarketApkSignature> {
    /**
     * Gets market apk signature.
     *
     * @param marketId the market id
     * @param apkId    the apk id
     * @return the market apk signature
     */
    MarketApkSignature getMarketApkSignature(@Param("marketId") Long marketId,
                                             @Param("resellerId") Long resellerId,
                                             @Param("factoryId") Long factoryId,
                                             @Param("apkId") Long apkId,
                                             @Param("signType") String signType);

    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_MARKET_SIGNATURE_CHANGED, idExpr = "apkId")
    int insert(MarketApkSignature entity);

    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_MARKET_SIGNATURE_CHANGED, idExpr = "apkId")
    int update(MarketApkSignature entity);

    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.APK_MARKET_SIGNATURE_CHANGED, idExpr = "apkId")
    int delete(MarketApkSignature entity);
}