/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.service.rki;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.rki.MarketRkiServerConfig;
import com.pax.market.domain.util.PasswordUtils;
import com.pax.market.dto.event.MarketRkiServerConfigChangedInfo;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventAccumulator;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventListener;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.rki.MarketRkiConfigDao;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * The type Market rki config service.
 *
 * <AUTHOR>
 * @date 2018 /10/16
 */
@SuppressWarnings("restriction")
@Service
public class MarketRkiConfigService extends CrudService<MarketRkiConfigDao, MarketRkiServerConfig> {

    /**
     * Save.
     *
     * @param marketRkiServerConfig the market rki server config
     * @param encryptPwdSet         the encrypt pwd set
     */
    @MasterDs
    public void save(MarketRkiServerConfig marketRkiServerConfig, boolean encryptPwdSet) {
        if (isRkiServerNameAlreadyExist(marketRkiServerConfig)) {
            throw new BusinessException(ApiCodes.RKI_SERVER_NAME_ALREADY_EXIST);
        }
        if (marketRkiServerConfig.getMarketId() == null) {
            marketRkiServerConfig.setMarketId(getCurrentMarketId());
        }

        if (!encryptPwdSet) {
            String encryptPwd = PasswordUtils.encryptSignaturePassword(marketRkiServerConfig.getPassword());
            marketRkiServerConfig.setPassword(encryptPwd);
        }

        super.save(marketRkiServerConfig);

        if (!marketRkiServerConfig.getIsNewRecord()) {
            EntityChangedEventAccumulator.add(new EntityChangedEventListener.EntityChangedEvent(
                    MarketRkiServerConfigChangedInfo.builder().rkiConfigId(marketRkiServerConfig.getId()).build()));
        }
    }


    /**
     * Get rki server market rki server config.
     *
     * @param id the id
     * @return the market rki server config
     */
    public MarketRkiServerConfig getRkiServer(Long id) {
        MarketRkiServerConfig marketRkiServerConfig = dao.get(id);
        if (marketRkiServerConfig != null) {
            String decryptPwd = PasswordUtils.decryptSignaturePassword(marketRkiServerConfig.getPassword());
            marketRkiServerConfig.setPassword(decryptPwd);
        }
        return marketRkiServerConfig;
    }

    /**
     * Get market rki server market rki server config.
     *
     * @param marketId the market id
     * @return the market rki server config
     */
    public MarketRkiServerConfig getMarketRkiServer(Long marketId) {
        MarketRkiServerConfig marketRkiServerConfig = dao.getByMarket(marketId);
        if (marketRkiServerConfig != null) {
            String decryptPwd = PasswordUtils.decryptSignaturePassword(marketRkiServerConfig.getPassword());
            marketRkiServerConfig.setPassword(decryptPwd);
        }
        return marketRkiServerConfig;
    }

    /**
     * Find rki server list list.
     *
     * @return the list
     */
    public List<MarketRkiServerConfig> findRkiServerList() {
        List<MarketRkiServerConfig> marketRkiServerConfigs = dao.findListByMarket(SystemConstants.SUPER_MARKET_ID);
        for (MarketRkiServerConfig rkiServer : marketRkiServerConfigs) {
            String decryptPwd = PasswordUtils.decryptSignaturePassword(rkiServer.getPassword());
            rkiServer.setPassword(decryptPwd);
        }
        return marketRkiServerConfigs;
    }

    /**
     * Is reference market boolean.
     *
     * @param rkiId the rki id
     * @return the boolean
     */
    public boolean isReferenceMarket(Long rkiId) {
        return dao.getReferenceCount(rkiId) > 0;
    }

    /**
     * Is rki server name already exist boolean.
     *
     * @param marketRkiServerConfig the market rki server config
     * @return the boolean
     */
    public boolean isRkiServerNameAlreadyExist(MarketRkiServerConfig marketRkiServerConfig) {
        return dao.getRkiServerNameCount(marketRkiServerConfig) > 0;
    }

    /**
     * Delete.
     *
     * @param id the id
     */
    @MasterDs
    public void delete(Long id) {
        dao.delete(id);
        EntityChangedEventAccumulator.add(new EntityChangedEventListener.EntityChangedEvent(
                MarketRkiServerConfigChangedInfo.builder().rkiConfigId(id).build()));
    }

    /**
     * Is ready rki server boolean.
     *
     * @param marketId the market id
     * @return the boolean
     */
    public boolean isReadyRkiServer(Long marketId) {
        boolean result = false;
        MarketRkiServerConfig marketRkiServerConfig = dao.getByMarket(marketId);
        if (marketRkiServerConfig != null) {
            result = true;
        }
        return result;
    }

    /**
     * Insert market rki server.
     *
     * @param marketId the market id
     * @param rkiId    the rki id
     */
    @MasterDs
    public void insertMarketRkiServer(Long marketId, Long rkiId) {
        dao.insertMarketRkiServer(marketId, rkiId);
    }


    /**
     * Delete market rki server.
     *
     * @param marketId the market id
     */
    @MasterDs
    public void deleteMarketRkiServer(Long marketId) {
        dao.deleteMarketRkiServer(marketId);
    }

    /**
     * Update market rki server.
     *
     * @param marketId the market id
     * @param rkiId    the rki id
     */
    @Transactional
    public void updateMarketRkiServer(Long marketId, Long rkiId) {
        deleteMarketRkiServer(marketId);
        MarketRkiServerConfig marketRkiServerConfig = getRkiServer(rkiId);
        if (marketRkiServerConfig != null) {
            insertMarketRkiServer(marketId, rkiId);
        }
    }

}
