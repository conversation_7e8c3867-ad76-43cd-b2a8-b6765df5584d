/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.paxstore.global.domain.service.terminal;

import com.pax.market.domain.entity.market.terminal.TerminalPushHistory;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.persistence.BatchOperator;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.persistence.handler.BatchConsumerHandler;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.terminal.TerminalPushHistoryDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/10/10
 */
@Service
public class TerminalPushHistoryService extends CrudService<TerminalPushHistoryDao, TerminalPushHistory> {


    /**
     * Get list -> push terminal history api
     *
     * @param page       the page
     * @param terminalId the terminal id
     * @return the page
     */
    public Page<TerminalPushHistory> findTerminalPushHistoryPage(Page<TerminalPushHistory> page, Long terminalId) {
        if (LongUtils.isBlankOrNotPositive(terminalId)) {
            return page;
        }
        List<TerminalPushHistory> terminalPushHistoryList;
        TerminalPushHistory terminalPushHistory = new TerminalPushHistory();
        terminalPushHistory.setPage(page);
        terminalPushHistory.setTerminalId(terminalId);
        terminalPushHistoryList = dao.findTerminalPushHistory(terminalPushHistory);
        page.setList(terminalPushHistoryList);
        return page;
    }

    @MasterDs
    public void createPushHistories(byte cmd, Set<Long> terminalIds) {
        if (!SystemPropertyHelper.isCreateTerminalPushHistory() || CollectionUtils.isEmpty(terminalIds)) {
            return;
        }
        List<TerminalPushHistory> terminalPushHistoryList = terminalIds.stream().map(terminalId -> {
            TerminalPushHistory terminalPushHistory = new TerminalPushHistory();
            terminalPushHistory.setCmd(Byte.toUnsignedInt(cmd));
            terminalPushHistory.setTerminalId(terminalId);
            return terminalPushHistory;
        }).collect(Collectors.toList());
        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                BatchConsumerHandler.of(terminalPushHistoryList, subList -> dao.insertList(subList))
        );
    }
}
