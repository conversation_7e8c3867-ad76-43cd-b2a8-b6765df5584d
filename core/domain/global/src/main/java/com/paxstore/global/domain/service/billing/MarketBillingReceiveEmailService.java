package com.paxstore.global.domain.service.billing;

import com.pax.market.domain.entity.global.billing.MarketBillingReceiveEmail;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.billing.MarketBillingReceiveEmailDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MarketBillingReceiveEmailService extends CrudService<MarketBillingReceiveEmailDao, MarketBillingReceiveEmail> {

    public MarketBillingReceiveEmail getMarketBillingReceiveEmail(Long id) {
        return dao.getMarketReceiveEmail(id);
    }

    public boolean isEmailExist(Long marketId, String email, Long id) {
        return dao.isEmailExist(marketId, email, id);
    }

    @MasterDs
    public void saveReceiveEmail(MarketBillingReceiveEmail email) {
        if (email.getIsNewRecord()) {
            dao.createMarketReceiveEmail(email);
        } else {
            dao.updateMarketReceiveEmail(email);
        }
    }

    @MasterDs
    public void deleteMarketReceiveEmail(Long id) {
        dao.deleteMarketReceiveEmail(id);
    }

    public List<MarketBillingReceiveEmail> findMarketReceiveEmailList(Long marketId) {
        MarketBillingReceiveEmail email = new MarketBillingReceiveEmail();
        email.setMarketId(marketId);
        return dao.findMarketReceiveEmailList(email);
    }

    public Page<MarketBillingReceiveEmail> findEmailPage(Page<MarketBillingReceiveEmail> page, MarketBillingReceiveEmail email) {
        email.setPage(page);
        List<MarketBillingReceiveEmail> list = dao.findMarketReceiveEmailList(email);
        page.setList(list);
        return page;
    }

}
