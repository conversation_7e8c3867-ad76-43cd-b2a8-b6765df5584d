/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.dao.vas.cloudmsg;

import com.pax.market.domain.entity.global.vas.cloudmsg.CloudMsgTag;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@MyBatisDao
public interface CloudMsgTagDao extends CrudDao<CloudMsgTag> {

    CloudMsgTag getByTagName(@Param("appId") Long appId, @Param("tagName") String tagName);

    Integer getTagCountByAppId(@Param("appId") Long appId);

    List<Long> findAppMsgTagIds(@Param("appId") Long appId, @Param("tagNames") List<String> tagNames);

    CloudMsgTag getIncludeDeleted(@Param("tagId") Long tagId);
}
