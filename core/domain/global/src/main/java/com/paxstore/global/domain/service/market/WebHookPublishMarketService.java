/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.service.market;

import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.framework.common.service.BaseService;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.market.WebHookPublishMarketDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;


/**
 * webhook远程推送Service
 *
 * <AUTHOR>
 * @date 2022/6/30
 */
@Service
@RequiredArgsConstructor
public class WebHookPublishMarketService extends BaseService {
    private final WebHookPublishMarketDao dao;


    @MasterDs
    public void updateGlobalWebHookPublish(Long webHookId, Set<Long> marketIds) {
        if (LongUtils.equals(SystemConstants.SUPER_MARKET_ID, getCurrentMarketId())) {
            dao.deleteGlobalWebHookPublish(webHookId);
            if (CollectionUtils.isNotEmpty(marketIds)) {
                dao.insertGlobalWebHookPublishList(webHookId, marketIds);
            }
        }
    }

   public List<Market> findPublishMarketList(Long webHookId) {
       return dao.findPublishMarketList(webHookId);
   }




}