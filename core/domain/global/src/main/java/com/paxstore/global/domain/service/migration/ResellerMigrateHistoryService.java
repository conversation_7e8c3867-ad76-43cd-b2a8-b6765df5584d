package com.paxstore.global.domain.service.migration;

import com.pax.market.constants.ActivityStatus;
import com.pax.market.domain.entity.global.migration.ResellerMigrateHistory;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.migration.ResellerMigrateHistoryDao;
import org.springframework.stereotype.Service;

/**
 * The type Reseller migrate history service.
 */
@Service
public class ResellerMigrateHistoryService extends CrudService<ResellerMigrateHistoryDao, ResellerMigrateHistory> {

    /**
     * Create reseller migrate history reseller migrate history.
     *
     * @param sourceMarketName   the source market name
     * @param sourceResellerName the source reseller name
     * @param targetMarketName   the target market name
     * @param targetResellerName the target reseller name
     * @return the reseller migrate history
     */
    @MasterDs
    public ResellerMigrateHistory createResellerMigrate(String sourceMarketName, String sourceResellerName, String targetMarketName, String targetResellerName) {
        ResellerMigrateHistory resellerMigrateHistory = new ResellerMigrateHistory();
        resellerMigrateHistory.setSourceMarketName(sourceMarketName);
        resellerMigrateHistory.setSourceResellerName(sourceResellerName);
        resellerMigrateHistory.setTargetMarketName(targetMarketName);
        resellerMigrateHistory.setTargetResellerName(targetResellerName);
        resellerMigrateHistory.setStatus(ActivityStatus.PENDING);
        resellerMigrateHistory.setLocale(RequestLocaleHolder.getLocale());
        super.save(resellerMigrateHistory);
        return resellerMigrateHistory;
    }

    /**
     * Update status.
     *
     * @param resellerMigrateHistory the reseller migrate history
     * @param status                 the status
     */
    @MasterDs
    public void updateStatus(ResellerMigrateHistory resellerMigrateHistory, String status) {
        resellerMigrateHistory.setStatus(status);
        save(resellerMigrateHistory);
    }

    /**
     * Is unfinished migration exist boolean.
     *
     * @return the boolean
     */
    public boolean isUnfinishedMigrationExist() {
        return dao.isUnfinishedMigrationExist();
    }

    @Override
    @MasterDs
    public ResellerMigrateHistory get(Long id) {
        return super.get(id);
    }
}
