/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.paxstore.global.domain.dao.dashboard;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.pax.market.domain.entity.global.dashboard.AdminDashboardColumn;
import com.pax.market.domain.entity.global.dashboard.AdminDashboardRow;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

/**
 *
 *
 * <AUTHOR>
 * @date Mar 2, 2017
 */
@MyBatisDao
public interface AdminDashboardDao {
    void insertRow(AdminDashboardRow row);

    void insertColumn(AdminDashboardColumn column);

    void deleteRowByUserId(@Param("userId") Long userId);

    void deleteColumnByUserId(@Param("userId") Long userId);

    List<AdminDashboardRow> findByUserId(@Param("userId") Long userId);
}
