/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.global.domain.dao.setting;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.pax.market.domain.entity.global.setting.CodeType;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

/**
 * Created by gubin_ on 2016/12/1.
 */
@MyBatisDao
public interface CodeTypeDao extends CrudDao<CodeType> {
    /**
     * Find code types list.
     *@param codeType the codeType
     *
     * @return the list
     */
    List<CodeType> findCodeTypes(CodeType codeType);

    /**
     * Gets by codeType.
     *
     * @param codeType the codeType
     * @return the CodeType
     */
    CodeType getByCodeType(@Param("codeType") String codeType);
}
