
/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.paxstore.global.domain.service.app;


import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.query.CreateApkResellerRequest;
import com.pax.market.domain.query.DeleteApkResellerRequest;
import com.pax.market.framework.common.service.BaseService;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.global.domain.dao.app.ApkPublishResellerDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * The type Apk publish reseller service.
 *
 * <AUTHOR>
 * @create 2023 /9/20
 */
@Service
@RequiredArgsConstructor
public class ApkPublishResellerService extends BaseService {
    private final ApkPublishResellerDao dao;

    /**
     * Is apk specific reseller boolean.
     *
     * @param apkId the apk id
     * @return the boolean
     */
    public boolean isApkSpecificReseller(Long apkId) {
        return dao.isApkSpecificReseller(apkId, getCurrentMarketId());
    }


    /**
     * Update apk reseller specific setting.
     *
     * @param apk          the apk
     * @param resellerList the apk reseller specific list
     */
    @Transactional
    public void updateApkResellerSpecificSetting(Apk apk, Long marketId, List<Long> currentApkResellerList, List<Long> resellerList) {
        List<Long> deleteList = new ArrayList<>();
        List<Long> insertList = new ArrayList<>();
        for (Long currentApkResellerId : currentApkResellerList) {
            if (!resellerList.contains(currentApkResellerId)) {
                deleteList.add(currentApkResellerId);
            }
        }

        resellerList.forEach(apkResellerSpecific -> {
            if (!currentApkResellerList.contains(apkResellerSpecific)) {
                insertList.add(apkResellerSpecific);
            }
        });

        if (!deleteList.isEmpty()) {
            this.performDeleteApkResellers(apk.getId(), deleteList);
        }
        if (!insertList.isEmpty()) {
            dao.insertApkResellerList(CreateApkResellerRequest.builder().apkId(apk.getId()).marketId(marketId).resellerIds(insertList).build());
        }
    }



    /**
     * Delete apk reseller specific setting.
     *
     * @param apkId      the apk id
     * @param resellerId the reseller id
     */
    @MasterDs
    public void deleteApkResellerSpecificSetting(Long apkId, Long resellerId) {
        this.performDeleteApkResellers(apkId, Collections.singletonList(resellerId));
    }

    private void performDeleteApkResellers(Long apkId, List<Long> resellers) {
        dao.deleteApkResellerList(DeleteApkResellerRequest.builder().apkId(apkId).resellerIdList(resellers).build());
    }

    /**
     * Find published all reseller id list.
     *
     * @param apkId the apk id
     * @return the list
     */
    public List<Long> findPublishedResellerIdAllList(Long apkId, Integer limit) {
        if (apkId == null) {
            return Collections.emptyList();
        }
        Reseller apkReseller = new Reseller();
        apkReseller.setApkId(apkId);
        apkReseller.setMarketId(getCurrentMarketId());
        apkReseller.getPage().setLimit(IntegerUtils.getValue(limit));
        return dao.findPublishedResellers4Apk(apkReseller);
    }

    public List<Long> findPublishedResellerIdAllList(Long apkId) {
        return findPublishedResellerIdAllList(apkId, null);
    }
}
