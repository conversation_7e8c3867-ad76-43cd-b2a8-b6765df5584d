package com.paxstore.global.domain.dao.cleardata;

import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.springframework.data.repository.query.Param;

@MyBatisDao
public interface GlobalTerminalPhysicalDeleteDao extends CrudDao<Terminal> {
    void deleteFromTerminalRelatedTable(@Param("relatedTableName") String relatedTableName, @Param("terminalId") Long terminalId);

}
