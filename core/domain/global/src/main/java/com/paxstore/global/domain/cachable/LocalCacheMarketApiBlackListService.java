/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.cachable;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.pax.market.dto.market.MarketApiBlackListInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.paxstore.global.domain.service.market.MarketApiBlackListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/3/8 19:44
 */
@Component
@Slf4j
public class LocalCacheMarketApiBlackListService {

    @Autowired
    private MarketApiBlackListService marketApiBlackListService;

    private static final long expireMinutes = 10;
    private static final long maxCacheSize = 200;

    private final LoadingCache<Long, List<MarketApiBlackListInfo>> marketApiBlackListCache;

    public LocalCacheMarketApiBlackListService() {
        this.marketApiBlackListCache = initMarketApiBlackListCache();
    }

    private LoadingCache<Long, List<MarketApiBlackListInfo>> initMarketApiBlackListCache() {
        return Caffeine
                .newBuilder()
                .removalListener((key, graph, cause) -> {
                    log.debug("marketApiBlackListCache: key {} was removed, cause: {}", key, cause);
                })
                .maximumSize(maxCacheSize)
                .expireAfterWrite(expireMinutes, TimeUnit.MINUTES)
                .build(marketId -> {
                    List<MarketApiBlackListInfo> value = BeanMapper.mapList(marketApiBlackListService.findListForThirdParty(marketId), MarketApiBlackListInfo.class);
                    log.debug("Load Market Api marketApiBlackListCache(ThirdParty), MarketId: {}, MarketApiBlackListInfo: {}", marketId, value);
                    return value;
                });
    }

    /**
     * Find market api black list
     *
     * @param marketId the market id
     * @return the list
     */
    public List<MarketApiBlackListInfo> findMarketApiBlackList(Long marketId) {
        return marketApiBlackListCache.get(marketId);
    }

    /**
     * Clear local cache
     *
     * @param marketId the market id
     */
    public void clearMarketApiBlackListCache(Long marketId) {
        marketApiBlackListCache.invalidate(marketId);
    }
}
