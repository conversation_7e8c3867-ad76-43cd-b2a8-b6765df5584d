/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.global.domain.service.audit;

import com.pax.core.json.JsonMapper;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.domain.entity.global.audit.AuditReqBody;
import com.pax.market.domain.entity.global.audit.AuditReqParam;
import com.pax.market.domain.entity.global.audit.AuditTrail;
import com.pax.market.framework.common.service.BaseService;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.vo.admin.system.auditlog.ApkParamCompareVo;
import com.pax.market.vo.admin.system.auditlog.AuditLogChangeRecordVo;
import com.pax.market.vo.admin.system.auditlog.ParameterTemplateVo;
import com.paxstore.global.domain.dao.audit.AuditTrailDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Audit Trail Service
 *
 * <AUTHOR>
 * @date Dec 20, 2017
 */
@Service
public class AuditTrailService extends BaseService {

    public static final String HISTORY_TABLE_SUFFIX = "history";
    @Autowired
    private AuditTrailDao dao;

    /**
     * Get audit trail.
     *
     * @param id        the id
     * @param crateDate the crate date
     * @return the audit trail
     */
    public AuditTrail get(Long id, Date crateDate, String suffix) {
        if (StringUtils.isBlank(suffix)) {
            suffix = getAuditTableSuffixByCreateDate(crateDate);
        }
        return dao.getById(id, suffix);
    }

    /**
     * Gets audit table suffix by create date.
     *
     * @param createdDate the created date
     * @return the audit table suffix by create date
     */
    public String getAuditTableSuffixByCreateDate(Date createdDate) {
        if (null == createdDate) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createdDate);
        int queryYear = calendar.get(Calendar.YEAR);
        return getAuditTableSuffixByCreateDate(queryYear);
    }

    /**
     * Gets audit table suffix by create date.
     *
     * @param queryYear the query year
     * @return the audit table suffix by create date
     */
    public String getAuditTableSuffixByCreateDate(int queryYear) {
        String tableSuffix = "_";
        if (queryYear < 2019) {
            tableSuffix += HISTORY_TABLE_SUFFIX;
        } else if (queryYear < Integer.parseInt(DateUtils.getYear(new Date())) - 1) {
            tableSuffix += queryYear;
        }
        if (tableSuffix.length() > 1 && doCheckAuditArchivalTable(tableSuffix)) {
            return tableSuffix;
        }
        return null;
    }

    /**
     * Do check audit archival table boolean.
     *
     * @param tableSuffix the table suffix
     * @return the boolean
     */
    public boolean doCheckAuditArchivalTable(String tableSuffix) {
        try {
            dao.checkTableExist(String.format("pax_audit_trail%s", tableSuffix));
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * Gets req body.
     *
     * @param auditId     the audit id
     * @param createdDate the created date
     * @return the req body
     */
    public AuditReqBody getReqBody(Long auditId, Date createdDate) {
        String tableSuffix = getAuditTableSuffixByCreateDate(createdDate);
        return dao.getAuditBody(auditId, tableSuffix);
    }

    /**
     * Gets req body.
     *
     * @param auditId the audit id
     * @param suffix  the suffix
     * @return the req body
     */
    public AuditReqBody getReqBody(Long auditId, String suffix) {
        return dao.getAuditBody(auditId, suffix);
    }

    /**
     * Gets req param.
     *
     * @param auditId     the audit id
     * @param createdDate the created date
     * @return the req param
     */
    public List<AuditReqParam> getReqParam(Long auditId, Date createdDate) {
        String tableSuffix = getAuditTableSuffixByCreateDate(createdDate);
        return dao.getAuditParam(auditId, tableSuffix);
    }

    /**
     * Gets req param.
     *
     * @param auditId the audit id
     * @param suffix  the suffix
     * @return the req param
     */
    public List<AuditReqParam> getReqParam(Long auditId, String suffix) {
        return dao.getAuditParam(auditId, suffix);
    }

    /**
     * Gets audit trail.
     *
     * @param auditId     the audit id
     * @param createdDate the created date
     * @return the audit trail
     */
    public AuditTrail getAuditTrail(Long auditId, Date createdDate) {
        String tableSuffix = getAuditTableSuffixByCreateDate(createdDate);
        return dao.getAuditTrail(auditId, tableSuffix);
    }

    public AuditLogChangeRecordVo getParameterChangeRecord(AuditLogChangeRecordVo changeRecord) {
        if (Objects.nonNull(changeRecord)) {
            if (IntegerUtils.equals(changeRecord.getDataType(), AuditDataTypes.TEMPLATE)
                    || IntegerUtils.equals(changeRecord.getDataType(), AuditDataTypes.TERMINAL_APK_PARAM)
                    || IntegerUtils.equals(changeRecord.getDataType(), AuditDataTypes.GROUP_APK_PARAM)
                    || IntegerUtils.equals(changeRecord.getDataType(), AuditDataTypes.GROUP_SOLUTION_PARAM)
                    || IntegerUtils.equals(changeRecord.getDataType(), AuditDataTypes.SANDBOX_TERMINAL_APK)) {
                AuditLogChangeRecordVo auditLogChangeRecordVo = new AuditLogChangeRecordVo();
                List<ApkParamCompareVo> result = new ArrayList<>();
                auditLogChangeRecordVo.setDataType(changeRecord.getDataType());
                if (StringUtils.isNotBlank(changeRecord.getOldData()) && StringUtils.isNotBlank(changeRecord.getNewData())) {
                    Set<String> pidList = new HashSet<>();
                    ParameterTemplateVo oldData = JsonMapper.fromJsonString(changeRecord.getOldData(), ParameterTemplateVo.class);
                    ParameterTemplateVo newData = JsonMapper.fromJsonString(changeRecord.getNewData(), ParameterTemplateVo.class);
                    Set<String> oldPid = oldData.getParameterList().stream().map(ParameterTemplateVo.ConfiguredParameterVo::getPid).collect(Collectors.toSet());
                    Set<String> newPid = newData.getParameterList().stream().map(ParameterTemplateVo.ConfiguredParameterVo::getPid).collect(Collectors.toSet());
                    pidList.addAll(oldPid);
                    pidList.addAll(newPid);
                    for (String pid : pidList) {
                        ApkParamCompareVo apkParamCompareVo = new ApkParamCompareVo();
                        apkParamCompareVo.setPid(pid);
                        for (ParameterTemplateVo.ConfiguredParameterVo configuredParameter : oldData.getParameterList()) {
                            if (StringUtils.equals(pid, configuredParameter.getPid())) {
                                apkParamCompareVo.setOldParamValue(configuredParameter.getParamValue());
                                apkParamCompareVo.setInputType(configuredParameter.getInputType());
                                apkParamCompareVo.setSelect(configuredParameter.getSelect());
                                apkParamCompareVo.setOldName(configuredParameter.getTitle());
                            }
                        }
                        for (ParameterTemplateVo.ConfiguredParameterVo configuredParameter : newData.getParameterList()) {
                            if (StringUtils.equals(pid, configuredParameter.getPid())) {
                                apkParamCompareVo.setNewParamValue(configuredParameter.getParamValue());
                                apkParamCompareVo.setInputType(configuredParameter.getInputType());
                                apkParamCompareVo.setSelect(configuredParameter.getSelect());
                                apkParamCompareVo.setNewName(configuredParameter.getTitle());
                            }
                        }
                        result.add(apkParamCompareVo);
                    }
                } else if (StringUtils.isNotBlank(changeRecord.getOldData())) {
                    ParameterTemplateVo oldData = JsonMapper.fromJsonString(changeRecord.getOldData(), ParameterTemplateVo.class);
                    for (ParameterTemplateVo.ConfiguredParameterVo configuredParameter : oldData.getParameterList()) {
                        ApkParamCompareVo apkParamCompareVo = new ApkParamCompareVo();
                        apkParamCompareVo.setOldParamValue(configuredParameter.getParamValue());
                        apkParamCompareVo.setOldName(configuredParameter.getTitle());
                        apkParamCompareVo.setSelect(configuredParameter.getSelect());
                        apkParamCompareVo.setInputType(configuredParameter.getInputType());
                        apkParamCompareVo.setPid(configuredParameter.getPid());
                        result.add(apkParamCompareVo);
                    }
                } else if (StringUtils.isNotBlank(changeRecord.getNewData())) {
                    ParameterTemplateVo newData = JsonMapper.fromJsonString(changeRecord.getNewData(), ParameterTemplateVo.class);
                    for (ParameterTemplateVo.ConfiguredParameterVo configuredParameter : newData.getParameterList()) {
                        ApkParamCompareVo apkParamCompareVo = new ApkParamCompareVo();
                        apkParamCompareVo.setNewParamValue(configuredParameter.getParamValue());
                        apkParamCompareVo.setNewName(configuredParameter.getTitle());
                        apkParamCompareVo.setSelect(configuredParameter.getSelect());
                        apkParamCompareVo.setInputType(configuredParameter.getInputType());
                        apkParamCompareVo.setPid(configuredParameter.getPid());
                        result.add(apkParamCompareVo);
                    }
                }
                result.sort(Comparator.comparing(ApkParamCompareVo::getPid));
                auditLogChangeRecordVo.setParameterTemplates(result);
                return auditLogChangeRecordVo;
            } else {
                return changeRecord;
            }
        }
        return null;
    }

    public List<AuditTrail> findMigrationList(Long startId, Long endId, String tableName, Date yearFirst) {
        final List<AuditTrail> list = new ArrayList<>();
        dao.findMigrationList(startId, endId, tableName, yearFirst, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    public Long getMigrationAuditTrailLogMaxId(String tableName) {
        return dao.getMigrationAuditTrailLogMaxId(tableName);
    }
}
