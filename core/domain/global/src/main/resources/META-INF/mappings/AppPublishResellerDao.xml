<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.app.AppPublishResellerDao">


    <select id="isAppSpecificReseller" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM PAX_APP_RESELLER appReseller
        WHERE appReseller.app_id = #{appId}
          AND appReseller.market_id = #{marketId}
    </select>


    <insert id="insertAppResellerList">
        INSERT INTO PAX_APP_RESELLER(
        app_id,
        reseller_id,
        market_id
        ) VALUES
        <foreach collection="resellerIds" index="index" item="resellerId" open="" separator="," close="">
            (
            #{appId},
            #{resellerId},
            #{marketId}
            )
        </foreach>
    </insert>

    <delete id="deleteAppResellerList">
        DELETE FROM PAX_APP_RESELLER
        WHERE app_id = #{appId}
        AND reseller_id IN
        <foreach collection="resellerIdList" index="index" item="resellerId" open="(" separator="," close=")">
            #{resellerId}
        </foreach>
    </delete>

    <select id="findPublishedResellers4App" resultType="java.lang.Long">
        SELECT
        r.reseller_id as "id"
        FROM PAX_APP_RESELLER r
        <where>
            r.app_id = #{appId}
            AND r.market_id = #{marketId}
        </where>
        ORDER BY r.reseller_id ASC
        <if test="page != null and page.limit > 0">
            LIMIT ${page.limit}
        </if>
    </select>
</mapper>