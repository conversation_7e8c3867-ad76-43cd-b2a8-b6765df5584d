<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.usage.TerminalEnrollFileDao">
    <select id="getByMarketIdYearMonth" resultType="com.pax.market.domain.entity.global.usage.TerminalEnrollFile">
        SELECT
            id AS "id",
            snapshot_fileid AS "snapshotFileId",
            global_snapshot_fileid AS "globalSnapshotFileId",
            active_snapshot_fileid AS "activeSnapshotFileId",
            billing_mode AS "billingMode"
        FROM pax_terminal_enroll_file
        WHERE market_id = #{marketId}
        AND year = #{year}
        AND month = #{month}
    </select>

    <insert id="insert">
        INSERT INTO pax_terminal_enroll_file (
            market_id,
            year,
            month,
            count,
            snapshot_fileid,
            global_snapshot_fileid,
            active_snapshot_fileid,
            billing_mode
        ) VALUES (
            #{marketId},
            #{year},
            #{month},
            #{count},
            #{snapshotFileId},
            #{globalSnapshotFileId},
            #{activeSnapshotFileId},
            #{billingMode}
        )
    </insert>

    <update id="update">
        UPDATE pax_terminal_enroll_file
        SET count = #{count},
            snapshot_fileid = #{snapshotFileId},
            global_snapshot_fileid = #{globalSnapshotFileId},
            active_snapshot_fileid = #{activeSnapshotFileId},
            billing_mode = #{billingMode}
        WHERE id = #{id}
    </update>

    <select id="findList" resultType="com.pax.market.domain.entity.global.usage.TerminalEnrollFile">
        SELECT
            year AS "year",
            month AS "month",
            SUM(count) AS "terminalCount"
        FROM pax_terminal_enroll_file
        <where>
            <if test="marketId != null">
                market_id = #{marketId}
            </if>
        </where>
        GROUP BY year, month
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY year DESC, month DESC
            </otherwise>
        </choose>
    </select>
    
    <select id="findGlobalUsageDetailList" resultType="com.pax.market.dto.billing.TerminalEnrollDetailInfo">
        SELECT
            a.market_id AS "marketId",
            a.year AS "year",
            a.month AS "month",
            a.count AS "terminalCount",
            m.NAME AS debtor,
            m.region AS region,
            m.country AS country
        FROM pax_terminal_enroll_file a
        JOIN pax_market m ON m.id = a.market_id
        WHERE a.year = #{year}
        AND a.month = #{month}
        <if test="name != null and name != ''">
            AND INSTR(m.name , #{name}) > 0
        </if>
        <choose>
            <when test="page!=null and page.orderBy!=null and  page.orderBy!='' ">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY terminalCount DESC
            </otherwise>
        </choose>
    </select>
</mapper>