<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.market.SsoSettingDao">
    <sql id="ssoSettingColumns">
            a.id AS "id",
            a.reference_id AS "referenceId",
            a.sso_type AS "ssoType",
            so.authorization_url AS "ssoOpenIdSetting.authorizationUrl",
            so.token_url AS "ssoOpenIdSetting.tokenUrl",
            so.client_id AS "ssoOpenIdSetting.clientId",
            so.client_secret AS "ssoOpenIdSetting.clientSecret",
            so.issuer AS "ssoOpenIdSetting.issuer",
            so.default_scopes AS "ssoOpenIdSetting.defaultScopes",
            so.forwarded_query_parameters AS "ssoOpenIdSetting.forwardedQueryParameters",
            so.encryption AS "ssoOpenIdSetting.encryption",
            so.jwks_url AS "ssoOpenIdSetting.jwksUrl",
            so.default_role AS "ssoOpenIdSetting.defaultRole",
            so.auth_method AS "ssoOpenIdSetting.authMethod"
    </sql>

    <sql id="ssoOpenIdSettingJoins">
      JOIN pax_sso_open_id_setting so ON so.id = a.reference_id
    </sql>

    <select id="getByMarketId" resultType="com.pax.market.domain.entity.global.setting.SsoSetting">
        SELECT
        <include refid="ssoSettingColumns" />
        FROM PAX_SSO_SETTING a
        <include refid="ssoOpenIdSettingJoins"/>
        WHERE so.market_id = #{marketId}
    </select>

    <update id="updateSsoSetting">
		UPDATE PAX_SSO_SETTING SET
			sso_type =    #{ssoType},
			updated_by = #{updatedBy.id},
			updated_date = #{updatedDate}
		WHERE id = #{id}
		AND reference_id = #{referenceId}
	</update>

    <update id="updateSsoOpenId">
		UPDATE PAX_SSO_OPEN_ID_SETTING SET
			authorization_url = #{authorizationUrl},
			token_url = #{tokenUrl},
			client_id = #{clientId},
			client_secret = #{clientSecret},
			issuer = #{issuer},
			default_scopes = #{defaultScopes},
			forwarded_query_parameters = #{forwardedQueryParameters},
			encryption = #{encryption},
			jwks_url = #{jwksUrl},
			default_role = #{defaultRole},
			auth_method = #{authMethod}
		WHERE market_id = #{marketId}
	</update>

    <insert id="insertSsoSetting">
          INSERT INTO PAX_SSO_SETTING(
            reference_id,
            sso_type,
            created_by,
            created_date
            )VALUES (#{referenceId}, #{ssoType}, #{createdBy.id}, #{createdDate})
    </insert>

    <insert id="insertSsoOpenId">
          INSERT INTO PAX_SSO_OPEN_ID_SETTING(
            market_id,
            authorization_url,
            token_url,
            client_id,
            client_secret,
            issuer,
            default_scopes,
            forwarded_query_parameters,
            encryption,
            jwks_url,
            default_role,
            auth_method
            )VALUES( #{marketId}, #{authorizationUrl}, #{tokenUrl}, #{clientId}, #{clientSecret},#{issuer}, #{defaultScopes}, #{forwardedQueryParameters}, #{encryption},#{jwksUrl},#{defaultRole},#{authMethod})
    </insert>

    <select id="getSsoOpenId" resultType="Long">
        SELECT
        so.id AS id
        FROM PAX_SSO_OPEN_ID_SETTING so
        WHERE so.market_id = #{marketId}
        ORDER BY so.id  DESC
        LIMIT 1
    </select>

    <delete id="deleteSsoSetting">
     DELETE
     FROM PAX_SSO_SETTING
     WHERE
       PAX_SSO_SETTING.reference_id IN (
		SELECT sub.id
		  FROM
			(SELECT
					a.id
				FROM
					pax_sso_open_id_setting a
				LEFT JOIN pax_sso_setting b ON a.id = b.reference_id
				WHERE
					a.market_id = #{marketId}
			) sub
	   )
    </delete>

    <delete id="deleteOpenIdSetting">
      DELETE
      FROM PAX_SSO_OPEN_ID_SETTING
      WHERE PAX_SSO_OPEN_ID_SETTING.market_id= #{marketId}
    </delete>



</mapper>