<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.vas.InsightMarketSettingDao">

    <insert id="insert">
        INSERT INTO PAX_INSIGHT_MARKET_SETTING(
        market_id,
        enabled
        ) VALUES (
        #{marketId},
        #{enabled}
        )
    </insert>

    <select id="get" resultType="com.pax.market.domain.entity.global.vas.InsightMarketSetting">
        SELECT
        enabled
        FROM `PAX_INSIGHT_MARKET_SETTING` where market_id = #{marketId}
    </select>

    <update id="update">
        UPDATE PAX_INSIGHT_MARKET_SETTING SET enabled = #{enabled} WHERE market_id = #{marketId}
    </update>

</mapper>