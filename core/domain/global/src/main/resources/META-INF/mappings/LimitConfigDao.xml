<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.LimitConfigDao">

    <sql id="limitConfigColumns">
        id AS "id",
        market_id AS "marketId",
        limit_key As "limitKey",
        max_count  AS "maxCount"
    </sql>


    <select id="get" resultType="com.pax.market.domain.entity.global.LimitConfig">
        SELECT
        <include refid="limitConfigColumns"/>
        FROM pax_limit_config
        WHERE id = #{id}
    </select>


    <select id="getByLimitKey" resultType="com.pax.market.domain.entity.global.LimitConfig">
        SELECT
        <include refid="limitConfigColumns"/>
        FROM pax_limit_config
        WHERE limit_key = #{limitKey} and market_id = #{marketId} limit 1
    </select>


    <select id="getByMarketId" resultType="com.pax.market.domain.entity.global.LimitConfig">
        SELECT
        <include refid="limitConfigColumns"/>
        FROM pax_limit_config
        WHERE market_id = #{marketId}
    </select>


    <delete id="deleteByLimitKeys">
        DELETE FROM pax_limit_config
        WHERE market_id = #{marketId}
        AND limit_key in
        <foreach collection="limitConfigs" index="index" item="cfg" open="(" separator="," close=")">
                #{cfg.limitKey}
        </foreach>
    </delete>


    <insert id="insertList">
         INSERT INTO pax_limit_config(
                                        market_id,
                                        limit_key ,
                                        max_count,
                                        created_by,
                                        created_date,
                                        updated_by,
                                        updated_date
         ) VALUES
         <foreach collection="limitConfigs" index="index" item="cfg" open="" separator="," close="">
             (
                #{cfg.marketId},
                #{cfg.limitKey},
                #{cfg.maxCount},
                #{cfg.createdBy.id},
                #{cfg.createdDate},
                #{cfg.updatedBy.id},
                #{cfg.updatedDate}
             )
         </foreach>
     </insert>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO pax_limit_config(
                                                market_id,
                                                limit_key ,
                                                max_count,
                                                created_by,
                                                created_date,
                                                updated_by,
                                                updated_date)
        VALUES (
                #{marketId},
                #{limitKey},
                #{maxCount},
                #{createdBy.id},
                #{createdDate},
                #{updatedBy.id},
                #{updatedDate})
    </insert>



    <update id="update">
        UPDATE pax_limit_config
        SET limit_key    = #{limitKey},
            max_count     = #{maxCount},
            updated_by   = #{updatedBy.id},
            updated_date = #{updatedDate}
        WHERE id = #{id}
    </update>

    <select id="findAllList" resultType="com.pax.market.domain.entity.global.LimitConfig">
            SELECT
                <include refid="limitConfigColumns"/>
           FROM pax_limit_config
    </select>

</mapper>