<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.setting.LicenseDao">
    <sql id="licenseColumns">
        a.id AS "id",
        a.market_id AS "marketId",
        a.license AS "license"
    </sql>

    <select id="get" resultType="com.pax.market.domain.entity.global.setting.License">
        SELECT
        <include refid="licenseColumns"/>
        FROM pax_license a
        LIMIT 1
    </select>

    <insert id="insert">
        INSERT INTO pax_license(
        market_id,
        license,
        created_by,
		created_date,
		updated_by,
		updated_date
        ) VALUES (
        #{marketId},
        #{license},
        #{createdBy.id},
		#{createdDate},
		#{updatedBy.id},
		#{updatedDate}
        )
    </insert>

    <update id="update">
        UPDATE pax_license SET
        license = #{license},
        updated_by = #{updatedBy.id},
		updated_date = #{updatedDate}
        WHERE id = #{id}
    </update>

</mapper>