<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ /*
  ~  * *******************************************************************************
  ~  * COPYRIGHT
  ~  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~  *   This software is supplied under the terms of a license agreement or
  ~  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~  *   or disclosed except in accordance with the terms in that agreement.
  ~  *
  ~  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~  * *******************************************************************************
  ~  */
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.app.ApkPatchDao">

    <sql id="apkPatchColumns">
        a.id AS "id",
        a.old_apk_id AS "oldApkId",
        a.new_apk_id AS "newApkId",
        a.patch_url AS "patchUrl",
        a.patch_size AS "patchSize",
        a.md AS "md",
        a.created_date AS "createdDate"
    </sql>


    <select id="get" resultType="com.pax.market.domain.entity.global.app.ApkPatch">
        SELECT
        <include refid="apkPatchColumns"/>
        FROM PAX_APK_PATCH a
        WHERE id = #{id}
    </select>

    <select id="findList" resultType="com.pax.market.domain.entity.global.app.ApkPatch">
        SELECT
        <include refid="apkPatchColumns"/>
        FROM PAX_APK_PATCH a
        <where>
            <if test="marketId != null">
                AND a.market_id = #{marketId}
            </if>
            <if test="factoryId != null">
                AND a.factory_id = #{factoryId}
            </if>
            <if test="oldApkId != null and oldApkId > 0">
                AND a.old_apk_id = #{oldApkId}
            </if>
            <if test="newApkId != null and newApkId > 0">
                AND a.new_apk_id = #{newApkId}
            </if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="findAllList" resultType="com.pax.market.domain.entity.global.app.ApkPatch">
        SELECT
        <include refid="apkPatchColumns"/>
        FROM PAX_APK_PATCH a
        <if test="page !=null and page.orderBy != null and page.orderBy != ''">
            ORDER BY ${page.orderBy}
        </if>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO PAX_APK_PATCH(
        market_id,
        factory_id,
        old_apk_id,
        new_apk_id,
        patch_url,
        patch_size,
        md,
        created_date
        ) VALUES (
        #{marketId},
        #{factoryId},
        #{oldApkId},
        #{newApkId},
        #{patchUrl},
        #{patchSize},
        #{md},
        #{createdDate}
        )
    </insert>

    <update id="update">
        UPDATE PAX_APK_PATCH SET
        patch_url = #{patchUrl},
        patch_size = #{patchSize},
        md = #{md}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM PAX_APK_PATCH
        WHERE id = #{id}
    </delete>

    <select id="getApkPatch" resultType="com.pax.market.domain.entity.global.app.ApkPatch">
        SELECT
        <include refid="apkPatchColumns"/>
        FROM PAX_APK_PATCH a
        WHERE
        a.old_apk_id = #{oldApkId}
        AND a.new_apk_id = #{newApkId}
        AND (a.factory_id = -1 OR a.factory_id = #{factoryId})
    </select>

</mapper>