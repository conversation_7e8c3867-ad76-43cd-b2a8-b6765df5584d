<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.agreement.AgreementMapDao">

	<select id="findList" resultType="com.pax.market.domain.entity.global.agreement.extend.AgreementMapExtend">
		SELECT
			a.id AS "id",
			a.market_id AS "marketId",
			m.name AS "marketName",
			a.user_id AS "userId",
			a.agreement_id AS "agreementId",
			a.type AS "type",
			a.created_date AS "createdDate",
			pua.content as "content"
		FROM pax_agreement_map a,pax_market m,pax_user_agreement pua
		where
			a.market_id=m.id
			and a.agreement_id =pua.id
			and a.user_id = #{userId}
			<if test="types != null and types.size > 0">
				AND a.type IN
				<foreach collection="types" index="index" item="type" open="(" separator="," close=")">
					#{type}
				</foreach>
			</if>
			<if test="clientId != null">
				AND a.client_id = #{clientId}
			</if>
		ORDER BY a.created_date DESC
	</select>

</mapper>