<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.vas.VasAppRegistryDao">


    <select id="get" resultType="com.pax.market.domain.entity.global.vas.VasAppRegistry">
        SELECT id, apk_id as apkId,
        charge_mode as chargeMode
        from pax_vas_app_registry
        where id = #{id}
    </select>

    <select id="getOnlineVasApp" resultType="com.pax.market.domain.entity.global.vas.VasAppRegistry">
        SELECT
        r.id, r.apk_id as apkId,
        r.charge_mode as chargeMode
        FROM
        pax_vas_app_registry r
        JOIN pax_app app ON r.id = app.id AND r.id = #{appId}
        AND app.`status` = 'A'
        JOIN pax_apk apk ON apk.id = r.apk_id
        AND apk.`status` = 'O'
        JOIN pax_apk_detail detail ON detail.apk_id = apk.id
    </select>

    <insert id="insert">
        INSERT INTO `pax_vas_app_registry` (`id`, `app_name`, `app_price`, `apk_id`, `approved_date`,`charge_mode`)
        VALUES (#{id},#{appName},#{appPrice}, #{apkId}, #{approveDate}, #{chargeMode})
    </insert>

    <update id="update">
        UPDATE `pax_vas_app_registry`
        SET `apk_id` = #{apkId}
        WHERE `id` = #{id}
    </update>


</mapper>