<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.vas.appscan.ScanTaskDao">

    <sql id="scanTaskColumns">
        a.id as "id",
        a.market_id as "marketId",
        a.app_id as "appId",
        a.apk_id as "apkId",
        a.apk_file_id as "apkFileId",
        a.task_id as "taskId",
        a.engine_identifier as "engineIdentifier",
        a.scan_mode as "scanMode",
        a.status as "status",
        a.md as "md",
        a.result_summary as "resultSummary",
		a.score as "score",
        a.fail_reason as "failReason",
        a.scan_time as "scanTime",
        a.scan_end_time as "scanEndTime",
        a.operator as "operator",
        a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate",
		a.result_zip_file AS "resultZipFile"

    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO pax_app_scan_task(
			id,
			market_id,
			app_id,
			apk_id,
			apk_file_id,
			task_id,
			engine_identifier,
			scan_mode,
			status,
			md,
			result_summary,
			score,
			fail_reason,
			scan_time,
			scan_end_time,
			operator,
			created_by,
			created_date,
			updated_by,
			updated_date
		) VALUES (
			#{id},
			#{marketId},
			#{appId},
			#{apkId},
			#{apkFileId},
			#{taskId},
			#{engineIdentifier},
			#{scanMode},
			#{status},
			#{md},
			#{resultSummary},
		    #{score},
			#{failReason},
			#{scanTime},
			#{scanEndTime},
			#{operator},
			#{createdBy.id},
			#{createdDate},
			#{updatedBy.id},
			#{updatedDate}
		)
	</insert>


	<select id="get" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
		<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		where a.id = #{id}
	</select>

	<update id="update">
		UPDATE `pax_app_scan_task` SET
			market_id =#{marketId},
			app_id =#{appId},
			apk_id =#{apkId},
			apk_file_id =#{apkFileId},
			task_id =#{taskId},
			engine_identifier =#{engineIdentifier},
			scan_mode =#{scanMode},
			status =#{status},
			md =#{md},
			result_summary =#{resultSummary},
		    score =#{score},
			fail_reason =#{failReason},
			scan_time =#{scanTime},
			scan_end_time =#{scanEndTime},
			operator =#{operator},
		    result_zip_file = #{resultZipFile},
			updated_by = #{updatedBy.id},
			updated_date = #{updatedDate}
		WHERE id = #{id}
	</update>

	<select id="getTaskIdForInprogress" resultType="java.lang.Long">
		select a.task_id as "taskId"
		from `pax_app_scan_task` a
		where a.status = 'I'
		ORDER BY id
	</select>

	<select id="getTaskIdForFail" resultType="java.lang.Long">
		select a.task_id as "taskId"
		from `pax_app_scan_task` a
		where a.status = 'F' and a.fail_reason = 'Network issue'
		ORDER BY id
		limit 200
	</select>


	<update id="updateByTaskId">
		UPDATE `pax_app_scan_task` SET
			status =#{status},
			md =#{md},
			result_summary =#{resultSummary},
			fail_reason =#{failReason},
			scan_time =#{scanTime},
			scan_end_time =#{scanEndTime},
		    result_zip_file = #{resultZipFile}
		<where> 1=1
			<if test="taskId!=null">
				and task_id = #{taskId}
			</if>
			<if test="id!=null">
				and id = #{id}
			</if>
		</where>
	</update>

	<delete id="delete">
		delete from `pax_app_scan_task`
		where id = #{id}
	</delete>

	<select id="getTaskInProgress" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
			<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		where a.market_id = #{marketId}
			and a.app_id = #{appId}
			and a.apk_id = #{apkId}
			and a.apk_file_id = #{apkFileId}
			and a.engine_identifier = #{identifier}
			and a.scan_mode = #{scanMode}
			and a.md = #{md}
			and a.del_flag = 0
			and a.status = 'I'
	</select>

	<select id="getTaskList" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
			<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		<where>
			<if test="marketId != null">
				a.market_id = #{marketId}
			</if>
			<if test="appId != null">
				and a.app_id = #{appId}
			</if>
			<if test="apkId != null">
				and a.apk_id = #{apkId}
			</if>
			and a.del_flag = 0
		</where>
	</select>

	<select id="getTaskForCompleted" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
			<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		where a.app_id = #{appId}
			and a.apk_id = #{apkId}
			and a.engine_identifier = #{identifier}
			and a.scan_mode = #{scanMode}
			and a.status = 'C'
			and a.del_flag = 0
	</select>

	<select id="getUsage" resultType="com.pax.market.dto.appscan.AppScanUsageInfo">
		SELECT
			<choose>
				<when test="marketId == -1 and groupByMarket == true">
					m.id as "marketId",
					m.NAME as "marketName",
					m.region as "region",
					m.country as "country",
					m.agent as "agent",
					COUNT( a.market_id ) AS "scanCount",
					COUNT( DISTINCT a.app_id ) AS "scanApps"
				</when>
				<otherwise>
					b.app_name AS "appName",
					d.version_name AS "version",
					e.NAME AS "engineName",
					e.charge,
					a.market_id as "marketId",
					a.scan_time AS "scanTime",
		       		a.created_date AS "createdDate",
					a.STATUS,
					a.score AS score,
					a.operator,
					f.factory_id AS "factoryId",
					g.name AS "factoryName"
				</otherwise>
			</choose>
		FROM
			pax_app_scan_task a
		<choose>
			<when test="marketId == -1 and groupByMarket == true">
				JOIN pax_market m ON a.market_id = m.id
			</when>
			<otherwise>
				JOIN pax_apk_detail b ON b.apk_id = a.apk_id
				JOIN pax_apk d ON d.id = a.apk_id
				JOIN pax_app_scan_engine e ON a.engine_identifier = e.identifier
				JOIN pax_apk_file f ON a.apk_file_id = f.id
				LEFT JOIN pax_factory g ON g.id=f.factory_id
			</otherwise>
		</choose>
		<where>
		 	1=1
			<if test="!groupByMarket">
				and a.market_id = #{marketId}
			</if>
			<if test="date != null">
				and date_format( a.created_date, '%Y-%m' ) = date_format( #{date}, '%Y-%m' )
			</if>
			<if test="score != null">
				and a.score = #{score}
			</if>
			<if test="status != null">
				and a.status = #{status}
			</if>
			<choose>
				<when test="marketId == -1 and groupByMarket == true">
					<if test="searchByName != null and searchByName != ''">
						and INSTR(m.name, #{searchByName}) > 0
					</if>
				</when>
				<otherwise>
					<if test="searchByName != null and searchByName != ''">
						and INSTR(b.app_name, #{searchByName}) > 0
					</if>
				</otherwise>
			</choose>
		</where>
		<if test="marketId == -1 and groupByMarket == true">
			GROUP BY
			m.id,
			m.NAME
		</if>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				<if test="!(marketId == -1 and groupByMarket == true)">
					ORDER BY a.created_date DESC
				</if>
			</otherwise>
		</choose>
	</select>

	<select id="getDashboard" resultType="com.pax.market.dto.appscan.AppScanDashBoardInfo">
		SELECT
			COUNT( a.id ) as "scanCount",
			COUNT( DISTINCT a.app_id ) as "scanApps"
		FROM
			pax_app_scan_task a
		JOIN pax_app_scan_engine b on b.identifier = a.engine_identifier
		<where>
			1=1
			<if test="marketId != -1">
				and a.market_id =#{marketId}
			</if>
			and date_format( a.created_date, '%Y-%m' ) = date_format( now( ), '%Y-%m' )
		</where>

	</select>

	<select id="getTasksForCreated" resultType="com.pax.market.dto.appscan.ScanTask4CreatedInfo">
		SELECT
		    a.market_id AS marketId,
	        a.app_id AS appId,
	        a.apk_id AS apkId,
	        a.engine_identifier AS engineIdentifier,
	        a.scan_mode AS scanMode,
			a.status as "status",
	        b.factory_id AS factoryId,
	        c.name AS factoryName,
			a.md as "md"
        FROM
	        `pax_app_scan_task` a
		LEFT JOIN pax_apk_file b ON a.apk_file_id = b.id
		LEFT join pax_factory c on c.id = b.factory_id
	    <where>
			<if test="marketId != null">
			a.market_id = #{marketId}
			</if>
			<if test="appId != null">
				and a.app_id = #{appId}
			</if>
			<if test="apkId != null">
				and a.apk_id = #{apkId}
			</if>
			and a.del_flag = 0
		</where>
	</select>

<!--	<select id="getCurrentUsageInfo" resultType="com.pax.market.dto.appscan.AppScanUsageInfo">-->
<!--		SELECT-->
<!--			m.id as "marketId",-->
<!--			m.NAME as "marketName",-->
<!--			COUNT( a.market_id ) AS "scanCount",-->
<!--			COUNT( DISTINCT a.app_id ) AS "scanApps"-->
<!--		FROM-->
<!--		pax_app_scan_task a-->
<!--		JOIN pax_market m ON a.market_id = m.id-->
<!--		<where>-->
<!--			<choose>-->
<!--				<when test="date == null">-->
<!--					date_format( a.created_date, '%Y-%m' ) = date_format( now( ), '%Y-%m' )-->
<!--				</when>-->
<!--				<otherwise>-->
<!--					date_format( a.created_date, '%Y-%m' ) &lt; date_format( #{date}, '%Y-%m' )-->
<!--				</otherwise>-->
<!--			</choose>-->
<!--		</where>-->
<!--			GROUP BY-->
<!--			m.id,-->
<!--			m.NAME-->
<!--	</select>-->

<!--	<select id="getCurrentUsageDetail" resultType="com.pax.market.dto.appscan.AppScanUsageInfo">-->
<!--		SELECT-->
<!--			b.NAME AS "appName",-->
<!--			d.version_name AS "version",-->
<!--			e.NAME AS "engineName",-->
<!--			e.charge,-->
<!--			a.scan_time AS "scanTime",-->
<!--			a.STATUS-->
<!--		FROM-->
<!--		pax_app_scan_task a-->
<!--			JOIN pax_app b ON b.id = a.app_id-->
<!--			JOIN pax_apk d ON d.id = a.apk_id-->
<!--			JOIN pax_app_scan_engine e ON a.engine_identifier = e.identifier-->
<!--		<where>-->
<!--			1=1-->
<!--			<if test="marketId != null">-->
<!--				and a.market_id =#{marketId}-->
<!--			</if>-->
<!--			<choose>-->
<!--				<when test="date == null">-->
<!--					and date_format( a.created_date, '%Y-%m' ) = date_format( now( ), '%Y-%m' )-->
<!--				</when>-->
<!--				<otherwise>-->
<!--					and date_format( a.created_date, '%Y-%m' ) &lt; date_format( #{date}, '%Y-%m' )-->
<!--				</otherwise>-->
<!--			</choose>-->
<!--		</where>-->

<!--	</select>-->

	<select id="getHistoricalUsage" resultType="com.pax.market.dto.appscan.AppScanHistoricalUsageInfo">
		SELECT
		b.YEAR AS "year",
		b.`month` AS "month",
		b.scanCount,
		b.scanApps
		FROM
		(
		SELECT
			YEAR ( a.created_date ) AS "year",
			MONTH ( a.created_date ) AS "month",
			COUNT( a.id ) AS "scanCount",
			COUNT( DISTINCT a.app_id ) AS "scanApps"
		FROM
			pax_app_scan_task a
		JOIN pax_app_scan_engine b on b.identifier = a.engine_identifier
		<where>
			1=1
			<if test="marketId != -1">
				and a.market_id = #{marketId}
			</if>
			and date_format( a.created_date, '%Y-%m' ) &lt; date_format( now( ), '%Y-%m' )
		</where>
		GROUP BY
		`year`,
		`month`
		) b
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY b.YEAR DESC, b.`month` DESC
			</otherwise>
		</choose>
	</select>

	<select id="getScanTaskByTaskId" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
		<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		where a.task_id = #{taskId}
			and (a.status = 'I'  or a.status = 'F' )
			and a.del_flag = 0 LIMIT 1
	</select>
    <select id="getScanResult4Complete" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
		<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		<where>
			<choose>
				<when test="marketId!=-1 ">
					(a.market_id = #{marketId}  or a.market_id = -1 )
				</when>
				<otherwise>
					a.market_id = #{marketId}
				</otherwise>
			</choose>

			<if test="appId != null">
				and a.app_id = #{appId}
			</if>
			<if test="apkId != null">
				and a.apk_id = #{apkId}
			</if>
			and a.status = 'C'
			and a.del_flag = 0
		</where>
	</select>

    <select id="getIncludeDeleted" resultType="com.pax.market.domain.entity.global.vas.appscan.ScanTask">
		select
		<include refid="scanTaskColumns" />
		from `pax_app_scan_task` a
		where a.id = #{id}
	</select>

	<select id="getTotalUsage" resultType="com.pax.market.dto.vas.AppScanUsageInfoV2">
		SELECT
		COUNT( t.id ) as "scanCount",
		COUNT( DISTINCT t.app_id ) as "scanApps"
		FROM
		pax_app_scan_task t
		JOIN pax_app_scan_engine e on e.identifier = t.engine_identifier
		<where>
			t.created_date BETWEEN #{startDate} AND #{endDate}
			<if test="marketId != null">
				and t.market_id = #{marketId}
			</if>
		</where>
	</select>

	<select id="findMarketHistoryUsage" resultType="com.pax.market.dto.vas.AppScanUsageInfoV2">
		SELECT
			m.id AS marketId,
			m.NAME AS marketName,
			m.region AS region,
			m.country AS country,
			m.agent AS agent,
			count( t.id ) AS scanCount,
			count( DISTINCT t.app_id ) AS scanApps
		FROM
			`pax_app_scan_task` t
				JOIN pax_app_scan_engine e ON e.identifier = t.engine_identifier
				JOIN pax_market m ON m.id = t.market_id
		WHERE
			DATE_FORMAT( t.created_date, '%Y-%m' ) = #{period}
			<if test="name != null and name != ''">
				AND INSTR(m.name , #{name}) > 0
			</if>
		GROUP BY
			t.market_id
		<if test="page !=null and page.orderBy != null and page.orderBy != ''">
			ORDER BY ${page.orderBy}
		</if>
	</select>
	
	<select id="findScanDetails" resultType="com.pax.market.dto.vas.AppScanUsageInfoV2">
		SELECT
		    d.app_id AS "appId",
			b.app_name AS "appName",
			d.version_name AS "version",
			e.NAME AS "engineName",
			e.charge,
			a.market_id as "marketId",
			a.scan_time AS "scanTime",
			a.STATUS,
			a.score AS score,
			f.factory_id AS "factoryId",
			g.name AS "factoryName"
		FROM
		pax_app_scan_task a
		JOIN pax_apk_detail b ON b.apk_id = a.apk_id
		JOIN pax_apk d ON d.id = a.apk_id
		JOIN pax_app_scan_engine e ON a.engine_identifier = e.identifier
		JOIN pax_apk_file f ON a.apk_file_id = f.id
		LEFT JOIN pax_factory g ON g.id = f.factory_id
		<where>
			a.market_id = #{marketId}
			and DATE_FORMAT( a.created_date, '%Y-%m' ) = #{period}
			<if test="score != null">
				and a.score = #{score}
			</if>
			<if test="status != null">
				and a.status = #{status.status}
			</if>
			<if test="name != null and name != ''">
				and INSTR(b.app_name, #{name}) > 0
			</if>
		</where>
		<if test="page !=null and page.orderBy != null and page.orderBy != ''">
			ORDER BY ${page.orderBy}
		</if>
	</select>

	<select id="findHistoryUsageByPeriod" resultType="com.pax.market.dto.vas.AppScanUsageInfoV2">
		SELECT
		DATE_FORMAT( t.created_date, '%Y-%m' ) AS `period`,
		count( t.id ) AS scanCount,
		count( DISTINCT t.app_id ) AS scanApps
		FROM
		`pax_app_scan_task` t
		JOIN pax_app_scan_engine e ON e.identifier = t.engine_identifier
		WHERE DATE_FORMAT( t.created_date, '%Y-%m') &lt; DATE_FORMAT( NOW(), '%Y-%m' )
		<if test="marketId != null">
			AND t.market_id = #{marketId}
		</if>
		GROUP BY `period`
		<choose>
			<when test="page!=null and page.orderBy!=null and  page.orderBy!='' ">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY `period` DESC
			</otherwise>
		</choose>
	</select>

	<update id="deleteById">
		UPDATE pax_app_scan_task
		SET del_flag = 1,status = 'D'
		WHERE id = #{id}
	</update>

</mapper>