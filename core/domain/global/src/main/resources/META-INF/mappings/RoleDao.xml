<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT      
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION     
  ~   This software is supplied under the terms of a license agreement or      
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied     
  ~   or disclosed except in accordance with the terms in that agreement.
  ~         
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.role.RoleDao">

    <sql id="roleColumns">
		a.id AS "id",
		a.market_id AS "marketId",
		a.reseller_id AS "resellerId",
		a.name AS "name",
		a.code AS "code",
		a.data_scope AS "dataScope",
		a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate"
	</sql>

    <select id="get" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT
        <include refid="roleColumns"/>
        FROM PAX_ROLE a
        WHERE a.id = #{id}
        AND a.del_flag = 0
    </select>

    <select id="getIncludeDeleted" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT
        <include refid="roleColumns"/>
        FROM PAX_ROLE a
        WHERE a.id = #{id}
    </select>

    <select id="getByResellerAndName" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT
        <include refid="roleColumns"/>
        FROM PAX_ROLE a
        WHERE a.name = #{name}
        AND a.del_flag = 0
        <choose>
            <when test="resellerId != null and resellerId > 0">
                AND a.reseller_id = #{resellerId}
            </when>
            <otherwise>
                AND a.reseller_id IS NULL
            </otherwise>
        </choose>
        LIMIT 1
    </select>

    <select id="findList" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT
        <include refid="roleColumns"/>
        FROM PAX_ROLE a
        <where>
            AND a.del_flag = 0
            <if test="name != null and name != ''">
                AND INSTR(a.name, #{name}) > 0
            </if>
            AND (
            a.reseller_id = #{resellerId}
            <if test="predefinedRoleIds != null and predefinedRoleIds.size > 0">
                OR a.id IN
                <foreach collection="predefinedRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            )
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="findResellerRoleListForVas" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT DISTINCT
        a.id AS "id",
        a.name AS "name",
        a.code AS "code"
        FROM PAX_ROLE a
        <where>
            AND a.del_flag = 0
            <choose>
                <when test="roleIds != null and roleIds.size > 0">
                    AND a.id IN
                    <foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
                        #{roleId}
                    </foreach>
                </when>
                <otherwise>
                    <choose>
                        <when test="role.resellerId != null and role.resellerId > 0">
                            AND (
                            a.reseller_id = #{role.resellerId}
                            <if test="role.predefinedRoleIds != null and role.predefinedRoleIds.size > 0">
                                OR a.id IN
                                <foreach collection="role.predefinedRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                                    #{roleId}
                                </foreach>
                            </if>
                            )
                        </when>
                        <otherwise>
                            AND a.reseller_id IS NULL
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </where>
        <choose>
            <when test="role.page !=null and role.page.orderBy != null and role.page.orderBy != ''">
                ORDER BY ${role.page.orderBy}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="findAllRoleList" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT
        a.id,
        a.name,
        a.code
        FROM (
            SELECT DISTINCT
            a.id AS "id",
            a.NAME AS "name",
            a.code AS "code"
            FROM PAX_ROLE a
            <where>
                AND a.del_flag = 0
                <choose>
                    <when test="resellerId != null and resellerId > 0">
                        AND (
                            a.reseller_id = #{resellerId}
                            <if test="predefinedRoleIds != null and predefinedRoleIds.size > 0">
                                OR a.id IN
                                <foreach collection="predefinedRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                                    #{roleId}
                                </foreach>
                            </if>
                        )
                    </when>
                    <otherwise>
                        AND a.reseller_id IS NULL
                    </otherwise>
                </choose>
            </where>
            UNION
            SELECT
            ${@com.pax.market.constants.RoleID@MERCHANT} AS "id",
            "Merchant" AS "name",
            "Merchant" AS "code"
            UNION
            SELECT
            ${@com.pax.market.constants.RoleID@DEVELOPER} AS "id",
            "Developer" AS "name",
            "Developer" AS "code"
        ) a
        <where>
            <if test="name != null and name != ''">
                INSTR(a.name, #{name}) > 0
            </if>
            <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
                AND a.id NOT IN
                <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
        order by a.name
    </select>

    <select id="findUserRoles" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT
        role.id AS "id",
        role.name AS "name",
        userRole.market_id AS "marketId",
        userRole.reseller_id AS "resellerId",
        NULL AS "merchantId",
        NULL AS "merchantName",
        NULL AS "developerId",
        NULL AS "developerType",
        NULL AS "mainDeveloper"
        FROM PAX_ROLE role
        JOIN PAX_USER_ROLE userRole ON userRole.role_id = role.id
        WHERE userRole.user_id = #{userId}
        AND role.del_flag = 0
        <if test="marketId != null and marketId >= -1">
            AND userRole.market_id = #{marketId}
        </if>
        <if test="resellerIds != null and resellerIds.size > 0">
            AND userRole.reseller_id IN
            <foreach collection="resellerIds" index="index" item="resellerId" open="(" separator="," close=")">
                #{resellerId}
            </foreach>
        </if>
        <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
            AND role.id NOT IN
            <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        ORDER BY marketId ASC, resellerId ASC, name ASC
        LIMIT 100
    </select>

    <select id="findAllRoleUserIds" resultType="java.lang.Long">
        SELECT
        DISTINCT userRole.user_id
        FROM PAX_ROLE role
        JOIN PAX_USER_ROLE userRole ON userRole.role_id = role.id
        WHERE role.del_flag = 0
        AND userRole.user_id IN
        <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
            AND userRole.role_id NOT IN
            <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
    </select>

    <select id="findByPrivilege" resultType="com.pax.market.domain.entity.market.role.Role">
        SELECT DISTINCT
        <include refid="roleColumns"/>
        FROM PAX_ROLE a
        JOIN PAX_ROLE_PRIVILEGE rolePrivilege ON rolePrivilege.role_id = a.id
        JOIN PAX_PRIVILEGE privilege ON rolePrivilege.privilege_id = privilege.id
        <where>
            AND a.del_flag = 0
            <choose>
                <when test="id != null and id > 0">
                    AND privilege.id = #{id}
                </when>
                <otherwise>
                    AND privilege.code = #{code}
                </otherwise>
            </choose>
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO PAX_ROLE (
            id,
            market_id,
			reseller_id,
			name,
			code,
			data_scope,
			created_by,
			created_date,
			updated_by,
			updated_date
		) VALUES (
            #{id},
			#{marketId},
			#{resellerId},
			#{name},
			#{code},
			#{dataScope},
			#{createdBy.id},
			#{createdDate},
			#{updatedBy.id},
			#{updatedDate}
		)
	</insert>

    <update id="update">
		UPDATE PAX_ROLE SET
			name = #{name},
			code = #{code},
			data_scope = #{dataScope},
			updated_by = #{updatedBy.id},
			updated_date = #{updatedDate}
		WHERE id = #{id}
	</update>

    <update id="delete">
		UPDATE PAX_ROLE
		SET del_flag = 1,
        updated_by = #{updatedBy.id},
		updated_date = #{updatedDate}
		WHERE id = #{id}
	</update>

    <insert id="createRoleUsers">
        REPLACE INTO PAX_USER_ROLE(
        user_id,
        role_id,
        market_id,
        reseller_id
        ) VALUES
        <foreach collection="userIds" index="index" item="userId" open="" separator="," close="">
            (#{userId}, #{roleId}, #{marketId}, #{resellerId})
        </foreach>
    </insert>

    <delete id="deleteRoleUsers">
        DELETE FROM PAX_USER_ROLE
        WHERE role_id = #{roleId}
        AND market_id = #{marketId}
        <if test="resellerId != null and resellerId >= -1">
            AND reseller_id = #{resellerId}
        </if>
        AND user_id IN
        <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="checkResellerAdminRoleUserExist" resultType="java.lang.Integer">
    	SELECT COUNT(*) FROM PAX_USER_ROLE
    	WHERE
    		role_id = #{roleId}
        	AND market_id = #{marketId}
            AND reseller_id = #{resellerId}
       		AND user_id = #{userId}
    </select>

    <delete id="deleteRoleUser">
        DELETE FROM PAX_USER_ROLE
        WHERE role_id = #{roleId}
        AND market_id = #{marketId}
        <if test="resellerId != null and resellerId >= -1">
            AND reseller_id = #{resellerId}
        </if>
        AND user_id = #{userId}
    </delete>

    <delete id="deleteUserRoles">
        DELETE a FROM PAX_USER_ROLE a
        WHERE a.market_id = #{marketId}
        <if test="resellerIds != null and resellerIds.size > 0">
            AND a.reseller_id IN
            <foreach collection="resellerIds" index="index" item="resellerId" open="(" separator="," close=")">
                #{resellerId}
            </foreach>
        </if>
        AND a.user_id IN
        <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <delete id="removeRoleForUsers">
        DELETE FROM PAX_USER_ROLE
        WHERE user_id IN
        <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND role_id NOT IN
        <foreach collection="excludedRoleIdList" index="index" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <select id="findRemoveRoleMarketIds" resultType="java.lang.Long">
        SELECT DISTINCT market_id FROM PAX_USER_ROLE
        WHERE user_id IN
        <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND role_id NOT IN
        <foreach collection="excludedRoleIdList" index="index" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <insert id="createRolePrivileges">
        INSERT INTO PAX_ROLE_PRIVILEGE(
        role_id,
        privilege_id
        ) VALUES
        <foreach collection="privilegeIds" index="index" item="privilegeId" open="" separator="," close="">
            (#{roleId}, #{privilegeId})
        </foreach>
    </insert>

    <delete id="deleteRolePrivileges">
        DELETE FROM PAX_ROLE_PRIVILEGE
        WHERE role_id = #{roleId}
        <if test="privilegeIds != null">
            AND privilege_id IN
            <foreach collection="privilegeIds" index="index" item="privilegeId" open="(" separator="," close=")">
                #{privilegeId}
            </foreach>
        </if>
    </delete>

    <select id="findRoleIds" resultType="java.lang.Long">
        SELECT DISTINCT role_id
        FROM pax_role_privilege
    </select>

    <select id="findPrivilegeIds" resultType="java.lang.Long">
        SELECT DISTINCT privilege_id
        FROM pax_role_privilege
        WHERE role_id=#{roleId} AND privilege_id IN
        <foreach collection="privilegeIds" index="index" item="privilegeId" open="(" separator="," close=")">
            #{privilegeId}
        </foreach>
    </select>

    <select id="existsRolePrivilege" resultType="java.lang.Boolean">
        SELECT EXISTS(SELECT *
                      FROM pax_role_privilege
                      WHERE role_id = #{roleId}
                        AND privilege_id = #{privilegeId})
    </select>

    <delete id="deleteRolePrivilege">
        DELETE
        FROM pax_role_privilege
        WHERE privilege_id = #{privilegeId}
    </delete>

    <update id="updateRolePrivilege">
        UPDATE pax_role_privilege
        SET privilege_id = #{privilegeId}
        WHERE privilege_id = #{id}
    </update>

    <delete id="deleteMarketUserRoles">
        DELETE a FROM PAX_USER_ROLE a
        WHERE a.market_id = #{marketId}
    </delete>

    <delete id="deleteResellerUserRoles">
        DELETE a FROM PAX_USER_ROLE a
        WHERE a.reseller_id = #{resellerId}
    </delete>

    <select id="findUserMarketInfoList" resultType="com.pax.market.dto.UserMarketInfo">
        SELECT DISTINCT
            userRole.user_id AS "userId",
            userRole.market_id AS "marketId",
            userRole.reseller_id AS "resellerId"
        FROM pax_user_role userRole
        JOIN PAX_ROLE role ON userRole.role_id = role.id AND role.del_flag = 0
        JOIN PAX_MARKET market ON market.id = userRole.market_id AND market.del_flag = 0
        <where>
            <if test="marketIds != null and marketIds.size > 0">
                AND market.id IN
                <foreach collection="marketIds" index="index" item="marketId" open="(" separator="," close=")">
                    #{marketId}
                </foreach>
            </if>
            <if test="resellerIds != null and resellerIds.size > 0">
                AND userRole.reseller_id IN
                <foreach collection="resellerIds" index="index" item="resellerId" open="(" separator="," close=")">
                    #{resellerId}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size > 0">
                AND userRole.user_id IN
                <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getBizCount" resultType="java.lang.Integer">
        SELECT count(1) FROM PAX_ROLE a
        WHERE a.reseller_id=#{resellerId}  AND a.del_flag = 0
    </select>

    <select id="getUserCountOfRole" resultType="java.lang.Integer">
        SELECT count(ur.user_id)
        FROM PAX_USER_ROLE ur
        JOIN PAX_ROLE role ON ur.role_id = role.id AND role.del_flag = 0
        join pax_user pu on pu.id =ur.user_id and pu.del_flag =0
        where
        ur.market_id = #{currentMarketId}
        AND ur.reseller_id = #{resellerId}
        AND ur.role_id = #{roleId}
    </select>

    <select id="isUserGlobalReadonly" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT 1
            FROM PAX_USER_ROLE ur
            WHERE ur.user_id = #{userId}
              AND ur.role_id = ${@com.pax.market.constants.RoleID@GLOBAL_VIEWER}
        ) AND NOT EXISTS(
            SELECT 1
            FROM PAX_USER_ROLE ur
            WHERE ur.user_id = #{userId}
              AND ur.market_id = ${marketId}
        )
    </select>

    <select id="findRoleUserIdList" resultType="java.lang.Long">
        SELECT DISTINCT ur.user_id AS "id"
        FROM PAX_USER_ROLE ur
        JOIN PAX_ROLE role ON ur.role_id = role.id AND role.del_flag = 0
        LEFT JOIN PAX_ROLE_PRIVILEGE rp ON rp.role_id = ur.role_id
        <where>
            ur.market_id = #{marketId}
            AND ur.reseller_id = #{resellerId}
            <if test="roleId != null">
                AND ur.role_id = #{roleId}
            </if>
            <if test="privilegeId != null">
                AND rp.privilege_id = #{privilegeId}
            </if>
        </where>
    </select>

    <select id="findPrivilegeUserIdList" resultType="java.lang.Long">
        SELECT DISTINCT ur.user_id AS "id"
        FROM PAX_USER_ROLE ur
        JOIN PAX_ROLE_PRIVILEGE rp ON rp.role_id = ur.role_id
        <where>
            ur.market_id = #{marketId}
            AND rp.privilege_id = #{privilegeId}
        </where>
    </select>

    <select id="isPrivilegeAssign2User" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT DISTINCT ur.user_id
            FROM PAX_USER_ROLE ur
            JOIN PAX_ROLE_PRIVILEGE rp ON rp.role_id = ur.role_id
            WHERE ur.user_id=#{userId}
            AND rp.privilege_id=#{privilegeId}
        )
    </select>

    <select id="isUserRoleExist" resultType="java.lang.Boolean">
        SELECT EXISTS(
        SELECT 1
        FROM PAX_USER_ROLE ur
        <where>
            ur.user_id = #{id}
            <if test="roleId != null and roleId > 0">
                AND ur.role_id = #{roleId}
            </if>
            <if test="roleIds !=null and roleIds.size > 0">
                AND ur.role_id in
                <foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="marketId != null">
                AND ur.market_id = #{marketId}
            </if>
            <if test="resellerId != null">
                AND ur.reseller_id = #{resellerId}
            </if>
            <if test="resellerIds !=null and resellerIds.size > 0">
                AND ur.reseller_id IN
                <foreach collection="resellerIds" index="index" item="resellerId" open="(" separator="," close=")">
                    #{resellerId}
                </foreach>
            </if>
            <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
                AND ur.role_id NOT IN
                <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
        )
    </select>

    <select id="findMarketUserIdList" resultType="java.lang.Long">
        SELECT DISTINCT userRole.user_id AS "id"
        FROM PAX_USER_ROLE userRole
        JOIN PAX_ROLE role ON role.id = userRole.role_id AND role.del_flag = 0
        <where>
            <if test="marketIds != null and marketIds.size > 0">
                AND userRole.market_id IN
                <foreach collection="marketIds" index="index" item="marketId" open="(" separator="," close=")">
                    #{marketId}
                </foreach>
            </if>
            <if test="roleIds !=null and roleIds.size > 0">
                AND role.id in
                <foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="resellerIds !=null and resellerIds.size > 0">
                AND userRole.reseller_id in
                <foreach collection="resellerIds" item="resellerId" open="(" separator="," close=")">
                    #{resellerId}
                </foreach>
            </if>
            <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
                AND userRole.role_id NOT IN
                <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findUserIdForAnnouncement" resultType="java.lang.Long">
        SELECT DISTINCT userRole.user_id AS "id"
        FROM PAX_USER_ROLE userRole
        JOIN PAX_ROLE role ON role.id = userRole.role_id
        <where>
            role.del_flag = 0
            <if test="marketId != null and marketId != -1">
                AND userRole.market_id = #{marketId}
            </if>
            <if test="roleIds !=null and roleIds.size > 0">
                AND role.id in
                <foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findUserIdByMarketIdAndRoleId" resultType="java.lang.Long">
        SELECT DISTINCT userRole.user_id AS "id"
        FROM PAX_USER_ROLE userRole
        JOIN PAX_ROLE role ON role.id = userRole.role_id
        <where>
            role.del_flag = 0
            <if test="marketId != null and marketId != ''">
                AND userRole.market_id = #{marketId}
            </if>
            <if test="roleIds !=null and roleIds.size > 0">
                AND role.id in
                <foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="resellerId !=null and resellerId != ''">
                AND userRole.reseller_id = #{resellerId}
            </if>
        </where>
    </select>

    <select id="findUserResellerIdList" resultType="java.lang.Long">
        SELECT
        DISTINCT ur.reseller_id
        FROM PAX_USER_ROLE ur
        <where>
            <if test="userId != null and userId != ''">
                ur.user_id = #{userId}
            </if>
            <if test="marketIds != null and marketIds.size > 0">
                AND ur.market_id IN
                <foreach collection="marketIds" index="index" item="marketId" open="(" separator="," close=")">
                    #{marketId}
                </foreach>
            </if>
            <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
                AND ur.role_id NOT IN
                <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
        <if test="userId != null and userId != ''">
            LIMIT ${@com.pax.market.domain.util.SystemPropertyHelper@getUserResellerMerchantRetrieveLimit()}
        </if>
    </select>



    <select id="checkUserRole" resultType="java.lang.Boolean">
        SELECT EXISTS(
        SELECT 1
        FROM PAX_USER_ROLE ur
        <where>
            ur.user_id = #{id}
            <if test="roleId != null and roleId > 0">
                AND ur.role_id = #{roleId}
            </if>
            <if test="roleIds !=null and roleIds.size > 0">
                AND ur.role_id in
                <foreach collection="roleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="marketId != null">
                AND ur.market_id = #{marketId}
            </if>
            <if test="resellerId != null">
                AND ur.reseller_id = #{resellerId}
            </if>
            <if test="excludeRoleIds != null and excludeRoleIds.size > 0">
                AND ur.role_id NOT IN
                <foreach collection="excludeRoleIds" index="index" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
        )
    </select>
</mapper>