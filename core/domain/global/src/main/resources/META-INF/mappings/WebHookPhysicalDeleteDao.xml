<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.cleardata.WebHookPhysicalDeleteDao">

    <select id="findDeletedWebHookIds" resultType="java.lang.Long">
        SELECT id
        FROM pax_web_hook a
        WHERE a.market_id = #{marketId}
        <if test="deleteDays != null">
            AND a.del_flag = 1
            AND TIMESTAMPADD(DAY, #{deleteDays}, a.updated_date) &lt;= CURRENT_TIMESTAMP
        </if>
    </select>

    <delete id="deleteWebHook">
        DELETE FROM pax_web_hook
        WHERE id = #{webHookId}
    </delete>

    <select id="findDeletedWebHookMessageHistoryIds" resultType="java.lang.Long">
        SELECT id
        FROM pax_web_hook_message_history
        WHERE web_hook_id = #{webHookId}
    </select>

    <delete id="deleteWebHookMessageHistory">
        DELETE FROM pax_web_hook_message_history
        WHERE id = #{id}
    </delete>

    <select id="getWebHookHistoryCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM pax_web_hook_message_history a
        <where>
            <if test="deleteDays != null">
                 TIMESTAMPADD(DAY, #{deleteDays}, a.created_date) &lt;= CURRENT_TIMESTAMP
            </if>
        </where>
    </select>

    <select id="findDeletedWebHookMessageHistoryIdsByDeleteDays" resultType="java.lang.Long">
        SELECT id
        FROM pax_web_hook_message_history a
        <where>
            <if test="startId != null">
                a.id > #{startId}
            </if>
            <if test="deleteDays != null">
              AND TIMESTAMPADD(DAY, #{deleteDays}, a.created_date) &lt;= CURRENT_TIMESTAMP
            </if>
        </where>
        ORDER BY created_date ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <delete id="deleteWebHookMessageHistoryByIds">
        DELETE FROM pax_web_hook_message_history
        WHERE
        id IN
        <foreach collection="historyIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>