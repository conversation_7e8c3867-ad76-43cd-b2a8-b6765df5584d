<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.app.IndustrySolutionAppRegistryDao">

    <sql id="filterOnlineSolutionApp">
                AND app.`status` = 'A'
                JOIN pax_apk apk ON apk.id = r.apk_id
                AND apk.`status` = 'O'
                JOIN pax_apk_detail detail ON detail.apk_id = apk.id
                JOIN pax_app_to_service s ON s.app_id = app.id
                AND s.STATUS != 'D'
	            AND s.market_id = #{currentMarketId}
    </sql>

    <select id="getOnlineSolutionApp" resultType="com.pax.market.domain.entity.global.app.SolutionApp">
        SELECT
        app.id AS "id",
        app.STATUS AS "status",
        app.package_name AS "packageName",
        app.auto_update AS "autoUpdate",
        apk.id AS "latestApk.id",
        detail.id AS "latestApk.apkDetail.id",
        detail.app_name as "latestApk.apkDetail.appName"
        FROM
        pax_vas_app_registry r
        JOIN pax_app app ON r.id = app.id AND r.id = #{appId}
        <include refid="filterOnlineSolutionApp" />
    </select>


    <select id="isOnlineSolutionAppExistByPackageName" resultType="java.lang.Boolean">
        SELECT count(*)
        FROM
        pax_vas_app_registry r
        JOIN pax_app app ON r.id = app.id AND app.package_name = #{packageName}
        <include refid="filterOnlineSolutionApp" />
    </select>

    <select id="findSolutionAppsForUsage" resultType="com.pax.market.domain.entity.global.app.App">
        SELECT
            app.id AS "id",
            app.package_name AS "packageName",
            app.dev_id AS "developer.id"
        FROM
            pax_vas_app_registry r
                JOIN pax_app app ON r.id = app.id
                AND app.market_id = -1
                AND app.type = 'S'
        ORDER BY app.created_date
    </select>


</mapper>