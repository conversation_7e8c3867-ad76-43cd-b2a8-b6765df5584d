<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ /*
  ~  * *******************************************************************************
  ~  * COPYRIGHT
  ~  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~  *   This software is supplied under the terms of a license agreement or
  ~  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~  *   or disclosed except in accordance with the terms in that agreement.
  ~  *
  ~  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~  * *******************************************************************************
  ~  */
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.client.ClientApkPublishDao">

    <select id="findPublishMarketList" resultType="com.pax.market.domain.entity.global.market.Market">
        SELECT
        m.id AS "id",
        m.name AS "name"
        FROM PAX_CLIENT_APK_GLOBAL_PUBLISH p
        JOIN PAX_CLIENT_APK a ON p.apk_id = a.id
        JOIN PAX_MARKET m ON p.market_id = m.id
        WHERE p.apk_id = #{id}
    </select>

    <select id="findMarketList" resultType="com.pax.market.vo.admin.clientapp.ClientApkMarketVo">
        SELECT
        m.id AS "id",
        m.name AS "name",
        IFNULL(publishAmount.amount, -1) AS "publishAmount",
        IF(globalPublish.apk_id IS NOT NULL, 1, 0) AS "specified"
        FROM PAX_MARKET m
        LEFT JOIN pax_client_apk_publish_amount publishAmount ON m.id = publishAmount.market_id AND publishAmount.apk_id = #{clientApkId}
        LEFT JOIN pax_client_apk_global_publish globalPublish ON m.id = globalPublish.market_id AND globalPublish.apk_id = #{clientApkId}
        WHERE m.del_flag = 0
        AND m.id > 0
        <if test="name != null and name != ''">
            AND INSTR(m.name, #{name}) > 0
        </if>
        <if test="limited != null and limited == true">
            AND publishAmount.amount IS NOT NULL
        </if>
        <if test="limited != null and limited == false">
            AND publishAmount.amount IS NULL
        </if>
        <if test="specified != null and specified == true">
            AND globalPublish.apk_id IS NOT NULL
        </if>
        <if test="specified != null and specified == false">
            AND globalPublish.apk_id IS NULL
        </if>
        ORDER BY m.id
    </select>

    <select id="isGlobalApkPublished" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM PAX_CLIENT_APK_GLOBAL_PUBLISH
        WHERE apk_id = #{clientApkId}
        AND market_id = #{marketId}
    </select>

    <insert id="addGlobalApkPublish">
        INSERT INTO PAX_CLIENT_APK_GLOBAL_PUBLISH (
        apk_id,
        market_id
        ) VALUES (
        #{clientApkId},
        #{marketId}
        )
    </insert>

    <delete id="removeGlobalApkPublish">
        DELETE FROM PAX_CLIENT_APK_GLOBAL_PUBLISH
        WHERE apk_id = #{clientApkId}
        AND market_id = #{marketId}
    </delete>

    <select id="getClientPublishAmount" resultType="java.lang.Integer">
        SELECT amount
        FROM pax_client_apk_publish_amount
        WHERE apk_id = #{clientApkId}
        AND market_id = #{marketId}
    </select>

    <insert id="createClientPublishAmount">
        INSERT INTO pax_client_apk_publish_amount (
        apk_id,
        market_id,
        amount
        ) VALUES (
        #{clientApkId},
        #{marketId},
        #{amount}
        )
    </insert>

    <insert id="updateClientPublishAmount">
        UPDATE pax_client_apk_publish_amount
        SET amount = #{amount}
        WHERE apk_id = #{clientApkId}
        AND market_id = #{marketId}
    </insert>

    <delete id="deleteClientPublishAmount">
        DELETE FROM pax_client_apk_publish_amount
        WHERE apk_id = #{clientApkId}
        AND market_id = #{marketId}
    </delete>
</mapper>