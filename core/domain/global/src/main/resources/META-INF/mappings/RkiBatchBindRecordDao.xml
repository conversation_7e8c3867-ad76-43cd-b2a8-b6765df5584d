<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.rki.RkiBatchBindRecordDao">

    <sql id="recordColumns">
        a.id AS "id",
        a.market_id AS "marketId",
        a.rki_id AS "marketRkiServerConfig.id",
        a.group_rki_key_id AS "terminalGroupRki.id",
        a.terminal_rki_key_id AS "terminalRki.id",
        a.task_id AS "taskId",
        a.key_id AS "keyId",
        a.req_date as "requestDate",
        a.res_result as "responseResult",
        a.res_msg AS "responseMsg",
        a.res_count AS "responseCount",
        a.created_by AS "createdBy.id",
        a.created_date AS "createdDate",
        a.updated_by AS "updatedBy.id",
        a.updated_date AS "updatedDate"
    </sql>

    <select id="getByTaskId" resultType="com.pax.market.domain.entity.global.rki.RkiBatchBindRecord">
        SELECT
        <include refid="recordColumns"/>
        FROM PAX_RKI_BATCH_BIND_RECORD a
        WHERE a.task_id = #{taskId}
        LIMIT 1
    </select>

    <select id="get" resultType="com.pax.market.domain.entity.global.rki.RkiBatchBindRecord">
        SELECT
        <include refid="recordColumns"/>
        FROM PAX_RKI_BATCH_BIND_RECORD a
        WHERE a.id = #{id}
        LIMIT 1
    </select>

    <select id="getByGroupRkiId" resultType="com.pax.market.domain.entity.global.rki.RkiBatchBindRecord">
        SELECT
        <include refid="recordColumns"/>
        FROM PAX_RKI_BATCH_BIND_RECORD a
        WHERE a.group_rki_key_id = #{referenceId}
        LIMIT 1
    </select>

    <select id="getByTerminalRkiId" resultType="com.pax.market.domain.entity.global.rki.RkiBatchBindRecord">
        SELECT
        <include refid="recordColumns"/>
        FROM PAX_RKI_BATCH_BIND_RECORD a
        WHERE a.terminal_rki_key_id = #{referenceId}
        LIMIT 1
    </select>

    <select id="isTaskIdAlreadyExists" resultType="java.lang.Boolean">
        SELECT DISTINCT 1
        FROM PAX_RKI_BATCH_BIND_RECORD a
        WHERE a.rki_id = #{rkiServerId}
        AND a.task_id = #{taskId}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO PAX_RKI_BATCH_BIND_RECORD(
            id,
            market_id,
			rki_id,
			group_rki_key_id,
			terminal_rki_key_id,
			task_id,
			key_id,
			req_date,
			res_result,
			res_msg,
			res_count,
			created_by,
			created_date,
			updated_by,
			updated_date
		) VALUES (
            #{id},
			#{marketId},
			#{marketRkiServerConfig.id},
			#{terminalGroupRki.id},
			#{terminalRki.id},
			#{taskId},
			#{keyId},
			#{requestDate},
			#{responseResult},
			#{responseMsg},
			#{responseCount},
			#{createdBy.id},
			#{createdDate},
			#{updatedBy.id},
			#{updatedDate}
		)
	</insert>

</mapper>