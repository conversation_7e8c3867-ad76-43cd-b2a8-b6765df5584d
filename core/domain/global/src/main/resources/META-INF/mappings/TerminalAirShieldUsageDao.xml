<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.vas.TerminalAirShieldUsageDao">


    <select id="getMarketTotalUsage" resultType="java.lang.Long">
        SELECT
        SUM( d.amount )
        FROM
        `pax_airshield_market_summary_detail`
        <where>
            `period` = #{period}
            <if test="marketId!=null and marketId>0">
                AND `market_id` = #{marketId}
            </if>
        </where>

    </select>


    <select id="findHistoryUsageByPeriod" resultType="com.pax.market.dto.vas.AirShieldUsageInfo">
        SELECT
        `period`,
        SUM( amount ) AS amount
        from pax_airshield_market_summary_detail
        where monthly_usage=true
        <if test="marketId!=null and marketId!=-1">
            and market_id=#{marketId}
        </if>
        GROUP BY `period`
        <choose>
            <when test="page!=null and page.orderBy!=null and  page.orderBy!='' ">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY `period` DESC
            </otherwise>
        </choose>
    </select>

    <select id="getAirShieldFileId" resultType="java.lang.String">
        SELECT
        <choose>
            <when test="globalOperation">
                IFNULL(`global_snapshot_fileid`,`snapshot_fileid`)
            </when>
            <otherwise>
                `snapshot_fileid`
            </otherwise>
        </choose>
        FROM
        pax_airshield_market_summary_detail
        WHERE
        market_id = #{marketId}
        AND period = #{period}
    </select>
    <select id="findFileList" resultType="com.pax.market.dto.vas.AirShieldUsageInfo">

        SELECT
            d.market_id AS marketId,
            d.snapshot_fileid AS snapshotFileId,
            d.global_snapshot_fileid as globalSnapshotFileid
        FROM
            `pax_airshield_market_summary_detail` d
        WHERE
            d.`period` = #{period}
          AND d.monthly_usage = 1

    </select>
    <select id="findMarketHistoryUsage" resultType="com.pax.market.dto.vas.AirShieldUsageInfo">

        SELECT
        m.id AS marketId,
        m.NAME AS marketName,
        m.region AS region,
        m.country AS country,
        m.agent AS agent,
        d.amount AS amount,
        d.snapshot_fileid AS snapshotFileId,
        d.global_snapshot_fileid as globalSnapshotFileid
        FROM
        pax_airshield_market_summary_detail d
        JOIN pax_market m ON m.id = d.market_id
        WHERE
        d.`period` = #{period}
        <if test="name != null and name != ''">
            AND INSTR(m.name , #{name}) > 0
        </if>
        <if test="page !=null and page.orderBy != null and page.orderBy != ''">
            ORDER BY ${page.orderBy}
        </if>

    </select>


</mapper>