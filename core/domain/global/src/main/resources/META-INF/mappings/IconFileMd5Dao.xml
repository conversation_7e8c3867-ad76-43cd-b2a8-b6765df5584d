<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.app.IconFileMd5Dao">


    <select id="get" resultType="com.pax.market.domain.entity.global.app.IconFileMd5">
        SELECT
        id AS "id",
        icon_file_id AS "iconFileId",
        md5 AS "md5"
        FROM PAX_ICON_MD5
        WHERE id = #{id}
    </select>

    <select id="getByMd5" resultType="com.pax.market.domain.entity.global.app.IconFileMd5">
        SELECT
        id AS "id",
        icon_file_id AS "iconFileId",
        md5 AS "md5"
        FROM PAX_ICON_MD5
        WHERE md5 = #{md5}
        LIMIT 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO PAX_ICON_MD5(
          icon_file_id,
          md5
        ) VALUES (
          #{iconFileId},
          #{md5}
        )
    </insert>

    <update id="update">
		UPDATE PAX_ICON_MD5 SET
            icon_file_id = #{iconFileId},
            md5 = #{md5}
		WHERE id = #{id}
	</update>

    <delete id="delete">
		DELETE FROM PAX_ICON_MD5
		WHERE id = #{id}
	</delete>
</mapper>