package com.pax.market.domain.event;

import com.pax.api.eventbus.AbstractEvent;

public class ReportActivateEvent extends AbstractEvent {

    private Long reportId;

    public ReportActivateEvent(Long reportId) {
        this.reportId = reportId;
    }

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    @Override
    public String toString() {
        return "ReportActivateEvent{" +
                "reportId=" + reportId +
                '}';
    }
}
