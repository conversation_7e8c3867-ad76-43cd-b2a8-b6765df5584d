/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.domain.searchcriteria;

import com.pax.market.domain.entity.report.FirmwareDetailReport;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Setter
@Getter
public class FirmwareDetailReportCriteria extends TerminalSearchCriteria<FirmwareDetailReport> {
    private static final long serialVersionUID = 6191128399450619514L;

    private Long marketId;
    private Set<String> firmwareNames;
    private String fmType;

}
