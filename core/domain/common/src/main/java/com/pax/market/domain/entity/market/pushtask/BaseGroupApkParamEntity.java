/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */
package com.pax.market.domain.entity.market.pushtask;

/**
 * 分组应用参数Entity
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
public class BaseGroupApkParamEntity<T extends BaseGroupApkParamEntity<?>> extends BaseGroupPushTaskEntity<T> implements Comparable<T> {

    private static final long serialVersionUID = 1L;
    private String paramTemplateName;
    private String paramTemplateId;
    private String param;
    private String downloadUrl;
    private String md5;
    private String sha256;
    private Long fileSize;
    private Boolean hasVariables;

    /**
     * Instantiates a new Base apk param entity.
     */
    public BaseGroupApkParamEntity() {
        super();
    }

    /**
     * Instantiates a new Base apk param entity.
     *
     * @param id the id
     */
    public BaseGroupApkParamEntity(Long id) {
        super(id);
    }

    /**
     * Gets param template name.
     *
     * @return the param template name
     */
    public String getParamTemplateName() {
        return paramTemplateName;
    }

    /**
     * Sets param template name.
     *
     * @param paramTemplateName the param template name
     */
    public void setParamTemplateName(String paramTemplateName) {
        this.paramTemplateName = paramTemplateName;
    }

    /**
     * Gets param template id
     *
     * @return the string
     */
    public String getParamTemplateId() {
        return paramTemplateId;
    }

    /**
     * Sets param template id
     *
     * @param paramTemplateId the param template id
     */
    public void setParamTemplateId(String paramTemplateId) {
        this.paramTemplateId = paramTemplateId;
    }

    /**
     * Gets param.
     *
     * @return the param
     */
    public String getParam() {
        return param;
    }

    /**
     * Sets param.
     *
     * @param param the param
     */
    public void setParam(String param) {
        this.param = param;
    }

    /**
     * Gets download url.
     *
     * @return the download url
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }

    /**
     * Sets download url.
     *
     * @param downloadUrl the download url
     */
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    /**
     * Gets md 5.
     *
     * @return the md 5
     */
    public String getMd5() {
        return md5;
    }

    /**
     * Sets md 5.
     *
     * @param md5 the md 5
     */
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    /**
     * Gets sha 256.
     *
     * @return the sha 256
     */
    public String getSha256() {
        return sha256;
    }

    /**
     * Sets sha 256.
     *
     * @param sha256 the sha 256
     */
    public void setSha256(String sha256) {
        this.sha256 = sha256;
    }

    /**
     * Gets file size.
     *
     * @return the file size
     */
    public Long getFileSize() {
        return fileSize;
    }

    /**
     * Sets file size.
     *
     * @param fileSize the file size
     */
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    /**
     * Gets has variables.
     *
     * @return the has variables
     */
    public Boolean getHasVariables() {
        return hasVariables;
    }

    /**
     * Sets has variables.
     *
     * @param hasVariables the has variables
     */
    public void setHasVariables(Boolean hasVariables) {
        this.hasVariables = hasVariables;
    }

    @Override
    public int compareTo(BaseGroupApkParamEntity o) {
        if (getCreatedDate() != null && o.getCreatedDate() != null) {
            return -getCreatedDate().compareTo(o.getCreatedDate());
        } else {
            return 0;
        }
    }
}