package com.pax.market.domain.entity.global.billing;

import com.pax.market.framework.common.excel.annotation.ExcelField;
import com.pax.market.framework.common.persistence.DataEntity;

import java.util.Date;

public class DiscountTerminal extends DataEntity<DiscountTerminal> {
    private static final long serialVersionUID = 6764084894462008986L;

    private String customer;
    private String pn;
    private String model;
    private int quantity;
    private String serialNo;
    private String purpose;
    private Date shipmentDate;

    @ExcelField(title = "title.customer.name", align = 2, sort = 1)
    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    @ExcelField(title = "title.pn", align = 2, sort = 2)
    public String getPn() {
        return pn;
    }

    public void setPn(String pn) {
        this.pn = pn;
    }

    @ExcelField(title = "title.model", align = 2, sort = 3)
    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    @ExcelField(title = "title.shipped.quantity", align = 2, sort = 4)
    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @ExcelField(title = "title.sn", align = 2, sort = 1)
    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    @ExcelField(title = "title.purpose", align = 2, sort = 5)
    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    @ExcelField(title = "title.shipment.date", align = 2, sort = 6, dateFormat = "yyyy-MM-dd HH:mm:ss")
    public Date getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(Date shipmentDate) {
        this.shipmentDate = shipmentDate;
    }
}
