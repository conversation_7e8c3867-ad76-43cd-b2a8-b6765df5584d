/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.domain.entity.global.report;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/12/26 14:01:03
 */

@Getter
@Setter
public class ReportJobHistory extends DataEntity<ReportJobHistory> {

    /**
     *
     */
    private static final long serialVersionUID = 8469033341490115560L;

    private Long reportJobId;
    private String reportFileName;
    private Date executeTime;
    private Boolean isSuccess;
    private String emailTo;
    private String reportFileId;

    public Boolean getIsSuccess() {
        return this.isSuccess;
    }

    public void setIsSuccess(Boolean isSuccess) {
        this.isSuccess = isSuccess;
    }

}
