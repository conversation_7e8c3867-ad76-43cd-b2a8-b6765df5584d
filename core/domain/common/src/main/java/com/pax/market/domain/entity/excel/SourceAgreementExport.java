package com.pax.market.domain.entity.excel;

import com.pax.market.framework.common.excel.annotation.ExcelField;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SourceAgreementExport extends GlobalAgreementExport {
    @Serial
    private static final long serialVersionUID = 3988386433001602291L;

    private String clientId;

    @ExcelField(title = "title.agreed.by", align = 1, sort = 1)
    public String getAcceptor() {
        return super.getAcceptor();
    }

    @ExcelField(title = "title.agreed.time", align = 1, sort = 2, dateFormat = "yyyy-MM-dd HH:mm:ss")
    public Date getAgreeTime() {
        return super.getAgreeTime();
    }

    @ExcelField(title = "title.source", align = 1, sort = 3)
    public String getClientId() {
        return clientId;
    }
}
