package com.pax.market.domain.entity.global.report;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ResellerTerminal extends DataEntity<ResellerTerminal> {
    /**
     *
     */
    private static final long serialVersionUID = 5239464890522148367L;
    private String marketName;
    private String resellerName;
    private int terminalCount;
    private int merchantCount;
    private Long resellerId;
    private ResellerTerminalModel resellerTerminalModel;

    @Getter
    @Setter
    public class ResellerTerminalModel implements Serializable {
        /**
         *
         */
        private static final long serialVersionUID = 4826529919571990146L;
        private String modelName;
        private int terminalCount;
    }
}
