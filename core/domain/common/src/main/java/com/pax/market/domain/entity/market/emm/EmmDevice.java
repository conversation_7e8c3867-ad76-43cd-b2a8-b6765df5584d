package com.pax.market.domain.entity.market.emm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.domain.entity.global.emm.BaseEmmDeviceEntity;
import com.pax.market.domain.entity.global.emm.EmmModel;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.framework.common.audit.AuditAware;
import com.pax.market.framework.common.excel.annotation.ExcelField;
import com.pax.market.framework.common.persistence.idgen.SnowflakeIdGeneratorAware;
import com.pax.market.framework.common.utils.StringUtils;

import java.io.Serial;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/5/16
 */
public class EmmDevice extends BaseEmmDeviceEntity<EmmDevice> implements SnowflakeIdGeneratorAware, AuditAware {

    @Serial
    private static final long serialVersionUID = -7298908303048847477L;

    private String resellerName;
    private String merchantName;
    private String modelName;
    private String factoryName;

    private Set<String> names;
    private Set<String> serialNos;
    private Set<Long> deviceResellerIds;
    private Date startTime;
    private Date endTime;
    private String number;

    public EmmDevice() {
        super();
    }

    public EmmDevice(Long id) {
        super(id);
    }

    public EmmDevice(String serialNo) {
        super();
        this.setSerialNo(serialNo);
    }

    @ExcelField(title = "title.imei", align = 2, sort = 1)
    public String getImei() {
        return super.getImei();
    }

    @ExcelField(title = "title.serial.no", align = 2, sort = 2)
    public String getSerialNo() {
        return super.getSerialNo();
    }

    @JsonIgnore
    public String getReverseSerialNo() {
        return StringUtils.reverse(getSerialNo());
    }

    @ExcelField(title = "title.model", align = 2, sort = 4, objectConvertorBeanName = "emmModelType")
    public EmmModel getModel() {
        return super.getModel();
    }

    public String getModelName() {
        if (StringUtils.isNotBlank(modelName)) {
            return modelName;
        }
        return super.getModelName();
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    @ExcelField(title = "title.reseller", align = 2, sort = 5, objectConvertorBeanName = "resellerType")
    public Reseller getReseller() {
        return super.getReseller();
    }

    @ExcelField(title = "title.merchant", align = 2, sort = 6, objectConvertorBeanName = "merchantType")
    public Merchant getMerchant() {
        return super.getMerchant();
    }

    @ExcelField(title = "title.manufacturer", align = 2, sort = 3)
    public String getFactoryName() {
        if (StringUtils.isNotBlank(factoryName)) {
            return factoryName;
        }
        return super.getFactoryName();
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }


    @ExcelField(title = "title.status", type = 1, align = 2, sort = 8, objectConvertorBeanName = "emmDeviceStatusType")
    public String getStatus() {
        if (isDeleted()) {
            return "D";
        }
        return super.getStatus();
    }

    public String getName() {
        return super.getDisplayName();
    }

    @JsonIgnore
    public String getReverseDisplayName() {
        return StringUtils.reverse(getDisplayName());
    }

    public String getResellerName() {
        if (StringUtils.isNotBlank(resellerName)) {
            return resellerName;
        } else if (getReseller() != null) {
            return getReseller().getName();
        }
        return null;
    }

    public void setResellerName(String resellerName) {
        this.resellerName = resellerName;
    }

    public String getMerchantName() {
        if (StringUtils.isNotBlank(merchantName)) {
            return merchantName;
        } else if (getMerchant() != null) {
            return getMerchant().getName();
        }
        return null;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }


    public Set<String> getSerialNos() {
        return serialNos;
    }

    public void setSerialNos(Set<String> serialNos) {
        this.serialNos = serialNos;
    }

    public Set<Long> getDeviceResellerIds() {
        return deviceResellerIds;
    }


    public void setDeviceResellerIds(Set<Long> deviceResellerIds) {
        this.deviceResellerIds = deviceResellerIds;
    }

    public Set<String> getNames() {
        return names;
    }

    public void setNames(Set<String> names) {
        this.names = names;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    @ExcelField(title = "title.enrollment.time", type = 1, align = 2, sort = 9, dateFormat = "yyyy-MM-dd HH:mm:ss")
    public Date getEmmDeviceRegisterTime() {
        return super.getRegisterTime();
    }

    @Override
    @JsonIgnore
    public int getAuditActionType() {
        return AuditTypes.TERMINAL;
    }
}
