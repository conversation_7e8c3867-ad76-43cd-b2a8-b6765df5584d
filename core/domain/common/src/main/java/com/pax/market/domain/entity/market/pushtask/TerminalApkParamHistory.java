package com.pax.market.domain.entity.market.pushtask;

import com.pax.market.domain.constants.RedisIdGeneratorAwareTable;
import com.pax.market.framework.common.persistence.DataEntity;
import com.pax.market.framework.common.persistence.idgen.RedisIdGeneratorAware;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
public class TerminalApkParamHistory extends DataEntity<TerminalApkParamHistory> implements RedisIdGeneratorAware {
    @Serial
    private static final long serialVersionUID = -7919954379703292669L;
    private Long terminalId;
    private Long appId;
    private Long apkId;
    private String paramTemplateName;
    private int actionType;
    private Long referenceId;
    private String paramVariables;
    private String convertedParamVariables;
    private String localParam;
    private String from;

    @Override
    public String getTableName() {
        return RedisIdGeneratorAwareTable.PAX_TERMINAL_APK_PARAM_HISTORY.getValue();
    }
}
