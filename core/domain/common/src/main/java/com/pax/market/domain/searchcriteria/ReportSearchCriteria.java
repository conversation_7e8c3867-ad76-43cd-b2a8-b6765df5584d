/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.domain.searchcriteria;

import com.pax.market.constants.ReportStatus;
import com.pax.market.domain.entity.global.report.Report;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/31 11:14:18
 */

@Getter
@Setter
public class ReportSearchCriteria extends Report {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private Long categoryId;
    private List<Long> reportIds;
    private Boolean checkPrivilege = false;
    private Boolean isSuperAdmin;
    private Boolean isMarketAdmin;
    private Boolean isResellerAdmin;

    public void setStatus(ReportStatus status) {
        if (status != null) {
            setStatus(status.val());
        }
    }

}
