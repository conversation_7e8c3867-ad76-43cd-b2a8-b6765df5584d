package com.pax.market.domain.entity.global.vas.airlink;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pax.market.constants.airlink.AirLinkEstateStatus;
import com.pax.market.framework.common.audit.AuditAware;
import com.pax.market.framework.common.excel.annotation.ExcelField;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.constants.airlink.AirLinkEstateStatus;
import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/4/21 13:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AirLinkEstate extends DataEntity<AirLinkEstate> implements AuditAware {
    private String imei;
    private String marketName;
    private AirLinkEstateStatus status;

    @ExcelField(title = "title.imei")
    public String getImei() {
        return imei;
    }

    @Override
    @JsonIgnore
    public int getAuditActionType() {
        return AuditTypes.AIR_LINK_ESTATE;
    }
}
