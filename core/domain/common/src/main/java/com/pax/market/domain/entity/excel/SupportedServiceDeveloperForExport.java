package com.pax.market.domain.entity.excel;

import com.pax.market.framework.common.excel.annotation.ExcelField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/11
 */
public class SupportedServiceDeveloperForExport extends MarketDeveloperForExport {

    private static final long serialVersionUID = -7728695883445739679L;

    @ExcelField(title = "title.enterprise.developer", align = 2, sort = 1)
    public String getDeveloperNickName() {
        return super.getDeveloperNickName();
    }

    @ExcelField(title = "title.company.email", align = 2, sort = 2)
    public String getCompanyEmail() {
        return super.getCompanyEmail();
    }

    @ExcelField(title = "title.number.integrated.sdk.apps", align = 2, sort = 3)
    public Integer getIntegratedSdkAppNum() {
        return super.getIntegratedSdkAppNum();
    }

    @ExcelField(title = "title.integrated.sdk.apps", align = 2, sort = 4, objectConvertorBeanName = "integratedSDKAppsConvertor", autoNewLine = true)
    public List<String> getIntegratedSdkApps() {
        return super.getIntegratedSdkApps();
    }

    @ExcelField(title = "title.status", align = 2, sort = 5, objectConvertorBeanName = "marketDeveloperStatusType")
    public String getStatus() {
        return super.getStatus();
    }

}
