package com.pax.market.domain.entity.global.report;


import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
public class ReportTemplate extends DataEntity<ReportTemplate> {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long reportId;
    private String name;
    private String outputFormat;
    private String status;
    private String templateFileId;
    private boolean isDefault;
    private String documentName;
    private boolean predefined;
    private String description;

    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }
}
