/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain.entity.global.terminal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.framework.common.audit.AuditAware;
import com.pax.market.framework.common.excel.annotation.ExcelField;
import com.pax.market.framework.common.persistence.DataEntity;
import com.pax.market.framework.common.utils.LongUtils;

import java.util.Set;

/**
 * The type Terminal stock stock.
 *
 * <AUTHOR>
 * @date 2018 /9/4
 */
public class TerminalStock extends DataEntity<TerminalStock> implements AuditAware {

    private static final long serialVersionUID = 7604177826303615739L;
    private String serialNo;
    private String factory;
    private Model model;
    private Market market;

    private Set<String> modelIdsFilter;     //查询条件，查询多个机型
    private Set<String> terminalIdsFilter;     //查询条件，查询多个终端
    private Set<String> marketIdsFilter;     //查询条件，查询多个market

    @Override
    @JsonIgnore
    public int getAuditActionType() {
        return AuditTypes.TERMINAL_DEVICES_STOCK;
    }


    /**
     * Instantiates a new Terminal stock stock.
     */
    public TerminalStock() {
    }

    /**
     * Instantiates a new Terminal stock stock.
     *
     * @param id the id
     */
    public TerminalStock(Long id) {
        super(id);
    }

    /**
     * Gets serial no.
     *
     * @return the serial no
     */
    @ExcelField(title = "title.serial.no", align = 2, sort = 4)
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * Sets serial no.
     *
     * @param serialNo the serial no
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    /**
     * Gets factory.
     *
     * @return the factory
     */
    @ExcelField(title = "title.manufacturer", align = 2, sort = 5, type = 2)
    public String getFactory() {
        return factory;
    }

    /**
     * Sets factory.
     *
     * @param factory the factory
     */
    public void setFactory(String factory) {
        this.factory = factory;
    }

    /**
     * Gets factory for export.
     *
     * @return the factory for export
     */
    @ExcelField(title = "title.manufacturer", align = 2, sort = 5, type = 1)
    public String getFactoryForExport() {
        if (model != null) {
            return this.model.getFactory().getName();
        } else {
            return null;
        }
    }

    /**
     * Gets model.
     *
     * @return the model
     */
    @ExcelField(title = "title.model", align = 2, sort = 6, objectConvertorBeanName = "modelType")
    public Model getModel() {
        return model;
    }

    /**
     * Sets model.
     *
     * @param model the model
     */
    public void setModel(Model model) {
        this.model = model;
    }

    /**
     * Gets model id.
     *
     * @return the model id
     */
    public Long getModelId() {
        if (getModel() != null) {
            return getModel().getId();
        } else {
            return null;
        }
    }

    /**
     * Sets model id.
     *
     * @param modelId the model id
     */
    public void setModelId(Long modelId) {
        setModel(new Model(modelId));
    }

    /**
     * Gets market.
     *
     * @return the market
     */
    public Market getMarket() {
        return market;
    }

    /**
     * Gets market for export.
     *
     * @return the market for export
     */
    @ExcelField(title = "title.marketplace", align = 2, sort = 7, type = 1)
    public String getMarketForExport() {
        if (market != null) {
            return this.market.getName();
        } else {
            return null;
        }
    }

    /**
     * Sets market.
     *
     * @param market the market
     */
    public void setMarket(Market market) {
        this.market = market;
    }

    public Long getMarketId() {
        if (LongUtils.isNotBlankAndPositive(super.getMarketId())) {
            return super.getMarketId();
        } else if (getMarket() != null) {
            return getMarket().getId();
        } else {
            return null;
        }
    }

    /**
     * Gets model ids filter.
     *
     * @return the model ids filter
     */
    @JsonIgnore
    public Set<String> getModelIdsFilter() {
        return modelIdsFilter;
    }

    /**
     * Sets model ids filter.
     *
     * @param modelIdsFilter the model ids filter
     */
    public void setModelIdsFilter(Set<String> modelIdsFilter) {
        this.modelIdsFilter = modelIdsFilter;
    }

    /**
     * Gets terminal ids filter.
     *
     * @return the terminal ids filter
     */
    @JsonIgnore
    public Set<String> getTerminalIdsFilter() {
        return terminalIdsFilter;
    }

    /**
     * Sets terminal ids filter.
     *
     * @param terminalIdsFilter the terminal ids filter
     */
    public void setTerminalIdsFilter(Set<String> terminalIdsFilter) {
        this.terminalIdsFilter = terminalIdsFilter;
    }

    /**
     * Gets market ids filter.
     *
     * @return the market ids filter
     */
    @JsonIgnore
    public Set<String> getMarketIdsFilter() {
        return marketIdsFilter;
    }

    /**
     * Sets market ids filter.
     *
     * @param marketIdsFilter the market ids filter
     */
    public void setMarketIdsFilter(Set<String> marketIdsFilter) {
        this.marketIdsFilter = marketIdsFilter;
    }
}
