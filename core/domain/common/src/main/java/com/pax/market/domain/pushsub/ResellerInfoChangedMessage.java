package com.pax.market.domain.pushsub;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/12/5
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ResellerInfoChangedMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = -7428783993295567715L;

    private Long resellerId;
}
