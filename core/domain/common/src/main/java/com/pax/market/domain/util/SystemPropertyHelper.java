/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain.util;

import com.google.common.collect.Maps;
import com.pax.market.constants.CacheNames;
import com.pax.market.constants.VASConstants;
import com.pax.market.constants.VASConstants.VasConfigPropertyKey;
import com.pax.market.domain.entity.global.setting.SystemProperty;
import com.pax.market.framework.common.config.Global;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.Profiles;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.support.config.props.StoreDeployInfoConfigProps;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * The type System property utils.
 */
public class SystemPropertyHelper {
    /**
     * The constant TERMINAL_TOKEN_EXPIRATION_PERIOD.
     */
    public static final String TERMINAL_TOKEN_EXPIRATION_PERIOD = "terminal.token.expiration.period";
    /**
     * The constant MARKET_TERMINAL_LIMIT_TOTAL.
     */
    public static final String MARKET_TERMINAL_LIMIT_TOTAL = "market.terminal.limit.total";
    /**
     * The constant MAX_NUM_OF_TERMINALS_IN_ONE_PURCHASE.
     */
    public static final String MAX_NUM_OF_TERMINALS_IN_ONE_PURCHASE = "max.num.of.terminals.in.one.purchase";
    /**
     * The constant APP_MAX_PRICE.
     */
    public static final String APP_MAX_PRICE = "app.max.price";
    /**
     * The constant APP_MAX_TRIAL.
     */
    public static final String APP_MAX_TRIAL = "app.max.trial";
    /**
     * The constant LINK_EXPIRATION_HOUR.
     */
    public static final String LINK_EXPIRATION_HOUR = "link.expiration.hour";
    /**
     * The constant ENABLE_BACKUP_TERMINAL_HISTORY_ACTIONS.
     */
    public static final String ENABLE_BACKUP_TERMINAL_HISTORY_ACTIONS = "enable.backup.terminal.history.actions";
    public static final String ENABLE_CONVERT_TERMINAL_LAST_APK_PARAM_VARIABLES = "enable.convert.terminal.last.apk.param.variables";
    /**
     * The constant REFRESH_TERMINAL_LAST_APK_PARAM.
     */
    public static final String REFRESH_TERMINAL_LAST_APK_PARAM = "refresh.terminal.last.apk.param";
    /**
     * The constant REFRESH_TERMINAL_LAST_APK_PARAM.
     */
    public static final String REFRESH_TERMINAL_LAST_LAUNCHER = "refresh.terminal.last.launcher";
    /**
     * The constant REFRESH_PUSH_TASK_DOWNLOAD_TIME.
     */
    public static final String REFRESH_PUSH_TASK_DOWNLOAD_TIME = "refresh.push.task.download.time";

    /**
     * The constant REFRESH_PUSH_TASK_RELATED_MARKET_ID.
     */
    public static final String REFRESH_PUSH_TASK_ACTION_TIME = "refresh.push.task.action.time";

    /**
     * The constant REFRESH_PUSH_TASK_RELATED_MARKET_ID.
     */
    public static final String REFRESH_PUSH_TASK_RELATED_MARKET_ID = "refresh.push.task.related.market.id";
    /**
     * The constant REFRESH_PARAM_TASK_SHA256.
     */
    public static final String REFRESH_PARAM_TASK_SHA256 = "refresh.param.task.sha256";
    /**
     * The constant GENERATE_PARAM_FILE_MAP.
     */
    public static final String GENERATE_PARAM_FILE_MAP = "generate.param.file.map";
    /**
     * The constant CONVERT_PARAM_VARIABLES.
     */
    public static final String CONVERT_PARAM_VARIABLES = "convert.param.variables";
    /**
     * The constant REFRESH_COMPLETE_ACTIVE_PUSH_TASK_STATUS.
     */
    public static final String REFRESH_COMPLETE_ACTIVE_PUSH_TASK_STATUS = "refresh.complete.active.push.task.status";
    /**
     * The constant MAX_PAGE_SIZE.
     */
    public static final String MAX_PAGE_SIZE = "max.page.size";
    /**
     * The constant STACKLY_HOST_4_STORE_CLIENT_CRASH_LOG_ACTION.
     */
    public static final String STACKLY_HOST_4_STORE_CLIENT_CRASH_LOG_ACTION = "stackly.host.for.store.client.action";
    /**
     * The constant MAIL_CONFIG_HOST.
     */
    public static final String MAIL_CONFIG_HOST = "mail.config.host";
    /**
     * The constant MAIL_CONFIG_PORT.
     */
    public static final String MAIL_CONFIG_PORT = "mail.config.port";
    /**
     * The constant MAIL_CONFIG_AUTH_USER.
     */
    public static final String MAIL_CONFIG_AUTH_USER = "mail.config.auth.user";
    /**
     * The constant MAIL_CONFIG_AUTH_PASSWORD.
     */
    public static final String MAIL_CONFIG_AUTH_PASSWORD = "mail.config.auth.password";
    /**
     * The constant MAIL_CONFIG_FROM.
     */
    public static final String MAIL_CONFIG_FROM = "mail.config.from";
    /**
     * The constant MAIL_CONFIG_FROM_NAME.
     */
    public static final String MAIL_CONFIG_FROM_NAME = "mail.config.from.name";
    /**
     * The constant MAIL_CONFIG_REPLY.
     */
    public static final String MAIL_CONFIG_REPLY = "mail.config.reply";
    /**
     * The constant MAIL_CONFIG_REPLY_NAME.
     */
    public static final String MAIL_CONFIG_REPLY_NAME = "mail.config.reply.name";
    /**
     * The constant mail security mode: 0=None, 1=Start TLS, 2=SSL.
     */
    public static final String MAIL_CONFIG_SECURITY_MODE = "mail.config.security.mode";
    /**
     * The constant MAIL_CONFIG_SOCKET_CONNECTION_TIME.
     */
    public static final String MAIL_CONFIG_SOCKET_CONNECTION_TIME = "mail.config.socket.connection.time";
    /**
     * The constant MAIL_CONFIG_SOCKET_IO_TIME.
     */
    public static final String MAIL_CONFIG_SOCKET_IO_TIME = "mail.config.socket.io.time";
    /**
     * The constant MAIL_CONFIG_AWS_SES_REGION.
     */
    public static final String MAIL_CONFIG_AWS_SES_REGION = "mail.config.aws.ses.region";
    /**
     * The constant SUPPORT_TEAM_EMAIL
     */
    public static final String SUPPORT_TEAM_EMAILS = "support.team.emails";
    /**
     * The constant SCHEDULE_TRIGGER_ENABLED
     */
    public static final String SCHEDULE_TRIGGER_ENABLED = "system.schedule.trigger.enabled";
    /**
     * The constant LONG_HUI_ENV_ENABLED.
     */
    public static final String LONG_HUI_ENV_ENABLED = "long.hui.env.enabled";
    /**
     * The constant CLOUD_MSG_BATCH_SEND_SIZE.
     */
    public static final String CLOUD_MSG_BATCH_SEND_SIZE = "cloudmsg.batch.size";
    /**
     * The constant IOT_PLATFORM_API_URL.
     */
    public static final String IOT_PLATFORM_API_URL = "iot.platform.api.url";
    /**
     * The constant IOT_PLATFORM_API_ACCESS_KEY.
     */
    public static final String IOT_PLATFORM_API_ACCESS_KEY = "iot.platform.api.access.key";
    /**
     * The constant MIGRATION_AUDIT_TRAIL_ENABLE
     */
    public static final String MIGRATION_AUDIT_TRAIL_ENABLE = "migration.audit.trail.enabled";
    /**
     * The constant MIGRATION_AUTH_LOG_ENABLE
     */
    public static final String MIGRATION_AUTH_LOG_ENABLE = "migration.auth.log.enabled";
    /**
     * The constant COMPLETE_MIGRATION_AUTH_LOG
     */
    public static final String MIGRATION_AUTH_LOG_COMPLETED = "migration.auth.log.completed";
    /**
     * The constant COMPLETE_MIGRATION_AUDIT_TRAIL
     */
    public static final String MIGRATION_AUDIT_TRAIL_COMPLETED = "migration.audit.trail.completed";
    /**
     * The constant MIGRATION_BILLING_SETTING_ENABLE
     */
    public static final String MIGRATION_BILLING_DATA_ENABLE = "migration.billing.data.enabled";
    /**
     * The constant MIGRATION_TERMINAL_APK_PARAM_ENABLE
     */
    public static final String MIGRATION_TERMINAL_APK_PARAM_ENABLE = "migration.terminal.apk.param.enabled";
    /**
     * The constant MIGRATION_TERMINAL_GROUP_APK_PARAM_ENABLE
     */
    public static final String MIGRATION_TERMINAL_GROUP_APK_PARAM_ENABLE = "migration.terminal.group.apk.param.enabled";
    /**
     * The constant MIGRATION_TERMINAL_GROUP_APK_PARAM_ENABLE
     */
    public static final String MIGRATION_SANDBOX_TERMINAL_APK_PARAM_ENABLE = "migration.sandbox.terminal.apk.param.enabled";
    /**
     * The constant MIGRATION_APK_PARAM_TEMPLATE_ENABLE
     */
    public static final String MIGRATION_APK_PARAM_TEMPLATE_ENABLE = "migration.apk.param.template.enabled";
    /**
     * MIGRATION_TERMINAL_ENROLL_DETAILS_ENABLE
     */
    public static final String MIGRATION_TERMINAL_ENROLL_DETAILS_ENABLE = "migration.terminal.enroll.details.enabled";
    /**
     * The constant BILLING_GEIDEA_MODE_ACCESS_MONTHS
     */
    public static final String BILLING_GEIDEA_MODE_ACCESS_MONTHS = "billing.geidea.access.months";
    /**
     * MIGRATION_AIRVIEWER_USAGE_DETAILS_ENABLE
     */
    public static final String MIGRATION_AIRVIEWER_USAGE_DETAILS_ENABLE = "migration.airviewer.details.enabled";
    /**
     * The constant NAVIGO_ASSISTANT_API_URL.
     */
    public static final String NAVIGO_ASSISTANT_API_URL = "navigo.assistant.api.url";
    /**
     * The constant NAVIGO_ASSISTANT_API_ACCESS_KEY.
     */
    public static final String NAVIGO_ASSISTANT_API_ACCESS_KEY = "navigo.assistant.api.access.key";
    /**
     * The constant NAVIGO_ASSISTANT_SOCKET_CONNECTION_TIME.
     */
    public static final String NAVIGO_ASSISTANT_SOCKET_CONNECTION_TIME = "navigo.assistant.socket.connection.time";
    /**
     * The constant NAVIGO_ASSISTANT_SOCKET_IO_TIME.
     */
    public static final String NAVIGO_ASSISTANT_SOCKET_IO_TIME = "navigo.assistant.socket.io.time";
    /**
     * The constant REQUEST_URL_SIGNATURE_ENABLED
     */
    public static final String REQUEST_URL_SIGNATURE_ENABLED = "request.url.signature.enabled";
    /**
     * The constant MailServiceConfigAsList.
     */
    public static final List<String> MailServiceConfigAsList = Arrays.asList(
            MAIL_CONFIG_HOST,
            MAIL_CONFIG_PORT,
            MAIL_CONFIG_AUTH_USER,
            MAIL_CONFIG_AUTH_PASSWORD,
            MAIL_CONFIG_FROM,
            MAIL_CONFIG_FROM_NAME,
            MAIL_CONFIG_REPLY,
            MAIL_CONFIG_REPLY_NAME,
            MAIL_CONFIG_SECURITY_MODE,
            MAIL_CONFIG_AWS_SES_REGION
    );
    /**
     * The constant defaultPropertyMap.
     */
    public static final Map<String, String> defaultPropertyMap = Maps.newHashMap();
    /**
     * The constant MIGRATION_GROUP_FILTERED_ACTION.
     */
    public static final String MIGRATION_GROUP_FILTERED_ACTION = "migration.group.filtered.action";
    /**
     * The constant CURRENT_TERMINAL_ENROLL_MARKET.
     */
    public static final String CURRENT_TERMINAL_ENROLL_MARKET = "current.terminal.enroll.market";
    /**
     * The constant INTERNAL_THIRD_PARTY_SYS_SIGNATURE.
     */
    public static final String INTERNAL_THIRD_PARTY_SYS_SIGNATURE = "internal.third.party.sys.key.signature";
    /**
     * The constant RKI_AUTH_SYSTEM_SECRET.
     */
    public static final String RKI_AUTH_SYSTEM_SECRET = "rki-auth-system.secret";
    /**
     * The constant AD_VISUAL_VIDEO_MAX_DURATION.
     */
    public static final String AD_VISUAL_VIDEO_MAX_DURATION = "ad.visual.video.max.duration";
    /**
     * The constant GEOFENCE_WHITELIST_BATCH_LIMIT.
     */
    public static final String GEOFENCE_WHITELIST_BATCH_LIMIT = "geofence.whitelist.batch.limit";
    /**
     * The constant ZOLON_FACTORY_NAME.
     */
    public static final String ZOLON_FACTORY_NAME = "zolon.factory.name";
    /**
     * The constant EMM_TOKEN_SIGN_KEY.
     */
    public static final String EMM_TOKEN_SIGN_KEY = "emm.token.sign.key";
    /**
     * The constant AUTH_SSO_JWT_SIGNING_KEY.
     */
    public static final String AUTH_SSO_JWT_SIGNING_KEY = "auth.sso.jwt.signing.key";
    /**
     * The constant EMAIL_TOKEN_SECRET.
     */
    public static final String EMAIL_TOKEN_SECRET = "email.token.secret";
    /**
     * The constant DOWNLOAD_TOKEN_SECRET.
     */
    public static final String DOWNLOAD_TOKEN_SECRET = "download.token.secret";
    /**
     * The constant sign key ref.
     */
    public static final List<String> signKeyRefAsList = Arrays.asList(
            AUTH_SSO_JWT_SIGNING_KEY,
            EMAIL_TOKEN_SECRET,
            DOWNLOAD_TOKEN_SECRET
    );
    private static final String ALLOW_SET_READONLY_PARAMETERS = "allow.set.readonly.parameters";
    private static final String SYNC_IMPORT_DATA_LIMIT = "sync.import.data.limit";
    private static final String SYNC_EXPORT_DATA_LIMIT = "sync.export.data.limit";
    private static final String TERMINAL_GROUP_MAX_TERMINAL_COUNT = "terminal.group.max.terminal.count";
    private static final String APK_ICON_SIZE_MAXIMUM = "apk.icon.size.maximum";
    private static final String DOWNLOAD_TASK_EXPIRE_DAYS = "download.task.expire.days";
    private static final String DOWNLOAD_TOKEN_EXPIRE_SECONDS = "download.token.expire.seconds";
    private static final String SIGNATURE_SUPPORTED = "online-signature-cfg.enabled";
    private static final String JDBC_BATCH_SIZE = "jdbc.batch-process-size";
    private static final String REPORT_QUERY_BATCH_SIZE = "report.query.batch.size";
    private static final String TERMINAL_NO_QUERY_BATCH_SIZE = "terminal.no.query.batch.size";
    private static final String TERMINAL_ACTION_RETRY_INTERVAL = "terminal.action.retry.interval";
    private static final String TERMINAL_NEW_PUSH_TASK_RETRIEVE_COUNT_PER_TIME = "terminal.new.push.task.retrieve.count.per.time";
    private static final String TERMINAL_INIT_ACTION_RETRIEVE_COUNT_PER_TIME = "terminal.init.action.retrieve.count.per.time";
    private static final String MIN_TERMINAL_INIT_ACTION_RETRIEVE_COUNT_PER_TIME = "min.terminal.init.action.retrieve.count.per.time";
    private static final String TERMINAL_PENDING_ACTION_RETRIEVE_COUNT_PER_TIME = "terminal.pending.action.retrieve.count.per.time";
    private static final String MIN_TERMINAL_PENDING_ACTION_RETRIEVE_COUNT_PER_TIME = "min.terminal.pending.action.retrieve.count.per.time";
    private static final String RKI_PUSH_TASK_BIND_COUNT_PER_TIME = "rki.push.task.bind.count.per.time";
    private static final String GLOBAL_SETTING_PROVINCE_ENABLED = "global.setting.province.enabled";
    private static final String MARKET_DOMAIN_BLACKLIST = "market.domain.blacklist";
    private static final String PRE_DEFINED_MERCHANT_TYPES = "pre.defined.merchant.types";
    private static final String PRE_DEFINED_RESELLER_ROLES = "pre.defined.reseller.roles";
    private static final String TERMINAL_EXPORT_LIMIT_TOTAL = "terminal.export.limit.total";
    private static final String PARAMETER_COMPARE_EXPORT_LIMIT_TOTAL = "param.compare.export.limit.total";
    private static final String APK_PARAMETER_EXPORT_LIMIT_TOTAL = "apk.paramter.export.limit.total";
    private static final String TERMINAL_VARIABLE_EXPORT_LIMIT_TOTAL = "terminal.variable.export.limit.total";
    private static final String ASSET_EXPORT_LIMIT_TOTAL = "asset.export.limit.total";
    private static final String USER_EXPORT_LIMIT_TOTAL = "user.export.limit.total";
    private static final String APP_SCAN_EXPORT_LIMIT_TOTAL = "app.scan.export.limit.total";
    private static final String MERCHANT_EXPORT_LIMIT_TOTAL = "merchant.export.limit.total";
    private static final String RESELLER_EXPORT_LIMIT_TOTAL = "reseller.export.limit.total";
    private static final String LOG_EXPORT_LIMIT_TOTAL = "log.export.limit.total";
    private static final String TERMINAL_IMPORT_LIMIT_TOTAL = "terminal.import.limit.total";
    private static final String EMM_ZTE_AUDIT_SC_EMAIL = "emm.zte.audit.sc.email";
    private static final String EMM_ZTE_FILE_UPLOAD_MERCHANT_LIMIT_TOTAL = "emm.zte.file.upload.merchant.limit.total";
    private static final String TMK_IMPORT_LIMIT_TOTAL = "tmk.import.limit.total";
    private static final String GROUP_TERMINAL_IMPORT_LIMIT_TOTAL = "group.terminal.import.limit.total";
    private static final String ASSERT_IMPORT_LIMIT_TOTAL = "assert.import.limit.total";
    private static final String TERMINAL_VARIABLE_IMPORT_LIMIT_TOTAL = "terminal.variable.import.limit.total";
    private static final String GROUP_VARIABLE_IMPORT_LIMIT_TOTAL = "group.variable.import.limit.total";
    private static final String MARKET_VARIABLE_IMPORT_LIMIT_TOTAL = "market.variable.import.limit.total";
    private static final String MERCHANT_VARIABLE_IMPORT_LIMIT_TOTAL = "merchant.variable.import.limit.total";
    private static final String MERCHANT_IMPORT_LIMIT_TOTAL = "merchant.import.limit.total";
    private static final String APP_SIGNATURE_FAILED_EXPORT_LIMIT_TOTAL = "app.signature.failed.export.limit.total";
    private static final String CAPTCHA_ASPECT_ENABLED = "captcha.aspect.enabled";
    private static final String SECURITY_BASIC_AUTH_ENABLED = "security.basic.auth.enabled";
    private static final String NOTIFICATION_DELIVERY_BATCH_SIZE = "notification-cfg.batch-delivery-size";
    private static final String NOTIFICATION_RECENTLY_ACTIVE_USER_CHECK_DAYS = "notification-cfg.recent-active-user-check-days";
    private static final String NOTIFICATION_TOPX_UNREAD_MESSAGES_LIST_COUNT = "notification-cfg.topx-unread-count";
    private static final String NOTIFICATION_MESSAGE_ARCHIVE_CHECK_DAYS = "notification-cfg.housekeep-check-days";
    private static final String APP_SUBSCRIPTION_LIST_EXPORT_LIMIT_TOTAL = "app.subscription.list.export.limit.total";
    private static final String FIRMWARE_SUBSCRIPTION_LIST_EXPORT_LIMIT_TOTAL = "firmware.subscription.list.export.limit.total";
    private static final String TOKEN_CHECK_REMOTEIP_USERAGENT = "token.check.remoteip.useragent";
    private static final String FILE_CLIENT_PREFIX = "file-service.client-access-url-prefix";
    private static final String CHUNK_FILE_SIZE = "chunk.file.size";
    private static final String CHUNK_FILE_PENDING_TOTAL_LIMIT = "chunk.file.pending.total.limit";
    private static final String AWS_3RDAPP_DOWNLOAD_PARAM_BY_SIGNED_URL_ENABLED = "aws.3rdapp-download-param-by-signed-url-enabled";
    private static final String EXPORT_INSTALLED_APP_IN_MONTHLY_REPORT = "export.installed.app.in.monthly.report";
    private static final String PAGE_LIMIT = "page.limit";
    private static final String TERMINAL_PUSH_TASK_PAGE_LIMIT = "terminal.push.task.page.limit";
    private static final String LOGCAT_DOWNLOAD_ENABLE = "logcat.download.enable";
    private static final String APK_ONLINE_LIMIT_TOTAL = "apk.online.limit.total";
    private static final String VARIABLE_KEYS_NOT_ALLOW_SAME_VALUE = "variable.keys.not.allow.same.value";
    private static final String REGISTER_USER_ENABLE = "register.user.enable";
    private static final String INTERNAL_THIRD_PARTY_SYS_KEY = "internal.third.party.sys.key";
    private static final String UPTRILLION_THIRD_PARTY_SYS_KEY = "uptrillion.third.party.sys.key";
    private static final String UPTRILLION_THIRD_PARTY_SYS_SIGNATURE = "uptrillion.third.party.sys.key.signature";
    private static final String UPTRILLION_THIRD_PARTY_SYS_ACCESS_MARKET = "uptrillion.third.party.sys.access.market";
    private static final String CHECK_PUSH_HISTORY = "check.push.history";
    private static final String PROFILE_COMMAND_SEND_NUMBER_PER_BATCH = "profile.command.send.number.per.batch";
    private static final String FORCE_UPDATE_COMMAND_SEND_NUMBER_PER_BATCH = "terminal.forceupdate.rate.cfg.batchSize";
    private static final String RESEND_EMAIL_INTERVAL = "resend.email.interval";
    private static final String RKI_CLIENT_PACKAGE_NAME = "rki.client.package.name";
    private static final String MIN_STORE_SDK_VERSION = "min.store.sdk.version";
    private static final String LOWER_STORE_SDK_FORBIDDEN_DATE = "lower.store.sdk.forbidden.date";
    private static final String APK_FILE_MAX_SIZE = "apk.file.max.size";
    private static final String IMPORT_EXCEL_FILE_MAX_SIZE = "import.excel.file.max.size";
    private static final String FIRMWARE_FILE_MAX_SIZE = "firmware.file.max.size";
    private static final String FIRMWARE_RESOURCE_FILE_MAX_SIZE = "firmware.resource.file.max.size";
    private static final String THIRD_PARTY_SYS_IP_LIMIT = "third.party.sys.ip.limit";
    private static final String THIRD_PARTY_SYS_IP_BIND_LIMIT_DATE = "third.party.sys.ip.bind.limit.date";
    private static final String FIRMWARE_MODEM_FILE_MAX_SIZE = "firmware.modem.file.max.size";
    private static final String CREATE_TERMINAL_PUSH_HISTORY = "create.terminal.push.history";
    private static final String ALLOW_RELEASE_NOTE = "allow.release.note";
    private static final String ALLOW_AIRVIEWER_LOGCAT = "allow.airviewer.logcat";
    private static final String ALLOW_USER_CHANGE_EMAIL = "allow.user.change.email";
    private static final String CHECK_TERMINAL_MODEL = "check.terminal.model";
    private static final String PED_KEY_STATUS_ENABLE = "ped.key.status.enable";
    private static final String ALLOW_RESELLER_TID_SETTING = "allow.reseller.tid.setting";
    private static final String REPORT_MAX_EXPORT_FIELD_SIZE = "report.max.export.field.size";
    private static final String TERMINAL_OFFLINE_EXPORT_LIMIT_TOTAL = "terminal.offline.export.limit.total";
    private static final String GLOBAL_STATISTICS_MARKET_EXPORT_LIMIT_TOTAL = "global.statistics.market.export.limit.total";
    private static final String GLOBAL_STATISTICS_APP_EXPORT_LIMIT_TOTAL = "global.statistics.app.export.limit.total";
    private static final String GLOBAL_STATISTICS_DEVELOPER_EXPORT_LIMIT_TOTAL = "global.statistics.developer.export.limit.total";
    private static final String PAX_FACTORY_DEFAULT_ID = "pax.factory.default.id";
    private static final String BYPASS_TERMINAL_URLS = "bypass.terminal.urls";
    private static final String LOG_APP_SEARCH_HISTORY = "log.app.search.history";
    private static final String ANNOUNCEMENT_LIMIT = "announcement.limit";
    private static final String USER_ACTIVE_TO_SUSPEND_RETENTION_HOURS = "user.active.to.suspend.retention.hours";
    private static final String ALLOW_BURIED_POINT_DATA_COLLECTION = "allow.buried.point.data.collection";
    private static final String DEBUG_APK_CERT_DN = "debug.apk.cert.dn";
    private static final String ENABLE_DAILY_REFRESH_RESELLER_INSTALLED_APKS = "enable.daily.refresh.reseller.installed.apks";
    private static final String ENABLE_DAILY_UPDATE_GROUP_PENDING_ACTIONS = "enable.daily.update.group.pending.actions";
    private static final String EXPORT_REPORT_TIME_LIMIT = "export.report.time.limit";
    private static final String EXPORT_SCHEDULE_REPORT_LIMIT = "export.schedule.report.limit";
    private static final String DEVELOPER_REGISTRATION_FEE = "developer.registration.fee";
    private static final String MAX_APK_DIFF_COUNT = "max.apk.diff.count";
    private static final String MAX_STORE_CLIENT_DIFF_COUNT = "max.store.client.diff.count";
    private static final String ALARM_RECEIVER_EMAIL_MAX = "alarm.receiver.email.max";
    private static final String EXTRACTION_CODE_EXPIRATION_DAY = "extraction.code.expiration.day";
    private static final String ALLOW_SYNC_TERMINAL_BASIC_INFO = "allow.sync.terminal.basic.info";
    private static final String ALLOW_SYNC_TERMINAL_APP_INFO = "allow.sync.terminal.app.info";
    private static final String ALLOW_SYNC_TERMINAL_BASIC_FULL_DATA = "allow.sync.terminal.basic.full";
    private static final String ALLOW_SYNC_TERMINAL_APP_FULL_DATA = "allow.sync.terminal.app.full";
    private static final String INSIGHT_TERMINAL_HISTORY_DATA_TIME_LIMIT_SWITCH = "insight.terminal.history.data.time.limit.switch";
    private static final String INSIGHT_QUERY_DATA_TIME_UNIT_LIMIT = "insight.query.data.time.unit.limit";
    private static final String INSIGHT_QUERY_DATA_TIME_UNIT_MINUTE = "insight.query.data.time.unit.minute";
    private static final String SHOW_SERVICE_AGREEMENT = "show.service.agreement";
    private static final String TERMINAL_OPERATION_TIMEOUT = "terminal.operation.timeout";
    private static final String TERMINAL_COLLECT_LOG_TIMEOUT = "terminal.collect.log.timeout";
    private static final String ENABLE_DAILY_CLEAN_DELETED_TERMINALS = "enable.daily.clean.deleted.terminals";
    private static final String PHYSICAL_DELETE_DATA_DAYS = "physical.delete.days";
    private static final String ENABLE_DAILY_CLEAR_DATA = "enable.daily.clear.data";
    private static final String ENABLE_DAILY_EXPIRE_TERMINAL_ACTIONS = "enable.daily.expire.terminal.actions";
    private static final String RKI_AUTH_SYSTEM_ENABLED = "rki.auth.system.enabled";
    private static final String TABLE_SPLIT_RECORD_BATCH_SIZE = "table.split.record.batch.size";
    private static final String DEFAULT_UNATTENDED_MODEL = "default.unattended.model";
    private static final String MERCHANT_TYPE_IMPORT_LIMIT_TOTAL = "merchant.type.import.limit.total";
    private static final String APP_TAG_LIMIT = "app.tag.limit";
    private static final String ZOLON_BILLING_CENTER_HOST = "zolon.billing.center.host";
    private static final String CALL_INSTALLED_APP_URL_LIMIT = "call.installed.app.url.limit";
    //RKI Config
    private static final String RKI_CONNECTION_TIMEOUT = "rki.connection-timeout-ms";
    private static final String RKI_REQUEST_TIMEOUT = "rki.request-timeout-ms";
    private static final String RKI_AUTH_SYSTEM_URL = "rki.auth-system.url";
    private static final String RKI_AUTH_SYSTEM_PLATFORM_ID = "rki.auth-system.platform-id";
    private static final String SHOW_SYSTEM_HIDDEN_CONFIG = "system.hidden-config.show";
    private static final String DATA_SCOPE_FILTER_MODE = "data.scope.filter.mode";
    private static final String TERMINAL_APP_INSTALL_WHITELIST = "terminal.app.install.whitelist";
    private static final String GROUP_PUSH_TERMINAL_LIMIT = "group.push.terminal.limit";
    private static final String REPORT_RECORD_EXPORT_LIMIT = "report.record.export.limit";
    private static final String LOG_API_ENABLED = "log.api.enabled";
    private static final String LOG_API_OVER_TIME_MILLS = "log.api.over-time-mills";
    private static final String LOG_SQL_ENABLED = "log.sql.enabled";
    private static final String LOG_SQL_OVER_TIME_MILLS = "log.sql.over-time-mills";
    private static final String USE_AGREEMENT_DEFAULT = "use.agreement.default";
    private static final String PRE_DEFINED_IP_WHITE_LIST = "pre.defined.ip.whitelist";
    private static final String PRE_DEFINED_DOMAIN_WHITE_LIST = "pre.defined.domain.whitelist";
    private static final String DASHBOARD_WIDGET_REFRESH_MINUTES = "dashboard.widget.refresh.minutes";


    //9.2新增业务限制
    private static final String DASHBOARD_WIDGET_FORCE_REFRESH_MINUTES = "dashboard.widget.force.refresh.minutes";
    private static final String REPORT_DATASOURCE_REFRESH_MINUTES = "report.datasource.refresh.minutes";
    private static final String PHYSICAL_DELETE_TERMINAL_DAYS = "physical.delete.terminal.days";
    private static final String MIGRATION_FILTER_ACTION_BATCH_SIZE = "migration.filter.action.batch.size";
    private static final String ALLOW_GEN_STATS_PUK = "allow.gen.stats.puk";
    private static final String RESELLER_TREE_RECORD_SIZE = "reseller.tree.record.size";
    private static final String CYBER_LAB_BLACKLISTS_LIMIT = "cyber.lab.terminal.blacklists.size.limit";
    private static final String LIMIT_FOOTER_NUM = "limit.footer.num";
    private static final String LIMIT_MERCHANT_TYPE_NUM = "limit.merchant_type.num";
    private static final String LIMIT_ATTRIBUTE_NUM = "limit.attribute.num";
    private static final String LIMIT_USER_AGREEMENT_NUM = "limit.user_agreement.num";
    private static final String LIMIT_DEVELOPER_AGREEMENT_NUM = "limit.developer_agreement.num";
    private static final String LIMIT_WEBHOOK_NUM = "limit.webhook.num";
    private static final String LIMIT_INVOICE_RECEIVER_NUM = "limit.invoice_receiver.num";
    private static final String LIMIT_NON_REMOVABLE_APP_NUM = "limit.non_removable_app.num";
    private static final String LIMIT_SENSITIVE_WORDS_NUM = "limit.sensitive_words.num";
    private static final String LIMIT_STORE_CLIENT_NUM = "limit.store_client.num";
    private static final String LIMIT_ROLE_NUM = "limit.role.num";
    private static final String LIMIT_FIRMWARE_NUM = "limit.firmware.num";
    private static final String LIMIT_IP_WHITELIST_NUM = "limit.ip_whitelist.num";
    private static final String LIMIT_USER_PER_ROLE_NUM = "limit.user_per_role.num";
    private static final String FUZZY_QUERY_PROVIDER = "fuzzy.query.provider";
    private static final String FUZZY_QUERY_TERMINAL_DISABLED = "fuzzy.query.terminal.disabled";
    private static final String USER_RESELLER_MERCHANT_RETRIEVE_LIMIT = "user.reseller.merchant.retrieve.limit";
    private static final String RESELLER_MIGRATE_CHILD_LIMIT = "reseller.migrate.child.limit";
    private static final String MERCHANT_MIGRATE_TERMINAL_LIMIT = "merchant.migrate.terminal.limit";
    private static final String GROUP_UNINSTALL_APP_LIMIT = "group.uninstall.app.limit";
    private static final String GROUP_PUSH_MESSAGE_LIMIT = "group.push.message.limit";
    private static final String APK_PARAM_DATA_FILE_SUFFIX = "apk.param.data.file.suffix";
    private static final String MARKET_STATS_JOB_ENABLED = "market.stats.job.enabled";
    private static final String AIRSHIELD_DATASET_QUERY_CODE = "airshield.dataset.query.code";
    private static final String SSO_MULTI_MARKET_LIMIT = "sso.multi.market.limit";
    private static final String EMM_CLIENT_PACKAGE_NAME = "emm.client.package.name";
    private static final String EMM_CLIENT_PACKAGE_NAME_US = "emm.client.package.name.us";
    private static final String EMM_AIRVIEWER_PACKAGE_NAME = "emm.airviewer.package.name";
    private static final String EMM_AIRVIEWER_PLUGIN_PACKAGE_NAME = "emm.airviewer.plugin.package.name";
    private static final String AIRVIEWER_TRADITIONAL_MODEL = "airviewer.traditional.model";
    private static final String AIRVIEWER_EMM_FACTORY = "airviewer.emm.factory";
    private static final String AIRVIEWER_EMM_DEFAULT_UNATTENDED_FACTORY = "airviewer.emm.default.unattended.factory";
    private static final String EMM_CUSTOM_POLICY_LIMIT = "emm.custom.policy.limit";
    private static final String EMM_POLICY_APP_LIMIT = "emm.policy.app.limit";
    private static final String EMM_LOCK_POLICY_LIMIT = "emm.lock.policy.limit";
    private static final String EMM_POLICY_MESSAGE_SEND_NUMBER_PER_BATCH = "emm.policy.message.send.number.per.batch";
    private static final String EMM_ZTE_OPERATION_MESSAGE_SEND_NUMBER_PER_BATCH = "emm.zte.operation.message.send.number.per.batch";
    private static final String EMM_NO_AUTH_DEVICE_MESSAGE_SEND_NUMBER_PER_BATCH = "emm.no.auth.device.message.send.number.per.batch";
    private static final String EMM_CALL_ANDROID_MANAGEMENT_API_LIMIT = "emm.call.android.management.api.per.batch";
    private static final String EMM_UNSUBSCRIBE_DAYS_LIMIT = "emm.unsubscribe.days.limit";
    private static final String EMM_ENTERPRISE_TOPIC = "emm.enterprise.topic";
    //1.0.1(1),保存格式，version_name(version_code)
    private static final String MIN_MOBILE_APP_VERSION = "min.mobile.app.version";
    private static final String LATEST_MOBILE_APP_VERSION = "latest.mobile.app.version";
    private static final String COMMON_PROCESS_BATCH_SIZE = "common.process.batch.size";
    private static final String WEB_HOOK_MESSAGE_PHYSICAL_DELETE_DATA_DAYS = "webhook.message.physical.delete.days";
    private static final String WEB_HOOK_MESSAGE_PHYSICAL_DELETE_BATCH_SIZE = "webhook.message.physical.batch.size";
    private static final String WEB_HOOK_MESSAGE_PHYSICAL_DELETE_ENABLE = "webhook.message.physical.delete.enable";

    private static final String ALLOW_MODEL_AUTO_SYNC = "allow.model.auto.sync"; //LEVEL 2
    private static final String ALLOW_CHANGE_TERMINAL_MODEL = "allow.change.terminal.model"; //LEVEL 1
    private static final String DISABLE_SEARCH_PUSH_HISTORY_WITHOUT_SN = "disable.search.push.history.without.sn";
    private static final String DELETE_FIRMWARE_AND_APP_FILE_DAYS = "delete.firmware.app.days";
    public static final String PUSH_LIMIT_TERMINAL_MAX_NUMBER = "push.limit.terminal.max.number";

    //Skip mq message names
    private static final String SKIP_MQ_MESSAGE_NAMES = "skip.mq.message.names";

    private static final String AIRLINK_TERMINAL_ACTIVATE_DETAIL_EXPORT_LIMIT_TOTAL = "airlink.terminal_activate_detal.export.limit.total";
    private static final String AIRLINK_ESTATE_IMPORT_LIMIT_TOTAL = "airlink.terminal_estate.import.limit.total";
    private static final String AIRLINK_ESTATE_EXPORT_LIMIT_TOTAL = "airlink.terminal_estate.export.limit.total";
    private static final String AIRLINK_TERMINAL_ACTIVE_HISTORY_DURATION = "airlink.terminal.active.history.duration";
    private static final String AIRLINK_TERMINAL_DATA_EXCEED_WARNING_NUMBER = "airlink.terminal.data.exceed.warning.number";
    static {
        defaultPropertyMap.put(SYNC_IMPORT_DATA_LIMIT, "500");
        defaultPropertyMap.put(SYNC_EXPORT_DATA_LIMIT, "500");
        defaultPropertyMap.put(TERMINAL_GROUP_MAX_TERMINAL_COUNT, "5000");
        defaultPropertyMap.put(APK_ICON_SIZE_MAXIMUM, "307200");
        defaultPropertyMap.put(DOWNLOAD_TASK_EXPIRE_DAYS, "180");
        defaultPropertyMap.put(DOWNLOAD_TOKEN_EXPIRE_SECONDS, "1800");
        defaultPropertyMap.put(SIGNATURE_SUPPORTED, "true");
        defaultPropertyMap.put(JDBC_BATCH_SIZE, "200");
        defaultPropertyMap.put(REPORT_QUERY_BATCH_SIZE, "200");
        defaultPropertyMap.put(TERMINAL_NO_QUERY_BATCH_SIZE, "10000");
        defaultPropertyMap.put(TERMINAL_ACTION_RETRY_INTERVAL, "360");
        defaultPropertyMap.put(TERMINAL_NEW_PUSH_TASK_RETRIEVE_COUNT_PER_TIME, "1000");
        defaultPropertyMap.put(TERMINAL_INIT_ACTION_RETRIEVE_COUNT_PER_TIME, "5000");
        defaultPropertyMap.put(MIN_TERMINAL_INIT_ACTION_RETRIEVE_COUNT_PER_TIME, "100");
        defaultPropertyMap.put(TERMINAL_PENDING_ACTION_RETRIEVE_COUNT_PER_TIME, "5000");
        defaultPropertyMap.put(MIN_TERMINAL_PENDING_ACTION_RETRIEVE_COUNT_PER_TIME, "100");
        defaultPropertyMap.put(RKI_PUSH_TASK_BIND_COUNT_PER_TIME, "1000");
        defaultPropertyMap.put(GLOBAL_SETTING_PROVINCE_ENABLED, "false");
        defaultPropertyMap.put(MARKET_DOMAIN_BLACKLIST, "www,auth");
        defaultPropertyMap.put(PRE_DEFINED_MERCHANT_TYPES, "");
        defaultPropertyMap.put(PRE_DEFINED_RESELLER_ROLES, "6");
        defaultPropertyMap.put(TERMINAL_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(PARAMETER_COMPARE_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(APK_PARAMETER_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(TERMINAL_VARIABLE_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(ASSET_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(USER_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(APP_SCAN_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(MERCHANT_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(RESELLER_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(LOG_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(TMK_IMPORT_LIMIT_TOTAL, "1000");
        defaultPropertyMap.put(TERMINAL_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(EMM_ZTE_AUDIT_SC_EMAIL, "<EMAIL>");
        defaultPropertyMap.put(EMM_ZTE_FILE_UPLOAD_MERCHANT_LIMIT_TOTAL, "100");
        defaultPropertyMap.put(GROUP_TERMINAL_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(ASSERT_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(TERMINAL_VARIABLE_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(GROUP_VARIABLE_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(MARKET_VARIABLE_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(MERCHANT_VARIABLE_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(MERCHANT_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(APP_SIGNATURE_FAILED_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(CAPTCHA_ASPECT_ENABLED, "true");
        defaultPropertyMap.put(SECURITY_BASIC_AUTH_ENABLED, "true");
        defaultPropertyMap.put(NOTIFICATION_DELIVERY_BATCH_SIZE, "500");
        defaultPropertyMap.put(NOTIFICATION_RECENTLY_ACTIVE_USER_CHECK_DAYS, "30");
        defaultPropertyMap.put(NOTIFICATION_TOPX_UNREAD_MESSAGES_LIST_COUNT, "5");
        defaultPropertyMap.put(NOTIFICATION_MESSAGE_ARCHIVE_CHECK_DAYS, "30");
        defaultPropertyMap.put(APP_SUBSCRIPTION_LIST_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(FIRMWARE_SUBSCRIPTION_LIST_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(TOKEN_CHECK_REMOTEIP_USERAGENT, "false");
        defaultPropertyMap.put(FILE_CLIENT_PREFIX, "https://www.paxsit.com/");
        defaultPropertyMap.put(CHUNK_FILE_SIZE, "20"); //单位M
        defaultPropertyMap.put(CHUNK_FILE_PENDING_TOTAL_LIMIT, "10");
        defaultPropertyMap.put(AWS_3RDAPP_DOWNLOAD_PARAM_BY_SIGNED_URL_ENABLED, "true");
        defaultPropertyMap.put(EXPORT_INSTALLED_APP_IN_MONTHLY_REPORT, "false");
        defaultPropertyMap.put(PAGE_LIMIT, "50000");
        defaultPropertyMap.put(TERMINAL_PUSH_TASK_PAGE_LIMIT, "1000");
        defaultPropertyMap.put(LOGCAT_DOWNLOAD_ENABLE, "true");
        defaultPropertyMap.put(APK_ONLINE_LIMIT_TOTAL, "10");
        defaultPropertyMap.put(MARKET_TERMINAL_LIMIT_TOTAL, "300000");
        defaultPropertyMap.put(VARIABLE_KEYS_NOT_ALLOW_SAME_VALUE, "");
        defaultPropertyMap.put(REGISTER_USER_ENABLE, "true");
        defaultPropertyMap.put(INTERNAL_THIRD_PARTY_SYS_KEY, "INTERNAL_THIRD_PARTY_SYS_KEY");
        defaultPropertyMap.put(INTERNAL_THIRD_PARTY_SYS_SIGNATURE, "FUKAF63IZ925G9TOCIK02YGSS5754IE305424DBJ6MDQVYVX9P9E5JPU25N89AC7");
        defaultPropertyMap.put(UPTRILLION_THIRD_PARTY_SYS_SIGNATURE, "");
        defaultPropertyMap.put(UPTRILLION_THIRD_PARTY_SYS_ACCESS_MARKET, "");
        defaultPropertyMap.put(UPTRILLION_THIRD_PARTY_SYS_KEY, "");
        defaultPropertyMap.put(CHECK_PUSH_HISTORY, "false");
        defaultPropertyMap.put(PROFILE_COMMAND_SEND_NUMBER_PER_BATCH, "100");
        defaultPropertyMap.put(FORCE_UPDATE_COMMAND_SEND_NUMBER_PER_BATCH, "100");
        defaultPropertyMap.put(RESEND_EMAIL_INTERVAL, "5");
        defaultPropertyMap.put(RKI_CLIENT_PACKAGE_NAME, "com.pax.rki");
        defaultPropertyMap.put(MIN_STORE_SDK_VERSION, "8.7.0");
        defaultPropertyMap.put(LOWER_STORE_SDK_FORBIDDEN_DATE, "2023-07-01");
        defaultPropertyMap.put(APK_FILE_MAX_SIZE, "100");
        defaultPropertyMap.put(IMPORT_EXCEL_FILE_MAX_SIZE, "2");
        defaultPropertyMap.put(FIRMWARE_FILE_MAX_SIZE, "800");
        defaultPropertyMap.put(FIRMWARE_RESOURCE_FILE_MAX_SIZE, "200");
        defaultPropertyMap.put(MAX_NUM_OF_TERMINALS_IN_ONE_PURCHASE, "100");
        defaultPropertyMap.put(APP_MAX_PRICE, "999.99");
        defaultPropertyMap.put(APP_MAX_TRIAL, "50");
        defaultPropertyMap.put(FIRMWARE_MODEM_FILE_MAX_SIZE, "200");
        defaultPropertyMap.put(CREATE_TERMINAL_PUSH_HISTORY, "false");
        defaultPropertyMap.put(ALLOW_RELEASE_NOTE, "false");
        defaultPropertyMap.put(ALLOW_AIRVIEWER_LOGCAT, "true");
        defaultPropertyMap.put(ALLOW_USER_CHANGE_EMAIL, "false");
        defaultPropertyMap.put(CHECK_TERMINAL_MODEL, "true");
        defaultPropertyMap.put(PED_KEY_STATUS_ENABLE, "false");
        defaultPropertyMap.put(LINK_EXPIRATION_HOUR, "48");
        defaultPropertyMap.put(EXTRACTION_CODE_EXPIRATION_DAY, "180");
        defaultPropertyMap.put(ALLOW_RESELLER_TID_SETTING, "false");
        defaultPropertyMap.put(REPORT_MAX_EXPORT_FIELD_SIZE, "100");
        defaultPropertyMap.put(TERMINAL_OFFLINE_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(PAX_FACTORY_DEFAULT_ID, "1");
        defaultPropertyMap.put(BYPASS_TERMINAL_URLS, "");
        defaultPropertyMap.put(LOG_APP_SEARCH_HISTORY, "false");
        defaultPropertyMap.put(ANNOUNCEMENT_LIMIT, "3");
        defaultPropertyMap.put(USER_ACTIVE_TO_SUSPEND_RETENTION_HOURS, "48");
        defaultPropertyMap.put(ALLOW_BURIED_POINT_DATA_COLLECTION, "false");
        defaultPropertyMap.put(DEBUG_APK_CERT_DN, "C=US, O=Android, CN=Android Debug");
        defaultPropertyMap.put(ENABLE_DAILY_REFRESH_RESELLER_INSTALLED_APKS, "false");
        defaultPropertyMap.put(ENABLE_DAILY_UPDATE_GROUP_PENDING_ACTIONS, "true");
        defaultPropertyMap.put(EXPORT_REPORT_TIME_LIMIT, "30");
        defaultPropertyMap.put(EXPORT_SCHEDULE_REPORT_LIMIT, "5");
        defaultPropertyMap.put(ENABLE_BACKUP_TERMINAL_HISTORY_ACTIONS, "false");
        defaultPropertyMap.put(REFRESH_TERMINAL_LAST_APK_PARAM, "false");
        defaultPropertyMap.put(REFRESH_TERMINAL_LAST_LAUNCHER, "false");
        defaultPropertyMap.put(REFRESH_PUSH_TASK_DOWNLOAD_TIME, "false");
        defaultPropertyMap.put(REFRESH_PUSH_TASK_ACTION_TIME, "false");
        defaultPropertyMap.put(REFRESH_PUSH_TASK_RELATED_MARKET_ID, "false");
        defaultPropertyMap.put(REFRESH_PARAM_TASK_SHA256, "false");
        defaultPropertyMap.put(GENERATE_PARAM_FILE_MAP, "false");
        defaultPropertyMap.put(CONVERT_PARAM_VARIABLES, "false");
        defaultPropertyMap.put(REFRESH_COMPLETE_ACTIVE_PUSH_TASK_STATUS, "false");
        defaultPropertyMap.put(ALLOW_SET_READONLY_PARAMETERS, "false");
        defaultPropertyMap.put(DEVELOPER_REGISTRATION_FEE, "99");
        defaultPropertyMap.put(MAX_APK_DIFF_COUNT, "5");
        defaultPropertyMap.put(MAX_STORE_CLIENT_DIFF_COUNT, "40");
        defaultPropertyMap.put(ALARM_RECEIVER_EMAIL_MAX, "20");
        defaultPropertyMap.put(MAX_PAGE_SIZE, "100");
        defaultPropertyMap.put(ALLOW_SYNC_TERMINAL_BASIC_INFO, "false");
        defaultPropertyMap.put(ALLOW_SYNC_TERMINAL_APP_INFO, "false");
        defaultPropertyMap.put(ALLOW_SYNC_TERMINAL_BASIC_FULL_DATA, "true");
        defaultPropertyMap.put(ALLOW_SYNC_TERMINAL_APP_FULL_DATA, "true");
        defaultPropertyMap.put(INSIGHT_TERMINAL_HISTORY_DATA_TIME_LIMIT_SWITCH, "true");
        defaultPropertyMap.put(INSIGHT_QUERY_DATA_TIME_UNIT_LIMIT, "5");
        defaultPropertyMap.put(INSIGHT_QUERY_DATA_TIME_UNIT_MINUTE, "1");
        defaultPropertyMap.put(SHOW_SERVICE_AGREEMENT, "false");
        defaultPropertyMap.put(TERMINAL_OPERATION_TIMEOUT, "120000");
        defaultPropertyMap.put(TERMINAL_COLLECT_LOG_TIMEOUT, "604800000");
        defaultPropertyMap.put(ENABLE_DAILY_CLEAN_DELETED_TERMINALS, "false");
        defaultPropertyMap.put(PHYSICAL_DELETE_DATA_DAYS, "366");
        defaultPropertyMap.put(ENABLE_DAILY_CLEAR_DATA, "false");
        defaultPropertyMap.put(ENABLE_DAILY_EXPIRE_TERMINAL_ACTIONS, "false");
        defaultPropertyMap.put(GLOBAL_STATISTICS_MARKET_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(GLOBAL_STATISTICS_APP_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(GLOBAL_STATISTICS_DEVELOPER_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(RKI_AUTH_SYSTEM_ENABLED, "false");
        defaultPropertyMap.put(DEFAULT_UNATTENDED_MODEL, "[{\"modelName\":\"IM30\",\"unattended\":\"1\"},{\"modelName\":\"SK600\",\"unattended\":\"1\"},{\"modelName\":\"SK800\",\"unattended\":\"1\"},{\"modelName\":\"AR8\",\"unattended\":\"0\"},{\"modelName\":\"ARIES8\",\"unattended\":\"0\"}]");
        defaultPropertyMap.put(TABLE_SPLIT_RECORD_BATCH_SIZE, "5000");
        defaultPropertyMap.put(MERCHANT_TYPE_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(ZOLON_BILLING_CENTER_HOST, "https://vas.sitvas.whatspos.cn");
        defaultPropertyMap.put(CALL_INSTALLED_APP_URL_LIMIT, "10");
        defaultPropertyMap.put(PHYSICAL_DELETE_TERMINAL_DAYS, "366");

        defaultPropertyMap.put(VasConfigPropertyKey.CLOUDMSG_ENABLE_IN_DEV_PORTAL, "false");

        defaultPropertyMap.put(STACKLY_HOST_4_STORE_CLIENT_CRASH_LOG_ACTION, "");

        defaultPropertyMap.put(APP_TAG_LIMIT, "100");
        defaultPropertyMap.put(DASHBOARD_WIDGET_REFRESH_MINUTES, "60");
        defaultPropertyMap.put(DASHBOARD_WIDGET_FORCE_REFRESH_MINUTES, "10");
        defaultPropertyMap.put(REPORT_DATASOURCE_REFRESH_MINUTES, "60");

        defaultPropertyMap.put(RKI_CONNECTION_TIMEOUT, "10000");
        defaultPropertyMap.put(RKI_REQUEST_TIMEOUT, "10000");
        defaultPropertyMap.put(SHOW_SYSTEM_HIDDEN_CONFIG, "false");
        defaultPropertyMap.put(DATA_SCOPE_FILTER_MODE, "2");
        defaultPropertyMap.put(USE_AGREEMENT_DEFAULT, "false");
        defaultPropertyMap.put(TERMINAL_APP_INSTALL_WHITELIST, "com.pax.ft;com.pax.idreader;com.pax.paxscans");
        defaultPropertyMap.put(GROUP_PUSH_TERMINAL_LIMIT, "300000");
        defaultPropertyMap.put(REPORT_RECORD_EXPORT_LIMIT, "300000");

        defaultPropertyMap.put(THIRD_PARTY_SYS_IP_LIMIT, "5");
        defaultPropertyMap.put(THIRD_PARTY_SYS_IP_BIND_LIMIT_DATE, "20240101");

        defaultPropertyMap.put(SUPPORT_TEAM_EMAILS, "");

        defaultPropertyMap.put(LOG_SQL_ENABLED, "false");
        defaultPropertyMap.put(LOG_SQL_OVER_TIME_MILLS, "200");
        defaultPropertyMap.put(LOG_API_ENABLED, "false");
        defaultPropertyMap.put(LOG_API_OVER_TIME_MILLS, "200");

        defaultPropertyMap.put(PRE_DEFINED_IP_WHITE_LIST, "");
        defaultPropertyMap.put(PRE_DEFINED_DOMAIN_WHITE_LIST, "");
        defaultPropertyMap.put(SCHEDULE_TRIGGER_ENABLED, "false");
        defaultPropertyMap.put(LONG_HUI_ENV_ENABLED, "false");
        defaultPropertyMap.put(CLOUD_MSG_BATCH_SEND_SIZE, "5000");
        defaultPropertyMap.put(IOT_PLATFORM_API_URL, "https://api.sit.whatspos.cn/iotplatform");
        defaultPropertyMap.put(IOT_PLATFORM_API_ACCESS_KEY, "iXgSTEGwADeOPjGdtlBLtwGWSJCkQqwemBRahCsQJllDIOqJ");
        defaultPropertyMap.put(MIGRATION_AUDIT_TRAIL_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_AUTH_LOG_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_AUTH_LOG_COMPLETED, "true");
        defaultPropertyMap.put(MIGRATION_AUDIT_TRAIL_COMPLETED, "true");
        defaultPropertyMap.put(MIGRATION_BILLING_DATA_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_TERMINAL_APK_PARAM_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_TERMINAL_GROUP_APK_PARAM_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_SANDBOX_TERMINAL_APK_PARAM_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_APK_PARAM_TEMPLATE_ENABLE, "false");
        defaultPropertyMap.put(MIGRATION_TERMINAL_ENROLL_DETAILS_ENABLE, "false");
        defaultPropertyMap.put(BILLING_GEIDEA_MODE_ACCESS_MONTHS, "3");
        defaultPropertyMap.put(MIGRATION_AIRVIEWER_USAGE_DETAILS_ENABLE, "false");
        defaultPropertyMap.put(NAVIGO_ASSISTANT_SOCKET_IO_TIME, "5000");
        defaultPropertyMap.put(REQUEST_URL_SIGNATURE_ENABLED, "true");
        defaultPropertyMap.put(NAVIGO_ASSISTANT_SOCKET_CONNECTION_TIME, "30000");

        defaultPropertyMap.put(MAIL_CONFIG_SOCKET_CONNECTION_TIME, "5000");
        defaultPropertyMap.put(MAIL_CONFIG_SOCKET_IO_TIME, "5000");

        defaultPropertyMap.put(VasConfigPropertyKey.VASPLATFORM_API_BASE_URL, "https://vas.paxsaas.com/platform");
        defaultPropertyMap.put(VasConfigPropertyKey.INSIGHT_DATA_API_INTERNAL_URL, "");
        defaultPropertyMap.put(VasConfigPropertyKey.VASPLATFORM_OAUTH_CLIENT_SECRET, "n0UZ278trMhEeegB");
        defaultPropertyMap.put(VasConfigPropertyKey.VASPLATFORM_OAUTH_CLIENT_TOKEN_SIGNING_KEY, "CQIKayrHlGwRBkQDwVJTlgQrOqCQyYVaoKjGddqbsEXCBuRK");
        defaultPropertyMap.put(MIGRATION_FILTER_ACTION_BATCH_SIZE, "1000");
        defaultPropertyMap.put(MIGRATION_GROUP_FILTERED_ACTION, "false");
        defaultPropertyMap.put(CURRENT_TERMINAL_ENROLL_MARKET, "");
        defaultPropertyMap.put(ALLOW_GEN_STATS_PUK, "false");
        defaultPropertyMap.put(RESELLER_TREE_RECORD_SIZE, "100");
        defaultPropertyMap.put(CYBER_LAB_BLACKLISTS_LIMIT, "2000");


        //9.2业务限制
        defaultPropertyMap.put(LIMIT_FOOTER_NUM, "5");
        defaultPropertyMap.put(LIMIT_MERCHANT_TYPE_NUM, "500");
        defaultPropertyMap.put(LIMIT_ATTRIBUTE_NUM, "100");
        defaultPropertyMap.put(LIMIT_USER_AGREEMENT_NUM, "500");
        defaultPropertyMap.put(LIMIT_DEVELOPER_AGREEMENT_NUM, "500");
        defaultPropertyMap.put(LIMIT_WEBHOOK_NUM, "10");
        defaultPropertyMap.put(LIMIT_INVOICE_RECEIVER_NUM, "100");
        defaultPropertyMap.put(LIMIT_NON_REMOVABLE_APP_NUM, "10");
        defaultPropertyMap.put(LIMIT_SENSITIVE_WORDS_NUM, "50");
        defaultPropertyMap.put(LIMIT_STORE_CLIENT_NUM, "500");
        defaultPropertyMap.put(LIMIT_ROLE_NUM, "100");
        defaultPropertyMap.put(LIMIT_FIRMWARE_NUM, "500");
        defaultPropertyMap.put(FUZZY_QUERY_PROVIDER, "mysql");
        defaultPropertyMap.put(LIMIT_IP_WHITELIST_NUM, "100");
        defaultPropertyMap.put(LIMIT_USER_PER_ROLE_NUM, "500");

        defaultPropertyMap.put(USER_RESELLER_MERCHANT_RETRIEVE_LIMIT, "1000");
        defaultPropertyMap.put(RESELLER_MIGRATE_CHILD_LIMIT, "1000");
        defaultPropertyMap.put(MERCHANT_MIGRATE_TERMINAL_LIMIT, "10000");
        defaultPropertyMap.put(GROUP_UNINSTALL_APP_LIMIT, "100");
        defaultPropertyMap.put(GROUP_PUSH_MESSAGE_LIMIT, "6");

        defaultPropertyMap.put(AD_VISUAL_VIDEO_MAX_DURATION, "60");
        defaultPropertyMap.put(GEOFENCE_WHITELIST_BATCH_LIMIT, "100");

        defaultPropertyMap.put(APK_PARAM_DATA_FILE_SUFFIX, "");

        defaultPropertyMap.put(MARKET_STATS_JOB_ENABLED, "true");
        defaultPropertyMap.put(AIRSHIELD_DATASET_QUERY_CODE, "r5cwtllt");
        defaultPropertyMap.put(ZOLON_FACTORY_NAME, "ZOLON");
        defaultPropertyMap.put(SSO_MULTI_MARKET_LIMIT, "5");

        defaultPropertyMap.put(EMM_CLIENT_PACKAGE_NAME, "com.zolon.maxstore.emm");
        defaultPropertyMap.put(EMM_CLIENT_PACKAGE_NAME_US, "com.zolon.maxstore.emm.us");
        defaultPropertyMap.put(EMM_AIRVIEWER_PACKAGE_NAME, "com.pax.posviewer.emm");
        defaultPropertyMap.put(EMM_AIRVIEWER_PLUGIN_PACKAGE_NAME, "com.zolon.airviewer.plugin.zolon");
        defaultPropertyMap.put(AIRVIEWER_TRADITIONAL_MODEL, "IM15,Q58,D270");
        defaultPropertyMap.put(AIRVIEWER_EMM_FACTORY, "MobiWire");
        defaultPropertyMap.put(AIRVIEWER_EMM_DEFAULT_UNATTENDED_FACTORY, "");
        defaultPropertyMap.put(EMM_TOKEN_SIGN_KEY, "eyJ0aXRsZSI6ImZ1Y2sgeW91IHBheSBtZSIsImxlYWQiOiJ1");
        defaultPropertyMap.put(MIN_MOBILE_APP_VERSION, "1.0.0");
        defaultPropertyMap.put(EMM_CUSTOM_POLICY_LIMIT, "1000");
        defaultPropertyMap.put(EMM_POLICY_APP_LIMIT, "100");
        defaultPropertyMap.put(EMM_LOCK_POLICY_LIMIT, "10");
        defaultPropertyMap.put(EMM_POLICY_MESSAGE_SEND_NUMBER_PER_BATCH, "100");
        defaultPropertyMap.put(EMM_ZTE_OPERATION_MESSAGE_SEND_NUMBER_PER_BATCH, "100");
        defaultPropertyMap.put(EMM_NO_AUTH_DEVICE_MESSAGE_SEND_NUMBER_PER_BATCH, "100");
        defaultPropertyMap.put(EMM_CALL_ANDROID_MANAGEMENT_API_LIMIT, "50");
        defaultPropertyMap.put(EMM_UNSUBSCRIBE_DAYS_LIMIT, "30");
        defaultPropertyMap.put(EMM_ENTERPRISE_TOPIC, "[{\"envCode\":\"dev\",\"topic\":\"register-topic-dev\"},{\"envCode\":\"sit\",\"topic\":\"register-topic-sit\"},{\"envCode\":\"docker86\",\"topic\":\"register-topic-docker-sit\"},{\"envCode\":\"usuat\",\"topic\":\"register-topic-usuat\"},{\"envCode\":\"paxus\",\"topic\":\"register-topic-paxus\"},{\"envCode\":\"awsstaging\",\"topic\":\"register-topic-staging\"},{\"envCode\":\"aws\",\"topic\":\"register-topic\"},{\"envCode\":\"ccv\",\"topic\":\"register-topic-ccv\"}]");
        defaultPropertyMap.put(COMMON_PROCESS_BATCH_SIZE, "200");
        defaultPropertyMap.put(ALLOW_MODEL_AUTO_SYNC, "false");
        defaultPropertyMap.put(ALLOW_CHANGE_TERMINAL_MODEL, "false");
        defaultPropertyMap.put(DISABLE_SEARCH_PUSH_HISTORY_WITHOUT_SN, "false");
        defaultPropertyMap.put(WEB_HOOK_MESSAGE_PHYSICAL_DELETE_DATA_DAYS, "180");
        defaultPropertyMap.put(WEB_HOOK_MESSAGE_PHYSICAL_DELETE_BATCH_SIZE, "1000");
        defaultPropertyMap.put(WEB_HOOK_MESSAGE_PHYSICAL_DELETE_ENABLE, "false");
        defaultPropertyMap.put(DELETE_FIRMWARE_AND_APP_FILE_DAYS, "30");
        defaultPropertyMap.put(PUSH_LIMIT_TERMINAL_MAX_NUMBER, "100");

        defaultPropertyMap.put(SKIP_MQ_MESSAGE_NAMES, "");
        defaultPropertyMap.put(AIRLINK_TERMINAL_ACTIVATE_DETAIL_EXPORT_LIMIT_TOTAL, "50000");
        defaultPropertyMap.put(AIRLINK_ESTATE_IMPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(AIRLINK_ESTATE_EXPORT_LIMIT_TOTAL, "5000");
        defaultPropertyMap.put(AIRLINK_TERMINAL_ACTIVE_HISTORY_DURATION, "7");
        defaultPropertyMap.put(AIRLINK_TERMINAL_DATA_EXCEED_WARNING_NUMBER, "5");
    }

    /**
     * Gets int property.
     *
     * @param key          the key
     * @param defaultValue the default value
     * @return the int property
     */
    public static int getIntPropertyValue(String key, String defaultValue) {
        String value = getPropertyValue(key, defaultValue);
        if (StringUtils.isNoneBlank(value) && StringUtils.isInteger(value) && NumberUtils.isNumber(value)) {
            return NumberUtils.toInt(value);
        } else {
            return NumberUtils.toInt(defaultValue);
        }
    }

    /**
     * Gets long property value.
     *
     * @param key          the key
     * @param defaultValue the default value
     * @return the long property value
     */
    public static long getLongPropertyValue(String key, String defaultValue) {
        String value = getPropertyValue(key, defaultValue);
        if (StringUtils.isNoneBlank(value) && NumberUtils.isNumber(value)) {
            return NumberUtils.toLong(value);
        } else {
            return NumberUtils.toLong(defaultValue);
        }
    }

    /**
     * Gets boolean property.
     *
     * @param key          the key
     * @param defaultValue the default value
     * @return the boolean property
     */
    public static boolean getBooleanPropertyValue(String key, String defaultValue) {
        String value = getPropertyValue(key, defaultValue);
        if (StringUtils.isNoneBlank(value)) {
            return StringUtils.toBoolean(value);
        } else {
            return StringUtils.toBoolean(defaultValue);
        }
    }

    /**
     * Gets float property.
     *
     * @param key          the key
     * @param defaultValue the default value
     * @return the float property
     */
    public static double getFloatPropertyValue(String key, String defaultValue) {
        String value = getPropertyValue(key, defaultValue);
        if (StringUtils.isNoneBlank(value) && NumberUtils.isNumber(value)) {
            return NumberUtils.toDouble(value);
        } else {
            return NumberUtils.toDouble(defaultValue);
        }
    }

    /**
     * Gets property value.
     *
     * @param key          the key
     * @param defaultValue the default value
     * @return the property value
     */
    public static String getPropertyValue(String key, String defaultValue) {
        String propertyValue = RedisUtils.get(String.format(CacheNames.SYSTEM_PROPERTY_CACHE, key), null);
        if (propertyValue != null) {
            return propertyValue;
        } else {
            return Global.getConfig(key, defaultValue);
        }
    }

    /**
     * Gets default property value.
     *
     * @param key the key
     * @return the default property value
     */
    public static String getDefaultPropertyValue(String key) {
        return defaultPropertyMap.get(key);
    }

    /**
     * Gets property value.
     *
     * @param property the property
     * @return the property value
     */
    public static String getPropertyValue(SystemProperty property) {
        return switch (property.getType()) {
            case SystemProperty.PROPERTY_TYPE_INTEGER -> String.valueOf(getIntValue(property.getKey()));
            case SystemProperty.PROPERTY_TYPE_LONG -> String.valueOf(getLongValue(property.getKey()));
            case SystemProperty.PROPERTY_TYPE_BOOLEAN -> String.valueOf(getBooleanValue(property.getKey()));
            case SystemProperty.PROPERTY_TYPE_FLOAT -> String.valueOf(getFloatValue(property.getKey()));
            default -> getStringValue(property.getKey());
        };
    }

    /**
     * Gets property value.
     *
     * @param key the key
     * @return the property value
     */
    public static String getStringValue(String key) {
        return getPropertyValue(key, defaultPropertyMap.get(key));
    }

    /**
     * Gets boolean property value.
     *
     * @param key the key
     * @return the boolean property value
     */
    public static boolean getBooleanValue(String key) {
        return getBooleanPropertyValue(key, defaultPropertyMap.get(key));
    }

    /**
     * Gets float property value.
     *
     * @param key the key
     * @return the float property value
     */
    public static double getFloatValue(String key) {
        return getFloatPropertyValue(key, defaultPropertyMap.get(key));
    }

    /**
     * Gets long property value.
     *
     * @param key the key
     * @return the long property value
     */
    public static long getLongValue(String key) {
        return getLongPropertyValue(key, defaultPropertyMap.get(key));
    }

    /**
     * Gets int property value.
     *
     * @param key the key
     * @return the int property value
     */
    public static int getIntValue(String key) {
        return getIntPropertyValue(key, defaultPropertyMap.get(key));
    }

    /**
     * Gets sync import data limit.
     *
     * @return the sync import data limit
     */
    public static int getSyncImportDataLimit() {
        return getIntValue(SYNC_IMPORT_DATA_LIMIT);
    }

    /**
     * Gets sync export data limit.
     *
     * @return the sync export data limit
     */
    public static int getSyncExportDataLimit() {
        return getIntValue(SYNC_EXPORT_DATA_LIMIT);
    }

    /**
     * Gets group max terminal count.
     *
     * @return the group max terminal count
     */
    public static int getGroupMaxTerminalCount() {
        return getIntValue(TERMINAL_GROUP_MAX_TERMINAL_COUNT);
    }

    /**
     * Gets apk icon size max size.
     *
     * @return the apk icon size max size
     */
    public static long getApkIconSizeMaxSize() {
        return getLongValue(APK_ICON_SIZE_MAXIMUM);
    }

    /**
     * Gets download task expire days.
     *
     * @return the download task expire days
     */
    public static int getDownloadTaskExpireDays() {
        return getIntValue(DOWNLOAD_TASK_EXPIRE_DAYS);
    }

    /**
     * Gets download token expire seconds.
     *
     * @return the download token expire seconds
     */
    public static int getDownloadTokenExpireSeconds() {
        return getIntValue(DOWNLOAD_TOKEN_EXPIRE_SECONDS);
    }

    /**
     * Is signature supported boolean.
     *
     * @return the boolean
     */
    public static boolean isSignatureSupported() {
        return getBooleanValue(SIGNATURE_SUPPORTED);
    }

    /**
     * Gets jdbc batch size.
     *
     * @return the jdbc batch size
     */
    public static int getJdbcBatchSize() {
        return getIntValue(JDBC_BATCH_SIZE);
    }

    /**
     * Gets report query batch size.
     *
     * @return the report query batch size
     */
    public static int getReportQueryBatchSize() {
        return getIntValue(REPORT_QUERY_BATCH_SIZE);
    }

    /**
     * Gets terminal no query batch size.
     * @return the terminal no query batch size
     */
    public static int getTerminalNoQueryBatchSize() {
        return getIntValue(TERMINAL_NO_QUERY_BATCH_SIZE);
    }

    /**
     * Gets terminal action retry interval.
     *
     * @return the terminal action retry interval
     */
    public static int getTerminalActionRetryInterval() {
        return getIntValue(TERMINAL_ACTION_RETRY_INTERVAL);
    }

    /**
     * Gets terminal new push task retrieve count per time.
     *
     * @return the terminal new push task retrieve count per time
     */
    public static int getTerminalNewPushTaskRetrieveCountPerTime() {
        return getIntValue(TERMINAL_NEW_PUSH_TASK_RETRIEVE_COUNT_PER_TIME);
    }

    /**
     * Gets rki push task bind count per time.
     *
     * @return the rki push task bind count per time
     */
    public static int getRkiPushTaskBindCountPerTime() {
        return getIntValue(RKI_PUSH_TASK_BIND_COUNT_PER_TIME);
    }

    /**
     * Gets terminal init action retrieve count per time.
     *
     * @return the terminal init action retrieve count per time
     */
    public static int getTerminalInitActionRetrieveCountPerTime() {
        return getIntValue(TERMINAL_INIT_ACTION_RETRIEVE_COUNT_PER_TIME);
    }

    /**
     * Gets terminal pending action retrieve count per time.
     *
     * @return the terminal pending action retrieve count per time
     */
    public static int getTerminalPendingActionRetrieveCountPerTime() {
        return getIntValue(TERMINAL_PENDING_ACTION_RETRIEVE_COUNT_PER_TIME);
    }

    /**
     * Gets min terminal pending action retrieve count per time.
     *
     * @return the min terminal pending action retrieve count per time
     */
    public static int getMinTerminalPendingActionRetrieveCountPerTime() {
        return getIntValue(MIN_TERMINAL_PENDING_ACTION_RETRIEVE_COUNT_PER_TIME);
    }

    /**
     * Gets min terminal init action retrieve count per time.
     *
     * @return the min terminal init action retrieve count per time
     */
    public static int getMinTerminalInitActionRetrieveCountPerTime() {
        return getIntValue(MIN_TERMINAL_INIT_ACTION_RETRIEVE_COUNT_PER_TIME);
    }

    /**
     * Is province enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isProvinceEnabled() {
        return getBooleanValue(GLOBAL_SETTING_PROVINCE_ENABLED);
    }

    /**
     * Gets market domain blacklist.
     *
     * @return the market domain blacklist
     */
    public static List<String> getMarketDomainBlacklist() {
        return StringUtils.splitToList(getStringValue(MARKET_DOMAIN_BLACKLIST), ",");
    }

    /**
     * Gets pre defined merchant types.
     *
     * @return the pre defined merchant types
     */
    public static List<String> getPreDefinedMerchantTypes() {
        return StringUtils.splitToList(getStringValue(PRE_DEFINED_MERCHANT_TYPES), ",");
    }

    /**
     * Gets pre defined reseller roles.
     *
     * @return the pre defined reseller roles
     */
    public static List<Long> getPreDefinedResellerRoles() {
        return StringUtils.splitToLongList(getStringValue(PRE_DEFINED_RESELLER_ROLES), ",");
    }

    /**
     * Gets terminal export limit.
     *
     * @return the terminal export limit
     */
    public static int getTerminalExportLimit() {
        return getIntValue(TERMINAL_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets param compare export limit
     *
     * @return the terminal export limit
     */
    public static int getParamCompareExportLimit() {
        return getIntValue(PARAMETER_COMPARE_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets apk parameter export limit.
     *
     * @return the apk parameter export limit
     */
    public static int getApkParameterExportLimit() {
        return getIntValue(APK_PARAMETER_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets variable export limit.
     *
     * @return the variable export limit
     */
    public static int getVariableExportLimit() {
        return getIntValue(TERMINAL_VARIABLE_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets asset export limit.
     *
     * @return the asset export limit
     */
    public static int getAssetExportLimit() {
        return getIntValue(ASSET_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets user export limit.
     *
     * @return the user export limit
     */
    public static int getUserExportLimit() {
        return getIntValue(USER_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets app scan export limit.
     *
     * @return the user export limit
     */
    public static int getAppScanExportLimit() {
        return getIntValue(APP_SCAN_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets merchant export limit.
     *
     * @return the merchant export limit
     */
    public static int getMerchantExportLimit() {
        return getIntValue(MERCHANT_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets reseller export limit.
     *
     * @return the reseller export limit
     */
    public static int getResellerExportLimit() {
        return getIntValue(RESELLER_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets log export limit.
     *
     * @return the log export limit
     */
    public static int getLogExportLimit() {
        return getIntValue(LOG_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets terminal import limit.
     *
     * @return the terminal import limit
     */
    public static int getTerminalImportLimit() {
        return getIntValue(TERMINAL_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets emm zte audit sc email.
     *
     * @return the emm zte audit sc email
     */
    public static String getEmmZteAuditScEmail() {
        return getStringValue(EMM_ZTE_AUDIT_SC_EMAIL);
    }

    /**
     * Gets emm zte file upload merchant limit.
     *
     * @return the emm zte file upload merchant limit
     */
    public static int getEmmZteFileUploadMerchantLimit() {
        return getIntValue(EMM_ZTE_FILE_UPLOAD_MERCHANT_LIMIT_TOTAL);
    }


    /**
     * Gets tmk import limit.
     *
     * @return the tmk import limit
     */
    public static int getTMKImportLimit() {
        return getIntValue(TMK_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets group terminal import limit.
     *
     * @return the group terminal import limit
     */
    public static int getGroupTerminalImportLimit() {
        return getIntValue(GROUP_TERMINAL_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets assert import limit.
     *
     * @return the assert import limit
     */
    public static int getAssertImportLimit() {
        return getIntValue(ASSERT_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets variable import limit.
     *
     * @return the variable import limit
     */
    public static int getTerminalVariableImportLimit() {
        return getIntValue(TERMINAL_VARIABLE_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets group variable import limit.
     *
     * @return the group variable import limit
     */
    public static int getGroupVariableImportLimit() {
        return getIntValue(GROUP_VARIABLE_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets market variable import limit.
     *
     * @return the market variable import limit
     */
    public static int getMarketVariableImportLimit() {
        return getIntValue(MARKET_VARIABLE_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets merchant variable import limit.
     *
     * @return the merchant variable import limit
     */
    public static int getMerchantVariableImportLimit() {
        return getIntValue(MERCHANT_VARIABLE_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets merchant import limit.
     *
     * @return the merchant import limit
     */
    public static int getMerchantImportLimit() {
        return getIntValue(MERCHANT_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Gets app signature failed export limit.
     *
     * @return the app signature failed export limit
     */
    public static int getAppSignatureFailedExportLimit() {
        return getIntValue(APP_SIGNATURE_FAILED_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Is captcha aspect enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isCaptchaAspectEnabled() {
        return getBooleanValue(CAPTCHA_ASPECT_ENABLED);
    }

    /**
     * Is basic auth enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isBasicAuthEnabled() {
        return getBooleanValue(SECURITY_BASIC_AUTH_ENABLED);
    }

    /**
     * Gets notification delivery batch size.
     *
     * @return the notification delivery batch size
     */
    public static int getNotificationDeliveryBatchSize() {
        return getIntValue(NOTIFICATION_DELIVERY_BATCH_SIZE);
    }

    /**
     * Gets notification recently active user check days.
     *
     * @return the notification recently active user check days
     */
    public static int getNotificationRecentlyActiveUserCheckDays() {
        return getIntValue(NOTIFICATION_RECENTLY_ACTIVE_USER_CHECK_DAYS);
    }

    /**
     * Gets notification top x unread message list count.
     *
     * @return the notification top x unread message list count
     */
    public static int getNotificationTopXUnreadMessageListCount() {
        return getIntValue(NOTIFICATION_TOPX_UNREAD_MESSAGES_LIST_COUNT);
    }

    /**
     * Gets notification message archive check days.
     *
     * @return the notification message archive check days
     */
    public static int getNotificationMessageArchiveCheckDays() {
        return getIntValue(NOTIFICATION_MESSAGE_ARCHIVE_CHECK_DAYS);
    }

    /**
     * Gets notification recently active user check days.
     *
     * @return the notification recently active user check days
     */
    public static int getAppSubscriptionListExportLimitTotal() {
        return getIntValue(APP_SUBSCRIPTION_LIST_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets notification recently active user check days.
     *
     * @return the notification recently active user check days
     */
    public static int getFirmwareSubscriptionListExportLimitTotal() {
        return getIntValue(FIRMWARE_SUBSCRIPTION_LIST_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Is token check request ip and user agent boolean.
     *
     * @return the boolean
     */
    public static boolean isTokenCheckRequestIpAndUserAgent() {
        return getBooleanValue(TOKEN_CHECK_REMOTEIP_USERAGENT);
    }

    /**
     * Gets file client prefix.
     *
     * @return the file client prefix
     */
    public static String getFileClientPrefix() {
        return getStringValue(FILE_CLIENT_PREFIX);
    }

    /**
     * Gets chunk file size.
     *
     * @return the chunk file size
     */
    public static long getChunkFileSize() {
        return (long) getIntValue(CHUNK_FILE_SIZE) * 1024 * 1024;
    }

    /**
     * Gets chunk file pending total limit.
     *
     * @return the chunk file size pending total limit
     */
    public static long getChunkFilePendingTotalLimit() {
        return getIntValue(CHUNK_FILE_PENDING_TOTAL_LIMIT);
    }

    /**
     * Is aws download param by signed url enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isAwsDownloadParamBySignedUrlEnabled() {
        return getBooleanValue(AWS_3RDAPP_DOWNLOAD_PARAM_BY_SIGNED_URL_ENABLED);
    }

    /**
     * Is export installed app in monthly report boolean.
     *
     * @return the boolean
     */
    public static boolean isExportInstalledAppInMonthlyReport() {
        return getBooleanValue(EXPORT_INSTALLED_APP_IN_MONTHLY_REPORT);
    }

    /**
     * Gets max page count.
     *
     * @return the max page count
     */
    public static int getPageLimit() {
        return getIntValue(PAGE_LIMIT);
    }

    /**
     * Gets terminal push task page limit.
     *
     * @return the terminal push task page limit
     */
    public static int getTerminalPushTaskPageLimit() {
        return getIntValue(TERMINAL_PUSH_TASK_PAGE_LIMIT);
    }

    /**
     * Is download logcat in terminal detail page
     *
     * @return boolean boolean
     */
    public static boolean isDownloadLogcatEnabled() {
        return getBooleanValue(LOGCAT_DOWNLOAD_ENABLE);
    }

    /**
     * Get max apk online count
     *
     * @return max apk online count
     */
    public static int getMaxApkOnlineCount() {
        return getIntValue(APK_ONLINE_LIMIT_TOTAL);
    }

    /**
     * Get market max terminal count
     *
     * @return market max terminal count
     */
    public static long getMarketMaxTerminalCount() {
        return getLongValue(MARKET_TERMINAL_LIMIT_TOTAL);
    }

    /**
     * Gets variable keys not allow same value.
     *
     * @return the variable keys not allow same value
     */
    public static String getVariableKeysNotAllowSameValue() {
        return getStringValue(VARIABLE_KEYS_NOT_ALLOW_SAME_VALUE);
    }

    /**
     * Get is allow register user
     *
     * @return boolean boolean
     */
    public static boolean isRegisterUserEnabled() {
        return getBooleanValue(REGISTER_USER_ENABLE);
    }

    /**
     * Gets internal third party sys key.
     *
     * @return the internal third party sys key
     */
    public static String getInternalThirdPartySysKey() {
        return getStringValue(INTERNAL_THIRD_PARTY_SYS_KEY);
    }

    /**
     * Gets uptrillion third party sys key.
     *
     * @return the internal third party sys key
     */
    public static String getUptrillionThirdPartySysKey() {
        return getStringValue(UPTRILLION_THIRD_PARTY_SYS_KEY);
    }

    /**
     * Gets internal third party sys signature.
     *
     * @return the internal third party sys signature
     */
    public static String getInternalThirdPartySysSignature() {
        return getStringValue(INTERNAL_THIRD_PARTY_SYS_SIGNATURE);
    }

    /**
     * Gets uptrillion third party sys signature.
     *
     * @return the internal third party sys signature
     */
    public static String getUptrillionThirdPartySysSignature() {
        return getStringValue(UPTRILLION_THIRD_PARTY_SYS_SIGNATURE);
    }

    /**
     * Gets uptrillion third party sys access market.
     *
     * @return the internal third party sys market
     */
    public static String getUptrillionThirdPartySysAccessMarket() {
        return StringUtils.lowerCase(getStringValue(UPTRILLION_THIRD_PARTY_SYS_ACCESS_MARKET));
    }

    /**
     * Is check push history.
     *
     * @return the boolean
     */
    public static boolean isCheckPushHistory() {
        return getBooleanValue(CHECK_PUSH_HISTORY);
    }

    /**
     * Gets profile command send number per batch.
     *
     * @return the profile command send number per batch
     */
    public static int getProfileCommandSendNumberPerBatch() {
        return getIntValue(PROFILE_COMMAND_SEND_NUMBER_PER_BATCH);
    }

    /**
     * Gets force update command send number per batch.
     *
     * @return the force update command send number per batch
     */
    public static int getForceUpdateCommandSendNumberPerBatch() {
        return getIntValue(FORCE_UPDATE_COMMAND_SEND_NUMBER_PER_BATCH);
    }

    /**
     * Gets resend email period.
     *
     * @return the resend email period
     */
    public static int getResendEmailPeriod() {
        return getIntValue(RESEND_EMAIL_INTERVAL);
    }

    /**
     * Gets rki client package names.
     *
     * @return the rki client package names
     */
    public static List<String> getRKIClientPackageNames() {
        return StringUtils.splitToList(getStringValue(RKI_CLIENT_PACKAGE_NAME), ",");
    }

    /**
     * Gets min app open sdk version.
     *
     * @return the expired 3 rd app sdk version
     */
    public static String getMinStoreSdkVersion() {
        return getStringValue(MIN_STORE_SDK_VERSION);
    }

    /**
     * Gets lower store sdk forbidden date.
     *
     * @return the lower store sdk forbidden date
     */
    public static String getLowerStoreSdkForbiddenDate() {
        return getStringValue(LOWER_STORE_SDK_FORBIDDEN_DATE);
    }

    /**
     * Gets Apk file max size.
     *
     * @return the Apk file max size.
     */
    public static int getApkFileMaxSize() {
        return getIntValue(APK_FILE_MAX_SIZE);
    }

    /**
     * Gets import excel file max size.
     *
     * @return the import excel file max size
     */
    public static int getImportExcelFileMaxSize() {
        return getIntValue(IMPORT_EXCEL_FILE_MAX_SIZE);
    }

    /**
     * Gets firmware file max size.
     *
     * @return the firmware file max size
     */
    public static int getFirmwareFileMaxSize() {
        return getIntValue(FIRMWARE_FILE_MAX_SIZE);
    }

    /**
     * Gets firmware resource file max size.
     *
     * @return the firmware resource file max size
     */
    public static int getFirmwareResourceFileMaxSize() {
        return getIntValue(FIRMWARE_RESOURCE_FILE_MAX_SIZE);
    }


    /**
     * Gets max num of terminals in one purchase.
     *
     * @return the max num of terminals in one purchase
     */
    public static int getMaxNumOfTerminalsInOnePurchase() {
        return getIntValue(MAX_NUM_OF_TERMINALS_IN_ONE_PURCHASE);
    }

    /**
     * Gets app max price.
     *
     * @return the app max price
     */
    public static double getAppMaxPrice() {
        return getFloatValue(APP_MAX_PRICE);
    }

    /**
     * Gets app max price.
     *
     * @return the app max price
     */
    public static Integer getAppMaxTrial() {
        return getIntValue(APP_MAX_TRIAL);
    }

    /**
     * Gets firmware modem file max size.
     *
     * @return the firmware modem file max size
     */
    public static int getFirmwareModemFileMaxSize() {
        return getIntValue(FIRMWARE_MODEM_FILE_MAX_SIZE);
    }

    /**
     * Is create terminal push history boolean.
     *
     * @return the boolean
     */
    public static boolean isCreateTerminalPushHistory() {
        return getBooleanValue(CREATE_TERMINAL_PUSH_HISTORY);
    }

    /**
     * Is allow release note boolean
     *
     * @return the boolean
     */
    public static boolean isAllowReleaseNote() {
        return getBooleanValue(ALLOW_RELEASE_NOTE);
    }

    /**
     * Is allow airViewer logcat boolean
     *
     * @return the boolean
     */
    public static boolean isAllowAirViewerLogcat() {
        return getBooleanValue(ALLOW_AIRVIEWER_LOGCAT);
    }

    /**
     * Is allow user change email boolean.
     *
     * @return the boolean
     */
    public static boolean isAllowUserChangeEmail() {
        return getBooleanValue(ALLOW_USER_CHANGE_EMAIL);
    }

    /**
     * Is check terminal model boolean.
     *
     * @return the boolean
     */
    public static boolean isCheckTerminalModel() {
        return getBooleanValue(CHECK_TERMINAL_MODEL);
    }

    /**
     * Is ped key status enable boolean
     *
     * @return boolean boolean
     */
    public static boolean isPedKeyStatusEnabled() {
        return getBooleanValue(PED_KEY_STATUS_ENABLE);
    }

    /**
     * Gets Link expiration time.
     *
     * @return the Link expiration time
     */
    public static int getLinkExpirationHour() {
        return getIntValue(LINK_EXPIRATION_HOUR);
    }

    /**
     * Is allow reseller tid setting boolean.
     *
     * @return the boolean
     */
    public static boolean isAllowResellerTidSetting() {
        return getBooleanValue(ALLOW_RESELLER_TID_SETTING);
    }

    /**
     * Gets report max export field size.
     *
     * @return the report max export field size
     */
    public static int getReportMaxExportFieldSize() {
        return getIntValue(REPORT_MAX_EXPORT_FIELD_SIZE);
    }

    /**
     * Gets Terminal Offline export limit
     *
     * @return the terminal Offline export limit size
     */
    public static int getTerminalOfflineExportLimit() {
        return getIntValue(TERMINAL_OFFLINE_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets Pax factory default ID
     *
     * @return Pax factory default ID
     */
    public static long getPaxFactoryDefaultId() {
        return getLongValue(PAX_FACTORY_DEFAULT_ID);
    }


    /**
     * Gets bypass terminal urls.
     *
     * @return the bypass terminal urls
     */
    public static String getBypassTerminalUrls() {
        return getStringValue(BYPASS_TERMINAL_URLS);
    }

    /**
     * Is log app search history boolean.
     *
     * @return the boolean
     */
    public static boolean isLogAppSearchHistory() {
        return getBooleanValue(LOG_APP_SEARCH_HISTORY);
    }

    /**
     * Gets announcement message limit
     *
     * @return the announcement message limit
     */
    public static int getAnnouncementLimit() {
        return getIntValue(ANNOUNCEMENT_LIMIT);
    }

    /**
     * Gets User active to suspend retention hours.
     *
     * @return the suspend retention hours
     */
    public static int getUserActiveToSuspendRetentionHours() {
        return getIntValue(USER_ACTIVE_TO_SUSPEND_RETENTION_HOURS);
    }

    /**
     * Gets User active to suspend retention hours.
     *
     * @return the suspend retention hours
     */
    public static boolean getAllowBuriedPointDataCollection() {
        return getBooleanValue(ALLOW_BURIED_POINT_DATA_COLLECTION);
    }

    /**
     * Gets debug apk cert dn.
     *
     * @return the debug apk cert dn
     */
    public static String getDebugApkCertDn() {
        return getStringValue(DEBUG_APK_CERT_DN);
    }

    /**
     * Is enable refresh reseller installed apks boolean.
     *
     * @return the boolean
     */
    public static boolean isEnableDailyRefreshResellerInstalledApks() {
        return getBooleanValue(ENABLE_DAILY_REFRESH_RESELLER_INSTALLED_APKS);
    }

    /**
     * Is enable daily update group pending actions boolean.
     *
     * @return the boolean
     */
    public static boolean isEnableDailyUpdateGroupPendingActions() {
        return getBooleanValue(ENABLE_DAILY_UPDATE_GROUP_PENDING_ACTIONS);
    }

    /**
     * Get export report time limit
     *
     * @return the int
     */
    public static int getExportReportTimeLimit() {
        return getIntValue(EXPORT_REPORT_TIME_LIMIT);
    }

    /**
     * Get export schedule report limit
     *
     * @return the int
     */
    public static int getExportScheduleReportLimit() {
        return getIntValue(EXPORT_SCHEDULE_REPORT_LIMIT);
    }

    /**
     * Is enable backup terminal history actions boolean.
     *
     * @return the boolean
     */
    public static boolean isEnableBackupTerminalHistoryActions() {
        return getBooleanValue(ENABLE_BACKUP_TERMINAL_HISTORY_ACTIONS);
    }

    public static boolean isEnableConvertTerminalLastApkParamVariables() {
        return getBooleanValue(ENABLE_CONVERT_TERMINAL_LAST_APK_PARAM_VARIABLES);
    }

    /**
     * Is refresh terminal last apk param boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshTerminalLastApkParam() {
        return getBooleanValue(REFRESH_TERMINAL_LAST_APK_PARAM);
    }

    /**
     * Is refresh terminal last launcher boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshTerminalLastLauncher() {
        return getBooleanValue(REFRESH_TERMINAL_LAST_LAUNCHER);
    }


    /**
     * Is refresh push task download time boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshPushTaskDownloadTime() {
        return getBooleanValue(REFRESH_PUSH_TASK_DOWNLOAD_TIME);
    }

    /**
     * Is refresh push task action time boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshPushTaskActionTime() {
        return getBooleanValue(REFRESH_PUSH_TASK_ACTION_TIME);
    }

    /**
     * Is refresh push entity market id boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshPushTaskRelatedMarketId() {
        return getBooleanValue(REFRESH_PUSH_TASK_RELATED_MARKET_ID);
    }

    /**
     * Is refresh param task sha 256 boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshParamTaskSha256() {
        return getBooleanValue(REFRESH_PARAM_TASK_SHA256);
    }

    /**
     * Is generate param file map boolean.
     *
     * @return the boolean
     */
    public static boolean isGenerateParamFileMap() {
        return getBooleanValue(GENERATE_PARAM_FILE_MAP);
    }

    /**
     * Is convert param variables boolean.
     *
     * @return the boolean
     */
    public static boolean isConvertParamVariables() {
        return getBooleanValue(CONVERT_PARAM_VARIABLES);
    }

    /**
     * Is refresh complete active push task status boolean.
     *
     * @return the boolean
     */
    public static boolean isRefreshCompleteActivePushTaskStatus() {
        return getBooleanValue(REFRESH_COMPLETE_ACTIVE_PUSH_TASK_STATUS);
    }

    /**
     * Is allow set readonly parameters boolean.
     *
     * @return the boolean
     */
    public static boolean isAllowSetReadonlyParameters() {
        return getBooleanValue(ALLOW_SET_READONLY_PARAMETERS);
    }

    /**
     * Gets developer registration fee
     *
     * @return the developer registration fee
     */
    public static long getDeveloperRegistrationFee() {
        return getLongValue(DEVELOPER_REGISTRATION_FEE);
    }

    /**
     * Gets max apk diff count.
     *
     * @return the max apk diff count
     */
    public static int getMaxApkDiffCount() {
        return getIntValue(MAX_APK_DIFF_COUNT);
    }

    /**
     * Gets max store client diff count.
     *
     * @return the max store client diff count
     */
    public static int getMaxStoreClientDiffCount() {
        return getIntValue(MAX_STORE_CLIENT_DIFF_COUNT);
    }

    /**
     * get alarm receiver email max
     *
     * @return the max
     */
    public static int getAlarmReceiverEmailMax() {
        return getIntValue(ALARM_RECEIVER_EMAIL_MAX);
    }

    /**
     * Gets max page size.
     *
     * @return the max page size
     */
    public static int getMaxPageSize() {
        return Math.max(100, getIntValue(MAX_PAGE_SIZE));
    }

    /**
     * Gets Allow Sync Terminal Basic Info.
     *
     * @return the Allow Sync Terminal Basic Info
     */
    public static boolean getAllowSyncTerminalBasicInfo() {
        return getBooleanValue(ALLOW_SYNC_TERMINAL_BASIC_INFO);
    }

    /**
     * Gets Allow Sync Terminal App Info.
     *
     * @return the Allow Sync Terminal App Info
     */
    public static boolean getAllowSyncTerminalAppInfo() {
        return getBooleanValue(ALLOW_SYNC_TERMINAL_APP_INFO);
    }

    /**
     * Gets allow sync terminal app full data.
     *
     * @return the allow sync terminal app full
     */
    public static boolean getAllowSyncTerminalAppFull() {
        return getBooleanValue(ALLOW_SYNC_TERMINAL_APP_FULL_DATA);
    }

    /**
     * Gets allow sync terminal basic full data.
     *
     * @return the allow sync terminal basic full
     */
    public static boolean getAllowSyncTerminalBasicFull() {
        return getBooleanValue(ALLOW_SYNC_TERMINAL_BASIC_FULL_DATA);
    }

    /**
     * Gets insight query data time unit limit.
     *
     * @return the insight query data time unit limit
     */
    public static int getInsightQueryDataTimeUnitLimit() {
        return getIntValue(INSIGHT_QUERY_DATA_TIME_UNIT_LIMIT);
    }

    /**
     * Gets insight query data time unit minute.
     *
     * @return the insight query data time unit minute
     */
    public static int getInsightQueryDataTimeUnitMinute() {
        return getIntValue(INSIGHT_QUERY_DATA_TIME_UNIT_MINUTE);
    }

    /**
     * Gets show service agreement.
     *
     * @return the show service agreement
     */
    public static boolean getShowServiceAgreement() {
        return getBooleanValue(SHOW_SERVICE_AGREEMENT);
    }

    /**
     * Get terminal history data time limit switch
     *
     * @return switch insight terminal history data time limit switch
     */
    public static boolean getInsightTerminalHistoryDataTimeLimitSwitch() {
        return getBooleanValue(INSIGHT_TERMINAL_HISTORY_DATA_TIME_LIMIT_SWITCH);
    }

    /**
     * Get terminal operation timeout long.
     *
     * @return the long
     */
    public static long getTerminalOperationTimeout() {
        return getLongValue(TERMINAL_OPERATION_TIMEOUT);
    }


    /**
     * Get terminal operation timeout long.
     *
     * @return the long
     */
    public static long getTerminalCollectLogTimeout() {
        return getLongValue(TERMINAL_COLLECT_LOG_TIMEOUT);
    }

    /**
     * Is enable daily clean deleted terminals boolean.
     *
     * @return the boolean
     */
    public static boolean isEnableDailyCleanDeletedTerminals() {
        return getBooleanValue(ENABLE_DAILY_CLEAN_DELETED_TERMINALS);
    }

    /**
     * Get physical delete days
     *
     * @return physical delete days
     */
    public static int getPhysicalDeleteDays() {
        return getIntValue(PHYSICAL_DELETE_DATA_DAYS);
    }

    /**
     * Is enable daily clear data
     *
     * @return boolean boolean
     */
    public static boolean isEnableDailyClearData() {
        return getBooleanValue(ENABLE_DAILY_CLEAR_DATA);
    }

    /**
     * Is enable daily expire terminal actions boolean.
     *
     * @return the boolean
     */
    public static boolean isEnableDailyExpireTerminalActions() {
        return getBooleanValue(ENABLE_DAILY_EXPIRE_TERMINAL_ACTIONS);
    }

    /**
     * Gets global statistics market export limit
     *
     * @return the export limit size
     */
    public static int getGlobalStatisticsMarketExportLimit() {
        return getIntValue(GLOBAL_STATISTICS_MARKET_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets global statistics app export limit
     *
     * @return the export limit size
     */
    public static int getGlobalStatisticsAppExportLimit() {
        return getIntValue(GLOBAL_STATISTICS_APP_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets global statistics developer export limit
     *
     * @return the export limit size
     */
    public static int getGlobalStatisticsDeveloperExportLimit() {
        return getIntValue(GLOBAL_STATISTICS_DEVELOPER_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets call installed app limit.
     *
     * @return the call installed app limit
     */
    public static int getCallInstalledAppLimit() {
        return getIntValue(CALL_INSTALLED_APP_URL_LIMIT);
    }

    /**
     * Gets physical delete terminal days.
     *
     * @return the physical delete terminal days
     */
    public static int getPhysicalDeleteTerminalDays() {
        return getIntValue(PHYSICAL_DELETE_TERMINAL_DAYS);
    }

    /**
     * Gets rki auth system enabled.
     *
     * @return the rki auth system enabled
     */
    public static boolean getRkiAuthSystemEnabled() {
        return getBooleanValue(RKI_AUTH_SYSTEM_ENABLED);
    }

    /**
     * Gets default unattended models.
     *
     * @return the default unattended models
     */
    public static String getDefaultUnattendedModels() {
        return getStringValue(DEFAULT_UNATTENDED_MODEL);
    }

    /**
     * Gets table split record batch size
     *
     * @return the batch limit size
     */
    public static int getTableSplitRecordBatchSize() {
        return getIntValue(TABLE_SPLIT_RECORD_BATCH_SIZE);
    }

    /**
     * Gets merchant type import limit total
     *
     * @return the limit total
     */
    public static int getMerchantTypeImportLimitTotal() {
        return getIntValue(MERCHANT_TYPE_IMPORT_LIMIT_TOTAL);
    }

    /**
     * Is cloud msg enabled in dev portal boolean.
     *
     * @return the boolean
     */
    public static boolean isCloudMsgEnabledInDevPortal() {
        return getBooleanValue(VasConfigPropertyKey.CLOUDMSG_ENABLE_IN_DEV_PORTAL);
    }

    /**
     * Gets vas platform api base url.
     *
     * @return the vas platform api base url
     */
    public static String getVasPlatformApiBaseUrl() {
        return getStringValue(VasConfigPropertyKey.VASPLATFORM_API_BASE_URL);
    }

    /**
     * Gets insight api internal url.
     *
     * @return the insight api internal url
     */
    public static String getInsightApiInternalUrl() {
        return getStringValue(VASConstants.VasConfigPropertyKey.INSIGHT_DATA_API_INTERNAL_URL);
    }

    /**
     * Get app tag limit integer.
     *
     * @return the integer
     */
    public static Integer getAppTagLimit() {
        return getIntValue(APP_TAG_LIMIT);
    }

    /**
     * Get dashboard widget refresh minutes integer.
     *
     * @return the integer
     */
    public static Integer getDashboardWidgetRefreshMinutes() {
        return getIntValue(DASHBOARD_WIDGET_REFRESH_MINUTES);
    }

    /**
     * Get dashboard widget force refresh minutes integer.
     *
     * @return the integer
     */
    public static Integer getDashboardWidgetForceRefreshMinutes() {
        return getIntValue(DASHBOARD_WIDGET_FORCE_REFRESH_MINUTES);
    }

    /**
     * Gets report datasource refresh minutes.
     *
     * @return the report datasource refresh minutes
     */
    public static Integer getReportDatasourceRefreshMinutes() {
        return getIntValue(REPORT_DATASOURCE_REFRESH_MINUTES);
    }

    /**
     * Get rki connection timeout int.
     *
     * @return the int
     */
    public static int getRkiConnectionTimeout() {
        return getIntValue(RKI_CONNECTION_TIMEOUT);
    }

    /**
     * Get rki request timeout int.
     *
     * @return the int
     */
    public static int getRkiRequestTimeout() {
        return getIntValue(RKI_REQUEST_TIMEOUT);
    }

    /**
     * Get rki auth system url string.
     *
     * @return the string
     */
    public static String getRkiAuthSystemUrl() {
        return getStringValue(RKI_AUTH_SYSTEM_URL);
    }

    /**
     * Get rki auth system platform id string.
     *
     * @return the string
     */
    public static String getRkiAuthSystemPlatformId() {
        return getStringValue(RKI_AUTH_SYSTEM_PLATFORM_ID);
    }

    /**
     * Get rki auth system secret string.
     *
     * @return the string
     */
    public static String getRkiAuthSystemSecret() {
        return getStringValue(RKI_AUTH_SYSTEM_SECRET);
    }

    /**
     * Get show system hidden config boolean.
     *
     * @return the boolean
     */
    public static boolean getShowSystemHiddenConfig() {
        return getBooleanValue(SHOW_SYSTEM_HIDDEN_CONFIG);
    }

    /**
     * Get insight data api url string.
     *
     * @return the string
     */
    public static String getInsightDataApiUrl() {
        return getStringValue(VasConfigPropertyKey.INSIGHT_DATA_API_URL);
    }

    /**
     * Gets insight data web api url.
     *
     * @return the insight data web api url
     */
    public static String getInsightDataWebApiUrl() {
        return getStringValue(VasConfigPropertyKey.INSIGHT_DATA_WEB_API_URL);
    }

    /**
     * Get data scope filter mode int.
     *
     * @return the int
     */
    public static int getDataScopeFilterMode() {
        return getIntValue(DATA_SCOPE_FILTER_MODE);
    }

    /**
     * Is use agreement default boolean.
     *
     * @return the boolean
     */
    public static boolean isUseAgreementDefault() {
        return getBooleanValue(USE_AGREEMENT_DEFAULT);
    }

    /**
     * Gets zolon billing center host.
     *
     * @return the zolon billing center host
     */
    public static String getZolonBillingCenterHost() {
        return getStringValue(ZOLON_BILLING_CENTER_HOST);
    }


    /**
     * Gets third party system ip limit.
     * MARKET AND RESELLER
     *
     * @return the third party sys ip limit
     */
    public static int getThirdPartySysIpLimit() {
        return getIntValue(THIRD_PARTY_SYS_IP_LIMIT);
    }

    /**
     * Gets third party system ip bind data limit.
     * MARKET AND RESELLER
     *
     * @return the third party sys ip date
     */
    public static String getThirdPartySysIpBindLimitDate() {
        return getStringValue(THIRD_PARTY_SYS_IP_BIND_LIMIT_DATE);
    }


    /**
     * Get support team emails list.
     *
     * @return the list
     */
    public static List<String> getSupportTeamEmails() {
        return StringUtils.splitToList(getStringValue(SUPPORT_TEAM_EMAILS), ",");
    }

    /**
     * Gets extraction code expiration days.
     *
     * @return the extraction code expiration days.
     */
    public static int getExtractionCodeExpirationDays() {
        return getIntValue(EXTRACTION_CODE_EXPIRATION_DAY);
    }

    /**
     * Gets group push terminal limit.
     *
     * @return the group push terminal limit
     */
    public static int getGroupPushTerminalLimit() {
        return getIntValue(GROUP_PUSH_TERMINAL_LIMIT);
    }

    /**
     * Gets report record export limit.
     *
     * @return the report record export limit
     */
    public static int getReportRecordExportLimit() {
        return getIntValue(REPORT_RECORD_EXPORT_LIMIT);
    }

    /**
     * Is log api enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isLogApiEnabled() {
        return getBooleanValue(LOG_API_ENABLED);
    }

    /**
     * Gets log api over time mills.
     *
     * @return the log api over time mills
     */
    public static int getLogApiOverTimeMills() {
        return getIntValue(LOG_API_OVER_TIME_MILLS);
    }

    /**
     * Is log sql enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isLogSqlEnabled() {
        return getBooleanValue(LOG_SQL_ENABLED);
    }

    /**
     * Gets log sql over time mills.
     *
     * @return the log sql over time mills
     */
    public static int getLogSqlOverTimeMills() {
        return getIntValue(LOG_SQL_OVER_TIME_MILLS);
    }

    /**
     * Is scheduled tasks can be triggered manually
     *
     * @return boolean boolean
     */
    public static boolean isScheduleTriggerEnabled() {
        return getBooleanValue(SCHEDULE_TRIGGER_ENABLED);
    }

    /**
     * Is long hui environment
     *
     * @return boolean boolean
     */
    public static boolean isLongHuiEnvironment() {
        return getBooleanValue(LONG_HUI_ENV_ENABLED);
    }

    /**
     * CloudMessage每次批量发送终端最大数量
     *
     * @return int int
     */
    public static int getCloudMsgBatchSendSize() {
        return getIntValue(CLOUD_MSG_BATCH_SEND_SIZE);
    }

    /**
     * Iot Platform Api访问URL
     *
     * @return int string
     */
    public static String getIotPlatformApiUrl() {
        return getStringValue(IOT_PLATFORM_API_URL);
    }

    /**
     * Iot Platform Api访问Access Key
     *
     * @return int string
     */
    public static String getIotPlatformApiAccessKey() {
        return getStringValue(IOT_PLATFORM_API_ACCESS_KEY);
    }

    /**
     * The Migration AuditTrail Enabled
     *
     * @return the boolean
     */
    public static boolean getMigrationAuditTrailEnabled() {
        return getBooleanValue(MIGRATION_AUDIT_TRAIL_ENABLE);
    }

    /**
     * GET Migration AuthLog Enabled
     *
     * @return the boolean
     */
    public static boolean getMigrationAuthLogEnabled() {
        return getBooleanValue(MIGRATION_AUTH_LOG_ENABLE);
    }

    /**
     * Gets migration billing data enabled.
     *
     * @return the migration billing data enabled
     */
    public static boolean getMigrationBillingDataEnabled() {
        return getBooleanValue(MIGRATION_BILLING_DATA_ENABLE);
    }

    /**
     * Gets migration terminal apk param enabled.
     *
     * @return the migration terminal apk param enabled.
     */
    public static boolean getMigrationTerminalApkParamEnabled() {
        return getBooleanValue(MIGRATION_TERMINAL_APK_PARAM_ENABLE);
    }

    /**
     * Gets migration terminal group apk param enabled.
     *
     * @return the migration terminal group apk param enabled.
     */
    public static boolean getMigrationTerminalGroupApkParamEnabled() {
        return getBooleanValue(MIGRATION_TERMINAL_GROUP_APK_PARAM_ENABLE);
    }

    /**
     * Gets migration apk param template enabled.
     *
     * @return the migration apk param template enabled.
     */
    public static boolean getMigrationApkParamTemplateEnabled() {
        return getBooleanValue(MIGRATION_APK_PARAM_TEMPLATE_ENABLE);
    }

    /**
     * Gets migration sandbox terminal apk param enabled.
     *
     * @return the migration sandbox terminal apk param enabled.
     */
    public static boolean getMigrationSandboxTerminalApkParamEnabled() {
        return getBooleanValue(MIGRATION_SANDBOX_TERMINAL_APK_PARAM_ENABLE);
    }

    /**
     * Gets migration terminal enroll details enabled.
     *
     * @return the migration terminal enroll details enabled
     */
    public static boolean getMigrationTerminalEnrollDetailsEnabled() {
        return getBooleanValue(MIGRATION_TERMINAL_ENROLL_DETAILS_ENABLE);
    }

    /**
     * Gets geidea access months.
     *
     * @return the geidea access months
     */
    public static int getGeideaAccessMonths() {
        return getIntValue(BILLING_GEIDEA_MODE_ACCESS_MONTHS);
    }

    /**
     * Gets migration airViewer usage detail enabled.
     *
     * @return the migration terminal enroll details enabled
     */
    public static boolean getMigrationAirViewerUsageDetailsEnabled() {
        return getBooleanValue(MIGRATION_AIRVIEWER_USAGE_DETAILS_ENABLE);
    }

    /**
     * Navigo Assistant访问URL
     *
     * @return int string
     */
    public static String getNavigoAssistantApiUrl() {
        return getStringValue(NAVIGO_ASSISTANT_API_URL);
    }

    /**
     * Navigo Assistant Api访问Access Key
     *
     * @return int string
     */
    public static String getNavigoAssistantApiAccessKey() {
        return getStringValue(NAVIGO_ASSISTANT_API_ACCESS_KEY);
    }

    /**
     * Get Navigo Assistant timeout long.
     *
     * @return the long
     */
    public static long getNavigoAssistantSocketConnectionTimeout() {
        return getLongValue(NAVIGO_ASSISTANT_SOCKET_CONNECTION_TIME);
    }

    /**
     * Get Navigo Assistant IO long.
     *
     * @return the long
     */
    public static long getNavigoAssistantSocketIOTimeout() {
        return getLongValue(NAVIGO_ASSISTANT_SOCKET_IO_TIME);
    }

    /**
     * GET Complete Migration AuthLog
     *
     * @return the boolean
     */
    public static boolean getRequestUrlSignatureEnabled() {
        return getBooleanValue(REQUEST_URL_SIGNATURE_ENABLED);
    }

    /**
     * GET Complete Migration AuthLog
     *
     * @return the boolean
     */
    public static boolean getAuthLogCompleted() {
        return getBooleanValue(MIGRATION_AUTH_LOG_COMPLETED);
    }

    /**
     * GET Complete Migration AuditTrail
     *
     * @return the boolean
     */
    public static boolean getAuditTrailCompleted() {
        return getBooleanValue(MIGRATION_AUDIT_TRAIL_COMPLETED);
    }

    /**
     * Gets migration filter action batch size.
     *
     * @return the migration filter action batch size
     */
    public static int getMigrationFilterActionBatchSize() {
        return getIntValue(MIGRATION_FILTER_ACTION_BATCH_SIZE);
    }

    /**
     * Is migration group filtered action boolean.
     *
     * @return the boolean
     */
    public static boolean isMigrationGroupFilteredAction() {
        return getBooleanValue(MIGRATION_GROUP_FILTERED_ACTION);
    }

    /**
     * Gets current terminal enroll market.
     *
     * @return the current terminal enroll market
     */
    public static String getCurrentTerminalEnrollMarket() {
        return getStringValue(CURRENT_TERMINAL_ENROLL_MARKET);
    }

    /**
     * Is allow gen stats puk boolean.
     *
     * @return the boolean
     */
    public static boolean isAllowGenStatsPUK() {
        return getBooleanValue(ALLOW_GEN_STATS_PUK);
    }

    /**
     * Gets reseller tree record size.
     *
     * @return the reseller tree record size
     */
    public static int getResellerTreeRecordSize() {
        return getIntValue(RESELLER_TREE_RECORD_SIZE);
    }


    /**
     * Gets limit footer num.
     *
     * @return the limit footer num
     */
//9.2业务限制
    public static int getLimitFooterNum() {
        return getIntValue(LIMIT_FOOTER_NUM);
    }

    /**
     * Gets limit merchant type num.
     *
     * @return the limit merchant type num
     */
    public static int getLimitMerchantTypeNum() {
        return getIntValue(LIMIT_MERCHANT_TYPE_NUM);
    }

    /**
     * Gets limit attribute num.
     *
     * @return the limit attribute num
     */
    public static int getLimitAttributeNum() {
        return getIntValue(LIMIT_ATTRIBUTE_NUM);
    }

    /**
     * Gets limit user agreement num.
     *
     * @return the limit user agreement num
     */
    public static int getLimitUserAgreementNum() {
        return getIntValue(LIMIT_USER_AGREEMENT_NUM);
    }


    /**
     * Gets limit developer agreement num.
     *
     * @return the limit developer agreement num
     */
    public static int getLimitDeveloperAgreementNum() {
        return getIntValue(LIMIT_DEVELOPER_AGREEMENT_NUM);
    }

    /**
     * Gets limit developer num.
     *
     * @return the limit developer num
     */
    public static int getLimitDeveloperNum() {
        return getIntValue(LIMIT_DEVELOPER_AGREEMENT_NUM);
    }

    /**
     * Gets limit webhook num.
     *
     * @return the limit webhook num
     */
    public static int getLimitWebhookNum() {
        return getIntValue(LIMIT_WEBHOOK_NUM);
    }

    /**
     * Gets limit invoice receiver num.
     *
     * @return the limit invoice receiver num
     */
    public static int getLimitInvoiceReceiverNum() {
        return getIntValue(LIMIT_INVOICE_RECEIVER_NUM);
    }

    /**
     * Gets limit non removable app num.
     *
     * @return the limit non removable app num
     */
    public static int getLimitNonRemovableAppNum() {
        return getIntValue(LIMIT_NON_REMOVABLE_APP_NUM);
    }

    /**
     * Gets limit sensitive words num.
     *
     * @return the limit sensitive words num
     */
    public static int getLimitSensitiveWordsNum() {
        return getIntValue(LIMIT_SENSITIVE_WORDS_NUM);
    }

    /**
     * Gets limit store client num.
     *
     * @return the limit store client num
     */
    public static int getLimitStoreClientNum() {
        return getIntValue(LIMIT_STORE_CLIENT_NUM);
    }

    /**
     * Gets limit role num.
     *
     * @return the limit role num
     */
    public static int getLimitRoleNum() {
        return getIntValue(LIMIT_ROLE_NUM);
    }

    /**
     * Gets limit firmware num.
     *
     * @return the limit firmware num
     */
    public static int getLimitFirmwareNum() {
        return getIntValue(LIMIT_FIRMWARE_NUM);
    }


    /**
     * Gets fuzzy query provider.
     *
     * @return the fuzzy query provider
     */
    public static String getFuzzyQueryProvider() {
        return getStringValue(FUZZY_QUERY_PROVIDER);
    }

    /**
     * Is terminal fuzzy query disabled boolean.
     *
     * @return the boolean
     */
    public static boolean isTerminalFuzzyQueryDisabled() {
        return getBooleanValue(FUZZY_QUERY_TERMINAL_DISABLED);
    }

    /**
     * Gets cyber lab blacklists limit.
     *
     * @return the cyber lab blacklists limit
     */
    public static int getCyberLabBlacklistsLimit() {
        return getIntValue(CYBER_LAB_BLACKLISTS_LIMIT);
    }

    /**
     * Gets limit ip whitelist num.
     *
     * @return the limit ip whitelist num
     */
    public static int getLimitIpWhitelistNum() {
        return getIntValue(LIMIT_IP_WHITELIST_NUM);
    }

    /**
     * Gets limit user per role num.
     *
     * @return the limit user per role num
     */
    public static int getLimitUserPerRoleNum() {
        return getIntValue(LIMIT_USER_PER_ROLE_NUM);
    }

    /**
     * Gets user reseller merchant retrieve limit.
     *
     * @return the user reseller merchant retrieve limit
     */
//used by dao.xml
    public static int getUserResellerMerchantRetrieveLimit() {
        return getIntValue(USER_RESELLER_MERCHANT_RETRIEVE_LIMIT);
    }

    /**
     * Gets reseller migrate child limit.
     *
     * @return the reseller migrate child limit
     */
    public static int getResellerMigrateChildLimit() {
        return getIntValue(RESELLER_MIGRATE_CHILD_LIMIT);
    }

    /**
     * Gets merchant migrate terminal limit.
     *
     * @return the merchant migrate terminal limit
     */
    public static int getMerchantMigrateTerminalLimit() {
        return getIntValue(MERCHANT_MIGRATE_TERMINAL_LIMIT);
    }

    /**
     * Gets group uninstall app limit.
     *
     * @return the group uninstall app limit
     */
    public static int getGroupUninstallAppLimit() {
        return getIntValue(GROUP_UNINSTALL_APP_LIMIT);
    }

    /**
     * Gets group push message limit.
     *
     * @return the group push message limit
     */
    public static int getGroupPushMessageLimit() {
        return getIntValue(GROUP_PUSH_MESSAGE_LIMIT);
    }

    /**
     * Gets air shield dataset query code.
     *
     * @return the air shield dataset query code
     */
    public static String getAirShieldDatasetQueryCode() {
        return getStringValue(AIRSHIELD_DATASET_QUERY_CODE);
    }

    /**
     * Gets zolon factory name.
     *
     * @return the zolon factory name
     */
    public static String getZolonFactoryName() {
        return getStringValue(ZOLON_FACTORY_NAME);
    }

    /**
     * Gets ad visual video max duration.
     *
     * @return the ad visual video max duration
     */
    public static long getAdVisualVideoMaxDuration() {
        return getLongValue(AD_VISUAL_VIDEO_MAX_DURATION);
    }

    /**
     * Gets geo fence white list batch limit.
     *
     * @return the geo fence white list batch limit
     */
    public static int getGeoFenceWhiteListBatchLimit() {
        return getIntValue(GEOFENCE_WHITELIST_BATCH_LIMIT);
    }

    /**
     * Gets emm client package name.
     *
     * @return the emm client package name
     */
    public static String getEmmClientPackageName() {
        StoreDeployInfoConfigProps configProps = SpringContextHolder.getBean(StoreDeployInfoConfigProps.class);
        return StringUtils.equals(configProps.getEnvCode(), Profiles.PAXUS) || StringUtils.equals(configProps.getEnvCode(), Profiles.USUAT)
                ? getStringValue(EMM_CLIENT_PACKAGE_NAME_US)
                : getStringValue(EMM_CLIENT_PACKAGE_NAME);
    }

    /**
     * Gets EMM air viewer package name.
     *
     * @return the EMM air viewer package name
     */
    public static String getEmmAirViewerPackageName() {
        return getStringValue(EMM_AIRVIEWER_PACKAGE_NAME);
    }

    /**
     * Gets EMM air viewer plugin package name.
     *
     * @return the EMM air viewer plugin package name
     */
    public static String getEmmAirViewerPluginPackageName() {
        return getStringValue(EMM_AIRVIEWER_PLUGIN_PACKAGE_NAME);
    }


    /**
     * Gets air viewer traditional models.
     *
     * @return the air viewer traditional models
     */
    public static String getAirViewerTraditionalModels() {
        return getStringValue(AIRVIEWER_TRADITIONAL_MODEL);
    }

    /**
     * Gets air viewer EMM factory.
     *
     * @return the air viewer EMM factory
     */
    public static String getAirViewerEmmFactory() {
        return getStringValue(AIRVIEWER_EMM_FACTORY);
    }

    /**
     * Gets air viewer EMM default unattended factory.
     *
     * @return the air viewer EMM default unattended factory
     */
    public static String getAirViewerEmmDefaultUnattendedFactory() {
        return getStringValue(AIRVIEWER_EMM_DEFAULT_UNATTENDED_FACTORY);
    }

    /**
     * Gets emm token sign key.
     *
     * @return the emm token sign key
     */
    public static String getEmmTokenSignKey() {
        return getStringValue(EMM_TOKEN_SIGN_KEY);
    }

    /**
     * Gets emm custom policy limit.
     *
     * @return the emm custom policy limit
     */
    public static int getEmmCustomPolicyLimit() {
        return getIntValue(EMM_CUSTOM_POLICY_LIMIT);
    }

    /**
     * Gets emm policy app limit.
     *
     * @return the emm policy app limit
     */
    public static int getEmmPolicyAppLimit() {
        return getIntValue(EMM_POLICY_APP_LIMIT);
    }

    /**
     * Gets emm lock policy limit.
     *
     * @return the emm lock policy limit
     */
    public static int getEmmLockPolicyLimit() {
        return getIntValue(EMM_LOCK_POLICY_LIMIT);
    }

    /**
     * Gets emm policy message send number per batch.
     *
     * @return the emm policy message send number per batch
     */
    public static int getEmmPolicyMessageSendNumberPerBatch() {
        return getIntValue(EMM_POLICY_MESSAGE_SEND_NUMBER_PER_BATCH);
    }

    /**
     * Gets emm no auth device message send number per batch.
     *
     * @return the emm no auth device message send number per batch
     */
    public static int getEmmNoAuthDeviceMessageSendNumberPerBatch() {
        return getIntValue(EMM_NO_AUTH_DEVICE_MESSAGE_SEND_NUMBER_PER_BATCH);
    }

    /**
     * Gets emm zte operation message send number per batch.
     *
     * @return the emm zte operation message send number per batch
     */
    public static int getEmmZteOperationMessageSendNumberPerBatch() {
        return getIntValue(EMM_ZTE_OPERATION_MESSAGE_SEND_NUMBER_PER_BATCH);
    }

    /**
     * Gets emm call android management api per batch.
     *
     * @return the emm call android management api per batch
     */
    public static int getEmmCallAndroidManagementApiPerBatch() {
        return getIntValue(EMM_CALL_ANDROID_MANAGEMENT_API_LIMIT);
    }

    /**
     * Get emm unsubscribe days limit int.
     *
     * @return the int
     */
    public static int getEmmUnsubscribeDaysLimit() {
        return getIntValue(EMM_UNSUBSCRIBE_DAYS_LIMIT);
    }

    /**
     * Get EMM enterprise topic.
     *
     * @return the string
     */
    public static String getEmmEnterpriseTopic() {
        return getStringValue(EMM_ENTERPRISE_TOPIC);
    }


    /**
     * Gets apk param data file suffix.
     *
     * @return the apk param data file suffix
     */
    public static List<String> getApkParamDataFileSuffix() {
        String apkParamDataFileSuffix = getStringValue(APK_PARAM_DATA_FILE_SUFFIX);
        if (StringUtils.isBlank(apkParamDataFileSuffix)) {
            return Lists.newArrayList();
        }
        return Arrays.asList(StringUtils.splitByWholeSeparatorPreserveAllTokens(apkParamDataFileSuffix, ","));
    }

    /**
     * Is market stats job enabled boolean.
     *
     * @return the boolean
     */
    public static boolean isMarketStatsJobEnabled() {
        return getBooleanValue(MARKET_STATS_JOB_ENABLED);
    }

    /**
     * Gets sso multi market limit.
     *
     * @return the sso multi market limit
     */
    public static int getSsoMultiMarketLimit() {
        return getIntValue(SSO_MULTI_MARKET_LIMIT);
    }

    /**
     * Gets min mobile app version.
     *
     * @return the min mobile app version
     */
    public static String getMinMobileAppVersion() {
        return getStringValue(MIN_MOBILE_APP_VERSION);
    }

    /**
     * Gets latest mobile app version.
     *
     * @return the latest mobile app version
     */
    public static String getLatestMobileAppVersion() {
        return getStringValue(LATEST_MOBILE_APP_VERSION);
    }

    /**
     * Gets auth sso jwt signing key.
     *
     * @return the auth sso jwt signing key
     */
    public static String getAuthSsoJwtSigningKey() {
        return getStringValue(AUTH_SSO_JWT_SIGNING_KEY);
    }

    /**
     * Gets email token secret.
     *
     * @return the email token secret
     */
    public static String getEmailTokenSecret() {
        return getStringValue(EMAIL_TOKEN_SECRET);
    }

    /**
     * Gets download token secret.
     *
     * @return the download token secret
     */
    public static String getDownloadTokenSecret() {
        return getStringValue(DOWNLOAD_TOKEN_SECRET);
    }

    /**
     * Gets common process batch size.
     *
     * @return the common process batch size
     */
    public static int getCommonProcessBatchSize() {
        return getIntValue(COMMON_PROCESS_BATCH_SIZE);
    }

    /**
     * Is allow model auto sync boolean.
     *
     * @return the boolean
     */
    public static boolean isAllowModelAutoSync() {
        return getBooleanValue(ALLOW_MODEL_AUTO_SYNC);
    }


    /**
     * Is allow change terminal model boolean.
     *
     * @return the boolean
     */
    public static boolean isAllowChangeTerminalModel() {
        return getBooleanValue(ALLOW_CHANGE_TERMINAL_MODEL);
    }


    /**
     * Is allow search push history without sn boolean.
     *
     * @return the boolean
     */
    public static boolean isDisableSearchPushHistoryWithoutSn() {
        return getBooleanValue(DISABLE_SEARCH_PUSH_HISTORY_WITHOUT_SN);
    }

    /**
     * Gets web hook message physical delete data days.
     *
     * @return the web hook message physical delete data days
     */
    public static int getWebHookMessagePhysicalDeleteDataDays() {
        return getIntValue(WEB_HOOK_MESSAGE_PHYSICAL_DELETE_DATA_DAYS);
    }


    /**
     * Gets web hook message physical delete batch size.
     *
     * @return the web hook message physical delete batch size
     */
    public static int getWebHookMessagePhysicalDeleteBatchSize() {
        return getIntValue(WEB_HOOK_MESSAGE_PHYSICAL_DELETE_BATCH_SIZE);
    }

    /**
     * Gets web hook message physical delete enable.
     *
     * @return the web hook message physical delete enable
     */
    public static boolean getWebHookMessagePhysicalDeleteEnable() {
        return getBooleanValue(WEB_HOOK_MESSAGE_PHYSICAL_DELETE_ENABLE);
    }

    /**
     * Get push limit max terminal number
     * @return the push limit max terminal number
     */
    public static int getPushLimitMaxTerminalNumber() {
        return getIntValue(PUSH_LIMIT_TERMINAL_MAX_NUMBER);
    }

    /**
     * Gets delete firmware and app file days.
     *
     * @return the delete firmware and app file days
     */
    public static int getDeleteFirmwareAndAppFileDays() {
        return getIntValue(DELETE_FIRMWARE_AND_APP_FILE_DAYS);
    }

    /**
     * Retrieves a list of mq message names to be skipped.
     * <p>
     * This method fetches the value of the property defined by the key SKIP_MESSAGE_NAMES.
     * If the property value is blank or null, an empty list is returned.
     * Otherwise, the property value is split by commas into a list of handler names, preserving all tokens.
     *
     * @return List<String> A list of message handler names to be skipped. Returns an empty list if the property is not set or is blank.
     */
    public static List<String> getSkipMqMessageNames() {
        String skipMqMessageNames = getStringValue(SKIP_MQ_MESSAGE_NAMES);
        if (StringUtils.isBlank(skipMqMessageNames)) {
            return Lists.newArrayList();
        }
        return Arrays.asList(StringUtils.splitByWholeSeparatorPreserveAllTokens(skipMqMessageNames, ","));
    }

    /**
     * Gets airlink terminal activate detail limit.
     *
     * @return the user export limit
     */
    public static int getAirLinkTerminalActivateExportLimit() {
        return getIntValue(AIRLINK_TERMINAL_ACTIVATE_DETAIL_EXPORT_LIMIT_TOTAL);
    }

    /**
     * Gets airlink estate import limit.
     *
     * @return the airlink estate import limit
     */
    public static int getAirLinkEstateImportLimit() {
        return getIntValue(AIRLINK_ESTATE_IMPORT_LIMIT_TOTAL);
    }


    /**
     * Gets airlink estate export limit.
     *
     * @return the airlink estate export limit
     */
    public static int getAirLinkEstateExportLimit() {
        return getIntValue(AIRLINK_ESTATE_EXPORT_LIMIT_TOTAL);
    }

    public static int getAirLinkTerminalActiveHistoryDuration(){
        return getIntValue(AIRLINK_TERMINAL_ACTIVE_HISTORY_DURATION);
    }

    /**
     * Get data usage exceeds the warning number.(GB)
     *
     * @return the data usage exceeds the warning number.
     */
    public static int getAirLinkUsageExceedsWarningNumber() {
        return getIntValue(AIRLINK_TERMINAL_DATA_EXCEED_WARNING_NUMBER);
    }


}
