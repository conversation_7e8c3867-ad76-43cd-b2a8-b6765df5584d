package com.pax.market.domain.searchcriteria;

import com.pax.market.domain.entity.global.report.ReportTask;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
public class ReportTaskSearchCriteria extends ReportTask {
    private Long userId;
    private Long marketId;
    private Long resellerId;
    private Set<String> statuses;
    private Set<String> recurrences;
}
