package com.pax.market.domain.util;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.emm.*;
import com.pax.market.domain.entity.market.emm.EmmAppPolicy;
import com.pax.market.domain.entity.market.emm.EmmPolicy;
import com.pax.market.domain.entity.market.emm.EmmPolicySetting;
import com.pax.market.dto.audit.log.admin.market.emm.EmmPolicyLog;
import com.pax.market.dto.emm.EmmAppPolicyInfo;
import com.pax.market.dto.emm.EmmPolicyDTO;
import com.pax.market.dto.emm.EmmPolicyInfo;
import com.pax.market.dto.emm.PolicyInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.Profiles;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.vo.admin.management.emm.EmmPolicyVo;
import com.pax.support.config.props.StoreDeployInfoConfigProps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Stream;

import static com.pax.market.constants.emm.EmmConstants.EMM_AIRVIEWER_TRACK_ID;

public class EmmPolicyConverter {


    public static EmmPolicyVo convertEmmPolicyVo(List<EmmPolicySetting> policySettings,
                                                 EmmPolicyInfo emmPolicyInfo,
                                                 List<EmmAppPolicyInfo> emmAppPolicyInfos,
                                                 int customPolicyTotalCount,
                                                 Map<String, String> inheritedPolicyLock,
                                                 Map<String, String> inheritedAppPolicyLock) {
        EmmPolicyVo emmPolicyVo = new EmmPolicyVo();
        List<EmmPolicyVo.Policy> policyVos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(policySettings) || Objects.isNull(emmPolicyInfo)) {
            return emmPolicyVo;
        }

        buildEmmPolicy(policySettings, emmPolicyInfo, policyVos, inheritedPolicyLock);
        buildEmmAppPolicy(emmPolicyInfo, emmAppPolicyInfos, policyVos, inheritedAppPolicyLock);

        List<EmmPolicyVo.Policy> generalSettingPolicyList = Lists.newArrayList();
        List<EmmPolicyVo.Policy> restrictionPolicyList = Lists.newArrayList();
        List<EmmPolicyVo.Policy> securityPolicyList = Lists.newArrayList();
        List<EmmPolicyVo.Policy> communicationNetworkPolicyList = Lists.newArrayList();
        List<EmmPolicyVo.Policy> applicationPolicyList = Lists.newArrayList();
        List<String> kioskModeAppNames = Lists.newArrayListWithExpectedSize(SystemPropertyHelper.getEmmPolicyAppLimit());
        for (EmmPolicyVo.Policy policy : policyVos) {
            if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_GENERAL_SETTING_TITLE)) {
                if (EmmPolicyConfiguration.parse(policy.getKey()) == EmmPolicyConfiguration.ENABLE_MULTI_APP_KIOSK_MODE
                        && BooleanUtils.toBoolean(policy.getValue())
                        && BooleanUtils.toBoolean(policy.getChangeFlag())) {
                    emmPolicyVo.setEnableMultiAppKioskMode(Boolean.TRUE);
                }
                generalSettingPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_RESTRICTION_TITLE)) {
                restrictionPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_SECURITY_TITLE)) {
                securityPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_COMMUNICATION_NETWORK_TITLE)) {
                communicationNetworkPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_APPLICATION_TITLE)) {
                if (EmmAppInstallType.parse(policy.getInstallType()) == EmmAppInstallType.KIOSK) {
                    kioskModeAppNames.add(policy.getAppName());
                }
                applicationPolicyList.add(policy);
            }
        }
        emmPolicyVo.setCustomPolicyTotalCount(customPolicyTotalCount);
        emmPolicyVo.setKioskModeAppNames(kioskModeAppNames);
        return emmPolicyVo.setPolicyType(
                new EmmPolicyVo.PolicyType()
                .setGeneralSettingPolicyList(generalSettingPolicyList)
                .setRestrictionPolicyList(restrictionPolicyList)
                .setSecurityPolicyList(securityPolicyList)
                .setCommunicationNetworkPolicyList(communicationNetworkPolicyList)
                .setApplicationPolicyList(applicationPolicyList));
    }


    public static EmmPolicyLog convertEmmPolicyLog(List<EmmPolicySetting> policySettings,
                                                              List<EmmPolicyInfo.Policy> policyList,
                                                              List<EmmAppPolicyInfo> emmAppPolicyInfos) {

        EmmPolicyLog emmPolicyLog = new EmmPolicyLog();
        List<EmmPolicyLog.Policy> policyLogs = Lists.newArrayList();
        List<EmmPolicyLog.Policy> generalSettingPolicyList = Lists.newArrayList();
        List<EmmPolicyLog.Policy> restrictionPolicyList = Lists.newArrayList();
        List<EmmPolicyLog.Policy> securityPolicyList = Lists.newArrayList();
        List<EmmPolicyLog.Policy> communicationNetworkPolicyList = Lists.newArrayList();
        List<EmmPolicyLog.Policy> applicationPolicyList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(policySettings)) {
            return emmPolicyLog;
        }
        buildEmmPolicyLog(policySettings, policyList, policyLogs);

        buildEmmAppPolicyLog(emmAppPolicyInfos, policyLogs);

        policyLogs.forEach(policy -> {
            if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_GENERAL_SETTING_TITLE)) {
                generalSettingPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_RESTRICTION_TITLE)) {
                restrictionPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_SECURITY_TITLE)) {
                securityPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_COMMUNICATION_NETWORK_TITLE)) {
                communicationNetworkPolicyList.add(policy);
            } else if (StringUtils.equals(policy.getUiTitle(), EmmConstants.EMM_POLICY_APPLICATION_TITLE)) {
                applicationPolicyList.add(policy);
            }
        });

        return emmPolicyLog.setPolicyType(
                new EmmPolicyLog.PolicyType()
                        .setGeneralSettingPolicyList(generalSettingPolicyList)
                        .setRestrictionPolicyList(restrictionPolicyList)
                        .setSecurityPolicyList(securityPolicyList)
                        .setCommunicationNetworkPolicyList(communicationNetworkPolicyList)
                        .setApplicationPolicyList(applicationPolicyList));

    }

    public static List<EmmPolicyVo.Policy> toPolicyList(EmmPolicyVo.PolicyType policyType) {
        return Stream.of(
                policyType.getRestrictionPolicyList(),
                policyType.getSecurityPolicyList(),
                policyType.getCommunicationNetworkPolicyList(),
                policyType.getGeneralSettingPolicyList(),
                policyType.getApplicationPolicyList()
        ).flatMap(Collection::stream).toList();
    }

    public static List<EmmPolicy> toEmmPolicyList(List<EmmPolicyVo.Policy> policyList, EmmPolicyDTO emmPolicyDTO) {

        List<EmmPolicyVo.Policy> emmPolicyVoList = policyList.stream()
                .filter(policy -> !StringUtils.equals(policy.getUiType(), EmmConstants.EMM_POLICY_APPLICATION_TYPE))
                .toList();

        List<EmmPolicy> emmPolicyList = Lists.newArrayListWithExpectedSize(emmPolicyVoList.size());

        for (EmmPolicyVo.Policy policy : emmPolicyVoList) {
            WebCryptoUtils.unMaskAndDecryptPasswordEmmPolicy(policy, emmPolicyDTO);
            convertDateIgnoreLeapYear(policy);
            EmmPolicy emmPolicy = BeanMapper.map(policy, EmmPolicy.class);
            emmPolicy.setType(emmPolicyDTO.getPolicyType());
            emmPolicy.setReferenceId(emmPolicyDTO.getReferenceId());
            if (JsonMapper.isJsonValue(policy.getValue())) {
                emmPolicy.setValue(null);
                emmPolicy.setJsonValue(policy.getValue());
            } else {
                emmPolicy.setJsonValue(null);
                emmPolicy.setValue(policy.getValue());
            }
            if (BooleanUtils.isFalse(policy.getChangeFlag())) {
                emmPolicy.setValue(null);
                emmPolicy.setJsonValue(null);
            }
            emmPolicyList.add(emmPolicy);
        }
        return emmPolicyList;
    }

    /**
     * 将 <code>2-29</code>日期 转为<code>2-28</code>，计算日期间隔也会忽略<code>2-29</code>
     *
     * @param policy the policy from front-end
     */
    private static void convertDateIgnoreLeapYear(EmmPolicyVo.Policy policy){
        if(Objects.nonNull(policy)){
            switch (Objects.requireNonNull(EmmPolicyConfiguration.parse(policy.getKey()))) {
                case SYSTEM_UPDATE_FREEZE_PERIOD -> {
                    if (StringUtils.isNotBlank(policy.getValue())) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, Map.class);
                        List<Map<String, String>> allFreezePeriodMap = JsonMapper.fromJsonString(policy.getValue(), javaType);
                        if (CollectionUtils.isNotEmpty(allFreezePeriodMap)) {
                            allFreezePeriodMap.forEach(freezePeriod -> {
                                if (StringUtils.equals(freezePeriod.get("startDate"),"02-29")) {
                                    freezePeriod.put("startDate", "02-28");
                                }
                                if (StringUtils.equals(freezePeriod.get("endDate"),"02-29")) {
                                    freezePeriod.put("endDate", "02-28");
                                }
                            });
                            policy.setValue(JsonMapper.toJsonString(allFreezePeriodMap));
                        }
                    }
                }
                default -> {
                }
            }
        }
    }


    public static List<EmmAppPolicy> toEmmAppPolicyList(List<EmmPolicyVo.Policy> policyList, EmmPolicyDTO emmPolicyDTO, boolean rootResellerFlag) {

        List<EmmPolicyVo.Policy> emmAppPolicyVoList = policyList.stream()
                .filter(policy -> StringUtils.equals(policy.getUiType(), EmmConstants.EMM_POLICY_APPLICATION_TYPE))
                .toList();

        List<EmmAppPolicy> emmAppPolicyList = Lists.newArrayListWithExpectedSize(emmAppPolicyVoList.size());

        for (EmmPolicyVo.Policy policy : emmAppPolicyVoList) {
            EmmAppPolicy emmAppPolicy = BeanMapper.map(policy, EmmAppPolicy.class);
            if(!rootResellerFlag){
                emmAppPolicy.setMcmId(null);
            }
            emmAppPolicy.setReferenceId(emmPolicyDTO.getReferenceId());
            emmAppPolicy.setType(emmPolicyDTO.getPolicyType());
            emmAppPolicyList.add(emmAppPolicy);
        }
        return emmAppPolicyList;

    }

    private static void buildEmmPolicy(List<EmmPolicySetting> policySettings,
                                       EmmPolicyInfo emmPolicyInfo,
                                       List<EmmPolicyVo.Policy> policyVos,
                                       Map<String, String> inheritedPolicyLock) {
        Map<String, EmmPolicyInfo.Policy> policyMap = Maps.newTreeMap();
        for (EmmPolicyInfo.Policy item : emmPolicyInfo.getPolicyList()) {
            policyMap.put(item.getKey(), item);
        }
        for (EmmPolicySetting policySetting : policySettings) {
            EmmPolicyVo.Policy policyVo = BeanMapper.map(policySetting, EmmPolicyVo.Policy.class);
            if (policyMap.containsKey(policySetting.getKey())) {
                EmmPolicyInfo.Policy policy = policyMap.get(policySetting.getKey());
                WebCryptoUtils.maskPasswordEmmPolicy(policy);
                policyVo.setChangeFlag(policy.getChangeFlag());
                policyVo.setFullyManageFlag(EmmPolicyConfiguration.isFullManagement(policy.getKey()));
                if (StringUtils.isNotBlank(policy.getValue())) {
                    policyVo.setValue(policy.getValue());
                } else if (StringUtils.isNotBlank(policy.getJsonValue())) {
                    policyVo.setValue(policy.getJsonValue());
                }
                if (inheritedPolicyLock.containsKey(policy.getKey())) {
                    policyVo.setLockedByReseller(inheritedPolicyLock.get(policy.getKey()));
                }
                if (BooleanUtils.isFalse(emmPolicyInfo.getPolicyExist())
                        || (Objects.nonNull(emmPolicyInfo.getInherited())
                        && BooleanUtils.isTrue(emmPolicyInfo.getInherited()))) {
                    policyVo.setLockFlag(Boolean.FALSE);
                } else {
                    policyVo.setLockFlag(policy.getLockFlag());
                }
            }
            policyVo.setInstallPriority(null);
            policyVo.setPermissionGrants(null);
            policyVo.setPermissionSetting(null);
            policyVo.setMcmId(null);
            policyVo.setSupportManagedConfig(null);
            policyVos.add(policyVo);
        }
    }

    private static void buildEmmAppPolicy(EmmPolicyInfo emmPolicyInfo,
                                          List<EmmAppPolicyInfo> emmAppPolicyInfos,
                                          List<EmmPolicyVo.Policy> policyVos,
                                          Map<String, String> inheritedAppPolicyLock) {
        for (EmmAppPolicyInfo info : emmAppPolicyInfos) {
            EmmPolicyVo.Policy policyVo = BeanMapper.map(info, EmmPolicyVo.Policy.class);
            policyVo.setValue(null);
            policyVo.setFullyManageFlag(null);
            policyVo.setChangeFlag(null);
            policyVo.setKey(info.getPackageName());
            policyVo.setMcmId(info.getMcmId());
            policyVo.setSupportManagedConfig(info.getSupportManagedConfig());
            policyVo.setUiTitle(EmmConstants.EMM_POLICY_APPLICATION_TITLE);
            policyVo.setUiType(EmmConstants.EMM_POLICY_APPLICATION_TYPE);
            if (inheritedAppPolicyLock.containsKey(String.valueOf(info.getAppId()))) {
                policyVo.setLockedByReseller(inheritedAppPolicyLock.get(String.valueOf(info.getAppId())));
            }
            if (BooleanUtils.isFalse(emmPolicyInfo.getPolicyExist())
                    || (Objects.nonNull(emmPolicyInfo.getInherited())
                    && BooleanUtils.isTrue(emmPolicyInfo.getInherited()))) {
                policyVo.setLockFlag(Boolean.FALSE);
            } else {
                policyVo.setLockFlag(info.getLockFlag());
            }
            policyVos.add(policyVo);
        }
    }


    private static void buildEmmPolicyLog(List<EmmPolicySetting> policySettings, List<EmmPolicyInfo.Policy> policyList, List<EmmPolicyLog.Policy> policyLogs) {
        Map<String, EmmPolicySetting> settingMap = Maps.newTreeMap();
        for (EmmPolicySetting item : policySettings) {
            settingMap.put(item.getKey(), item);
        }
        for (EmmPolicyInfo.Policy policy : policyList) {
            if (settingMap.containsKey(policy.getKey())) {
                WebCryptoUtils.maskPasswordEmmPolicy(policy);
                EmmPolicySetting setting = settingMap.get(policy.getKey());
                EmmPolicyLog.Policy policyLog = BeanMapper.map(setting, EmmPolicyLog.Policy.class);
                policyLog.setLockedByReseller(null);
                policyLog.setPermissionSetting(null);
                policyLog.setMcmId(null);
                policyLog.setMcmName(null);
                policyLog.setSupportManagedConfig(null);
                policyLog.setChangeFlag(policy.getChangeFlag());
                policyLog.setLockFlag(policy.getLockFlag());
                policyLog.setFullyManageFlag(EmmPolicyConfiguration.isFullManagement(policy.getKey()));
                if (StringUtils.isNotBlank(policy.getValue())) {
                    policyLog.setValue(policy.getValue());
                } else if (StringUtils.isNotBlank(policy.getJsonValue())) {
                    policyLog.setValue(policy.getJsonValue());
                }
                policyLogs.add(policyLog);
            }
        }
    }


    private static void buildEmmAppPolicyLog(List<EmmAppPolicyInfo> emmAppPolicyInfos,
                                             List<EmmPolicyLog.Policy> policyLogs) {
        for (EmmAppPolicyInfo info : emmAppPolicyInfos) {
            EmmPolicyLog.Policy policyLog = BeanMapper.map(info, EmmPolicyLog.Policy.class);
            policyLog.setValue(null);
            policyLog.setFullyManageFlag(null);
            policyLog.setChangeFlag(null);
            policyLog.setKey(info.getPackageName());
            policyLog.setUiTitle(EmmConstants.EMM_POLICY_APPLICATION_TITLE);
            policyLog.setUiType(EmmConstants.EMM_POLICY_APPLICATION_TYPE);
            policyLogs.add(policyLog);
        }
    }

    public static PolicyInfo toPolicyInfo(List<EmmPolicyInfo.Policy> emmPolicyList, List<EmmAppPolicyInfo> emmAppPolicyList) {
        List<EmmPolicyInfo.Policy> policyList = emmPolicyList.stream().filter(policy -> BooleanUtils.isTrue(policy.getChangeFlag())).toList();

        PolicyInfo policyInfo = new PolicyInfo();
        PolicyInfo.DeviceRadioState deviceRadioState = new PolicyInfo.DeviceRadioState();
        PolicyInfo.DeviceConnectivityManagement deviceConnectivityManagement = new PolicyInfo.DeviceConnectivityManagement();
        PolicyInfo.AlwaysOnVpnPackage alwaysOnVpnPackage = new PolicyInfo.AlwaysOnVpnPackage();
        PolicyInfo.AdvancedSecurityOverrides advancedSecurityOverrides = new PolicyInfo.AdvancedSecurityOverrides();
        PolicyInfo.SystemUpdate systemUpdate = new PolicyInfo.SystemUpdate();
        PolicyInfo.PasswordRequirements passwordRequirements = new PolicyInfo.PasswordRequirements();
        PolicyInfo.KioskCustomization kioskCustomization = new PolicyInfo.KioskCustomization();
        List<String> keyguardDisabledFeatures = Lists.newArrayListWithExpectedSize(12);
        List<PolicyInfo.ApplicationPolicy> applications = Lists.newArrayListWithExpectedSize(emmAppPolicyList.size() + 1);
        buildBasePolicy(policyInfo, applications);

        for (EmmPolicyInfo.Policy policy : policyList) {
            if (Objects.nonNull(policy)) {
                switch (Objects.requireNonNull(EmmPolicyConfiguration.parse(policy.getKey()))) {
                    case DISABLE_AIRPLANE_MODE -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            deviceRadioState.setAirplaneModeState(AirplaneModeState.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_OUTGOING_CALLS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setOutgoingCallsDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_SMS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setSmsDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_CELL_BROADCASTS_CONFIG ->{
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setCellBroadcastsConfigDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_BLUETOOTH ->{
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setBluetoothDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case MINIMUM_WIFI_SECURITY_LEVEL -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            deviceRadioState.setMinimumWifiSecurityLevel(EmmMinimumWifiSecurityLevel.parse(policy.getValue()).toString());
                        }
                    }
                    case WIFI_CONFIG -> {
                       if (StringUtils.isNotBlank(policy.getJsonValue())) {
                           JavaType javaType = JsonMapper.getInstance().createCollectionType(List.class, Map.class);
                           List<Map<String, Object>> mapList = JsonMapper.fromJsonString(policy.getJsonValue(), javaType);
                           List<Map<String, Object>> networkConfigurationMaps = Lists.newArrayListWithExpectedSize(mapList.size());
                           for (Map<String , Object> map : mapList) {
                               Map<String, Object> networkConfigurationMapInfo = Maps.newHashMapWithExpectedSize(5);
                               networkConfigurationMapInfo.put("Name", map.get("SSID"));
                               networkConfigurationMapInfo.put("Type", "WiFi");
                               networkConfigurationMapInfo.put("GUID", UUID.randomUUID().toString());
                               Map<String, Object> wifiMapInfo = Maps.newHashMapWithExpectedSize(4);
                               wifiMapInfo.put("SSID", map.get("SSID"));
                               wifiMapInfo.put("Security", EmmWifiSecurity.parse((Integer) map.get("cipherType")).getGoogleValue());
                               wifiMapInfo.put("AutoConnect", true);
                               wifiMapInfo.put("Passphrase", map.get("password"));
                               if (EmmWifiProxy.parse((Integer) map.get("proxyType")) != EmmWifiProxy.NONE) {
                                   Map<String, Object> proxySettingsMapInfo = Maps.newHashMapWithExpectedSize(4);
                                   if (EmmWifiProxy.parse((Integer) map.get("proxyType")) == EmmWifiProxy.MANUAL) {
                                       Map<String,Map<String,Object>> httpProxy = Maps.newHashMapWithExpectedSize(1);
                                       Map<String,Object> hostAndPort = Maps.newHashMapWithExpectedSize(2);
                                       hostAndPort.put("Host", map.get("hostName"));
                                       hostAndPort.put("Port", map.get("port"));
                                       httpProxy.put("HTTPProxy", hostAndPort);
                                       proxySettingsMapInfo.put(EmmWifiProxy.MANUAL.getGoogleValue(),httpProxy);
                                       proxySettingsMapInfo.put("Type",EmmWifiProxy.MANUAL.getGoogleValue());
                                   } else {
                                       proxySettingsMapInfo.put("Type", EmmWifiProxy.PAC.getGoogleValue());
                                       proxySettingsMapInfo.put(EmmWifiProxy.PAC.getGoogleValue(), map.get("pacUrl"));
                                   }
                                   networkConfigurationMapInfo.put("ProxySettings", proxySettingsMapInfo);
                               }
                               networkConfigurationMapInfo.put("WiFi", wifiMapInfo);
                               networkConfigurationMaps.add(networkConfigurationMapInfo);
                           }
                           Map<String, Object> networkConfigurations = Map.of("NetworkConfigurations", networkConfigurationMaps);
                           policyInfo.setOpenNetworkConfiguration(networkConfigurations);
                       }
                    }
                    case DISABLE_MOBILE_NETWORK_CONFIG -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setMobileNetworksConfigDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_DATA_ROAMING ->{
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setDataRoamingDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_NETWORK_RESET -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setNetworkResetDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case ENABLE_NETWORK_ESCAPE_HATCH -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setNetworkEscapeHatchEnabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_VPN_CONFIG -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            policyInfo.setVpnConfigDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case PACKAGE_NAME_OF_VPN_APP -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            alwaysOnVpnPackage.setPackageName(policy.getValue());
                        }
                    }
                    case ENABLE_LOCKDOWN -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            alwaysOnVpnPackage.setLockdownEnabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case TETHERING_SETTINGS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            deviceConnectivityManagement.setTetheringSettings(EmmTetheringSettings.parse(policy.getValue()).toString());
                        }
                    }
                    case CAMERA_ACCESS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setCameraAccess(EmmCameraAccess.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_SCREEN_CAPTURE -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setScreenCaptureDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_LOCATION_SHARING -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setShareLocationDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case LOCATION_DETECTION -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setLocationMode(EmmLocationMode.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_VOLUME_ADJUSTMENT -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setAdjustVolumeDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case MICROPHONE_ACCESS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setMicrophoneAccess(EmmMicrophoneAccess.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_MOUNTING_PHYSICAL_EXTERNAL_MEDIA -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setMountPhysicalMediaDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_PRINTING -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setPrintingPolicy(PrintingPolicy.parse(policy.getValue()).toString());
                        }
                    }
                    case USB_DATA_TRANSFER -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            deviceConnectivityManagement.setUsbDataAccess(EmmUsbDataAccess.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_NOTIFICATIONS_ON_LOCK_SCREENS -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.NOTIFICATIONS);
                        }
                    }
                    case DISABLE_UNREDACTED_NOTIFICATIONS_ON_LOCK_SCREENS -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.UNREDACTED_NOTIFICATIONS);
                        }
                    }
                    case IGNORE_TRUST_AGENT_STATE_ON_LOCK_SCREENS -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.TRUST_AGENTS);
                        }
                    }
                    case DISABLE_FINGERPRINT_SENSOR_ON_LOCK_SCREENS -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.DISABLE_FINGERPRINT);
                        }
                    }
                    case DISABLE_FACE_AUTHENTICATION -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.FACE);
                        }
                    }
                    case DISABLE_ALL_BIOMETRIC_AUTHENTICATION -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.BIOMETRICS);
                        }
                    }
                    case DISABLE_ALL_SHORTCUTS_ON_LOCK_SCREEN -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.SHORTCUTS);
                        }
                    }
                    case DISABLE_ALL_KEYGUARD_FEATURES -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            keyguardDisabledFeatures.add(EmmConstants.ALL_FEATURES);
                        }
                    }
                    case ENABLE_MULTI_APP_KIOSK_MODE -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            policyInfo.setKioskCustomLauncherEnabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case POWER_BUTTON_ACTIONS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            kioskCustomization.setPowerButtonActions(EmmPowerButtonActions.parse(policy.getValue()).toString());
                        }
                    }
                    case SYSTEM_ERROR_WARNINGS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            kioskCustomization.setSystemErrorWarnings(EmmSystemErrorWarnings.parse(policy.getValue()).toString());
                        }
                    }
                    case SYSTEM_NAVIGATION -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            kioskCustomization.setSystemNavigation(EmmSystemNavigation.parse(policy.getValue()).toString());
                        }
                    }
                    case STATUS_BAR -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            kioskCustomization.setStatusBar(EmmStatusBar.parse(policy.getValue()).toString());
                        }
                    }
                    case DEVICE_SETTINGS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            kioskCustomization.setDeviceSettings(EmmDeviceSettings.parse(policy.getValue()).toString());
                        }
                    }
                    case ENABLE_REMOTE_CONTROL -> {
                        if (BooleanUtils.toBoolean(policy.getValue())) {
                            buildAirViewerPolicy(policyInfo, applications);
                        }
                    }
                    case ENABLE_AUTO_DATE_AND_TIME_ZONE -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setAutoDateAndTimeZone(EmmAutoDateAndTimeZone.parse(policy.getValue()).toString());
                        }
                    }
                    case OWNER_INFO -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            PolicyInfo.UserFacingMessage deviceOwnerLockScreenInfo = new PolicyInfo.UserFacingMessage();
                            deviceOwnerLockScreenInfo.setLocalizedMessages(Map.of("en-US", policy.getValue()));
                            deviceOwnerLockScreenInfo.setDefaultMessage(policy.getValue());
                            policyInfo.setDeviceOwnerLockScreenInfo(deviceOwnerLockScreenInfo);
                        }
                    }
                    case BATTERY_PLUGGED_MODE -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            List<String> strings = StringUtils.splitToList(policy.getValue(), SystemConstants.COMMAS);
                            List<String> stayOnPluggedModes = strings.stream().map(str -> EmmBatteryPluggedMode.parse(str).toString()).toList();
                            policyInfo.setStayOnPluggedModes(stayOnPluggedModes);
                            policyInfo.setMaximumTimeToLock(null);
                        }
                    }
                    case MAXIMUM_TIME_TO_LOCK -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setMaximumTimeToLock(EmmMaximumTimeToLock.parse(policy.getValue()).getGoogleValue());
                        }
                    }
                    case INPUT_METHODS_PACKAGE_NAME -> {
                        PolicyInfo.PackageNameList packageNameList = new PolicyInfo.PackageNameList();
                        if (StringUtils.isBlank(policy.getValue())) {
                            policyInfo.setPermittedInputMethods(packageNameList);
                        }
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            List<String> packageNames = StringUtils.splitToList(policy.getValue(), SystemConstants.COMMAS);
                            packageNameList.setPackageNames(packageNames);
                            policyInfo.setPermittedInputMethods(packageNameList);
                        }
                    }
                    case ACCESSIBILITY_SERVICES_PACKAGE_NAME -> {
                        PolicyInfo.PackageNameList packageNameList = new PolicyInfo.PackageNameList();
                        if (StringUtils.isBlank(policy.getValue())) {
                            policyInfo.setPermittedAccessibilityServices(packageNameList);
                        }
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            List<String> packageNames = StringUtils.splitToList(policy.getValue(), SystemConstants.COMMAS);
                            packageNameList.setPackageNames(packageNames);
                            policyInfo.setPermittedAccessibilityServices(packageNameList);
                        }
                    }
                    case DISABLE_ANDROID_EASTER_EGG_GAME -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setFunDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case GOOGLE_PLAY_PROTECT_VERIFICATION -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            advancedSecurityOverrides.setGooglePlayProtectVerifyApps(EmmGooglePlayProtectVerifyApps.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_WALLPAPER_CHANGING -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setSetWallpaperDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case SYSTEM_UPDATE_TYPE -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            systemUpdate.setType(EmmSystemUpdateType.parse(policy.getValue()).toString());
                        }
                    }
                    case SYSTEM_UPDATE_WINDOW -> {
                        if (StringUtils.isNotBlank(policy.getJsonValue())) {
                            Map map = jsonConvertObject(policy.getJsonValue(), Map.class);
                            LocalTime startTime = LocalTime.parse((String) map.get("startTime"));
                            LocalTime endTime = LocalTime.parse((String) map.get("endTime"));
                            int startMinutes = startTime.getHour() * 60 + startTime.getMinute();
                            int endMinutes = endTime.getHour() * 60 + endTime.getMinute();
                            systemUpdate.setStartMinutes(startMinutes);
                            systemUpdate.setEndMinutes(endMinutes);
                        }
                    }
                    case SYSTEM_UPDATE_FREEZE_PERIOD -> {
                        if (StringUtils.isNotBlank(policy.getJsonValue())) {
                            ObjectMapper objectMapper = new ObjectMapper();
                            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, Map.class);
                            List<Map<String, String>> maps = JsonMapper.fromJsonString(policy.getJsonValue(), javaType);
                            List<PolicyInfo.FreezePeriod> freezePeriodsList = Lists.newArrayListWithCapacity(maps.size());
                            for (Map<String, String> map : maps) {
                                Date startDate = DateUtils.parseDate(map.get("startDate"), "MM-dd");
                                Date endDate = DateUtils.parseDate(map.get("endDate"), "MM-dd");
                                PolicyInfo.CustomDate start = new PolicyInfo.CustomDate();
                                start.setMonth(DateUtils.getMonth(startDate));
                                start.setDay(DateUtils.getDay(startDate));
                                PolicyInfo.CustomDate end = new PolicyInfo.CustomDate();
                                end.setMonth(DateUtils.getMonth(endDate));
                                end.setDay(DateUtils.getDay(endDate));

                                PolicyInfo.FreezePeriod freezePeriod = new PolicyInfo.FreezePeriod();
                                freezePeriod.setStartDate(start);
                                freezePeriod.setEndDate(end);
                                freezePeriodsList.add(freezePeriod);
                            }
                            systemUpdate.setFreezePeriods(freezePeriodsList);

                        }
                    }
                    case PASSWORD_QUALITY_REQUIREMENT -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordQuality(EmmPasswordQuality.parse(policy.getValue()).toString());
                        }
                    }
                    case PASSWORD_MINIMUM_LENGTH -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordMinimumLength(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case MAXIMUM_PASSWORD_HISTORY_LENGTH -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordHistoryLength(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case PASSWORD_EXPIRATION_TIMEOUT -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordExpirationTimeout(policy.getValue() + "s");
                        }
                    }
                    case ALLOWED_IDLE_TIME_FOR_STRONGER_AUTHENTICATION -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setRequirePasswordUnlock(EmmRequirePasswordUnlock.parse(policy.getValue()).getGoogleValue());
                        }

                    }
                    case MAXIMUM_FAILED_PASSWORD_ATTEMPTS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setMaximumFailedPasswordsForWipe(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case PASSWORD_MINIMUM_NUMBERS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordMinimumNumeric(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case PASSWORD_MINIMUM_LETTERS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordMinimumLetters(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case MINIMUM_NUMBER_OF_LOWER_CASE_LETTERS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordMinimumLowerCase(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case MINIMUM_NUMBER_OF_UPPER_CASE_LETTERS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordMinimumUpperCase(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case MINIMUM_NUMBER_OF_SYMBOLS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            passwordRequirements.setPasswordMinimumSymbols(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case DISABLE_FACTORY_RESET -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setFactoryResetDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }

                    case DEVELOPER_SETTINGS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            advancedSecurityOverrides.setDeveloperSettings(EmmDeveloperSettings.parse(policy.getValue()).toString());
                        }
                    }
                    case UNTRUSTED_APPS_POLICY -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            advancedSecurityOverrides.setUntrustedAppsPolicy(EmmUntrustedAppsPolicy.parse(policy.getValue()).toString());
                        }
                    }
                    case DISABLE_USER_INSTALLATION_OF_APPS -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setInstallAppsDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case DISABLE_USER_UNINSTALLATION_OF_APPS ->{
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setUninstallAppsDisabled(BooleanUtils.toBoolean(policy.getValue()));
                        }
                    }
                    case CONFIGURE_RUNTIME_APP_PERMISSIONS -> {
                        if (StringUtils.isNotBlank(policy.getJsonValue())) {
                            ObjectMapper objectMapper = new ObjectMapper();
                            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, PolicyInfo.PermissionGrant.class);
                            List<PolicyInfo.PermissionGrant> permissionGrants = JsonMapper.fromJsonString(policy.getJsonValue(), javaType);
                            permissionGrants.forEach(permissionGrant -> permissionGrant.setPolicy(EmmPermissionPolicy.parse(permissionGrant.getPolicy()).toString()));
                            policyInfo.setPermissionGrants(permissionGrants);
                        }
                    }
                    case DEVICE_ENCRYPTION_POLICY -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setEncryptionPolicy(EmmEncryptionPolicy.parse(policy.getValue()).toString());
                        }
                    }
                    case MINIMUM_API_LEVEL -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setMinimumApiLevel(Integer.valueOf(policy.getValue()));
                        }
                    }
                    case POLICY_ENFORCEMENT_RULES -> {
                        if (StringUtils.isNotBlank(policy.getJsonValue())) {

                            ObjectMapper objectMapper = new ObjectMapper();
                            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, Map.class);
                            List<Map<String, Object>> maps = JsonMapper.fromJsonString(policy.getJsonValue(), javaType);
                            List<PolicyInfo.PolicyEnforcementRule> policyEnforcementRules = Lists.newArrayListWithExpectedSize(maps.size());

                            for (Map<String, Object> map : maps) {
                                PolicyInfo.PolicyEnforcementRule policyEnforcementRule = new PolicyInfo.PolicyEnforcementRule();
                                PolicyInfo.BlockAction blockAction = new PolicyInfo.BlockAction();
                                PolicyInfo.WipeAction wipeAction = new PolicyInfo.WipeAction();
                                policyEnforcementRule.setSettingName(EmmPolicySettingName.parse((String) map.get("policySettingName")).getGoogleValue());
                                blockAction.setBlockAfterDays((Integer) map.get("blockAfterDays"));
                                policyEnforcementRule.setBlockAction(blockAction);
                                wipeAction.setWipeAfterDays((Integer) map.get("wipeAfterDays"));
                                wipeAction.setPreserveFrp((Boolean) map.get("preserveFrp"));
                                policyEnforcementRule.setWipeAction(wipeAction);
                                policyEnforcementRules.add(policyEnforcementRule);
                            }
                            policyInfo.setPolicyEnforcementRules(policyEnforcementRules);
                        }
                    }
                    case APP_AUTO_UPDATE_POLICY -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setAppAutoUpdatePolicy(EmmAppAutoUpdatePolicy.parse(policy.getValue()).toString());
                        }
                    }
                    case PLAY_STORE_MODE -> {
                        if (StringUtils.isNotBlank(policy.getValue())) {
                            policyInfo.setPlayStoreMode(EmmPlayStoreMode.parse(policy.getValue()).toString());
                        }
                    }
                    default -> {}
                }
            }
        }
        for (EmmAppPolicyInfo emmAppPolicy : emmAppPolicyList) {

            PolicyInfo.ApplicationPolicy applicationPolicy = new PolicyInfo.ApplicationPolicy();
            applicationPolicy.setPackageName(emmAppPolicy.getPackageName());
            applicationPolicy.setInstallType(EmmAppInstallType.parse(emmAppPolicy.getInstallType()).toString());
            applicationPolicy.setAutoUpdateMode(EmmAppAutoUpdateMode.parse(emmAppPolicy.getAutoUpdateMode()).toString());
            applicationPolicy.setDefaultPermissionPolicy(EmmPermissionPolicy.parse(emmAppPolicy.getDefaultPermissionPolicy()).toString());

            if (StringUtils.isNotBlank(emmAppPolicy.getMcmId())) {
                PolicyInfo.ManagedConfigurationTemplate managedConfigurationTemplate = new PolicyInfo.ManagedConfigurationTemplate();
                managedConfigurationTemplate.setTemplateId(emmAppPolicy.getMcmId());
                applicationPolicy.setManagedConfigurationTemplate(managedConfigurationTemplate);
            }
            if (EmmAppInstallType.parse(emmAppPolicy.getInstallType()) == EmmAppInstallType.FORCE_INSTALLED
                    || EmmAppInstallType.parse(emmAppPolicy.getInstallType()) == EmmAppInstallType.PREINSTALLED) {
                applicationPolicy.setInstallPriority(EmmAppInstallPriority.parse(emmAppPolicy.getInstallPriority()).getGoogleValue());
            }
            if (StringUtils.isNotBlank(emmAppPolicy.getGoogleTrackId())) {
                applicationPolicy.setAccessibleTrackIds(List.of(emmAppPolicy.getGoogleTrackId()));
            }

            if (BooleanUtils.isTrue(emmAppPolicy.getPermissionGrants()) && StringUtils.isNotBlank(emmAppPolicy.getPermissionSetting())) {
                ObjectMapper objectMapper = new ObjectMapper();
                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, PolicyInfo.PermissionGrant.class);
                List<PolicyInfo.PermissionGrant> permissionGrants = JsonMapper.fromJsonString(emmAppPolicy.getPermissionSetting(), javaType);
                permissionGrants.forEach(permissionGrant -> permissionGrant.setPolicy(EmmPermissionPolicy.parse(permissionGrant.getPolicy()).toString()));
                applicationPolicy.setPermissionGrants(permissionGrants);
            }
            applications.add(applicationPolicy);
        }
        if (StringUtils.isNotBlank(deviceRadioState.getAirplaneModeState())
                || StringUtils.isNotBlank(deviceRadioState.getCellularTwoGState())
                || StringUtils.isNotBlank(deviceRadioState.getMinimumWifiSecurityLevel())
                || StringUtils.isNotBlank(deviceRadioState.getUltraWidebandState())
                || StringUtils.isNotBlank(deviceRadioState.getWifiState())) {
            policyInfo.setDeviceRadioState(deviceRadioState);
        }
        if (StringUtils.isNotBlank(deviceConnectivityManagement.getConfigureWifi())
                || StringUtils.isNotBlank(deviceConnectivityManagement.getTetheringSettings())
                || StringUtils.isNotBlank(deviceConnectivityManagement.getUsbDataAccess())
                || StringUtils.isNotBlank(deviceConnectivityManagement.getWifiDirectSettings())) {
            policyInfo.setDeviceConnectivityManagement(deviceConnectivityManagement);
        }
        if (StringUtils.isNotBlank(alwaysOnVpnPackage.getPackageName())
                || Objects.nonNull(alwaysOnVpnPackage.getLockdownEnabled())) {
            policyInfo.setAlwaysOnVpnPackage(alwaysOnVpnPackage);
        }
        if (StringUtils.isNotBlank(advancedSecurityOverrides.getDeveloperSettings())
                || StringUtils.isNotBlank(advancedSecurityOverrides.getGooglePlayProtectVerifyApps())
                || StringUtils.isNotBlank(advancedSecurityOverrides.getUntrustedAppsPolicy())) {
            policyInfo.setAdvancedSecurityOverrides(advancedSecurityOverrides);
        }
        if (Boolean.TRUE.equals(policyInfo.getKioskCustomLauncherEnabled())) {
            if (StringUtils.isBlank(kioskCustomization.getSystemNavigation())) {
                kioskCustomization.setSystemNavigation(EmmSystemNavigation.NAVIGATION_DISABLED.toString());
            }
            if (StringUtils.isBlank(kioskCustomization.getStatusBar())) {
                kioskCustomization.setStatusBar(EmmStatusBar.NOTIFICATIONS_AND_SYSTEM_INFO_DISABLED.toString());
            }
        }
        if (StringUtils.isNotBlank(kioskCustomization.getPowerButtonActions())
                || StringUtils.isNotBlank(kioskCustomization.getSystemErrorWarnings())
                || StringUtils.isNotBlank(kioskCustomization.getSystemNavigation())
                || StringUtils.isNotBlank(kioskCustomization.getStatusBar())
                || StringUtils.isNotBlank(kioskCustomization.getDeviceSettings())) {
            policyInfo.setKioskCustomization(kioskCustomization);
        }
        if (Objects.nonNull(systemUpdate.getStartMinutes())
                || Objects.nonNull(systemUpdate.getEndMinutes())
                || StringUtils.isNotBlank(systemUpdate.getType())
                || CollectionUtils.isNotEmpty(systemUpdate.getFreezePeriods())) {
            policyInfo.setSystemUpdate(systemUpdate);
        }
        if (Objects.nonNull(passwordRequirements.getMaximumFailedPasswordsForWipe())
                || StringUtils.isNotBlank(passwordRequirements.getPasswordExpirationTimeout())
                || Objects.nonNull(passwordRequirements.getPasswordHistoryLength())
                || Objects.nonNull(passwordRequirements.getPasswordMinimumLength())
                || Objects.nonNull(passwordRequirements.getPasswordMinimumLetters())
                || Objects.nonNull(passwordRequirements.getPasswordMinimumLowerCase())
                || Objects.nonNull(passwordRequirements.getPasswordMinimumNumeric())
                || Objects.nonNull(passwordRequirements.getPasswordMinimumSymbols())
                || Objects.nonNull(passwordRequirements.getPasswordMinimumUpperCase())
                || StringUtils.isNotBlank(passwordRequirements.getPasswordQuality())
                || StringUtils.isNotBlank(passwordRequirements.getRequirePasswordUnlock())) {
            policyInfo.setPasswordPolicies(List.of(passwordRequirements));
        }
        if (CollectionUtils.isNotEmpty(keyguardDisabledFeatures)) {
            policyInfo.setKeyguardDisabledFeatures(keyguardDisabledFeatures);
        }
        return policyInfo;

    }

    private static void buildBasePolicy(PolicyInfo info, List<PolicyInfo.ApplicationPolicy> applications) {
        PolicyInfo.StatusReportingSettings defaultStatusReportingSettings = new PolicyInfo.StatusReportingSettings();
        defaultStatusReportingSettings.setSoftwareInfoEnabled(Boolean.TRUE);
        defaultStatusReportingSettings.setDeviceSettingsEnabled(Boolean.TRUE);
        defaultStatusReportingSettings.setDisplayInfoEnabled(Boolean.TRUE);
        defaultStatusReportingSettings.setNetworkInfoEnabled(Boolean.TRUE);
        defaultStatusReportingSettings.setMemoryInfoEnabled(Boolean.TRUE);
        info.setStatusReportingSettings(defaultStatusReportingSettings);

        PolicyInfo.ApplicationPolicy authClientPolicy = new PolicyInfo.ApplicationPolicy();
        String clientPackageName = SystemPropertyHelper.getEmmClientPackageName();
        authClientPolicy.setPackageName(clientPackageName);
        authClientPolicy.setInstallType(EmmAppInstallType.REQUIRED_FOR_SETUP.name());
        authClientPolicy.setUserControlSettings("USER_CONTROL_DISALLOWED");
        authClientPolicy.setPermissionGrants(buildPermissionGrants());
        applications.add(authClientPolicy);

        PolicyInfo.LaunchAppAction launchAppAction = new PolicyInfo.LaunchAppAction();
        launchAppAction.setPackageName(clientPackageName);
        PolicyInfo.SetupAction setupAction = new PolicyInfo.SetupAction();
        setupAction.setLaunchApp(launchAppAction);
        info.setSetupActions(List.of(setupAction));

        info.setApplications(applications);
    }

    private static void buildAirViewerPolicy(PolicyInfo info, List<PolicyInfo.ApplicationPolicy> applications) {
        PolicyInfo.ApplicationPolicy airViewerClientPolicy = new PolicyInfo.ApplicationPolicy();
        String airViewerPackageName = SystemPropertyHelper.getEmmAirViewerPackageName();
        airViewerClientPolicy.setPackageName(airViewerPackageName);
        airViewerClientPolicy.setInstallType(EmmAppInstallType.FORCE_INSTALLED.name());
        airViewerClientPolicy.setUserControlSettings("USER_CONTROL_DISALLOWED");
        StoreDeployInfoConfigProps configProps = SpringContextHolder.getBean(StoreDeployInfoConfigProps.class);
        if (StringUtils.equals(configProps.getEnvCode(), Profiles.SIT)
            || StringUtils.equals(configProps.getEnvCode(), Profiles.DOCKER)
            || StringUtils.equals(configProps.getEnvCode(), Profiles.DEVELOPMENT)) {
            airViewerClientPolicy.setAccessibleTrackIds(List.of(EMM_AIRVIEWER_TRACK_ID));
        }
        applications.add(airViewerClientPolicy);

        List<String> airViewerPluginPackageNameList = StringUtils.splitToList(SystemPropertyHelper.getEmmAirViewerPluginPackageName(), SystemConstants.COMMAS);
        for (String packageName : airViewerPluginPackageNameList) {
            PolicyInfo.ApplicationPolicy airViewerPluginPackageNamePolicy = new PolicyInfo.ApplicationPolicy();
            airViewerPluginPackageNamePolicy.setPackageName(packageName);
            airViewerPluginPackageNamePolicy.setInstallType(EmmAppInstallType.FORCE_INSTALLED.name());
            airViewerPluginPackageNamePolicy.setUserControlSettings("USER_CONTROL_DISALLOWED");
            applications.add(airViewerPluginPackageNamePolicy);
        }
        info.setApplications(applications);
    }

    private static List<PolicyInfo.PermissionGrant> buildPermissionGrants() {
        List<PolicyInfo.PermissionGrant> permissionGrants = new ArrayList<>(3);
        permissionGrants.add(generateGrantPolicy("android.permission.ACCESS_FINE_LOCATION"));
        permissionGrants.add(generateGrantPolicy("android.permission.ACCESS_BACKGROUND_LOCATION"));
        permissionGrants.add(generateGrantPolicy("android.permission.READ_PHONE_STATE"));
        return permissionGrants;
    }

    private static PolicyInfo.PermissionGrant generateGrantPolicy(String permission) {
        PolicyInfo.PermissionGrant permissionGrant = new PolicyInfo.PermissionGrant();
        permissionGrant.setPermission(permission);
        permissionGrant.setPolicy(EmmPermissionPolicy.GRANT.name());
        return permissionGrant;
    }

    public static <T> T jsonConvertObject(String json, Class<T> clazz) throws IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception exception) {
            throw new IllegalArgumentException("json convert object fail", exception);
        }
    }
}
