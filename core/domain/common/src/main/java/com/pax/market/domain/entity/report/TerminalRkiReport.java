package com.pax.market.domain.entity.report;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

@Getter
@Setter
public class TerminalRkiReport extends TerminalReport {

    @Serial
    private static final long serialVersionUID = 1L;

    private String serialNo;
    private String resellerName;
    private String merchantName;
    private String modelName;
    private String keyTemplate;
    private Date taskActivationTime;
    private String taskStatus;
    private Date lastUpdatedTime;
    private String operator;
    private int actionType;
    private Long referenceId;
    private int actionStatus;
    private String failedReason;
    private int errorCode;
    private String remarks;
}
