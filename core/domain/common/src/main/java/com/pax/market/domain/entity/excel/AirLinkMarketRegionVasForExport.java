package com.pax.market.domain.entity.excel;

import com.pax.market.framework.common.excel.annotation.ExcelField;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 9.7
 */
@Setter
public class AirLinkMarketRegionVasForExport extends MarketRegionVasForExport {


    @Serial
    private static final long serialVersionUID = 2248257593603621261L;
    private String balance;
    private String overdueDate;


    @ExcelField(title = "title.marketplace.name", align = 1, sort = 1)
    public String getName() {
        return super.getName();
    }

    @ExcelField(title = "title.domain", align = 1, sort = 2)
    public String getDomain() {
        return super.getDomain();
    }

    @ExcelField(title = "title.region", align = 1, sort = 3)
    public String getRegion() {
        return super.getRegion();
    }

    @ExcelField(title = "title.country", align = 1, sort = 4)
    public String getCountry() {
        return super.getCountry();
    }

    @ExcelField(title = "title.agent", align = 1, sort = 5)
    public String getAgent() {
        return super.getAgent();
    }

    @ExcelField(title = "title.subscription", align = 1, sort = 6, objectConvertorBeanName = "vasServiceStatusType")
    public String getSubscribedStatus() {
        return super.getSubscribedStatus();
    }

    @ExcelField(title = "title.charge.date", align = 1, sort = 7)
    public String getChargeDate() {
        return super.getChargeDate();
    }

    @ExcelField(title = "title.balance", align = 1, sort = 8, objectConvertorBeanName = "bigDecimalFormatType")
    public String getBalance() {
        return balance;
    }

    @ExcelField(title = "title.overdue.date", align = 1, sort = 9)
    public String getOverdueDate() {
        return overdueDate;
    }

    @ExcelField(title = "title.service.status", align = 1, sort = 10, objectConvertorBeanName = "marketServiceStatusType")
    public String getServiceStatus() {
        return super.getServiceStatus();
    }

}
