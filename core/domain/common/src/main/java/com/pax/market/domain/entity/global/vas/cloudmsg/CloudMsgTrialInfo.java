package com.pax.market.domain.entity.global.vas.cloudmsg;

import com.pax.market.framework.common.persistence.DataEntity;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.LongUtils;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;


/**
 * @author: <PERSON> @since 8.7
 */
@Getter
@Setter
public class CloudMsgTrialInfo extends DataEntity<CloudMsgTrialInfo> {

    @Serial
    private static final long serialVersionUID = 7649988749484132819L;
    private boolean updateTrial;
    private Long trialMsgCount;
    private Long sumOfArrivedNum;
    private Long freeNumIgnored;
    private Integer alertTimes;
    private Date endTrialTime;
    private Date lastCancelChargeTime;

    public Integer getAlertTimes() {
        if (IntegerUtils.isNotBlankAndPositive(alertTimes)) {
            return alertTimes;
        }
        return 0;
    }

    public Long getTrialMsgCount() {
        if (LongUtils.isNotBlankAndPositive(trialMsgCount)) {
            return trialMsgCount;
        }
        return 0L;
    }

    public Long getSumOfArrivedNum() {
        if (LongUtils.isNotBlankAndPositive(sumOfArrivedNum)) {
            return sumOfArrivedNum;
        }
        return 0L;
    }
}
