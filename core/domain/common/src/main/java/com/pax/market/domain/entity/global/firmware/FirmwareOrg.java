package com.pax.market.domain.entity.global.firmware;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.framework.common.persistence.DataEntity;

import java.util.Set;


/**
 * <AUTHOR>
 */
public class FirmwareOrg extends DataEntity<FirmwareOrg> {

    private static final long serialVersionUID = 1976040459829503260L;

    private Firmware firmware;

    private Reseller reseller;

    private Boolean hasCurrentReseller;

    private Set<Long> resellerIdsFilter;

    public Firmware getFirmware() {
        return firmware;
    }

    public void setFirmware(Firmware firmware) {
        this.firmware = firmware;
    }


    public Reseller getReseller() {
        return reseller;
    }

    public void setReseller(Reseller reseller) {
        this.reseller = reseller;
    }

    public Boolean getHasCurrentReseller() {
        return hasCurrentReseller;
    }

    public void setHasCurrentReseller(Boolean hasCurrentReseller) {
        this.hasCurrentReseller = hasCurrentReseller;
    }


    public Set<Long> getResellerIdsFilter() {
        return resellerIdsFilter;
    }

    public void setResellerIdsFilter(Set<Long> resellerIdsFilter) {
        this.resellerIdsFilter = resellerIdsFilter;
    }
}
