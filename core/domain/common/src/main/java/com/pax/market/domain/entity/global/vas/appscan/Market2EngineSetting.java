package com.pax.market.domain.entity.global.vas.appscan;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/1/19 13:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Market2EngineSetting extends DataEntity<Market2EngineSetting> {
    private static final long serialVersionUID = 5923569896601579771L;
    private Long marketId;
    private Long engineId;
    private String scanMode;
    private boolean enabled;
}
