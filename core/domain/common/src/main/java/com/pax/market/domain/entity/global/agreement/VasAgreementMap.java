/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.domain.entity.global.agreement;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/8/23
 */
@Getter
@Setter
public class VasAgreementMap extends DataEntity<VasAgreementMap> {

    private static final long serialVersionUID = 4931251884611358350L;

    private Long agreementId;
    private Long referenceId;

}
