/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.domain.entity.market.attribute;

import com.pax.market.constants.EntityAttributeConstants;
import com.pax.market.constants.SystemConstants;
import com.pax.market.framework.common.audit.AuditAware;
import com.pax.market.framework.common.persistence.DataEntity;
import com.pax.market.framework.common.persistence.idgen.RedisIdGeneratorAware;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;

import java.util.List;

/**
 * The type Entity attribute.
 */
public class EntityAttribute extends DataEntity<EntityAttribute> implements AuditAware, RedisIdGeneratorAware {

    private static final long serialVersionUID = 1L;

    private String entityType; //属性属于那个Entity，比如Merchant，Reseller，App
    private String inputType = EntityAttributeConstants.INPUT_TYPE_TEXT; //属性的输入类型，比如数字，日期，字符串，文件等
    private Integer minLength;
    private Integer maxLength = SystemConstants.ENTITY_ATTRIBUTE_VALUE_MAX_LENGTH;
    private boolean required = false;
    private String selector;
    private int index;
    private String key;

    private List<EntityAttributeLabel> entityAttributeLabelList;

    /**
     * Instantiates a new Entity attribute.
     */
    public EntityAttribute() {
        super();
    }

    /**
     * Instantiates a new Entity attribute.
     *
     * @param id the id
     */
    public EntityAttribute(Long id) {
        super(id);
    }

    /**
     * Gets entity type.
     *
     * @return the entity type
     */
    public String getEntityType() {
        return entityType;
    }

    /**
     * Sets entity type.
     *
     * @param entityType the entity type
     */
    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    /**
     * Gets input type.
     *
     * @return the input type
     */
    public String getInputType() {
        return inputType;
    }

    /**
     * Sets input type.
     *
     * @param inputType the input type
     */
    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    /**
     * Gets min length.
     *
     * @return the min length
     */
    public Integer getMinLength() {
        return minLength;
    }

    /**
     * Sets min length.
     *
     * @param minLength the min length
     */
    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }

    /**
     * Gets max length.
     *
     * @return the max length
     */
    public Integer getMaxLength() {
        return maxLength;
    }

    /**
     * Sets max length.
     *
     * @param maxLength the max length
     */
    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    /**
     * Is required boolean.
     *
     * @return the boolean
     */
    public boolean isRequired() {
        return required;
    }

    /**
     * Sets required.
     *
     * @param required the required
     */
    public void setRequired(boolean required) {
        this.required = required;
    }

    /**
     * Gets select.
     *
     * @return the select
     */
    public String getSelector() {
        return selector;
    }

    /**
     * Sets select.
     *
     * @param selector the select
     */
    public void setSelector(String selector) {
        this.selector = selector;
    }

    /**
     * Gets index.
     *
     * @return the index
     */
    public int getIndex() {
        return index;
    }

    /**
     * Sets index.
     *
     * @param index the index
     */
    public void setIndex(int index) {
        this.index = index;
    }

    /**
     * Gets key.
     *
     * @return the key
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets key.
     *
     * @param key the key
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * Gets entity attribute label list.
     *
     * @return the entity attribute label list
     */
    public List<EntityAttributeLabel> getEntityAttributeLabelList() {
        return entityAttributeLabelList;
    }

    /**
     * Sets entity attribute label list.
     *
     * @param entityAttributeLabelList the entity attribute label list
     */
    public void setEntityAttributeLabelList(List<EntityAttributeLabel> entityAttributeLabelList) {
        this.entityAttributeLabelList = entityAttributeLabelList;
    }

    /**
     * Gets default label.
     *
     * @return the default label
     */
    public String getDefaultLabel() {
        if (getEntityAttributeLabelList() != null && !getEntityAttributeLabelList().isEmpty()) {
            for (EntityAttributeLabel entityAttributeLabel : getEntityAttributeLabelList()) {
                if (StringUtils.equals(entityAttributeLabel.getLocale(), RequestLocaleHolder.getLocale())) {
                    return entityAttributeLabel.getLabel();
                }
            }

            for (EntityAttributeLabel entityAttributeLabel : getEntityAttributeLabelList()) {
                if (StringUtils.equals(entityAttributeLabel.getLocale(), SystemConstants.DEFAULT_LOCALE)) {
                    return entityAttributeLabel.getLabel();
                }
            }

            return getEntityAttributeLabelList().get(0).getLabel();
        } else {
            return null;
        }
    }

    /**
     * Gets default label with key.
     *
     * @return the default label with key
     */
    public String getDefaultLabelWithKey() {
        String defaultLabel = getDefaultLabel();
        if (StringUtils.isNotEmpty(defaultLabel)) {
            return String.format("%s(%s)", defaultLabel, getKey());
        } else {
            return getKey();
        }
    }

    @Override
    public int getAuditActionType() {
        return 39;
    }

    @Override
    public String getTableName() {
        return "PAX_ENTITY_ATTRIBUTE";
    }
}