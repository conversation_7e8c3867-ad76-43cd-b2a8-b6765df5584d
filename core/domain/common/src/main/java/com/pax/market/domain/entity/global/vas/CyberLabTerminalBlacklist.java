package com.pax.market.domain.entity.global.vas;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 9.2
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = false)
public class CyberLabTerminalBlacklist extends DataEntity<CyberLabTerminalBlacklist> {

    @Serial
    private static final long serialVersionUID = 8271470847053675059L;
    private String serialNo;

}
