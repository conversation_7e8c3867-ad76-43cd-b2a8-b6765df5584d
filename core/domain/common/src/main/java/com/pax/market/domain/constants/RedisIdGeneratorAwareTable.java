package com.pax.market.domain.constants;

public enum RedisIdGeneratorAwareTable {
    TERMINAL_ACCESSORY_PROFILE("PAX_TERMINAL_ACCESSORY_PROFILE"),
    ACTIVITY("PAX_ACTIVITY"),
    ALARM("PAX_ALARM"),

    APK_PARAMETER("PAX_APK_PARAMETER"),
    DEVICE_INFO("PAX_DEVICE_INFO"),
    DOWNLOAD_TASK("PAX_DOWNLOAD_TASK"),
    ENTITY_ATTRIBUTE("PAX_ENTITY_ATTRIBUTE"),
    ENTITY_ATTRIBUTE_VALUE("PAX_ENTITY_ATTRIBUTE_VALUE"),

    LAUNCHER_TEMPLATE("PAX_LAUNCHER_TEMPLATE"),

    VARIABLE("PAX_VARIABLE"),

    MERCHANT("PAX_MERCHANT"),
    MERCHANT_MIGRATE_HISTORY("PAX_MERCHANT_MIGRATE_HISTORY"),
    MERCHANT_VARIABLE("PAX_MERCHANT_VARIABLE"),
    PROFILE("PAX_PROFILE"),
    RESELLER_APK_SIGNATURE("PAX_RESELLER_APK_SIGNATURE"),
    RESELLER_CERTIFICATE("PAX_RESELLER_CERTIFICATE"),
    RESELLER("PAX_RESELLER"),
    RESELLER_INSTALLED_APK("PAX_RESELLER_INSTALLED_APK"),
    SANDBOX_TERMINAL_APK("PAX_SANDBOX_TERMINAL_APK"),
    SANDBOX_TERMINAL_APK_PARAM("PAX_SANDBOX_TERMINAL_APK_PARAM"),
    SANDBOX_TERMINAL("PAX_SANDBOX_TERMINAL"),
    //SENSITIVE_WORD("PAX_SENSITIVE_WORD"),
    TERMINAL_ACCESSORY("PAX_TERMINAL_ACCESSORY"),
    TERMINAL_ACCESSORY_DETAILS("PAX_TERMINAL_ACCESSORY_DETAILS"),
    TERMINAL_ACCESSORY_EVENTS("PAX_TERMINAL_ACCESSORY_EVENTS"),
    TERMINAL_ACCESSORY_ACTION("PAX_TERMINAL_ACCESSORY_ACTION"),
    TERMINAL_ACTION("PAX_TERMINAL_ACTION"),
    TERMINAL_APK("PAX_TERMINAL_APK"),
    TERMINAL_APK_PARAM("PAX_TERMINAL_APK_PARAM"),
    TERMINAL_CHECKUP_INFO("PAX_TERMINAL_CHECKUP_INFO"),
    TERMINAL_FM("PAX_TERMINAL_FM"),
    TM_GROUP_ACTION_COUNT("PAX_TM_GROUP_ACTION_COUNT"),
    TM_GROUP_APK("PAX_TM_GROUP_APK"),
    TM_GROUP_APK_PARAM("PAX_TM_GROUP_APK_PARAM"),
    TM_GROUP("PAX_TM_GROUP"),
    TM_GROUP_FM("PAX_TM_GROUP_FM"),
    TM_GROUP_OPT("PAX_TM_GROUP_OPT"),
    TM_GROUP_PUSH_FILTER("PAX_TM_GROUP_PUSH_FILTER"),
    TM_GROUP_PUSH_LIMIT("PAX_TM_GROUP_PUSH_LIMIT"),
    TM_GROUP_PUSH_APPROVAL("PAX_TM_GROUP_PUSH_APPROVAL"),
    TM_GROUP_RKI_KEY("PAX_TM_GROUP_RKI_KEY"),
    TM_GROUP_UNINSTALL_APK("PAX_TM_GROUP_UNINSTALL_APK"),
    TM_GROUP_VARIABLE("PAX_TM_GROUP_VARIABLE"),
    TERMINAL_LOG("PAX_TERMINAL_LOG"),
    TERMINAL_MASTER_KEY("PAX_TERMINAL_MASTER_KEY"),
    TERMINAL_OPT("PAX_TERMINAL_OPT"),
    TERMINAL_PED_INFO("PAX_TERMINAL_PED_INFO"),
    TERMINAL_PROFILE("PAX_TERMINAL_PROFILE"),
    TERMINAL_REPLACE_HISTORY("PAX_TERMINAL_REPLACE_HISTORY"),
    TERMINAL_RKI_KEY("PAX_TERMINAL_RKI_KEY"),
    TERMINAL_SKYHOOK_ACCESS_TIME("PAX_TERMINAL_SKYHOOK_ACCESS_TIME"),
    TERMINAL_TID_SETTING("PAX_TERMINAL_TID_SETTING"),
    TERMINAL_VARIABLE("PAX_TERMINAL_VARIABLE"),
    TERMINAL_REVERSAL_RECORD("PAX_RKI_REVERSAL_REQ_RECORD"),
    TERMINAL_DEDUCTION_RECORD("PAX_RKI_DEDUCTION_REQ_RECORD"),
    EMM_DEVICE_VARIABLE("PAX_EMM_DEVICE_VARIABLE"),
    TERMINAL_TO_SERVICE("PAX_TERMINAL_TO_SERVICE"),
    PAX_TERMINAL_BATTERY("PAX_TERMINAL_BATTERY"),
    PAX_TERMINAL_APK_PARAM_HISTORY("PAX_TERMINAL_APK_PARAM_HISTORY");

    private final String value;

    RedisIdGeneratorAwareTable(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
