/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain.entity.excel;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.framework.common.excel.annotation.ExcelField;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/26
 */
public class TerminalForExportWithInspectAlarm extends TerminalForExportWithAlarm {
    private static final long serialVersionUID = 6188571831156914578L;

    @ExcelField(title = "title.terminal.name", align = 2, sort = 1)
    public String getName() {
        return super.getName();
    }

    @ExcelField(title = "title.tid", align = 2, sort = 2)
    public String getTID() {
        return super.getTID();
    }

    @ExcelField(title = "title.serial.no", align = 2, sort = 4)
    public String getSerialNo() {
        return super.getSerialNo();
    }

    @ExcelField(title = "title.manufacturer", align = 2, sort = 5)
    public String getFactoryName() {
        return super.getFactoryName();
    }

    @ExcelField(title = "title.model", align = 2, sort = 6)
    public String getModelName() {
        return super.getModelName();
    }

    @ExcelField(title = "title.reseller", align = 2, sort = 7, objectConvertorBeanName = "resellerTypeForReport")
    public Reseller getReseller() {
        return super.getReseller();
    }

    @ExcelField(title = "title.merchant", align = 2, sort = 8)
    public String getMerchantName() {
        return super.getMerchantName();
    }

    @ExcelField(title = "title.location", align = 2, sort = 9)
    public String getLocation() {
        return super.getLocation();
    }

    @ExcelField(title = "title.remark", align = 2, sort = 10)
    public String getRemark() {
        return super.getRemark();
    }

    @ExcelField(title = "title.status", align = 2, sort = 11, objectConvertorBeanName = "terminalStatusType")
    public String getStatus() {
        return super.getStatus();
    }

    @ExcelField(title = "title.alert.time", align = 2, sort = 14, dateFormat = "yyyy-MM-dd HH:mm:ss")
    public Date getAlertTime() {
        return super.getAlertTime();
    }

    @ExcelField(title = "title.severity.level", align = 2, sort = 15, objectConvertorBeanName = "terminalSeverityType")
    public String getSeverity() {
        return super.getSeverity();
    }

}
