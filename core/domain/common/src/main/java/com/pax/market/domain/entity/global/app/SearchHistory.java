/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain.entity.global.app;

import com.pax.market.framework.common.persistence.DataEntity;

/**
 * 搜索记录
 *
 * <AUTHOR>
 */
public class SearchHistory extends DataEntity<SearchHistory> {
    private static final long serialVersionUID = 3851519660570120397L;
    private String keyWords;
    private Integer frequency;

    /**
     * Instantiates a new Search history.
     */
    public SearchHistory() {
        super();
    }

    /**
     * Gets frequency.
     *
     * @return the frequency
     */
    public Integer getFrequency() {
        return frequency;
    }

    /**
     * Sets frequency.
     *
     * @param frequency the frequency
     */
    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    /**
     * Gets key words.
     *
     * @return the key words
     */
    public String getKeyWords() {
        return keyWords;
    }

    /**
     * Sets key words.
     *
     * @param keyWords the key words
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
    }
}
