package com.pax.market.domain.exception;

import com.pax.core.exception.BusinessException;

public class RemoteBizException extends BusinessException {

    private Integer remoteErrorCode;
    private String remoteErrorMessage;

    public RemoteBizException(int businessCode, Integer remoteErrorCode, String remoteErrorMessage) {
        super(businessCode);
        this.remoteErrorCode = remoteErrorCode;
        this.remoteErrorMessage = remoteErrorMessage;
    }

    public RemoteBizException(int businessCode, String message) {
        super(businessCode, message);
    }

    public RemoteBizException(int businessCode, String message, String... args) {
        super(businessCode, message, args);
    }

    public Integer getRemoteErrorCode() {
        return remoteErrorCode;
    }

    public void setRemoteErrorCode(Integer remoteErrorCode) {
        this.remoteErrorCode = remoteErrorCode;
    }

    public String getRemoteErrorMessage() {
        return remoteErrorMessage;
    }

    public void setRemoteErrorMessage(String remoteErrorMessage) {
        this.remoteErrorMessage = remoteErrorMessage;
    }
}
