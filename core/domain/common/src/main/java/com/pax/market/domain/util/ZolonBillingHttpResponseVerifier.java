package com.pax.market.domain.util;

import com.pax.core.exception.BusinessException;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.ApiCodes;
import com.pax.market.dto.ZolonBillingResponse;
import com.pax.support.http.HttpResponse;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class ZolonBillingHttpResponseVerifier {

    public static void onFailure(HttpResponse response) {
        if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getRawData())) {
            ZolonBillingResponse baseResponse = JsonMapper.nonNullMapper().fromJson(response.getRawData(), ZolonBillingResponse.class);
            if (Objects.nonNull(baseResponse)) {
                throw new BusinessException(baseResponse.getBizCode(), baseResponse.getMessage());
            }
        }
        throw new BusinessException(ApiCodes.UNKNOWN);
    }

    public static void onFailureExceptionWithArgs(HttpResponse response) {
        if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getRawData())) {
            ZolonBillingResponse baseResponse = JsonMapper.nonNullMapper().fromJson(response.getRawData(), ZolonBillingResponse.class);
            if (Objects.nonNull(baseResponse)) {
                if (Objects.nonNull(baseResponse.getArgs()) && baseResponse.getArgs().length > 0) {
                    throw new BusinessException(baseResponse.getBizCode(), baseResponse.getMessage(), baseResponse.getArgs());
                }
                throw new BusinessException(baseResponse.getBizCode(), baseResponse.getMessage());
            }
        }
        throw new BusinessException(ApiCodes.UNKNOWN);
    }


    public static void onRemoteApiError(Throwable throwable) {
        throw new BusinessException(ApiCodes.REMOTE_API_CONNECT_TIMEOUT_ERROR);
    }

}
