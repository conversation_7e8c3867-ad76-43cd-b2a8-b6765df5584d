/**
 * ********************************************************************************
 * COPYRIGHT      
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION     
 *   This software is supplied under the terms of a license agreement or      
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied     
 *   or disclosed except in accordance with the terms in that agreement.
 *         
 *      Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.domain.query;

import java.util.Date;

import com.pax.market.domain.entity.global.audit.AuthLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AuthLogQueryParam
 *
 * <AUTHOR>
 * @date Mar 5, 2018
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class AuthLogQueryParam extends AuthLog {
    
    private static final long serialVersionUID = 4075421152277391060L;
    private Date startDt, endDt;
    private Boolean actionSuccess;
    private String tableSuffix;
    private Integer year;
}
