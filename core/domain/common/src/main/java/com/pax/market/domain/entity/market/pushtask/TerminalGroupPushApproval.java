package com.pax.market.domain.entity.market.pushtask;

import com.pax.market.domain.constants.RedisIdGeneratorAwareTable;
import com.pax.market.dto.UserInfo;
import com.pax.market.framework.common.persistence.DataEntity;
import com.pax.market.framework.common.persistence.idgen.RedisIdGeneratorAware;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

@Getter
@Setter
public class TerminalGroupPushApproval extends DataEntity<TerminalGroupPushApproval> implements RedisIdGeneratorAware {
    @Serial
    private static final long serialVersionUID = 2403276149496181870L;

    private Long groupId;
    private String groupName;
    private Long referenceId;
    private int actionType;
    private String type;
    private String status;
    protected UserInfo submitBy;
    private Date submitDate;
    private String rejectReason;

    public TerminalGroupPushApproval() {
        super();
    }

    public TerminalGroupPushApproval(Long groupId, Long referenceId, int actionType) {
        this.groupId = groupId;
        this.referenceId = referenceId;
        this.actionType = actionType;
    }
    @Override
    public String getTableName() {
        return RedisIdGeneratorAwareTable.TM_GROUP_PUSH_APPROVAL.getValue();
    }
}
