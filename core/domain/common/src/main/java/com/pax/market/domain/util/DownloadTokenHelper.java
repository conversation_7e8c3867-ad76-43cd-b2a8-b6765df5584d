package com.pax.market.domain.util;

import com.pax.market.constants.ApiCodes;
import com.pax.core.exception.BusinessException;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.StringUtils;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * The type Download token helper.
 */
public class DownloadTokenHelper {
    private static final String CLAIMS_FILE_ID = "fileId";
    private static final String CLAIMS_FILE_NAME = "fileName";
    private static final String CLAIMS_USE_ORIGINAL = "useOriginal";
    private static JwtTokenHelper jwtTokenHelper;

    private static JwtTokenHelper getJwtTokenHelper() {
        if (jwtTokenHelper == null) {
            String secret = SystemPropertyHelper.getDownloadTokenSecret();
            if (!StringUtils.hasText(secret)) {
                secret = JwtTokenHelper.generateSecret(SystemPropertyHelper.DOWNLOAD_TOKEN_SECRET);
            }
            jwtTokenHelper = JwtTokenHelper.secret(secret);
        }
        return jwtTokenHelper;
    }

    /**
     * Generate download token string.
     *
     * @param fileId   the file id
     * @param fileName the file name
     * @return the string
     */
    public static String generateDownloadToken(String fileId, String fileName) {
        return generateDownloadToken(fileId, fileName, false, SystemPropertyHelper.getDownloadTokenExpireSeconds());
    }

    /**
     * Generate download token string.
     *
     * @param fileId        the file id
     * @param fileName      the file name
     * @param useOriginal   the use original
     * @param expireSeconds the expire seconds
     * @return the string
     */
    public static String generateDownloadToken(String fileId, String fileName, boolean useOriginal, int expireSeconds) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIMS_FILE_ID, fileId);
        claims.put(CLAIMS_FILE_NAME, fileName);
        claims.put(CLAIMS_USE_ORIGINAL, useOriginal);

        return getJwtTokenHelper().generateToken(claims, Calendar.SECOND, expireSeconds);
    }

    /**
     * Generate notification download token string.
     *
     * @param fileId   the file id
     * @param fileName the file name
     * @return the string
     */
    public static String generateNotificationDownloadToken(String fileId, String fileName) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIMS_FILE_ID, fileId);
        claims.put(CLAIMS_FILE_NAME, fileName);

        return getJwtTokenHelper().generateToken(claims, Calendar.DAY_OF_MONTH, SystemPropertyHelper.getDownloadTaskExpireDays());
    }

    /**
     * Validate and get file name string.
     *
     * @param token the token
     * @return the string
     */
    public static String validateAndGetFileName(String token) {
        String value = getJwtTokenHelper().validateAndGetValue(token, CLAIMS_FILE_NAME);
        if (StringUtils.isEmpty(value)) {
            throw new BusinessException(ApiCodes.INVALID_ACCESS_TOKEN);
        }
        return value;
    }

    /**
     * Validate and get file id string.
     *
     * @param token the token
     * @return the string
     */
    public static String validateAndGetFileId(String token) {
        String value = getJwtTokenHelper().validateAndGetValue(token, CLAIMS_FILE_ID);
        if (StringUtils.isEmpty(value)) {
            throw new BusinessException(ApiCodes.INVALID_ACCESS_TOKEN);
        }
        return value;
    }

    /**
     * Validate and get use original boolean.
     *
     * @param token the token
     * @return the boolean
     */
    public static boolean validateAndGetUseOriginal(String token) {
        Boolean value = getJwtTokenHelper().validateAndGetValue(token, CLAIMS_USE_ORIGINAL, Boolean.class);
        return BooleanUtils.isTrue(value);
    }
}
