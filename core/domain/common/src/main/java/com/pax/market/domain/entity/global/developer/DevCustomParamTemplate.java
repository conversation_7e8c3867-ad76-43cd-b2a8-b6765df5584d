/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.domain.entity.global.developer;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;


/**
 * @Description 开发者自定义参数模板Entity
 * @Author: Shawn
 * @Date: 2019/9/12 9:40
 */
@Getter
@Setter
public class DevCustomParamTemplate extends DataEntity<DevCustomParamTemplate> {
    private static final long serialVersionUID = -7534680319485581087L;

    private Long devId;
    private Long appId;
    private String appName;
    private String name;
    private String schema;
    private String description;

    /**
     * Instantiates a new Data entity.
     */
    public DevCustomParamTemplate() {
    }

    /**
     * Instantiates a new Data entity.
     *
     * @param id the id
     */
    public DevCustomParamTemplate(Long id) {
        super(id);
    }
}
