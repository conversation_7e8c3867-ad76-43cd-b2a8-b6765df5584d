/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2020. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.domain.entity.global.rki;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/10
 */
@Getter
@Setter
@NoArgsConstructor
public class RkiImportTmkRecord extends DataEntity<RkiImportTmkRecord> {

    private static final long serialVersionUID = 4456356245709163901L;
    private MarketRkiServerConfig marketRkiServerConfig;
    private String taskId;
    private Date requestDate;
    private String responseResult;
    private String responseMsg;


}
