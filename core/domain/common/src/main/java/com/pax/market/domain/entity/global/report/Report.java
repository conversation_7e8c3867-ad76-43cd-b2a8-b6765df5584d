package com.pax.market.domain.entity.global.report;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
public class Report extends DataEntity<Report> {

    /**
     *
     */
    private static final long serialVersionUID = 3766414938823207634L;

    private String name;

    /**
     * 报表分类
     */
    private ReportCategory category;

    private String bandFileName;

    /**
     * report状态 A(Active)/I(Inactive)
     */
    private String status;

    /**
     * 是否支持定时生成
     */
    private boolean supportSchedule;

    private List<String> scheduleTypes;

    /**
     * 报表所支持的导出格式，在定义报表模板的时候需要做验证
     */
    private List<String> outputFormats;

    private boolean supportDynamicField;
    private boolean supportSendByMail;
    private String description;
    private boolean superAdminPrivilege;
    private boolean marketAdminPrivilege;
    private boolean resellerAdminPrivilege;

    private List<ReportField> reportFields;
    private List<ReportParameter> reportParameters;
    private List<ReportTemplate> reportTemplates;

    private boolean supportAddToDashboard;

    public Long getCategoryId() {
        if (this.category != null) {
            return category.getId();
        } else {
            return null;
        }
    }

}
