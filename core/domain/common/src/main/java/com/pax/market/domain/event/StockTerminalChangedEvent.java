/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.domain.event;

import com.pax.market.domain.entity.global.terminal.TerminalStock;
import com.pax.api.eventbus.AbstractEvent;
import com.pax.api.eventbus.EventOperator;

/**
 * The type Terminal changed event.
 */
public class StockTerminalChangedEvent extends AbstractEvent {

    private static final long serialVersionUID = -1337174589755149422L;

    private TerminalStock terminal;

    /**
     * Instantiates a new Terminal changed event.
     *
     * @param terminal the terminal
     * @param op       the op
     */
    public StockTerminalChangedEvent(TerminalStock terminal, EventOperator op) {
        this.setTerminal(terminal);
        setEventOperator(op);
    }

    /**
     * Gets terminal.
     *
     * @return the terminal
     */
    public TerminalStock getTerminal() {
        return terminal;
    }

    /**
     * Sets terminal.
     *
     * @param terminal the terminal
     */
    public void setTerminal(TerminalStock terminal) {
        this.terminal = terminal;
    }

    @Override
    public String toString() {
        return "StockTerminalChangedEvent [stockTerminal=" + terminal + ", getSource()=" + getSource() + ", getEventOperator()="
                + getEventOperator() + "]";
    }

}
