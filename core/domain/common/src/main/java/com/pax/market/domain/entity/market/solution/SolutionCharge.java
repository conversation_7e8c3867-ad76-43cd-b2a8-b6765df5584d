/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.domain.entity.market.solution;

import com.pax.market.framework.common.persistence.DataEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/1/16
 */
@Getter
@Setter
public class SolutionCharge extends DataEntity<SolutionCharge> {

    @Serial
    private static final long serialVersionUID = 375488548999854348L;
    private Long appId;
    private Integer chargeMode; // 0:Installation,1:Count
}
