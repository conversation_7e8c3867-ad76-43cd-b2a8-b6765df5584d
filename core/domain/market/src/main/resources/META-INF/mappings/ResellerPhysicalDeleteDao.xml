<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.cleardata.ResellerPhysicalDeleteDao">
    <select id="findResellerIdsByMarketId" resultType="java.lang.Long">
        SELECT id
        FROM pax_reseller
        WHERE market_id = #{marketId}
    </select>
    <select id="findDeletedResellerIds" resultType="java.lang.Long">
        SELECT id
        FROM pax_reseller
        WHERE market_id = #{marketId}
          AND del_flag = 1
          AND TIMESTAMPADD(DAY, #{deleteDays}, updated_date) &lt;= CURRENT_TIMESTAMP
    </select>
    <delete id="deleteReseller">
        DELETE
        FROM pax_reseller
        WHERE id = #{resellerId}
    </delete>
    <delete id="deleteProfile">
        DELETE
        FROM pax_profile
        WHERE reference_id = #{resellerId}
        AND `type` = 'R'
    </delete>
    <delete id="deleteEmmPolicy">
        DELETE
        FROM pax_emm_policy
        WHERE reference_id = #{resellerId}
        AND `type` = 'R'
    </delete>
    <delete id="deleteFromResellerRelatedTable">
        DELETE
        FROM ${relatedTableName}
        WHERE reseller_id = #{resellerId}
    </delete>
</mapper>