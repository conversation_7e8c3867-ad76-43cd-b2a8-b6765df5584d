<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.alarm.AlarmDao">
    <sql id="getAlarmColumns">
		a.id AS "id",
		a.market_id AS "marketId",
		a.reseller_id AS "reseller.id",
		a.receive_type AS "receiveType",
		a.receive_emails AS "receiveEmails",
		a.type AS "type",
	    a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate"
	</sql>

    <sql id="alarmJoins">
		JOIN PAX_RESELLER reseller ON reseller.id = a.reseller_id AND reseller.del_flag = 0
	</sql>


    <select id="get" resultType="com.pax.market.domain.entity.market.alarm.Alarm">
        SELECT
        <include refid="getAlarmColumns"/>
        FROM PAX_ALARM a
        <include refid="alarmJoins"/>
        WHERE a.del_flag = 0
        <if test="id != null">
            AND a.id = #{id}
        </if>
        <if test="marketId != null">
            AND a.market_id = #{marketId}
        </if>
        <if test="reseller != null and reseller.id != null">
            AND a.reseller_id = #{reseller.id}
        </if>
        <if test="type != null">
            AND a.type = #{type}
        </if>
    </select>

	<select id="findList" resultType="com.pax.market.domain.entity.market.alarm.Alarm">
        SELECT
        <include refid="getAlarmColumns"/>
        FROM PAX_ALARM a
        <include refid="alarmJoins"/>
        WHERE a.del_flag = 0
        <if test="id != null">
            AND a.id = #{id}
        </if>
        <if test="marketId != null">
            AND a.market_id = #{marketId}
        </if>
        <if test="reseller != null and reseller.id != null">
            AND a.reseller_id = #{reseller.id}
        </if>
        <if test="type != null">
            AND a.type = #{type}
        </if>
        <if test="receiveTypeEnable == true">
            AND a.receive_type IS NOT NULL
        </if>

		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.id DESC
			</otherwise>
		</choose>
        <if test="limit != null and limit > 0 and startIndex != null and startIndex >= 0">
            LIMIT #{startIndex}, #{limit}
        </if>
    </select>

    <select id="getAlarmId" resultType="Long">
        SELECT
        a.id AS id
        FROM PAX_ALARM a
        WHERE a.del_flag = 0
        <if test="marketId != null">
            AND a.market_id = #{marketId}
        </if>
        <if test="reseller != null and reseller.id != null">
            AND a.reseller_id = #{reseller.id}
        </if>
        <if test="type != null">
            AND a.type = #{type}
        </if>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO PAX_ALARM(
			id,
			market_id,
			reseller_id,
			receive_type,
			receive_emails,
			`type`,
			created_by,
			created_date,
			updated_by,
			updated_date
		) VALUES (
			#{id},
			#{marketId},
			#{reseller.id},
			#{receiveType},
			#{receiveEmails},
			#{type},
			#{createdBy.id},
			#{createdDate},
			#{updatedBy.id},
			#{updatedDate}
		)
	</insert>

    <update id="update">
		UPDATE PAX_ALARM SET
			receive_type = #{receiveType},
			receive_emails = #{receiveEmails},
			updated_by= #{updatedBy.id},
			updated_date = #{updatedDate}
		WHERE id = #{id}
	</update>

    <update id="clearMobileConfig">
        UPDATE pax_alarm
        SET receive_type = REPLACE(REPLACE(REPLACE(receive_type, ',M', ''),'M,',''),'M','')
        WHERE market_id = #{marketId} AND receive_type like '%M%'
    </update>

</mapper>