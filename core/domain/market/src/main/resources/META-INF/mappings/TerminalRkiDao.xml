<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ /*		
  ~  * ===========================================================================================
  ~  * = COPYRIGHT		          
  ~  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION		
  ~  *   This software is supplied under the terms of a license agreement or nondisclosure 	
  ~  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or 
  ~  *   disclosed except in accordance with the terms in that agreement.   		
  ~  *     Copyright (C) 2019. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~  * ===========================================================================================
  ~  */
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.pushtask.TerminalRkiDao">

    <sql id="terminalRkiColumns">
        a.id AS "id",
        a.terminal_id AS "terminal.id",
        a.key_id AS "rkiKey",
        a.status AS "status",
        a.effective_time AS "effectiveTime",
        a.expired_time AS "expiredTime",
		a.created_by AS "createdBy.id",
        a.created_date AS "createdDate",
		a.activated_by AS "activatedBy.id",
		a.activated_date AS "activatedDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate",
        a.action_status AS "actionStatus",
		a.action_time AS "actionTime",
		a.error_code AS "errorCode",
        a.del_flag AS "delFlag"
    </sql>


    <select id="get" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
        <include refid="terminalRkiColumns"/>
        FROM PAX_TERMINAL_RKI_KEY a
        WHERE a.id = #{id}
        LIMIT 1
    </select>

    <select id="getNoneCompletePushTask" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
        <include refid="terminalRkiColumns"/>
        FROM PAX_TERMINAL_RKI_KEY a
        WHERE a.terminal_id = #{terminal.id}
        AND a.del_flag = 0
        <if test="rkiKey != null and rkiKey != ''">
            AND a.key_id = #{rkiKey}
        </if>
        <choose>
            <when test="status != null and status != ''">
                AND a.status = #{status}
            </when>
            <otherwise>
                AND a.status IN ('P', 'A')
            </otherwise>
        </choose>
        LIMIT 1
    </select>

    <select id="findList" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
        <include refid="terminalRkiColumns"/>
        FROM PAX_TERMINAL_RKI_KEY a
        <where>
            AND a.del_flag = 0
            AND a.terminal_id = #{terminal.id}
            <if test="rkiKey != null and rkiKey != ''">
                AND a.key_id = #{rkiKey}
            </if>
            <if test="status != null and status != ''">
                <choose>
                    <when test='status == "A"'>
                        AND a.status IN ('A', 'C')
                    </when>
                    <otherwise>
                        AND a.status = #{status}
                    </otherwise>
                </choose>
            </if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.created_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findListByTerminal" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
        <include refid="terminalRkiColumns"/>
        FROM PAX_TERMINAL_RKI_KEY a
        <where>
            AND a.del_flag = 0
            AND a.terminal_id = #{terminalId}
        </where>
    </select>

    <select id="findNewPushTaskList" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
        terminalRki.id AS "id",
        terminalRki.terminal_id AS "terminalId"
        FROM PAX_TERMINAL_RKI_KEY terminalRki
        WHERE terminalRki.status = 'A'
        AND terminalRki.del_flag = 0
        AND terminalRki.action_status = ${@com.pax.market.constants.TerminalActionStatus@INIT}
        AND (terminalRki.effective_time IS NULL OR terminalRki.effective_time &lt;= CURRENT_TIMESTAMP)
        AND (terminalRki.expired_time IS NULL OR terminalRki.expired_time &gt;= CURRENT_TIMESTAMP)
        <if test="referenceId != null and referenceId > 0">
            AND terminalRki.terminal_id = #{referenceId}
        </if>
        LIMIT ${@com.pax.market.domain.util.SystemPropertyHelper@getTerminalNewPushTaskRetrieveCountPerTime()}
    </select>

    <select id="findPendingPushTaskList" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
            a.id AS "id",
            a.key_id AS "rkiKey",
            a.status AS "status",
            a.effective_time AS "effectiveTime",
            a.action_status AS "actionStatus",
            a.action_time AS "actionTime",
            a.error_code AS "errorCode",
            a.created_date AS "createdDate",
            a.updated_date AS "updatedDate",
            a.id AS "actionId"
        FROM PAX_TERMINAL_RKI_KEY a
        WHERE a.terminal_id = #{terminal.id}
        AND a.del_flag = 0
        AND a.status IN ('P', 'A')
        ORDER BY a.id DESC
        LIMIT ${@com.pax.market.domain.util.SystemPropertyHelper@getTerminalPushTaskPageLimit()}
    </select>

    <select id="findHistoryPushTaskList" resultType="com.pax.market.domain.entity.market.pushtask.TerminalRki">
        SELECT
            a.id AS "id",
            a.key_id AS "rkiKey",
            a.status AS "status",
            a.effective_time AS "effectiveTime",
            a.action_status AS "actionStatus",
            IFNULL(a.action_time, a.updated_date) AS "actionTime",
            a.error_code AS "errorCode",
            a.created_date AS "createdDate",
            a.updated_date AS "updatedDate",
            a.id AS "actionId"
        FROM PAX_TERMINAL_RKI_KEY a
        WHERE a.terminal_id = #{terminal.id}
        AND a.status IN ('S', 'E', 'C')
        AND a.action_status IN (2, 3)
        ORDER BY a.id DESC
        LIMIT ${@com.pax.market.domain.util.SystemPropertyHelper@getTerminalPushTaskPageLimit()}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO PAX_TERMINAL_RKI_KEY(
        id,
        terminal_id,
        key_id,
        status,
        effective_time,
        expired_time,
        created_by,
        created_date,
        activated_by,
        activated_date,
        updated_by,
        updated_date
        ) VALUES (
        #{id},
        #{terminal.id},
        #{rkiKey},
        #{status},
        #{effectiveTime},
        #{expiredTime},
        #{createdBy.id},
        #{createdDate},
        #{activatedBy.id},
        #{activatedDate},
        #{updatedBy.id},
        #{updatedDate}
        )
    </insert>

    <update id="update">
        UPDATE PAX_TERMINAL_RKI_KEY SET
        status = #{status},
        effective_time = #{effectiveTime},
        expired_time = #{expiredTime},
		activated_by = #{activatedBy.id},
		activated_date = #{activatedDate},
        updated_by = #{updatedBy.id},
        updated_date = #{updatedDate}
        WHERE id = #{id}
    </update>

    <update id="updateStatus">
        UPDATE PAX_TERMINAL_RKI_KEY SET
        status = #{status},
        action_status = #{actionStatus},
        error_code = #{errorCode},
        action_time = #{actionTime},
        updated_by = #{updatedBy.id},
        updated_date = #{updatedDate}
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE PAX_TERMINAL_RKI_KEY
        SET
        del_flag = 1,
        status = #{status},
        action_status = #{actionStatus},
        error_code = #{errorCode},
        updated_by = #{updatedBy.id},
        updated_date = #{updatedDate}
        <choose>
            <when test="id !=null and id > 0">
                WHERE id = #{id}
            </when>
            <otherwise>
                WHERE terminal_id = #{terminal.id}
            </otherwise>
        </choose>
    </update>

    <update id="updatePushTaskActionStatus">
        UPDATE PAX_TERMINAL_RKI_KEY SET
        action_status = #{actionStatus}
        <if test="status != null">
            ,status = #{status}
        </if>
        <if test="actionStatus == @com.pax.market.constants.TerminalActionStatus@SUCCEED or actionStatus == @com.pax.market.constants.TerminalActionStatus@FAILED">
            ,action_time = CURRENT_TIMESTAMP
            ,error_code = #{errorCode}
        </if>
        WHERE id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updatePushTaskActionTime">
        UPDATE PAX_TERMINAL_RKI_KEY SET
            action_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <update id="updateInProgressPushTaskActionTime">
        UPDATE PAX_TERMINAL_RKI_KEY a
        JOIN PAX_TERMINAL_ACTION b ON b.action_type = ${@com.pax.market.constants.TerminalActionType@DOWNLOAD_TERMINAL_RKI} AND b.reference_id = a.id
        SET
        a.action_time = b.action_time
        WHERE a.id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findCompletePushTasksWithoutDownloadTime" resultType="java.lang.Long" fetchSize="1000">
        SELECT a.id
        FROM PAX_TERMINAL_RKI_KEY a
        WHERE a.action_status IN (${@com.pax.market.constants.TerminalActionStatus@SUCCEED}, ${@com.pax.market.constants.TerminalActionStatus@FAILED})
        AND a.action_time IS NULL
        UNION ALL
        SELECT a.id AS "id"
        FROM PAX_TERMINAL_RKI_KEY a
        WHERE (a.status IN ('S', 'E', 'C') OR a.del_flag = 1)
        AND a.action_status IN (${@com.pax.market.constants.TerminalActionStatus@INIT}, ${@com.pax.market.constants.TerminalActionStatus@PENDING})
    </select>

    <update id="updatePushTaskDownloadTime">
        UPDATE PAX_TERMINAL_RKI_KEY a
        JOIN PAX_TERMINAL_ACTION_HISTORY b ON b.action_type = ${@com.pax.market.constants.TerminalActionType@DOWNLOAD_TERMINAL_RKI} AND b.reference_id = a.id
        SET
        a.action_status = b.status,
        a.action_time = b.action_time,
        a.error_code = b.error_code
        WHERE a.id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updatePushTaskStatusToComplete">
        UPDATE PAX_TERMINAL_RKI_KEY a
        SET a.status = 'C'
        WHERE a.id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>