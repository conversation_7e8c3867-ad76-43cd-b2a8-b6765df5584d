<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.airlink.AirLinkTerminalProfileDao">

    <sql id="columns">
        a.id as "id",
        a.airlink_terminal_id as "airlinkTerminalId",
        a.operator as "operator",
        a.iccid as "iccid",
        a.msisdn as "msisdn",
        a.status as "status",
		a.created_date AS "createdDate"
    </sql>

    <insert id="insertList">
        REPLACE INTO pax_airlink_terminal_profile(
            id,
            airlink_terminal_id,
            operator,
            iccid,
            msisdn,
            status,
            created_date
        )VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.id},
            #{item.airlinkTerminalId},
            #{item.operator},
            #{item.iccid},
            #{item.msisdn},
            #{item.status},
            #{item.createdDate}
        )
        </foreach>
    </insert>

    <select id="getProfileList"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile">
        select
        <include refid="columns" />
        from `pax_airlink_terminal_profile` a
        where a.airlink_terminal_id = #{airlinkTerminalId}
    </select>

    <select id="getProfileByIccid"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile">
        select
        <include refid="columns" />
        from `pax_airlink_terminal_profile` a
        where a.iccid = #{iccid}
    </select>
    <select id="getCurrentUsedProfile"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile">
        select
        <include refid="columns" />
        from `pax_airlink_terminal_profile` a
        where a.airlink_terminal_id = #{airLinkTerminalId}
          and a.status = 'U'
    </select>
    <select id="getUsedOrSwitchProfile"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile">
        select
        <include refid="columns" />
        from `pax_airlink_terminal_profile` a
        where a.airlink_terminal_id = #{airLinkTerminalId}
        and a.status in ('U','W')
        limit 1
    </select>

    <update id="update">
        UPDATE pax_airlink_terminal_profile SET
        operator = #{operator},
        status = #{status}
        WHERE id = #{id}
    </update>
    <delete id="physicalDelete">
        DELETE FROM pax_airlink_terminal_profile
        WHERE id = #{id}
    </delete>

    <delete id="physicalDeleteByIds">
        DELETE FROM pax_airlink_terminal_profile
        WHERE id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>