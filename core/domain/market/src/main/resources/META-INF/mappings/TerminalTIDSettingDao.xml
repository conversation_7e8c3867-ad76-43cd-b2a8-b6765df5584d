<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.terminal.TerminalTIDSettingDao">

    <insert id="insertList">
        INSERT INTO PAX_TERMINAL_TID_SETTING(
        id,
        `key`,
        `value`,
        created_date,
        reseller_id
        ) VALUES
        <foreach collection="terminalTIDSettings" index="index" item="tidSetting" open="" separator="," close="">
            (
            #{tidSetting.id},
            #{tidSetting.key},
            #{tidSetting.value},
            #{tidSetting.createdDate},
            #{tidSetting.resellerId}
            )
        </foreach>
    </insert>

    <select id="findTIDSettingList" resultType="com.pax.market.domain.entity.market.terminal.TerminalTIDSetting">
        SELECT
        `id` AS "id",
        `key` AS "key",
        `value` AS "value",
        reseller_id AS "resellerId"
        FROM PAX_TERMINAL_TID_SETTING
        WHERE reseller_id=#{resellerId}
    </select>

    <delete id="deleteByResellerId">
        DELETE FROM
        PAX_TERMINAL_TID_SETTING
        <where>
            reseller_id=#{resellerId}
        </where>
    </delete>

    <select id="isSettingExist" resultType="java.lang.Boolean">
        SELECT EXISTS(SELECT id FROM PAX_TERMINAL_TID_SETTING WHERE reseller_id=#{resellerId})
    </select>

    <select id="getTIDSetting" resultType="com.pax.market.domain.entity.market.terminal.TerminalTIDSetting">
        SELECT
        `id` AS "id",
        `key` AS "key",
        `value` AS "value",
        reseller_id AS "resellerId"
        FROM PAX_TERMINAL_TID_SETTING
        WHERE reseller_id=#{resellerId}
        AND `key`=#{key}
    </select>
</mapper>