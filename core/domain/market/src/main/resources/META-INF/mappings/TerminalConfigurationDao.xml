<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.terminal.TerminalConfigurationDao">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO PAX_TERMINAL_CONFIGURATION(
            id,
            terminal_id,
            `type`,
            `key`,
            `value`,
            json_value,
            sync_date
        ) VALUES (
            #{id},
            #{terminalId},
            #{type},
            #{key},
            #{value},
            #{jsonValue},
            #{syncDate}
        )
    </insert>

    <update id="update">
        UPDATE PAX_TERMINAL_CONFIGURATION SET
        `type` = #{type},
        `value` = #{value},
        json_value = #{jsonValue},
        sync_date = #{syncDate}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM PAX_TERMINAL_CONFIGURATION
        WHERE id = #{id}
    </delete>

    <select id="findList" resultType="com.pax.market.domain.entity.market.terminal.TerminalConfiguration">
        SELECT
        `id` AS "id",
        terminal_id AS "terminalId",
        `type` AS "type",
        `key` AS "key",
        `value` AS "value",
        json_value AS "jsonValue",
        sync_date AS "syncDate"
        FROM PAX_TERMINAL_CONFIGURATION terminalConfiguration
        <where>
            terminalConfiguration.terminal_id = #{terminalId}
            <if test="type != null">
                AND `type` = #{type}
            </if>
        </where>
    </select>

    <select id="getByKey" resultType="com.pax.market.domain.entity.market.terminal.TerminalConfiguration">
        SELECT
        `id` AS "id",
        terminal_id AS "terminalId",
        `type` AS "type",
        `key` AS "key",
        `value` AS "value",
        json_value AS "jsonValue",
        sync_date AS "syncDate"
        FROM PAX_TERMINAL_CONFIGURATION terminalConfiguration
        <where>
            terminalConfiguration.terminal_id = #{terminalId}
            AND `key` = #{key}
            <if test="type != null">
                AND `type` = #{type}
            </if>
        </where>
        ORDER BY id DESC
        LIMIT 1
    </select>

    <delete id="deleteByTerminal">
        DELETE FROM
        PAX_TERMINAL_CONFIGURATION
        <where>
            terminal_id = #{terminalId}
        </where>
    </delete>

    <delete id="deleteByTerminals">
        DELETE FROM PAX_TERMINAL_CONFIGURATION
        WHERE terminal_id IN
        <foreach collection="terminalIds" open="(" close=")" item="terminalId" separator=",">
            #{terminalId}
        </foreach>
    </delete>

</mapper>