<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.airlink.AirLinkTerminalDao">
    <resultMap id="airLinkTerminalMap" type="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        <id property="id" column="id" javaType="java.lang.Long"/>
        <result property="marketId" column="marketId" javaType="java.lang.Long"/>
        <result property="resellerId" column="resellerId" javaType="java.lang.Long"/>
        <result property="activeHistoryId" column="activeHistoryId" javaType="java.lang.Long"/>
        <result property="imei" column="imei" javaType="java.lang.String"/>
        <result property="eid" column="eid" javaType="java.lang.String"/>
        <result property="serialNo" column="serialNo" javaType="java.lang.String"/>
        <result property="status" typeHandler="com.pax.market.framework.common.persistence.enumeration.StringCodeEnumTypeHandler"
                column="status" javaType="com.pax.market.constants.airlink.AirLinkTerminalStatus"/>
        <result property="activeTime" column="activeTime" javaType="java.util.Date"/>
        <result property="monthUsage" column="monthUsage" javaType="java.math.BigDecimal"/>
        <result property="lastMonthUsage" column="lastMonthUsage" javaType="java.math.BigDecimal"/>
        <result property="totalUsage" column="totalUsage" javaType="java.math.BigDecimal"/>
        <result property="reason" column="reason" javaType="java.lang.String"/>
        <result property="delFlag" column="delFlag" javaType="java.lang.String"/>
        <result property="delTime" column="delTime" javaType="java.util.Date"/>
        <result property="overdueStatus" typeHandler="com.pax.market.framework.common.persistence.enumeration.StringCodeEnumTypeHandler"
                column="overdueStatus" javaType="com.pax.market.constants.airlink.AirLinkTerminalOverdueStatus"/>
        <result property="overdueStatusUpdateTime" column="overdueStatusUpdateTime" javaType="java.util.Date"/>
        <result property="createdBy.id" column="createdBy.id" javaType="java.lang.Long"/>
        <result property="createdDate" column="createdDate" javaType="java.util.Date"/>
        <result property="updatedBy.id" column="updatedBy.id" javaType="java.lang.Long"/>
        <result property="updatedDate" column="updatedDate" javaType="java.util.Date"/>
    </resultMap>
    
    <sql id="columns">
        a.id as "id",
        a.market_id as "marketId",
        a.reseller_id as "resellerId",
        a.active_history_id as "activeHistoryId",
        a.imei as "imei",
        a.eid as "eid",
        a.serial_no as "serialNo",
        a.status as "status",
        a.active_time as "activeTime",
        a.month_usage as "monthUsage",
        a.last_month_usage as "lastMonthUsage",
        a.total_usage as "totalUsage",
        a.reason as "reason",
        a.overdue_status as "overdueStatus",
        a.overdue_status_update_time as "overdueStatusUpdateTime",
        a.del_flag as "delFlag",
        a.del_time as "delTime",
		a.created_date AS "createdDate"
    </sql>

    <insert id="insertList" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO pax_airlink_terminal(
            id,
            market_id,
            reseller_id,
            active_history_id,
            imei,
            eid,
            status,
            active_time,
            reason,
            created_date,
            created_by,
            updated_date,
            updated_by
        )VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.id},
            #{item.marketId},
            #{item.resellerId},
            #{item.activeHistoryId},
            #{item.imei},
            #{item.eid},
            #{item.status.code},
            #{item.activeTime},
            #{item.reason},
            #{item.createdDate},
            #{item.createdBy.id},
            #{item.updatedDate},
            #{item.updatedBy.id}
        )
        </foreach>
    </insert>

    <select id="getByIdAndResellerId"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        select
        <include refid="columns" />
        from `pax_airlink_terminal` a
        where a.id = #{id}
        <if test="resellerId != null">
            and a.reseller_id = #{resellerId}
        </if>
        AND a.del_flag = 0
    </select>


    <select id="get" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal" resultMap="airLinkTerminalMap">
        select
        <include refid="columns" />
        from `pax_airlink_terminal` a
        where a.id = #{id}
    </select>

    <select id="existImei" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT 1
            FROM `pax_airlink_terminal`
            WHERE imei = #{imei}
                AND status != '${@<EMAIL>}'
                AND del_flag = 0
        )
    </select>

    <select id="findTerminalList" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal" resultMap="airLinkTerminalMap">
        select
        <include refid="columns" />
        FROM `pax_airlink_terminal` a
        <where>
            a.del_flag = 0
            <if test="operator != null and operator != ''" >
                and EXISTS (SELECT 1 from `pax_airlink_terminal_profile` b where b.airlink_terminal_id = a.id and b.operator = #{operator} and b.status = 'U')
            </if>
            <if test="iccid != null and iccid != ''" >
                and EXISTS (SELECT 1 from `pax_airlink_terminal_profile` b where b.airlink_terminal_id = a.id and INSTR(b.iccid , #{iccid}) > 0)
            </if>
            <if test="serialNo != null and serialNo != ''">
                AND INSTR(a.serial_no , #{serialNo}) > 0
            </if>
            <if test="marketId != null">
                and a.market_id = #{marketId}
            </if>
            <if test="resellerId != null">
                and a.reseller_id = #{resellerId}
            </if>
            <if test="status != null and status.code != null and status.code != ''">
                and a.status = #{status.code}
            </if>
            <if test="imei != null and imei != ''">
                AND INSTR(a.imei , #{imei}) > 0
            </if>
            <if test="fromDate != null">
                AND a.active_time &gt;= #{fromDate}
            </if>
            <if test="toDate != null">
                AND a.active_time &lt;= #{toDate}
            </if>
            <if test="status == null">
                AND a.status in ('${@<EMAIL>}',
                '${@<EMAIL>}',
                '${@<EMAIL>}',
                '${@<EMAIL>}'
                )
            </if>
        </where>
        <choose>
            <when test="page!=null and page.orderBy!=null and  page.orderBy!='' ">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.active_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="getDeletedAirLinkTerminalList"
            resultType="com.pax.market.dto.airlink.AirLinkDeletedTerminalInfo">
        select a.imei,
               a.eid,
               a.reseller_id AS "resellerId",
               a.serial_no AS "serialNo",
               a.total_usage AS "totalUsage",
               a.created_date AS "createdDate",
               a.del_time AS "delTime",
               b.iccid,
               b.operator
        from pax_airlink_terminal a
        left join pax_airlink_terminal_profile b on a.id = b.airlink_terminal_id
        <where>
            a.del_flag = 1
            <if test="request.resellerId != null and request.resellerId != ''">
                AND a.reseller_id = #{request.resellerId}
            </if>
            <if test="request.operator != null and request.operator != ''">
                AND b.operator = #{request.operator}
            </if>
            <if test="request.imei != null and request.imei != ''">
                AND INSTR(a.imei , #{request.imei}) > 0
            </if>
            <if test="request.iccid != null and request.iccid != ''">
                AND INSTR(b.iccid , #{request.iccid}) > 0
            </if>
            <if test="request.serialNo != null and request.serialNo != ''">
                AND INSTR(a.serial_no , #{request.serialNo}) > 0
            </if>
            <if test="request.createdStartTime != null">
                AND a.created_date &gt;= #{request.createdStartTime}
            </if>
            <if test="request.createdEndTime != null">
                AND a.created_date &lt;= #{request.createdEndTime}
            </if>
            <if test="request.deletedStartTime != null">
                AND a.del_time &gt;= #{request.deletedStartTime}
            </if>
            <if test="request.deletedEndTime != null">
                AND a.del_time &lt;= #{request.deletedEndTime}
            </if>
        </where>
        <choose>
            <when test="page!=null and page.orderBy!=null and  page.orderBy!='' ">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.del_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="getAirLinkTerminalOperatorList" resultType="java.lang.String">
        select distinct b.operator
        from pax_airlink_terminal a
                  join pax_airlink_terminal_profile b on a.id = b.airlink_terminal_id
        where a.reseller_id  = #{resellerId}
    </select>

    <select id="getByIdAndResellerIdAndStatus"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        select
        <include refid="columns" />
        from `pax_airlink_terminal` a
        where a.id = #{id}
        <if test="resellerId != null">
            and a.reseller_id = #{resellerId}
        </if>
        <if test="status != null">
            and a.status = #{status.code}
        </if>
        AND a.del_flag = 0
    </select>

    <select id="getNewTerminalsTodayCount" resultType="java.lang.Long">
        select count(1)
        from `pax_airlink_terminal` a
        where  a.active_time &gt;= CURDATE() AND a.active_time &lt;= DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        <if test="resellerId != null">
            and a.reseller_id = #{resellerId}
         </if>
        AND a.status in ('${@<EMAIL>}',
        '${@<EMAIL>}',
        '${@<EMAIL>}',
        '${@<EMAIL>}'
        )
    </select>

    <select id="getTotalTerminalCount" resultType="java.lang.Long">
        select count(1)
        from `pax_airlink_terminal` a
        where  a.del_flag = 0
        <if test="resellerId != null">
            and a.reseller_id = #{resellerId}
        </if>
        AND a.status in ('${@<EMAIL>}',
        '${@<EMAIL>}',
        '${@<EMAIL>}',
        '${@<EMAIL>}'
        )
    </select>

    <select id="getCurrentMonthUsage" resultType="java.lang.Long">
        select SUM(month_usage)
        from `pax_airlink_terminal` a
        <where>
            <if test="beginOfMonth != null">
                AND a.updated_date &gt;= #{beginOfMonth}
            </if>
            <if test="resellerId != null">
                AND a.reseller_id = #{resellerId}
            </if>
            AND a.status in ('${@<EMAIL>}',
            '${@<EMAIL>}',
            '${@<EMAIL>}',
            '${@<EMAIL>}'
            )
        </where>
    </select>

    <select id="statisticsTerminalNum" resultType="com.pax.market.dto.airlink.AirLinkTerminalStatisticsInfo">
        SELECT
            SUM(status = 'U' OR status = 'D' OR status = 'G' OR status = 'S') as activeNum,
            SUM(status = 'F') as failNum,
            SUM(status = 'I' OR status = 'P') as pendingNum
        FROM pax_airlink_terminal
        WHERE active_history_id = #{activeHistoryId}
    </select>

    <select id="findCurrentMonthNotDeductActiveTermianls" resultType="java.lang.Long">
        SELECT id
        FROM pax_airlink_terminal t
        WHERE t.active_history_id = #{activeHistoryId}
          AND (status = 'U' OR status = 'D' OR status = 'G' OR status = 'S')
          AND t.active_time >= #{startTime}
          AND t.active_time &lt;= #{endTime}
          AND NOT EXISTS(
              SELECT 1
              FROM pax_airlink_terminal_month_deduct_detail d
              WHERE d.airLink_terminal_id = t.id
                AND d.period = #{period}
        )
    </select>
    <select id="findTerminalListByHistoryId"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        select
        <include refid="columns" />
        FROM pax_airlink_terminal a
        WHERE a.active_history_id = #{activeHistoryId}
        <if test="limit != null and limit > 0">
            LIMIT  #{limit}
        </if>
    </select>

    <select id="findNonCurrentMonthActiveAvailTerminals" resultType="java.lang.Long">
        SELECT id
        FROM pax_airlink_terminal t
        WHERE t.market_id = #{marketId}
            AND t.status IN ('U', 'G')
            AND t.active_time &lt; #{monthStartTime}
            AND t.del_flag = 0
            AND NOT EXISTS(
                SELECT 1
                FROM pax_airlink_terminal_month_deduct_detail d
                WHERE d.airLink_terminal_id = t.id
                AND d.period = #{period}
            )
    </select>
    <select id="countInUseByMarket" resultType="java.lang.Long">
        SELECT count(1) FROM pax_airlink_terminal t
        WHERE t.market_id = #{marketId}
        AND t.status in ('${@<EMAIL>}',
                         '${@<EMAIL>}')
        AND t.del_flag = 0
    </select>

    <select id="findNotActiveImeisByActiveHistoryId" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        SELECT a.id, a.imei
        FROM pax_airlink_terminal a
        WHERE a.active_history_id = #{activeHistoryId}
            AND a.status IN ('I', 'P')
    </select>

    <select id="getTerminalListCountByHistoryIdForExport" resultType="java.lang.Integer">
        select
        count(1)
        FROM pax_airlink_terminal a
        WHERE a.active_history_id = #{activeHistoryId}
        <if test="limit != null and limit > 0">
            LIMIT  #{limit}
        </if>
    </select>

    <update id="delete">
        UPDATE pax_airlink_terminal SET
        del_flag = 1,
        total_usage = #{totalUsage},
        month_usage = #{monthUsage},
        updated_date = CURRENT_TIMESTAMP,
        del_time = CURRENT_TIMESTAMP
        WHERE id = #{id}  AND del_flag = 0
    </update>

    <update id="batchUpdate">
        UPDATE pax_airlink_terminal SET
        status = #{toStatus.code},
        status_update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="airLinkTerminalList" index="index" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
        AND status = #{fromStatus.code}
    </update>

    <select id="getAirLinkActiveTerminalList" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        SELECT
            a.id as "id",
            a.imei as "imei"
        FROM `pax_airlink_terminal` a
        <where>
            a.market_id = #{marketId}
            AND a.status IN ('U','D','G','S')
            AND a.del_flag = 0
            <if test="resellerId != null">
                AND a.reseller_id = #{resellerId}
            </if>
            <if test="lastId != null">
                AND a.id > #{lastId}
            </if>
        </where>
        ORDER BY a.id
        LIMIT #{limit}
    </select>

    <update id="updateByMarketOrReseller">
        UPDATE pax_airlink_terminal
        SET del_flag = 1,
            del_time = CURRENT_TIMESTAMP,
            updated_date = CURRENT_TIMESTAMP
        WHERE market_id = #{marketId}
            <if test="resellerId != null">
            AND reseller_id = #{resellerId}
            </if>
            AND del_flag = 0
    </update>

    <select id="findByImeis" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        SELECT
        <include refid="columns" />
        FROM pax_airlink_terminal a
        WHERE  market_id = #{marketId}
          AND a.status IN ('U','D','G','S')
          AND a.imei IN
        <foreach collection="imeis" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND del_flag = 0
    </select>

    <update id="update2PendingStatusById">
        UPDATE pax_airlink_terminal
        SET status = #{status.code}
        <if test="activeTime != null">
            ,active_time = #{activeTime}
        </if>
        <if test="updatedDate != null">
            ,updated_date = #{updatedDate}
        </if>
        <if test="reason != null and reason != ''">
            ,reason = #{reason}
        </if>
        <if test="eid != null and eid != ''">
            ,eid = #{eid}
        </if>
        <if test="serialNo != null and serialNo != ''">
            ,serial_no = #{serialNo}
        </if>
        WHERE status = 'I'
        <choose>
            <when test="id != null">
                AND id = #{id}
            </when>
            <otherwise>
                AND imei = #{imei}
                <if test="activeHistoryId != null">
                    AND active_history_id = #{activeHistoryId}
                </if>
                <if test="marketId != null">
                    AND market_id = #{marketId}
                </if>
            </otherwise>
        </choose>
    </update>

    <select id="getNotActiveTerminalByMarketAndImei" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        SELECT
        <include refid="columns" />
        FROM pax_airlink_terminal a
        WHERE a.imei = #{imei}
            AND a.market_id = #{marketId}
            AND a.status IN ('I', 'P')
            AND a.del_flag = 0
    </select>

    <select id="getActiveTerminalByMarketAndImei" resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        SELECT
        id
        FROM pax_airlink_terminal
        WHERE imei = #{imei}
        AND market_id = #{marketId}
        AND status = 'U'
        AND del_flag = 0
    </select>

    <select id="findListByMarketId"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        select
        <include refid="columns" />
        from pax_airlink_terminal a
        where a.market_id = #{marketId}
          and a.del_flag = 0;
    </select>

    <select id="getUsedAirLinkTerminalListByMarket"
            resultType="com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal">
        SELECT
        a.id as "id",
        a.imei as "imei"
        FROM `pax_airlink_terminal` a
        <where>
            a.market_id = #{marketId}
            AND status = 'U'
            AND a.del_flag = 0
            <if test="lastId != null">
                AND a.id > #{lastId}
            </if>
        </where>
        ORDER BY a.id
        LIMIT #{limit}
    </select>

    <update id="updateAirLinkTerminalStatus">
        UPDATE pax_airlink_terminal
        SET status = #{status.code},
            active_time = #{activeTime},
            updated_date = #{updatedDate}
        <if test="reason != null and reason != ''">
            ,reason = #{reason}
        </if>
        <if test="eid != null and eid != ''">
            ,eid = #{eid}
        </if>
        WHERE id = #{id}
    </update>
    <update id="batchUpdateOverdueStatus">
        UPDATE pax_airlink_terminal SET
        overdue_status = #{toStatus.code},
        overdue_status_update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="airLinkTerminalList" index="index" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
        <if test="fromStatus.code != null and fromStatus.code != null and fromStatus.code != ''">
            AND overdue_status = #{fromStatus.code}
        </if>
        AND del_flag = 0
    </update>

</mapper>