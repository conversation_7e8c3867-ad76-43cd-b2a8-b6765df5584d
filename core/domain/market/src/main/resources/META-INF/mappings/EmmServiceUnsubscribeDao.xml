<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.global.domain.dao.emm.EmmServiceUnsubscribeDao">

    <sql id="unsubscribeMap">
        `id` as "id",
        `market_id` as "marketId",
        `enterprise_id` as "enterpriseId",
        `unsubscribe_date` as "unsubscribeDate",
        `created_by` as "createdBy.id",
        `updated_by` as "updatedBy.id",
        `created_date` as "createdDate"
    </sql>

    <insert id="insert">
        INSERT INTO PAX_EMM_SERVICE_UNSUBSCRIBE
        (`market_id`,
         `enterprise_id`,
         `unsubscribe_date`,
         `created_by`,
         `updated_by`,
         `created_date`,
         `updated_date`)
        VALUES (#{marketId},
                #{enterpriseId},
                #{unsubscribeDate},
                #{createdBy.id},
                #{updatedBy.id},
                #{createdDate},
                #{updatedDate})
    </insert>

    <update id="update">
        UPDATE PAX_EMM_SERVICE_UNSUBSCRIBE
        SET `enterprise_id` = #{enterpriseId},
            `unsubscribe_date` = #{unsubscribeDate},
            `updated_by`       = #{updatedBy.id},
            `updated_date`     = #{updatedDate}
        WHERE `market_id` = #{marketId}
    </update>

    <select id="get" resultType="com.pax.market.domain.entity.global.emm.EmmServiceUnsubscribe">
        SELECT
            <include refid="unsubscribeMap" />
        FROM
            PAX_EMM_SERVICE_UNSUBSCRIBE
        WHERE
            `market_id` = #{marketId}
    </select>

</mapper>