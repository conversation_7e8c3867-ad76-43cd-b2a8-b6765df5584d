<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.terminal.TerminalReplaceHistoryDao">

    <select id="getLatestTerminalReplaceLog" resultType="com.pax.market.domain.entity.market.terminal.TerminalReplaceLog">
        SELECT
            a.original_sn AS "originalSN",
            a.replaced_sn AS "replacedSN",
            a.replaced_by AS "replacedBy.id",
            a.replaced_date AS "replacedDate"
        FROM PAX_TERMINAL_REPLACE_HISTORY a
        WHERE a.terminal_id = #{terminalId}
        ORDER BY a.replaced_date DESC
            LIMIT 1
    </select>

    <select id="findTerminalReplaceLogs" resultType="com.pax.market.domain.entity.market.terminal.TerminalReplaceLog">
        SELECT
            a.original_sn AS "originalSN",
            a.replaced_sn AS "replacedSN",
            a.replaced_by AS "replacedBy.id",
            a.replaced_date AS "replacedDate"
        FROM PAX_TERMINAL_REPLACE_HISTORY a
        WHERE a.terminal_id = #{terminalId}
        ORDER BY a.replaced_date DESC
    </select>


    <insert id="insert">
        INSERT INTO PAX_TERMINAL_REPLACE_HISTORY(
            id,
            terminal_id,
            original_sn,
            replaced_sn,
            replaced_by,
            replaced_date
        ) VALUES (
            #{id},
            #{terminalId},
            #{originalSN},
            #{replacedSN},
            #{createdBy.id},
            #{createdDate}
        )
    </insert>


</mapper>