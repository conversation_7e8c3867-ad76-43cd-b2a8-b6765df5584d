<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.emm.EmmDeviceInstalledAppDao">

    <sql id="columns">
        a.id AS "id",
        a.terminal_id AS "terminalId",
        a.package_name AS "packageName",
        a.name AS "name",
        a.version AS "version",
        a.type AS "type",
        a.size AS "size",
        a.icon_md5 AS "iconMd5",
        a.install_time AS "installTime",
        a.last_time_update AS "lastTimeUpdate",
        a.is_launcher AS "isLauncher",
        a.is_default_launcher AS "isDefaultLauncher",
        a.sync_date AS "syncDate"
    </sql>

    <insert id="insert">
        INSERT INTO PAX_EMM_DEVICE_INSTALLED_APP (
            id,
            terminal_id,
            package_name,
            name,
            version,
            type,
            size,
            icon_md5,
            install_time,
            last_time_update,
            is_launcher,
            is_default_launcher,
            sync_date
        ) VALUES (
             #{id},
             #{terminalId},
             #{packageName},
             #{name},
             #{version},
             #{type},
             #{size},
             #{iconMd5},
             #{installTime},
             #{lastTimeUpdate},
             #{isLauncher},
             #{isDefaultLauncher},
             #{syncDate}
        )
    </insert>

    <select id="findListByDevice" resultType="com.pax.market.domain.entity.market.emm.EmmDeviceInstalledApp">
        SELECT <include refid="columns"/>
        FROM PAX_EMM_DEVICE_INSTALLED_APP a
        WHERE a.terminal_id = #{terminalId}
    </select>

    <select id="findList" resultType="com.pax.market.domain.entity.market.emm.EmmDeviceInstalledApp">
        SELECT <include refid="columns"/>
        FROM PAX_EMM_DEVICE_INSTALLED_APP a
        <where>
            a.terminal_id = #{terminalId}
        </where>
        <choose>
            <when test="page != null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.install_time DESC
            </otherwise>
        </choose>
    </select>

    <update id="update">
        UPDATE PAX_EMM_DEVICE_INSTALLED_APP SET
        name = #{name},
        version = #{version},
        size = #{size},
        type = #{type},
        icon_md5 = #{iconMd5},
        install_time = #{installTime},
        last_time_update = #{lastTimeUpdate},
        is_launcher = #{isLauncher},
        is_default_launcher = #{isDefaultLauncher},
        sync_date = #{syncDate}
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM PAX_EMM_DEVICE_INSTALLED_APP
        WHERE id = #{id}
    </delete>

    <delete id="deleteByDevices">
        DELETE FROM PAX_EMM_DEVICE_INSTALLED_APP
        WHERE terminal_id IN
        <foreach collection="terminalIds" open="(" close=")" item="terminalId" separator=",">
            #{terminalId}
        </foreach>
    </delete>

</mapper>