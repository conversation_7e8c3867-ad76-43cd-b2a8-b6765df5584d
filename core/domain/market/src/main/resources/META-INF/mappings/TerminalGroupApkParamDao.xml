<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.pushtask.TerminalGroupApkParamDao">

	<sql id="groupApkParamColumns">
		a.id AS "id",
		a.group_apk_id AS "groupApk.id",
		groupApk.type AS "groupApk.type",
		groupApk.status AS "groupApk.status",
		groupApk.group_id AS "groupApk.group.id",
		groupApk.apk_id AS "groupApk.apk.id",
		groupApk.group_id AS "group.id",
		a.status AS "status",
		a.effective_time AS "effectiveTime",
		a.expired_time AS "expiredTime",
		a.download_url AS "downloadUrl",
		a.md5 AS "md5",
		a.sha256 AS "sha256",
		a.param_template_name AS "paramTemplateName",
		a.param_template_id AS "paramTemplateId",
		a.param_files AS "paramFiles",
		a.param_file_map AS "paramFileMap",
		a.param AS "param",
		a.action_no AS "actionNo",
		a.action_status AS "actionStatus",
		a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate",
		a.del_flag AS "delFlag"
	</sql>

    <sql id="groupApkParamJoins">
		JOIN PAX_TM_GROUP_APK groupApk ON groupApk.id = a.group_apk_id
	</sql>

    <select id="get" resultType="com.pax.market.domain.entity.market.pushtask.TerminalGroupApkParam">
        SELECT
        <include refid="groupApkParamColumns"/>
        FROM PAX_TM_GROUP_APK_PARAM a
        <include refid="groupApkParamJoins"/>
		WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="com.pax.market.domain.entity.market.pushtask.TerminalGroupApkParam">
        SELECT
        <include refid="groupApkParamColumns"/>
        FROM PAX_TM_GROUP_APK_PARAM a
        <include refid="groupApkParamJoins"/>
        <where>
			AND a.del_flag = 0
            <if test="groupApk != null and groupApk.id != null and groupApk.id > 0">
                AND a.group_apk_id = #{groupApk.id}
            </if>
			<if test="groupApk != null and groupApk.group != null and groupApk.group.id != null and groupApk.group.id > 0">
				AND groupApk.group_id = #{groupApk.group.id}
			</if>
			<if test="groupApk != null and groupApk.apk != null and groupApk.apk.id != null and groupApk.apk.id > 0">
				AND groupApk.apk_id = #{groupApk.apk.id}
			</if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.created_date desc
            </otherwise>
        </choose>
    </select>

	<select id="findListByGroup" resultType="com.pax.market.domain.entity.market.pushtask.TerminalGroupApkParam">
		SELECT
		a.id AS "id"
		FROM PAX_TM_GROUP_APK_PARAM a
		JOIN PAX_TM_GROUP_APK groupApk ON groupApk.id = a.group_apk_id
		<where>
			AND a.del_flag = 0
			AND groupApk.group_id = #{groupId}
			<if test="pushTaskStatus != null and pushTaskStatus != ''">
				AND a.status = #{pushTaskStatus}
			</if>
		</where>
	</select>

	<select id="getLatestHistoryGroupApkParam" resultType="com.pax.market.domain.entity.market.pushtask.TerminalGroupApkParam">
		SELECT
		groupApk.apk_id AS "apkId",
		a.param_template_name AS "paramTemplateName",
		a.param AS "param"
		FROM PAX_TM_GROUP_APK_PARAM a
		JOIN PAX_TM_GROUP_APK groupApk ON groupApk.id = a.group_apk_id AND groupApk.del_flag = 0
		WHERE
		groupApk.group_id = #{groupId}
		AND a.del_flag = 0
		AND groupApk.apk_id IN
		<foreach collection="apkIds" index="index" item="apkId" open="(" separator="," close=")">
			#{apkId}
		</foreach>
		<if test="paramTemplateName != null">
			AND (
				a.param_template_name = #{paramTemplateName}
				OR INSTR(a.param_template_name, CONCAT('|',#{paramTemplateName})) > 0
				OR INSTR(a.param_template_name, CONCAT(#{paramTemplateName},'|')) > 0
				OR INSTR(a.param_template_name,  CONCAT('|',#{paramTemplateName},'|')) > 0
			)
		</if>
		AND a.status != 'P'
		AND groupApk.status != 'P'
		ORDER BY a.updated_date DESC
		LIMIT 1
	</select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		INSERT INTO PAX_TM_GROUP_APK_PARAM(
		    id,
			group_apk_id,
			status,
			effective_time,
			expired_time,
			download_url,
			md5,
			sha256,
			param_template_name,
			param_template_id,
			param,
			created_by,
			created_date,
			updated_by,
			updated_date
	    	) VALUES (
	        #{id},
			#{groupApk.id},
			#{status},
			#{effectiveTime},
			#{expiredTime},
			#{downloadUrl},
			#{md5},
			#{sha256},
			#{paramTemplateName},
			#{paramTemplateId},
			#{param},
			#{createdBy.id},
			#{createdDate},
			#{updatedBy.id},
			#{updatedDate}
		)
	</insert>

    <update id="update">
		UPDATE PAX_TM_GROUP_APK_PARAM SET
			   status = #{status},
			   param_template_name = #{paramTemplateName},
			   param_template_id = #{paramTemplateId},
			   param = #{param},
			   updated_by = #{updatedBy.id},
			   updated_date = #{updatedDate}
		WHERE id = #{id}
	</update>

	<update id="delete">
		UPDATE PAX_TM_GROUP_APK_PARAM SET
		del_flag = 1
		WHERE id = #{id}
	</update>

	<update id="physicalDelete">
		DELETE FROM PAX_TM_GROUP_APK_PARAM
		WHERE id = #{id}
	</update>

	<update id="updateStatus">
		UPDATE PAX_TM_GROUP_APK_PARAM SET
		status = #{status}
		WHERE id = #{id}
	</update>

	<update id="updateDownloadInfo">
		UPDATE PAX_TM_GROUP_APK_PARAM SET
		param = #{param},
		download_url = #{downloadUrl},
		md5 = #{md5},
		sha256 = #{sha256},
		file_size = #{fileSize},
		param_files = #{paramFiles},
		param_file_map = #{paramFileMap}
		WHERE id = #{id}
	</update>

	<update id="updatePushTaskActionStatus">
		UPDATE PAX_TM_GROUP_APK_PARAM SET
		action_status = #{actionStatus}
		WHERE id IN
		<foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<select id="findResellerTerminalActions" resultType="com.pax.market.domain.entity.market.terminal.TerminalAction" fetchSize="1000">
		SELECT
		terminalAction.id AS "id",
		terminalAction.terminal_id AS "terminalId"
		FROM
		pax_terminal_action terminalAction
		JOIN pax_tm_group_apk_param groupApkParam ON terminalAction.action_type = ${@com.pax.market.constants.TerminalActionType@DOWNLOAD_GROUP_PARAM} AND terminalAction.reference_id = groupApkParam.id
		JOIN pax_tm_group_apk groupApk ON groupApkParam.group_apk_id = groupApk.id
		WHERE
		terminalAction.terminal_id IN (
			SELECT terminal.id FROM pax_terminal terminal
			WHERE
			terminal.del_flag = 0
			AND terminal.reseller_id IN
			<foreach collection="resellerIds" index="index" item="resellerId" open="(" separator="," close=")">
				#{resellerId}
			</foreach>
		)
		AND groupApk.group_id IN
		<foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
			#{groupId}
		</foreach>
	</select>

	<select id="findGroupTerminalActions" resultType="com.pax.market.domain.entity.market.terminal.TerminalAction" fetchSize="1000">
		SELECT
		terminalAction.id AS "id",
		terminalAction.terminal_id AS "terminalId",
		terminal.reseller_id AS "referenceId"
		FROM
		pax_terminal_action terminalAction
		JOIN pax_terminal terminal ON terminal.id = terminalAction.terminal_id
		JOIN pax_tm_group_apk_param groupApkParam ON terminalAction.action_type = ${@com.pax.market.constants.TerminalActionType@DOWNLOAD_GROUP_PARAM} AND terminalAction.reference_id = groupApkParam.id
		JOIN pax_tm_group_apk groupApk ON groupApkParam.group_apk_id = groupApk.id
		WHERE groupApk.group_id = #{groupId} AND terminal.del_flag = 0
	</select>

	<select id="findActivePushTaskIds" resultType="java.lang.Long" fetchSize="1000">
		SELECT id AS "id"
		FROM pax_tm_group_apk_param
		WHERE `status`='A'
		  AND del_flag=0
	</select>

	<select id="findAllPushTaskIds" resultType="java.lang.Long" fetchSize="1000">
		SELECT a.id
		FROM pax_tm_group_apk_param a
	</select>

	<select id="findSuccessTerminalApkParamPushHistoryList" resultType="com.pax.market.domain.entity.market.pushtask.TerminalLastApkParam">
		SELECT
			terminalAction.terminal_id AS "terminalId",
			groupApk.apk_id AS "apkId",
			groupApkParam.param_template_name AS "paramTemplateName",
			terminalAction.action_type AS "actionType",
			groupApkParam.id AS "referenceId",
			terminalAction.param_variables AS "paramVariables",
			terminalAction.action_time AS "downloadTime"
		FROM PAX_TERMINAL_ACTION_HISTORY terminalAction
		JOIN pax_tm_group_apk_param groupApkParam ON groupApkParam.id = terminalAction.reference_id
		JOIN pax_tm_group_apk groupApk ON groupApkParam.group_apk_id = groupApk.id
		WHERE terminalAction.action_type = ${@com.pax.market.constants.TerminalActionType@DOWNLOAD_GROUP_PARAM}
		AND terminalAction.terminal_id = #{terminalId}
		AND terminalAction.status = ${@com.pax.market.constants.TerminalActionStatus@SUCCEED}
		AND groupApk.type = '${@com.pax.market.constants.PushApkType@NORMAL}'
	</select>

	<update id="updateParamFileMap">
		UPDATE PAX_TM_GROUP_APK_PARAM
		SET param_files = #{paramFiles}, param_file_map = #{paramFileMap}
		WHERE id = #{id}
	</update>

	<select id="getParamDownloadUrl" resultType="java.lang.String">
		SELECT download_url
		FROM PAX_TM_GROUP_APK_PARAM
		WHERE id = #{id}
	</select>

	<select id="getLatestSuccessTerminalApkParamPushHistory" resultType="com.pax.market.domain.entity.market.pushtask.TerminalLastApkParam">
		SELECT
			groupApkParam.param AS "param",
			groupApkParam.file_size AS "fileSize",
			groupApkParam.download_url AS "downloadUrl",
			groupApkParam.md5 AS "md5",
			groupApkParam.sha256 AS "sha256",
			groupApkParam.param_template_name AS "paramTemplateName",
			terminalAction.action_type AS "actionType",
			groupApkParam.id AS "referenceId",
			terminalAction.action_time AS "downloadTime",
			groupApk.apk_id AS "apkId"
		FROM PAX_TERMINAL_ACTION_HISTORY terminalAction
		JOIN pax_tm_group_apk_param groupApkParam ON groupApkParam.id = terminalAction.reference_id
		JOIN pax_tm_group_apk groupApk ON groupApkParam.group_apk_id = groupApk.id
		WHERE terminalAction.action_type = ${@com.pax.market.constants.TerminalActionType@DOWNLOAD_GROUP_PARAM}
		AND terminalAction.terminal_id = #{terminalId}
		AND terminalAction.status = ${@com.pax.market.constants.TerminalActionStatus@SUCCEED}
		AND groupApk.type = '${@com.pax.market.constants.PushApkType@LAUNCHER}'
		ORDER BY terminalAction.action_time DESC
		LIMIT 1
	</select>

	<update id="updateParamFileSha256">
		UPDATE PAX_TM_GROUP_APK_PARAM
		SET sha256 = #{sha256}
		WHERE id = #{id}
	</update>

	<update id="updateParamTemplateId">
		UPDATE PAX_TM_GROUP_APK_PARAM
		SET param_template_id = #{paramTemplateId}
		WHERE id = #{id}
	</update>
</mapper>