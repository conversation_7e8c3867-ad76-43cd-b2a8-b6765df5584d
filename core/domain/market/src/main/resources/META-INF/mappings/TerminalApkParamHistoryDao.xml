<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ /*
  ~  * *******************************************************************************
  ~  * COPYRIGHT
  ~  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~  *   This software is supplied under the terms of a license agreement or
  ~  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~  *   or disclosed except in accordance with the terms in that agreement.
  ~  *
  ~  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~  * *******************************************************************************
  ~  */
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.terminal.TerminalApkParamHistoryDao">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO `pax_terminal_apk_param_history` (
            id,
            terminal_id,
            app_id,
            apk_id,
            param_template_name,
            action_type,
            reference_id,
            param_variables,
            converted_param_variables,
            local_param,
            `from`,
            updated_date
        ) VALUES (
             #{id},
             #{terminalId},
             #{appId},
             #{apkId},
             #{paramTemplateName},
             #{actionType},
             #{referenceId},
             #{paramVariables},
             #{convertedParamVariables},
             #{localParam},
             #{from},
             #{updatedDate}
         )
    </insert>

    <select id="getCountByTerminalAndApp" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pax_terminal_apk_param_history
        WHERE app_id = #{appId}
        AND terminal_id = #{terminalId}
    </select>

    <select id="getLatestTerminalApkParamHistory" resultType="com.pax.market.domain.entity.market.pushtask.TerminalApkParamHistory">
        SELECT a.id AS "id",
        a.apk_id AS "apkId",
        a.param_template_name AS "paramTemplateName",
        a.action_type AS "actionType",
        a.reference_id AS "referenceId",
        a.param_variables AS "paramVariables",
        a.converted_param_variables AS "convertedParamVariables",
        a.local_param AS "localParam",
        a.from AS "from",
        a.updated_date AS "updatedDate"
        FROM pax_terminal_apk_param_history a
        WHERE a.terminal_id = #{terminalId}
        AND a.app_id = #{appId}
        <if test="paramTemplateName != null and paramTemplateName != ''">
            AND a.param_template_name = #{paramTemplateName}
        </if>
        ORDER BY a.updated_date DESC
        LIMIT 1
    </select>

    <delete id="deleteTerminalApkParamHistory">
        DELETE FROM pax_terminal_apk_param_history
        WHERE id &lt;= (SELECT id FROM pax_terminal_apk_param_history ORDER BY id DESC LIMIT #{limit}, 1)
    </delete>
</mapper>