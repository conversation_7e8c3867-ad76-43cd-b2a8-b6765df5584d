<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.cleardata.TerminalAccessoryPhysicalDeleteDao">
    <select id="findDeletedTerminalAccessoryIds" resultType="java.lang.Long">
        SELECT id
        FROM pax_terminal_accessory
        WHERE terminal_id = #{terminalId}
    </select>

    <delete id="deleteTerminalAccessory">
        DELETE FROM pax_terminal_accessory
        WHERE id = #{id}
    </delete>

    <delete id="deleteTerminalAccessoryDetail">
        DELETE FROM pax_terminal_accessory_details
        WHERE accessory_id = #{accessoryId}
    </delete>

    <delete id="deleteTerminalAccessoryEvent">
        DELETE FROM pax_terminal_accessory_events
        WHERE accessory_id = #{accessoryId}
    </delete>

    <delete id="deleteTerminalAccessoryAction">
        DELETE FROM pax_terminal_accessory_action
        WHERE accessory_id = #{accessoryId}
    </delete>
</mapper>