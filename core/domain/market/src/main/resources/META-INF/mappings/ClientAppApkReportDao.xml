<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ /*
  ~  * *******************************************************************************
  ~  * COPYRIGHT
  ~  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~  *   This software is supplied under the terms of a license agreement or
  ~  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~  *   or disclosed except in accordance with the terms in that agreement.
  ~  *
  ~  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~  * *******************************************************************************
  ~  */
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.market.domain.dao.client.ClientAppApkReportDao">

    <select id="getClientModelTerminalReport" resultType="com.paxstore.market.domain.entity.client.ClientVersionMarketReport">
        SELECT c.client_version_code as "clientVersionCode",
               t.model_id as "modelId",
               COUNT(*) AS "terminalNum"
        FROM pax_terminal_channel_mapping c
        JOIN pax_terminal t ON c.terminal_id = t.id AND t.del_flag = 0
        <if test="marketId != null">
            And t.market_id = #{marketId}
        </if>
        <if test="resellerIds != null and resellerIds.size > 0">
            AND t.reseller_id in
            <foreach collection="resellerIds" open="(" close=")" separator="," item="resellerId">
                #{resellerId}
            </foreach>
        </if>
        WHERE c.client_version_code IS NOT NULL
        GROUP BY c.client_version_code, t.model_id
        ORDER BY terminalNum DESC
    </select>



    <select id="getClientResellerTerminalReport" resultType="com.paxstore.market.domain.entity.client.ClientVersionMarketReport">
        SELECT c.client_version_code as "clientVersionCode", t.reseller_id as "resellerId", COUNT(*) AS "terminalNum"
        FROM pax_terminal_channel_mapping c
        JOIN pax_terminal t ON c.terminal_id = t.id AND t.del_flag = 0
        AND t.market_id = #{marketId}
        <if test="resellerIds != null and resellerIds.size > 0">
            AND t.reseller_id in
            <foreach collection="resellerIds" item="resellerId" open="(" close=")" separator=",">
                #{resellerId}
            </foreach>
        </if>
        WHERE c.client_version_code IS NOT NULL
        GROUP BY c.client_version_code, t.reseller_id
        ORDER BY terminalNum DESC
    </select>


    <select id="getClientTerminalReport" resultType="com.paxstore.market.domain.entity.client.ClientVersionMarketReport">
        SELECT c.client_version_code as "clientVersionCode", COUNT(*) AS "terminalNum"
        FROM pax_terminal_channel_mapping c
        JOIN pax_terminal t ON c.terminal_id = t.id AND t.del_flag = 0
        <if test="marketId != null">
            And t.market_id = #{marketId}
        </if>
        <if test="resellerIds != null and resellerIds.size > 0">
            AND t.reseller_id in
            <foreach collection="resellerIds" open="(" close=")" separator="," item="resellerId">
                #{resellerId}
            </foreach>
        </if>
        WHERE c.client_version_code IS NOT NULL
        GROUP BY c.client_version_code
        ORDER BY terminalNum DESC
    </select>



    <select id="getMaxStoreClientVersions" resultType="com.pax.market.domain.entity.report.ApkVersionOfTerminalReport">
        SELECT DISTINCT
        tcp.client_version_code AS versionCode
        FROM
        `pax_terminal_channel_mapping` tcp
        <if test="marketId != null" >
            LEFT JOIN pax_terminal terminal ON terminal.id = tcp.terminal_id and terminal.del_flag = 0
        </if>
        WHERE tcp.client_version_code IS NOT NULL
        <if test="marketId != null">
            AND terminal.market_id = #{marketId}
            ${sqlMap.dsf}
        </if>
        ORDER BY versionCode
    </select>
</mapper>