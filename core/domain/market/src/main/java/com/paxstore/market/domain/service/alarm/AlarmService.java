/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain.service.alarm;

import com.pax.market.domain.entity.market.alarm.Alarm;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.dao.alarm.AlarmDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 告警Service
 *
 * <AUTHOR>
 * @date 2020/12/18
 */
@Service
@RequiredArgsConstructor
public class AlarmService extends CrudService<AlarmDao, Alarm> {


    /**
     * 获取单条数据
     *
     * @param id the id
     * @return t t
     */
    @Override
    public Alarm get(Long id) {
        return get(new Alarm(id));
    }

    /**
     * 获取单条数据
     *
     * @param alarm the alarm
     * @return alarm
     */
    @Override
    public Alarm get(Alarm alarm) {
        if (alarm == null) {
            return null;
        }
        return super.get(alarm);
    }



    /**
     * 保存告警配置信息
     *
     * @param alarm the alarm
     */
    @Override
    @MasterDs
    public void save(Alarm alarm) {
        Alarm oldAlarm = get(alarm);
        if (oldAlarm != null) {
            alarm.setId(oldAlarm.getId());
        }
        super.save(alarm);
    }

    /**
     * 清除mobile端配置信息
     * @param marketId 市场Id
     */
    @MasterDs
    public void clearMobileConfig(Long marketId){
        dao.clearMobileConfig(marketId);
    }

}
