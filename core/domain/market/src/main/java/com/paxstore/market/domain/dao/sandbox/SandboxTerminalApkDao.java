/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain.dao.sandbox;

import java.util.List;

import com.pax.market.domain.entity.market.sandbox.SandboxTerminalApkParam;
import org.apache.ibatis.annotations.Param;

import com.pax.market.domain.entity.market.sandbox.SandboxTerminalApk;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

/**
 * The interface Sandbox terminal apk dao.
 *
 * <AUTHOR>
 * @date 2018 /5/14
 */
@MyBatisDao
public interface SandboxTerminalApkDao extends CrudDao<SandboxTerminalApk> {

    /**
     * Find apk sandbox terminal apk list list.
     *
     * @param terminalApk the terminal apk
     * @return the list
     */
    List<SandboxTerminalApk> findApkSandboxTerminalApkList(SandboxTerminalApk terminalApk);


    /**
     * get apk sandbox terminal apk list count.
     *
     * @param terminalApk the terminal apk
     * @return the count
     */
    List<SandboxTerminalApk> getApkSandboxTerminalApk(SandboxTerminalApk terminalApk);


    /**
     * Delete by terminal id.
     *
     * @param sandboxTerminalId the terminal id
     */
    void deleteByTerminalId(@Param("sandboxTerminalId") Long sandboxTerminalId);


    /**
     * Gets none complete sandbox terminal apk.
     *
     * @param terminalApk the terminal apk
     * @return the none complete sandbox terminal apk
     */
    SandboxTerminalApk getNoneCompleteSandboxTerminalApk(SandboxTerminalApk terminalApk);

    /**
     * Gets include deleted.
     *
     * @param id the id
     * @return the include deleted
     */
    SandboxTerminalApk getIncludeDeleted(Long id);

    void updateParamTemplateId(SandboxTerminalApkParam terminalApkParam);
}
