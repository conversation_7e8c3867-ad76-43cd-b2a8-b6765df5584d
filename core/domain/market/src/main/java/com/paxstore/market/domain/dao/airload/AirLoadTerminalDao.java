package com.paxstore.market.domain.dao.airload;

import com.pax.market.constants.airload.AirLoadTerminalActiveTaskStatus;
import com.pax.market.constants.airload.AirLoadTerminalStatus;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal;
import com.pax.market.domain.entity.market.vas.airload.AirLoadTerminal;
import com.pax.market.domain.entity.market.vas.airload.AirLoadTerminalActiveTask;
import com.pax.market.dto.airload.AirLoadTerminalActiveTaskInfo;
import com.pax.market.dto.airload.AirLoadTerminalInfo;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/6 15:29
 */
@MyBatisDao
public interface AirLoadTerminalDao extends CrudDao<AirLoadTerminal> {

    void insertList(List<AirLoadTerminal> airLoadTerminalList);

    List<AirLoadTerminalInfo> findAirLoadTerminalList(@Param("page") Page<AirLoadTerminalInfo> page,
                                                      @Param("activeTaskId") Long activeTaskId,
                                                      @Param("serialNo") String sn,
                                                      @Param("iccid") String iccid,
                                                      @Param("status") AirLoadTerminalStatus status);

    int getTerminalCountForExport(@Param("activeTaskId") Long activeTaskId,@Param("limit") int limit);

    List<AirLoadTerminal> findTerminalListByActiveTaskId(@Param("activeTaskId") Long activeTaskId,@Param("limit") int limit);
}
