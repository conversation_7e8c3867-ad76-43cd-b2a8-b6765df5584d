/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain.dao.terminal;

import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * The interface Terminal action history dao.
 */
@MyBatisDao
public interface TerminalActionHistoryDao extends CrudDao<TerminalAction> {

    /**
     * Gets terminal action.
     *
     * @param terminalAction the terminal action
     * @return the terminal action
     */
    TerminalAction getTerminalActionHistory(TerminalAction terminalAction);

    /**
     * Gets latest terminal action history.
     *
     * @param terminalAction the terminal action
     * @return the latest terminal action history
     */
    TerminalAction getLatestTerminalActionHistory(TerminalAction terminalAction);

    /**
     * Find by terminal list.
     *
     * @param terminalId  the terminal id
     * @param actionTypes the action types
     * @param limit       the limit
     * @return the list
     */
    List<TerminalAction> findByTerminal(@Param("terminalId") Long terminalId, @Param("actionTypes") List<Integer> actionTypes, @Param("limit") int limit);

    /**
     * Create terminal action.
     *
     * @param terminalAction the terminal action
     */
    void createTerminalActionHistory(TerminalAction terminalAction);

    /**
     * Update terminal action history.
     *
     * @param terminalAction the terminal action
     */
    void updateTerminalActionHistory(TerminalAction terminalAction);


    /**
     * Update terminal action history status.
     *
     * @param id     the id
     * @param status the status
     */
    void updateTerminalActionHistoryStatus(@Param("id") Long id, @Param("status") int status);

    /**
     * Create terminal action histories.
     *
     * @param terminalActionList the terminal action list
     */
    void createTerminalActionHistories(@Param("terminalActionList") List<TerminalAction> terminalActionList);

    /**
     * Delete terminal action histories.
     *
     * @param terminalActionList the terminal action list
     */
    void deleteTerminalActionHistories(@Param("terminalActionList") List<TerminalAction> terminalActionList);

    /**
     * Find push task error code list list.
     *
     * @param actionType  the action type
     * @param referenceId the reference id
     * @return the list
     */
    List<Integer> findPushTaskErrorCodeList(@Param("actionType") Integer actionType, @Param("referenceId") Long referenceId);

    /**
     * Create terminal action history backups.
     *
     * @param terminalActionList the terminal action list
     */
    void createTerminalActionHistoryBackups(@Param("terminalActionList") List<TerminalAction> terminalActionList);

    /**
     * Find action history ids list.
     *
     * @param terminalAction the terminal action
     * @param handler        the handler
     */
    void findActionHistoryIds(TerminalAction terminalAction, ResultHandler<Long> handler);

    /**
     * Gets action history ids count.
     *
     * @param actionType  the action type
     * @param referenceId the reference id
     * @param status      the status
     * @param errorCode   the error code
     * @return the action history ids count
     */
    Integer getActionHistoryIdsCount(@Param("actionType") int actionType,
                                      @Param("referenceId") Long referenceId,
                                      @Param("status") int status,
                                      @Param("errorCode") int errorCode);
}
