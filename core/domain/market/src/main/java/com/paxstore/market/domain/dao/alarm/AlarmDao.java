package com.paxstore.market.domain.dao.alarm;

import com.pax.market.domain.entity.market.alarm.Alarm;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;


/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@MyBatisDao
public interface AlarmDao extends CrudDao<Alarm> {

    /**
     * get id by alarm
     * @param alarm  alarm
     * @return alarm id
     */
    Long getAlarmId(Alarm alarm);

    void clearMobileConfig(Long marketId);

}
