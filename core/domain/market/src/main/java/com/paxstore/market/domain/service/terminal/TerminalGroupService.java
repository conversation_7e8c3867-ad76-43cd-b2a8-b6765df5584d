/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.paxstore.market.domain.service.terminal;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pax.api.cache.CacheService;
import com.pax.api.eventbus.EventOperator;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.converter.ConverterUtils;
import com.pax.market.domain.entity.global.model.Model;
import com.pax.market.domain.entity.global.terminal.BaseTerminalEntity;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalGroup;
import com.pax.market.domain.event.TerminalGroupChangedEvent;
import com.pax.market.domain.util.BizLimitCacheHelper;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.BaseEntity;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.persistence.TreeEntity;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.DataScopeFilter;
import com.paxstore.market.domain.dao.terminal.TerminalGroupDao;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.service.pushtask.PushTaskUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 终端分组Service
 *
 * <AUTHOR>
 * @version 2016 -09-08
 */
@Service
public class TerminalGroupService extends CrudService<TerminalGroupDao, TerminalGroup> {
    private static final String TERMINAL_GROUP_CACHE_ID_ = "id_";
    private static final String TERMINAL_GROUP_CACHE_NAME_ = "name_";
    @Autowired
    private CacheService cacheService;
    @Autowired
    private MarketTerminalService marketTerminalService;
    @Autowired
    private ResellerService resellerService;
    @Autowired
    private MerchantService merchantService;


    /**
     * Clear terminal group cache.
     *
     * @param terminalGroup the terminal group
     */
    public static void clearTerminalGroupCache(TerminalGroup terminalGroup) {
        CacheService cacheService = SpringContextHolder.getApplicationContext().getBean(CacheService.class);

        cacheService.remove(CacheNames.TERMINAL_GROUP_CACHE,
                TERMINAL_GROUP_CACHE_ID_ + terminalGroup.getId());
        if (terminalGroup.getReseller() != null) {
            cacheService.remove(CacheNames.TERMINAL_GROUP_CACHE,
                    TERMINAL_GROUP_CACHE_NAME_ + terminalGroup.getCreatedByResellerId() + StringUtils.upperCase(terminalGroup.getName()));
        }
    }

    /**
     * Load details.
     *
     * @param terminalGroup the terminal group
     */
    @MasterDs
    public void loadDetails(TerminalGroup terminalGroup) {
        if (terminalGroup == null) {
            return;
        }
        TerminalGroup terminalGroupDetail = getIncludeDeleted(terminalGroup.getId());
        if (terminalGroupDetail != null) {
            terminalGroup.setMarketId(terminalGroupDetail.getMarketId());
            terminalGroup.setName(terminalGroupDetail.getName());
            terminalGroup.setStatus(terminalGroupDetail.getStatus());
            terminalGroup.setReseller(terminalGroupDetail.getReseller());
            terminalGroup.setModel(terminalGroupDetail.getModel());
            terminalGroup.setDynamic(terminalGroupDetail.getDynamic());
            terminalGroup.setCreatedByResellerId(terminalGroupDetail.getCreatedByResellerId());
            terminalGroup.setContainSubResellerTerminal(terminalGroupDetail.getContainSubResellerTerminal());
        }
    }

    @MasterDs
    public TerminalGroup get(Long id) {
        return get(new TerminalGroup(id));
    }

    @MasterDs
    public TerminalGroup get(TerminalGroup group) {
        TerminalGroup result = getIncludeDeleted(group.getId());
        if (result == null || result.isDeleted()) {
            return null;
        }
        if (!checkGroupCreateByReseller(result.getCreatedByResellerId())) {
            return null;
        }
        return result;
    }


    /**
     * Get include deleted terminal group.
     *
     * @param terminalGroupId the terminal group id
     * @return the terminal group
     */

    public TerminalGroup getIncludeDeleted(Long terminalGroupId) {
        if (LongUtils.isBlankOrNotPositive(terminalGroupId)) return null;
        TerminalGroup result = (TerminalGroup) cacheService.get(CacheNames.TERMINAL_GROUP_CACHE, TERMINAL_GROUP_CACHE_ID_ + terminalGroupId);
        if (result == null) {
            result = dao.get(new TerminalGroup(terminalGroupId));
            if (result == null) {
                return null;
            }

            cacheService.put(CacheNames.TERMINAL_GROUP_CACHE, TERMINAL_GROUP_CACHE_ID_ + terminalGroupId, result);
        }

        resellerService.loadDetails(result.getReseller());
        return result;
    }

    /**
     * Get reseller id by TerminalGroup id including deleted one
     *
     * @param id the id
     * @return reseller id
     */

    public Long getResellerId(Long id) {
        return dao.getResellerId(id);
    }


    public List<TerminalGroup> findList(TerminalGroup group) {
        return super.findList(group);
    }


    public List<TerminalGroup> findAllList(TerminalGroup group) {
        return dao.findAllList(group);
    }

    /**
     * Create group terminal group.
     *
     * @param group the group create request
     * @return the terminal group
     */
    @Transactional
    public TerminalGroup createGroup(TerminalGroup group) {
        //判断是否超限
        BizLimitCacheHelper.checkBizLimit(this, group, 1);
        save(group);
        //更新缓存
        BizLimitCacheHelper.updateBizCount(group, 1);
        return group;
    }

    @Transactional
    public void save(TerminalGroup group) {
        TerminalGroup originalGroup = get(group);
        if (originalGroup != null) {
            fireEvent(new TerminalGroupChangedEvent(originalGroup, EventOperator.UPDATED));
        }
        boolean isNewRecord = group.getIsNewRecord();
        if (isNewRecord) {
            if (getCurrentUser() != null) {
                group.setCreatedByResellerId(getCurrentResellerId());
            } else {
                group.setCreatedByResellerId(getCurrentThirdPartySys().getReseller().getId());
            }
        } else if (TerminalGroupStatus.PENDING.equals(group.getStatus())) {
            dao.deleteGroupMerchants(group.getId());
        }
        super.save(group);
        if (StringUtils.isNotBlank(group.getMerchantIds()) && BooleanUtils.isFalse(group.getContainSubResellerTerminal())) {
            dao.createGroupMerchants(group.getId(), StringUtils.splitToLongSet(group.getMerchantIds(), ","));
        }
        fireEvent(new TerminalGroupChangedEvent(group, EventOperator.UPDATED));
    }

    @MasterDs
    public void delete(TerminalGroup group) {
        if (TerminalGroupStatus.ACTIVE.equals(group.getStatus())) {
            throw new BusinessException(ApiCodes.TERMINAL_GROUP_ACTIVE_NOT_DELETE);
        }
        TerminalActionType.GROUP_ACTION_TYPES.forEach(actionType -> PushTaskUtils.getGroupPushTaskService(actionType).deleteByGroupId(group.getId()));
        super.delete(group);
        fireEvent(new TerminalGroupChangedEvent(group, EventOperator.DELETED));

        //更新缓存
        BizLimitCacheHelper.updateBizCount(group, -1);
    }


    /**
     * Gets by name.
     *
     * @param name the name
     * @return the by name
     */

    public TerminalGroup getByName(String name) {
        if (getCurrentUser() != null) {
            return getByName(name, getCurrentResellerId());
        } else {
            return getByName(name, getCurrentThirdPartySys().getReseller().getId());
        }

    }

    /**
     * Gets by name.
     *
     * @param name       the name
     * @param resellerId the reseller id
     * @return the by name
     */

    public TerminalGroup getByName(String name, Long resellerId) {
        TerminalGroup result = (TerminalGroup) cacheService.get(CacheNames.TERMINAL_GROUP_CACHE, TERMINAL_GROUP_CACHE_NAME_ + resellerId + StringUtils.upperCase(name));
        if (result == null) {
            TerminalGroup group = new TerminalGroup();
            group.setName(name);
            group.setReseller(new Reseller(resellerId));
            result = dao.getByName(group);
            if (result == null) {
                return null;
            }

            cacheService.put(CacheNames.TERMINAL_GROUP_CACHE, TERMINAL_GROUP_CACHE_NAME_ + resellerId + StringUtils.upperCase(name), result);
        }
        return result;
    }

    /**
     * Update status.
     *
     * @param group  the group
     * @param status the status
     */
    @MasterDs
    public void updateStatus(TerminalGroup group, String status) {
        if (TerminalGroupStatus.SUSPEND.equals(status) && !TerminalGroupStatus.ACTIVE.equals(group.getStatus())) {
            throw new BusinessException(ApiCodes.TERMINAL_GROUP_NOT_ACTIVE);
        }
        if (TerminalGroupStatus.ACTIVE.equals(status) && TerminalGroupStatus.ACTIVE.equals(group.getStatus())) {
            throw new BusinessException(ApiCodes.TERMINAL_GROUP_ACTIVE);
        }

        group.setStatus(status);
        dao.updateStatus(group);
        fireEvent(new TerminalGroupChangedEvent(group, EventOperator.UPDATED));
    }

    /**
     * Create group terminals.
     *
     * @param group       the group
     * @param terminalIds the terminal ids
     */
    @MasterDs
    public void createGroupTerminals(TerminalGroup group, Set<Long> terminalIds) {
        if (terminalIds == null || terminalIds.isEmpty()) {
            return;
        }

        checkGroupTerminalLimit(group, terminalIds.size());

        List<Long> terminalIdsToSave = Lists.newArrayList();

        for (Long terminalId : terminalIds) {
            Terminal terminal = marketTerminalService.get(terminalId);
            validateGroupTerminalWithoutDuplication(group, terminal);

            if (getGroupTerminal(group.getId(), terminal.getId()) == null) {
                terminalIdsToSave.add(terminalId);
            }
        }

        if (CollectionUtils.isNotEmpty(terminalIdsToSave)) {
            createGroupTerminalsWithoutValidate(group, terminalIdsToSave);
        }
    }

    /**
     * Create group terminal.
     *
     * @param group    the group
     * @param terminal the terminal
     */
    @MasterDs
    public void createGroupTerminal(TerminalGroup group, Terminal terminal) {
        checkGroupTerminalLimit(group, 1);
        validateGroupTerminal(group, terminal);
        createGroupTerminalsWithoutValidate(group, Collections.singletonList(terminal.getId()));
    }

    /**
     * validate import group terminal
     *
     * @param group    the group
     * @param terminal the terminal
     */

    public void validateGroupTerminalWithoutDuplication(TerminalGroup group, BaseTerminalEntity<?> terminal) {
        if (terminal == null || !LongUtils.equals(terminal.getMarketId(), getCurrentMarketId())) {
            throw new BusinessException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        if (!StringUtils.equals(TerminalStatus.ACTIVE, terminal.getStatus())) {
            throw new BusinessException(ApiCodes.TERMINAL_NOT_ACTIVE);
        }
        Reseller reseller = resellerService.get(terminal.getReseller());
        if (!checkDataScope(reseller, BeanMapper.map(group.getReseller(), ResellerInfo.class))) {
            throw new BusinessException(ApiCodes.TERMINAL_RESELLER_MISMATCH);
        }
        if (!LongUtils.equals(terminal.getModelId(), group.getModel().getId())) {
            throw new BusinessException(ApiCodes.TERMINAL_MODEL_MISMATCH);
        }
    }

    /**
     * Validate group terminal.
     *
     * @param group    the group
     * @param terminal the terminal
     */

    public void validateGroupTerminal(TerminalGroup group, Terminal terminal) {
        validateGroupTerminalWithoutDuplication(group, terminal);

        if (getGroupTerminal(group.getId(), terminal.getId()) != null) {
            throw new BusinessException(ApiCodes.GROUP_TERMINAL_ALREADY_EXIST);
        }
    }

    /**
     * Create group terminals directly.
     *
     * @param group       the group
     * @param terminalIds the terminal ids
     */
    @MasterDs
    public void createGroupTerminalsWithoutValidate(TerminalGroup group, List<Long> terminalIds) {
        if (BooleanUtils.isFalse(group.getDynamic())) {
            dao.createGroupTerminals(group.getId(), terminalIds);
        }
        //如果是动态分组，市场不支持动态分组，那么不创建分组任务
        if (BooleanUtils.isFalse(group.getDynamic()) || BooleanUtils.isTrue(getCurrentMarket().getAllowDynamicGroup())) {
            TerminalActionType.GROUP_ACTION_TYPES.forEach(actionType -> PushTaskUtils.getGroupPushTaskService(actionType).createPendingTerminalActions(group, terminalIds));
        }
        addGroupTerminalCount(group.getId(), terminalIds.size());
        fireEvent(new TerminalGroupChangedEvent(group, EventOperator.UPDATED));
    }

    /**
     * Delete group terminals.
     *
     * @param group       the group
     * @param terminalIds the terminal ids
     */
    @MasterDs
    public void deleteGroupTerminalsAndPushTasks(TerminalGroup group, Set<Long> terminalIds, boolean isModelChanged) {
        if (CollectionUtils.isEmpty(terminalIds)) {
            return;
        }
        if (BooleanUtils.isFalse(group.getDynamic())) {
            dao.deleteGroupTerminals(group.getId(), terminalIds);
        }
        final int errorCode = isModelChanged ? TerminalActionErrorCode.TERMINAL_MODEL_CHANGED : TerminalActionErrorCode.TERMINAL_REMOVED_FROM_GROUP;
        TerminalActionType.GROUP_ACTION_TYPES.forEach(actionType -> PushTaskUtils.getGroupPushTaskService(actionType).movePendingTerminalActionsToHistory(group, terminalIds, errorCode));
        addGroupTerminalCount(group.getId(), -terminalIds.size());
        fireEvent(new TerminalGroupChangedEvent(group, EventOperator.UPDATED));
    }

    /**
     * 根据创建代理商查找分组
     *
     * @param resellerIds the reseller ids
     * @return the list
     */

    public List<TerminalGroup> findGroupsCreatedByResellers(List<Long> resellerIds) {
        return dao.findGroupsCreatedByResellers(resellerIds);
    }

    /**
     * Gets group terminal.
     *
     * @param groupId    the group id
     * @param terminalId the terminal id
     * @return the group terminal
     */

    public Terminal getGroupTerminal(Long groupId, Long terminalId) {
        TerminalGroup terminalGroup = get(groupId);
        if (terminalGroup == null || isGroupResellerUnavailable(terminalGroup)) {
            return null;
        }
        Terminal terminal = new Terminal();
        terminal.setId(terminalId);
        terminal.setReferenceId(groupId);
        terminal.setMarketId(terminalGroup.getMarketId());
        if (BooleanUtils.toBoolean(terminalGroup.getDynamic())) {
            setDynamicGroupTerminalCondition(terminalGroup, terminal);
            return dao.getDynamicGroupTerminal(terminal);
        } else {
            terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminalGroup.getReseller()));
            return dao.getGroupTerminal(terminal);
        }
    }

    /**
     * Is group reseller unavailable boolean.
     *
     * @param terminalGroup the terminal group
     * @return the boolean
     */
    /*
     * 分组代理商是否可用，createdByReseller是否对reseller可见，代理商移动以后可能是不可见的
     */
    public boolean isGroupResellerUnavailable(TerminalGroup terminalGroup) {
        Reseller reseller = resellerService.get(terminalGroup.getReseller());
        if (reseller == null) {
            return true;
        }
        Reseller createdByReseller = resellerService.get(terminalGroup.getCreatedByResellerId());
        if (createdByReseller == null) {
            return true;
        }
        String resellerChain = reseller.getParentIds() + reseller.getId() + TreeEntity.PARENT_IDS_DELEMETER;
        String createdByResellerChain = createdByReseller.getParentIds() + createdByReseller.getId() + TreeEntity.PARENT_IDS_DELEMETER;
        return !resellerChain.contains(createdByResellerChain);
    }

    /**
     * Find group terminal page page.
     *
     * @param page     the page
     * @param terminal the terminal
     * @return the page
     */

    public Page<Terminal> findGroupTerminalPage(Page<Terminal> page, Terminal terminal) {
        if (StringUtils.containsIgnoreCase(page.getOrderBy(), "serialNo")) {
            page.setOrderBy(StringUtils.replaceIgnoreCase(page.getOrderBy(), "serialNo", "serial_no"));
        }
        page.setCount(getGroupTerminalCount(terminal));
        if (page.getCount() > 0) {
            terminal.setPage(page);
            List<Terminal> terminalList = findGroupTerminalList(terminal);
            page.setList(terminalList);
        }

        return page;
    }

    private List<Terminal> findGroupTerminalList(Terminal terminal) {
        TerminalGroup terminalGroup = get(terminal.getReferenceId());
        if (terminalGroup == null) {
            throw new BusinessException(ApiCodes.TERMINAL_GROUP_NOT_FOUND);
        }
        terminal.setMarketId(terminalGroup.getMarketId());
        if (BooleanUtils.isTrue(terminalGroup.getDynamic())) {
            setDynamicGroupTerminalCondition(terminalGroup, terminal);
            return dao.findDynamicGroupTerminalList(terminal);
        } else {
            terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminalGroup.getReseller()));
            return dao.findGroupTerminalList(terminal);
        }
    }

    /**
     * Find group terminal ids for push task list.
     *
     * @param terminalGroup the terminal group
     * @return the list
     */

    public List<Long> findGroupTerminalIdsForPushTask(TerminalGroup terminalGroup) {
        if (isGroupResellerUnavailable(terminalGroup)) {
            return Lists.newArrayList();
        }
        Terminal terminal = new Terminal();
        terminal.setReferenceId(terminalGroup.getId());
        terminal.setMarketId(terminalGroup.getMarketId());
        if (BooleanUtils.isTrue(terminalGroup.getDynamic())) {
            setDynamicGroupTerminalCondition(terminalGroup, terminal);
            return marketTerminalService.findDynamicGroupTerminalIdsForPushTask(terminal);
        } else {
            terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminalGroup.getReseller()));
            return marketTerminalService.findGroupTerminalIdsForPushTask(terminal);
        }
    }


    /**
     * Gets group terminal count.
     *
     * @param groupId the group id
     * @return the group terminal count
     */

    @MasterDs
    public int getGroupTerminalCount(Long groupId) {
        String cacheKey = String.format("%s:%s", CacheNames.GROUP_TERMINAL_COUNT_CACHE, groupId);
        Long result = RedisUtils.getCounter(cacheKey);
        if (result == null) {
            Terminal terminal = new Terminal();
            terminal.setReferenceId(groupId);
            result = RedisUtils.incrBy(cacheKey, getGroupTerminalCount(terminal));
        }
        return result.intValue();
    }

    /**
     * Add group terminal count.
     *
     * @param groupId the group id
     * @param value   the value
     */
    public void addGroupTerminalCount(Long groupId, int value) {
        String cacheKey = String.format("%s:%s", CacheNames.GROUP_TERMINAL_COUNT_CACHE, groupId);
        Long count = RedisUtils.getCounter(cacheKey);
        if (count == null || value == 0) {
            return;
        }
        if (value > 0) {
            RedisUtils.incrBy(cacheKey, value);
        } else if (count + value > 0) {
            RedisUtils.decrBy(cacheKey, -value);
        } else {
            RedisUtils.decrBy(cacheKey, count);
        }
    }

    /**
     * Clear group terminal count.
     *
     * @param groupId the group id
     */
    @MasterDs
    public void resetGroupTerminalCount(Long groupId) {
        String cacheKey = String.format("%s:%s", CacheNames.GROUP_TERMINAL_COUNT_CACHE, groupId);
        Long count = RedisUtils.getCounter(cacheKey);
        if (count == null) {
            return;
        }
        Terminal terminal = new Terminal();
        terminal.setReferenceId(groupId);
        int newCount = getGroupTerminalCount(terminal);
        addGroupTerminalCount(groupId, newCount - count.intValue());
    }

    /**
     * Gets group terminal count.
     *
     * @param terminal the terminal
     * @return the group terminal count
     */

    public int getGroupTerminalCount(Terminal terminal) {
        TerminalGroup terminalGroup = get(terminal.getReferenceId());
        if (terminalGroup == null) {
            throw new BusinessException(ApiCodes.TERMINAL_GROUP_NOT_FOUND);
        }
        if (isGroupResellerUnavailable(terminalGroup)) {
            return 0;
        }
        terminal.setMarketId(terminalGroup.getMarketId());
        if (BooleanUtils.isTrue(terminalGroup.getDynamic())) {
            setDynamicGroupTerminalCondition(terminalGroup, terminal);
            return dao.getDynamicGroupTerminalCount(terminal);
        } else {
            terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminalGroup.getReseller()));
            return dao.getGroupTerminalCount(terminal);
        }
    }

    private void setDynamicGroupTerminalCondition(TerminalGroup terminalGroup, Terminal terminal) {
        if (BooleanUtils.isTrue(terminalGroup.getDynamic())) {
            if (BooleanUtils.isTrue(terminalGroup.getContainSubResellerTerminal())) {
                terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminalGroup.getReseller()));
            } else {
                terminal.getSqlMap().remove("dsf");
                terminal.setReseller(terminalGroup.getReseller());
                List<Merchant> merchantList = merchantService.findDynamicGroupMerchantList(terminalGroup.getId());
                if (CollectionUtils.isNotEmpty(merchantList)) {
                    if (terminal.getMerchantIdsFilter() == null) {
                        terminal.setMerchantIdsFilter(Sets.newHashSet());
                    }
                    terminal.getMerchantIdsFilter().addAll(merchantList.stream().map(BaseEntity::getId).collect(Collectors.toSet()));
                }
            }
            terminal.setModel(new Model(terminalGroup.getModel().getId()));
        }
    }

    /*
        判断分组是否可能包含传入代理商以及其子代理商的终
    */
    public boolean willGroupContainsResellerTerminals(TerminalGroup group, String resellerChain, boolean isMoveReseller) {
        String groupResellerChain = group.getReseller().getParentIds() + group.getReseller().getId() + TreeEntity.PARENT_IDS_DELEMETER;
        // 如果分组代理商的链路等于代理商的链路，说明分组是基于代理商的，分组肯定符合条件
        if (groupResellerChain.equals(resellerChain)) {
            return true;
        }
        // 如果分组代理商的链路包含代理商的链路，说明分组是子代理商的，如果移动的是代理商那么肯定符合条件，如果移动的是商户，那么不符合条件
        if (groupResellerChain.contains(resellerChain)) {
            return isMoveReseller;
        }
        // 如果代理商的链路包含分组代理商的链路，说明分组是基于代理商父代理商的，那么不包含子代理商的分组就不满足条件
        if (resellerChain.contains(groupResellerChain)) {
            return BooleanUtils.isFalse(group.getDynamic())
                    || BooleanUtils.isTrue(group.getContainSubResellerTerminal());
        }
        // 其他说明分组代理商和代理商不在一条链路上，不存在包含终端的可能性
        return false;
    }


    /**
     * Find all terminal group list list.
     *
     * @param terminal the terminal
     * @return the list
     */

    public List<TerminalGroup> findAllTerminalGroupList(Terminal terminal) {
        if (terminal == null) {
            return Lists.newArrayList();
        }
        terminal.setTerminalResellerIds(getTerminalResellerIds(terminal));
        terminal.setDynamicGroup(null);
        return dao.findTerminalGroupList(terminal);
    }

    /**
     * Find dynamic terminal group list list.
     *
     * @param terminal the terminal
     * @return the list
     */

    public List<TerminalGroup> findDynamicTerminalGroupList(Terminal terminal) {
        if (terminal == null) {
            return Lists.newArrayList();
        }
        terminal.setTerminalResellerIds(getTerminalResellerIds(terminal));
        terminal.setDynamicGroup(true);
        return dao.findTerminalGroupList(terminal);
    }

    /**
     * Find standard terminal group list list.
     *
     * @param terminal the terminal
     * @return the list
     */

    public List<TerminalGroup> findStandardTerminalGroupList(Terminal terminal) {
        if (terminal == null) {
            return Lists.newArrayList();
        }
        terminal.setTerminalResellerIds(getTerminalResellerIds(terminal));
        terminal.setDynamicGroup(false);
        return dao.findTerminalGroupList(terminal);
    }

    /**
     * Delete by terminal id.
     *
     * @param terminal the terminal
     */
    @MasterDs
    public void deleteByTerminal(Terminal terminal) {
        terminal.setTerminalResellerIds(getTerminalResellerIds(terminal));
        terminal.setMoveTerminal(true);//删除终端不需要检查分组的创建者
        List<TerminalGroup> terminalGroupList = dao.findTerminalGroupList(terminal);
        for (TerminalGroup group : terminalGroupList) {
            deleteGroupTerminalsAndPushTasks(group, Sets.newHashSet(terminal.getId()), false);
        }
    }

    /**
     * Create dynamic group terminals.
     *
     * @param terminalList the terminal list
     */
    @Transactional
    public void createDynamicGroupTerminals(List<Terminal> terminalList) {
        TerminalGroup group = new TerminalGroup();
        group.setDynamic(true);
        List<TerminalGroup> dynamicGroupList = dao.findAllList(group);
        if (Collections3.isEmpty(dynamicGroupList)) {
            return;
        }
        Map<TerminalGroup, List<Long>> groupTerminalMap = new HashMap<>();
        for (Terminal terminal : terminalList) {
            if (!StringUtils.equals(terminal.getStatus(), TerminalStatus.ACTIVE) || terminal.isIgnoreDynamicGroupCheck()) {
                continue;
            }
            for (TerminalGroup dynamicGroup : dynamicGroupList) {
                if (!LongUtils.equals(terminal.getModelId(), dynamicGroup.getModel().getId())) {
                    continue;
                }
                if (!LongUtils.equals(terminal.getMarketId(), dynamicGroup.getMarketId())) {
                    continue;
                }
                if (BooleanUtils.isTrue(dynamicGroup.getContainSubResellerTerminal())) {
                    if (!checkDataScope(terminal.getReseller(), BeanMapper.map(dynamicGroup.getReseller(), ResellerInfo.class))) {
                        continue;
                    }
                } else {
                    if (!LongUtils.equals(terminal.getResellerId(), dynamicGroup.getResellerId())) {
                        continue;
                    }
                    List<Merchant> merchantList = merchantService.findDynamicGroupMerchantList(dynamicGroup.getId());
                    if (CollectionUtils.isNotEmpty(merchantList) && !merchantList.contains(terminal.getMerchant())) {
                        continue;
                    }
                }
                if (!groupTerminalMap.containsKey(dynamicGroup)) {
                    groupTerminalMap.put(dynamicGroup, Lists.newArrayList());
                }
                groupTerminalMap.get(dynamicGroup).add(terminal.getId());
            }
        }

        for (TerminalGroup dynamicGroup : groupTerminalMap.keySet()) {
            createGroupTerminalsWithoutValidate(dynamicGroup, groupTerminalMap.get(dynamicGroup));
        }
    }

    /**
     * Create dynamic group terminal.
     *
     * @param terminal the terminal
     */
    @Transactional
    public void createDynamicGroupTerminal(Terminal terminal) {
        if (!TerminalStatus.ACTIVE.equals(terminal.getStatus()) || terminal.isIgnoreDynamicGroupCheck()) {
            return;
        }
        TerminalGroup group = new TerminalGroup();
        group.setModel(terminal.getModel());
        group.setDynamic(true);
        List<TerminalGroup> dynamicGroupList = dao.findAllList(group);
        if (Collections3.isEmpty(dynamicGroupList)) {
            return;
        }
        for (TerminalGroup dynamicGroup : dynamicGroupList) {
            if (BooleanUtils.isTrue(dynamicGroup.getContainSubResellerTerminal())) {
                if (!checkDataScope(terminal.getReseller(), BeanMapper.map(dynamicGroup.getReseller(), ResellerInfo.class))) {
                    continue;
                }
            } else {
                if (!LongUtils.equals(terminal.getReseller().getId(), dynamicGroup.getResellerId())) {
                    continue;
                }
                List<Merchant> merchantList = merchantService.findDynamicGroupMerchantList(dynamicGroup.getId());
                if (CollectionUtils.isNotEmpty(merchantList) && !merchantList.contains(terminal.getMerchant())) {
                    continue;
                }

            }
            createGroupTerminalsWithoutValidate(dynamicGroup, Collections.singletonList(terminal.getId()));
        }
    }

    /**
     * Check group terminal limti.
     *
     * @param group the group
     * @param size  the size
     */

    public void checkGroupTerminalLimit(TerminalGroup group, int size) {
        if (!BooleanUtils.toBoolean(group.getDynamic()) && (dao.getGroupTerminalTotalCount(group.getId()) + size) > SystemPropertyHelper.getGroupMaxTerminalCount()) {
            throw new BusinessException(ApiCodes.GROUP_TERMINAL_COUNT_LIMIT_EXCEEDED);
        }
    }

    /**
     * Check dynamic group merchant changed boolean.
     *
     * @param terminalGroup the terminal group
     * @param merchantIds   the merchant ids
     * @return the boolean
     */

    public boolean checkDynamicGroupMerchantChanged(TerminalGroup terminalGroup, String merchantIds) {
        if (BooleanUtils.isFalse(terminalGroup.getDynamic()) ||
                (StringUtils.isBlank(merchantIds) && !StringUtils.equals(TerminalGroupStatus.PENDING, terminalGroup.getStatus()))) {
            return false;
        }

        setMerchantList(terminalGroup, merchantIds);
        List<Merchant> merchantList = merchantService.findDynamicGroupMerchantList(terminalGroup.getId());
        return !CollectionUtils.isEqualCollection(terminalGroup.getMerchants(), merchantList);
    }

    /**
     * Sets merchant list.
     *
     * @param terminalGroup the terminal group
     * @param merchantIds   the merchant ids
     */
    public void setMerchantList(TerminalGroup terminalGroup, String merchantIds) {
        List<Merchant> merchantList = new ArrayList<>();
        if (StringUtils.isNotBlank(merchantIds)) {
            String[] merchantIdArray = merchantIds.split(",");
            for (String merchantId : merchantIdArray) {
                if (StringUtils.isNotBlank(merchantId)) {
                    merchantList.add(new Merchant(ConverterUtils.parseLong(merchantId, -1L)));
                }
            }
        }
        terminalGroup.setMerchants(merchantList);
    }

    /**
     * Find available standard group ids string.
     *
     * @param terminal the terminal
     * @param reseller the reseller
     * @return the string
     */

    public String findAvailableStandardGroupIds(Terminal terminal, Reseller reseller) {
        List<Long> availableGroupResellerIds = StringUtils.splitToLongList((reseller.getParentIds() + reseller.getId()).substring(1), ",");
        terminal.setMoveTerminal(true);
        List<TerminalGroup> terminalGroupList = findStandardTerminalGroupList(terminal);
        List<Long> availableGroupIds = new ArrayList<>();
        for (TerminalGroup terminalGroup : terminalGroupList) {
            if (availableGroupResellerIds.contains(terminalGroup.getReseller().getId())) {
                availableGroupIds.add(terminalGroup.getId());
            }
        }
        if (availableGroupIds.isEmpty()) {
            return null;
        }
        return StringUtils.join(availableGroupIds, ",");
    }


    /**
     * Find group names by terminal id list.
     *
     * @param terminalId the terminal id
     * @return the list
     */

    public List<String> findGroupNamesByTerminalId(Long terminalId) {
        return dao.findGroupNamesByTerminalId(terminalId);
    }

    /**
     * Is terminal group exist by reseller boolean.
     *
     * @param resellerId the reseller id
     * @param status     the status
     * @return the boolean
     */

    public boolean isTerminalGroupExist(Long resellerId, String status) {
        Integer count = dao.getTerminalGroupCount(resellerId, status);
        return count > 0;
    }

    /**
     * Is terminal group exist by merchant boolean.
     *
     * @param merchantId the merchant id
     * @param status     the status
     * @return the boolean
     */

    public List<TerminalGroup> findTerminalDynamicGroupListByMerchantId(Long merchantId, String status) {
        return dao.findTerminalDynamicGroupList(merchantId, status);
    }

    /**
     * Sets terminal reseller ids.
     *
     * @param terminal the terminal
     * @return the terminal reseller ids
     */
    private Set<Long> getTerminalResellerIds(Terminal terminal) {
        if (terminal == null) {
            throw new BusinessException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        if (terminal.getReseller() == null) {
            throw new BusinessException(ApiCodes.RESELLER_NOT_EXIST);
        }
        resellerService.loadDetails(terminal.getReseller());
        return StringUtils.splitToLongSet((terminal.getReseller().getParentIds() + terminal.getReseller().getId()).substring(1), ",");
    }


    /**
     * 更新终端或者移动终端会调用
     * 通过第三方API或则终端API更新终端的不做处理，getCurrentUser()
     * 批量移动终端并且没有更换代理商的不做处理，isIgnoreStandardGroupCheck()
     */
    @Transactional
    public void updateStandardTerminalGroup(Terminal originalTerminal, Terminal terminal) {
        if (StringUtils.equals(terminal.getStatus(), TerminalStatus.PENDING) || terminal.isIgnoreStandardGroupCheck()) {
            return;
        }
        //页面上修改终端或者移动终端
        if (getCurrentUser() != null || terminal.isMoveTerminal()) {
            List<TerminalGroup> originalStandardTerminalGroupList = null;
            if (originalTerminal != null) {
                //如果是创建终端，不需要检查原始分组，都是新增
                originalTerminal.setMoveTerminal(terminal.isMoveTerminal());
                originalStandardTerminalGroupList = findStandardTerminalGroupList(originalTerminal);
            }
            List<TerminalGroup> addList = new ArrayList<>();
            List<TerminalGroup> deleteList = new ArrayList<>();

            if (CollectionUtils.isEmpty(originalStandardTerminalGroupList)) {
                //如果原始列表为空，那么所有的都是新增
                if (CollectionUtils.isNotEmpty(terminal.getTerminalGroupList())) {
                    addList.addAll(terminal.getTerminalGroupList());
                }
            } else if (CollectionUtils.isNotEmpty(terminal.getTerminalGroupList())) {
                //当前列表不为空，需要比对哪些删除，哪些新增
                //当前列表不在原始列表里的，新增
                for (TerminalGroup terminalGroup : terminal.getTerminalGroupList()) {
                    if (!originalStandardTerminalGroupList.contains(terminalGroup)) {
                        addList.add(terminalGroup);
                    }
                }
                //原始列表不在当前列表里的，删除
                for (TerminalGroup terminalGroup : originalStandardTerminalGroupList) {
                    if (!terminal.getTerminalGroupList().contains(terminalGroup)) {
                        deleteList.add(terminalGroup);
                    }
                }
            } else {
                //当前列表为空，全部删除
                deleteList.addAll(originalStandardTerminalGroupList);
            }

            for (TerminalGroup terminalGroup : addList) {
                terminalGroup = get(terminalGroup);
                if (terminalGroup == null || BooleanUtils.isTrue(terminalGroup.getDynamic())) {
                    throw new BusinessException(ApiCodes.TERMINAL_GROUP_NOT_FOUND);
                }
                createGroupTerminal(terminalGroup, terminal);
            }

            for (TerminalGroup terminalGroup : deleteList) {
                deleteGroupTerminalsAndPushTasks(terminalGroup, Sets.newHashSet(terminal.getId()), terminal.isModelChanged());
            }
        }
    }

    /**
     * 更新终端或者移动终端会调用
     */
    @Transactional
    public void updateDynamicTerminalGroup(Terminal originalTerminal, Terminal terminal) {
        if (StringUtils.equals(terminal.getStatus(), TerminalStatus.PENDING) || terminal.isIgnoreDynamicGroupCheck()) {
            return;
        }
        List<TerminalGroup> originalDynamicGroupList;
        if (originalTerminal == null) {
            //如果是创建终端，不需要检查原始动态分组，都是新增
            originalDynamicGroupList = null;
        } else {
            originalTerminal.setMoveTerminal(terminal.isMoveTerminal());
            originalDynamicGroupList = findDynamicTerminalGroupList(originalTerminal);
        }
        List<TerminalGroup> currentDynamicGroupList = findDynamicTerminalGroupList(terminal);

        List<TerminalGroup> addList = new ArrayList<>();
        List<TerminalGroup> deleteList = new ArrayList<>();
        if (CollectionUtils.isEmpty(originalDynamicGroupList)) {
            if (CollectionUtils.isNotEmpty(currentDynamicGroupList)) {
                addList.addAll(currentDynamicGroupList);
            }
        } else {
            for (TerminalGroup terminalGroup : currentDynamicGroupList) {
                if (!originalDynamicGroupList.contains(terminalGroup)) {
                    addList.add(terminalGroup);
                }
            }
            for (TerminalGroup terminalGroup : originalDynamicGroupList) {
                if (!currentDynamicGroupList.contains(terminalGroup)) {
                    deleteList.add(terminalGroup);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            addList.forEach(terminalGroup -> {
                if (terminalGroup.getDynamic()) {
                    createGroupTerminalsWithoutValidate(terminalGroup, Collections.singletonList(terminal.getId()));
                }
            });
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            deleteList.forEach(terminalGroup -> {
                deleteGroupTerminalsAndPushTasks(terminalGroup, Sets.newHashSet(terminal.getId()), terminal.isModelChanged());
            });
        }
    }


}