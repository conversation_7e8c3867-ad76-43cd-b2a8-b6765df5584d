package com.paxstore.market.domain.service.vas;


import com.pax.market.domain.entity.market.vas.TerminalAttestationInitialAttribute;
import com.pax.market.domain.entity.market.vas.TerminalAttestationSyncData;
import com.pax.market.framework.common.service.CrudService;
import org.apache.commons.lang3.time.DateUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.dao.vas.TerminalAttestationInitialAttributeDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

@Service
public class TerminalAttestationInitialAttributeService extends CrudService<TerminalAttestationInitialAttributeDao, TerminalAttestationInitialAttribute> {

    @MasterDs
    public TerminalAttestationInitialAttribute getByMarketIdAndTid(Long marketId, Long terminalId) {
        return dao.selectByMarketIdAndTid(marketId, terminalId);
    }

    @MasterDs
    public boolean existTabTime(Long marketId, Long terminalId) {
        TerminalAttestationInitialAttribute attribute = getByMarketIdAndTid(marketId, terminalId);
        return attribute != null
                && attribute.getTabStartTime() != null
                && attribute.getTabExpiredTime() != null
                && attribute.getTabStartTime().before(new Date())
                && attribute.getTabExpiredTime().after(new Date());

    }

    /**
     * 保存初始化的上送数据
     *
     * @param data
     */
    @Transactional
    public void saveInitialAttribute(TerminalAttestationSyncData data, TerminalAttestationInitialAttribute attribute) {

        if (attribute == null) {
            attribute = new TerminalAttestationInitialAttribute()
                    .setTerminalId(data.getTerminalId())
                    .setOsVersion(data.getOsVersion())
                    .setAndroidVersion(data.getAndroidVersion())
                    .setAndroidCode(data.getAndroidCode())
                    .setDeviceId(data.getDeviceId());
            attribute.setMarketId(data.getMarketId());
        } else {
            if (StringUtils.isBlank(attribute.getAndroidVersion()) && StringUtils.isNotBlank(data.getAndroidVersion())) {
                attribute.setAndroidVersion(data.getAndroidVersion());

            }
            if (StringUtils.isBlank(attribute.getDeviceId()) && StringUtils.isNotBlank(data.getDeviceId())) {
                attribute.setDeviceId(data.getDeviceId());

            }
            if (StringUtils.isBlank(attribute.getOsVersion()) && StringUtils.isNotBlank(data.getOsVersion())) {
                attribute.setOsVersion(data.getOsVersion());

            }
            if (Objects.isNull(attribute.getAndroidCode()) && Objects.nonNull(data.getAndroidCode())) {
                attribute.setAndroidCode(data.getAndroidCode());
            }

        }
        if (attribute.getTabStartTime() == null) {
            attribute.setTabStartTime(DateUtils.addMinutes(new Date(), 6));
        }
        attribute.setTabExpiredTime(DateUtils.addDays(new Date(), 7));
        save(attribute);
    }

    @Transactional
    public void resetExpireDate(Long currentMarketId, Long terminalId) {
        dao.updateExpiredDate(currentMarketId, terminalId);
    }
}
