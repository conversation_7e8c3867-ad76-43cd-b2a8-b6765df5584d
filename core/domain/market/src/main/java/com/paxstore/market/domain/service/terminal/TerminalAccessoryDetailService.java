package com.paxstore.market.domain.service.terminal;

import com.paxstore.market.domain.dao.terminal.TerminalAccessoryDetailDao;
import com.pax.market.domain.entity.market.terminal.TerminalAccessoryDetail;
import com.pax.market.framework.common.service.CrudService;
import org.springframework.stereotype.Service;

/**
 * @Author: <PERSON>
 * @Date: 2022/4/26 18:08
 */
@Service
public class TerminalAccessoryDetailService extends CrudService<TerminalAccessoryDetailDao, TerminalAccessoryDetail> {

    public TerminalAccessoryDetail getDetailByAccessoryId(Long accessoryId) {
        return dao.getDetailByAccessoryId(accessoryId);
    }
}
