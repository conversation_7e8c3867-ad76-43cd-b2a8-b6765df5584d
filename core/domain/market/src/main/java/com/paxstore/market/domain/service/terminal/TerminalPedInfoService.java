package com.paxstore.market.domain.service.terminal;

import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.dao.terminal.TerminalPedInfoDao;
import com.pax.market.domain.entity.market.terminal.TerminalPedInfo;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * The type Terminal ped info service.
 */
@Service
public class TerminalPedInfoService extends CrudService<TerminalPedInfoDao, TerminalPedInfo> {
    /**
     * Gets by terminal.
     *
     * @param terminalId the terminal id
     * @return the by terminal
     */
    
    public TerminalPedInfo getByTerminal(Long terminalId) {
        return dao.getByTerminal(terminalId);
    }

    /**
     * Save terminal ped info.
     *
     * @param terminalId the terminal id
     * @param info       the info
     */
    @MasterDs
    public void saveTerminalPedInfo(Long terminalId, String info) {
        TerminalPedInfo existPedInfo = getByTerminal(terminalId);
        if (existPedInfo == null && StringUtils.isNotEmpty(info)) {
            //新增
            TerminalPedInfo pedInfo = new TerminalPedInfo();
            pedInfo.setTerminalId(terminalId);
            pedInfo.setInfo(info);
            dao.insert(pedInfo);
        } else if (existPedInfo != null && StringUtils.isEmpty(info)) {
            //删除
            dao.delete(existPedInfo);
        } else if (existPedInfo != null && !StringUtils.equalsIgnoreBlank(existPedInfo.getInfo(), info)) {
            //更新
            existPedInfo.setInfo(info);
            dao.update(existPedInfo);
        }
    }

    /**
     * Delete terminal ped info.
     *
     * @param terminalId the terminal id
     */
    @MasterDs
    public void deleteByTerminal(Long terminalId) {
        dao.deleteByTerminal(terminalId);
    }

    /**
     * Delete by terminal ids.
     *
     * @param terminalIds the terminal ids
     */
    @MasterDs
    public void deleteByTerminals(List<Long> terminalIds) {
        dao.deleteByTerminals(terminalIds);
    }
}
