package com.paxstore.market.domain.dao.terminal;

import com.pax.market.domain.entity.report.ReportForWidget;
import com.pax.market.domain.entity.market.terminal.TerminalDetail;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The interface Terminal detail dao.
 */
@MyBatisDao
public interface TerminalDetailDao extends CrudDao<TerminalDetail> {
    /**
     * Find list.
     *
     * @param terminalId the terminal id
     * @return the list
     */
    List<TerminalDetail> findByTerminal(@Param("terminalId") Long terminalId);


    /**
     * Find puk md5 list
     *
     * @param marketId    the market id
     * @param resellerIds the reseller ids
     * @param key         the key
     * @return the list
     */
    List<ReportForWidget> findPUKMd5ListForWidget(@Param("marketId") Long marketId, @Param("resellerIds") List<Long> resellerIds, @Param("key") String key);

    /**
     * Gets by key.
     *
     * @param terminalId the terminal id
     * @param key        the key
     * @return the by key
     */
    TerminalDetail getByKey(@Param("terminalId") Long terminalId, @Param("key") String key);

    /**
     * Delete by terminal id.
     *
     * @param terminalId the terminal id
     */
    void deleteByTerminal(@Param("terminalId") Long terminalId);

    /**
     * Delete by terminals.
     *
     * @param terminalId the terminal id
     */
    void deleteByTerminals(@Param("terminalIds") List<Long> terminalId);

    /**
     * Delete by key.
     *
     * @param terminalId the terminal id
     * @param key        the key
     */
    void deleteByKey(@Param("terminalId") Long terminalId, @Param("key") String key);

    /**
     * Find SIM operator
     *
     * @param resellerIds the reseller ids
     * @param simOperator the SIM operator
     * @return the list
     */
    List<String> findSimOperator(@Param("marketId") Long marketId, @Param("resellerIds") List<Long> resellerIds, @Param("simOperator") String simOperator);

    TerminalDetail getByKeyAndValue(@Param("key") String key,@Param("value") String value);

}
