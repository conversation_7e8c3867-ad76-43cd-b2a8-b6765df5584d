package com.paxstore.market.domain.service.terminal;

import com.pax.market.domain.entity.market.terminal.TerminalCheckupInfo;
import com.pax.market.framework.common.service.CrudService;
import com.paxstore.market.domain.dao.terminal.TerminalCheckupInfoDao;
import org.springframework.stereotype.Service;

@Service
public class TerminalCheckupInfoService extends CrudService<TerminalCheckupInfoDao, TerminalCheckupInfo> {

    public TerminalCheckupInfo getByTerminalId(Long terminalId) {
        return dao.getByTerminalId(terminalId);
    }
}
