package com.paxstore.market.domain.service.airlink;

import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalTask;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.persistence.BatchOperator;
import com.pax.market.framework.common.persistence.handler.BatchConsumerHandler;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.dao.airlink.AirLinkTerminalTaskDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * author mengxiaoxian
 * Date   2025/4/24 17:45
 */
@Service
@RequiredArgsConstructor
public class AirLinkTerminalTaskService extends CrudService<AirLinkTerminalTaskDao, AirLinkTerminalTask> {

    @MasterDs
    public void batchCreateTask(List<AirLinkTerminalTask> terminalTasks){
        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                BatchConsumerHandler.of(terminalTasks, dao::insertList)
        );
    }

    @MasterDs
    public void batchDeleteTask(List<String> imeiList){
        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                BatchConsumerHandler.of(imeiList, dao::deleteByImeis)
        );
    }

    public boolean existByImei(String imei, String status){
        return dao.existByImei(imei, status);
    }
    @MasterDs
    public void deleteByMarketAndType(Long marketId, String type){
        dao.deleteByMarketAndType(marketId, type);
    }
}
