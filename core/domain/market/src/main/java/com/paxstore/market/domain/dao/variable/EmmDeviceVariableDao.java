package com.paxstore.market.domain.dao.variable;

import com.pax.market.domain.entity.market.emm.EmmDevice;
import com.pax.market.domain.entity.market.emm.EmmDeviceVariable;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/6
 */
@MyBatisDao
public interface EmmDeviceVariableDao extends BaseVariableDao<EmmDeviceVariable> {

    Integer getVariableCountForExport(EmmDevice device);

    List<EmmDeviceVariable> findVariableListForExport(EmmDevice device);

    List<EmmDeviceVariable> findVariableListByIds(List<Long> ids);

}
