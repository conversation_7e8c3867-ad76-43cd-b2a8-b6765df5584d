package com.paxstore.market.domain.dao.terminal;

import com.pax.market.domain.entity.market.pushtask.TerminalApkParamHistory;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

@MyBatisDao
public interface TerminalApkParamHistoryDao extends CrudDao<TerminalApkParamHistory> {
    int getCountByTerminalAndApp(@Param("terminalId") Long terminalId, @Param("appId")Long appId);
    void deleteTerminalApkParamHistory(@Param("terminalId") Long terminalId, @Param("appId")Long appId, @Param("limit") int limit);
    TerminalApkParamHistory getLatestTerminalApkParamHistory(@Param("terminalId") Long terminalId, @Param("appId") Long appId, @Param("paramTemplateName") String paramTemplateName);
}
