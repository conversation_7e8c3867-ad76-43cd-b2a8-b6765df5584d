package com.paxstore.market.domain.dao.terminal;

import com.pax.market.domain.entity.market.terminal.TerminalCheckupInfo;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

@MyBatisDao
public interface TerminalCheckupInfoDao extends CrudDao<TerminalCheckupInfo> {

    TerminalCheckupInfo getByTerminalId(@Param("terminalId") Long terminalId);
}
