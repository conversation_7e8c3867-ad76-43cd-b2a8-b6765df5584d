/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.paxstore.market.domain.service;


import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.market.DownloadTask;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.dao.DownloadTaskDao;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * The type Download task service.
 */
@Service
public class DownloadTaskService extends CrudService<DownloadTaskDao, DownloadTask> {

    @MasterDs
    @Override
    public DownloadTask get(Long id) {
        return super.get(id);
    }

    /**
     * Create download task download task.
     *
     * @param fileName the file name
     * @param fileId   the file id
     * @param status   the status
     * @param isTemp   the is temp
     * @param type     the type
     * @return the download task
     */
    @MasterDs
    public DownloadTask createDownloadTask(String fileName, String fileId, String status, boolean isTemp, String type) {
        return createDownloadTask(fileName, fileId, getCurrentMarketId(), status, isTemp, type);
    }

    /**
     * Create download task download task.
     *
     * @param fileName       the file name
     * @param fileId         the file id
     * @param status         the status
     * @param isTemp         the is temp
     * @param type           the type
     * @param extractionCode the extraction code
     * @return the download task
     */
    @MasterDs
    public DownloadTask createDownloadTask(String fileName, String fileId, String status, boolean isTemp, String type, String extractionCode) {
        return createDownloadTask(fileName, fileId, getCurrentMarketId(), status, isTemp, type, extractionCode);
    }

    /**
     * Create download task download task.
     *
     * @param fileName     the file name
     * @param fileId       the file id
     * @param fileMarketId the file market id
     * @param status       the status
     * @param isTemp       the is temp
     * @param type         the type
     * @return the download task
     */
    @MasterDs
    public DownloadTask createDownloadTask(String fileName, String fileId, Long fileMarketId, String status, boolean isTemp, String type) {
        return createDownloadTask(fileName, fileId, fileMarketId, status, isTemp, type, null);
    }

    /**
     * Create download task download task.
     *
     * @param fileName       the file name
     * @param fileId         the file id
     * @param fileMarketId   the file market id
     * @param status         the status
     * @param isTemp         the is temp
     * @param type           the type
     * @param extractionCode the extraction code
     * @return the download task
     */
    @MasterDs
    public DownloadTask createDownloadTask(String fileName, String fileId, Long fileMarketId, String status, boolean isTemp, String type, String extractionCode) {
        DownloadTask downloadTask = new DownloadTask();
        if (!isMobileRequest()) {
            downloadTask.setFileName(formatFileName(fileName));
        } else {
            downloadTask.setFileName(fileName);
        }

        downloadTask.setFileId(fileId);
        downloadTask.setFileMarketId(fileMarketId);
        downloadTask.setStatus(status);
        downloadTask.setTemp(isTemp);
        downloadTask.setType(type);
        downloadTask.setExtractionCode(extractionCode);
        super.save(downloadTask);
        return downloadTask;
    }

    private boolean isMobileRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return false;
        }
        HttpServletRequest request = requestAttributes.getRequest();
        return "mobile".equals(request.getHeader("X-Client-ID"));
    }

    private String formatFileName(String sourceName) {
        if (StringUtils.isEmpty(sourceName)) return "-";
        return sourceName.replaceAll("\\s+", "_");
    }

    /**
     * Update download task.
     *
     * @param downloadTaskId the download task id
     * @param status         the status
     * @param remarks        the remarks
     */
    @MasterDs
    public void updateDownloadTask(Long downloadTaskId, String status, String remarks) {
        DownloadTask downloadTask = get(downloadTaskId);
        if (downloadTask == null) {
            throw new BusinessException(ApiCodes.DOWNLOAD_TASK_NOT_FOUND);
        }
        downloadTask.setStatus(status);
        downloadTask.setRemarks(StringUtils.ensure(remarks, 255));
        super.save(downloadTask);
    }

}
