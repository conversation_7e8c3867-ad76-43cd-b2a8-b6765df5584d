package com.paxstore.market.domain.dao.cleardata;

import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2023/2/14 15:50
 */
@MyBatisDao
public interface ResellerPhysicalDeleteDao extends CrudDao<Reseller> {
    List<Long> findResellerIdsByMarketId(@Param("marketId") Long marketId);
    List<Long> findDeletedResellerIds(@Param("marketId") Long marketId, @Param("deleteDays") int deleteDays);
    void deleteReseller(@Param("resellerId") Long resellerId);
    void deleteProfile(@Param("resellerId") Long resellerId);
    void deleteEmmPolicy(@Param("resellerId") Long resellerId);
    void deleteFromResellerRelatedTable(@Param("relatedTableName") String relatedTableName, @Param("resellerId") Long resellerId);
}
