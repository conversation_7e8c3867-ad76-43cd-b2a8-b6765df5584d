/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.paxstore.market.domain.dao.pushtask;

import com.pax.market.domain.entity.market.pushtask.TerminalApkParam;
import com.pax.market.domain.entity.market.pushtask.TerminalLastApkParam;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 终端应用参数DAO接口
 *
 * <AUTHOR>
 */
@MyBatisDao
public interface TerminalApkParamDao extends BasePushTaskDao<TerminalApkParam> {
    /**
     * Update terminal apk param variables.
     *
     * @param terminalApkParam the terminal apk param
     */
    void updateDownloadInfo(TerminalApkParam terminalApkParam);

    /**
     * Gets terminal last success history.
     *
     * @param terminalId        the terminal id
     * @param apkIdList             the apk id list
     * @param paramTemplateName the param template name
     * @return the terminal last success history
     */
    TerminalLastApkParam getTerminalLastSuccessHistory(@Param("terminalId") Long terminalId, @Param("apkIdList") List<Long> apkIdList, @Param("paramTemplateName") String paramTemplateName);

    /**
     * Find success terminal apk param push history list list.
     *
     * @param terminalId the terminal id
     * @return the list
     */
    List<TerminalLastApkParam> findSuccessTerminalApkParamPushHistoryList(@Param("terminalId") Long terminalId);

    TerminalLastApkParam getLatestSuccessTerminalApkParamPushHistory(@Param("terminalId") Long terminalId, @Param("type") String type);

    TerminalLastApkParam getTerminalLastLauncherSuccessHistory(@Param("terminalId") Long terminalId, @Param("apkIdList") List<Long> apkIdList);

    void updateParamTemplateId(TerminalApkParam terminalApkParam);
}