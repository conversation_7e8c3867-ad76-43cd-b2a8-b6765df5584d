package com.paxstore.market.domain.dao.airlink;

import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalActiveHistory;
import com.pax.market.domain.query.AirLinkTerminalActiveHistoryQuery;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12 13:46
 */
@MyBatisDao
public interface AirLinkTerminalActiveHistoryDao extends CrudDao<AirLinkTerminalActiveHistory> {
    BigDecimal getMarketTotalOccupationFee(@Param("marketId")Long marketId, @Param("status")String status);

    List<AirLinkTerminalActiveHistory> findTerminalActiveHistoryList(AirLinkTerminalActiveHistoryQuery airLinkTerminalActiveHistoryQuery);

    List<AirLinkTerminalActiveHistory> findMarketActiveHistory(@Param("marketId") Long marketId,
                                                                           @Param("status") String status);

    List<AirLinkTerminalActiveHistory> findResellerActiveHistory(@Param("resellerId") Long resellerId,
                                                               @Param("status") String status);
    void updateActiveHistoryTerminalNum(AirLinkTerminalActiveHistory activeHistory);

    void completeActiveHistory(AirLinkTerminalActiveHistory activeHistory);

    AirLinkTerminalActiveHistory getByIdAndResellerId(@Param("id") Long id, @Param("resellerId") Long resellerId);

    void updateActiveFailNum(@Param("id") Long id,
                         @Param("failNum") int failNum);

    List<AirLinkTerminalActiveHistory> findLastMonthNotDeductActiveHistoryByMarket(@Param("marketId") Long marketId,
                                                                                   @Param("startTime") Date startTime,
                                                                                   @Param("endTime") Date endTime);

    void successLastMonthBilled(AirLinkTerminalActiveHistory activeHistory);
}
