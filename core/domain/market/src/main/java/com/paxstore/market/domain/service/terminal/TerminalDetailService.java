package com.paxstore.market.domain.service.terminal;

import com.pax.core.json.JsonMapper;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.market.pushtask.TerminalOperation;
import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.domain.entity.market.terminal.TerminalDetail;
import com.pax.market.domain.entity.report.ReportForWidget;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.terminal.TerminalOptInfo;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.dao.terminal.TerminalDetailDao;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.service.pushtask.TerminalOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * The type Terminal detail service.
 */
@Service
public class TerminalDetailService extends CrudService<TerminalDetailDao, TerminalDetail> {

    @Autowired
    private TerminalOperationService terminalOperationService;
    @Autowired
    private TerminalActionHistoryService terminalActionHistoryService;
    @Autowired
    private ResellerService resellerService;
    @Autowired
    private TerminalActionService terminalActionService;

    /**
     * Delete terminal details.
     *
     * @param terminalId the terminal id
     */
    @MasterDs
    public void deleteByTerminal(Long terminalId) {
        dao.deleteByTerminal(terminalId);
    }

    /**
     * Delete by terminal ids.
     *
     * @param terminalIds the terminal ids
     */
    @MasterDs
    public void deleteByTerminals(List<Long> terminalIds) {
        dao.deleteByTerminals(terminalIds);
    }

    /**
     * Gets by key.
     *
     * @param terminalId the terminal id
     * @param key        the key
     * @return the by key
     */
    public TerminalDetail getByKey(Long terminalId, String key) {
        return dao.getByKey(terminalId, key);
    }

    /**
     * Find list list.
     *
     * @param terminalId the terminal id
     * @return the list
     */
    public List<TerminalDetail> findByTerminal(Long terminalId) {
        return dao.findByTerminal(terminalId);
    }

    /**
     * Find puk md5 list
     *
     * @param currentMarketId the market id
     * @param resellerId      the reseller id
     * @param key             the key
     * @return the list
     */
    
    public List<ReportForWidget> findPUKMd5ListForWidget(Long currentMarketId, Long resellerId, String key) {
        List<Long> resellerIds = resellerService.findAvailableResellerIds(resellerId);
        return dao.findPUKMd5ListForWidget(currentMarketId, resellerIds, key);
    }

    /**
     * Load operation status.
     *
     * @param terminalId      the terminal id
     * @param terminalDetails the terminal details
     * @return the boolean
     */
    public boolean loadOperationStatus(Long terminalId, List<TerminalDetail> terminalDetails) {
        if (Collections3.isEmpty(terminalDetails)) {
            return false;
        }
        AtomicBoolean result = new AtomicBoolean(false);
        terminalDetails.forEach(terminalDetail -> {
            Integer actionStatus = StringUtils.equals(terminalDetail.getKey(), TerminalOperationKeys.PUK_INJECT) ? null : TerminalActionStatus.PENDING;
            TerminalOperation terminalOperation = terminalOperationService.getLatestTerminalOperations(terminalId, terminalDetail.getKey(), null, actionStatus);
            if (terminalOperation != null) {
                if (terminalOperation.getActionStatus() == TerminalActionStatus.PENDING) {
                    if (TerminalOperationKeys.TERMINAL_CONFIG_OPERATION.contains(terminalOperation.getKey()) || System.currentTimeMillis() - terminalOperation.getEffectiveTime().getTime() < SystemPropertyHelper.getTerminalOperationTimeout()) {
                        terminalDetail.setOptStatus(1);
                        if (StringUtils.equals(terminalDetail.getKey(), TerminalDetailKeys.PUK_INJECT) && JsonMapper.isJsonValue(terminalOperation.getValue())) {
                            terminalDetail.setOptValue(JsonMapper.fromJsonString(terminalOperation.getValue(), TerminalOptInfo.class).getValue());
                        } else {
                            terminalDetail.setOptValue(terminalOperation.getValue());
                        }
                        //如果存在一定时间内未完成的操作，页面需要自动刷新
                        result.set(true);
                        terminalDetail.setOperationId(terminalOperation.getId());
                    } else {
                        //超过一定时间自动更新任务为失败,PUK和LOCK_TM除外
                        if (actionStatus == null || StringUtils.equals(terminalDetail.getKey(), TerminalDetailKeys.LOCK_TM)) {
                            terminalDetail.setOptStatus(1);
                            terminalDetail.setOptValue(terminalOperation.getValue());
                        } else {
                            TerminalAction terminalAction = terminalActionService.getPendingTerminalAction(terminalId, terminalOperation.getId(), TerminalActionType.TERMINAL_OPERATION);
                            if (terminalAction != null) {
                                terminalOperationService.updatePushTaskActionStatus(terminalOperation, TerminalActionStatus.FAILED, TerminalActionErrorCode.ACTION_TIME_OUT);
                                terminalActionService.completeTerminalAction(terminalAction, TerminalActionStatus.FAILED, TerminalActionErrorCode.ACTION_TIME_OUT, null);
                            }
                        }
                    }
                } else {
                    TerminalAction historyAction = terminalActionHistoryService.getTerminalActionHistoryByTerminal(terminalId, terminalOperation.getId(), TerminalActionType.TERMINAL_OPERATION, 0, 0);
                    if (historyAction != null) {
                        terminalDetail.setHistoryStatus(historyAction.getStatus());
                        terminalDetail.setHistoryRemarks(historyAction.getRemarks());
                    }
                }
            }
        });
        return result.get();
    }

    /**
     * Find SIM operator
     *
     * @param simOperator the SIM operator
     * @return the list
     */
    
    public List<String> findSimOperator(String simOperator) {
        List<Long> resellerIds = resellerService.findAvailableResellerIds(getCurrentResellerId());
        return dao.findSimOperator(getCurrentMarketId(), resellerIds, simOperator);
    }

    @MasterDs
    public void insert(TerminalDetail terminalDetail) {
        dao.insert(terminalDetail);
    }

    @MasterDs
    public void update(TerminalDetail terminalDetail) {
        dao.update(terminalDetail);
    }

    @Override
    @MasterDs
    public void delete(TerminalDetail terminalDetail) {
        super.delete(terminalDetail);
    }


    @MasterDs
    public void deleteByKey(Long terminalId, String key) {
        dao.deleteByKey(terminalId, key);
    }

    public TerminalDetail getByKeyAndValue(String key,String value) {
        return dao.getByKeyAndValue(key, value);
    }


    public Boolean isPci7Terminal(Long terminalId){
        TerminalDetail terminalDetail =  getByKey(terminalId, TerminalDetailKeys.PCI_VERSION);
        if (Objects.isNull(terminalDetail)){
            return null;
        }else {
            return "7".equals(terminalDetail.getValue());
        }
    }
}
