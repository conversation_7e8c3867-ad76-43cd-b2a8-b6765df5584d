package com.paxstore.market.domain.service.terminal;

import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.paxstore.market.domain.dao.terminal.TerminalTrafficDao;
import com.pax.market.domain.entity.market.terminal.TerminalTraffic;

import java.util.Date;
import java.util.List;

/**
 * The type Terminal traffic service.
 */
@Service
public class TerminalTrafficService {
    @Autowired
    private TerminalTrafficDao dao;
    @Autowired
    private MarketTerminalService marketTerminalService;

    /**
     * Create terminal traffic.
     *
     * @param terminalTraffic the terminal traffic
     */
    @MasterDs
    public void saveTerminalTraffic(TerminalTraffic terminalTraffic){
        TerminalTraffic existTerminalTraffic = dao.getByTerminal(terminalTraffic.getTerminalId());
        if (existTerminalTraffic == null) {
            dao.insert(terminalTraffic);
        } else {
            terminalTraffic.setId(existTerminalTraffic.getId());
            dao.update(terminalTraffic);
        }
    }

    /**
     * Delete by sync date.
     *
     * @param terminalId     the terminal id
     */
    @MasterDs
    public void deleteByTerminal(Long terminalId) {
        dao.deleteByTerminal(terminalId);
    }

    /**
     * Delete by terminal ids.
     *
     * @param terminalIds the terminal ids
     */
    @MasterDs
    public void deleteByTerminals(List<Long> terminalIds) {
        dao.deleteByTerminals(terminalIds);
    }

    /**
     * Get terminal traffic terminal traffic.
     *
     * @param terminalId the terminal id
     * @return the terminal traffic
     */
    
    public TerminalTraffic getTerminalTraffic(Long terminalId){
        TerminalTraffic terminalTraffic = dao.getByTerminal(terminalId);
        Terminal terminal = marketTerminalService.get(terminalId);
        if(terminalTraffic != null && terminal != null){
            Date syncDate = terminalTraffic.getSyncDate(); //终端流量是和已安装应用一起上送的，所以可以使用app_sync_date
            if(syncDate == null){
                syncDate = new Date();
            }
            if(!DateUtils.isThisMonth(syncDate)){
                terminalTraffic.setTraffic(null);
                terminalTraffic.setTraffic4g(null);
            }
            if(!DateUtils.isToday(syncDate)){
                terminalTraffic.setTrafficDay(null);
                terminalTraffic.setTraffic4gDay(null);
            }
        }
        return terminalTraffic;
    }

}
