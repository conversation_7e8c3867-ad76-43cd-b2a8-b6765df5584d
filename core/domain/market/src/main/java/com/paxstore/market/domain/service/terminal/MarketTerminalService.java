package com.paxstore.market.domain.service.terminal;


import com.pax.api.cache.CacheService;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.client.ClientApk;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.OfflineTerminalExportExcelEntity;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalGroup;
import com.pax.market.domain.entity.market.terminal.TerminalReplaceLog;
import com.pax.market.domain.entity.report.ModelDashboardReportForWidget;
import com.pax.market.domain.entity.report.ReportForWidget;
import com.pax.market.domain.query.TerminalOfflineWidgetQuery;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.MerchantInfo;
import com.pax.market.dto.ResellerInfo;
import com.pax.market.dto.terminal.TerminalQtySummary;
import com.pax.market.dto.terminal.TerminalQtyTopTenByMerchant;
import com.pax.market.dto.terminal.TerminalQtyTopTenByReseller;
import com.pax.market.framework.common.persistence.BatchOperator;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.persistence.handler.BatchConsumerHandler;
import com.pax.market.framework.common.persistence.handler.SingleConsumerHandler;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.DataScopeFilter;
import com.paxstore.market.domain.dao.terminal.MarketTerminalDao;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The type Market terminal service.
 *
 * @Author: Zhou Dong
 * @Date: 2021 /4/26 10:29
 */
@Service
public class MarketTerminalService extends CrudService<MarketTerminalDao, Terminal> {
    /**
     * The constant TERMINAL_CACHE_ID_.
     */
    private static final String TERMINAL_CACHE_ID_ = "id_";
    /**
     * The constant TERMINAL_CACHE_SN_.
     */
    private static final String TERMINAL_CACHE_SN_ = "sn_";
    /**
     * The constant TERMINAL_CACHE_TID_.
     */
    private static final String TERMINAL_CACHE_TID_ = "tid_";

    @Autowired
    private MarketTerminalDao dao;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ResellerService resellerService;
    @Autowired
    private TerminalAccessTimeService terminalAccessTimeService;
    @Autowired
    private TerminalGroupService terminalGroupService;
    @Autowired
    private TerminalReplaceHistoryService terminalReplaceHistoryService;

    /**
     * Clear terminal cache.
     *
     * @param terminal the terminal
     */
    public static void clearTerminalCache(Terminal terminal) {
        if (terminal == null) {
            return;
        }
        CacheService cacheService = SpringContextHolder.getApplicationContext().getBean(CacheService.class);
        if (StringUtils.isBlank(terminal.getSerialNo()) || StringUtils.isBlank(terminal.getTID())) {
            MarketTerminalService marketTerminalService = SpringContextHolder.getApplicationContext().getBean(MarketTerminalService.class);
            Terminal terminalInDb = marketTerminalService.get(terminal.getId());
            if (terminalInDb != null) {
                terminal = terminalInDb;
            }
        }
        cacheService.remove(CacheNames.TERMINAL_CACHE,
                TERMINAL_CACHE_ID_ + terminal.getId(),
                TERMINAL_CACHE_SN_ + StringUtils.upperCase(terminal.getSerialNo()),
                TERMINAL_CACHE_TID_ + StringUtils.upperCase(terminal.getTID()));

    }

    /**
     * Clear terminal cache.
     *
     * @param terminals the terminals
     */
    public static void clearTerminalCache(List<Terminal> terminals) {
        if (Collections3.isEmpty(terminals)) {
            return;
        }

        CacheService cacheService = SpringContextHolder.getApplicationContext().getBean(CacheService.class);

        List<String> keys = new ArrayList<>();
        for (Terminal terminal : terminals) {
            keys.add(TERMINAL_CACHE_ID_ + terminal.getId());
            keys.add(TERMINAL_CACHE_SN_ + StringUtils.upperCase(terminal.getSerialNo()));
            keys.add(TERMINAL_CACHE_TID_ + StringUtils.upperCase(terminal.getTID()));
        }
        cacheService.remove(CacheNames.TERMINAL_CACHE, keys);
    }

    /**
     * Clear terminal count cache.
     *
     * @param marketId the market id
     */
    public void clearTerminalCountCache(Long marketId) {
        List<Long> marketIds = new ArrayList<>();
        if (marketId != null && marketId > -1) {
            marketIds.add(marketId);
            marketIds.add(SystemConstants.SUPER_MARKET_ID);
        }
        if (CollectionUtils.isNotEmpty(marketIds)) {
            marketIds.forEach(id -> {
                String cacheKey = String.format("%s:%s", CacheNames.TERMINAL_COUNT_CACHE, id);
                cacheService.remove(cacheKey);
            });
        }
    }

    /**
     * Gets by serial no.
     *
     * @param serialNo the serial no
     * @return the by serial no
     */
    @MasterDs
    public Terminal getBySerialNo(String serialNo) {
        if (StringUtils.isEmpty(serialNo)) {
            return null;
        }

        return getBySerialNo(serialNo, true);
    }

    /**
     * Gets by serial no list.
     *
     * @param serialNoList the serial no list
     * @return the by serial no list
     */
    @MasterDs
    public List<Terminal> getBySerialNoList(Set<String> serialNoList) {
        if (CollectionUtils.isEmpty(serialNoList)) {
            return null;
        }
        return dao.getBySerialNoList(null, serialNoList);
    }

    /**
     * Gets by serial no list.
     *
     * @param marketId     the market id
     * @param serialNoList the serial no list
     * @return the by serial no list
     */
    public List<Terminal> getBySerialNoList(Long marketId, Set<String> serialNoList) {
        if (CollectionUtils.isEmpty(serialNoList)) {
            return null;
        }
        return dao.getBySerialNoList(marketId, serialNoList);
    }

    /**
     * Gets by serial no.
     *
     * @param serialNo       the serial no
     * @param checkDataScope the check reseller
     * @return the by serial no
     */
    @MasterDs
    public Terminal getBySerialNo(String serialNo, boolean checkDataScope) {
        Terminal result = (Terminal) cacheService.get(CacheNames.TERMINAL_CACHE, TERMINAL_CACHE_SN_ + StringUtils.upperCase(serialNo));
        if (result == null) {
            result = dao.getBySerialNo(serialNo);
            if (result == null) {
                return null;
            }

            cacheService.put(CacheNames.TERMINAL_CACHE, TERMINAL_CACHE_SN_ + StringUtils.upperCase(serialNo), result);
        }

        if (checkDataScope) {
            resellerService.loadDetails(result.getReseller());
            if (!checkDataScope(result.getReseller())) {
                return null;
            }
        }
        return result;
    }

    /**
     * Gets by tid.
     *
     * @param tid the tid
     * @return the by tid
     */
    @MasterDs
    public Terminal getByTID(String tid) {
        return getByTID(tid, true);
    }

    /**
     * Gets by tid.
     *
     * @param tid            the tid
     * @param checkDataScope the check data scope
     * @return the by tid
     */
    @MasterDs
    public Terminal getByTID(String tid, boolean checkDataScope) {
        Terminal result = (Terminal) cacheService.get(CacheNames.TERMINAL_CACHE, TERMINAL_CACHE_TID_ + StringUtils.upperCase(tid));
        if (result == null) {
            result = dao.getByTID(tid);
            if (result == null) {
                return null;
            }

            cacheService.put(CacheNames.TERMINAL_CACHE, TERMINAL_CACHE_TID_ + StringUtils.upperCase(tid), result);
        }

        if (checkDataScope) {
            resellerService.loadDetails(result.getReseller());
            if (!checkDataScope(result.getReseller())) {
                return null;
            }
        }
        return result;
    }

    @MasterDs
    @Override
    public Terminal get(Long id) {
        return get(new Terminal(id), true);
    }

    @MasterDs
    @Override
    public Terminal get(Terminal terminal) {
        return get(terminal, true);
    }

    /**
     * Get terminal.
     *
     * @param id             the id
     * @param checkDataScope the check data scope
     * @return the terminal
     */
    @MasterDs
    public Terminal get(Long id, boolean checkDataScope) {
        return get(new Terminal(id), checkDataScope);
    }

    /**
     * Get terminal.
     *
     * @param terminal       the terminal
     * @param checkDataScope the check data scope
     * @return the terminal
     */
    @MasterDs
    public Terminal get(Terminal terminal, boolean checkDataScope) {
        Terminal result = getIncludeDeleted(terminal.getId());
        //数据隔离
        if (result == null || result.isDeleted()) {
            return null;
        }
        if (checkDataScope) {
            resellerService.loadDetails(result.getReseller());
            if (!checkDataScope(result.getReseller())) {
                return null;
            }
        }
        return result;
    }

    /**
     * Get include deleted terminal.
     *
     * @param terminalId the terminal id
     * @return the terminal
     */
    @MasterDs
    public Terminal getIncludeDeleted(Long terminalId) {
        if (LongUtils.isBlankOrNotPositive(terminalId)) {
            return null;
        }

        Terminal result = (Terminal) cacheService.get(CacheNames.TERMINAL_CACHE, TERMINAL_CACHE_ID_ + terminalId);
        if (result == null) {
            result = super.get(new Terminal(terminalId));
            if (result == null) {
                return null;
            }
            cacheService.put(CacheNames.TERMINAL_CACHE, TERMINAL_CACHE_ID_ + terminalId, result);
        }
        return result;
    }


    /**
     * Load details.
     *
     * @param terminal the terminal
     */
    @MasterDs
    public void loadDetails(Terminal terminal) {
        if (terminal == null) {
            return;
        }
        Terminal terminalDetail = getIncludeDeleted(terminal.getId());
        if (terminalDetail != null) {
            terminal.setMarketId(terminalDetail.getMarketId());
            terminal.setName(terminalDetail.getName());
            terminal.setSerialNo(terminalDetail.getSerialNo());
            terminal.setTID(terminalDetail.getTID());
            terminal.setStatus(terminalDetail.getStatus());
            terminal.setReseller(terminalDetail.getReseller());
            terminal.setModel(terminalDetail.getModel());
            terminal.setMerchant(terminalDetail.getMerchant());
            terminal.setLocation(terminalDetail.getLocation());
            terminal.setNetwork(terminalDetail.getNetwork());
            terminal.setRemark(terminalDetail.getRemark());
            terminal.setCreatedDate(terminalDetail.getCreatedDate());
            terminal.setUpdatedDate(terminalDetail.getUpdatedDate());
            terminal.setFirstActiveTime(terminalDetail.getFirstActiveTime());
            terminal.setLastActiveTime(terminalDetail.getLastActiveTime());
            terminal.setLastDisableTime(terminalDetail.getLastDisableTime());
            terminal.setDelFlag(terminalDetail.getDelFlag());
        }
        resellerService.loadDetails(terminal.getReseller());
    }

    /**
     * Load details.
     *
     * @param terminalList the terminal list
     */
    @MasterDs
    public void loadDetails(List<Terminal> terminalList) {
        if (CollectionUtils.isEmpty(terminalList)) {
            return;
        }
        terminalList.forEach(this::loadDetails);
    }

    @Override
    public Page<Terminal> findPage(Page<Terminal> page, Terminal terminal) {
        // 生成数据权限过滤条件（dsf为dataScopeFilter的简写，在xml中使用 ${sqlMap.dsf}调用权限SQL）
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        terminal.setPage(page);
        page.setList(dao.findList(terminal));
        loadDetails(page.getList());
        return page;
    }

    /**
     * Find page for 3 rd sys page.
     *
     * @param page     the page
     * @param terminal the terminal
     * @return the page
     */
    public Page<Terminal> findPageFor3rdSys(Page<Terminal> page, Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        terminal.setPage(page);
        if (StringUtils.isNotEmpty(terminal.getSnNameTID())) {
            page.setOrderBy(null);
            page.setList(dao.findListBySnNameTidFor3rdSys(terminal));
        } else {
            if (StringUtils.containsIgnoreCase(page.getOrderBy(), "serialNo")) {
                page.setOrderBy(StringUtils.replaceIgnoreCase(page.getOrderBy(), "serialNo", "serial_no"));
            }
            page.setList(dao.findListFor3rdSys(terminal));
        }
        return page;
    }

    /**
     * Gets count.
     *
     * @param terminal the terminal
     * @return the count
     */
    public Integer getCount(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getCount(terminal);
    }

    /**
     * Gets count by reseller.
     *
     * @param resellerId the reseller id
     * @param modelIds   the model ids
     * @return the count by reseller
     */
    public Integer getCountByReseller(long resellerId, Set<Long> modelIds) {
        return dao.getCountByReseller(resellerId, modelIds);
    }

    /**
     * Gets count for 3 rd sys.
     *
     * @param terminal the terminal
     * @return the count for 3 rd sys
     */
    public Integer getCountFor3rdSys(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        if (StringUtils.isNotEmpty(terminal.getSnNameTID())) {
            return dao.getCountBySnNameTidFor3rdSys(terminal);
        } else {
            return dao.getCountFor3rdSys(terminal);
        }
    }

    /**
     * Find page for ALARM.
     *
     * @param page     the page
     * @param terminal the terminal
     * @param type     the alarm type
     * @return the page
     */
    public Page<Terminal> findPageForAlarm(Page<Terminal> page, Terminal terminal, String type) {
        List<Terminal> terminalList = new ArrayList<>();
        // 生成数据权限过滤条件（dsf为dataScopeFilter的简写，在xml中使用 ${sqlMap.dsf}调用权限SQL）
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        terminal.setPage(page);
        switch (type) {
            case AlarmType.OUT_GEO_FENCE -> {
                terminalList = dao.findListForOGFAlarm(terminal);
            }
            case AlarmType.PRINTER_OUT_PAPER -> {
                terminalList = dao.findListForPOPAlarm(terminal);
            }
            case AlarmType.TAMPER -> {
                terminalList = dao.findListForTamperAlarm(terminal);
            }
            case AlarmType.LOW_COIN_BATTERY -> {
                terminalList = dao.findListForLCBAlarm(terminal);
            }
            case AlarmType.BATTERY_UNHEALTHY -> {
                terminalList = dao.findListForBatteryUnHealthAlarm(terminal);
            }
            default -> {
            }
        }
        loadDetails(terminalList);
        page.setList(terminalList);
        return page;
    }

    /**
     * Find list for export list.
     *
     * @param terminal the terminal
     * @param type     the type
     * @return the list
     */
    public List<Terminal> findListForAlarmExport(Terminal terminal, String type) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        List<Terminal> terminalList = new ArrayList<>();
        switch (type) {
            case AlarmType.OUT_GEO_FENCE -> {
                terminalList = dao.findListForOGFAlarm(terminal);
            }
            case AlarmType.PRINTER_OUT_PAPER -> {
                terminalList = dao.findListForPOPAlarm(terminal);
            }
            case AlarmType.TAMPER -> {
                terminalList = dao.findListForTamperAlarm(terminal);
            }
            case AlarmType.LOW_COIN_BATTERY -> {
                terminalList = dao.findListForLCBAlarm(terminal);
            }
            case AlarmType.BATTERY_UNHEALTHY -> {
                terminalList = dao.findListForBatteryUnHealthAlarm(terminal);
            }

            default -> {
            }
        }
        loadDetails(terminalList);
        return terminalList;
    }


    /**
     * Find list for export list.
     *
     * @param terminal the terminal
     * @return the list
     */
    public List<Terminal> findListForExport(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        final List<Terminal> list = new ArrayList<>();
        dao.findListForExport(terminal, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }


    /**
     * Load offline terminal widget
     *
     * @param terminalOfflineWidgetQueries the query
     * @param resellerIds                  the reseller ids
     * @param merchantId                   the merchant id
     * @return the report for widget
     */
    public ReportForWidget loadOfflineTerminalWidget(TerminalOfflineWidgetQuery terminalOfflineWidgetQueries, List<Long> resellerIds, Long merchantId) {
        return dao.loadOfflineTerminalWidget(terminalOfflineWidgetQueries, resellerIds, merchantId);
    }

    @MasterDs
    @Override
    public void delete(Terminal terminal) {
        super.delete(terminal);
        clearTerminalCache(terminal);
    }

    /**
     * Delete.
     *
     * @param deleteTerminals the delete terminals
     */
    @MasterDs
    public void batchDeleteTerminals(List<Terminal> deleteTerminals) {
        this.batchDeleteTerminals(deleteTerminals, getCurrentUserId());
    }

    /**
     * Delete.
     *
     * @param deleteTerminals the delete terminals
     * @param userId          the user id
     */
    @MasterDs
    public void batchDeleteTerminals(List<Terminal> deleteTerminals, Long userId) {
        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                BatchConsumerHandler.of(deleteTerminals, each -> dao.deleteList(each, userId, new Date()))
        );
    }

    /**
     * pureBatchSave： 纯的批量保存， 不涉及任何校验规则
     *
     * @param terminalList the terminal list
     */
    @Transactional
    public void batchCreateTerminals(List<Terminal> terminalList) {
        // terminal batch save
        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                BatchConsumerHandler.of(terminalList, dao::insertList)
        );

        // Create Group
        Map<Long, Set<Long>> groupTerminalMap = new HashMap<>();
        for (Terminal terminal : terminalList) {
            if (CollectionUtils.isNotEmpty(terminal.getTerminalGroupList())) {
                for (TerminalGroup terminalGroup : terminal.getTerminalGroupList()) {
                    Set<Long> terminalIdSet = groupTerminalMap.computeIfAbsent(terminalGroup.getId(), key -> new HashSet<>());
                    terminalIdSet.add(terminal.getId());
                }
            }
        }
        groupTerminalMap.forEach((groupId, terminalIds) -> {
            TerminalGroup terminalGroup = terminalGroupService.get(groupId);
            if (terminalGroup == null) {
                throw new BusinessException(ApiCodes.TERMINAL_GROUP_NOT_FOUND);
            }
            terminalGroupService.createGroupTerminals(terminalGroup, terminalIds);
        });
        terminalGroupService.createDynamicGroupTerminals(terminalList);
    }

    /**
     * Save.
     *
     * @param terminal         the terminal
     * @param originalTerminal the original terminal
     */
    @Transactional
    public void save(Terminal terminal, Terminal originalTerminal) {
        super.save(terminal);
        handleTerminalReplacement(originalTerminal, terminal);
        terminalGroupService.updateStandardTerminalGroup(originalTerminal, terminal);
        terminalGroupService.updateDynamicTerminalGroup(originalTerminal, terminal);
    }

    private void handleTerminalReplacement(Terminal originalTerminal, Terminal terminal) {
        //检查是否是换机操作
        if (originalTerminal != null && !TerminalStatus.PENDING.equals(originalTerminal.getStatus())
                && StringUtils.isNoneBlank(originalTerminal.getSerialNo())
                && !originalTerminal.getSerialNo().equals(terminal.getSerialNo())) {
            terminal.setHasReplacement(true);
            originalTerminal.setHasReplacement(true);
            terminalReplaceHistoryService.insert(new TerminalReplaceLog(terminal.getId(), originalTerminal.getSerialNo(), terminal.getSerialNo()));
        }
    }

    /**
     * Remote init.
     *
     * @param terminal         the terminal
     * @param originalTerminal the original terminal
     */
    @Transactional
    public void remoteInit(Terminal terminal, Terminal originalTerminal) {
        save(terminal, originalTerminal);
        if (!TerminalStatus.ACTIVE.equals(originalTerminal.getStatus())) {
            terminal.setStatus(TerminalStatus.ACTIVE);
            updateStatus(terminal);
        }
    }

    /**
     * Find list for delete list.
     *
     * @param marketId the market id
     * @param limit    the limit
     * @return the list
     */
    public List<Terminal> findListForDelete(Long marketId, int limit) {
        return dao.findListForDelete(marketId, limit);
    }

    /**
     * Delete by market.
     *
     * @param marketId the market id
     * @param userId   the user id
     */
    @MasterDs
    public void deleteByMarket(Long marketId, Long userId) {
        dao.deleteByMarket(marketId, userId, new Date());
    }

    /**
     * Update terminal model.
     *
     * @param terminalId the terminalId
     * @param modelId the modelId
     * @param factoryId the factoryId
     */
    @Transactional
    public void updateModel(Long terminalId, Long modelId, Long factoryId) {
        dao.updateModel(terminalId, modelId, factoryId);
        clearTerminalCache(new Terminal(terminalId));
    }

    /**
     * Update status.
     *
     * @param terminal the terminal
     */
    @Transactional
    public void updateStatus(Terminal terminal) {
        dao.updateStatus(terminal);
        if (TerminalStatus.ACTIVE.equals(terminal.getStatus())) {
            terminalGroupService.createDynamicGroupTerminal(terminal);
        }
        clearTerminalCache(terminal);
    }

    /**
     * Update status.
     *
     * @param terminals the terminals
     * @param status    the status
     * @return the list
     */
    @Transactional
    public void batchUpdateStatus(List<Terminal> terminals, String status) {
        if (CollectionUtils.isEmpty(terminals)) {
            return;
        }
        BatchOperator.batchHandle(
                SystemPropertyHelper.getJdbcBatchSize(),
                SingleConsumerHandler.of(terminals, dao::updateStatus)
        );
        clearTerminalCache(terminals);
        if (TerminalStatus.ACTIVE.equals(status)) {
            terminalGroupService.createDynamicGroupTerminals(terminals);
        }
    }

    /**
     * Update terminal reseller by merchant.
     *
     * @param merchantId       the merchant id
     * @param targetResellerId the target reseller id
     */
    @MasterDs
    public void updateTerminalResellerByMerchant(Long merchantId, Long targetResellerId) {
        dao.updateTerminalResellerByMerchant(merchantId, targetResellerId);
    }

    /**
     * Gets all reseller serial no.
     *
     * @param resellerId the reseller id
     * @return the all reseller serial no
     */
    public List<Terminal> getAllResellerSerialNo(long resellerId) {
        Terminal terminal = new Terminal();
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", new Reseller(resellerId)));
        final List<Terminal> list = new ArrayList<>();
        dao.getAllResellerSerialNo(terminal, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    /**
     * Find reseller terminal ids list.
     *
     * @param resellerId the reseller id
     * @return the list
     */
    public List<Long> findResellerTerminalIds(Long resellerId) {
        Terminal terminal = new Terminal();
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", new Reseller(resellerId)));
        final List<Long> list = new ArrayList<>();
        dao.findResellerTerminalIds(terminal, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    /**
     * Summarize terminal qty summary.
     *
     * @param resellerId the reseller id
     * @param merchantId the merchant id
     * @param modelIds   the model ids
     * @return the terminal qty summary
     */
    public TerminalQtySummary summarizeTerminalQty(Long resellerId, Long merchantId, Set<Long> modelIds) {
        TerminalQtySummary result = new TerminalQtySummary();
        Reseller reseller = resellerService.get(resellerId);
        if (reseller == null) {
            return result;
        }

        Terminal terminal = new Terminal();
        terminal.setReseller(reseller);
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        terminal.setMerchant(new Merchant(merchantId));
        terminal.setModelIdsFilter(modelIds);
        ArrayList<Map<String, Object>> resultList = dao.summarizeTerminalQtyByStatus(terminal);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (Map<String, Object> mapEntry : resultList) {
                String status = (String) mapEntry.get("status");
                long count = safeGetLong(mapEntry.get("count"));
                switch (status) {
                    case TerminalStatus.ACTIVE:
                        result.setNumOfActive(count);
                        break;
                    case TerminalStatus.PENDING:
                        result.setNumOfInactive(count);
                        break;
                    case TerminalStatus.SUSPEND:
                        result.setNumOfDisabled(count);
                        break;
                    default:
                        break;
                }
            }
        }
        if (BooleanUtils.isTrue(getCurrentMarket().getAllowMpush())) {
            terminal.setStatus(TerminalStatus.ACTIVE);
            resultList = dao.summarizeTerminalQtyByOnlineOffline(terminal);
            if (!CollectionUtils.isEmpty(resultList)) {
                for (Map<String, Object> mapEntry : resultList) {
                    Object onlineStatus = mapEntry.get("onlineStatus");
                    long count = safeGetLong(mapEntry.get("count"));
                    if (onlineStatus == null || !NumberUtils.isDigits(onlineStatus.toString())) {
                        continue;
                    }
                    switch (Integer.parseInt(onlineStatus.toString())) {
                        case TerminalOnlineStatus.ONLINE:
                            result.setNumOfOnline(count);
                            break;
                        case TerminalOnlineStatus.OFFLINE:
                            result.setNumOfOffline(count);
                            break;
                        case TerminalOnlineStatus.UNAVAILABLE:
                            result.setNumOfUnavailable(count);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return result;
    }

    private long safeGetLong(Object obj) {
        if (obj != null) {
            if (obj instanceof Long) {
                return (Long) obj;
            }
            if (obj instanceof Integer) {
                return ((Integer) obj).longValue();
            }
            try {
                return Long.parseLong((String) obj);
            } catch (java.lang.NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }

    /**
     * Summarize terminal qty by merchant terminal qty top ten by merchant.
     *
     * @param resellerId the reseller id
     * @param merchantId the merchant id
     * @param modelIds   the model ids
     * @return the terminal qty top ten by merchant
     */
    public TerminalQtyTopTenByMerchant summarizeTerminalQtyByMerchant(Long resellerId, Long merchantId, Set<Long> modelIds) {
        TerminalQtyTopTenByMerchant result = new TerminalQtyTopTenByMerchant();
        Reseller reseller = resellerService.get(resellerId);
        if (reseller == null) {
            return result;
        }
        Terminal terminal = new Terminal();
        terminal.setModelIdsFilter(modelIds);
        terminal.setReseller(reseller);
        terminal.setMerchant(new Merchant(merchantId));
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        ArrayList<Map<Object, Object>> resultList = dao.summarizeTop10TerminalQtyByMerchant(terminal);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (Map<Object, Object> mapEntry : resultList) {
                long mid = safeGetLong(mapEntry.get("merchantId"));
                long count = safeGetLong(mapEntry.get("count"));
                Merchant merchant = merchantService.get(mid);
                if (merchant != null) {
                    MerchantInfo info = new MerchantInfo();
                    info.setId(merchant.getId());
                    info.setEmail(merchant.getEmail());
                    info.setName(merchant.getName());
                    if (merchant.getReseller() != null) {
                        info.setReseller(new ResellerInfo());
                        info.getReseller().setId(merchant.getReseller().getId());
                    }
                    result.add(info, count);
                }
            }
        }
        return result;
    }

    /**
     * Summarize terminal qty by merchant terminal qty top ten by merchant.
     *
     * @param resellerId the reseller id
     * @param modelId    the model id
     * @return the terminal qty top ten by merchant
     */
    public TerminalQtyTopTenByReseller summarizeTerminalQtyByReseller(Long resellerId, Set<Long> modelId) {
        TerminalQtyTopTenByReseller result = new TerminalQtyTopTenByReseller();
        Reseller reseller = resellerService.get(resellerId);
        if (reseller == null) {
            return result;
        }
        Terminal terminal = new Terminal();
        terminal.setModelIdsFilter(modelId);
        terminal.setReseller(reseller);
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller(), true));
        ArrayList<Map<Object, Object>> resultList = dao.summarizeTop10TerminalQtyByReseller(terminal);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (Map<Object, Object> mapEntry : resultList) {
                long rid = safeGetLong(mapEntry.get("resellerId"));
                long count = safeGetLong(mapEntry.get("count"));
                reseller = resellerService.get(rid);
                if (reseller != null) {
                    ResellerInfo info = new ResellerInfo();
                    info.setId(reseller.getId());
                    info.setEmail(reseller.getEmail());
                    info.setName(reseller.getName());
                    result.add(info, count);
                }
            }
        }
        return result;
    }


    /**
     * Update terminal network.
     *
     * @param terminalId the terminal id
     * @param network    the network
     */
    @MasterDs
    public void updateTerminalNetwork(Long terminalId, String network) {
        if (!StringUtils.equals(network, dao.getTerminalNetwork(terminalId))) {
            dao.updateTerminalNetwork(terminalId, network);
            clearTerminalCache(new Terminal(terminalId));
        }
    }

    /**
     * Update terminal debug mode.
     *
     * @param terminalId the terminal id
     * @param debugMode  the debug mode
     */
    @MasterDs
    public void updateTerminalDebugMode(Long terminalId, String debugMode) {
        dao.updateTerminalDebugMode(terminalId, debugMode);
        clearTerminalCache(new Terminal(terminalId));
    }

    /**
     * Load offline terminal widget list.
     *
     * @param resellerId                   the reseller id
     * @param merchantId                   the merchant id
     * @param terminalOfflineWidgetQueries the terminal offline widget queries
     * @return the list
     */
    public List<ReportForWidget> loadOfflineTerminalWidget(Long resellerId, Long merchantId, List<TerminalOfflineWidgetQuery> terminalOfflineWidgetQueries) {
        List<ReportForWidget> reportForWidgets = new LinkedList<>();
        List<Long> childResellerIds = resellerService.findAvailableResellerIds(resellerId);
        for (TerminalOfflineWidgetQuery widgetQuery : terminalOfflineWidgetQueries) {
            if (widgetQuery.getMarketId() == null) {
                continue;
            }
            ReportForWidget widget = dao.loadOfflineTerminalWidget(widgetQuery, childResellerIds, merchantId);
            if (widget == null) {
                widget = new ReportForWidget();
            }
            widget.setXKey(widgetQuery.getXKey());
            reportForWidgets.add(widget);
        }

        return reportForWidgets;
    }

    /**
     * Load offline terminal sn list.
     *
     * @param widgetQuery the widget query
     * @param resellerIds the reseller ids
     * @return the list
     */
    public List<OfflineTerminalExportExcelEntity> loadOfflineTerminalSN(TerminalOfflineWidgetQuery widgetQuery, List<Long> resellerIds) {
        return dao.loadOfflineTerminalSN(widgetQuery, resellerIds);
    }

    /**
     * Load enroll terminal details for current month list.
     *
     * @param marketId                the market id
     * @param resellerId              the reseller id
     * @param resellerParentIds       the reseller parent ids
     * @param includeSubReseller      the include sub reseller
     * @param startTerminalId         the start terminal id
     * @param includeDisabledTerminal the include disabled terminal
     * @param limit                   the limit
     * @return the list
     */
    public List<Terminal> findEnrollTerminalListForExport(Long marketId,
                                                          Long resellerId,
                                                          String resellerParentIds,
                                                          boolean includeSubReseller,
                                                          Long startTerminalId,
                                                          Boolean includeDisabledTerminal,
                                                          int limit) {
        Date firstActivatedBefore = DateUtils.beginOfMonth(new Date());
        Date lastActivatedBefore = DateUtils.addMonths(firstActivatedBefore, 1);

        return dao.loadEnrollTerminalDetailsForCurrentMonth(
                marketId,
                resellerId,
                resellerParentIds,
                includeSubReseller,
                firstActivatedBefore,
                lastActivatedBefore,
                includeDisabledTerminal,
                startTerminalId,
                limit);
    }

    /**
     * Gets enroll terminal count for current month.
     *
     * @param market             the market
     * @param resellerId         the reseller id
     * @param includeSubReseller the include sub reseller
     * @return the enroll terminal count for current month
     */
    public Integer getEnrollTerminalCountForCurrentMonth(Market market, Long resellerId,
                                                         boolean includeSubReseller) {
        Date firstActivatedBefore = DateUtils.beginOfMonth(new Date());
        Date lastActivatedBefore = DateUtils.addMonths(firstActivatedBefore, 1);
        boolean includeDisabledTerminal = StringUtils.equals(MarketBillingMode.EMEA_MODE, market.getBillingMode())
                || StringUtils.equals(MarketBillingMode.NIGERIA_MODE, market.getBillingMode());
        int limit = 100000;
        Long lastTerminalId = null;
        Set<String> uniqueSerialNos = new HashSet<>();
        while (true) {
            List<Terminal> terminals = dao.getEnrollTerminalCountForCurrentMonth(market.getId(),
                    resellerId,
                    includeSubReseller,
                    firstActivatedBefore,
                    lastActivatedBefore,
                    includeDisabledTerminal,
                    lastTerminalId,
                    limit);
            if (CollectionUtils.isEmpty(terminals)) {
                break;
            }
            lastTerminalId = terminals.get(terminals.size() - 1).getId();
            terminals = terminals.stream().filter(terminal -> isTerminalAccessed(terminal, market.getBillingMode())).collect(Collectors.toList());
            for (Terminal terminal : terminals) {
                if (uniqueSerialNos.contains(terminal.getSerialNo())) {
                    continue;
                }
                uniqueSerialNos.add(terminal.getSerialNo());
            }
        }
        return Math.toIntExact(uniqueSerialNos.size());
    }

    private boolean isTerminalAccessed(Terminal terminal, String billMode) {
        terminalAccessTimeService.loadTerminalLastAccessTime(terminal);
        boolean isGeideaModel = MarketBillingMode.GEIDEA_MODE.equals(billMode);
        Date beginOfNextMonth = DateUtils.addMonths(DateUtils.beginOfMonth(new Date()), 1);
        return isGeideaModel ? (terminal.getLastAccessTime() != null && !DateUtils.addMonths(terminal.getLastAccessTime(), SystemPropertyHelper.getGeideaAccessMonths()).before(beginOfNextMonth))
                : terminal.getLastAccessTime() != null;
    }

    /**
     * Gets number terminal printer out of paper.
     *
     * @param terminal the terminal
     * @return the count
     */
    public Integer getTerminalPOPAlarmCount(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getTerminalPOPAlarmCount(terminal);
    }


    /**
     * Gets number terminal tampered
     *
     * @param terminal the terminal
     * @return the count
     */
    public Integer getTerminalTamperAlarmCount(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getTerminalTamperAlarmCount(terminal);
    }


    /**
     * Gets count.
     *
     * @param terminal the terminal
     * @return the count
     */
    public Integer getTerminalOGFAlarmCount(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getTerminalOGFAlarmCount(terminal);
    }

    /**
     * Gets terminal locked count
     *
     * @return the count
     */
    public Integer getTerminalLockedCount() {
        Terminal terminal = new Terminal();
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getTerminalLockedCount(terminal);
    }

    /**
     * Find locked terminal list
     *
     * @param terminal the terminal
     * @return the list
     */
    public List<Terminal> findLockedTerminalListForExport(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        List<Terminal> list = dao.findLockedTerminalListForExport(terminal);
        loadDetails(list);
        return list;
    }


    /**
     * Get terminal by serialNo for quick search
     *
     * @param terminal the terminal
     * @return the terminal
     */
    public Terminal getBySerialNoForQuickSearch(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getBySerialNoForQuickSearch(terminal);
    }


    /**
     * 导入终端使用：根据SN或者TID查找终端，只取其一
     * 不校验SN/TID是否匹配
     *
     * @param serialNo the serial no
     * @param tid      the tid
     * @return the terminal
     */
    public Terminal getTerminalBySnOrTidForImport(String serialNo, String tid) {
        Terminal terminalSn = null;
        Terminal terminalTid = null;
        if (StringUtils.isNotBlank(serialNo)) {
            terminalSn = getBySerialNo(serialNo);
        }
        if (StringUtils.isNotBlank(tid)) {
            terminalTid = getByTID(tid);
        }

        return terminalSn != null ? terminalSn : terminalTid;
    }


    /**
     * Find group terminal ids for push task list.
     *
     * @param terminal the terminal
     * @return the dynamic group terminal count for push task
     */
    public List<Long> findGroupTerminalIdsForPushTask(Terminal terminal) {
        final List<Long> list = new ArrayList<>();
        dao.findGroupTerminalIdsForPushTask(terminal, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    /**
     * Find dynamic group terminal ids for push task list.
     *
     * @param terminal the terminal
     * @return the group terminal count for push task
     */
    public List<Long> findDynamicGroupTerminalIdsForPushTask(Terminal terminal) {
        final List<Long> list = new ArrayList<>();
        dao.findDynamicGroupTerminalIdsForPushTask(terminal, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    /**
     * Is terminal exist by reseller boolean.
     *
     * @param resellerId the reseller id
     * @param status     the status
     * @return the boolean
     */
    public boolean isTerminalExist4Reseller(Long resellerId, String status) {
        Integer count = dao.getTerminalCount(resellerId, null, status, null);
        return count > 0;
    }

    /**
     * Is terminal exist by merchant boolean.
     *
     * @param merchant the merchant
     * @param status   the status
     * @return the boolean
     */
    public boolean isTerminalExist4Merchant(Merchant merchant, String status) {
        return dao.getTerminalCount(merchant.getResellerId(), merchant.getId(), status, null) > 0;
    }

    /**
     * Gets terminal count.
     *
     * @param merchant the merchant
     * @return the terminal count
     */
    public int getTerminalCount4Merchant(Merchant merchant) {
        return dao.getTerminalCount(merchant.getResellerId(), merchant.getId(), null, merchant.getModelIds());
    }

    /**
     * Find terminal ids by merchant
     *
     * @param merchantId the merchant id
     * @return the terminal ids
     */
    public List<Long> findTerminalIdsByMerchant(Long merchantId) {
        return dao.findTerminalIdsByMerchant(merchantId);
    }

    /**
     * Find terminal model count report list.
     *
     * @param currentMarketId the current market id
     * @param resellerIds     the reseller ids
     * @param merchantId      the merchant id
     * @param limit           the limit
     * @return the list
     */
    public List<ModelDashboardReportForWidget> findTerminalModelCountReport(Long currentMarketId, List<Long> resellerIds, Long merchantId, Integer limit) {
        if (currentMarketId == null) {
            return null;
        }
        return dao.findTerminalModelCountReport(currentMarketId, resellerIds, merchantId, limit);
    }

    /**
     * Gets limit push force update terminal ids.
     *
     * @param clientApk the client apk
     * @param modelIds  the model ids
     * @param marketId  the market id
     * @return the limit push force update terminal ids
     */
    public Set<Long> findTerminalIdsForClientUpdate(ClientApk clientApk, Set<Long> modelIds, Long marketId, List<Long> resellerIds) {
        final Set<Long> list = new HashSet<>();
        dao.findTerminalIdsForClientUpdate(clientApk.getVersionCode(), modelIds, marketId, resellerIds, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    public Set<Long> findTerminalIdsForClientUpdate(ClientApk clientApk, Set<Long> modelIds, Long marketId) {
        return findTerminalIdsForClientUpdate(clientApk, modelIds, marketId, null);
    }

    /**
     * find market online terminal
     *
     * @param marketId the market id
     * @return set set
     */
    public List<String> findMarketOnlineTerminalIds(Long marketId) {
        return dao.findMarketOnlineTerminalIds(marketId);
    }

    /**
     * Find reseller online terminal ids list.
     *
     * @param reseller            the reseller
     * @param isAccessoryTerminal the is accessory terminal
     * @param modelIds            the model ids
     * @return the list
     */
    public List<String> findResellerOnlineTerminalIds(Reseller reseller, Boolean isAccessoryTerminal, Set<Long> modelIds) {
        reseller.setAccessoryTerminal(isAccessoryTerminal);
        reseller.setModelIds(modelIds);
        return dao.findResellerOnlineTerminalIds(reseller);
    }

    /**
     * Find merchant online terminal ids set.
     *
     * @param merchantIds         the merchant ids
     * @param isAccessoryTerminal the is accessory terminal
     * @param modelIds            the model ids
     * @return the set
     */
    public List<String> findMerchantOnlineTerminalIds(Set<Long> merchantIds, Boolean isAccessoryTerminal, Set<Long> modelIds) {
        return dao.findMerchantOnlineTerminalIds(merchantIds, isAccessoryTerminal, modelIds);
    }

    /**
     * Find merchant out of range terminal ids list.
     *
     * @param merchantIds the merchant ids
     * @return the list
     */
    public List<String> findMerchantOutOfRangeTerminalIds(Set<Long> merchantIds) {
        return dao.findMerchantOutOfRangeTerminalIds(merchantIds);
    }

    /**
     * Find current terminal ids list.
     *
     * @param terminalIds the terminal ids
     * @return the list
     */
    public List<Long> findCurrentTerminalIds(List<Long> terminalIds) {
        return dao.findCurrentTerminalIds(terminalIds, getCurrentMarketId());
    }

    /**
     * Find terminal list list.
     *
     * @param terminalIds the terminal ids
     * @return the list
     */
    public List<Terminal> findTerminalList(List<Long> terminalIds) {
        return dao.findByIdList(terminalIds, getCurrentMarketId());
    }


    public Integer getTerminalLowCoinBatteryAlarmCount(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getTerminalLowCoinBatteryAlarmCount(terminal);
    }

    public Integer getTerminalBatteryUnHealthAlarmCount(Terminal terminal) {
        terminal.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", terminal.getReseller()));
        return dao.getTerminalBatteryUnHealthAlarmCount(terminal);
    }
}
