/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain.event.listener;


import com.google.common.eventbus.Subscribe;
import com.pax.api.eventbus.EventListener;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.event.*;
import com.pax.market.event.MaxIdLoadEvent;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.support.dynamic.datasource.tools.PaxDsUtils;
import com.paxstore.market.domain.service.MaxIdService;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.service.sandbox.SandboxTerminalService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.market.domain.service.terminal.TerminalChannelMappingService;
import com.paxstore.market.domain.service.terminal.TerminalGroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * The type Pax market domain event listener.
 */
@Component
public class MarketDomainEventListener implements EventListener {

    private static final Logger log = LoggerFactory.getLogger(MarketDomainEventListener.class);


    /**
     * On reseller changed event.
     *
     * @param event the event
     */
    @Subscribe
    public void onResellerChangedEvent(ResellerChangedEvent event) {
        if (event.getReseller() == null) {
            return;
        }
        log.debug("ResellerChangedEvent trigged, event=\"{}\"", event);
        ResellerService.clearResellerCache(event.getReseller());
    }

    /**
     * On merchant changed event.
     *
     * @param event the event
     */
    @Subscribe
    public void onMerchantChangedEvent(MerchantChangedEvent event) {
        if (event.getMerchant() == null) {
            return;
        }
        log.debug("MerchantChangedEvent trigged, event=\"{}\"", event);
        MerchantService.clearMerchantCache(event.getMerchant());
    }

    /**
     * On terminal changed event.
     *
     * @param event the event
     */
    @Subscribe
    public void onTerminalChangedEvent(TerminalChangedEvent event) {
        if (event.getTerminal() == null) {
            return;
        }
        log.debug("TerminalChangedEvent trigged, event=\"{}\"", event);
        MarketTerminalService.clearTerminalCache(event.getTerminal());
    }

    /**
     * On terminal group changed event.
     *
     * @param event the event
     */
    @Subscribe
    public void onTerminalGroupChangedEvent(TerminalGroupChangedEvent event) {
        if (event.getTerminalGroup() == null) {
            return;
        }
        log.debug("TerminalGroupChangedEvent trigged, event=\"{}\"", event);
        TerminalGroupService.clearTerminalGroupCache(event.getTerminalGroup());
    }

    @Subscribe
    public void onTerminalChannelMappingEvent(TerminalChannelMappingChangedEvent event) {
        log.debug("TerminalChannelMappingChangedEvent trigged, event=\"{}\"", event);
        if (event.getTerminalId() != null) {
            TerminalChannelMappingService.clearTerminalChannelMappingCache(event.getTerminalId());
        }
        if (CollectionUtils.isNotEmpty(event.getTerminalIds())) {
            TerminalChannelMappingService.clearTerminalChannelMappingsCache(event.getTerminalIds());
        }
    }

    /**
     * On sandbox terminal changed event
     *
     * @param event the event
     */
    @Subscribe
    public void onSandboxTerminalChangedEvent(SandboxTerminalChangedEvent event) {
        if (event.getSandboxTerminal() == null) {
            return;
        }
        log.debug("SandboxTerminalChangedEvent trigged, event=\"{}\"", event);
        SandboxTerminalService.clearTerminalCache(event.getSandboxTerminal());
    }
}
