package com.paxstore.market.domain.service.rki;

import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.rki.RKIConfigLoader;
import com.pax.market.framework.common.utils.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class RKIConfigLoaderImpl implements RKIConfigLoader {
    @Override
    public int getRkiServerConnnTimeout() {
        return SystemPropertyHelper.getRkiConnectionTimeout();
    }

    @Override
    public int getRkiRequestTimeout() {
        return SystemPropertyHelper.getRkiRequestTimeout();
    }

    @Override
    public String getAuthSystemUrl() {
        return SystemPropertyHelper.getRkiAuthSystemUrl();
    }

    @Override
    public String getPlatformId() {
        return SystemPropertyHelper.getRkiAuthSystemPlatformId();
    }

    @Override
    public String getSignKey() {
        return SystemPropertyHelper.getRkiAuthSystemSecret();
    }

    @Override
    public String getReversalSystemUrl() {
        if (StringUtils.isBlank(getAuthSystemUrl())) {
            return null;
        }

        return SystemPropertyHelper.getRkiAuthSystemUrl().replace("pre-auth-deduction", "reversal-request");
    }
}
