package com.paxstore.market.domain.service.terminal;

import com.pax.api.cache.CacheService;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.rki.RkiBatchBindRecord;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.domain.entity.market.terminal.TerminalTask;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.service.BaseService;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.mq.producer.gateway.push.TerminalActionUpdateGateway;
import com.paxstore.integration.mq.message.TerminalActionUpdateMessage;
import com.paxstore.market.domain.dao.terminal.TerminalTaskDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * The type Terminal task service.
 */
@Service
public class TerminalTaskService extends BaseService {
    @Autowired
    private TerminalTaskDao dao;
    @Autowired
    private TerminalActionHistoryService terminalActionHistoryService;
    @Autowired(required = false)
    private TerminalActionUpdateGateway terminalActionUpdateGateway;
    @Autowired
    private CacheService cacheService;


    /**
     * Update terminal action.
     *
     * @param marketId   the market id
     * @param terminalId the terminal id
     * @param actionId   the action id
     * @param status     the status
     * @param errorCode  the error code
     * @param remarks    the remarks
     */
    public void updateTerminalAction(Long marketId, Long terminalId, Long actionId, int status, int errorCode, String remarks) {
        TerminalActionUpdateMessage terminalActionUpdateMessage = new TerminalActionUpdateMessage();
        terminalActionUpdateMessage.setMarketId(marketId);
        terminalActionUpdateMessage.setActionId(actionId);
        terminalActionUpdateMessage.setTerminalId(terminalId);
        terminalActionUpdateMessage.setStatus(status);
        terminalActionUpdateMessage.setErrorCode(errorCode);
        terminalActionUpdateMessage.setRemarks(remarks);
        updateTerminalAction(terminalActionUpdateMessage);
    }

    /**
     * Update terminal action.
     *
     * @param terminalActionUpdateMessage the terminal action update message
     */
    private void updateTerminalAction(TerminalActionUpdateMessage terminalActionUpdateMessage) {
        String cacheKey = String.valueOf(terminalActionUpdateMessage.getActionId());
        Integer actionStatus = (Integer) cacheService.get(CacheNames.TERMINAL_ACTION_CACHE, cacheKey);
        if (IntegerUtils.equals(terminalActionUpdateMessage.getStatus(), actionStatus)) {
            return;
        } else {
            cacheService.put(CacheNames.TERMINAL_ACTION_CACHE, cacheKey, terminalActionUpdateMessage.getStatus());
        }
        terminalActionUpdateMessage.setTerminalPlatform(OsType.TRADITIONAL);
        terminalActionUpdateGateway.send(terminalActionUpdateMessage);
    }

    /**
     * Update terminal RKI task result
     * Result source: RKI Server / Terminal RKI Client
     *
     * @param actionType       the action type
     * @param referenceId      the reference id
     * @param terminal         the terminal
     * @param isSuccess        the is success
     */
    public void updateTerminalRkiTask(int actionType, Long referenceId, TerminalRegistry terminal, boolean isSuccess) {
        int downloadStatus = TerminalActionStatus.FAILED;
        int errorCode = TerminalActionErrorCode.DOWNLOAD_ERROR;
        String remarks = MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.DOWNLOAD_RKI_KEY_FAILED));
        if (isSuccess) {
            downloadStatus = TerminalActionStatus.SUCCEED;
            errorCode = 0;
            remarks = MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.DOWNLOAD_RKI_KEY_SUCCESS));
        }
        int defaultErrorCode = TerminalActionErrorCode.DOWNLOAD_ERROR;
        //update RKI push action status
        if (referenceId > 0) {
            String resultFrom = "(from RKI server)";
            //查找是否有进行中的RKI推送任务
            List<TerminalTask> terminalTaskList = dao.findPendingTerminalRkiTasks(terminal.getId());
            if (CollectionUtils.isNotEmpty(terminalTaskList)) {
                String defaultRemarks = MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.EXIST_COMPLETE_DOWNLOAD_RKI_TASK));
                for (TerminalTask terminalTask : terminalTaskList) {
                    if (LongUtils.equals(terminalTask.getReferenceId(), referenceId) && terminalTask.getActionType() == actionType) {
                        //是同一个任务，则直接改状态
                        updateTerminalAction(terminal.getMarketId(), terminal.getId(), terminalTask.getActionId(), downloadStatus, errorCode, remarks + resultFrom);
                    } else {
                        //否则改为失败
                        updateTerminalAction(terminal.getMarketId(), terminal.getId(), terminalTask.getActionId(), TerminalActionStatus.FAILED, defaultErrorCode, defaultRemarks);
                    }
                }
            }
        }
    }

}
