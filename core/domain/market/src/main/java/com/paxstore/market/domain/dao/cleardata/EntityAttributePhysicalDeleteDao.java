package com.paxstore.market.domain.dao.cleardata;

import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.domain.entity.market.attribute.EntityAttribute;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface EntityAttributePhysicalDeleteDao extends CrudDao<EntityAttribute> {
    List<Long> findDeletedEntityAttributeIds(@Param("marketId") Long marketId, @Param("deleteDays") Integer deleteDays);
    void deleteEntityAttribute(@Param("entityAttributeId") Long entityAttributeId);
    void deleteEntityAttributeLabel(@Param("entityAttributeId") Long entityAttributeId);
    void deleteEntityAttributeValue(@Param("entityAttributeId") Long entityAttributeId);

}
