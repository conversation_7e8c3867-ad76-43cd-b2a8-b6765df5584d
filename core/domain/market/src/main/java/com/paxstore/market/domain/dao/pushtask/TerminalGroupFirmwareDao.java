/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain.dao.pushtask;

import com.pax.market.domain.entity.market.pushtask.TerminalGroupFirmware;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The interface Terminal firmware dao.
 */
@MyBatisDao
public interface TerminalGroupFirmwareDao extends BasePushTaskDao<TerminalGroupFirmware> {

    /**
     * Find list by firmware id list.
     *
     * @param firmwareId the firmware id
     * @return the list
     */
    List<TerminalGroupFirmware> findListByFirmwareId(@Param("firmwareId") Long firmwareId);

    int countByFirmwareId(@Param("firmwareId")Long firmwareId);

    List<TerminalGroupFirmware> findNoneCompletePushTask(TerminalGroupFirmware groupFirmware);
}
