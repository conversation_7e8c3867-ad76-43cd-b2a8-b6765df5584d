/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain;

import com.pax.market.domain.entity.global.app.App;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Domain context holder.
 */
public final class DomainContextHolder {
    private static final ThreadLocal<DomainContextHolder> domainContextThreadLocal = new ThreadLocal<>();

    private Map<String, App> appMap;

    /**
     * Gets app map.
     *
     * @return the app map
     */
    public static Map<String, App> getAppMap() {
        if (domainContextThreadLocal.get() == null) {
            init();
        }
        return domainContextThreadLocal.get().appMap;
    }

    /**
     * Init.
     */
    public static void init() {
        DomainContextHolder ctx = new DomainContextHolder();
        ctx.appMap = new HashMap<>();
        domainContextThreadLocal.set(ctx);
    }

    /**
     * Remove domain context.
     */
    public static void removeDomainContext() {
        domainContextThreadLocal.remove();
    }
}