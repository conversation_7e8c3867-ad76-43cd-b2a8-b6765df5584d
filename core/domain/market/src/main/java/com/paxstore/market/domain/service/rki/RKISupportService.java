package com.paxstore.market.domain.service.rki;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.TerminalActionType;
import com.pax.market.domain.entity.market.pushtask.BasePushTaskEntity;
import com.pax.market.domain.entity.market.pushtask.TerminalGroupRki;
import com.pax.market.domain.entity.market.pushtask.TerminalRki;
import com.pax.market.domain.entity.market.rki.RkiDeductionRecord;
import com.pax.market.domain.entity.market.rki.RkiDeductionTerminal;
import com.pax.market.domain.entity.market.rki.RkiReversalRecord;
import com.pax.market.domain.entity.market.rki.RkiReversalTerminal;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalGroup;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.rki.authsystem.AuthSystemResultCodes;
import com.pax.market.dto.rki.authsystem.PreDeductionResponse;
import com.pax.market.dto.rki.authsystem.ReversalBalanceResponse;
import com.pax.market.dto.rki.rkiserver.RkiCustomerInfo;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.logback.CustomLoggers;
import com.pax.market.framework.common.rki.PaxAuthSystemService;
import com.pax.market.framework.common.rki.RKIException;
import com.pax.market.framework.common.rki.RkiBillingIgnoreException;
import com.pax.market.framework.common.service.AbstractService;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.RandomUtil;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.market.domain.service.terminal.TerminalGroupService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.pax.market.constants.ApiCodes.DEDUCTION_NOT_FOUND;

/**
 * The type Rki support.
 */
@Component
@RequiredArgsConstructor
public class RKISupportService extends AbstractService {
    private static final Logger RkiLog = CustomLoggers.RkiLog;

    private final PaxAuthSystemService paxAuthSystemService;
    private final MarketTerminalService terminalService;
    private final TerminalGroupService groupService;
    private final RkiDeductionService deductionService;
    private final RkiReversalService reversalService;

    /**
     * Check pre auth deduction pre deduction response.
     *
     * @param pushTask  the push task
     * @param pushCount the push count
     * @return the pre deduction response
     */
    public PreDeductionResponse checkPreAuthDeduction(BasePushTaskEntity<?> pushTask, int pushCount) {
        String remarks = null;
        String taskId = RandomUtil.generateUpperMixString(16);
        PreDeductionResponse checkResult = PreDeductionResponse.of(AuthSystemResultCodes.success);
        if (SystemPropertyHelper.getRkiAuthSystemEnabled()) {
            boolean configIsReady = StringUtils.isNotEmpty(SystemPropertyHelper.getRkiAuthSystemUrl())
                    && StringUtils.isNotEmpty(SystemPropertyHelper.getRkiAuthSystemPlatformId())
                    && StringUtils.isNotEmpty(SystemPropertyHelper.getRkiAuthSystemSecret());
            if (configIsReady) {
                checkResult = preAuthDeduction(taskId, pushTask.getMarketId(), pushTask.getResellerId(), pushCount, false);
                if (!AuthSystemResultCodes.allowedContinueCodes.contains(checkResult.getResult())) {
                    remarks = "The error occurred at the pre-deduction stage: " + checkResult.getJointMsg();
                    if (StringUtils.equals(AuthSystemResultCodes.systemConnErr, checkResult.getResult())) {
                        remarks = checkResult.getJointMsg();
                    } else if (!StringUtils.equals(AuthSystemResultCodes.balanceIsInsufficient, checkResult.getResult())) {
                        remarks = "Pre auth system error: " + checkResult.getResult() + ":" + checkResult.getMsg() + checkResult.getJointMsg();
                        RkiLog.warn("An error occurred when createPendingTerminalActions for group push. The PreAuthDeductionResponse:{}", remarks);
                    }
                }
            }
        }
        //保存预扣记录，失败时可以允许推送
        if (AuthSystemResultCodes.allowedContinueCodes.contains(checkResult.getResult()) && checkResult.isNeedCreateDeduction()) {
            RkiDeductionRecord deductionRecord = deductionService.buildDeductionRecord(taskId, pushCount, pushTask);
            if (deductionRecord != null) {
                //回填扣费操作记录id
                deductionService.saveDeductionRecord(deductionRecord, checkResult);
            }
            loadRkiDeductionRecordId(pushTask, deductionRecord, checkResult.getResult());
        }
        if (StringUtils.equals(AuthSystemResultCodes.ignoreRkiBillingErr, checkResult.getResult())){
            checkResult.setResult(AuthSystemResultCodes.success);
            checkResult.setMsg("Success");
        }
        checkResult.setRemarks(remarks);
        return checkResult;
    }

    public void loadRkiDeductionRecordId(BasePushTaskEntity<?> pushTask, RkiDeductionRecord deductionRecord, String resultCode){
        if (deductionRecord == null || AuthSystemResultCodes.ignoreRkiBillingErr.equals(resultCode)){
            pushTask.setDeductionId(SystemConstants.DUMMY_DEDUCTION_ID);
        }else {
            pushTask.setDeductionId(deductionRecord.getId());
        }
    }

    /**
     * Pre auth deduction pre deduction response.
     *
     * @param taskId       the task id
     * @param marketId     the market id
     * @param resellerId   the reseller id
     * @param pushCount    the push count
     * @param isThrowError the is throw error
     * @return the pre deduction response
     */
    public PreDeductionResponse preAuthDeduction(String taskId, Long marketId, Long resellerId, Integer pushCount, boolean isThrowError) {
        PreDeductionResponse response = new PreDeductionResponse();
        if (!SystemPropertyHelper.getRkiAuthSystemEnabled() || Objects.isNull(pushCount) || pushCount <= 0) {
            response.setResult(AuthSystemResultCodes.success);
            return response;
        }

        RkiCustomerInfo customerInfo = new RkiCustomerInfo();
        customerInfo.setMarketId(marketId);
        customerInfo.setResellerId(resellerId);
        try {
            response = paxAuthSystemService.preAuthDeduction(taskId, customerInfo, String.valueOf(pushCount));
            if (Objects.isNull(response)){
                throw new RkiBillingIgnoreException(String.valueOf(ApiCodes.RKI_BILLING_PRE_AUTH_DEDUCTION_RESPONSE_NULL));
            }
            response.setNeedCreateDeduction(true);
        } catch (RkiBillingIgnoreException ex) {
            response.setResult(AuthSystemResultCodes.ignoreRkiBillingErr);
            response.setMsg(MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.RKI_BILLING_PRE_AUTH_DEDUCTION_FAILED)));
            response.setNeedCreateDeduction(true);
        } catch (RKIException e) {
            if (isThrowError) {
                throw new RKIException(e);
            }
            response.setResult(AuthSystemResultCodes.systemConnErr);
            response.setExtMsg1(MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.TERMINAL_RKI_CREATE_FAILED_PAX_AUTH_SYSTEM_ERR)));
        }
        return response;
    }

    /***
     * 终端推送RKI
     * 仅退费
     * @param terminalRki the terminal rki
     */
    public void reversalSingle(TerminalRki terminalRki) {
        RkiDeductionTerminal terminal = new RkiDeductionTerminal();
        terminal.setTerminalId(terminalRki.getTerminalId());
        terminal.setActionType(TerminalActionType.DOWNLOAD_TERMINAL_RKI);
        terminal.setReferenceId(terminalRki.getId());
        RkiDeductionTerminal result = deductionService.getAvailableByTerminalAndReferenceId(terminal);
        if (result == null) {
            throw new BusinessException(DEDUCTION_NOT_FOUND);
        }

        reversalSingle(result.getDeductionId(), terminalRki);
    }

    /***
     * 终端推送RKI
     * 退费+解绑
     * @param deductionId the deduction id
     * @param terminalRki the terminal rki
     */
    public void reversalSingle(Long deductionId, TerminalRki terminalRki) {
        doReversalLogic(deductionId, terminalRki, null, terminalRki.getTerminalId());
    }

    /***
     * 分组推送RKI
     * 退费+解绑
     * @param deductionId the deduction id
     * @param groupRki the group rki
     * @param terminalId the terminal id
     */
    public void reversalGroup(Long deductionId, TerminalGroupRki groupRki, Long terminalId) {
        doReversalLogic(deductionId, null, groupRki, terminalId);
    }

    /**
     * Do reversal logic.
     *
     * @param deductionId 扣费请求Id
     * @param terminalRki 终端RKI
     * @param groupRki    分组RKI
     * @param terminalId  the terminal id
     */
    private void doReversalLogic(Long deductionId, TerminalRki terminalRki, TerminalGroupRki groupRki, Long terminalId) {
        RkiDeductionRecord deductionRecord = validateReversalLogicParam(deductionId, terminalRki, groupRki);
        if (Objects.isNull(deductionRecord)) return;

        //进行退费
        RkiCustomerInfo customerInfo = new RkiCustomerInfo();
        RkiReversalRecord reversalRequest = new RkiReversalRecord();
        RkiReversalTerminal terminalDetail  = new RkiReversalTerminal();
        loadRkiReversalAndTMRef(terminalRki, groupRki, customerInfo,
                reversalRequest, terminalDetail, deductionId, terminalId);


        String paymentToken = deductionRecord.getPaymentToken();
        //解决无限制扣费问题
        if (StringUtils.isBlank(paymentToken)) {
            RkiLog.warn("**skipped for deductionId={}, because paymentToken is null**", deductionId);
            reversalRequest.setOperationResult(AuthSystemResultCodes.ignoreRkiBillingErr);
            reversalRequest.setOperationMsg(MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.RKI_BILLING_REVERSAL_PAYMENT_TOKEN_NULL)));
            reversalService.save(reversalRequest, terminalDetail);
            return;
        }else {
            reversalRequest.setPaymentToken(paymentToken);
        }

        ReversalBalanceResponse reversalResponse = new ReversalBalanceResponse();
        try {
            //先调用第三方外部系统发起退费
            reversalResponse = paxAuthSystemService.reversalBalance(customerInfo, reversalRequest.getTaskId(), paymentToken, 1);
        } catch (Exception e) {
            reversalResponse.setResult(AuthSystemResultCodes.ignoreRkiBillingErr);
            reversalResponse.setErrMsg(MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.RKI_BILLING_REVERSAL_REQUEST_INVALID)));
        }

        if (!AuthSystemResultCodes.allowedContinueCodes.contains(reversalResponse.getResult())
                && StringUtils.isBlank(reversalResponse.getErrMsg())) {
            reversalResponse.setErrMsg(MessageUtils.getEnglishMessage(String.valueOf(ApiCodes.RKI_BILLING_REVERSAL_REQUEST_INVALID)));
        }

        reversalRequest.setOperationMsg(reversalResponse.getErrMsg());
        reversalRequest.setOperationResult(reversalResponse.getResult());
        reversalService.save(reversalRequest, terminalDetail);
    }

    private RkiDeductionRecord validateReversalLogicParam(Long deductionId, TerminalRki terminalRki, TerminalGroupRki groupRki){
        if (deductionId == null || (terminalRki == null && groupRki == null)) {
            if (deductionId == null) {
                RkiLog.warn("****reversal skip**: deductionId is null");
            }
            if (terminalRki == null) {
                RkiLog.warn("****reversal skip**: terminalRki is null");
            }
            if (groupRki == null) {
                RkiLog.warn("****reversal skip**: groupRki is null");
            }
            return null;
        }

        RkiDeductionRecord deductionRecord = deductionService.get(deductionId);
        if (deductionRecord == null) {
            RkiLog.warn("**deductRecord.id={} not found**", deductionId);
            return null;
        }
        return deductionRecord;
    }
    private void loadRkiReversalAndTMRef(TerminalRki terminalRki,
                                         TerminalGroupRki groupRki,
                                         RkiCustomerInfo customerInfo,
                                         RkiReversalRecord reversalRequest,
                                         RkiReversalTerminal terminalDetail,
                                         Long deductionId,
                                         Long terminalId){
        Long marketId;
        Long resellerId;
        Long referenceId;
        int actionType;
        if (terminalRki != null) {
            Terminal terminal = terminalService.getIncludeDeleted(terminalRki.getTerminalId());
            marketId = terminal.getMarketId();
            resellerId = terminal.getResellerId();
            actionType = TerminalActionType.DOWNLOAD_TERMINAL_RKI;
            referenceId = terminalRki.getId();
        } else {
            TerminalGroup terminalGroup = groupService.getIncludeDeleted(groupRki.getGroup().getId());
            marketId = terminalGroup.getMarketId();
            resellerId = terminalGroup.getResellerId();
            actionType = TerminalActionType.DOWNLOAD_GROUP_RKI;
            referenceId = groupRki.getId();
        }
        customerInfo.setMarketId(marketId);
        customerInfo.setResellerId(resellerId);
        String taskId = RandomUtil.generateUpperMixString(16);

        reversalRequest.setReferenceId(referenceId);
        reversalRequest.setActionType(actionType);
        reversalRequest.setQuantity(1);
        reversalRequest.setOperationDate(new Date());
        reversalRequest.setTaskId(taskId);
        reversalRequest.setDeductionId(deductionId);
        terminalDetail.setTerminalId(terminalId);

    }

    /**
     * Gets deduction record.
     *
     * @param referenceId the reference id
     * @param actionType  the action type
     * @return the deduction record
     */
    public RkiDeductionRecord getDeductionRecord(Long referenceId, int actionType) {
        return deductionService.getByReferenceIdAndActionType(referenceId, actionType);
    }



    /**
     * Save deduction record.
     *
     * @param deductionRecord the deduction record
     * @param checkResult     the check result
     */
    public void saveDeductionRecord(RkiDeductionRecord deductionRecord, PreDeductionResponse checkResult) {
        deductionService.saveDeductionRecord(deductionRecord, checkResult);
    }

    public void saveDeductionRecord(PreDeductionResponse checkResult, BasePushTaskEntity<?> pushTask,String taskId, int pushCount ) {
        if (checkResult.isNeedCreateDeduction()) {
            RkiDeductionRecord deductionRecord = deductionService.buildDeductionRecord(taskId, pushCount, pushTask);
            deductionService.saveDeductionRecord(deductionRecord, checkResult);
            loadRkiDeductionRecordId(pushTask, deductionRecord, checkResult.getResult());
        }
    }

    /**
     * Save deduction terminal.
     *
     * @param terminalList the terminal list
     */
    public void saveDeductionTerminal(List<RkiDeductionTerminal> terminalList) {
        deductionService.saveDeductionTerminal(terminalList);
    }

    /**
     * Save deduction terminal.
     *
     * @param pushTask    the push task
     * @param terminalIds the terminal ids
     */
    public void saveDeductionTerminal(BasePushTaskEntity<?> pushTask, List<Long> terminalIds) {
        if (LongUtils.isNotBlankAndPositive(pushTask.getDeductionId()) && SystemPropertyHelper.getRkiAuthSystemEnabled()){
            deductionService.saveDeductionTerminal(convert2DeductionTerminalList(pushTask, terminalIds));
        }
    }

    private List<RkiDeductionTerminal> convert2DeductionTerminalList(BasePushTaskEntity<?> pushTask, List<Long> terminalIdList) {
        return terminalIdList.stream().map(r -> {
            RkiDeductionTerminal deductionTerminal = new RkiDeductionTerminal();
            deductionTerminal.setTerminalId(r);
            deductionTerminal.setDeductionId(pushTask.getDeductionId());
            return deductionTerminal;
        }).collect(Collectors.toList());
    }

}
