/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.market.domain.dao.terminal;

import com.pax.market.dto.terminal.TerminalOnlineStatusInfo;
import com.pax.market.framework.common.persistence.BaseDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;
import java.util.Set;

/**
 * 终端DAO接口
 *
 * <AUTHOR>
 * @version 2016 -09-08
 */
@MyBatisDao
public interface TerminalOnlineStatusDao extends BaseDao {

    /**
     * Find all terminal online status info list.
     *
     * @param handler the handler
     */
    void findAllTerminalOnlineStatusInfo(@Param("start") int start, @Param("limit") int limit, ResultHandler<TerminalOnlineStatusInfo> handler);

    /**
     * Find terminal online status info list.
     *
     * @param terminalIds the terminal ids
     * @return the list
     */
    List<TerminalOnlineStatusInfo> findTerminalOnlineStatusInfoList(@Param("terminalIds") Set<Long> terminalIds);

    /**
     * Batch create terminal online status.
     *
     * @param terminalIds  the terminal ids
     * @param onlineStatus the online status
     */
    void batchCreateTerminalOnlineStatus(@Param("terminalIds") Set<Long> terminalIds, @Param("onlineStatus") Integer onlineStatus);

    /**
     * Batch update terminal online status
     *
     * @param terminalIds  the terminal Id set
     * @param onlineStatus tht online status
     */
    void batchUpdateTerminalOnlineStatus(@Param("terminalIds") Set<Long> terminalIds, @Param("onlineStatus") Integer onlineStatus);

    /**
     * find all online terminal ids
     *
     * @param handler the handler
     * @return list set
     */
    List<String> findAllOnlineTerminalIds(ResultHandler<String> handler);

    /**
     * Get terminal online status and date
     *
     * @param id the id
     * @return terminal realtime info
     */
    TerminalOnlineStatusInfo getTerminalOnlineStatusInfo(@Param("id") Long id);


    /**
     * Create terminal online status.
     *
     * @param terminalOnlineStatusInfo the terminal online status info
     */
    void createTerminalOnlineStatus(TerminalOnlineStatusInfo terminalOnlineStatusInfo);

    /**
     * Update terminal online status.
     *
     * @param terminalOnlineStatusInfo the terminal online status info
     */
    void updateTerminalOnlineStatus(TerminalOnlineStatusInfo terminalOnlineStatusInfo);

    /**
     * Delete terminal online status.
     *
     * @param terminalIds the terminal ids
     */
    void deleteTerminalOnlineStatus(@Param("terminalIds") List<Long> terminalIds);

}