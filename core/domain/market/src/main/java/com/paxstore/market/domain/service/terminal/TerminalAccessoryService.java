package com.paxstore.market.domain.service.terminal;

import com.pax.api.cache.CacheService;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalAccessory;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent.EventType;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventAccumulator;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.market.domain.DataScopeFilter;
import com.paxstore.market.domain.dao.terminal.TerminalAccessoryDao;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: Zhou Dong
 * @Date: 2022/4/26 18:08
 */
@Service
@RequiredArgsConstructor
public class TerminalAccessoryService extends CrudService<TerminalAccessoryDao, TerminalAccessory> {
    public static final String RESELLER_CACHE = "r_";
    public static final String MERCHANT_CACHE = "m_";

    private final MarketTerminalService marketTerminalService;
    private final CacheService cacheService;
    private final ResellerService resellerService;

    public TerminalAccessory getTerminalAccessoryBySN(Long terminalId, String type, String serialNo) {
        return dao.getTerminalAccessoryBySN(terminalId, type, serialNo);
    }

    @MasterDs
    public void deleteAccessory(TerminalAccessory accessory) {
        delete(accessory);
        clearAccessoryTypeCache(accessory.getTerminalId());
        if (!isExistTerminalAccessory(accessory.getTerminalId(), null, null)) {
            EntityChangedEventAccumulator.add(EventType.TERMINAL_ACCESSORY_CHANGED, Collections.singletonList(accessory.getTerminalId()));
        }
    }

    private void clearAccessoryTypeCache(Long terminalId) {
        Terminal terminal = marketTerminalService.getIncludeDeleted(terminalId);
        if (terminal == null) return;
        resellerService.loadDetails(terminal.getReseller());

        clearAccessoryTypeCache(terminal.getMerchantId(), terminal.getReseller().getParentIds() + terminal.getResellerId());
    }

    public static void clearAccessoryTypeCache(Long merchantId, String resellerParentIds) {
        CacheService cacheService = SpringContextHolder.getApplicationContext().getBean(CacheService.class);
        if (merchantId != null) {
            cacheService.remove(String.format("%s:%s", CacheNames.TERMINAL_ACCESSORY_TYPE_CACHE, MERCHANT_CACHE + merchantId));
        }
        if (StringUtils.isNotBlank(resellerParentIds)) {
            Arrays.asList(StringUtils.split(resellerParentIds, ",")).forEach(resellerId -> {
                cacheService.remove(String.format("%s:%s", CacheNames.TERMINAL_ACCESSORY_TYPE_CACHE, RESELLER_CACHE + resellerId));
            });
        }
    }

    public List<String> findAccessoryTypeList(TerminalAccessory accessory) {
        String cacheKey;
        if (accessory.getReseller() != null) {
            cacheKey = RESELLER_CACHE + accessory.getReseller().getId();
        } else {
            cacheKey = MERCHANT_CACHE + accessory.getMerchantId();
        }
        String accessTypes = (String)cacheService.get(String.format("%s:%s", CacheNames.TERMINAL_ACCESSORY_TYPE_CACHE, cacheKey));
        List<String> accessTypeList = new ArrayList<>();
        if (StringUtils.isBlank(accessTypes)) {
            accessory.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", accessory.getReseller()));
            accessTypeList = dao.findTerminalAccessoryType(accessory);
            accessTypes = StringUtils.join(accessTypeList, ",");
            if (StringUtils.isBlank(accessTypes)) {
                accessTypes = "null";
            }
            cacheService.put(String.format("%s:%s", CacheNames.TERMINAL_ACCESSORY_TYPE_CACHE, cacheKey), accessTypes);
        } else {
            if (!StringUtils.equals(accessTypes, "null")) {
                accessTypeList = Arrays.asList(accessTypes.split(","));
            }
        }
        return accessTypeList;
    }

    public List<TerminalAccessory> findList(TerminalAccessory accessory) {
        accessory.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", accessory.getReseller()));
        return dao.findList(accessory);
    }

    public Integer getCount(TerminalAccessory accessory) {
        accessory.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", accessory.getReseller()));
        return dao.getCount(accessory);
    }

    public List<TerminalAccessory> findListForExport(TerminalAccessory accessory) {
        accessory.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", accessory.getReseller()));
        final List<TerminalAccessory> list = new ArrayList<>();
        dao.findListForExport(accessory, resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    public List<TerminalAccessory> findTerminalAccessoryList(Long terminalId) {
        return dao.findTerminalAccessoryList(terminalId);
    }

    public boolean isExistTerminalAccessory(Long terminalId, Reseller reseller, Long merchantId) {
        return BooleanUtils.isTrue(dao.existTerminalAccessory(terminalId, reseller, merchantId));
    }

    public TerminalAccessory validateTerminalAccessory(Long accessoryId) {
        TerminalAccessory terminalAccessory = get(accessoryId);
        if (terminalAccessory == null) {
            throw new BusinessException(ApiCodes.TERMINAL_ACCESSORY_NOT_EXIST);
        }
        if (marketTerminalService.get(terminalAccessory.getTerminalId()) == null) {
            throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
        }
        return terminalAccessory;
    }

    public ArrayList<Map<String, Object>> summarizeAccessoryQtyByType(TerminalAccessory accessory) {
        accessory.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", accessory.getReseller()));
        return dao.summarizeAccessoryQtyByType(accessory);
    }

    public ArrayList<Map<String, Object>> summarizeAccessoryQtyByModel(TerminalAccessory accessory) {
        accessory.getSqlMap().put("dsf", DataScopeFilter.filterX(getCurrentUser(), "a", accessory.getReseller()));
        return dao.summarizeAccessoryQtyByModel(accessory);
    }

    public Set<String> findAccessoryTypeList(Long marketId, Reseller reseller) {
        return dao.findAccessoryTypeList(marketId, reseller);
    }
}
