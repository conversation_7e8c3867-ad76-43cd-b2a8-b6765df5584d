package com.paxstore.domain.support.xa;

import com.pax.market.domain.entity.global.emm.EmmZteRecord;
import com.pax.market.domain.entity.market.emm.EmmZteDeviceRecord;
import com.pax.support.dynamic.datasource.aspectj.XATransactional;
import com.paxstore.global.domain.service.emm.EmmZteRecordService;
import com.paxstore.market.domain.service.emm.EmmZteDeviceRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/24
 */
@RequiredArgsConstructor
@Component
public class EmmZteRecordSupport {

    private final EmmZteRecordService zteRecordService;
    private final EmmZteDeviceRecordService zteDeviceRecordService;

    @XATransactional
    public void createEmmZteRecord(EmmZteRecord zteRecord, List<EmmZteDeviceRecord> deviceRecordList) {
        zteRecordService.save(zteRecord);
        zteDeviceRecordService.batchCreateZteDeviceRecords(deviceRecordList);
    }

    @XATransactional
    public void updateEmmZteRecordFailedResult(Long zteRecordId, List<EmmZteDeviceRecord> deviceRecordList) {
        zteDeviceRecordService.batchUpdateFailedReason(deviceRecordList);
        zteRecordService.removeBatchOperationId(zteRecordId);
    }

}
