package com.pax.market.billing;

import com.pax.support.http.HttpClient;
import com.pax.support.http.HttpInvoker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

public abstract class BaseBillingService {
    @Autowired
    private HttpClient httpClient;

    @Value("${billing-api.base-url}")
    public String billingApiBaseUrl;


    public HttpInvoker getHttpInvoker() {
        return HttpInvoker.newInstance(httpClient);
    }
}
