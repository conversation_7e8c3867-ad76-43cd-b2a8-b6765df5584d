package com.pax.market.core.integration.terminalV2;

import com.paxstore.integration.mq.message.EvictCacheMessage;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DoNothingTerminalServiceV2CacheEvictor implements TerminalServiceV2CacheEvictor {

	@Override
	public void evict(EvictCacheMessage msg) {
		if(log.isDebugEnabled()) {
			log.debug(">>>>>>> received EvictCacheMessage for TerminalServiceV2 cache evictor: {}", msg);			
		}
	}
}
