/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.report.validator;

import com.pax.core.exception.BusinessException;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.ReportEngineConstants;
import com.pax.market.dto.UserInfo;
import com.pax.market.framework.common.utils.date.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

@Service
public class TerminalRkiParameterValidator extends AbstractReportParameterValidator {

    @Override
    public boolean validate(Long reportId, Map<String, Object> parameters, UserInfo userInfo) {

        String browserTz = String.valueOf(parameters.get(ReportEngineConstants.SpecialParameter.KEY_CURRENT_TZ));
        Date actionTimeFrom = convertDateTypeParameter((Date) parameters.get(ReportEngineConstants.SpecialParameter.KEY_ACTION_TIME_FROM), browserTz);
        Date actionTimeTo = convertDateTypeParameter((Date) parameters.get(ReportEngineConstants.SpecialParameter.KEY_ACTION_TIME_TO), browserTz);
        //开始时间确保在一年以内
        Date lastYear = DateUtils.lastYear(new Date());
        if (actionTimeFrom == null || actionTimeFrom.before(lastYear)) {
            actionTimeFrom = lastYear;
        }
        if (actionTimeFrom != null && actionTimeTo != null && (actionTimeFrom.getTime() >= actionTimeTo.getTime())) {
            throw new BusinessException(ApiCodes.END_DATE_MUST_AFTER_START_DATE);
        }
        if (actionTimeFrom != null && actionTimeFrom.before(DateUtils.lastYear(new Date()))) {
            throw new BusinessException(ApiCodes.END_DATE_TO_START_DATE_MUST_LESS_THAN_SEVEN_DAYS);
        }
        return true;
    }

}
