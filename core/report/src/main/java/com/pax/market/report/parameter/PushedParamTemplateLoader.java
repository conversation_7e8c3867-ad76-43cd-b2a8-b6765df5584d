/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.report.parameter;

import com.google.common.collect.Sets;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.searchcriteria.TerminalApkParamReportSearchCriteria;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.response.report.ParameterSourceItem;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.market.domain.DataScopeFilter;
import com.paxstore.market.domain.service.report.ReportDataSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Component("pushedParamTemplateLoader")
public class PushedParamTemplateLoader implements ParameterSourceLoader {
    @Autowired
    private ApkService apkService;
    @Autowired
    private ReportDataSourceService reportDataSourceService;

    @Override
    public List<ParameterSourceItem> loadParameterSource(Long currentMarketId, UserInfo currentUser, String parameter, String dependValue) {
        List<ParameterSourceItem> sourceItems = new LinkedList<>();
        if (StringUtils.isEmpty(dependValue)) {
            return sourceItems;
        }
        //这里可能包含版本信息，第一个是应用id，后面的是版本号
        dependValue = cleanDependValue(dependValue);
        List<Long> dependValueList = StringUtils.splitToLongList(dependValue, ",");
        if (dependValueList.isEmpty()) {
            return sourceItems;
        }
        Long apkId = dependValueList.get(0);
        Apk apk = apkService.get(apkId);
        if (apk != null && currentUser.getCurrentReseller() != null) {
            TerminalApkParamReportSearchCriteria searchCriteria = new TerminalApkParamReportSearchCriteria();
            searchCriteria.setAppId(apk.getAppId());
            searchCriteria.setCurrentMarketId(currentMarketId);
            if (LongUtils.isNotBlankAndPositive(currentUser.getCurrentReseller().getParentId())) {
                searchCriteria.getSqlMap().put("dsf", DataScopeFilter.filter(currentUser, "a"));
            }
            if (dependValueList.size() > 1) {
                //排除第一个，后面的是版本号
                dependValueList.remove(0);
                searchCriteria.setVersionCodes(Sets.newHashSet(dependValueList));
            }
            searchCriteria.setApkIdList(apkService.findApkIdsByAppIdAndVersionCodes(searchCriteria.getAppId(), searchCriteria.getVersionCodes()));
            List<String> templateNameList = reportDataSourceService.findPushedParamTemplateNameList(searchCriteria);
            Collections.sort(templateNameList);
            for (String paramTemplateName : templateNameList) {
                ParameterSourceItem sourceItem = new ParameterSourceItem();
                sourceItem.setLabel(paramTemplateName);
                sourceItem.setValue(paramTemplateName);
                sourceItems.add(sourceItem);
            }
        }
        return sourceItems;
    }

    private static String cleanDependValue(String dependValue) {
        if (!dependValue.contains(",")) {
            return dependValue;
        }
        int firstCommaIndex = dependValue.indexOf(',');
        String firstPart = dependValue.substring(0, firstCommaIndex);
        String secondPart = dependValue.substring(firstCommaIndex + 1);

        // 移除所有 null
        List<String> secondParts = Arrays.asList(secondPart.split(","));
        List<String> filtered = secondParts.stream()
                .filter(s -> !s.trim().equalsIgnoreCase("null"))
                .collect(Collectors.toList());

        return filtered.isEmpty() ?
                firstPart :
                firstPart + "," + String.join(",", filtered);
    }

}
