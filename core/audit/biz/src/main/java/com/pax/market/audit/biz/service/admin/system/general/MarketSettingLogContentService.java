/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.system.general;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.constants.MarketSettingKey;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.market.MarketSetting;
import com.pax.market.domain.entity.global.rki.MarketRkiServerConfig;
import com.paxstore.global.domain.service.rki.MarketRkiConfigService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.market.MarketSettingService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.admin.general.setting.MarketSettingLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MarketSettingLogContentService extends BaseLogContentService {

    private final MarketSettingService marketSettingService;
    private final MarketService marketService;
    private final MarketRkiConfigService marketRkiConfigService;

    @Override
    public int getDataType() {
        return AuditDataTypes.MARKET_SETTING;
    }

    @Override
    public BaseLog getChangeRecord(Long id) {
        if (isCurrentUserInRootReseller()) {
            MarketSettingLog marketSettingLog = new MarketSettingLog();
            Market market = marketService.get(getCurrentMarketId());
            if (nonNull(market)) {
                marketSettingLog.setMarketName(market.getName());
                marketSettingLog.setMarketEmail(market.getAdmin().getEmail());
                marketSettingLog.setMarketExpireDate(market.getExpireDate());
                marketSettingLog.setDevTerminalLimit(getCurrentMarket().getDevTerminalLimit());
                MarketRkiServerConfig rkiServer = marketRkiConfigService.getMarketRkiServer(getCurrentMarketId());
                marketSettingLog.setRkiServerName(nonNull(rkiServer) ? rkiServer.getName() : null);
            }
            marketSettingLog.setMarketSettingList(getMarketSettings(getCurrentMarketId()));
            if (!LongUtils.equals(getCurrentMarketId(), SystemConstants.SUPER_MARKET_ID)) {
                marketSettingLog.setDefaultMarketSettingList(getMarketSettings(SystemConstants.SUPER_MARKET_ID));
            }
            return marketSettingLog;
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        return null;
    }

    private List<MarketSettingLog.Setting> getMarketSettings(Long marketId) {
        List<MarketSetting> marketSettingList = marketSettingService.getMarketSettings(marketId);
        boolean hasAllowActiveTerminalKey = false;
        for (MarketSetting marketSetting : marketSettingList) {
            if (!SystemPropertyHelper.isAllowResellerTidSetting() && StringUtils.equals(marketSetting.getKey(), MarketSettingKey.tidGeneratePolicy.getValue()) &&
                    BooleanUtils.isTrue(StringUtils.toBoolean(marketSetting.getValue()))) {
                marketSetting.setValue("false");
            }
            if (StringUtils.equals(MarketSettingKey.allowActiveTerminal.getValue(), marketSetting.getKey())) {
                hasAllowActiveTerminalKey = true;
            }
        }
        //针对子市场没设置过则默认true
        if (!hasAllowActiveTerminalKey) {
            MarketSetting marketSetting  = new MarketSetting();
            marketSetting.setMarketId(marketId);
            marketSetting.setKey(MarketSettingKey.allowActiveTerminal.getValue());
            marketSetting.setValue("true");
            marketSettingList.add(marketSetting);
        }
        return BeanMapper.mapList(marketSettingList, MarketSettingLog.Setting.class);
    }
}
