/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.system.general;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.market.terminal.TerminalTIDSetting;
import com.paxstore.market.domain.service.terminal.TerminalTIDSettingService;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.admin.general.setting.TerminalTIDSettingLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TidSettingLogContentService extends BaseLogContentService {

    private final TerminalTIDSettingService terminalTIDSettingService;

    @Override
    public int getDataType() {
        return AuditDataTypes.TID;
    }

    @Override
    public BaseLog getChangeRecord(Long id) {
        List<TerminalTIDSetting> tidSettingList = terminalTIDSettingService.findTIDSettingList(getCurrentReseller().getId());
        if (CollectionUtils.isNotEmpty(tidSettingList)) {
            boolean isSubReseller = getCurrentReseller() != null && getCurrentReseller().getParentId() != null;
            return TerminalTIDSettingLog.of(BeanMapper.mapList(tidSettingList, TerminalTIDSettingLog.TerminalTIDLog.class),
                    isSubReseller ? !terminalTIDSettingService.isSettingExist(getCurrentResellerId()) : null);
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        return null;
    }
}
