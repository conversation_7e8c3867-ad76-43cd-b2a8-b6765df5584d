/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.terminal.profile;

import com.pax.core.json.JsonMapper;
import com.pax.market.audit.biz.service.admin.BaseProfileLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.constants.ProductTypeUtils;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.search.NameLog;
import com.pax.market.dto.audit.log.admin.ProfileLog;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.dto.request.profile.ProfileCreateRequest;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.pax.market.framework.common.mapper.BeanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class MerchantProfileLogContentService extends BaseProfileLogContentService {


    private final MerchantService merchantService;

    @Override
    public int getDataType() {
        return AuditDataTypes.MERCHANT_PROFILE;
    }

    @Override
    public ProfileLog getChangeRecord(Map<String, String> params, ApiAuditContext context) {
        String request = context.getQueryVariables().get("requestData");
        if (StringUtils.isBlank(request)) {
            return null;
        }
        ProfileCreateRequest requestData = JsonMapper.fromJsonString(request, ProfileCreateRequest.class);
        if (isNull(requestData)) return null;
        String productType = requestData.getProductType();
        Long merchantId = LongUtils.parse(params.get("merchantId"));
        if (LongUtils.isNotBlankAndPositive(merchantId) && StringUtils.isNotBlank(productType)) {
            loadAction2(productType,context);
            Merchant merchant = merchantService.get(merchantId);
            if (ProductTypeUtils.isAccessoryProfile(productType)){
               if (nonNull(merchant)){
                   return getAsyProfileLog(merchantId, SystemConstants.PROFILE_TYPE_MERCHANT, productType, true);
               }
           }else {
                if (nonNull(merchant)){
                    return getProfileLog(merchantId,SystemConstants.PROFILE_TYPE_MERCHANT, productType, true);
                }
           }
        }
        return  null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        Merchant merchant = merchantService.get(id);
        if (Objects.nonNull(merchant)) {
            BasicInfoLog basicInfoLog = new BasicInfoLog();
            basicInfoLog.setMerchant(BeanMapper.map(merchant, BasicInfoLog.MerchantLog.class));
            return info.setBasicInfo(basicInfoLog).setSearchField(NameLog.of(merchant.getName()));
        }
        return null;
    }
}
