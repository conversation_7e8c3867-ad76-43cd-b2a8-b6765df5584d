/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.terminal;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.market.pushtask.TerminalFirmware;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.paxstore.global.domain.service.firmware.FirmwareService;
import com.paxstore.market.domain.service.pushtask.TerminalFirmwareService;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.request.terminal.TerminalFirmwareCreateRequest;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TerminalFirmwareLogContentService extends BaseLogContentService {

    private final TerminalFirmwareService terminalFirmwareService;
    private final MarketTerminalService marketTerminalService;
    private final TerminalRegistryService terminalRegistryService;
    private final FirmwareService firmwareService;

    @Override
    public int getDataType() {
        return AuditDataTypes.TERMINAL_FM;
    }

    @Override
    public BaseLog getChangeRecord(Long id) {
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        TerminalFirmware terminalFirmware = terminalFirmwareService.getWithoutCheck(id);
        if (nonNull(terminalFirmware)) {
            return getBasicInfo(info, terminalFirmware.getTerminalId(), terminalFirmware.getFirmware().getId());
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        if (isBeforeProcess()) {
            return null;
        }
        String terminalId = params.get("terminalId");
        if (StringUtils.isNotBlank(terminalId) && nonNull(context.getRequestBody()) && context.getRequestBody() instanceof TerminalFirmwareCreateRequest) {
            return getBasicInfo(info, Long.valueOf(terminalId), ((TerminalFirmwareCreateRequest) context.getRequestBody()).getFirmwareId());
        }
        return null;
    }

    private BasicInfoAndSearchFieldLog getBasicInfo(BasicInfoAndSearchFieldLog info, Long terminalId, Long firmwareId) {
        Terminal terminal = marketTerminalService.get(terminalId);
        if (nonNull(terminal)) {
            BasicInfoLog basicInfoLog = new BasicInfoLog();
            BasicInfoLog.TerminalLog terminalLog = BeanMapper.map(terminal, BasicInfoLog.TerminalLog.class);
            basicInfoLog.setTerminal(terminalLog);
            loadTerminalLogModel(terminal.getModelId(),basicInfoLog);
            basicInfoLog.setFirmware(BeanMapper.map(firmwareService.get(firmwareId), BasicInfoLog.FirmwareLog.class));
            info.setSearchField(terminalLog);
            return info.setBasicInfo(basicInfoLog);
        }
        return null;
    }
}
