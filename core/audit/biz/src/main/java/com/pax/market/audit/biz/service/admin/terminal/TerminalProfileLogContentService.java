/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.terminal;

import com.pax.market.audit.biz.service.BaseTerminalLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.market.terminal.TerminalProfile;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.admin.TerminalProfileLog;
import com.pax.market.dto.request.terminal.TerminalProfileRequest;
import com.pax.market.framework.common.utils.LongUtils;
import com.paxstore.market.domain.service.terminal.TerminalProfileService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.pax.market.domain.util.WebCryptoUtils.maskPasswordTerminalProfileAndMapperForValue;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TerminalProfileLogContentService extends BaseTerminalLogContentService {

    private final TerminalProfileService terminalProfileService;

    @Override
    public int getDataType() {
        return AuditDataTypes.TERMINAL_PROFILE;
    }


    @Override
    public TerminalProfileLog getChangeRecord(Map<String, String> params, ApiAuditContext context) {
        Long terminalId = LongUtils.parse(params.get("terminalId"));

        if (LongUtils.isNotBlankAndPositive(terminalId) && nonNull(context.getRequestBody())) {
            if (context.getRequestBody() instanceof TerminalProfileRequest profileRequest) {
                if (CollectionUtils.isNotEmpty(profileRequest.getProfileList())){
                    return getTerminalProfileLog(terminalId, profileRequest.getProfileList());
                }
            }
        }
        return null;

    }

    private TerminalProfileLog getTerminalProfileLog(Long terminalId, List<TerminalProfileRequest.Profile> terminalProfileList) {
        TerminalProfileLog terminalProfileLog = new TerminalProfileLog();
        List<TerminalProfileLog.Profile> profileList = new ArrayList<>();
        for (TerminalProfileRequest.Profile profile : terminalProfileList) {
            TerminalProfile terminalProfile = terminalProfileService.getByKey(terminalId, profile.getKey());
            if (terminalProfile !=null){
                maskPasswordTerminalProfileAndMapperForValue(terminalProfile);
                TerminalProfileLog.Profile profileLog = new TerminalProfileLog.Profile();
                profileLog.setKey(terminalProfile.getKey());
                profileLog.setValue(terminalProfile.getValue());
                profileList.add(profileLog);
            }
            terminalProfileLog.setProfileList(profileList);
        }
      return terminalProfileLog;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        return buildTerminalSearchField(info, id);
    }
}
