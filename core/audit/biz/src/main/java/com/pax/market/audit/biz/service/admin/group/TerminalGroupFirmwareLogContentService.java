/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.group;

import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.market.pushtask.TerminalGroupFirmware;
import com.paxstore.global.domain.service.firmware.FirmwareService;
import com.paxstore.global.domain.service.model.ModelService;
import com.paxstore.market.domain.service.pushtask.TerminalGroupFirmwareService;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.search.NameLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TerminalGroupFirmwareLogContentService extends BaseGroupPushLogContentService {

    private final TerminalGroupFirmwareService terminalGroupFirmwareService;
    private final FirmwareService firmwareService;
    private final ModelService modelService;

    @Override
    public int getDataType() {
        return AuditDataTypes.GROUP_FM;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        TerminalGroupFirmware terminalGroupFirmware = terminalGroupFirmwareService.get(id);
        if (nonNull(terminalGroupFirmware)) {
            modelService.loadDetails(terminalGroupFirmware.getModel());
            BasicInfoLog basicInfoLog = new BasicInfoLog();
            basicInfoLog.setGroup(BeanMapper.map(terminalGroupFirmware.getGroup(), BasicInfoLog.GroupLog.class));
            terminalGroupFirmware.setFirmware(firmwareService.getIncludeDeleted(terminalGroupFirmware.getFirmware()));
            basicInfoLog.setFirmware(BeanMapper.map(terminalGroupFirmware.getFirmware(), BasicInfoLog.FirmwareLog.class));
            return info.setBasicInfo(basicInfoLog).setSearchField(NameLog.of(terminalGroupFirmware.getGroup().getName()));
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        if (isBeforeProcess()) {
            return null;
        }
        BasicInfoLog.GroupLog group = getGroup(params);
        if (nonNull(group)) {
            BasicInfoLog basicInfoLog = new BasicInfoLog();
            basicInfoLog.setGroup(group);
            String firmwareId = params.get("firmwareId");
            if (StringUtils.isNotBlank(firmwareId)) {
                basicInfoLog.setFirmware(BeanMapper.map(firmwareService.get(Long.valueOf(firmwareId)), BasicInfoLog.FirmwareLog.class));
            }
            return info.setBasicInfo(basicInfoLog).setSearchField(NameLog.of(group.getName()));
        }
        return null;
    }
}
