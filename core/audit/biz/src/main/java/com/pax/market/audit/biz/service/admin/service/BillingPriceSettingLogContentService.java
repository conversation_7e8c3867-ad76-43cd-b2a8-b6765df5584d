/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.service;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.billing.MarketBillingPriceSettingService;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.billing.MarketBillingType;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.vas.Market2Service;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.domain.entity.global.vas.cloudmsg.CloudMsgTrialInfo;
import com.pax.market.dto.audit.log.admin.vas.AirLinkSubscriptionPlanLog;
import com.pax.market.dto.MarketBillingPriceSettingInfo;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.admin.vas.BillingPriceSettingLog;
import com.pax.market.dto.audit.log.search.NameLog;
import com.pax.market.dto.request.market.billing.MarketBillingPriceSettingRequest;
import com.pax.market.dto.request.market.billing.MarketBillingSettingUpdateRequest;
import com.pax.market.dto.vas.MarketAirLinkSettingInfo;
import com.pax.market.dto.vas.MarketAirLoadSettingInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.service.center.ServiceCenterFunc;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.setting.LicenseService;
import com.paxstore.global.domain.service.vas.Market2ServiceReadonlyService;
import com.paxstore.global.domain.service.vas.VasInfoService;
import com.paxstore.global.domain.service.vas.airload.MarketAirLoadSettingService;
import com.paxstore.global.domain.service.vas.cloudmsg.CloudMsgUsageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BillingPriceSettingLogContentService extends BaseLogContentService {

    private final MarketBillingPriceSettingService marketBillingPriceSettingService;
    private final VasInfoService vasInfoService;
    private final MarketService marketService;
    private final CloudMsgUsageService cloudMsgUsageService;
    private final Market2ServiceReadonlyService market2ServiceService;
    private final LicenseService licenseService;
    private final ServiceCenterFunc serviceCenterFunc;
    private final MarketAirLoadSettingService marketAirLoadSettingService;

    @Override
    public int getDataType() {
        return AuditDataTypes.BILLING_PRICE_SETTING;
    }

    @Override
    public BaseLog getChangeRecord(Long id, ApiAuditContext context) {
        if (nonNull(context) && nonNull(context.getRequestBody()) && context.getRequestBody() instanceof MarketBillingSettingUpdateRequest request) {
            MarketBillingPriceSettingRequest billingPriceSettingRequest = request.getBillingPriceSettingRequest();
            if (nonNull(billingPriceSettingRequest)) {
                MarketBillingPriceSettingInfo marketBillingPriceSetting = null;
                if (isSupported()) {
                    String billMode = null;
                    if (MarketBillingType.from(billingPriceSettingRequest.getServiceType()) == MarketBillingType.TerminalEnrollment) {
                        Market market = marketService.get(id);
                        if (market != null) {
                            billMode = market.getBillingMode();
                        }
                    }
                    marketBillingPriceSetting = marketBillingPriceSettingService.getMarketBillingPriceSetting(id, billingPriceSettingRequest.getServiceType(), billMode);
                }
                if (Objects.nonNull(marketBillingPriceSetting)) {
                    if (!licenseService.getLicenseInfo().isAllowConnectionFeeViolationPolicy()) {
                        marketBillingPriceSetting.setTerminalViolationPolicySetting(null);
                    }
                    if (MarketBillingType.from(billingPriceSettingRequest.getServiceType()) == MarketBillingType.AIR_LOAD) {
                        marketBillingPriceSetting.setFreeUsage(Optional
                                .ofNullable(marketAirLoadSettingService.getByMarketId(id))
                                .map(MarketAirLoadSettingInfo::getFreeUsage)
                                .orElse(0));
                    }
                    if (MarketBillingType.from(billingPriceSettingRequest.getServiceType()) == MarketBillingType.CloudMsg) {
                        marketBillingPriceSetting.setTrialMsgCount(Optional
                                .ofNullable(cloudMsgUsageService.getCloudMsgTrialInfo(id))
                                .map(CloudMsgTrialInfo::getTrialMsgCount)
                                .orElse(0L));
                    }

                    Market2Service market2Service = market2ServiceService.getByMarketIdAndServiceType(id, billingPriceSettingRequest.getServiceType());
                    if (nonNull(market2Service)) {
                        marketBillingPriceSetting.setStartFreeTime(market2Service.getStartFreeTime());
                        marketBillingPriceSetting.setEndFreeTime(market2Service.getEndFreeTime());
                    }
                    return BeanMapper.map(marketBillingPriceSetting, BillingPriceSettingLog.class);
                }
                if (MarketBillingType.from(billingPriceSettingRequest.getServiceType()) == MarketBillingType.AIR_LINK) {
                    MarketAirLinkSettingInfo marketAirLinkSetting = serviceCenterFunc.getMarketAirLinkSetting(id);
                    if (nonNull(marketAirLinkSetting)) {
                        return BeanMapper.map(marketAirLinkSetting, AirLinkSubscriptionPlanLog.class);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        if (nonNull(context) && nonNull(context.getRequestBody()) && context.getRequestBody() instanceof MarketBillingSettingUpdateRequest request) {
            MarketBillingPriceSettingRequest billingPriceSettingRequest = request.getBillingPriceSettingRequest();
            if (nonNull(billingPriceSettingRequest)) {
                BasicInfoLog basicInfoLog = new BasicInfoLog();
                Market market = marketService.get(id);
                if (nonNull(market)) {
                    BasicInfoLog.MarketLog marketLog = BeanMapper.map(market, BasicInfoLog.MarketLog.class);
                    marketLog.setAdminEmail(isNull(market.getAdmin()) ? null : market.getAdmin().getEmail());
                    basicInfoLog.setMarketList(Collections.singletonList(marketLog));
                    info.setSearchField(NameLog.of(market.getDomain()));
                }
                if (StringUtils.equals(billingPriceSettingRequest.getServiceType(), SystemConstants.TERMINAL_ENROLLMENT)) {
                    basicInfoLog.setServiceType(billingPriceSettingRequest.getServiceType());
                    basicInfoLog.setServiceName(SystemConstants.MAXSTORE);
                } else {
                    VasInfo vasInfo = vasInfoService.getByServiceTypeWithoutCheck(billingPriceSettingRequest.getServiceType());
                    if (nonNull(vasInfo)) {
                        basicInfoLog.setServiceName(vasInfo.getServiceName());
                        basicInfoLog.setServiceType(vasInfo.getServiceType());
                    }
                }
                return info.setBasicInfo(basicInfoLog);
            }
        }
        return null;
    }

    private boolean isSupported() {
        return BooleanUtils.isTrue(licenseService.getLicenseInfo().isAllowMarketBilling());
    }

}
