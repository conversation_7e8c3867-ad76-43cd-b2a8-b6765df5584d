/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.service;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.paxstore.global.domain.service.vas.VasInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AgreementVasAgreementLogContentService extends BaseLogContentService {

    private final VasInfoService vasInfoService;

    @Override
    public int getDataType() {
        return AuditDataTypes.AGREE_VAS_AGREEMENT;
    }

    @Override
    public BaseLog getChangeRecord(Long id) {
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        String serviceType = params.get("serviceType");
        if (nonNull(serviceType)) {
            VasInfo vasInfo = vasInfoService.getByServiceType(serviceType);
            if (nonNull(vasInfo)) {
                BasicInfoLog basicInfoLog = new BasicInfoLog();
                basicInfoLog.setServiceName(vasInfo.getServiceName());
                basicInfoLog.setServiceType(vasInfo.getServiceType());
                return info.setBasicInfo(basicInfoLog);
            }
        }
        return super.getBasicInfoOrSearchFieldFromParam(params, context, info);
    }
}
