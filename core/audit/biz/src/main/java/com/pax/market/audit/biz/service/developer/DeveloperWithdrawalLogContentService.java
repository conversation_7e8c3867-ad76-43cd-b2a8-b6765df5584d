/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.developer;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.search.EmailLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;


@RequiredArgsConstructor
@Component
public class DeveloperWithdrawalLogContentService extends BaseLogContentService {


    @Override
    public int getDataType() {
        return AuditDataTypes.DEVELOPER_WITHDRAWAL;
    }


    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        BasicInfoLog basicInfoLog = new BasicInfoLog();
        basicInfoLog.setDeveloper(BeanMapper.map(getCurrentUser().getCurrentDeveloper(), BasicInfoLog.DeveloperLog.class));
        info.setSearchField(EmailLog.of(getCurrentUser().getCurrentDeveloper().getEmail()));
        return info.setBasicInfo(basicInfoLog);
    }
}
