package com.pax.market.audit.biz.service.developer.emm;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.market.emm.EmmApk;
import com.pax.market.domain.entity.market.emm.EmmAppTrack;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.developer.DeveloperApkLog;
import com.pax.market.dto.audit.log.search.NameLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.paxstore.market.domain.service.emm.EmmApkService;
import com.paxstore.market.domain.service.emm.EmmAppTrackService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/4/17
 */
@Component
@RequiredArgsConstructor
public class DeveloperEmmApkLogContentService extends BaseLogContentService {

    private final EmmApkService apkService;
    private final EmmAppTrackService appTrackService;

    @Override
    public int getDataType() {
        return AuditDataTypes.DEVELOPER_EMM_APK;
    }

    @Override
    public BaseLog getChangeRecord(Long id) {
        EmmApk emmApk = apkService.get(id);
        if (nonNull(emmApk)) {
            apkService.loadApkDetail(emmApk);
            DeveloperApkLog developerApkLog = BeanMapper.map(emmApk, DeveloperApkLog.class);
            if (nonNull(emmApk.getApkDetail())) {
                developerApkLog.setAccessUrl(emmApk.getApkDetail().getAccessUrl());
                developerApkLog.setDescription(emmApk.getApkDetail().getDescription());
                developerApkLog.setShortDesc(emmApk.getApkDetail().getShortDesc());
            }
            if (Objects.nonNull(emmApk.getTrackId())) {
                developerApkLog.setTrackAlias(Optional.ofNullable(appTrackService.get(emmApk.getTrackId())).map(EmmAppTrack::getTrackAlias).orElse(null));
            }
            return developerApkLog;
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        EmmApk emmApk = apkService.get(id);
        if (nonNull(emmApk) && LongUtils.equals(emmApk.getDeveloperId(), getCurrentUser().getCurrentDeveloperId())) {
            apkService.generateApkIconScreenshotLink(emmApk);
            BasicInfoLog basicInfoLog = new BasicInfoLog();
            basicInfoLog.setApkList(Collections.singletonList(BeanMapper.map(emmApk, BasicInfoLog.ApkLog.class)));
            return info.setBasicInfo(basicInfoLog).setSearchField(NameLog.of(emmApk.getPackageName()));
        }
        return null;
    }


}
