/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.audit.biz.service.admin.emm;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.global.emm.EmmEnterprise;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.admin.emm.EmmZteCustomerLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.LongUtils;
import com.paxstore.global.domain.service.emm.EmmEnterpriseService;
import com.paxstore.global.domain.service.market.MarketService;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>" rel="nofollow">suyunlong</a>
 * @date 2024/11/22
 */
@Component
@RequiredArgsConstructor
public class EmmZteCustomerLogContentService extends BaseLogContentService {

    private final MarketService marketService;
    private final EmmEnterpriseService emmEnterpriseService;

    @Override
    public int getDataType() {
        return AuditDataTypes.EMM_ZTE_CUSTOMER;
    }
    @Override
    public BaseLog getChangeRecord(Map<String, String> params, ApiAuditContext context) {
        String marketId = params.get("marketId");
        if(StringUtils.isNotBlank(marketId)){
            EmmEnterprise entityForSelect = new EmmEnterprise();
            entityForSelect.setMarketId(LongUtils.parse(marketId));
            EmmEnterprise emmEnterprise = emmEnterpriseService.get(entityForSelect);
            if(Objects.nonNull(emmEnterprise)){
                return BeanMapper.map(emmEnterprise, EmmZteCustomerLog.class);
            }
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        String marketId = params.get("marketId");
        if(StringUtils.isNotBlank(marketId)){
            Market market = marketService.get(LongUtils.parse(marketId));
            BasicInfoLog.MarketLog marketLog = BeanMapper.map(market, BasicInfoLog.MarketLog.class);
            marketLog.setAdminEmail(nonNull(market.getAdmin()) ? market.getAdmin().getEmail() : null);
            info.setBasicInfo(new BasicInfoLog().setMarketList(Collections.singletonList(marketLog)));
            return info;
        }
        return null;
    }
}
