package com.pax.market.audit.biz.service.admin.approval.center;

import com.pax.core.json.JsonMapper;
import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.constants.PushApkType;
import com.pax.market.constants.TerminalActionType;
import com.pax.market.constants.TerminalOperationKeys;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.firmware.Firmware;
import com.pax.market.domain.entity.market.pushtask.*;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.request.market.RejectRequest;
import com.pax.market.dto.terminal.TerminalMessageInfo;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.firmware.FirmwareService;
import com.paxstore.market.domain.service.pushtask.PushTaskUtils;
import com.paxstore.market.domain.service.pushtask.TerminalGroupOperationService;
import com.paxstore.market.domain.service.pushtask.TerminalGroupPushApprovalService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class AdminApprovalCenterLogContentService extends BaseLogContentService {
    private final ApkService apkService;
    private final FirmwareService firmwareService;
    private final TerminalGroupOperationService terminalGroupOperationService;
    private final TerminalGroupPushApprovalService groupPushApprovalService;
    @Override
    public int getDataType() {
        return AuditDataTypes.APPROVAL_CENTER;
    }
    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        String actionType = params.get("actionType");
        String referenceId = params.get("referenceId");
        BasicInfoLog.ApprovalPushTaskAuditLog auditLog = new BasicInfoLog.ApprovalPushTaskAuditLog();
        fillApprovalPushTask(Integer.parseInt(actionType), LongUtils.parse(referenceId), auditLog);
        BasicInfoLog basicInfoLog = new BasicInfoLog();
        TerminalGroupPushApproval groupPushApproval = groupPushApprovalService.getGroupPushApproval(Integer.parseInt(actionType), LongUtils.parse(referenceId));
        if (groupPushApproval != null) {
            auditLog.setType(groupPushApproval.getType());
        }
        if (nonNull(context) && context.getRequestBody() instanceof RejectRequest) {
            basicInfoLog.setRejectReason(((RejectRequest) context.getRequestBody()).getRejectReason());
        }
        basicInfoLog.setApprovalPushTaskAuditLog(auditLog);
        return info.setBasicInfo(basicInfoLog);
    }


    @Override
    public void setAuditType(ApiAuditContext context) {
        if (MapUtils.isNotEmpty(context.getQueryVariables())) {
            int actionType = Integer.parseInt(context.getQueryVariables().get("actionType"));
            String type = context.getQueryVariables().get("type");
            switch (actionType) {
                case TerminalActionType.GROUP_UNINSTALL_APP -> context.setType(AuditTypes.TERMINAL_GROUP_UNINSTALL_APP_PUSH);
                case TerminalActionType.DOWNLOAD_GROUP_FIRMWARE -> context.setType(AuditTypes.TERMINAL_GROUP_FM_PUSH);
                case TerminalActionType.DOWNLOAD_GROUP_RKI -> context.setType(AuditTypes.TERMINAL_GROUP_RKI_PUSH);
                case TerminalActionType.DOWNLOAD_GROUP_APP -> {
                    if (StringUtils.equals(PushApkType.NORMAL, type)) {
                        context.setType(AuditTypes.TERMINAL_GROUP_APP_PUSH);
                    } else if (StringUtils.equals(PushApkType.SOLUTION, type)) {
                        context.setType(AuditTypes.TERMINAL_GROUP_SOLUTION_PUSH);
                    } else if (StringUtils.equals(PushApkType.LAUNCHER, type)) {
                        context.setType(AuditTypes.TERMINAL_GROUP_LAUNCHER_PUSH);
                    }
                }
                case TerminalActionType.GROUP_OPERATION -> {
                    if (StringUtils.equals(type, TerminalOperationKeys.SEND_MSG)) {
                        context.setType(AuditTypes.TERMINAL_GROUP_OPT_PUSH);
                    } else {
                        context.setType(AuditTypes.TERMINAL_GROUP_PUK_PUSH);
                    }
                }
                default -> {

                }

            }
        }
    }

    private void fillApprovalPushTask(int actionType, Long pushTaskId, BasicInfoLog.ApprovalPushTaskAuditLog auditLog) {
        switch (actionType) {
            case TerminalActionType.GROUP_UNINSTALL_APP -> fillGroupUninstallAppAction(actionType, pushTaskId, auditLog);
            case TerminalActionType.DOWNLOAD_GROUP_APP -> fillDownloadGroupAppAction(actionType, pushTaskId, auditLog);
            case TerminalActionType.DOWNLOAD_GROUP_FIRMWARE -> fillDownloadGroupFirmwareAction(actionType, pushTaskId, auditLog);
            case TerminalActionType.DOWNLOAD_GROUP_RKI -> fillDownloadGroupRkiAction(actionType, pushTaskId, auditLog);
            case TerminalActionType.GROUP_OPERATION -> fillGroupOperationAction(actionType, pushTaskId, auditLog);
            default -> {
            }
        }
    }
    

    private void fillGroupUninstallAppAction(int actionType, Long pushTaskId, BasicInfoLog.ApprovalPushTaskAuditLog auditLog) {
        TerminalGroupUninstallApk groupUninstallApk = (TerminalGroupUninstallApk) PushTaskUtils.getPushTaskService(actionType).getIncludeDeleted(pushTaskId);
        if (groupUninstallApk == null) {
            return;
        }
        auditLog.setAppName(groupUninstallApk.getPackageName() + (StringUtils.isNotBlank(groupUninstallApk.getVersionName()) ? "(" + groupUninstallApk.getVersionName() + ")" : ""));
        auditLog.setAppVersion(groupUninstallApk.getVersionName());
        Long apkId = apkService.getApkId(getCurrentMarketId(), groupUninstallApk.getPackageName(), groupUninstallApk.getVersionName(), null);
        if (LongUtils.isNotBlankAndPositive(apkId)) {
            Apk apk = apkService.get(apkId);
            if (apk == null) {
                return;
            }
            auditLog.setAppName(apk.getAppName());
        }
    }

    private void fillDownloadGroupAppAction(int actionType, Long pushTaskId, BasicInfoLog.ApprovalPushTaskAuditLog auditLog) {
        TerminalGroupApk terminalGroupApk = (TerminalGroupApk) PushTaskUtils.getPushTaskService(actionType).getIncludeDeleted(pushTaskId);
        if (terminalGroupApk == null) {
            return;
        }
        Apk apk = apkService.get(terminalGroupApk.getApk());
        auditLog.setAppName(apk.getAppName());
        auditLog.setAppVersion(apk.getVersionName());
        if (terminalGroupApk.getGroupApkParam() != null && StringUtils.equals(terminalGroupApk.getType(), PushApkType.LAUNCHER)) {
            auditLog.setParamTemplateName(terminalGroupApk.getGroupApkParam().getParamTemplateName());
        }
    }

    private void fillDownloadGroupFirmwareAction(int actionType, Long pushTaskId, BasicInfoLog.ApprovalPushTaskAuditLog auditLog) {
        TerminalGroupFirmware terminalGroupFirmware = (TerminalGroupFirmware) PushTaskUtils.getPushTaskService(actionType).getIncludeDeleted(pushTaskId);
        if (terminalGroupFirmware == null) {
            return;
        }
        Firmware firmware = firmwareService.getIncludeDeleted(terminalGroupFirmware.getFirmware());
        if (firmware != null){
            auditLog.setFirmwareName(firmware.getFmName());
        }
    }

    private void fillDownloadGroupRkiAction(int actionType, Long pushTaskId, BasicInfoLog.ApprovalPushTaskAuditLog auditLog) {
        TerminalGroupRki terminalGroupRki = (TerminalGroupRki) PushTaskUtils.getPushTaskService(actionType).getIncludeDeleted(pushTaskId);
        if (terminalGroupRki == null) {
            return;
        }
        auditLog.setRkiKey(terminalGroupRki.getRkiKey());
    }

    private void fillGroupOperationAction(int actionType, Long pushTaskId, BasicInfoLog.ApprovalPushTaskAuditLog auditLog) {
        TerminalGroupOperation terminalGroupOpt = (TerminalGroupOperation) PushTaskUtils.getPushTaskService(actionType).getIncludeDeleted(pushTaskId);
        if (terminalGroupOpt == null) {
            return;
        }
        if (StringUtils.equals(terminalGroupOpt.getKey(), TerminalOperationKeys.SEND_MSG) && JsonMapper.isJsonValue(terminalGroupOpt.getValue())) {
            TerminalMessageInfo terminalMessageInfo = JsonMapper.fromJsonString(terminalGroupOpt.getValue(), TerminalMessageInfo.class);
            auditLog.setOptValue(terminalMessageInfo.getValue());
        } else if (StringUtils.equals(terminalGroupOpt.getKey(), TerminalOperationKeys.PUK_INJECT) && JsonMapper.isJsonValue(terminalGroupOpt.getValue())) {
            auditLog.setOptValue(terminalGroupOperationService.getOptPukNameListValue(terminalGroupOpt.getValue()));
        } else {
            auditLog.setOptValue(terminalGroupOpt.getValue());
        }
        auditLog.setOptKey(terminalGroupOpt.getKey());
    }
}
