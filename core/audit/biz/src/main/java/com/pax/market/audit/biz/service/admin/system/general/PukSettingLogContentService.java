/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.system.general;

import com.pax.core.json.JsonMapper;
import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.constants.SignatureType;
import com.pax.market.domain.entity.global.factory.Factory;
import com.pax.market.domain.entity.global.market.MarketAdvancedSetting;
import com.pax.market.domain.entity.global.market.MarketSignatureConfigServer;
import com.pax.market.framework.common.signature.SignatureConfigProvider;
import com.paxstore.global.domain.service.market.MarketAdvancedSettingService;
import com.paxstore.global.domain.service.market.MarketSignatureConfigService;
import com.paxstore.global.domain.support.signature.SignatureSupportService;
import com.paxstore.global.domain.service.model.FactoryService;
import com.paxstore.global.domain.service.puk.FactoryPukService;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.admin.general.setting.MarketSignatureConfigServerLog;
import com.pax.market.dto.market.MarketSignatureConfigServerInfo;
import com.pax.market.dto.puk.FactoryPukInfo;
import com.pax.market.dto.request.signature.MarketSignatureSettingRequest;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.signature.SignatureProvider;
import com.pax.market.framework.common.signature.SignatureProviderHelper;
import com.pax.market.framework.common.signature.SignatureProviderName;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PukSettingLogContentService extends BaseLogContentService {

    private final FactoryService factoryService;
    private final FactoryPukService factoryPukService;
    private final MarketSignatureConfigService marketSignatureConfigService;
    private final SignatureSupportService signatureSupportService;
    private final MarketAdvancedSettingService marketAdvancedSettingService;

    @Override
    public int getDataType() {
        return AuditDataTypes.PUK_SETTING;
    }

    @Override
    public BaseLog getChangeRecord(Long id, ApiAuditContext context) {
        String request = context.getQueryVariables().get("requestData");
        if (StringUtils.isBlank(request)) {
            return null;
        }
        MarketSignatureSettingRequest requestData = JsonMapper.fromJsonString(request, MarketSignatureSettingRequest.class);
        if (nonNull(requestData)) {
            String signType = Objects.nonNull(requestData.getSignType()) ? requestData.getSignType() : SignatureType.RSA_2048;
            Long factoryId = requestData.getFactoryId();
            Factory factory = factoryService.get(factoryId);
            if (isNull(factory)) {
                return null;
            }
            MarketSignatureConfigServer signatureServer;
            SignatureProviderName factorySignatureProviderName = SignatureProviderName.fromValue(factory.getSignatureProvider());
            if (factorySignatureProviderName == SignatureProviderName.NONE) {
                signatureServer = new MarketSignatureConfigServer();
                signatureServer.setCustomSignature(false);
            } else {
                signatureServer = marketSignatureConfigService.getSignatureConfigServer(getCurrentMarketId(), getCurrentResellerId(), factoryId, requestData.getSignType());
                //pci7配置关闭则paxRhino的配置或者4096也不会返回等于未配置自定义签名-global默认打开custom
                if (BooleanUtils.isFalse(getCurrentMarket().getAllowPci7Signature())
                        && (SignatureType.RSA_4096.equals(signType) || SignatureConfigProvider.PAX_RHINO.val().equals(signatureServer.getProvider()))){
                    signatureServer = new MarketSignatureConfigServer();
                    signatureServer.setProvider(SignatureConfigProvider.CUSTOM.val());
                    if (!isSuperMarket() || !LongUtils.equals(SystemPropertyHelper.getPaxFactoryDefaultId(), factoryId)){
                        signatureServer.setCustomSignature(false);
                    }
                }else {
                    signatureServer.setAccessSecret(StringUtils.mask(signatureServer.getAccessSecret()));
                    signatureServer.setPassword(StringUtils.mask(signatureServer.getPassword()));
                    signatureServer.setCertificate(null);
                    signatureServer.setPublicCertificate(null);
                }
                if (BooleanUtils.isFalse(signatureServer.isCustomSignature()) && SignatureType.RSA_4096.equals(signType)){
                    signatureServer.setProvider(SignatureConfigProvider.PAX_RHINO.val());
                }
            }
            MarketSignatureConfigServerInfo signatureServerInfo = BeanMapper.map(signatureServer, MarketSignatureConfigServerInfo.class);
            MarketAdvancedSetting marketAdvancedSetting = marketAdvancedSettingService.getByMarketId(getCurrentMarketId());
            signatureServerInfo.setFactoryId(factoryId);
            signatureServerInfo.setSignType(signType);
            signatureServerInfo.setPaxPuk(nonNull(marketAdvancedSetting) ? marketAdvancedSetting.getPaxPuk() : Boolean.FALSE);
            signatureServerInfo.setPaxPuk4096(nonNull(marketAdvancedSetting) ? marketAdvancedSetting.getPaxPuk4096() : Boolean.FALSE);
            signatureServerInfo.setFactoryPuk(BeanMapper.map(factoryPukService.getCurrentResellerPuk(getCurrentResellerId(), factoryId, signatureServerInfo.getSignType()), FactoryPukInfo.class));
            signatureServerInfo.setAllowUseGlobalSignature(LongUtils.equals(SystemPropertyHelper.getPaxFactoryDefaultId(), factoryId) && isCurrentUserInRootReseller());
            signatureServerInfo.setSignatureSupport(signatureSupportService.isSignatureSupport(getCurrentMarketId(), factoryId));
            if (factorySignatureProviderName != SignatureProviderName.NONE) {
                SignatureProvider signatureProvider = SignatureProviderHelper.getSignatureProvider(factorySignatureProviderName.val(), signatureServerInfo.getProvider());
                signatureServerInfo.setAuthenticationMode(SignatureProviderHelper.getAuthenticationMode(signatureProvider.getName()));
                loadProviderForUI(factorySignatureProviderName.val(), signatureServerInfo);
            }
            MarketSignatureConfigServerLog map = BeanMapper.map(signatureServerInfo, MarketSignatureConfigServerLog.class);
            map.setIsRootReseller(isCurrentUserInRootReseller());
            return map;
        }
        return super.getChangeRecord(id, context);
    }

    private void loadProviderForUI(String signatureProviderName, MarketSignatureConfigServerInfo signatureServerInfo){
        if (SignatureProviderName.SUPPORT_SIGNATURE_PROVIDERS.contains(signatureProviderName)){
            signatureServerInfo.setProvider(SignatureConfigProvider.CUSTOM.val());
        }
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        MarketSignatureSettingRequest requestData = getParameterValue(request, "requestData", MarketSignatureSettingRequest.class);
        if (nonNull(requestData)) {
            Long factoryId = requestData.getFactoryId();
            Factory factory = factoryService.get(factoryId);
            if (nonNull(factory)) {
                return info.setBasicInfo(new BasicInfoLog().setFactory(BeanMapper.map(factory, BasicInfoLog.FactoryLog.class)));
            }
        }
        return null;
    }
}
