/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.audit.biz.service.admin.service;

import com.pax.market.audit.biz.service.BaseLogContentService;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.domain.entity.global.agreement.VasAgreement;
import com.pax.market.domain.entity.global.vas.VasInfo;
import com.pax.market.dto.audit.log.BaseLog;
import com.pax.market.dto.audit.log.BasicInfoAndSearchFieldLog;
import com.pax.market.dto.audit.log.BasicInfoLog;
import com.pax.market.dto.audit.log.admin.vas.VasAgreementLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.global.domain.service.agreement.VasAgreementService;
import com.paxstore.global.domain.service.vas.VasInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class VasAgreementLogContentService extends BaseLogContentService {

    private final VasAgreementService vasAgreementService;
    private final VasInfoService vasInfoService;

    @Override
    public int getDataType() {
        return AuditDataTypes.VAS_AGREEMENT;
    }

    @Override
    public BaseLog getChangeRecord(Long id) {
        VasAgreement vasAgreement = vasAgreementService.get(id);
        if (nonNull(vasAgreement)) {
            return BeanMapper.map(vasAgreement, VasAgreementLog.class);
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchField(Long id, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        VasAgreement vasAgreement = vasAgreementService.get(id);
        if (nonNull(vasAgreement)) {
            return getBasicInfoAndSearchFieldLog(info, vasAgreement.getServiceType(), vasAgreement.getType());
        }
        return null;
    }

    @Override
    public BasicInfoAndSearchFieldLog getBasicInfoOrSearchFieldFromParam(Map<String, String> params, ApiAuditContext context, BasicInfoAndSearchFieldLog info) {
        String serviceType = params.get("serviceType");
        if (StringUtils.isNotBlank(serviceType)) {
            return getBasicInfoAndSearchFieldLog(info, serviceType, params.get("type"));
        }
        return super.getBasicInfoOrSearchFieldFromParam(params, context, info);
    }

    private BasicInfoAndSearchFieldLog getBasicInfoAndSearchFieldLog(BasicInfoAndSearchFieldLog info, String serviceType, String agreementType) {
        VasInfo vasInfo = vasInfoService.getByServiceTypeWithoutCheck(serviceType);
        BasicInfoLog basicInfoLog = new BasicInfoLog();
        basicInfoLog.setAgreementType(agreementType);
        basicInfoLog.setServiceName(nonNull(vasInfo) ? vasInfo.getServiceName() : null);
        return info.setBasicInfo(basicInfoLog);
    }
}
