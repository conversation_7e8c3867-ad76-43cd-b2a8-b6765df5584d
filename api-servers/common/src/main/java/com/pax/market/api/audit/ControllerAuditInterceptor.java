/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.api.audit;


import java.util.*;

import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.pax.core.json.JsonMapper;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.dto.audit.AuditRecordInfo;
import com.pax.market.framework.common.audit.AuditRecordContext;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.mq.contract.audit.AuditTrailMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.pax.market.framework.common.audit.AuditTrailContext;
import com.pax.market.framework.common.audit.AuditTrailContextHolder;
import com.pax.market.mq.producer.gateway.audit.ApiLogGateway;

/**
 * <AUTHOR>
 * @date 2018-06-29
 */
public class ControllerAuditInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(ControllerAuditInterceptor.class);


    @Autowired
    private ApiLogGateway apiLogGateway;

    @Override
    public void afterCompletion(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, Exception exception)
            throws Exception {
        AuditTrailContext context = AuditTrailContextHolder.getInstance().getCurrent();
        if (context != null) {
            try {
                this.processResponse(exception, Collections.singletonList((ApiAuditContext) context));
            } catch (Throwable e) {
                throw new Exception(e);
            } finally {
                AuditTrailContextHolder.getInstance().clear();
            }
        }

    }

    private void processResponse(Throwable exception, List<ApiAuditContext> contexts) throws Throwable {
        HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();
        for (ApiAuditContext context : contexts) {
            assert response != null;
            context.setHttpCode(response.getStatus());
            logger.trace("[AuditTrailAspect] Send ApiAuditContext to MQ, details: {}", context);
            apiLogGateway.send(convertContext2Message(context));
        }

        if (exception != null) {
            throw exception;
        }
    }

    @Override
    public void postHandle(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, ModelAndView arg3) {

    }

    @Override
    public boolean preHandle(HttpServletRequest req, HttpServletResponse arg1, Object handler) {
        return true;
    }

    private AuditTrailMessage convertContext2Message(ApiAuditContext context) {
        AuditTrailMessage message = new AuditTrailMessage();
        message.setUserId(context.getUserId());
        message.setDeveloperId(context.getDeveloperId());
        message.setCurrentDeveloperId(context.getCurrentDeveloperId());
        message.setTz(RequestLocaleHolder.getLocale());
        message.setDeveloperNickName(context.getDeveloperNickName());
        message.setResellerId(context.getResellerId());
        message.setTerminalId(context.getTerminalId());
        message.setMarketId(context.getMarketId());
        message.setEntityIds(context.getEntityIds());
        message.setAuditAction(context.getAction());
        message.setAuditAction2(context.getAction2() > 0 ? context.getAction2() : null);
        message.setAuditType(context.getType());
        message.setBizCode(context.getBusinessCode());
        message.setMsgArgs(context.getMsgArgs());
        message.setClientIp(context.getClientIp());
        message.setCreatedDate(context.getAuditDate());
        message.setHttpCode(context.getHttpCode());
        message.setRequestUri(context.getRequestUri());
        message.setTimeSpent(System.currentTimeMillis() - context.getAuditDate().getTime());
        message.setUsername(context.getUsername());
        message.setUserLoginName(context.getUserLoginName());
        message.setResellerName(context.getResellerName());
        message.setDataType(context.getDataType());
        message.setBasicInfoMap(context.getBasicInfoMap());
        message.setSearchFields(JsonMapper.toJsonString(context.getSearchFields()));
        message.setQueryVariables(context.getQueryVariables());
        message.setPathVariables(context.getPathVariables());
        if (null != context.getRequestBody()) {
            String requestUri = context.getRequestUri();
            if (StringUtils.isNotBlank(requestUri) && (requestUri.startsWith("v1/3rd") || requestUri.startsWith("v1/internal"))) {
                message.setRequestBody(JsonMapper.alwaysMapper().toJson(context.getRequestBody()));
            } else {
                message.setRequestBody(context.getRequestBody());
            }
        }
        if (!Collections3.isEmpty(context.getRecords())) {
            Map<Long, AuditRecordInfo> map = new HashMap<>();
            for (Map.Entry<Long, AuditRecordContext> contextEntry : context.getRecords().entrySet()) {
                map.put(contextEntry.getKey(), new AuditRecordInfo(contextEntry.getValue().getOldRecord(), contextEntry.getValue().getNewRecord()));
            }
            message.setRecords(map);
        }
        return message;
    }

}
