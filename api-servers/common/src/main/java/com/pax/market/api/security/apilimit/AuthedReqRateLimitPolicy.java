package com.pax.market.api.security.apilimit;


import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@NoArgsConstructor
@ToString
public class AuthedReqRateLimitPolicy {

    private boolean enabled;
    private Long maxRequestNum;
    private Long duration;

    private List<RequestMatcher> ignoreRequests;
    private Map<String, ReqLimitInfo> customizedUrlReqLimit;

    // 深拷贝构造函数
    public AuthedReqRateLimitPolicy(AuthedReqRateLimitPolicy other) {
        this.maxRequestNum = other.getMaxRequestNum();
        this.duration = other.getDuration();
        this.enabled = other.isEnabled();
        this.ignoreRequests = other.getIgnoreRequests();
        this.customizedUrlReqLimit = other.getCustomizedUrlReqLimit();
    }

    public void setIgnoreRequestsOrigin(List<RequestMatcher> ignoreRequests) {
        this.ignoreRequests = ignoreRequests;
    }

    public AuthedReqRateLimitPolicy(boolean enabled, Long maxRequestNum, Long duration) {
        this.enabled = enabled;
        this.maxRequestNum = maxRequestNum;
        this.duration = duration;
    }

    public void setIgnoreRequests(List<ReqUrlMethodDto> ignoreRequests){
        if(CollectionUtils.isNotEmpty(ignoreRequests)) {
            this.ignoreRequests = new ArrayList<>(ignoreRequests.size());
            ignoreRequests.forEach(item -> this.ignoreRequests.add(new AntPathRequestMatcher(item.getUrl(), item.getMethod())));
        }
    }

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReqLimitInfo{
        private long duration;
        private long maxRequestNum;
    }


}
