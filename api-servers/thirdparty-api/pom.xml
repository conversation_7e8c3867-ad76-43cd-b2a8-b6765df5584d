<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pax.market</groupId>
		<artifactId>p-market-api-servers</artifactId>
		<version>9.8.0-SNAPSHOT</version>
	</parent>
	<artifactId>p-market-api-thirdparty</artifactId>
	<name>PAX Market :: API Servers :: 3rd Sys</name>

	<properties>
		<api.props.dir>api/thirdparty</api.props.dir>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-api-common</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.mpusher</groupId>
			<artifactId>mpush-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pax.vas.config</groupId>
			<artifactId>paxvas-config-mp-adapt</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>p-market-3rd-ext</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>paxstore-core-audit-remote-service-api</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>paxstore-core-audit-remote-service-http</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pax.market</groupId>
			<artifactId>paxstore-core-emm-google-impl</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
			</resource>
			<resource>
				<directory>${config.files.module.path}/resources/common</directory>
				<targetPath>${project.build.directory}/classes</targetPath>
				<includes>
					<include>*.yml</include>
				</includes>
			</resource>
			<resource>
				<directory>${config.files.module.path}/resources/api</directory>
				<targetPath>${project.build.directory}/classes</targetPath>
				<includes>
					<include>api-common.yml</include>
				</includes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<skip>false</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<dependency>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>${lombok-mapstruct-binding.version}</version>
						</dependency>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${mapstruct.version}</version>
						</path>
					</annotationProcessorPaths>
					<compilerArgs>
						<compilerArg>
							-Amapstruct.defaultComponentModel=spring
						</compilerArg>
					</compilerArgs>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>