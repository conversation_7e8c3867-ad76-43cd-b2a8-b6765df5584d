/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.api.thirdparty.dto.terminal;


import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.Date;
import java.util.List;

/**
 * The type Terminal dto.
 */
public class TerminalDTO extends AbstractDTO {

    private static final long serialVersionUID = -5204382338333893588L;

    @Schema(description = "Terminal Id", example = "**********")
    private Long id;

    @Schema(description = "Terminal name", example = "termina A")
    private String name;

    @Schema(description = "Terminal TID", example = "R15ET7F5")
    private String tid;

    @Schema(description = "Terminal SN", example = "1640000069TID")
    private String serialNo;

    @Schema(description = "Terminal status", example = "A")
    private String status;

    @Schema(description = "Merchant name of terminal", example = "Merchant_A")
    private String merchantName;

    @Schema(description = "Model name of terminal", example = "A920")
    private String modelName;

    @Schema(description = "Reseller name of terminal", example = "Reseller_A")
    private String resellerName;

    @Schema(description = "The location", example = "locationDemo")
    private String location;

    @Schema(description = "The remark", example = "remark")
    private String remark;

    @Schema(description = "The created time", type = "integer", format = "int64")
    private Date createdDate;

    @Schema(description = "The updated time", type = "integer", format = "int64")
    private Date updatedDate;
    @Schema(description = "The activation time", type = "integer", format = "int64")
    private Date lastActiveTime;

    @Schema(description = "The access time", type = "integer", format = "int64")
    private Date lastAccessTime;


    @Schema(description = "The geo location of the terminal")
    private TerminalLocationDTO geoLocation;

    @Schema(description = "The installed firmware of the terminal")
    private TerminalInstalledFirmwareDTO installedFirmware;

    @Schema(description ="The installed applications of the terminal")
    private List<TerminalInstalledApkDTO> installedApks;

    @Schema(description ="The terminal detail info")
    private TerminalDetailDTO terminalDetail;

    @Schema(description ="The terminal accessory info")
    private TerminalAccessoryDTO terminalAccessory;

    @Schema(description ="The terminal accessory info list")
    private List<TerminalAccessoryDTO> terminalAccessoryList;
    /**
     * Gets id.
     *
     * @return the id
     */
    public Long getId() {
        return id;
    }

    /**
     * Sets id.
     *
     * @param id the id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Gets name.
     *
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * Sets name.
     *
     * @param name the name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets tid.
     *
     * @return the tid
     */
    public String getTid() {
        return tid;
    }

    /**
     * Sets tid.
     *
     * @param tid the tid
     */
    public void setTid(String tid) {
        this.tid = tid;
    }

    /**
     * Gets serial no.
     *
     * @return the serial no
     */
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * Sets serial no.
     *
     * @param serialNo the serial no
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    /**
     * Gets status.
     *
     * @return the status
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets status.
     *
     * @param status the status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * Gets merchant name.
     *
     * @return the merchant name
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * Sets merchant name.
     *
     * @param merchantName the merchant name
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    /**
     * Gets model name.
     *
     * @return the model name
     */
    public String getModelName() {
        return modelName;
    }

    /**
     * Sets model name.
     *
     * @param modelName the model name
     */
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    /**
     * Gets reseller name.
     *
     * @return the reseller name
     */
    public String getResellerName() {
        return resellerName;
    }

    /**
     * Sets reseller name.
     *
     * @param resellerName the reseller name
     */
    public void setResellerName(String resellerName) {
        this.resellerName = resellerName;
    }

    /**
     * Gets location.
     *
     * @return the location
     */
    public String getLocation() {
        return location;
    }

    /**
     * Sets location.
     *
     * @param location the location
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(Date lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    /**
     * Gets geo location.
     *
     * @return the geo location
     */
    public TerminalLocationDTO getGeoLocation() {
        return geoLocation;
    }

    /**
     * Sets geo location.
     *
     * @param geoLocation the geo location
     */
    public void setGeoLocation(TerminalLocationDTO geoLocation) {
        this.geoLocation = geoLocation;
    }

    /**
     * Gets installed firmware.
     *
     * @return the installed firmware
     */
    public TerminalInstalledFirmwareDTO getInstalledFirmware() {
        return installedFirmware;
    }

    /**
     * Sets installed firmware.
     *
     * @param installedFirmware the installed firmware
     */
    public void setInstalledFirmware(TerminalInstalledFirmwareDTO installedFirmware) {
        this.installedFirmware = installedFirmware;
    }

    /**
     * Gets installed apks.
     *
     * @return the installed apks
     */
    public List<TerminalInstalledApkDTO> getInstalledApks() {
        return installedApks;
    }

    /**
     * Sets installed apks.
     *
     * @param installedApks the installed apks
     */
    public void setInstalledApks(List<TerminalInstalledApkDTO> installedApks) {
        this.installedApks = installedApks;
    }

    public TerminalDetailDTO getTerminalDetail() {
        return terminalDetail;
    }

    public void setTerminalDetail(TerminalDetailDTO terminalDetail) {
        this.terminalDetail = terminalDetail;
    }

    public TerminalAccessoryDTO getTerminalAccessory() {
        return terminalAccessory;
    }

    public void setTerminalAccessory(TerminalAccessoryDTO terminalAccessory) {
        this.terminalAccessory = terminalAccessory;
    }

    public List<TerminalAccessoryDTO> getTerminalAccessoryList() {
        return terminalAccessoryList;
    }

    public void setTerminalAccessoryList(List<TerminalAccessoryDTO> terminalAccessoryList) {
        this.terminalAccessoryList = terminalAccessoryList;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Date getLastAccessTime() {
        return lastAccessTime;
    }

    public void setLastAccessTime(Date lastAccessTime) {
        this.lastAccessTime = lastAccessTime;
    }
}
