/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2019. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.api.service.impl;

import com.pax.core.exception.BusinessException;
import com.pax.market.api.service.AbstractThirdPartyService;
import com.pax.market.api.service.ThirdPartyAppService;
import com.pax.market.api.thirdparty.dto.app.AppCostDTO;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.app.AppCost;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.functional.support.AppEntitySupport;
import com.paxstore.global.domain.cachable.LocalCacheMarketInfoService;
import com.paxstore.global.domain.dao.app.AppDao;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppPublishResellerService;
import com.paxstore.global.domain.service.app.AppPublishMerchantCategoryService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.market.domain.service.attribute.EntityAttributeService;
import com.paxstore.market.domain.service.organization.ResellerService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/5/15
 */
@Service
@RequiredArgsConstructor
public class ThirdPartyAppServiceImpl extends AbstractThirdPartyService implements ThirdPartyAppService {

    private final ApkService apkService;
    private final AppDao dao;
    private final AppPublishResellerService appPublishResellerService;
    private final AppPublishMerchantCategoryService appPublishMerchantCategoryService;
    private final EntityAttributeService entityAttributeService;
    private final ResellerService resellerService;
    private final AppService appService;
    private final AppEntitySupport appEntitySupport;
    private final LocalCacheMarketInfoService localCacheMarketInfoService;

    public Page<App> findPageForAdmin(Page<App> page, AppApkQuery appApkQuery, Long marketId, Boolean includeSubscribedApp) {
        if (StringUtils.containsIgnoreCase(page.getOrderBy(), "developer")) {
            page.setOrderBy(null);
        }
        appApkQuery.setPage(page);
        appApkQuery.setExcludePackageNames(SystemConstants.VAS_APP_PACKAGE_NAMES);
        appApkQuery.setAllowAppFirmwareSpecific(getCurrentMarket().getAllowAppFirmwareSpecific());
        appApkQuery.setLicenseAllowAppFirmwareSpecific(getCurrentMarket().getLicenseAllowAppFirmwareSpecific());
        List<App> appList = new ArrayList<>();
        if (!SystemConstants.SUPER_MARKET_ID.equals(marketId) && BooleanUtils.isTrue(includeSubscribedApp)) {
            appList = dao.findAppListForAdminIncludeSubscribed(appApkQuery);
        } else {
            List<Long> appIdList = dao.findAppListForAdmin(appApkQuery);
            for (Long appId : appIdList) {
                App app = appService.get(appId);
                if(app != null){
                    appList.add(app);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(appList)){
            appList.forEach(entityAttributeService::setEntityAttributeValueList);
        }
        return loadAppPageInfo(page, appList, marketId, null);
    }

    public Page<App> findPageForReseller(Page<App> page, AppApkQuery appApkQuery, Long marketId) {
        if (StringUtils.containsIgnoreCase(page.getOrderBy(), "developer")) {
            page.setOrderBy(null);
        }
        appApkQuery.setPage(page);
        appApkQuery.setExcludePackageNames(SystemConstants.VAS_APP_PACKAGE_NAMES);
        if (appPublishMerchantCategoryService.setAppMerchantCategoryFilter(appApkQuery)) {
            return new Page<>();
        }

        appApkQuery.setAllowAppFirmwareSpecific(getCurrentMarket().getAllowAppFirmwareSpecific());
        appApkQuery.setLicenseAllowAppFirmwareSpecific(getCurrentMarket().getLicenseAllowAppFirmwareSpecific());
        List<App> appList = dao.findAppListForReseller(appApkQuery);
        return loadAppPageInfo(page, appList, marketId, appApkQuery.getResellerId());
    }

    @Override
    public AppCostDTO getAppCost(Long appId, Long resellerId) {
        if (resellerService.get(resellerId) == null) {
            throw new BusinessException(ApiCodes.RESELLER_NOT_EXIST);
        }
        if (appService.get(appId) == null) {
            throw new BusinessException(ApiCodes.APP_NOT_FOUND);
        }
        MarketInfo marketInfo = localCacheMarketInfoService.getMarketInfo(getCurrentMarketId());
        if (marketInfo == null) {
            throw new BusinessException(ApiCodes.MARKET_NOT_FOUND);
        }
        if (!marketInfo.getAllowAppOfflinePurchase() || !marketInfo.getAllowAppCostSpecific()) {
            return null;
        }
        AppCost appCost = appEntitySupport.getAppCostRecursion(appId, resellerId);
        if (appCost != null) {
            return BeanMapper.map(appCost, AppCostDTO.class);
        }
        return null;
    }

    private Page<App> loadAppPageInfo(Page<App> page, List<App> appList, Long marketId, Long resellerId) {
        AppApkQuery query = new AppApkQuery();
        //全局应用市场不需要检查子应用市场的定向发布
        if (!LongUtils.equals(SystemConstants.SUPER_MARKET_ID, marketId)) {
            query.setFilterApkPublish(true);
        }
        //代理商需要验证代理商定向发布
        if (LongUtils.isNotBlankAndPositive(resellerId)) {
            query.setFilterApkReseller(true);
            query.setResellerId(resellerId);
        }
        for (App app : appList) {
            if (!SystemConstants.SUPER_MARKET_ID.equals(marketId)) {
                app.setSpecificReseller(appPublishResellerService.isAppSpecificReseller(app.getId()));
            }
            List<Apk> apkList = apkService.findList(app.getId(), query);
            for (Apk apk : apkList) {
                apkService.loadApkFile(apk);
            }
            app.setApkList(apkList);
            MarketInfo marketInfo = localCacheMarketInfoService.getMarketInfo(getCurrentMarketId());
            if (marketInfo == null) {
                throw new BusinessException(ApiCodes.MARKET_NOT_FOUND);
            }
            if (marketInfo.getAllowAppOfflinePurchase() && marketInfo.getAllowAppCostSpecific()) {
                AppCost appCost = appEntitySupport.getAppCostRecursion(app.getId(), resellerId == null ? getCurrentThirdPartySysInfo().getReseller().getId() : resellerId);
                if (appCost != null) {
                    app.setChargeType(appCost.getChargeType());
                    app.setPrice(appCost.getPrice());
                    app.setText(appCost.getText());
                }
            }
        }
        page.setList(appList);
        return page;
    }
}
