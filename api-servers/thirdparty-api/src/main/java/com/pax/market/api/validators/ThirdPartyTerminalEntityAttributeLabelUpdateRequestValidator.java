/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.api.validators;

import com.pax.market.api.thirdparty.dto.entityAttribute.EntityAttributeLabelUpdateRequest;
import com.pax.market.constants.ApiCodes;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import org.apache.commons.collections4.CollectionUtils;


/**
 * @Description
 * @Author: Shawn
 * @Date: 2019/12/19 13:24
 * @Version 7.1
 */
public class ThirdPartyTerminalEntityAttributeLabelUpdateRequestValidator extends Validator<EntityAttributeLabelUpdateRequest> {
    public ThirdPartyTerminalEntityAttributeLabelUpdateRequestValidator(EntityAttributeLabelUpdateRequest t) {
        super(t);
    }

    @Override
    public boolean validate() {
        if(this.validateTarget == null) {
            throw new BusinessException(ApiCodes.THIRD_PARTY_INVALID_REQUEST_PARAMETER);
        }else {
            if(CollectionUtils.isEmpty(validateTarget.getEntityAttributeLabelList())) {
                throw new BusinessException(ApiCodes.ENTITY_ATTRIBUTE_LABEL_MANDATORY);
            }
        }
        return false;
    }
}