package com.pax.market.api.service.internal;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.pax.api.cache.CacheService;
import com.pax.api.fs.FileService;
import com.pax.core.exception.BusinessException;
import com.pax.market.api.ApiContextHolder;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.CacheNames;
import com.pax.market.constants.TerminalActionType;
import com.pax.market.domain.entity.global.app.ApkPatch;
import com.pax.market.domain.entity.global.client.ClientApkPatch;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.organization.ResellerCertificate;
import com.pax.market.domain.entity.market.pushtask.BaseGroupPushTaskEntity;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.app.ApkPatchInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.terminal.TerminalOnlineStatusInfo;
import com.pax.market.framework.common.persistence.annotation.processor.EntityChangedEventListener;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.sm.SecurityMgmtService;
import com.pax.market.framework.common.utils.DigestUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.market.functional.support.ResellerApkSignatureSupport;
import com.pax.market.functional.utils.TerminalOnlineChecker;
import com.pax.market.mq.contract.stock.TerminalStockSyncMessage;
import com.pax.market.mq.contract.sync.TerminalLastApkParamMessage;
import com.pax.market.mq.contract.sync.internal.GenerateTerminalLastApkParamMessage;
import com.pax.market.mq.contract.sync.internal.UpdatePushTaskDownloadTimeMessage;
import com.pax.market.mq.contract.terminal.RefreshTerminalLastAccessTimeMessage;
import com.pax.market.mq.producer.gateway.apk.ResellerOperationGateway;
import com.pax.market.mq.producer.gateway.stock.TerminalStockSyncGateway;
import com.pax.market.mq.producer.gateway.sync.InternalDataSyncGateway;
import com.pax.market.mq.producer.gateway.sync.TerminalLastApkParamSyncGateway;
import com.pax.market.mq.producer.gateway.terminal.RefreshTerminalLastAccessTimeGateway;
import com.pax.market.signature.utils.PaxSignatureUtils;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.support.dynamic.datasource.tools.PaxDsUtils;
import com.paxstore.domain.support.SignatureSupport;
import com.paxstore.global.domain.service.app.ApkPatchService;
import com.paxstore.global.domain.service.client.ClientApkPatchService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.integration.mq.message.ResellerOperationMessage;
import com.paxstore.market.domain.service.organization.ResellerCertificateService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.service.pushtask.PushTaskUtils;
import com.paxstore.market.domain.service.pushtask.TerminalGroupActionCountService;
import com.paxstore.market.domain.service.terminal.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnection;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.ScanParams;
import redis.clients.jedis.resps.ScanResult;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * The type Internal sys service.
 */
@RequiredArgsConstructor
@Component
public class InternalThirdPartySysService {
    private static final Logger logger = LoggerFactory.getLogger(InternalThirdPartySysService.class);

    private static final int DEFAULT_VALIDITY =365 * 5;  //默认刷新5年有效期
    private static final int MIN_VALIDITY = 1;  //最小刷新1天
    private static final int MAX_VALIDITY = 365 * 20;  //最大刷新20年有效期

    private final CacheService cacheService;
    private final TerminalActionService terminalActionService;
    private final TerminalActionHistoryService terminalActionHistoryService;
    private final TerminalRegistryService terminalRegistryService;
    private final TerminalOnlineStatusService terminalOnlineStatusService;
    private final TerminalStockSyncGateway terminalStockSyncGateway;
    private final ApkPatchService apkPatchService;
    private final ClientApkPatchService clientApkPatchService;
    private final FileService fileService;
    private final InternalDataSyncGateway internalDataSyncGateway;
    private final TerminalLastApkParamService terminalLastApkParamService;
    private final TerminalLastApkParamSyncGateway terminalLastApkParamSyncGateway;
    private final TerminalGroupActionCountService terminalGroupActionCountService;
    private final TerminalGroupService terminalGroupService;
    private final RefreshTerminalLastAccessTimeGateway refreshTerminalLastAccessTimeGateway;
    private final MarketService marketService;
    private final ResellerService resellerService;
    private final ResellerCertificateService resellerCertificateService;
    private final ResellerOperationGateway resellerOperationGateway;
    private final SignatureSupport signatureSupport;
    private final ResellerApkSignatureSupport resellerApkSignatureSupport;

    /**
     * Refresh group action count string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshGroupActionCount(String action) {
        String clearStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshGroupActionCount");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (clearStatus == null) {
                return "No refreshGroupActionCount job is running.";
            } else {
                return "refreshGroupActionCount job is in-progress: " + clearStatus;
            }
        } else {
            if (clearStatus != null) {
                return "refreshGroupActionCount job is in-progress: " + clearStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshGroupActionCount", "0/0");
                logger.info("Start refreshGroupActionCount...");
                for (String datasource : PaxDsUtils.getAllDataSources()) {
                    try {
                        PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
                        TerminalActionType.GROUP_ACTION_TYPES.forEach(actionType -> {
                            List<Long> pushTaskIds = PushTaskUtils.getGroupPushTaskService(actionType).findActivePushTaskIds();
                            int totalSize = pushTaskIds.size();
                            logger.info("ActionType: {}, activePushTaskIds size: {}", actionType, totalSize);
                            for (int i = 0; i < totalSize; i++) {
                                if (i % 100 == 0) {
                                    cacheService.put(CacheNames.LOCK_CACHE, "refreshGroupActionCount", String.format("%s - ActionType{%d}: %d/%d", datasource, actionType, i, totalSize));
                                }
                                terminalGroupActionCountService.refreshGroupActionCount(actionType, pushTaskIds.get(i));
                            }
                            logger.info("Finished refreshGroupActionCount for actionType {}", actionType);
                        });
                    } catch (Exception ex) {
                        logger.error("Error when refreshGroupActionCount", ex);
                    } finally {
                        PaxDynamicDsThreadLocal.removePreferenceDatasource();
                    }
                }
                cacheService.remove(CacheNames.LOCK_CACHE, "refreshGroupActionCount");
            }).start();
        }
        return "";
    }

    /**
     * Repair group push task string.
     *
     * @param action      the action
     * @param actionType  the action type
     * @param referenceId the reference id
     * @return the string
     */
    public String repairGroupPushTask(String action, Integer actionType, Long referenceId) {
        String clearStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "repairGroupPushTask");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (clearStatus == null) {
                return "No repairGroupPushTask job is running.";
            } else {
                return "repairGroupPushTask job is in-progress: " + clearStatus;
            }
        } else {
            if (clearStatus != null) {
                return "repairGroupPushTask job is in-progress: " + clearStatus;
            }
            new Thread(() -> {
                logger.info("Start repairGroupPushTask...");
                logger.info(StringUtils.join(PaxDsUtils.getAllDataSources()));
                for (String datasource : PaxDsUtils.getAllDataSources()) {
                    try {
                        PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
                        cacheService.put(CacheNames.LOCK_CACHE, "repairGroupPushTask", datasource + " - 0/0");
                        BaseGroupPushTaskEntity<?> groupPushTask = PushTaskUtils.getGroupPushTaskService(actionType).getWithoutCheck(referenceId);
                        if (groupPushTask == null) {
                            continue;
                        }
                        ApiContextHolder.setApiContext(new MarketInfo(groupPushTask.getGroup().getMarketId()));
                        List<Long> groupTerminalIds = terminalGroupService.findGroupTerminalIdsForPushTask(groupPushTask.getGroup());
                        int totalSize = groupTerminalIds.size();
                        List<Long> repairTerminalIds = Lists.newArrayList();
                        logger.info("groupTerminalIds size: {}", totalSize);
                        AtomicInteger i = new AtomicInteger();
                        groupTerminalIds.forEach(terminalId -> {
                            if (i.get() % 100 == 0) {
                                cacheService.put(CacheNames.LOCK_CACHE, "repairGroupPushTask", String.format("%s - %d/%d", datasource, i.get(), totalSize));
                            }
                            if (terminalActionService.getPendingTerminalAction(terminalId, referenceId, actionType) == null
                                    && terminalActionHistoryService.getTerminalActionHistoryByTerminal(terminalId, referenceId, actionType, -1, 0) == null) {
                                repairTerminalIds.add(terminalId);
                            }
                            i.getAndIncrement();
                        });
                        PushTaskUtils.getGroupPushTaskService(actionType).createPendingTerminalActions(groupPushTask, repairTerminalIds);
                    } catch (Exception ex) {
                        logger.error("Error when repairGroupPushTask", ex);
                    } finally {
                        ApiContextHolder.removeApiContext();
                        PaxDynamicDsThreadLocal.removePreferenceDatasource();
                    }
                }
                logger.info("Finished repairGroupPushTask");
                cacheService.remove(CacheNames.LOCK_CACHE, "repairGroupPushTask");
            }).start();
        }
        return "";
    }

    /**
     * Refresh terminal online status string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshTerminalOnlineStatus(String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshTerminalOnlineStatus");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                return "No refreshTerminalOnlineStatus job is running.";
            } else {
                return "refreshTerminalOnlineStatus job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                return "refreshTerminalOnlineStatus job is in-progress: " + refreshStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalOnlineStatus", "0/0");
                logger.info("Start refreshTerminalOnlineStatus...");
                for (String datasource : PaxDsUtils.getAllDataSources()) {
                    try {
                        PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
                        int limit = 10000; // 每批处理的数据量
                        int start = 0;
                        int totalSize = 0;
                        int processedCount = 0;
                        List<Long> deletedTerminalIds = Lists.newArrayList();
                        while (true) {
                            List<TerminalOnlineStatusInfo> batchTerminalOnlineStatusInfo = terminalOnlineStatusService.findAllTerminalOnlineStatusInfo(start, limit);
                            if (batchTerminalOnlineStatusInfo.isEmpty()) {
                                break;
                            }
                            totalSize += batchTerminalOnlineStatusInfo.size();
                            logger.info("Processing batch of TerminalOnlineStatusInfo, offset: {}, size: {}", start, batchTerminalOnlineStatusInfo.size());
                            for (TerminalOnlineStatusInfo terminalOnlineStatusInfo : batchTerminalOnlineStatusInfo) {
                                if (processedCount % 100 == 0) {
                                    cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalOnlineStatus", String.format("%s - %d/%d", datasource, processedCount, totalSize));
                                }
                                if (terminalRegistryService.get(terminalOnlineStatusInfo.getId()) == null) {
                                    deletedTerminalIds.add(terminalOnlineStatusInfo.getId());
                                } else {
                                    terminalOnlineStatusService.updateTerminalOnlineStatus(terminalOnlineStatusInfo, TerminalOnlineChecker.getTerminalOnlineStatus(terminalOnlineStatusInfo.getId()));
                                }
                                processedCount++;
                            }
                            // 批量删除已处理的终端ID
                            if (deletedTerminalIds.size() >= limit) {
                                Iterable<List<Long>> lists = Iterables.partition(deletedTerminalIds, 100);
                                for (List<Long> list : lists) {
                                    terminalOnlineStatusService.deleteTerminalOnlineStatus(list);
                                }
                                deletedTerminalIds.clear(); // 清空已删除的ID列表
                            }
                            start += limit;
                        }
                        // 处理剩余的删除ID
                        if (!deletedTerminalIds.isEmpty()) {
                            Iterable<List<Long>> lists = Iterables.partition(deletedTerminalIds, 100);
                            for (List<Long> list : lists) {
                                terminalOnlineStatusService.deleteTerminalOnlineStatus(list);
                            }
                        }
                        logger.info(String.format("Finished processing TerminalOnlineStatusInfo for datasource [%s], total processed: %d", datasource, processedCount));
                    } catch (Exception ex) {
                        logger.error("Error when refreshTerminalOnlineStatus", ex);
                    } finally {
                        PaxDynamicDsThreadLocal.removePreferenceDatasource();
                    }
                }
                logger.info("Finished refreshTerminalOnlineStatus");
                cacheService.remove(CacheNames.LOCK_CACHE, "refreshTerminalOnlineStatus");
            }).start();
        }
        return "";
    }

    /**
     * Refresh terminal last access time string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshTerminalLastAccessTime(String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshTerminalLastAccessTime");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                return "No refreshTerminalLastAccessTime job is running.";
            } else {
                return "refreshTerminalLastAccessTime job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                return "refreshTerminalLastAccessTime job is in-progress: " + refreshStatus;
            }

            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalLastAccessTime", "0/0");
                JedisConnection connection = null;
                try {
                    logger.info("Start refreshTerminalLastAccessTime...");
                    RedisConnectionFactory connectionFactory = SpringContextHolder.getBean(RedisConnectionFactory.class);
                    connection = (JedisConnection) connectionFactory.getConnection();
                    byte[] cursor = ScanParams.SCAN_POINTER_START_BINARY;
                    ScanParams scanParams = new ScanParams().count(10000);
                    int count = 0;
                    long totalSize = connection.getJedis().hlen(CacheNames.TERMINAL_ACCESS_TIME_CACHE);
                    logger.info("refreshTerminalLastAccessTime, total count [{}]", totalSize);
                    do {
                        ScanResult<Map.Entry<byte[], byte[]>> scanResult = connection.getJedis().hscan(RedisUtils.serializeStr(CacheNames.TERMINAL_ACCESS_TIME_CACHE), cursor, scanParams);
                        cursor = scanResult.getCursorAsBytes();
                        List<Map.Entry<byte[], byte[]>> entries = scanResult.getResult();
                        count += entries.size();
                        logger.info("refreshTerminalLastAccessTime, current count [{}]", count);
                        cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalLastAccessTime", String.format("%d/%d", count, totalSize));
                        for (Map.Entry<byte[], byte[]> entry : entries) {
                            Date lastAccessTime = (Date) RedisUtils.deserializeObj(entry.getValue());
                            if (lastAccessTime != null && lastAccessTime.getTime() > 1) {
                                Long terminalId = LongUtils.parse(RedisUtils.deserializeStr(entry.getKey()));
                                refreshTerminalLastAccessTimeGateway.send(new RefreshTerminalLastAccessTimeMessage(terminalId, lastAccessTime));
                            }
                        }
                    } while (!Arrays.equals(ScanParams.SCAN_POINTER_START_BINARY, cursor));
                    logger.info("Finished refreshTerminalLastAccessTime");
                } catch (Exception ex) {
                    logger.error("Error when refreshTerminalLastAccessTime", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "refreshTerminalLastAccessTime");
                    if (connection != null) {
                        connection.close();
                    }
                }
            }).start();
        }
        return "";
    }

    /**
     * Refresh terminal stock string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshTerminalStock(String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshTerminalStock");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                return "No refreshTerminalStock job is running.";
            } else {
                return "refreshTerminalStock job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                return "refreshTerminalStock job is in-progress: " + refreshStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalStock", "0/0");
                try {
                    logger.info("Start refreshTerminalStock...");
                    List<TerminalRegistry> listNotMatchTerminalStock = terminalRegistryService.findListNotMatchTerminalStock();
                    int totalSize = listNotMatchTerminalStock.size();
                    logger.info("allTerminals size: {}", totalSize);
                    for (int i = 0; i < totalSize; i++) {
                        if (i % 100 == 0) {
                            cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalStock", String.format("%d/%d", i, totalSize));
                        }
                        TerminalRegistry terminal = listNotMatchTerminalStock.get(i);
                        terminalStockSyncGateway.send(new TerminalStockSyncMessage(terminal.getMarketId(), terminal.getModelId(), terminal.getSerialNo()));
                    }
                    logger.info("Finished refreshTerminalStock");
                } catch (Exception ex) {
                    logger.error("Error when refreshTerminalStock", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "refreshTerminalStock");
                }
            }).start();
        }
        return "";
    }


    /**
     * Generate apk patch md 5 string.
     *
     * @param action the action
     * @return the string
     */
    public String generateApkPatchMd5(String action) {
        String clearStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "generateApkPatchMd5");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (clearStatus == null) {
                return "No generateApkPatchMd5 job is running.";
            } else {
                return "generateApkPatchMd5 job is in-progress: " + clearStatus;
            }
        } else {
            if (clearStatus != null) {
                return "generateApkPatchMd5 job is in-progress: " + clearStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "generateApkPatchMd5", "0/0");
                try {
                    logger.info("Start generateApkPatchMd5...");
                    List<ApkPatch> apkPatchList = apkPatchService.findAllList(new ApkPatch());
                    int totalSize = apkPatchList.size();
                    logger.info("generateApkPatchMd5 size: {}", totalSize);
                    int i = 0;
                    for (ApkPatch apkPatch : apkPatchList) {
                        if (i % 100 == 0) {
                            cacheService.put(CacheNames.LOCK_CACHE, "generateApkPatchMd5", String.format("%d/%d", i, totalSize));
                        }
                        if (StringUtils.isEmpty(apkPatch.getMd())) {
                            if (fileService.isExisted(apkPatch.getPatchUrl())) {
                                apkPatch.setMd(DigestUtils.md5Hex(fileService.download(apkPatch.getPatchUrl())));
                                apkPatchService.save(apkPatch);
                            } else {
                                apkPatchService.delete(apkPatch);
                            }
                            clearTerminalV2ApkPatchCache(apkPatch);
                        }
                        i++;
                    }
                    logger.info("Finished generateApkPatchMd5");
                } catch (Exception ex) {
                    logger.error("Error when generateApkPatchMd5", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "generateApkPatchMd5");
                }
            }).start();
        }
        return "";
    }

    private void clearTerminalV2ApkPatchCache(ApkPatch apkPatch) {
        ApkPatchInfo apkPatchInfo = ApkPatchInfo.builder().newApkId(apkPatch.getNewApkId()).oldApkId(apkPatch.getOldApkId()).build();
        EntityChangedEventListener entityChangedEventListener = SpringContextHolder.getBean(EntityChangedEventListener.class);
        entityChangedEventListener.onEvents(Collections.singleton(new EntityChangedEventListener.EntityChangedEvent(apkPatchInfo)));
    }

    /**
     * Generate client apk patch md 5 string.
     *
     * @param action the action
     * @return the string
     */
    public String generateClientApkPatchMd5(String action) {
        String clearStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "generateClientApkPatchMd5");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (clearStatus == null) {
                return "No generateClientApkPatchMd5 job is running.";
            } else {
                return "generateClientApkPatchMd5 job is in-progress: " + clearStatus;
            }
        } else {
            if (clearStatus != null) {
                return "generateClientApkPatchMd5 job is in-progress: " + clearStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "generateClientApkPatchMd5", "0/0");
                try {
                    logger.info("Start generateClientApkPatchMd5...");
                    List<ClientApkPatch> clientApkPatchList = clientApkPatchService.findAllList(new ClientApkPatch());
                    int totalSize = clientApkPatchList.size();
                    logger.info("generateClientApkPatchMd5 size: {}", totalSize);
                    int i = 0;
                    for (ClientApkPatch clientApkPatch : clientApkPatchList) {
                        if (i % 100 == 0) {
                            cacheService.put(CacheNames.LOCK_CACHE, "generateClientApkPatchMd5", String.format("%d/%d", i, totalSize));
                        }
                        if (StringUtils.isEmpty(clientApkPatch.getMd())) {
                            if (fileService.isExisted(clientApkPatch.getPatchUrl())) {
                                clientApkPatch.setMd(DigestUtils.md5Hex(fileService.download(clientApkPatch.getPatchUrl())));
                                clientApkPatchService.save(clientApkPatch);
                            } else {
                                clientApkPatchService.delete(clientApkPatch);
                            }
                        }
                        i++;
                    }
                    logger.info("Finished generateClientApkPatchMd5");
                } catch (Exception ex) {
                    logger.error("Error when generateClientApkPatchMd5", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "generateClientApkPatchMd5");
                }
            }).start();
        }
        return "";
    }

    /**
     * Refresh push task download time string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshPushTaskDownloadTime(String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshPushTaskDownloadTime");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                return "No refreshPushTaskDownloadTime job is running.";
            } else {
                return "refreshPushTaskDownloadTime job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                return "refreshPushTaskDownloadTime job is in-progress: " + refreshStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshPushTaskDownloadTime", "0/0");
                try {
                    logger.info("Start refreshPushTaskDownloadTime...");
                    refreshPushTaskDownloadTime(TerminalActionType.DOWNLOAD_TERMINAL_APP);
                    refreshPushTaskDownloadTime(TerminalActionType.DOWNLOAD_TERMINAL_PARAM);
                    refreshPushTaskDownloadTime(TerminalActionType.DOWNLOAD_TERMINAL_FIRMWARE);
                    refreshPushTaskDownloadTime(TerminalActionType.DOWNLOAD_TERMINAL_RKI);
                    refreshPushTaskDownloadTime(TerminalActionType.TERMINAL_OPERATION);
                    logger.info("Finished refreshPushTaskDownloadTime");
                } catch (Exception ex) {
                    logger.error("Error when refreshPushTaskDownloadTime", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "refreshPushTaskDownloadTime");
                }
            }).start();
        }
        return "";
    }

    private void refreshPushTaskDownloadTime(int actionType) {
        List<Long> pushTaskIds = PushTaskUtils.getPushTaskService(actionType).findCompletePushTasksWithoutDownloadTime();
        int totalSize = pushTaskIds.size();
        logger.info("refreshPushTaskDownloadTime({}) size: {}", actionType, totalSize);
        Iterable<List<Long>> lists = Iterables.partition(pushTaskIds, 100);

        int i = 0;
        for (List<Long> list : lists) {
            internalDataSyncGateway.send(new UpdatePushTaskDownloadTimeMessage(actionType, list));
            cacheService.put(CacheNames.LOCK_CACHE, "refreshPushTaskDownloadTime", String.format("%d/%d/%d", actionType, i, totalSize));
            i += 100;
        }
    }

    /**
     * Refresh terminal last apk param string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshTerminalLastApkParam(String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshTerminalLastApkParam");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                return "No refreshTerminalLastApkParam job is running.";
            } else {
                return "refreshTerminalLastApkParam job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                return "refreshTerminalLastApkParam job is in-progress: " + refreshStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalLastApkParam", "0/0");
                try {
                    logger.info("Start refreshTerminalLastApkParam...");
                    List<Long> terminalIds = terminalRegistryService.findAllActivatedTerminalIds();
                    int totalSize = terminalIds.size();
                    logger.info("refreshTerminalLastApkParam size: {}", totalSize);
                    int i = 0;
                    for (Long terminalId : terminalIds) {
                        if (i % 100 == 0) {
                            cacheService.put(CacheNames.LOCK_CACHE, "refreshTerminalLastApkParam", String.format("%d/%d", i, totalSize));
                        }
                        internalDataSyncGateway.send(new GenerateTerminalLastApkParamMessage(terminalId));
                        i++;
                    }
                    logger.info("Finished refreshTerminalLastApkParam");
                } catch (Exception ex) {
                    logger.error("Error when refreshTerminalLastApkParam", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "refreshTerminalLastApkParam");
                }
            }).start();
        }
        return "";
    }

    /**
     * Refresh reseller pushed param apk string.
     *
     * @param action the action
     * @return the string
     */
    public String refreshResellerPushedParamApk(String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshResellerPushedParamApk");
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                return "No refreshResellerPushedParamApk job is running.";
            } else {
                return "refreshResellerPushedParamApk job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                return "refreshResellerPushedParamApk job is in-progress: " + refreshStatus;
            }
            new Thread(() -> {
                cacheService.put(CacheNames.LOCK_CACHE, "refreshResellerPushedParamApk", "0");
                try {
                    logger.info("Start refreshResellerPushedParamApk...");
                    for (String datasource : PaxDsUtils.getAllDataSources()) {
                        PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
                        int startIndex = 0;
                        int limit = 50000;
                        while (true) {
                            List<TerminalLastApkParamMessage> messages = terminalLastApkParamService.findAllMessages(startIndex, limit);
                            for (TerminalLastApkParamMessage message : messages) {
                                if (startIndex % 100 == 0) {
                                    cacheService.put(CacheNames.LOCK_CACHE, "refreshResellerPushedParamApk", String.format("%s - %d", datasource, startIndex));
                                }
                                terminalLastApkParamSyncGateway.send(message);
                                startIndex++;
                            }
                            if (messages.size() < limit) {
                                break;
                            }
                        }
                    }
                    logger.info("Finished refreshResellerPushedParamApk");
                } catch (Exception ex) {
                    logger.error("Error when refreshResellerPushedParamApk", ex);
                } finally {
                    cacheService.remove(CacheNames.LOCK_CACHE, "refreshResellerPushedParamApk");
                    PaxDynamicDsThreadLocal.removePreferenceDatasource();
                }
            }).start();
        }
        return "";
    }

    public String refreshGenResellerCertificate(String marketDomain, String resellerName, Integer validity) {
        try {
            if (Objects.isNull(validity)) {
                validity = DEFAULT_VALIDITY;
            } else {
                if (validity < MIN_VALIDITY || validity > MAX_VALIDITY) {
                    throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
                }
            }
            Market market = marketService.findMarketByDomain(marketDomain);
            if (Objects.isNull(market)) {
                throw new BusinessException(ApiCodes.MARKET_NOT_FOUND);
            }
            PaxDynamicDsThreadLocal.setPreferenceMarketId(market.getId());
            Reseller reseller = resellerService.getByName(market.getId(), resellerName);
            if (Objects.isNull(reseller)) {
                throw new BusinessException(ApiCodes.RESELLER_NOT_EXIST);
            }
            if (resellerApkSignatureSupport.isResellerSignatureDisabled(market.getId(), SystemPropertyHelper.getPaxFactoryDefaultId())) {
                logger.warn("generateResellerCertificate: {}-{}, reseller signature not allowed, return", market.getId(), reseller.getId());
                throw new BusinessException(ApiCodes.RESELLER_SIGNATURE_NOT_ALLOWED);
            }
            ResellerCertificate resellerCertificate = resellerCertificateService.getResellerCertificate(reseller.getId());
            if (Objects.isNull(resellerCertificate) || Objects.isNull(resellerCertificate.getCertificate())) {
                resellerOperationGateway.send(ResellerOperationMessage.of(
                        new ResellerOperationMessage.GenerateResellerCertificateMessage(market.getId(), reseller.getId(), SystemPropertyHelper.getPaxFactoryDefaultId())));
                throw new BusinessException(ApiCodes.RESELLER_CERTIFICATE_NOT_EXIST);
            }
            resellerCertificate.setMarketId(market.getId());
            resellerCertificate = signatureSupport.refreshGenResellerCertificate(resellerCertificate, validity);
            if (Objects.nonNull(resellerCertificate) && Objects.nonNull(resellerCertificate.getCertificate())) {
                return PaxSignatureUtils.parseCertificateExpiryTime(resellerCertificate.getCertificate());
            } else {
                logger.warn("Reseller certificate or data is null");
                return null;
            }
        } catch (Exception e) {
            if (e instanceof BusinessException bex) {
                throw bex;
            } else {
                logger.error("refreshGenResellerCertificate error", e);
                throw new BusinessException(ApiCodes.UNKNOWN);
            }
        } finally {
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

}
