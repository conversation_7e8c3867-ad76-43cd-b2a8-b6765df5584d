/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.api.thirdparty.dto.developer;

import com.pax.market.api.thirdparty.dto.base.Response;
import com.pax.market.api.thirdparty.dto.terminal.TerminalConfigDTO;

/**
 * 2 * @Author: <PERSON> zhengWen
 * 3 * @Date: 2024-01-31
 * 4
 */
public class CreateAppResponse extends Response<Long> {

    private static final long serialVersionUID = 4437264018562754187L;

    public CreateAppResponse() {
    }

    public CreateAppResponse(Long data) {
        super(data);
    }
}
