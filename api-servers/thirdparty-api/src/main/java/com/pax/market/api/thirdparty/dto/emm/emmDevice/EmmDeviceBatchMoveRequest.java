package com.pax.market.api.thirdparty.dto.emm.emmDevice;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import com.pax.market.dto.audit.AuditableBatchOperationAwareRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

@Getter
@Setter
@Schema(description = "EMM device batch move request body")
public class EmmDeviceBatchMoveRequest extends AbstractDTO implements AuditableBatchOperationAwareRequest {

    @Serial
    private static final long serialVersionUID = -2119942552049394247L;
    
    @Schema(description = "List of devices for batch movement", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1644194720383012]")
    private List<Long> deviceIds;

    @Schema(description = "Reseller name to whom the EMM device belongs, max length is 64", requiredMode = Schema.RequiredMode.REQUIRED, example = "R1")
    private String resellerName;

    @Schema(description = "Merchant name to whom the EMM device belongs, max length is 64", requiredMode = Schema.RequiredMode.REQUIRED, example = "M1")
    private String merchantName;

    @Override
    public Long[] getEntityIds() {
        if (deviceIds != null) {
            return deviceIds.toArray(new Long[deviceIds.size()]);
        }
        return null;
    }
}
