/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2019. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.api.validators;

import com.pax.market.api.thirdparty.dto.terminalApk.UpdateTerminalApkRequest;
import com.pax.market.constants.ApiCodes;
import com.pax.core.exception.BusinessException;
import com.pax.market.functional.validation.Validator;
import com.pax.market.framework.common.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2019/5/15
 */
public class ThirdPartyTerminalApkUpdateValidator extends Validator<UpdateTerminalApkRequest> {

    public ThirdPartyTerminalApkUpdateValidator(UpdateTerminalApkRequest request){
        super(request);
    }

    @Override
    public boolean validate() {

        if(validateTarget == null){
            throw new BusinessException(ApiCodes.BAD_REQUEST);
        }

        if(StringUtils.isBlank(validateTarget.getSerialNo()) && StringUtils.isBlank(validateTarget.getTid())) {
            throw new BusinessException(ApiCodes.TRD_TERMINAL_APK_SERIALNO_TID_EMPTY);
        }
        if(StringUtils.isBlank(validateTarget.getPackageName())) {
            throw new BusinessException(ApiCodes.TRD_TERMINAL_APK_PACKAGE_NAME_EMPTY);
        }

        return false;
    }
}
