package com.pax.market.api.thirdparty.dto.emm.emmDevice;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Date;

@Getter
@Setter
@Schema(description = "EMM device register QR code information")
public class EmmDeviceRegisterQRCodeCreateDTO extends AbstractDTO {

    @Serial
    private static final long serialVersionUID = 582450775128141247L;

    @Schema(description = "The device register QR code id", example = "1000001")
    private Long id;

    @Schema(description = "The market name", example = "Market A")
    private String marketName;

    @Schema(description = "The reseller name", example = "Reseller A")
    private String resellerName;

    @Schema(description = "The merchant name", example = "Merchant A")
    private String merchantName;

    @Schema(description = "The device type, value is C(management mode is DEVICE_OWNER)", example = "C")
    private String deviceType;

    @Schema(description = "The register QR code")
    private String registerQRCode;

    @Schema(type = "integer", format = "int64", example = "1575451257000")
    private Date expireDate;

}
