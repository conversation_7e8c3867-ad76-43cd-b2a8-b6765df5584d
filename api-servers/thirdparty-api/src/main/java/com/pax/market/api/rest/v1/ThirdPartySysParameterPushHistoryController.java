/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1;


import com.pax.core.exception.BusinessException;
import com.pax.market.api.service.ThirdPartyPushHistoryService;
import com.pax.market.api.thirdparty.dto.pushStatus.ParameterPushHistoryPageResponse;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.pushtask.ParameterPushHistory;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.paxstore.market.domain.service.terminal.MarketTerminalService;
import com.pax.market.framework.common.utils.IntegerUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;


/**
 * The type Third party sys parameter push history controller.
 */
@RestController("ThirdPartySysParameterPushHistoryControllerV1")
@RequestMapping(value = "v1/3rdsys/parameter/push/history")
@Tag(name = "【Parameter Push History】", description = "Parameter Push History APIs")
public class ThirdPartySysParameterPushHistoryController extends AbstractThirdpartyController {
    @Autowired
    private ThirdPartyPushHistoryService thirdPartyPushHistoryService;
    @Autowired
    private MarketTerminalService marketTerminalService;

    @GetMapping
    @Operation(summary = "Find Parameter Push History by Page, return a paged merchant list",
            parameters = {
                    @Parameter(name = "pageNo", required = true, description = "The page number, example: 1", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "pageSize", required = true, description = "The page size, example: 5", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "packageName", required = true, description = "The application package name, example: com.demo.test", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
                    @Parameter(name = "serialNo", description = "The terminal serial no, example: SN1234567", schema = @Schema(type = "string")),
                    @Parameter(name = "pushStatus", description = "pushStatus, value can be one of 2(Success), 3(Failed)", schema = @Schema(type = "int")),
                    @Parameter(name = "pushTime", description = "The push history after date", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "yyyy-MM-dd HH:mm:ss Z")),
                    @Parameter(name = "onlyLastPushHistory", description = "onlyLastPushHistory, whether only search the last push history,onlyLastPushHistory returns the last push record from every terminal", schema = @Schema(type = "boolean")),
                    @Parameter(name = "optimizeParameters", description = "optimizeParameters, whether optimize the return parameters", schema = @Schema(type = "boolean"))
            })
    public ParameterPushHistoryPageResponse findParameterPushHistory(
            @RequestParam String packageName,
            @RequestParam(required = false) String serialNo,
            @RequestParam(required = false) Integer pushStatus,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss Z") Date pushTime,
            @RequestParam(required = false) Boolean onlyLastPushHistory,
            @RequestParam(required = false) Boolean optimizeParameters) {
        ParameterPushHistory query = new ParameterPushHistory();
        if (StringUtils.isNotEmpty(serialNo)) {
            Terminal terminal = marketTerminalService.getBySerialNo(serialNo);
            if (terminal == null) {
                return convertPage(parsePage(), new ArrayList<>(), ParameterPushHistoryPageResponse.class);
            }
            query.setTerminalId(terminal.getId());
        } else if (SystemPropertyHelper.isDisableSearchPushHistoryWithoutSn()) {
            throw new BusinessException(ApiCodes.BAD_REQUEST, "Serial no is required");
        }
        query.setPackageName(StringUtils.trim(packageName));
        query.setParameterPushStatus(IntegerUtils.getValue(pushStatus));
        Date lastYear = DateUtils.lastYear(new Date());
        //开始时间确保在一年以内
        if (pushTime == null || pushTime.before(lastYear)) {
            query.setParameterPushTime(lastYear);
        } else {
            query.setParameterPushTime(pushTime);
        }
        if (BooleanUtils.isTrue(onlyLastPushHistory)) {
            return thirdPartyPushHistoryService.findLastParameterPushHistoryPage(parsePage(), query, optimizeParameters);
        } else {
            return thirdPartyPushHistoryService.findParameterPushHistoryPage(parsePage(), query, optimizeParameters);
        }
    }
}
