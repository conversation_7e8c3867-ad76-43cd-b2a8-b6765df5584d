package com.pax.market.api.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.mpush.api.spi.common.CacheManagerFactory;
import com.pax.vas.config.mp.Mp8Config;

import lombok.Getter;
import lombok.Setter;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "push-client-cfg")
public class MpCacheConfig extends Mp8Config implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        CacheManagerFactory.create().init();
    }
}
