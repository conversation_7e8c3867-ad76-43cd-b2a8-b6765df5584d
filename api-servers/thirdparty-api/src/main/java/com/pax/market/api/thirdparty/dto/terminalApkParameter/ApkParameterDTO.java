/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.api.thirdparty.dto.terminalApkParameter;

import com.pax.market.api.thirdparty.dto.app.ApkDTO;
import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;


@Getter
@Setter
@Schema
public class ApkParameterDTO extends AbstractDTO {
    private static final long serialVersionUID = 3967245872225264118L;

    @Schema(description ="The apk parameter id", example = "1000001")
    private Long id;

    @Schema(description ="Apk information")
    private ApkDTO apk;
    @Schema(description ="The apk parameter name", example = "paramTemplate")
    private String name;
    @Schema(description ="The apk parameter template name", example = "paramTemplate")
    private String paramTemplateName;
    @Schema(description ="The apk parameter template parameters", example = "{\"sys.pid.test01\":\"t1\", \"sys.pid.test02\":\"t2\", \"sys.pid.test03\":\"t3\"}")
    private Map<String, String> configuredParameters;
    @Schema(type = "integer", format = "int64", example = "1575451257000")
    private Date createdDate;
    @Schema(type = "integer", format = "int64", example = "1575451257000")
    private Date updatedDate;
    @Schema(description ="The apk available", example = "true")
    private Boolean apkAvailable;
}
