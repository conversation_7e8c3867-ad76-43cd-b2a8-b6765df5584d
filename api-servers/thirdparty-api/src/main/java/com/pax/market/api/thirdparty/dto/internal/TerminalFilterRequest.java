/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.api.thirdparty.dto.internal;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * The type Terminal filter request.
 */
@Schema(description = "Terminal Filter Request")
public class TerminalFilterRequest extends AbstractDTO {
    private static final long serialVersionUID = -1L;

    @Schema(description = "Terminal Serial No List")
    private List<String> serialNoList;

    /**
     * Gets serial no list.
     *
     * @return the serial no list
     */
    public List<String> getSerialNoList() {
        return serialNoList;
    }

    /**
     * Sets serial no list.
     *
     * @param serialNoList the serial no list
     */
    public void setSerialNoList(List<String> serialNoList) {
        this.serialNoList = serialNoList;
    }
}
