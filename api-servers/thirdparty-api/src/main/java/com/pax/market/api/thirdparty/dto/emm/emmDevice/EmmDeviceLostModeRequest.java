package com.pax.market.api.thirdparty.dto.emm.emmDevice;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
@Schema(description = "EMM device lost mode request body")
public class EmmDeviceLostModeRequest extends AbstractDTO {

    @Serial
    private static final long serialVersionUID = -2214685641061507140L;

    @Schema(description = "Lost message, max length is 64", requiredMode = Schema.RequiredMode.REQUIRED, example = "example")
    private String lostMessage;

    @Schema(description = "Lost phone number, max length is 32", requiredMode = Schema.RequiredMode.REQUIRED, example = "12345678910")
    private String lostPhoneNumber;
}
