package com.pax.market.api.converter;

import com.pax.market.api.thirdparty.dto.terminal.TerminalDTO;
import com.pax.market.domain.entity.market.terminal.Terminal;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;


@Mapper
public interface TerminalConverter {

    @Mapping(target = "tid", source = "TID")
    @Mapping(target = "merchantName", source = "merchant.name")
    @Mapping(target = "resellerName", source = "reseller.name")
    @Mapping(target = "modelName", source = "model.name")
    @Mapping(target = "installedFirmware", ignore = true)
    TerminalDTO toTerminalDto(Terminal terminal);
    List<TerminalDTO> toTerminalDtoList(List<Terminal> terminalList);
}
