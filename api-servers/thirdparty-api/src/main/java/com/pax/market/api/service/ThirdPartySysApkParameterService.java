/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.api.service;

import com.pax.market.domain.entity.market.app.ApkParameter;
import org.apache.ibatis.annotations.Param;


/**
 * The interface Third party sys apk parameter service.
 *
 * @Description CURD apk param template
 * @Author: Shawn
 * @Date: 2019 /10/28 15:19
 * @Version 7.0
 */
public interface ThirdPartySysApkParameterService {

    /**
     * get ApkParameter
     *
     * @param apkParameterId the apk parameter id
     * @return the apk parameter
     */
    ApkParameter getApkParameter(@Param("apkParameterId") Long apkParameterId);

    /**
     * Gets apk parameter.
     *
     * @param packageName      the package name
     * @param version          the version
     * @param pushTemplateName the push template name
     * @return the apk parameter
     */
    ApkParameter getApkParameter(Long marketId, String packageName, String version, String pushTemplateName);
}
