/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2020. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.api.thirdparty.dto.terminalRki;

import com.pax.market.api.thirdparty.dto.base.Response;

/**
 * <AUTHOR>
 * @date 2020/2/6
 */
public class TerminalRkiResponse extends Response<TerminalRkiDTO> {

    private static final long serialVersionUID = 9020419653537961425L;

    public TerminalRkiResponse() {
    }

    public TerminalRkiResponse(TerminalRkiDTO data) {
        super(data);
    }
}
