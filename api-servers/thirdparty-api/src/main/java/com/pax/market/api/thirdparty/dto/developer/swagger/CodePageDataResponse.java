/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.api.thirdparty.dto.developer.swagger;

import com.pax.market.api.thirdparty.dto.base.PageDataResponse;
import com.pax.market.api.thirdparty.dto.developer.CodeInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * Reponse with pagination data list
 */
@Schema(description = "code page data response")
public class CodePageDataResponse extends PageDataResponse<CodeInfoDTO> {

    private static final long serialVersionUID = 7727172161706015077L;

    @Schema(description = "code dataSet")
    private List<CodeInfoDTO> dataset = new ArrayList<>();

    private int limit;

    private int pageNo;

    private long totalCount;

    @Override
    public List<CodeInfoDTO> getDataset() {
        return dataset;
    }

    @Override
    public void setDataset(List<CodeInfoDTO> dataset) {
        this.dataset = dataset;
    }

    /**
     * Gets limit.
     *
     * @return the limit
     */
    public int getLimit() {
        return limit;
    }

    /**
     * Sets limit.
     *
     * @param limit the limit
     */
    public void setLimit(int limit) {
        this.limit = limit;
    }

    /**
     * Is has next boolean.
     *
     * @return the boolean
     */
    public boolean isHasNext() {
        return (pageNo * limit) < totalCount;
    }

    /**
     * Gets total count.
     *
     * @return the total count
     */
    public Long getTotalCount() {
        return totalCount;
    }

    /**
     * Sets total count.
     *
     * @param totalCount the total count
     */
    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    /**
     * Gets page no.
     *
     * @return the page no
     */
    public int getPageNo() {
        return pageNo;
    }

    /**
     * Sets page no.
     *
     * @param pageNo the page no
     */
    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }


}
