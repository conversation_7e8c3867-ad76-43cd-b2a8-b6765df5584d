/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.service.ThirdPartySysMerchantVariableService;
import com.pax.market.api.thirdparty.dto.base.SuccessResponse;
import com.pax.market.api.thirdparty.dto.variable.merchantVariable.*;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.biz.annotation.Audit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * merchant parameter variables 3rd Api
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
@RestController("ThirdPartySysMerchantVariableControllerV1")
@RequestMapping("v1/3rdsys/merchant/variables")
@Tag( name= "【Merchant Variable】", description = "Merchant Variable APIs")
@RequiredArgsConstructor
public class ThirdPartySysMerchantVariableController extends AbstractThirdpartyController {

    private final ThirdPartySysMerchantVariableService thirdPartySysMerchantVariableService;

    /**
     * Find terminal variable list.
     *
     * @param key         the key
     * @param packageName packageName
     * @param source      source
     * @throws IOException the io exception
     */
    @GetMapping
    @Operation(summary = "Get Merchant Variable List",
            parameters = {
                    @Parameter(name = "pageNo", required = true,description = "The page number, example: 1", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "pageSize", required = true,description = "The page size, example: 5", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "orderBy", description = "The sort order of search result, the value can be one of createdDate DESC,createdDate ASC", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
                    @Parameter(name = "merchantId", required = true, description = "the id of merchant, example: 1000000000", schema = @Schema(type = "Long")),
                    @Parameter(name = "packageName", description = "package name, example: com.demo.test", schema = @Schema(type = "string")),
                    @Parameter(name = "key", description = "the key of variable, example: test", schema = @Schema(type = "string")),
                    @Parameter(name = "source", description = "the source of variable, the value can be one of M(market), C(merchant)", schema = @Schema(type = "string")),
            })
    public MerchantVariablePageResponse findMerchantVariablePage(
             @RequestParam Long merchantId,
             @RequestParam(required = false) String packageName,
             @RequestParam(required = false) String key,
             @RequestParam(required = false) String source) throws IOException {


        MerchantVariableQueryRequest queryRequest = new MerchantVariableQueryRequest();
        queryRequest.setMerchantId(merchantId);
        queryRequest.setKey(key);
        queryRequest.setPackageName(packageName);
        queryRequest.setSource(source);
        return thirdPartySysMerchantVariableService.findMerchantVariablePage(queryRequest);

    }

    /**
     * Create merchant variable.
     *
     * @param createRequest the create request
     * @throws IOException the io exception
     */
    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Create Merchant Parameter Variables In Batch",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = MerchantVariableCreateRequest.class)),required = true)
    )
    @Audit(type = AuditTypes.PARAMETER_VARIABLE, primaryAction = AuditActions.CREATE, dataType = AuditDataTypes.MERCHANT_VARIABLE)
    public SuccessResponse createMerchantVariable(@RequestBody MerchantVariableCreateRequest createRequest) throws IOException {
        thirdPartySysMerchantVariableService.createMerchantVariable(createRequest);
        return new SuccessResponse();
    }

    /**
     * update merchant variable.
     *
     * @param merchantVariableId the merchant variable id
     * @param updateRequest      the update request
     * @throws IOException the io exception
     */
    @PutMapping("{merchantVariableId}")
    @Audit(type = AuditTypes.PARAMETER_VARIABLE, primaryAction = AuditActions.UPDATE, dataType = AuditDataTypes.MERCHANT_VARIABLE)
    @Operation(summary = "Update Merchant Variable by Variable Id",
            parameters = {
                    @Parameter(name = "merchantVariableId", required = true, description = "merchant variable id, example: 1000000000", schema = @Schema(type = "long")),
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = MerchantVariableUpdateRequest.class)),required = true)
    )
    public SuccessResponse updateMerchantVariable(
            @PathVariable Long merchantVariableId,
             @RequestBody MerchantVariableUpdateRequest updateRequest) throws IOException {
        thirdPartySysMerchantVariableService.updateMerchantVariable(merchantVariableId, updateRequest);
        return new SuccessResponse();
    }


    /**
     * Delete merchant variable.
     *
     * @param merchantVariableId the merchant variable id
     * @throws IOException the io exception
     */
    @DeleteMapping("{merchantVariableId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.PARAMETER_VARIABLE, primaryAction = AuditActions.DELETE, dataType = AuditDataTypes.MERCHANT_VARIABLE)
    @Operation(summary = "Delete Merchant Variable by Variable Id",
            parameters = {
                    @Parameter(name = "merchantVariableId", required = true, description = "merchant variable id, example: 1000000000", schema = @Schema(type = "long")),
            })
    public void deleteMerchantVariable(
            @PathVariable Long merchantVariableId) throws IOException {
        thirdPartySysMerchantVariableService.deleteMerchantVariable(merchantVariableId);
    }

    /**
     * Batch delete merchant variables.
     *
     * @param deleteRequest the delete request
     * @throws IOException the io exception
     */
    @DeleteMapping("batch/deletion")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.PARAMETER_VARIABLE, primaryAction = AuditActions.DELETE, dataType = AuditDataTypes.MERCHANT_VARIABLE)
    @Operation(summary = "Batch Deletion of Merchant Variables",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = MerchantVariableDeleteRequest.class)), required = true),
            responses =
                    { @io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_NO_CONTENT),
                    })
    public void batchDeleteMerchantVariables(
            @RequestBody MerchantVariableDeleteRequest deleteRequest) throws IOException {
        thirdPartySysMerchantVariableService.batchDeleteMerchantVariables(deleteRequest);
    }


}