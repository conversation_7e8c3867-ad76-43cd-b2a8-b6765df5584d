/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.api.thirdparty.dto.entityAttribute;

import com.pax.market.api.thirdparty.dto.base.PageDataResponse;

/**
 *
 */
public class EntityAttributePageResponse extends PageDataResponse<EntityAttributeDTO> {

    private static final long serialVersionUID = -6686738998147424910L;
}
