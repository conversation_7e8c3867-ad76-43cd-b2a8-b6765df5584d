/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.thirdparty.dto.terminal;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/1
 */
public class TerminalDeviceDTO extends AbstractDTO {
    private static final long serialVersionUID = 1430325907891823715L;

    @Schema(description = "The accessory information name", example = "RAM")
    private String name;

    @Schema(description = "The accessory information version", example = "V1.00.08_201801")
    private String version;

    @Schema(description = "The status of the related historical push of the accessory device", example = "Success")
    private String status;

    @Schema(description = "The accessory information install time", example = "1588053630000", type="integer", format = "int64")
    private Date installTime;

    @Schema(description = "The size of the file pushed by the accessory device", example = "2165940")
    private Long fileSize;

    @Schema(description = "The type of the file pushed by the accessory device", example = "Private file")
    private String fileType;

    @Schema(description = "The file source", example = "Local Upgrade")
    private String source;

    @Schema(description = "The remarks information", example = "sn")
    private String remarks;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getInstallTime() {
        return installTime;
    }

    public void setInstallTime(Date installTime) {
        this.installTime = installTime;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
