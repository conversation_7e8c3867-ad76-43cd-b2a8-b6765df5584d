/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2019. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.api.rest.v1;

import com.pax.api.fs.FileService;
import com.pax.api.fs.SupportedFileTypes;
import com.pax.core.exception.BusinessException;
import com.pax.core.json.JsonMapper;
import com.pax.market.api.service.ThirdPartySysApkParameterService;
import com.pax.market.api.thirdparty.dto.base.BaseApkPushRequest;
import com.pax.market.api.thirdparty.dto.terminalApk.FileParameter;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.market.app.ApkParameter;
import com.pax.market.domain.entity.market.pushtask.*;
import com.pax.market.domain.parameter.ParameterUtils;
import com.pax.market.domain.parameter.SchemaProcess;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.parameter.ParameterInfo;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.utils.FileUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.support.ApkParameterTemplateSupport;
import com.pax.market.mq.contract.schedule.CreatePendingTerminalActionMessage;
import com.pax.market.mq.producer.gateway.push.CreatePendingTerminalActionGateway;
import com.paxstore.global.domain.service.app.ApkParamTemplateService;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.support.signature.SignatureSupportService;
import com.paxstore.market.domain.service.app.ApkParameterService;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.paxstore.market.domain.util.ApkParameterUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * The type Third party sys base push controller.
 *
 * <AUTHOR>
 * @date 2019 /5/20
 */
public abstract class ThirdPartySysBasePushController extends AbstractThirdpartyController {

    /**
     * The App service.
     */
    @Autowired
    protected AppService appService;
    /**
     * The Apk service.
     */
    @Autowired
    protected ApkService apkService;
    /**
     * The Apk param template service.
     */
    @Autowired
    protected ApkParamTemplateService apkParamTemplateService;
    /**
     * The Apk parameter service.
     */
    @Autowired
    protected ApkParameterService apkParameterService;
    /**
     * The File service.
     */
    @Autowired
    protected FileService fileService;
    /**
     * The Third party sys apk parameter service.
     */
    @Autowired
    protected ThirdPartySysApkParameterService thirdPartySysApkParameterService;
    /**
     * The Reseller service.
     */
    @Autowired
    protected ResellerService resellerService;
    /**
     * The Apk parameter template support.
     */
    @Autowired
    protected ApkParameterTemplateSupport apkParameterTemplateSupport;
    @Autowired
    private CreatePendingTerminalActionGateway createPendingTerminalActionGateway;
    @Autowired
    private SignatureSupportService signatureSupportService;

    /**
     * Check new terminal action message.
     *
     * @param terminalApk the terminal apk
     */
    void createPendingTerminalActionMessage(TerminalApk terminalApk) {
        if (!terminalApk.getEffectiveTime().after(new Date()) && StringUtils.equals(terminalApk.getTerminal().getStatus(), PushTaskStatus.ACTIVE)) {
            createPendingTerminalActionGateway.send(new CreatePendingTerminalActionMessage(TerminalActionType.DOWNLOAD_TERMINAL_APP, terminalApk.getId()));
        }
    }

    /**
     * Check new terminal action message.
     *
     * @param groupApk the group apk
     */
    void createPendingTerminalActionMessage(TerminalGroupApk groupApk) {
        if (!groupApk.getEffectiveTime().after(new Date()) && StringUtils.equals(groupApk.getGroup().getStatus(), PushTaskStatus.ACTIVE)) {
            createPendingTerminalActionGateway.send(new CreatePendingTerminalActionMessage(TerminalActionType.DOWNLOAD_GROUP_APP, groupApk.getId()));
        }
    }

    /**
     * Check new terminal action message.
     *
     * @param terminalFirmware the terminal firmware
     */
    void createPendingTerminalActionMessage(TerminalFirmware terminalFirmware) {
        if (!terminalFirmware.getEffectiveTime().after(new Date()) && StringUtils.equals(terminalFirmware.getTerminal().getStatus(), PushTaskStatus.ACTIVE)) {
            createPendingTerminalActionGateway.send(new CreatePendingTerminalActionMessage(TerminalActionType.DOWNLOAD_TERMINAL_FIRMWARE, terminalFirmware.getId()));
        }
    }

    /**
     * Create pending terminal action message.
     *
     * @param terminalRki the terminal rki
     */
    void createPendingTerminalActionMessage(TerminalRki terminalRki) {
        if (!terminalRki.getEffectiveTime().after(new Date()) && StringUtils.equals(terminalRki.getTerminal().getStatus(), PushTaskStatus.ACTIVE)) {
            createPendingTerminalActionGateway.send(new CreatePendingTerminalActionMessage(TerminalActionType.DOWNLOAD_TERMINAL_RKI, terminalRki.getId(), terminalRki.getDeductionId()));
        }
    }

    /**
     * Check new terminal action message.
     *
     * @param groupRki the group rki
     */
    void createPendingTerminalActionMessage(TerminalGroupRki groupRki) {
        if (!groupRki.getEffectiveTime().after(new Date()) && StringUtils.equals(groupRki.getGroup().getStatus(), PushTaskStatus.ACTIVE)) {
            createPendingTerminalActionGateway.send(new CreatePendingTerminalActionMessage(TerminalActionType.DOWNLOAD_GROUP_RKI, groupRki.getId(), groupRki.getDeductionId()));
        }
    }

    /**
     * Gets apk parameter from request.
     *
     * @param marketId      the market id
     * @param createRequest the create request
     * @return the apk parameter from request
     */
    ApkParameter getApkParameterFromRequest(Long marketId, BaseApkPushRequest createRequest) {
        ApkParameter apkParameter = null;
        if (StringUtils.isNotBlank(createRequest.getPushTemplateName())) {
            apkParameter = thirdPartySysApkParameterService.getApkParameter(marketId, createRequest.getPackageName(), createRequest.getVersion(), createRequest.getPushTemplateName());
            createRequest.setTemplateName(apkParameter.getParamTemplateName());
        }
        return apkParameter;
    }

    /**
     * Gets apk parameters from request.
     *
     * @param parameters           the parameters
     * @param base64FileParameters the base 64 file parameters
     * @return the apk parameters from request
     */
    Map<String, String> getApkParametersFromRequest(Map<String, String> parameters, List<FileParameter> base64FileParameters) {
        List<String> createdFileUrls = new ArrayList<>();
        try {
            Map<String, String> convertedParameters = new HashMap<>();
            //base64 --> fileId
            createdFileUrls = uploadApkParameterDataFiles(base64FileParameters, convertedParameters);
            //合并file parameter到parameters
            if (!CollectionUtils.isEmpty(parameters)) {
                for (Map.Entry<String, String> entry : parameters.entrySet()) {
                    convertedParameters.put(ParameterInfo.convertPidToFormId(entry.getKey()), entry.getValue());
                }
            }
            return convertedParameters;
        } catch (Exception ex) {
            //如果保存失败,需要将新创建的文件从文件服务器删除
            createdFileUrls.forEach(createdFileUrl -> fileService.delete(createdFileUrl));
            throw ex;
        }
    }

    /**
     * Upload apk parameter data files list.
     *
     * @param base64FileParameters  the base 64 file parameters
     * @param uploadReturnMapValues the upload return map values
     * @return the list
     */
    List<String> uploadApkParameterDataFiles(List<FileParameter> base64FileParameters, Map<String, String> uploadReturnMapValues) {
        List<String> createdFileUrls = new ArrayList<>();
        if (!CollectionUtils.isEmpty(base64FileParameters)) {
            List<String> supportFileSuffix = SystemPropertyHelper.getApkParamDataFileSuffix();
            for (FileParameter fileParameter : base64FileParameters) {
                if (StringUtils.isEmpty(fileParameter.getFileName())
                        || StringUtils.isEmpty(fileParameter.getFileData())
                        || StringUtils.isEmpty(fileParameter.getPid())) {
                    throw new BusinessException(ApiCodes.APK_PARAM_DATA_FILE_TOO_3RD_INVALID);
                }
                if (FileUtils.getBase64FileSize(fileParameter.getFileData()) > SystemConstants.APK_PARAM_DATA_FILE_MAXIMUM_SIZE) {
                    throw new BusinessException(ApiCodes.APK_PARAM_DATA_FILE_TOO_LARGE);
                }
                String fileExtension = FileUtils.getFileExtension(fileParameter.getFileName());
                if (!CollectionUtils.isEmpty(supportFileSuffix) && !supportFileSuffix.contains(fileExtension)) {
                    throw new BusinessException(ApiCodes.APK_PARAM_DATA_FILE_NOT_SUPPORT, null, fileExtension);
                }
            }
            for (FileParameter fileParameter : base64FileParameters) {
                byte[] byteContent = null;
                String base64FileStr = fileParameter.getFileData();
                if (base64FileStr.lastIndexOf(",") != -1) { //一定要去掉base64头部
                    base64FileStr = base64FileStr.substring(base64FileStr.lastIndexOf(",") + 1);
                }
                try {
                    byteContent = Base64.getDecoder().decode(base64FileStr);
                } catch (Exception ex) {
                    throw new BusinessException(ApiCodes.APK_PARAM_DATA_FILE_TOO_INVALID);
                }
                if (byteContent != null) {
                    String fileUrl = FileUploader.uploadFile(byteContent, "paramdata", ".data", SupportedFileTypes.APP_PARAM);
                    String pidKey = ParameterInfo.convertPidToFormId(fileParameter.getPid());
                    uploadReturnMapValues.put(pidKey + SystemConstants.URL_PID_SUFFIX, fileUrl);
                    uploadReturnMapValues.put(pidKey, fileParameter.getFileName());
                    createdFileUrls.add(fileUrl);
                }
            }
        }
        return createdFileUrls;
    }

    /**
     * 获取配置的参数值，如果pidList没有传入那么不返回
     *
     * @param apk               the apk
     * @param paramTemplateName the param template name
     * @param param             the param
     * @param paramVariables    the param variables
     * @param pidList           the pid list
     * @return the configured parameters
     */
    Map<String, String> getConfiguredParameters(Apk apk, String paramTemplateName, String param, String paramVariables, List<String> pidList) {
        if (apk == null || StringUtils.isEmpty(paramTemplateName) || CollectionUtils.isEmpty(pidList)) {
            return null;
        }
        ApkParamTemplate apkParamTemplate = apkParamTemplateService.getParamTemplate(apk.getId(), paramTemplateName);
        SchemaProcess schemaProcess = ApkParameterUtils.getSchemaProcess(apkParamTemplate, param, JsonMapper.fromJsonString(paramVariables), false);
        return getConfiguredParameters(schemaProcess, paramVariables, pidList);
    }

    /**
     * 获取配置的参数值，如果pidList没有传入那么不返回
     *
     * @param schemaProcess  the schema process
     * @param paramVariables the param variables
     * @param pidList        the pid list
     * @return the configured parameters
     */
    Map<String, String> getConfiguredParameters(SchemaProcess schemaProcess, String paramVariables, List<String> pidList) {
        if (CollectionUtils.isEmpty(pidList)) {
            return null;
        }
        Map<String, String> configuredParameters = new HashMap<>();
        for (ParameterInfo parameterInfo : schemaProcess.getUsedParameterInfoList()) {
            for (String pid : pidList) {
                pid = ParameterInfo.convertPidToFormId(pid);
                if (StringUtils.equals(parameterInfo.getFormPid(), pid) || StringUtils.equals(parameterInfo.getFormPidWithFileId(), pid)) {
                    ParameterInfo cloneParameterInfo = BeanMapper.clone(parameterInfo, ParameterInfo.class);
                    if (ParameterUtils.replaceParamVariables(cloneParameterInfo, paramVariables, schemaProcess.getComboParametersMap().get(parameterInfo.getPid()))) {
                        //因为变量是没有经过转换的，所以如果替换了变量值，需要进行转换
                        ParameterUtils.convertAmountValue(cloneParameterInfo, false);
                    }
                    configuredParameters.put(cloneParameterInfo.getPidWithIndex(), cloneParameterInfo.getDefaultValue());
                    break;
                }
            }
        }
        return configuredParameters;
    }

    /**
     * Gets apk by request.
     *
     * @param packageName the package name
     * @param versionName the version name
     * @param resellerId  the reseller id
     * @param modelId     the model id
     * @return the apk by request
     */
    Apk getApkByRequest(String packageName, String versionName, Long resellerId, Long modelId) {
        AppApkQuery appApkQuery = new AppApkQuery();
        appApkQuery.setResellerId(resellerId);
        appApkQuery.setFilterAppReseller(true);
        appApkQuery.setFilterAppPublish(true);
        appApkQuery.setFilterAppSubscribe(true);
        appApkQuery.setModelId(modelId);
        appApkQuery.setAppStatus(AppSwitchStatus.ACTIVE);
        appApkQuery.setPackageName(packageName);
        appApkQuery.setVersionName(versionName);
        if (signatureSupportService.isOnlyWhiteListAppAllowed(getCurrentMarketId(), modelId, null)) {
            appApkQuery.setOnlyWhiteList(true);
        }

        Long apkId = apkService.getLatestApkId(getCurrentMarketId(), null, AppStatus.ONLINE, appApkQuery);
        if (LongUtils.isBlankOrNotPositive(apkId)) {
            throw new BusinessException(ApiCodes.TRD_TERMINAL_APK_APK_NOT_FOUND);
        }
        Apk apk = apkService.get(apkId);
        if (apk == null) {
            throw new BusinessException(ApiCodes.TRD_TERMINAL_APK_APK_NOT_FOUND);
        }
        if (StringUtils.containsIgnoreCase(SystemConstants.VAS_APP_PACKAGE_NAMES, apk.getPackageName())) {
            throw new BusinessException(ApiCodes.APP_INVALID);
        }
        return apk;
    }
}
