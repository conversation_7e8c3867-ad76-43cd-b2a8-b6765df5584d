/*
 *
 *  * ===========================================================================================
 *  * = COPYRIGHT
 *  *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or nondisclosure
 *  *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *  *   disclosed except in accordance with the terms in that agreement.
 *  *     Copyright (C) 2019. PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 *  * ===========================================================================================
 *
 */

package com.pax.market.api.rest.v1.internal;

import com.pax.api.cache.CacheService;
import com.pax.api.fs.FileService;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.v1.AbstractThirdpartyController;
import com.pax.market.api.service.goinsight.DictionaryDataToGoInsightSender;
import com.pax.market.api.service.internal.InternalThirdPartyClearDataService;
import com.pax.market.api.service.internal.InternalThirdPartySysService;
import com.pax.market.api.thirdparty.dto.base.ErrorResponse;
import com.pax.market.api.thirdparty.dto.base.SuccessResponse;
import com.pax.market.constants.ActivityType;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.market.Activity;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.internal.InternalThirdPartyFunc;
import com.pax.market.functional.utils.ApkParameterParserUtils;
import com.pax.market.functional.vas.goinsight.GoInsightMarketDataSyncFunc;
import com.pax.market.mq.contract.report.GenerateTerminalEnrollFilesMessage;
import com.pax.market.mq.contract.report.GenerateTerminalReportMessage;
import com.pax.market.mq.contract.sync.internal.RefreshResellerInstalledApkMessage;
import com.pax.market.mq.contract.user.UserMarketChangedMessage;
import com.pax.market.mq.producer.gateway.sync.InternalDataSyncGateway;
import com.pax.market.mq.producer.gateway.user.UserMarketChangedGateway;
import com.pax.market.schedule.mq.producer.gateway.billing.GenerateTerminalReportGateway;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.support.dynamic.datasource.tools.PaxDsUtils;
import com.paxstore.global.domain.service.app.ApkParamTemplateService;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.report.ReportExecutionContextService;
import com.paxstore.global.domain.service.vas.VasConfigEntityService;
import com.paxstore.market.domain.service.ActivityService;
import com.paxstore.market.domain.service.pushtask.TerminalGroupActionCountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * The type Internal sys sync controller.
 */
@RestController("InternalSysSyncControllerV1")
@RequestMapping(value = "v1/3rdsys/internal")
@Tag(name = "【InternalSysSync】", description = "API for Internal third-party system sync")
@RequiredArgsConstructor
public class InternalSysSyncController extends AbstractThirdpartyController {

    private final TerminalGroupActionCountService terminalGroupActionCountService;
    private final InternalThirdPartySysService internalThirdPartySysService;
    private final ActivityService activityService;
    private final VasConfigEntityService vasConfigEntityService;
    private final GoInsightMarketDataSyncFunc goInsightMarketDataSyncFunc;
    private final ReportExecutionContextService reportExecutionContextService;
    private final InternalThirdPartyFunc internalThirdPartyFunc;
    private final DictionaryDataToGoInsightSender dictionaryDataToGoInsightSender;
    private final UserMarketChangedGateway userMarketChangedGateway;
    private final MarketService marketService;
    private final AppService appService;
    private final ApkService apkService;
    private final ApkParamTemplateService apkParamTemplateService;
    private final FileService fileService;
    private final CacheService cacheService;
    private final InternalDataSyncGateway internalDataSyncGateway;
    private final GenerateTerminalReportGateway generateTerminalReportGateway;
    private final InternalThirdPartyClearDataService internalThirdPartyClearDataService;

    /**
     * Refresh push count.
     *
     * @param action      the action
     * @param actionType  the action type
     * @param referenceId the reference id
     * @return the success response
     */
    @RequestMapping(value = "refreshGroupActionCount", method = RequestMethod.POST)
    @Operation(summary = "Refresh Group Action Count",
            parameters = {
                    @Parameter(name = "actionType", description = "actionType", in = ParameterIn.QUERY, schema = @Schema(type = "Integer")),
                    @Parameter(name = "referenceId", description = "referenceId", in = ParameterIn.QUERY, schema = @Schema(type = "long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshGroupActionCount(@RequestParam(required = false) String action,
                                                   @RequestParam(required = false) Integer actionType,
                                                   @RequestParam(required = false) Long referenceId) {
        if (actionType == null || referenceId == null) {
            return new SuccessResponse(internalThirdPartySysService.refreshGroupActionCount(action));
        }
        int refreshedCount = 0;
        for (String datasource : PaxDsUtils.getAllDataSources()) {
            PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
            refreshedCount += terminalGroupActionCountService.refreshGroupActionCount(actionType, referenceId);
        }
        return new SuccessResponse("refreshedCount: " + refreshedCount);
    }

    /**
     * Clear group pending terminal actions success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "clearGroupPendingTerminalActions", method = RequestMethod.POST)
    @Operation(summary = "Clear Group Pending Terminal Actions",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse clearGroupPendingTerminalActions(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartyClearDataService.clearGroupPendingTerminalActions(action));
    }

    /**
     * Repair group push task success response.
     *
     * @param action      the action
     * @param actionType  the action type
     * @param referenceId the reference id
     * @return the success response
     */
    @RequestMapping(value = "repairGroupPushTask", method = RequestMethod.POST)
    @Operation(summary = "Repair Group Push Task",
            parameters = {
                    @Parameter(name = "actionType", description = "actionType", in = ParameterIn.QUERY, schema = @Schema(type = "Integer")),
                    @Parameter(name = "referenceId", description = "referenceId", in = ParameterIn.QUERY, schema = @Schema(type = "long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse repairGroupPushTask(@RequestParam(required = false) String action,
                                               @RequestParam(required = false) Integer actionType,
                                               @RequestParam(required = false) Long referenceId) {
        return new SuccessResponse(internalThirdPartySysService.repairGroupPushTask(action, actionType, referenceId));
    }

    /**
     * Clear terminal pending terminal actions success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "clearTerminalPendingTerminalActions", method = RequestMethod.POST)
    @Operation(summary = "Clear Terminal Pending Terminal Actions",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse clearTerminalPendingTerminalActions(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartyClearDataService.clearTerminalPendingTerminalActions(action));
    }

    /**
     * Clear expired pending terminal actions success response.
     *
     * @param action      the action
     * @param expireMonth the expire month
     * @return the success response
     */
    @RequestMapping(value = "clearExpiredPendingTerminalActions", method = RequestMethod.POST)
    @Operation(summary = "Clear Expired Pending Terminal Actions",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
                    @Parameter(name = "expireMonth", description = "expireMonth", in = ParameterIn.QUERY, schema = @Schema(type = "Integer")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse clearExpiredPendingTerminalActions(@RequestParam(required = false) String action, @RequestParam(required = false) Integer expireMonth) {
        if (expireMonth == null) {
            expireMonth = 6;
        }
        return new SuccessResponse(internalThirdPartyClearDataService.clearExpiredPendingTerminalActions(action, expireMonth));
    }


    /**
     * Refresh reseller installed apks success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshResellerInstalledApks", method = RequestMethod.POST)
    @Operation(summary = "Refresh Reseller Installed Apks",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshResellerInstalledApks(@RequestParam(required = false) String action) {
        String refreshStatus = (String) cacheService.get(CacheNames.LOCK_CACHE, "refreshResellerInstalledApks");
        String message = "";
        if (StringUtils.equalsIgnoreCase("status", action)) {
            if (refreshStatus == null) {
                message = "No refreshResellerInstalledApks job is running.";
            } else {
                message = "refreshResellerInstalledApks job is in-progress: " + refreshStatus;
            }
        } else {
            if (refreshStatus != null) {
                message = "refreshResellerInstalledApks job is in-progress: " + refreshStatus;
            } else {
                internalDataSyncGateway.send(new RefreshResellerInstalledApkMessage());
            }
        }
        return new SuccessResponse(message);
    }

    /**
     * Refresh terminal online status success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshTerminalOnlineStatus", method = RequestMethod.POST)
    @Operation(summary = "Refresh Terminal Online Status",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshTerminalOnlineStatus(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.refreshTerminalOnlineStatus(action));
    }

    /**
     * Refresh terminal last access time success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshTerminalLastAccessTime", method = RequestMethod.POST)
    @Operation(summary = "Refresh Terminal Last Access Time",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshTerminalLastAccessTime(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.refreshTerminalLastAccessTime(action));
    }

    /**
     * Refresh terminal stock success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshTerminalStock", method = RequestMethod.POST)
    @Operation(summary = "Refresh Terminal Stock",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshTerminalStock(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.refreshTerminalStock(action));
    }

    /**
     * Clear deleted terminals success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "clearDeletedTerminals", method = RequestMethod.DELETE)
    @Operation(summary = "Clear Deleted Terminals",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
                    @Parameter(name = "type", description = "type", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse clearDeletedTerminals(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartyClearDataService.clearDeletedTerminals(action));
    }

    /**
     * Generate apk patch md 5 success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "generateApkPatchMd5", method = RequestMethod.POST)
    @Operation(summary = "Generate Apk Patch Md5",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse generateApkPatchMd5(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.generateApkPatchMd5(action));
    }

    /**
     * Generate client apk patch md 5 success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "generateClientApkPatchMd5", method = RequestMethod.POST)
    @Operation(summary = "Generate Apk Patch Md5",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse generateClientApkPatchMd5(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.generateClientApkPatchMd5(action));
    }

    /**
     * Refresh push task download time success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshPushTaskDownloadTime", method = RequestMethod.POST)
    @Operation(summary = "Refresh Push Task Download Time",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshPushTaskDownloadTime(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.refreshPushTaskDownloadTime(action));
    }

    /**
     * Refresh terminal last apk param success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshTerminalLastApkParam", method = RequestMethod.POST)
    @Operation(summary = "Refresh Terminal Last Apk Param",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshTerminalLastApkParam(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.refreshTerminalLastApkParam(action));
    }

    /**
     * Refresh reseller pushed param apk success response.
     *
     * @param action the action
     * @return the success response
     */
    @RequestMapping(value = "refreshResellerPushedParamApk", method = RequestMethod.POST)
    @Operation(summary = "Refresh reseller pushed param apk",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshResellerPushedParamApk(@RequestParam(required = false) String action) {
        return new SuccessResponse(internalThirdPartySysService.refreshResellerPushedParamApk(action));
    }

    /**
     * Repair activity status success response.
     *
     * @return the success response
     */
    @RequestMapping(value = "repairActivityStatus", method = RequestMethod.POST)
    @Operation(summary = "Repair Activity Status",
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse repairActivityStatus() {
        int repairedCount = 0;
        for (String datasource : PaxDsUtils.getAllDataSources()) {
            PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
            repairedCount += activityService.repairActivityStatus();
        }
        return new SuccessResponse("repairedCount: " + repairedCount);
    }

    /**
     * Repair report execution context status success response.
     *
     * @return the success response
     */
    @RequestMapping(value = "repairReportExecutionContextStatus", method = RequestMethod.POST)
    @Operation(summary = "Repair ReportExecutionContext Status",
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse repairReportExecutionContextStatus() {
        int repairedCount = reportExecutionContextService.repairReportExecutionContextStatus();
        return new SuccessResponse("repairedCount: " + repairedCount);
    }

    /**
     * Repair merchant migration status success response.
     *
     * @return the success response
     */
    @RequestMapping(value = "repairMerchantMigrationStatus", method = RequestMethod.POST)
    @Operation(summary = "Repair Merchant Migration Status",
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse repairMerchantMigrationStatus() {
        int repairedCount = 0;
        for (String datasource : PaxDsUtils.getAllDataSources()) {
            PaxDynamicDsThreadLocal.setPreferenceDatasource(datasource);
            repairedCount += merchantService.repairMerchantMigrationStatus();
        }
        return new SuccessResponse("repairedCount: " + repairedCount);
    }

    /**
     * Refresh user market success response.
     *
     * @param marketId the market id
     * @return the success response
     */
    @RequestMapping(value = "refreshUserMarket", method = RequestMethod.POST)
    @Operation(summary = "Repair User Market",
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshUserMarket(@RequestParam Long marketId) {
        userMarketChangedGateway.send(new UserMarketChangedMessage(marketId));
        return new SuccessResponse();
    }

    /**
     * Refresh apk param template success response.
     *
     * @param marketDomain the market domain
     * @param packageName  the package name
     * @return the success response
     */
    @RequestMapping(value = "refreshApkParamTemplate", method = RequestMethod.POST)
    @Operation(summary = "Refresh Apk Param Template",
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse refreshApkParamTemplate(@RequestParam String marketDomain, @RequestParam String packageName) {
        Market market = marketService.findMarketByDomain(marketDomain);
        if (market == null) {
            return new SuccessResponse("Market Not Found");
        }
        App app = appService.getByPackageName(market.getId(), packageName);
        if (app == null) {
            return new SuccessResponse("App Not Found");
        }
        List<Long> apkIdList = apkService.findApkIdsByAppId(app.getId());
        if (CollectionUtils.isEmpty(apkIdList)) {
            return new SuccessResponse("Apk Not Found");
        }
        int totalCount = 0;
        for (Long apkId : apkIdList) {
            List<ApkParamTemplate> apkParamTemplateList = apkParamTemplateService.findParamTemplateListIncludeDelete(apkId);
            for (ApkParamTemplate apkParamTemplate : apkParamTemplateList) {
                if (StringUtils.isEmpty(apkParamTemplate.getZipFileUrl())) {
                    continue;
                }
                byte[] paramTemplateFile = fileService.download(apkParamTemplate.getZipFileUrl());
                if (paramTemplateFile == null) {
                    continue;
                }
                ApkParamTemplate newApkParamTemplate = ApkParameterParserUtils.parseParamTemplate(apkParamTemplate.getName(), paramTemplateFile, true, false);
                apkParamTemplate.setParam(newApkParamTemplate.getParam());
                apkParamTemplateService.updateParamTemplate(apkParamTemplate);
                totalCount++;
            }
        }
        return new SuccessResponse(String.format("Total %d templates updated", totalCount));
    }

    /**
     * Send dictionary data to go insight success response.
     *
     * @param marketId   the market id
     * @param resellerId the reseller id
     * @param merchantId the merchant id
     * @param terminalId the terminal id
     * @param factoryId  the factory id
     * @param modelId    the model id
     * @return the success response
     */
    @RequestMapping(value = "insight/sync/dictionary-data", method = RequestMethod.POST)
    @Operation(summary = "Sync Dictionary Data to GoInsight",
            parameters = {
                    @Parameter(name = "marketId", description = "marketId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
                    @Parameter(name = "resellerId", description = "resellerId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
                    @Parameter(name = "merchantId", description = "merchantId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
                    @Parameter(name = "terminalId", description = "terminalId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
                    @Parameter(name = "factoryId", description = "factoryId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
                    @Parameter(name = "modelId", description = "modelId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse sendDictionaryDataToGoInsight(@RequestParam(required = false) Long marketId,
                                                         @RequestParam(required = false) Long resellerId,
                                                         @RequestParam(required = false) Long merchantId,
                                                         @RequestParam(required = false) Long terminalId,
                                                         @RequestParam(required = false) Long factoryId,
                                                         @RequestParam(required = false) Long modelId) {
        if (!vasConfigEntityService.isVasEnabledGlobally()) {
            return new SuccessResponse("Vas Disabled");
        }
        String resultMsg = "";
        if(Objects.nonNull(marketId)){
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
        }
        if (LongUtils.isNotBlankAndPositive(marketId)) {
            resultMsg = dictionaryDataToGoInsightSender.reSendMarketDataToGoInsight(marketId);
        }
        if (LongUtils.isNotBlankAndPositive(resellerId)) {
            resultMsg += dictionaryDataToGoInsightSender.reSendResellerDataToGoInsight(resellerId);
        }
        if (LongUtils.isNotBlankAndPositive(merchantId)) {
            resultMsg += dictionaryDataToGoInsightSender.reSendMerchantDataToGoInsight(merchantId);
        }
        if (LongUtils.isNotBlankAndPositive(terminalId)) {
            resultMsg += dictionaryDataToGoInsightSender.reSendTerminalDataToGoInsight(terminalId);
        }
        if (LongUtils.isNotBlankAndPositive(factoryId)) {
            resultMsg += dictionaryDataToGoInsightSender.reSendFactoryDataToGoInsight(factoryId);
        }
        if (Objects.nonNull(modelId)) {
            resultMsg += dictionaryDataToGoInsightSender.reSendModelDataToGoInsight(modelId);
        }
        return new SuccessResponse(resultMsg);
    }

    /**
     * Send market data to go insight success response.
     *
     * @param action    the action
     * @param marketIds the market ids
     * @return the success response
     */
    @PostMapping(value = "insight/sync/market-data")
    @Operation(summary = "Sync Market Data to GoInsight",
            parameters = {
                    @Parameter(name = "action", description = "action", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
                    @Parameter(name = "marketIds", description = "marketIds", in = ParameterIn.QUERY, schema = @Schema(type = "String")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse sendMarketDataToGoInsight(@RequestParam(required = false) String action, @RequestParam(required = false) String marketIds) {

        if (!vasConfigEntityService.isVasEnabledGlobally()) {
            return new SuccessResponse("Vas Disabled");
        }
        return new SuccessResponse(goInsightMarketDataSyncFunc.sendMarketDataToInsight(action, marketIds));
    }


    /**
     * migrationGroupFilteredAction.
     *
     * @param actionType  the action type
     * @param referenceId the reference id
     * @return the success response
     */
    @RequestMapping(value = "migrationGroupFilteredAction", method = RequestMethod.POST)
    @Operation(summary = "Migration Group Filtered Action",
            parameters = {
                    @Parameter(name = "actionType", description = "actionType", in = ParameterIn.QUERY, schema = @Schema(type = "Integer")),
                    @Parameter(name = "referenceId", description = "referenceId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse migrationGroupFilteredAction(@RequestParam(required = false) Integer actionType,
                                                        @RequestParam(required = false) Long referenceId) {
        return new SuccessResponse(internalThirdPartyFunc.migrationGroupFilteredAction(actionType, referenceId));

    }


    @RequestMapping(value = "generateTerminalEnrollFiles", method = RequestMethod.POST)
    @Operation(summary = "Generate Terminal Enroll Files",
            parameters = {
                    @Parameter(name = "marketId", description = "marketId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
                    @Parameter(name = "year", description = "year", in = ParameterIn.QUERY, schema = @Schema(type = "Integer")),
                    @Parameter(name = "month", description = "month", in = ParameterIn.QUERY, schema = @Schema(type = "Integer")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse generateTerminalEnrollFiles(@RequestParam Long marketId,
                                                       @RequestParam int year,
                                                       @RequestParam int month) {
        PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
        Activity activity = activityService.createActivity(MessageUtils.getLocaleMessage("title.generate.terminal.enroll.files", LocaleUtils.getEnvLocale()), ActivityType.EXPORT_TERMINAL, marketId);
        GenerateTerminalReportMessage message = new GenerateTerminalEnrollFilesMessage(activity.getId(), marketId, year, month);
        generateTerminalReportGateway.send(message);
        PaxDynamicDsThreadLocal.removePreferenceMarketId();
        return new SuccessResponse();
    }

    /**
     * clearWebHookMessages.
     *
     * @return the success response
     */
    @RequestMapping(value = "clearWebHookMessages", method = RequestMethod.DELETE)
    @Operation(summary = "Clear WebHook Messages",
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse clearDeletedWebHookMessageHistory() {
        internalThirdPartyClearDataService.clearDeletedWebHookMessageHistory();
        return new SuccessResponse();
    }
}
