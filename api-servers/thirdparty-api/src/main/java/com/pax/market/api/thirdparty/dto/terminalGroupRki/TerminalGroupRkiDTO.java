/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.thirdparty.dto.terminalGroupRki;

import com.pax.market.api.thirdparty.dto.terminalRki.TerminalRkiDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/9/24
 */
@Getter
@Setter
public class TerminalGroupRkiDTO extends TerminalRkiDTO {
    private static final long serialVersionUID = -332536754281415003L;

    private int pendingCount;
    @Schema(description ="the number of terminal group apk push task executed successfully", example = "100")
    private int successCount;
    @Schema(description ="the number of terminal group apk push task failed", example = "100")
    private int failedCount;
    @Schema(description = "whether the  push task is completed",example = "true")
    private Boolean completed;
    private PushStrategyDTO pushStrategy;
}
