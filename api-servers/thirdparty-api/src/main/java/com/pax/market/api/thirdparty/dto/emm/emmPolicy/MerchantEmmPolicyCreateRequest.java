package com.pax.market.api.thirdparty.dto.emm.emmPolicy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
@Schema(description = "merchant EMM policy create request body")
public class MerchantEmmPolicyCreateRequest extends EmmPolicyCreateRequest {

    @Serial
    private static final long serialVersionUID = -5303836970824650280L;

    @Schema(description = "Merchant name, max length is 64", requiredMode = Schema.RequiredMode.REQUIRED, example = "M1")
    private String merchantName;

}