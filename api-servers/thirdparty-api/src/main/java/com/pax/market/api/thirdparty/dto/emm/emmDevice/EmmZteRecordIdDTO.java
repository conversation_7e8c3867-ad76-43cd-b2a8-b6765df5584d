package com.pax.market.api.thirdparty.dto.emm.emmDevice;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
@Schema(description = "EMM zte record id information")
public class EmmZteRecordIdDTO extends AbstractDTO {

    @Serial
    private static final long serialVersionUID = -2518833761598459826L;

    @Schema(description = "EMM zte record id", example = "1000000000")
    private Long id;

}
