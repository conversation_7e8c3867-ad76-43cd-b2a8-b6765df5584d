/*
 * ********************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */
package com.pax.market.api.rest.v1;


import com.google.common.collect.Maps;
import com.pax.core.exception.BusinessException;
import com.pax.core.json.JsonMapper;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.thirdparty.dto.base.SuccessResponse;
import com.pax.market.api.thirdparty.dto.terminalApkParameter.*;
import com.pax.market.api.utils.ParameterComboUtils;
import com.pax.market.api.validators.ThirdPartyApkParameterCreateValidator;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.AppStatus;
import com.pax.market.constants.AppType;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.ApkParamTemplate;
import com.pax.market.domain.entity.market.app.ApkParameter;
import com.pax.market.domain.parameter.ParameterUtils;
import com.pax.market.domain.parameter.SchemaProcess;
import com.pax.market.dto.app.ApkParameterInfo;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.Collections3;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.validation.Validators;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Description
 * @Author: Shawn
 * @Date: 2019/10/28 15:06
 * @Version 7.0
 */
@RestController("ThirdPartySysApkParameterControllerV1")
@RequestMapping(value = "v1/3rdsys/apkParameters")
@Tag(name = "【ApkParameter】", description = "Apk Parameter APIs")
public class ThirdPartySysApkParameterController extends ThirdPartySysBasePushController {

    @RequestMapping(method = RequestMethod.POST)
    @Operation(summary = "Create Apk Parameter",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = CreateApkParameterRequest.class)), required = true))
    public SuccessResponse createApkTemplate(
            @RequestBody CreateApkParameterRequest createRequest) {
        Validators.validate(new ThirdPartyApkParameterCreateValidator(createRequest));
        Long apkId = getApkId(createRequest.getPackageName(), createRequest.getVersion());
        if (apkId == null) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }
        String paramTemplateName = createRequest.getParamTemplateName();
        Long resellerId = getCurrentThirdPartySysInfo().getReseller().getId();
        Apk apk = apkService.get(apkId);
        String name = StringUtils.trim(createRequest.getName());
        List<ApkParamTemplate> paramTemplateList = apkParamTemplateService.findParamTemplateList(Arrays.asList(apkId));
        validateCreate(apk, resellerId, name, paramTemplateList);

        ApkParameter apkParameter = new ApkParameter();
        apkParameter.setApk(apk);
        apkParameter.setName(name);
        apkParameter.setResellerId(resellerId);

        if (StringUtils.isNotBlank(paramTemplateName)) {
            ApkParamTemplate apkParamTemplate = apkParamTemplateService.getParamTemplate(apk.getId(), StringUtils.trim(paramTemplateName));
            apkParameter.setParamTemplateName(apkParamTemplate.getName());
            apkParameter.setParamTemplateId(apkParamTemplate.getParamTemplateId());
        } else {
            apkParameter.setParamTemplateName(paramTemplateList.get(0).getName());
            apkParameter.setParamTemplateId(paramTemplateList.get(0).getParamTemplateId());
        }

        Map<String, String> requestParameters = getApkParametersFromRequest(createRequest.getParameters(), createRequest.getBase64FileParameters());
        saveApkParameterWithDefaultValue(apkParameter, requestParameters, createRequest.getValidateUndefinedParameter(), ParameterComboUtils.getOriginalRequestKeys(createRequest.getParameters(), createRequest.getBase64FileParameters()));
        return new SuccessResponse();
    }


    private ApkParameterInfo saveApkParameterWithDefaultValue(ApkParameter apkParameter, Map<String, String> requestParameters, Boolean validateUndefinedParameter, Set<String> originalRequestKeys) {
        ApkParamTemplate apkParamTemplate = apkParamTemplateService.getParamTemplate(apkParameter.getApk().getId(), apkParameter.getParamTemplateName());
        if (apkParamTemplate != null) {
            Map<String, String> defaultValues = JsonMapper.fromJsonString(apkParamTemplate.getParam());
            if (defaultValues == null) {
                defaultValues = Maps.newHashMap();
            }
            if (requestParameters != null) {
                defaultValues.putAll(requestParameters);
            }
            SchemaProcess schemaProcess = SchemaProcess.create()
                    .defaultValues(defaultValues)
                    .originalValues(JsonMapper.fromJsonString(apkParamTemplate.getParam()))
                    .parseXml(apkParamTemplate.getParamTemplate());
            String invalidMsg = schemaProcess.getInvalidMsg();
            if (StringUtils.isNotBlank(invalidMsg)) {
                throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, ParameterUtils.convertInvalidMsg(invalidMsg));
            }
            invalidMsg = schemaProcess.ensureNoChangedReadonlyParamValues(requestParameters);
            if (StringUtils.isNotBlank(invalidMsg)) {
                throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, ParameterUtils.convertInvalidMsg(invalidMsg));
            }
            if (BooleanUtils.isTrue(validateUndefinedParameter) && requestParameters != null) {
                invalidMsg = schemaProcess.validateParameterKeysExist(originalRequestKeys);
                if (StringUtils.isNotBlank(invalidMsg)) {
                    throw new BusinessException(ApiCodes.INVALID_APK_PARAMETER, ParameterUtils.convertInvalidMsg(invalidMsg));
                }
            }
            apkParameter.setParamTemplateId(apkParamTemplate.getParamTemplateId());
            apkParameter.setParam(JsonMapper.toJsonString(schemaProcess.getDefaultValues()));
            apkParameterService.save(apkParameter);
            return BeanMapper.map(apkParameter, ApkParameterInfo.class);
        } else {
            throw new BusinessException(ApiCodes.APK_PARAMETER_NOT_FOUND);
        }
    }


    @RequestMapping(method = RequestMethod.GET)
    @Operation(summary = "Find Apk Parameter List",
            parameters = {
                    @Parameter(name = "pageNo", required = true, description = "The page number, example: 1", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "pageSize", required = true, description = "The page size, example: 5", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "orderBy", description = "The sort order of search result, the value can be one of a.created_date DESC, a.created_date ASC", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
                    @Parameter(name = "templateName", description = "Apk parameterName, example: paramTemplate", schema = @Schema(type = "string")),
                    @Parameter(name = "packageName", required = true, description = "Apk packageName, example: com.app.package", schema = @Schema(type = "string")),
                    @Parameter(name = "versionName", required = true, description = "Apk versionName, example: 1.0", schema = @Schema(type = "string"))
            })
    public ApkParameterPageResponse findApkParameters(
            @RequestParam(required = false) String templateName,
            @RequestParam String packageName,
            @RequestParam String versionName
    ) {
        ApkParameter apkParameter = new ApkParameter();
        apkParameter.setName(StringUtils.trim(templateName));
        List<Long> apkIdList = apkService.findApkIdsByPackageNameAndVersionName(getCurrentMarketId(), packageName, versionName, true);
        if (Collections3.isEmpty(apkIdList)) {
            throw new BusinessException(ApiCodes.TRD_TERMINAL_APK_APK_NOT_FOUND);
        }
        apkParameter.setApkIdList(apkIdList);
        Page<ApkParameter> apkParameterPage = apkParameterService.findPage(parsePage(), apkParameter);
        for (ApkParameter each : apkParameterPage.getList()) {
            each.setApk(apkService.get(each.getApk()));
        }
        return convertPage(apkParameterPage, BeanMapper.mapList(apkParameterPage.getList(), ApkParameterDTO.class), ApkParameterPageResponse.class);
    }

    /**
     * Gets apk parameter.
     *
     * @param apkParameterId the apk parameter id
     */
    @RequestMapping(value = "{apkParameterId}", method = RequestMethod.GET)
    @Operation(summary = "Get Apk Parameter Details",
            parameters = {
                    @Parameter(name = "apkParameterId", required = true, description = "App parameter push id, example: 1000000000", schema = @Schema(type = "Long")),
                    @Parameter(name = "pidList", description = "Defined parameters to return, example: [sys.pid.test01, sys.pid.test02, sys.pid.test03]", in = ParameterIn.QUERY, array = @ArraySchema(schema = @Schema(implementation = String.class, type = "string")))
            })
    public ApkParameterResponse getApkParameterById(
            @PathVariable Long apkParameterId,
            @RequestParam(required = false) List<String> pidList) {
        ApkParameter apkParameter = thirdPartySysApkParameterService.getApkParameter(apkParameterId);
        ApkParameterDTO apkParameterDTO = BeanMapper.map(apkParameter, ApkParameterDTO.class);
        apkParameterDTO.setApkAvailable(true); //参数模板始终允许编辑
        apkParameterDTO.setConfiguredParameters(getConfiguredParameters(apkParameter.getApk(), apkParameter.getParamTemplateName(), apkParameterService.getParam(apkParameterId), null, pidList));
        return new ApkParameterResponse(apkParameterDTO);
    }


    @RequestMapping(value = "{apkParameterId}", method = RequestMethod.PUT)
    @Operation(summary = "Update Apk Parameter",
            parameters = {
                    @Parameter(name = "apkParameterId", required = true, description = "App parameter push id, example: 1000000000", schema = @Schema(type = "long"))
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = UpdateApkParameterRequest.class)), required = true))
    public SuccessResponse updateApkParameter(
            @PathVariable Long apkParameterId,
            @RequestBody UpdateApkParameterRequest updateRequest) {
        if (updateRequest == null || apkParameterId == null ||
                (updateRequest.getParameters() == null && StringUtils.isBlank(updateRequest.getParamTemplateName()))) {
            throw new BusinessException(ApiCodes.THIRD_PARTY_INVALID_REQUEST_PARAMETER);
        }
        ApkParameter apkParameter = thirdPartySysApkParameterService.getApkParameter(apkParameterId);
        if (StringUtils.isNotBlank(updateRequest.getParamTemplateName())) {
            apkParameter.setParamTemplateName(updateRequest.getParamTemplateName());
        }

        apkParamTemplateService.checkParamTemplateExist(apkParameter.getApk(), updateRequest.getParamTemplateName());

        Map<String, String> requestParameters = getApkParametersFromRequest(updateRequest.getParameters(), updateRequest.getBase64FileParameters());
        saveApkParameterWithDefaultValue(apkParameter, requestParameters, updateRequest.getValidateUndefinedParameter(), ParameterComboUtils.getOriginalRequestKeys(updateRequest.getParameters(), updateRequest.getBase64FileParameters()));
        return new SuccessResponse();
    }


    @RequestMapping(value = "{apkParameterId}", method = RequestMethod.DELETE)
    @Operation(summary = "Update Apk Parameter",
            parameters = {
                    @Parameter(name = "apkParameterId", required = true, description = "App parameter push id, example: 1000000000", schema = @Schema(type = "Long"))
            })
    public void deleteApkParameter(
            @PathVariable Long apkParameterId) {
        apkParameterService.delete(thirdPartySysApkParameterService.getApkParameter(apkParameterId));
    }


    private Long getApkId(String packageName, String versionName) {
        return apkService.getApkId(getCurrentMarketId(), packageName, versionName, AppStatus.ONLINE);
    }

    private void validateCreate(Apk apk, Long resellerId, String name, List<ApkParamTemplate> paramTemplateList) {
        if (resellerId == null) {
            throw new BusinessException(ApiCodes.RESELLER_NOT_EXIST);
        }
        if (apk == null) {
            throw new BusinessException(ApiCodes.APK_NOT_FOUND);
        }
        if (!StringUtils.equals(AppStatus.ONLINE, apk.getStatus())) {
            throw new BusinessException(ApiCodes.INVALID_APP_STATUS);
        }

        if (AppType.SOLUTION.equals(apk.getAppType())) {
            if (!apkService.isSolutionApkAvailable(apk.getId(), null)) {
                throw new BusinessException(ApiCodes.APP_UNAVAILABLE);
            }
        } else if (!apkService.isApkAvailable(apk.getId(),
                getCurrentMarketId(),
                resellerId,
                null,
                null)) {
            throw new BusinessException(ApiCodes.APP_UNAVAILABLE);
        }

        if (paramTemplateList == null || paramTemplateList.isEmpty()) {
            throw new BusinessException(ApiCodes.APK_PARAM_TEMPLATE_NOT_FOUND);
        }

        if (StringUtils.isEmpty(name)) {
            throw new BusinessException(ApiCodes.APK_PARAMETER_NAME_MANDATORY);
        }
        if (name.length() >= SystemConstants.APK_PARAMETER_NAME_LENGTH) {
            throw new BusinessException(ApiCodes.APK_PARAMETER_NAME_TOO_LONG);
        }
        if (apkParameterService.isNameExist(name, apk.getId(), resellerId)) {
            throw new BusinessException(ApiCodes.APK_PARAMETER_NAME_EXIST);
        }
    }

}
