/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.thirdparty.dto.terminal;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 终端在线状态，网络，电池状态
 * <AUTHOR>
 * @date 2023/4/19
 */
public class TerminalNetworkDTO extends AbstractDTO {
    private static final long serialVersionUID = -913921067406634385L;

    @Schema(description = "Terminal Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "**********")
    private Long id;

    @Schema(description = "Terminal TID", requiredMode = Schema.RequiredMode.REQUIRED, example = "R15ET7F5")
    private String tid;

    @Schema(description = "Terminal SN", example = "1640000069TID")
    private String serialNo;

    @Schema(description = "Terminal status", requiredMode = Schema.RequiredMode.REQUIRED, example = "A")
    private String status;

    @Schema(description = "Terminal battery", example = "91")
    private Float battery;

    @Schema(description = "Terminal's online status. the value can be 0(Unknown), 1(Offline), 2(Online)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer onlineStatus;

    @Schema(description = "Terminal's network status. the value can be NETWORK_WIFI, NETWORK_5G,NETWORK_4G,NETWORK_3G,NETWORK_2G,NETWORK_ETHERNET,NETWORK_UNKNOWN", example = "NETWORK_WIFI")
    private String network;

    @Schema(description = "Terminal mac address", example = "08:00:20:0A:8C:6D")
    private String macAddress;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Float getBattery() {
        return battery;
    }

    public void setBattery(Float battery) {
        this.battery = battery;
    }

    public Integer getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(Integer onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }
}