/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.api.thirdparty.dto.terminal;

import com.pax.market.api.thirdparty.dto.base.Response;

public class TerminalNetworkDTOResponse extends Response<TerminalNetworkDTO> {


    private static final long serialVersionUID = -5954040735389936926L;

    public TerminalNetworkDTOResponse() {

    }

    public TerminalNetworkDTOResponse(TerminalNetworkDTO terminal) {
        super(terminal);
    }

}
