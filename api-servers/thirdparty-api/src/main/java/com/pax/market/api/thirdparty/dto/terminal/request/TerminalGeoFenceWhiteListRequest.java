/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.api.thirdparty.dto.terminal.request;

import com.pax.market.api.thirdparty.dto.base.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/2/22
 */
@Schema(description = "Terminal geoFence whitelist request body")
@Getter
@Setter
public class TerminalGeoFenceWhiteListRequest extends AbstractDTO {
    @Serial
    private static final long serialVersionUID = 3112374949275507395L;

    @Schema(description = "Terminal SN", example = "**********")
    private String serialNo;
}
