package com.pax.market.api.rest.v1.emm;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.v1.AbstractThirdpartyController;
import com.pax.market.api.service.emm.ThirdPartySysEmmPolicyService;
import com.pax.market.api.thirdparty.dto.base.SuccessResponse;
import com.pax.market.api.thirdparty.dto.emm.emmPolicy.EmmPolicyResponse;
import com.pax.market.api.thirdparty.dto.emm.emmPolicy.MerchantEmmPolicyCreateRequest;
import com.pax.market.api.thirdparty.dto.emm.emmPolicy.ResellerEmmPolicyCreateRequest;
import com.pax.market.api.validators.ThirdPartyCheckValidator;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.organization.Reseller;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RequiredArgsConstructor
@RestController("ThirdPartySysEmmPolicyControllerV1")
@RequestMapping(value = "v1/3rdsys/emm/policy")
@Tag(name = "【EMMPolicy】", description = "EMM Policy APIs")
public class ThirdPartySysEmmPolicyController extends AbstractThirdpartyController {

    private final ThirdPartySysEmmPolicyService thirdPartySysEmmPolicyService;

    private final ThirdPartyCheckValidator thirdPartyCheckValidator;

    @GetMapping("reseller")
    @Operation(summary = "Get Reseller EMM Policy",
            parameters = {
                    @Parameter(name = "resellerName", required = true, description = "resellerName, only the policy under the reseller, example: R1")
            })
    public EmmPolicyResponse getResellerEmmPolicy(@RequestParam String resellerName) throws IOException {
        return thirdPartySysEmmPolicyService.getResellerEmmPolicy(getCurrentMarketId(), resellerName);
    }


    @GetMapping("merchant")
    @Operation(summary = "Get Merchant EMM Policy",
            parameters = {
                    @Parameter(name = "resellerName", required = true, description = "resellerName, only the policy under the reseller, example: R1"),
                    @Parameter(name = "merchantName", required = true, description = "merchantName, only the policy under the merchant, example: M1")
            })
    public EmmPolicyResponse getMerchantEmmPolicy(@RequestParam String resellerName, @RequestParam String merchantName) throws IOException {
        return thirdPartySysEmmPolicyService.getMerchantEmmPolicy(getCurrentMarketId(), resellerName, merchantName);
    }


    @PostMapping("reseller")
    @Audit(type = AuditTypes.RESELLER, primaryAction = AuditActions.EMM_POLICY, secondaryAction = AuditActions.UPDATE_EMM_POLICY, dataType = AuditDataTypes.EMM_RESELLER_POLICY)
    @Operation(summary = "Create Reseller EMM Policy",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON),
                    required = true)
    )
    public SuccessResponse createResellerEmmPolicy(@RequestBody ResellerEmmPolicyCreateRequest request) throws IOException {
        Reseller reseller = thirdPartyCheckValidator.checkResellerNameValid(request.getResellerName());
        loadAuditEntityIdAndOldRecord(reseller.getId());
        thirdPartySysEmmPolicyService.createResellerEmmPolicy(getCurrentMarketId(), reseller, request);
        return new SuccessResponse();
    }


    @PostMapping("merchant")
    @Audit(type = AuditTypes.MERCHANT, primaryAction = AuditActions.EMM_POLICY, secondaryAction = AuditActions.UPDATE_EMM_POLICY, dataType = AuditDataTypes.EMM_MERCHANT_POLICY)
    @Operation(summary = "Create Merchant EMM Policy",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON),
                    required = true)
    )
    public SuccessResponse createMerchantEmmPolicy(@RequestBody MerchantEmmPolicyCreateRequest request) throws IOException {
        Reseller reseller = thirdPartyCheckValidator.checkResellerNameValid(request.getResellerName());
        Merchant merchant = thirdPartyCheckValidator.checkMerchantNameValid(reseller.getId(), request.getMerchantName());
        loadAuditEntityIdAndOldRecord(merchant.getId());
        thirdPartySysEmmPolicyService.createMerchantEmmPolicy(getCurrentMarketId(), merchant, request);
        return new SuccessResponse();
    }


}
