/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.api.rest.v1;


import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.service.ThirdPartyTerminalService;
import com.pax.market.api.thirdparty.dto.base.DownloadTaskResponse;
import com.pax.market.api.thirdparty.dto.base.SuccessResponse;
import com.pax.market.api.thirdparty.dto.terminal.*;
import com.pax.market.api.thirdparty.dto.terminal.request.*;
import com.pax.market.api.validators.ThirdPartyCheckValidator;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.constants.TRDTerminalPushCmd;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.entity.market.terminal.TerminalLog;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.persistence.Page;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * The type Third party sys terminal controller.
 */
@RestController("ThirdPartySysTerminalControllerV1")
@RequestMapping(value = "v1/3rdsys/terminals")
@Tag(name = "【TerminalApiBaseOnId】", description = "Terminal APIs")
public class ThirdPartySysTerminalController extends BaseThirdPartySysTerminalController {

    @Autowired
    private ThirdPartyTerminalService thirdPartyTerminalService;
    @Autowired
    private ThirdPartyCheckValidator thirdPartyCheckValidator;

    /**
     * Create terminal terminal response.
     *
     * @param request the request
     * @return the terminal response
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.CREATE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Create Terminal",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Terminal create request",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalCreateRequest.class)), required = true))
    public TerminalResponse createTerminal(
            @RequestBody(required = true) TerminalCreateRequest request) {
        return super.createTerminal(request);
    }

    /**
     * Update terminal terminal response.
     *
     * @param terminalId the terminal id
     * @param request    the request
     * @return the terminal response
     */
    @PutMapping("{terminalId}")
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.UPDATE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Update Terminal",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = "id of terminal, example: 1000000000", schema = @Schema(type = "long")),
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Terminal update request",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalUpdateRequest.class)), required = true))
    public TerminalResponse updateTerminal(
            @PathVariable Long terminalId,
            @RequestBody TerminalUpdateRequest request) {
        Terminal terminal = thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        return super.updateTerminal(terminal, request);
    }

    /**
     * copy terminal.
     *
     * @param request the copy request
     * @return the terminal response
     */
    @PostMapping("copy")
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.UPDATE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Copy Terminal",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalCopyRequest.class)), required = true))
    public TerminalResponse copyTerminal(@RequestBody TerminalCopyRequest request) {
        return super.copyTerminal(request);
    }

    /**
     * Find terminals termina page response.
     *
     * @param status                   the status
     * @param snNameTID                the sn name tid
     * @param includeGeoLocation       the include geo location
     * @param includeInstalledApks     the include installed apks
     * @param includeInstalledFirmware the include installed firmware
     * @return the termina page response
     */
    @GetMapping
    @Operation(summary = "Find Terminal List by Page",
            parameters = {
                    @Parameter(name = "pageNo", required = true, description = "The page number, example: 1", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "pageSize", required = true, description = "The page size, example: 5", in = ParameterIn.QUERY, schema = @Schema(type = "int")),
                    @Parameter(name = "orderBy", description = "The sort order of search result, the value can be one of name, tid, serialNo", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
                    @Parameter(name = "resellerName", description = "resellerName, only the terminals under the reseller or it's child resellers will be returned, example: R1", schema = @Schema(type = "string")),
                    @Parameter(name = "merchantName", description = "merchantName, only the terminals under the reseller merchant will be returned, example: M1", schema = @Schema(type = "string")),
                    @Parameter(name = "status", description = "status, value can be one of A(Active), P(Pending), S(Suspend,Inactive)", schema = @Schema(type = "string")),
                    @Parameter(name = "snNameTID", description = "snNameTID, either of serialNo, name or tid exactly match this property will be returned, example: SN123456", schema = @Schema(type = "string")),
                    @Parameter(name = "includeGeoLocation", description = "includeGeoLocation, whether to return the geo location, example: true", schema = @Schema(type = "boolean")),
                    @Parameter(name = "includeInstalledApks", description = "includeInstalledApks, whether to return the installed applications, example: true", schema = @Schema(type = "boolean")),
                    @Parameter(name = "includeInstalledFirmware", description = "includeInstalledFirmware, whether to return installed firmware, example: true", schema = @Schema(type = "boolean"))
            })
    public TerminalPageResponse findTerminals(
            @RequestParam(required = false) String resellerName,
            @RequestParam(required = false) String merchantName,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String snNameTID,
            @RequestParam(required = false) Boolean includeGeoLocation,
            @RequestParam(required = false) Boolean includeInstalledApks,
            @RequestParam(required = false) Boolean includeInstalledFirmware) {
        return super.findTerminals(resellerName, merchantName, status, snNameTID, includeGeoLocation, includeInstalledApks, includeInstalledFirmware);
    }

    /**
     * Gets terminal.
     *
     * @param terminalId the terminal id
     * @return the terminal
     */
    @GetMapping("{terminalId}")
    @Operation(summary = "Get Terminal by Id",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long")),
                    @Parameter(name = "includeDetailInfo", description = "includeDetailInfo, whether to return Detail Info and Accessory Info, example: false", schema = @Schema(type = "boolean")),
                    @Parameter(name = "includeDetailInfoList", description = "includeDetailInfoList, whether to return Detail Info and Accessory Info list, example: false", schema = @Schema(type = "boolean")),
                    @Parameter(name = "includeInstalledApks", description = "includeInstalledApks, whether to return the installed applications, example: false", schema = @Schema(type = "boolean")),
                    @Parameter(name = "includeInstalledFirmware", description = "includeInstalledFirmware, whether to return the installed firmware, example: false", schema = @Schema(type = "boolean")),
            })
    public TerminalResponse getTerminal(
            @PathVariable Long terminalId,
            @RequestParam(required = false) Boolean includeDetailInfo,
            @RequestParam(required = false) Boolean includeDetailInfoList,
            @RequestParam(required = false) Boolean includeInstalledApks,
            @RequestParam(required = false) Boolean includeInstalledFirmware) {
        return super.getTerminal(terminalId, includeDetailInfo, includeDetailInfoList, includeInstalledApks, includeInstalledFirmware);
    }


    /**
     * Activate terminal.
     *
     * @param terminalId the terminal id
     */
    @PutMapping("{terminalId}/active")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.ACTIVATE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Activate a terminal by terminalId as Path Variable, only Pending and Suspend terminal can be activated",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    public void activateTerminalInPath(
            @PathVariable Long terminalId) {
        super.activateTerminal(terminalId);
    }


    /**
     * Activate terminal 2.
     *
     * @param terminalId the terminal id
     */
    @PutMapping("active")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.ACTIVATE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Activate a terminal by terminalId as request parameter, only Pending and Suspend terminal can be activated",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    public void activateTerminalInParam(
            @RequestParam Long terminalId) {
        super.activateTerminal(terminalId);
    }


    /**
     * Disable terminal.
     *
     * @param terminalId the terminal id
     */
    @PutMapping("{terminalId}/disable")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.DISABLE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Disable a terminal, only Active terminal can be disabled",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    public void disableTerminal(
            @PathVariable Long terminalId) {
        thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        super.disableTerminal(terminalId);
    }

    @PutMapping("{terminalId}/move")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.MOVE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Move Terminal", description = "Move a terminal to another reseller and merchant",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalMoveRequest.class)), required = true)
    )
    public void moveTerminal(
            @PathVariable Long terminalId,
            @RequestBody TerminalMoveRequest terminalMoveRequest) {
        super.moveTerminal(terminalId, terminalMoveRequest);
    }

    /**
     * Del terminal.
     *
     * @param terminalId the terminal id
     */
    @DeleteMapping("{terminalId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.DELETE, dataType = AuditDataTypes.TERMINAL)
    @Operation(summary = "Delete a terminal, only Pending, Suspend terminal can be deleted",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    public void deleteTerminal(
            @PathVariable Long terminalId) {
        thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        super.deleteTerminal(terminalId);
    }

    /**
     * Add terminal move in group.
     *
     * @param terminalGroupRequest the group terminal request
     * @return the abstract response
     * @throws IOException the io exception
     */
    @RequestMapping(value = "groups", method = RequestMethod.POST)
    @Audit(primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.ADD, type = AuditTypes.TERMINAL_GROUP, dataType = AuditDataTypes.GROUP_TERMINAL_UPDATE)
    @Operation(summary = "Batch Add Terminal to Group",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalGroupRequest.class)), required = true))
    public SuccessResponse createTerminalsGroup(
            @RequestBody TerminalGroupRequest terminalGroupRequest) throws IOException {
        return super.createTerminalsGroup(terminalGroupRequest);
    }

    /**
     * Gets terminal ped status.
     *
     * @param terminalId the terminal id
     * @return the terminal ped status
     */
    @GetMapping("{terminalId}/ped")
    @Operation(summary = "Get Terminal PED Information by Terminal Id",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    public TerminalPedInfoResponse getTerminalPed(
            @PathVariable Long terminalId) {
        return super.getTerminalPed(terminalId);
    }


    /**
     * Update terminal remote config.
     *
     * @param terminalId            the terminal id
     * @param terminalConfigRequest the terminal update remote config request
     * @return the abstract response
     * @throws IOException the io exception
     */
    @RequestMapping(value = "{terminalId}/config", method = RequestMethod.PUT)
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.UPDATE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "Update Terminal Configuration", description = "Update terminal configurations like allow replacement",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalConfigUpdateRequest.class)), required = true)
    )
    public void updateTerminalConfig(@PathVariable Long terminalId,
                                     @RequestBody TerminalConfigUpdateRequest terminalConfigRequest) throws IOException {
        super.updateTerminalConfig(terminalId, terminalConfigRequest);
    }

    /**
     * get terminal remote config.
     *
     * @param terminalId the terminal id
     * @return the terminal remote configG
     * @throws IOException the io exception
     */
    @RequestMapping(value = "{terminalId}/config", method = RequestMethod.GET)
    @Operation(summary = "Get Terminal Configuration", description = "Get terminal configurations like allow replacement",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    public TerminalConfigResponse getTerminalConfig(
            @PathVariable Long terminalId) throws IOException {
        return super.getTerminalConfig(terminalId);
    }


    /**
     * Push terminal actions.
     * LOCK_TM and RESTART_TM
     *
     * @param terminalId the terminal id
     * @throws IOException the io exception
     */
    @RequestMapping(value = "{terminalId}/operation", method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "Push Command to Terminal", description = "Push lock, unlock and restart command to specified terminal",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long")),
                    @Parameter(name = "command", description = "Command", required = true, schema = @Schema(implementation = TRDTerminalPushCmd.class))
            })
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.OPERATION, dataType = AuditDataTypes.OPERATION)
    public void pushTerminalAction(
            @PathVariable Long terminalId,
            @RequestParam TRDTerminalPushCmd command) throws IOException {
        super.pushTerminalAction(terminalId, command);
    }


    /**
     * Gets terminal network dto.
     *
     * @param serialNo the terminal id
     * @return the terminal
     */
    @GetMapping("network")
    @Operation(summary = "Get Terminal Network by SN Or TID",
            parameters = {
                    @Parameter(name = "serialNo", description = "the serialNo of terminal, example: SN123456", schema = @Schema(type = "string")),
                    @Parameter(name = "tid", description = "the tid of terminal, example: TID123456", schema = @Schema(type = "string"))
            })
    public TerminalNetworkDTOResponse getTerminalNetwork(
            @RequestParam(required = false) String serialNo,
            @RequestParam(required = false) String tid) {
        return new TerminalNetworkDTOResponse(thirdPartyTerminalService.getTerminalNetworkDTO(serialNo, tid));
    }


    /**
     * Send terminal message
     *
     * @return the terminal
     */
    @PostMapping("{terminalId}/push/message")
    @Operation(summary = "Push message to terminal ",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            })
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.OPERATION, dataType = AuditDataTypes.OPERATION)
    public SuccessResponse pushTerminalMessage(@PathVariable Long terminalId, @RequestBody TerminalMessageRequest terminalMessageRequest) {
        Terminal terminal =  thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        thirdPartyTerminalService.pushTerminalAction4SendMsg(terminal, terminalMessageRequest);
        return new SuccessResponse();
    }

    /**
     * Gets apks statistics.
     * for view monitor dialog
     * @param terminalId the terminal id
     * @throws IOException the io exception
     */
    @GetMapping("{terminalId}/system/usage")
    @ApiOperation(value = "Get terminal system usage", response = TerminalSystemUsageResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = TerminalSystemUsageResponse.class)})
    public TerminalSystemUsageResponse getTerminalSystemUsageById(@PathVariable Long terminalId) throws IOException {
        return super.getTerminalSystemUsage(terminalId);
    }

    @PostMapping("{terminalId}/collect/log")
    @Operation(summary = "Collect terminal log ",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Request body",
                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON,
                            schema = @Schema(implementation = TerminalLogCatRequest.class)), required = true)
    )
    public SuccessResponse collectTerminalLog(@PathVariable Long terminalId, @RequestBody TerminalLogCatRequest request) {
        thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        return super.collectTerminalLog(terminalId, request);
    }

    @GetMapping("{terminalId}/logs")
    @Operation(summary = "search terminal log ",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long"))
            }
    )
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = TerminalLogPageResponse.class)})
    public TerminalLogPageResponse searchTerminalLogPage(@PathVariable Long terminalId) {
        thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        Page<TerminalLog> terminalLogPage = super.searchTerminalLogs(terminalId);
        return convertPage(terminalLogPage, BeanMapper.mapList(terminalLogPage.getList(), TerminalLogDTO.class), TerminalLogPageResponse.class);
    }

    @GetMapping("{terminalId}/logs/{terminalLogId}/download-task")
    @Operation(summary = "Get terminal log download task",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long")),
                    @Parameter(name = "terminalLogId", required = true, description = "Terminal Log Id, example: 1000000000", schema = @Schema(type = "long"))
            }
    )
    public DownloadTaskResponse getTerminalLogDownloadTask(@PathVariable Long terminalId,
                                                           @PathVariable Long terminalLogId) {
        Terminal terminal = thirdPartyCheckValidator.checkTerminalIdValid(terminalId);
        return super.getTerminalLogDownloadTask(terminal, terminalLogId);
    }

    @PutMapping("{terminalId}/model")
    @Operation(summary = "change terminal model by id",
            parameters = {
                    @Parameter(name = "terminalId", required = true, description = API_PARAM_DESC_TERMINAL_ID, schema = @Schema(type = "long")),
                    @Parameter(name = "modelName", required = true, description = API_PARAM_DESC_TERMINAL_MODEL, schema = @Schema(type = "string"))
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = ApiConstants.STR_HTTP_STATUS_OK, content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = SuccessResponse.class)))})
    public SuccessResponse changeModelById(@PathVariable Long terminalId, @RequestParam String modelName) {
        return super.changeModel(terminalId, modelName);
    }

}
