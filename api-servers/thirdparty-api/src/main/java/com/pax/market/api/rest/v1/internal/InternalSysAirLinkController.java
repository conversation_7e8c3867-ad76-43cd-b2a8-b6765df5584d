package com.pax.market.api.rest.v1.internal;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.v1.AbstractThirdpartyController;
import com.pax.market.api.thirdparty.dto.base.ErrorResponse;
import com.pax.market.api.thirdparty.dto.base.SuccessResponse;
import com.pax.market.constants.airlink.AirLinkTerminalOperateLevel;
import com.pax.market.dto.PageInfo;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.mq.contract.airlink.AirLinkTerminalActiveHistoryDeductMessage;
import com.pax.market.mq.contract.airlink.AirLinkTerminalMonthDeductMessage;
import com.pax.market.mq.contract.airlink.AirLinkTerminalOverageTrafficDeductMessage;
import com.pax.market.mq.producer.gateway.airlink.AirLinkTerminalDeductGateway;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * author mengxiaoxian
 * Date   2025/5/23 9:51
 */
@RestController("InternalSysAirLinkController")
@RequestMapping(value = "v1/3rdsys/internal/airlink")
@Tag(name = "【InternalSysAirLink】", description = "AirLink内部调用相关的RestAPI")
@RequiredArgsConstructor
public class InternalSysAirLinkController extends AbstractThirdpartyController {

    private final AirLinkTerminalDeductGateway airLinkTerminalDeductGateway;

    /**
     * 扣除市场AirLink终端当月包月费用
     * @param marketId 市场id
     * @return response
     */
    @RequestMapping(value = "deduct/month/package-fee", method = RequestMethod.POST)
    @Operation(summary = "deduct market current month package fee",
            parameters = {
                    @Parameter(name = "marketId", required = true, description = "marketId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = PageInfo.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse deductMarketCurrentMonthFee(@RequestParam Long marketId){
        AirLinkTerminalMonthDeductMessage message = new AirLinkTerminalMonthDeductMessage();
        message.setMarketId(marketId);
        message.setDeductTime(new Date());
        airLinkTerminalDeductGateway.send(message);
        return new SuccessResponse();
    }

    /**
     * 扣除市场上月超额流量费用，产生属于上个月周期的订单
     * 由于上月账单是在本月2号同步到金牛座
     * 所以请在2号之前调用该接口
     * @param marketId 市场id
     * @return response
     */
    @RequestMapping(value = "deduct/lastmonth/overage-traffic-fee", method = RequestMethod.POST)
    @Operation(summary = "deduct market last month traffic fee",
            parameters = {
                    @Parameter(name = "marketId", required = true, description = "marketId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = PageInfo.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse deductMarketLastMonthTrafficFee(@RequestParam Long marketId){
        AirLinkTerminalOverageTrafficDeductMessage message = new AirLinkTerminalOverageTrafficDeductMessage();
        message.setMarketId(marketId);
        message.setDeductTime(DateUtils.lastMonth(new Date()));
        airLinkTerminalDeductGateway.send(message);
        return new SuccessResponse();
    }

    /**
     * 扣除市场上月终端激活费用，产生属于上个月周期的订单
     * 由于上月账单是在本月2号同步到金牛座
     * 所以请在2号之前调用该接口
     * @param marketId 市场id
     * @return response
     */
    @RequestMapping(value = "deduct/lastmonth/active-fee", method = RequestMethod.POST)
    @Operation(summary = "deduct market last month active fee",
            parameters = {
                    @Parameter(name = "marketId", required = true, description = "marketId", in = ParameterIn.QUERY, schema = @Schema(type = "Long")),
            },
            responses =
                    {@io.swagger.v3.oas.annotations.responses.ApiResponse(
                            responseCode = ApiConstants.STR_HTTP_STATUS_OK,
                            description = API_SUCCESS,
                            content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = PageInfo.class))),
                            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                                    responseCode = ApiConstants.STR_HTTP_STATUS_BAD_REQUEST,
                                    description = API_BAD_REQ,
                                    content = @Content(mediaType = ApiConstants.STR_HTTP_MEDIA_TYPE_JSON, schema = @Schema(implementation = ErrorResponse.class)))
                    })
    public SuccessResponse deductMarketLastMonthActiveFee(@RequestParam Long marketId){
        Date date = DateUtils.endOfMonth(DateUtils.lastMonth(new Date()));
        AirLinkTerminalActiveHistoryDeductMessage message = new AirLinkTerminalActiveHistoryDeductMessage();
        message.setMarketId(marketId);
        message.setNeedDeduct(true);
        message.setCloseActiveHistory(false);
        message.setDeductTime(date);
        message.setTerminalOperateLevel(AirLinkTerminalOperateLevel.MARKET);
        message.setDeductLastMonth(true);
        airLinkTerminalDeductGateway.send(message);
        return new SuccessResponse();
    }


}
