/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1.account;


import com.google.zxing.WriterException;
import com.pax.core.exception.BusinessException;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.rest.v1.account.support.QRCodes;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.constants.ApiCodes;
import com.pax.market.domain.entity.global.user.UserOTP;
import com.pax.market.domain.util.PasswordUtils;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.request.user.ChangePasswordRequest;
import com.pax.market.dto.request.user.UserUpdateRequest;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.functional.account.AccountUserFuncService;
import com.pax.market.vo.account.AccountVo;
import com.pax.market.vo.account.UserOTPBackupCodeVo;
import com.pax.market.vo.account.UserOTPQrSecretVo;
import com.pax.market.vo.account.UserOTPVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController("AccountUserController")
@RequestMapping("v1/account/user")
@Api(value = "【Account User API】")
@RequiredArgsConstructor
public class AccountUserController extends BaseController {

    private final AccountUserFuncService accountUserFuncService;

    /**
     * Gets user profile.
     *
     * @throws IOException the io exception
     */
    @GetMapping( "profile")
    @ApiOperation(value = "获取当前用户信息", response = AccountVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = AccountVo.class)})
    public void getUserProfile() throws IOException {
        sendResponse(BeanMapper.map(accountUserFuncService.getCurrentUserInfo(), AccountVo.class));
    }

    /**
     * Update user.
     *
     * @param request      the request
     * @throws IOException the io exception
     */
    @PutMapping("profile")
    @ApiOperation(value = "更新用户信息", response = AccountVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更新成功", response = AccountVo.class)})
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.UPDATE)
    public void updateUser(HttpServletRequest request) throws IOException {
        UserUpdateRequest userUpdateRequest = getParameterValue(request, "userData", UserUpdateRequest.class);
        if (userUpdateRequest == null) {
            throw new BusinessException(ApiCodes.INVALID_REQUEST_PARAMETER);
        }
        sendResponse(ApiUtils.getSuccessJson(BeanMapper.map(accountUserFuncService.updateUser(request, userUpdateRequest), AccountVo.class)));
    }

    /**
     * Reset user email.
     *
     * @throws IOException the io exception
     */
    @PostMapping( "reset-email")
    @ApiOperation(value = "发送用户更改邮箱邮件", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功")})
    public void resetUserEmail() throws IOException {
        accountUserFuncService.resetUserEmail();
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Change user password.
     *
     * @param request the change password request
     * @throws IOException          the io exception
     */
    @PostMapping( "change-password")
    @ApiOperation(value = "更改密码", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更改成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.CHANGE_PASSWORD)
    public void changeUserPwd(@RequestBody ChangePasswordRequest request) throws IOException {
    	request.setLoginName(getCurrentUser().getLoginName());
    	request.setOriginalPassword(PasswordUtils.decryptWebPassword(request.getOriginalPassword()));
    	request.setPlainPassword(PasswordUtils.decryptWebPassword(request.getPlainPassword()));
        accountUserFuncService.changeUserPassword(request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Bind otp.
     * @throws WriterException the writer exception
     * @throws IOException     the io exception
     */
    @GetMapping("otp/qrcode")
    @ApiOperation(value = "获取当前用户OTP二维码图片")
    public void bindOTP() throws WriterException, IOException {
        UserOTP userOTP = accountUserFuncService.bindOTP();
        UserOTPQrSecretVo result = new UserOTPQrSecretVo();
        result.setQrContent(QRCodes.generateUserOtpBindingQRCode(getCurrentUser().getLoginName(), userOTP.getSecret()));
        result.setKey(userOTP.getSecret());
        sendResponse(ApiUtils.getSuccessJson(result));
    }

    /**
     * Verify otp.
     *
     * @param code the code
     * @throws IOException the io exception
     */
    @PostMapping( "otp/activate")
    @ApiOperation(value = "启用当前用户OTP")
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.ACTIVATE_OTP)
    public void activateOTP(@RequestParam int code) throws IOException {
        sendResponse(UserOTPBackupCodeVo.builder().backupCode(accountUserFuncService.activateOTP(code)).build());
    }

    /**
     * Reset otp backup code.
     *
     * @param code the code
     * @throws IOException the io exception
     */
    @PostMapping( "otp/reset")
    @ApiOperation(value = "重置用户OTP的backupCode")
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.RESET_OTP_BACKUP_CODE)
    public void resetOtpBackupCode(@RequestParam int code) throws IOException {
        sendResponse(UserOTPBackupCodeVo.builder().backupCode(accountUserFuncService.resetOtpBackupCode(code)).build());
    }

    /**
     * Gets otp.
     *
     * @throws IOException the io exception
     */
    @GetMapping("otp")
    @ApiOperation(value = "获取当前用户OTP")
    public void getOTP() throws IOException {
        sendResponse(ApiUtils.getSuccessJson(BeanMapper.map(accountUserFuncService.getOTP(), UserOTPVo.class)));
    }

    /**
     * Disable otp.
     *
     * @param code         the code
     * @throws IOException the io exception
     */
    @PostMapping( "otp/disable")
    @ApiOperation(value = "关闭当前用户OTP")
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.DISABLE_OTP)
    public void disableOTP(@RequestParam int code) throws IOException {
        accountUserFuncService.disableOTP(code);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Delete user.
     *
     * @throws IOException the io exception
     */
    @DeleteMapping
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.DELETE)
    @ApiOperation(value = "注销删除用户")
    public void deleteAccount(@RequestParam String code) throws IOException {
        accountUserFuncService.deleteAccount(getCurrentUserId(), code);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Send code.
     *
     * @throws IOException the io exception
     */
    @PostMapping( "delete")
    @ApiOperation(value = "发送删除用户验证码邮件")
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.DELETE)
    public void sendDeleteAccountCode() throws IOException {
        accountUserFuncService.sendDeleteUserCode();
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Gets otp.
     *
     * @throws IOException the io exception
     */
    @PostMapping( "send-usage")
    @ApiOperation(value = "配置是否允许发送使用数据开关")
    @Audit(type = AuditTypes.USER, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.CHANGE_DATA_COLLECTION)
    public void updateAllowSendUsageData(@RequestParam Boolean isAllow) throws IOException {
        accountUserFuncService.updateSendUsageDataStatus(BooleanUtils.toBoolean(isAllow));
        sendResponse(ApiUtils.getSuccessJson());
    }
}
