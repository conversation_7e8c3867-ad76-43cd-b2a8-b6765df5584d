package com.pax.market.api.rest.v1.common;

import com.pax.market.api.captcha.CaptchaService;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.request.common.CaptchaRefreshRequest;
import com.pax.market.functional.common.CommonUserFunc;
import com.pax.market.vo.common.AgreementAgreedVo;
import com.pax.market.vo.common.router.RouterSwitchVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController("CommonUserControllerV1")
@RequestMapping("v1/common/users")
@Api(value = "【Common System Rest API】")
@RequiredArgsConstructor
public class CommonUserController extends BaseController {

    private final CommonUserFunc commonUserFunc;
    private final CaptchaService captchaService;

    /**
     * Gets captcha.
     *
     * @param captchaRefreshRequest the captcha refresh request
     * @throws IOException the io exception
     */
    @PostMapping("captcha")
    public void generateCaptcha(@RequestBody CaptchaRefreshRequest captchaRefreshRequest) throws IOException {
        sendResponse(captchaService.generateCaptcha(captchaRefreshRequest.getToken()));
    }

    /**
     * Verify captcha.
     *
     * @throws AuthenticationException the authentication exception
     * @throws IOException             the io exception
     */
    @GetMapping("captcha/verify")
    @ApiOperation(value = "验证当前用户验证码", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "操作成功", response = BaseResponse.class)})
    public void verifyCaptcha(@RequestParam String captcha) throws AuthenticationException, IOException {
        captchaService.validateCaptcha(captcha, false);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * get User Router Switch
     *
     * @throws IOException the io exception
     */
    @GetMapping("routers")
    @ApiOperation(value = "获取当前用户拥有的市场路由相关信息")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "操作成功", response = PageInfo.class)})
    public void getCurrentUserRouterSwitchList() throws IOException {
        List<RouterSwitchVo> routerSwitchVoList = commonUserFunc.getCurrentUserRouterSwitchList();
        sendResponse(new PageInfo<>(routerSwitchVoList, routerSwitchVoList.size()));
    }


    @GetMapping("agreement/agreed")
    @ApiOperation(value = "获取用户在当前市场同意的协议")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "操作成功", response = AgreementAgreedVo.class)})
    public void getCurrentUserAgreementAgreed() throws IOException {
        sendResponse(commonUserFunc.getCurrentUserAgreementAgreed());
    }

}
