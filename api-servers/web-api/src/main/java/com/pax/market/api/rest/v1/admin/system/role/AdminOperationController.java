package com.pax.market.api.rest.v1.admin.system.role;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.OperationInfo;
import com.pax.market.dto.PageInfo;
import com.pax.market.functional.admin.system.role.AdminOperationFunc;
import com.pax.market.vo.admin.system.role.OperationUserVo;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.biz.annotation.Audit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController("AdminOperationControllerV1")
@RequestMapping("v1/admin/operations")
@Api(value = "【操作设置-Operation】", description = "操作设置相关的RestAPI")
@RequiredArgsConstructor
public class AdminOperationController extends BaseController {

    private final AdminOperationFunc adminOperationFunc;

    /**
     * Gets protected operations.
     *
     * @throws IOException the io exception
     */
    @GetMapping
    @ApiOperation(value = "获取所有受保护的操作列表", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void findProtectedOperations() throws IOException {
        sendResponse(adminOperationFunc.findProtectedOperations());
    }

    /**
     * Gets protected operation.
     *
     * @param key the key
     * @throws IOException the io exception
     */
    @GetMapping("{key}")
    @ApiOperation(value = "获取所有受保护的操作", response = List.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = OperationInfo.class)})
    public void getProtectedOperation(@PathVariable(value = "key") String key) throws IOException {
        sendResponse(adminOperationFunc.getProtectedOperation(key));
    }

    /**
     * Open operation.
     *
     * @param key the key
     * @throws IOException the io exception
     */
    @PutMapping("{key}/open")
    @ApiOperation(value = "打开操作权限", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功")})
    @Audit(type = AuditTypes.OPERATION_CONTROL, primaryAction = AuditActions.OPEN, dataType = AuditDataTypes.OPERATION_CONTROL)
    public void openOperation(@PathVariable(value = "key") String key) throws IOException {
        adminOperationFunc.changeOperation(key, true);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Close operation.
     *
     * @param key the key
     * @throws IOException the io exception
     */
    @PutMapping("{key}/close")
    @ApiOperation(value = "关闭操作权限", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功")})
    @Audit(type = AuditTypes.OPERATION_CONTROL, primaryAction = AuditActions.CLOSE, dataType = AuditDataTypes.OPERATION_CONTROL)
    public void closeOperation(@PathVariable(value = "key") String key) throws IOException {
        adminOperationFunc.changeOperation(key, false);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Gets market setting users.
     *
     * @param key      the key
     * @param userName the user name
     * @throws IOException the io exception
     */
    @GetMapping("{key}/users")
    @ApiOperation(value = "查询操作用户", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getOperationUsers(@PathVariable(value = "key") String key, @RequestParam(required = false) String userName) throws IOException {
        sendResponse(adminOperationFunc.getOperationUsers(key, userName));
    }

    /**
     * Create operation user.
     *
     * @param key    the key
     * @param userId the user id
     * @throws IOException the io exception
     */
    @PostMapping("{key}/users/{userId}")
    @ApiOperation(value = "添加操作用户", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.OPERATION_CONTROL, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.ADD, dataType = AuditDataTypes.OPERATION_CONTROL)
    public void addOperationUser(@PathVariable(value = "key") String key,
                                    @PathVariable(value = "userId") Long userId) throws IOException {
        adminOperationFunc.addOperationUser(key, userId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Remove operation user.
     *
     * @param key    the key
     * @param userId the user id
     * @throws IOException the io exception
     */
    @DeleteMapping( "{key}/users/{userId}")
    @ApiOperation(value = "删除操作用户", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "删除成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.OPERATION_CONTROL, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.REMOVE, dataType = AuditDataTypes.OPERATION_CONTROL)
    public void removeOperationUser(@PathVariable(value = "key") String key,
                                    @PathVariable(value = "userId") Long userId) throws IOException {
        adminOperationFunc.removeOperationUser(key, userId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Gets role user.
     *
     * @param name the name
     * @throws IOException the io exception
     */
    @GetMapping("user")
    @ApiOperation(value = "获取角色的用户", response = OperationUserVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = OperationUserVo.class)})
    public void getUser(@RequestParam String name, @RequestParam(required = false) String operationKey) throws IOException {
        sendResponse(adminOperationFunc.getUser(name, operationKey));
    }

    /**
     * Open operation.
     *
     * @param key the key
     * @throws IOException the io exception
     */
    @PutMapping("{key}/reseller/operation/open")
    @ApiOperation(value = "打开子代理商操作权限", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功")})
    @Audit(type = AuditTypes.OPERATION_CONTROL, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.OPEN, dataType = AuditDataTypes.OPERATION_CONTROL)
    public void openResellerOperationControl(@PathVariable(value = "key") String key) throws IOException {
        adminOperationFunc.changeResellerOperation(key, true);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Close operation.
     *
     * @param key the key
     * @throws IOException the io exception
     */
    @PutMapping("{key}/reseller/operation/close")
    @ApiOperation(value = "关闭子代理商操作权限", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功")})
    @Audit(type = AuditTypes.OPERATION_CONTROL, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.CLOSE, dataType = AuditDataTypes.OPERATION_CONTROL)
    public void closeResellerOperationControl(@PathVariable(value = "key") String key) throws IOException {
        adminOperationFunc.changeResellerOperation(key, false);
        sendResponse(ApiUtils.getSuccessJson());
    }
}
