/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.api.security.cross;

import com.pax.market.api.utils.ApiUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * The type Cross filter.
 */
@Component
public class CrossFilter implements Filter {

//    @Value("${token.header}")
//    private String tokenHeader;
//    @Value("${market.header}")
//    private String marketHeader;
//    @Value("${market.host.header}")
//    private String marketHostHeader;
//    @Value("${reseller.header}")
//    private String resellerHeader;

    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        ApiUtils.addWebApiCrossHeaders(request, (HttpServletResponse) res);
        chain.doFilter(new TrimWrappedHttpServletRequest(request), res);
    }

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void destroy() {
    }


    static class TrimWrappedHttpServletRequest extends HttpServletRequestWrapper {
        private final Map<String, String[]> parameterMap;

        public TrimWrappedHttpServletRequest(HttpServletRequest request) {
            super(request);
            Map<String, String[]> requestParameterMap = request.getParameterMap();
            this.parameterMap = new HashMap<>(requestParameterMap.size());
            for (Map.Entry<String, String[]> entry : requestParameterMap.entrySet()) {
                String key = entry.getKey();
                String[] value = entry.getValue();
                for (int i = 0; i < value.length; i++) {
                    value[i] = value[i].trim();
                }
                this.parameterMap.put(key, value);
            }
        }

        @Override
        public String getParameter(String name) {
            String[] values = parameterMap.get(name);
            if (values != null && values.length > 0) {
                return values[0];
            }
            return null;
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            return parameterMap;
        }

        @Override
        public Enumeration<String> getParameterNames() {
            return Collections.enumeration(parameterMap.keySet());
        }

        @Override
        public String[] getParameterValues(String name) {
            return parameterMap.get(name);
        }
    }

}