/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1.admin.monitoring;


import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.constants.DashboardConstants;
import com.pax.market.domain.entity.report.ReportForWidget;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.ListResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.dashboard.AdminDashboardInfo;
import com.pax.market.dto.request.activity.ExportDigitalDisplayRequest;
import com.pax.market.dto.request.admin.monitoring.map.MapTerminalsRequest;
import com.pax.market.dto.response.report.StackedBarResponse;
import com.pax.market.dto.widget.DigitalDisplayWidget;
import com.pax.market.dto.widget.DigitalDisplayWidgetInfo;
import com.pax.market.dto.widget.DigitalDisplayWidgetRequest;
import com.pax.market.functional.admin.monitoring.dashboard.DashboardFuncService;
import com.pax.market.functional.admin.monitoring.geolocation.TerminalLocationFuncService;
import com.pax.market.vo.admin.monitoring.dashboard.DashboardVo;
import com.pax.market.vo.admin.monitoring.dashboard.TerminalQtySummaryVo;
import com.pax.market.vo.admin.monitoring.dashboard.TerminalQtyTopTenByResellerVo;
import com.pax.market.vo.admin.monitoring.geolocation.ResellerProfileLocationVo;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;


@RestController("AdminDashboardController")
@RequestMapping("v1/admin/dashboard")
@Api(value = "【Admin Dashboard API】")
@RequiredArgsConstructor
public class AdminDashboardController extends BaseController {

    private final DashboardFuncService dashboardFuncService;
    private final TerminalLocationFuncService terminalLocationFuncService;

    /**
     * Gets dashboard layout.
     *
     * @throws IOException the io exception
     */
    @GetMapping("layout")
    @ApiOperation(value = "获取应用市场管理员Dashboard布局", response = DashboardVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = DashboardVo.class)})
    public void getDashboardLayout() throws IOException {
        sendResponse(dashboardFuncService.getDashboardLayout());
    }

    /**
     * Save dashboard layout
     *
     * @param adminDashboardInfo the admin dashboard info
     * @throws IOException the io exception
     */
    @PostMapping("layout")
    @ApiOperation(value = "保存应用市场管理员Dashboard布局", response = DashboardVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "保存成功", response = DashboardVo.class)})
    public void saveDashboard(@RequestBody AdminDashboardInfo adminDashboardInfo) throws IOException {
        sendResponse(dashboardFuncService.saveDashboardLayout(adminDashboardInfo));
    }

    /**
     * Gets Dashboard里终端数量部件(W09)的数据
     *
     * @param resellerId   the reseller id
     * @param merchantId   the merchant id
     * @param forceRefresh the force refresh
     * @throws IOException the io exception
     */
    @GetMapping("widgets/" + DashboardConstants.WIDGET_TERNIMAL_QUANTITY_SUMMARY)
    @ApiOperation(value = "获取Dashboard里终端数量部件(W09)的数据", response = TerminalQtySummaryVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = TerminalQtySummaryVo.class)})
    public void getTerminalNumberStatisticData(
            @RequestParam(required = false) Long resellerId,
            @RequestParam(required = false) Long merchantId,
            @RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getTerminalNumberStatisticDataW09(resellerId, merchantId, null, forceRefresh));
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_RESELLER_SUMMARY)
    @ApiOperation(value = "获取Dashboard里代理商终端汇总部件(W12)的数据", response = TerminalQtyTopTenByResellerVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = TerminalQtyTopTenByResellerVo.class)})
    public void getTerminalNumberOfResellerData(
            @RequestParam long resellerId,
            @RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getTerminalNumberOfResellerDataW12(resellerId, null, forceRefresh));
    }

    /**
     * Gets Dashboard里 固件版本-终端数量-组织 (W13)的数据
     *
     * @param forceRefresh the force refresh
     * @throws IOException the io exception
     */
    @GetMapping("widgets/" + DashboardConstants.WIDGET_FM_TERMINAL_ORGANIZE)
    @ApiOperation(value = "获取Dashboard里 固件版本-终端数量-组织 (W13)的数据", response = StackedBarResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = StackedBarResponse.class)})
    public void getFmTerminalForWidget(@RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getFmTerminalForWidgetW13(forceRefresh));
    }

    /**
     * Export FmTerminalOrgWidget
     *
     * @param tz the tz
     * @throws IOException the io exception
     */
    @PostMapping("widgets/" + DashboardConstants.WIDGET_FM_TERMINAL_ORGANIZE + "/export")
    @ApiOperation(value = "创建Dashboard里 FM-Terminal_Org(W13) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void exportFmTerminalOrgWidget(@RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportFmTerminalOrgWidgetW13(tz));
    }

    /**
     * Get client terminal widget
     *
     * @param widgetType   the widget type
     * @param forceRefresh the force refresh
     * @throws IOException the io exception
     */
    @GetMapping("widgets/" + DashboardConstants.WIDGET_CLIENT_TERMINAL_ORG)
    @ApiOperation(value = "获取Dashboard里 Client-终端数量-组织 (W14)的数据", response = StackedBarResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = StackedBarResponse.class)})
    public void getClientTerminalWidget(
            @RequestParam Integer widgetType,
            @RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getClientTerminalWidgetW14(widgetType, forceRefresh));
    }

    /**
     * Export client terminal widget
     *
     * @param tz         the tz
     * @param widgetType the widget type
     * @throws IOException the io exception
     */
    @PostMapping("widgets/" + DashboardConstants.WIDGET_CLIENT_TERMINAL_ORG + "/export")
    @ApiOperation(value = "创建Dashboard里 Client-终端数量-组织(W14) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void exportClientTerminalWidget(
            @RequestParam(required = false) String tz,
            @RequestParam Integer widgetType) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportClientTerminalWidget(widgetType, tz));
    }

    /**
     * Gets Reseller profile location
     *
     * @throws IOException the io exception
     */
    @GetMapping("profile/location")
    @ApiOperation(value = "获取代理商定位开启状态", response = ResellerProfileLocationVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = ResellerProfileLocationVo.class)})
    public void getResellerProfile() throws IOException {
        sendResponse(terminalLocationFuncService.getResellerProfileLocation());
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_MODEL_TERMINAL)
    @ApiOperation(value = "获取Dashboard里 机型-终端数量 (W15)的数据", response = ListResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = ListResponse.class)})
    public void loadWidgetModelTerminal(@RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getWidgetModelTerminalW15(forceRefresh));
    }

    @PostMapping("widgets/" + DashboardConstants.WIDGET_MODEL_TERMINAL + "/export")
    @ApiOperation(value = "创建Dashboard里 MODEL-Terminal_Org(W15) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void exportModelTerminalWidget(@RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportWidgetModelTerminalW15(tz));
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_TERMINAL_OFFLINE)
    @ApiOperation(value = "获取Dashboard 长时间 offline (W16)的数据", response = ReportForWidget.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = ReportForWidget.class)})
    public void loadWidgetTerminalOffline(@RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getWidgetTerminalOfflineW16(forceRefresh));
    }

    @PostMapping("widgets/" + DashboardConstants.WIDGET_TERMINAL_OFFLINE + "/export")
    @ApiOperation(value = "创建Dashboard里 offline-terminal(W16) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void exportTerminalOfflineWidget(@RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportWidgetTerminalOfflineW16(tz));
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_CLIENT_TERMINAL)
    @ApiOperation(value = "获取Dashboard里 Client-Terminal(W19) 数据", response = ReportForWidget.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = ReportForWidget.class)})
    public void loadClientTerminalWidget(@RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getClientTerminalWidgetW19(forceRefresh));
    }

    @PostMapping("widgets/" + DashboardConstants.WIDGET_CLIENT_TERMINAL + "/export")
    @ApiOperation(value = "创建Dashboard里 Client-Terminal(W19) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void downloadClientTerminalWidget(@RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(dashboardFuncService.downloadClientTerminalWidgetW19(tz));
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_FM_TERMINAL)
    @ApiOperation(value = "获取Dashboard里 FM-Terminal(W18) 数据", response = ReportForWidget.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = ReportForWidget.class)})
    public void getFmTerminalWidget(@RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getFmTerminalWidgetW18(forceRefresh));
    }

    @PostMapping("widgets/" + DashboardConstants.WIDGET_FM_TERMINAL + "/export")
    @ApiOperation(value = "创建Dashboard里 FM-Terminal(W18) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void exportFmTerminalWidget(@RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportFmTerminalWidgetW18(tz));
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_DIGITAL_DISPLAY + "/number/{type}")
    @ApiOperation(value = "获取Dashboard里 数字卡片(W20) 数量", response = DigitalDisplayWidgetInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = DigitalDisplayWidgetInfo.class)})
    public void getWidgetCardNumberActive(
            @PathVariable("type") String type,
            @RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getWidgetCardNumberActive(type, forceRefresh));
    }

    @PutMapping("widgets/" + DashboardConstants.WIDGET_DIGITAL_DISPLAY + "/setting")
    @ApiOperation(value = "更新Dashboard里 数字卡片(W20) 显示数据", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = BaseResponse.class)})
    public void updateWidgetDigitalDisplay(@RequestBody DigitalDisplayWidgetRequest digitalDisplayWidgetRequest) throws IOException {
        dashboardFuncService.updateWidgetDigitalDisplay(digitalDisplayWidgetRequest);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_DIGITAL_DISPLAY + "/setting")
    @ApiOperation(value = "获取Dashboard里 数字卡片(W20) 设置", response = DigitalDisplayWidget.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = DigitalDisplayWidget.class)})
    public void getWidgetDigitalDisplaySetting() throws IOException {
        sendResponse(dashboardFuncService.getWidgetDigitalDisplaySetting());
    }

    @PostMapping("widgets/" + DashboardConstants.WIDGET_DIGITAL_DISPLAY + "/export/{type}")
    @ApiOperation(value = "创建Dashboard里 数字卡片(W20) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void createExportTerminalsDownloadTask(@ApiParam(value = "导出数字卡片信息") @RequestBody(required = false) ExportDigitalDisplayRequest exportDigitalDisplayRequest,
                                                  @RequestParam(required = false) String tz, @PathVariable("type") String type) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportTerminalsDownloadTask(exportDigitalDisplayRequest, tz, type, parsePage()));
    }

    @GetMapping("widgets/" + DashboardConstants.WIDGET_PUK_SUMMERY)
    @ApiOperation(value = "获取Dashboard里 PUK数量(W22) 显示数据", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = BaseResponse.class)})
    public void getPukTerminalWidget(@RequestParam(required = false) Boolean forceRefresh) throws IOException {
        sendResponse(dashboardFuncService.getPukTerminalWidgetW22(forceRefresh));
    }

    @PostMapping("widgets/" + DashboardConstants.WIDGET_PUK_SUMMERY + "/export")
    @ApiOperation(value = "创建Dashboard里 PUK数量(W22) 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void exportPUKTerminalWidget(@RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(dashboardFuncService.exportPUKTerminalWidgetW22(tz));
    }

    /**
     * List markers by bound(In Area).
     *
     * @throws IOException the io exception
     */
    @GetMapping("map-markers")
    @ApiOperation(value = "获取地图当前边界内的标记", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchMarkers(@RequestParam(required = false) Integer maxLimit) throws IOException {
        sendResponse(terminalLocationFuncService.findMarkersPage(maxLimit, null, null, null));
    }


    /**
     * List map terminals.
     *
     * @param mapTerminalsRequest the map terminals request
     * @throws IOException the io exception
     */
    @PostMapping("map-terminals")
    @ApiOperation(value = "获取某地点内的终端", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchTerminalsByPlace(@RequestBody MapTerminalsRequest mapTerminalsRequest) throws IOException {
        sendResponse(terminalLocationFuncService.findTerminalLocationPage(parsePage(),mapTerminalsRequest));
    }

    @GetMapping("apps-pending")
    @ApiOperation(value = "查询待审核应用列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchPendingApps() throws IOException {
        sendResponse(dashboardFuncService.searchPendingApps(parsePage()));
    }

    @GetMapping("apps-top10")
    @ApiOperation(value = "查询应用列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchAppsTop10() throws IOException {
        sendResponse(dashboardFuncService.searchAppsTop10(parsePage()));
    }


}
