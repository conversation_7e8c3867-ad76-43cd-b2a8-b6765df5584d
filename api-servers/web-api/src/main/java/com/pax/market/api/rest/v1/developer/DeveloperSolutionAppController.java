/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1.developer;


import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.dto.BaseResponse;
import com.pax.market.functional.developer.solution.DeveloperSolutionAppFunc;
import com.pax.market.vo.developer.app.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 开发者Solution App相关接口
 *
 * <AUTHOR>
 */
@RestController("DeveloperSolutionAppController")
@RequestMapping("v1/developer")
@Api(value = "【Developer Solution Application API】")
@RequiredArgsConstructor
public class DeveloperSolutionAppController extends BaseController {


    private final DeveloperSolutionAppFunc developerSolutionAppFunc;

    /**
     * apply industry solution
     * send email && notify to global admin
     *
     * @throws IOException the io exception
     */
    @PostMapping("industry-solution/apply")
    @ApiOperation(value = "申请IndustrySolution", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.APPLY, type = AuditTypes.INDUSTRY_SOLUTION)
    public void applyIndustrySolution() throws IOException {
        developerSolutionAppFunc.applyIndustrySolution();
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * apply industry solution
     * send email && notify to global admin
     *
     * @throws IOException the io exception
     */
    @GetMapping("apps/{appId}/industry-solution/usage")
    @ApiOperation(value = "查询Solution用量", response = SolutionUsageSummaryPageVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = SolutionUsageSummaryPageVo.class)})
    public void findSolutionAppUsage(@PathVariable Long appId) throws IOException {
        sendResponse(developerSolutionAppFunc.findSolutionAppUsage(appId));
    }


    @GetMapping("apps/{appId}/industry-solution/usage/period")
    @ApiOperation(value = "查询Solution用量", response = SolutionUsageSummaryPageVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = SolutionUsageSummaryPageVo.class)})
    public void findSolutionAppUsagePeriod(@PathVariable Long appId, @RequestParam String period) throws IOException {
        sendResponse(developerSolutionAppFunc.findSolutionAppDetail(appId, period, parsePage()));
    }


}
