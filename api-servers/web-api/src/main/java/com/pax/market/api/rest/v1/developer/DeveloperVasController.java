package com.pax.market.api.rest.v1.developer;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.vas.UsageInfo;
import com.pax.market.functional.developer.DeveloperVasFunc;
import com.pax.market.functional.vas.VasAgreementFunctionService;
import com.pax.market.functional.vas.VasUsageSummaryFunctionService;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.vas.common.VasConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/9/6 13:55
 */
@RestController("DeveloperVasController")
@RequestMapping("v1/developer/vas")
@Api(value = "【App Vas API】")
@AllArgsConstructor
public class DeveloperVasController extends BaseController {

    private final ServiceCenterFunc serviceCenterFunc;
    private final VasUsageSummaryFunctionService vasUsageSummaryFunctionService;
    private final VasAgreementFunctionService vasAgreementFunctionService;

    private final DeveloperVasFunc developerVasFunc;

    @GetMapping
    @ApiOperation(value = "获取当前开发者服务相关信息", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getValueAddServiceSummaryVo() throws IOException {
        sendResponse(new PageInfo<>(developerVasFunc.findDeveloperVasVos()));
    }

    /**
     * Developer services apply and send email.
     *
     * @param serviceType
     * @throws IOException
     */
    @PostMapping("{serviceType}/apply")
    @ApiOperation(value = "开发者服务申请")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功")})
    public void serviceApply(@PathVariable String serviceType, @RequestParam(required = false) Long agreementId) throws IOException {
        serviceCenterFunc.applyMarketDevService(serviceType, agreementId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("statistics/types")
    @ApiOperation(value = "开发者能查看的用量列表")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findStatisticsTypes() throws IOException {
        sendResponse(new PageInfo<>(developerVasFunc.findEnabledServiceTypeByDevId(getCurrentUser().getCurrentDeveloperId())));
    }


    @GetMapping("{serviceType}/usage/dashboard")
    @ApiOperation(value = "开发者服务当月用量dashboard")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = UsageInfo.class)})
    public void getUsageDashboardByServiceType(@PathVariable String serviceType) throws IOException {
        sendResponse(vasUsageSummaryFunctionService.getDashboardUsageInDeveloper(serviceType));
    }

    @GetMapping("{serviceType}/history/usage")
    @ApiOperation(value = "开发者服务历史用量")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findHistoryUsageByServiceType(@PathVariable String serviceType) throws IOException {
        sendResponse(vasUsageSummaryFunctionService.findHistoryUsageInDeveloper(serviceType, getCurrentUser().getCurrentDeveloperId()));
    }

    /**
     * Agree vas agreement
     *
     * @param serviceType the service type
     * @param agreementId the agreement id
     * @throws IOException the io exception
     */
    @PostMapping(value = "{serviceType}/agreement/{agreementId}")
    @ApiOperation(value = "开发者同意服务协议")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.AGREEMENT, primaryAction = AuditActions.AGREE, dataType = AuditDataTypes.AGREE_VAS_AGREEMENT)
    public void agreeVasAgreement(@PathVariable String serviceType, @PathVariable Long agreementId, @RequestParam String type) throws IOException {
        vasAgreementFunctionService.agreeServiceAgreement(agreementId, serviceType, type);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Get vas agreement
     *
     * @param serviceType the service type
     * @param agreed      the agreed
     * @throws IOException the io exception
     */
    @GetMapping(value = "{serviceType}/agreement")
    @ApiOperation(value = "开发者获取服务协议")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void getVasAgreement(@PathVariable String serviceType, @RequestParam Boolean agreed, @RequestParam String type) throws IOException {
        sendResponse(vasAgreementFunctionService.getServiceAgreement(serviceType, agreed, type));
    }

    /**
     * Find vas agreed agreements
     *
     * @param agreed
     * @throws IOException
     */
    @GetMapping(value = "agreements")
    @ApiOperation(value = "开发者获取服务协议列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = BaseResponse.class)})
    public void findVasAgreedAgreements(@RequestParam Boolean agreed) throws IOException {
        sendResponse(vasAgreementFunctionService.findServiceAgreements(agreed));
    }


    @GetMapping("apps")
    @ApiOperation(value = "服务相关获取应用列表信息", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findDeveloperAppForVas(@RequestParam(required = false) String serviceType,
                                       @RequestParam(required = false) Boolean isIntegrated) throws IOException {

        if (VasConstants.ServiceType.CLOUD_MSG.equals(serviceType)) {
            sendResponse(developerVasFunc.findDeveloperAppForCloudMsg(parsePage(-2), isIntegrated));
        } else if (VasConstants.ServiceType.STACKLYTICS.equals(serviceType)) {
            sendResponse(developerVasFunc.findDeveloperAppForStackly(parsePage(-2), isIntegrated));
        } else sendResponse(new PageInfo<>());
    }


    @DeleteMapping("clear-data")
    @ApiOperation(value = "清除服务相关数据")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "清除成功", response = PageInfo.class)})
    public void clearAppCloudMessagesData(@RequestParam String serviceType) throws IOException {
        if (VasConstants.ServiceType.CLOUD_MSG.equals(serviceType)) {
            developerVasFunc.clearAppCloudMessagesData(serviceType);
        } else if (VasConstants.ServiceType.STACKLYTICS.equals(serviceType)) {
            developerVasFunc.clearAppStacklyData(serviceType);
        }
    }


    @GetMapping(value = "show/connect/dialog")
    @ApiOperation(value = "是否显示跳转提示框", response = Boolean.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = Boolean.class)})
    public void showConnectDialog() throws IOException {
        sendResponse(developerVasFunc.showCyberLabConnectDialog(getCurrentUserId()));
    }

    @PostMapping(value = "confirm/connect/dialog")
    @ApiOperation(value = "确认跳转提示", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void confirmConnectDialog() throws IOException {
        developerVasFunc.confirmCyberLabNotify(getCurrentUserId());
        sendResponse(ApiUtils.getSuccessJson());
    }


}
