package com.pax.market.api.rest.v1.admin.platform.congifuration;

import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.dto.auth.CreateUpdateOauthClientRequest;
import com.pax.market.functional.admin.platform.configuration.OauthClientFunc;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * @Author: <PERSON>
 * @Date: 2023/1/10 13:45
 */
@RequiredArgsConstructor
@RestController("OauthClientControllerV1")
@RequestMapping("v1/admin/platform/configuration/oauth-client")
@Api(value = "【ServiceProvider-OauthClient】", description = "ServiceProvider相关的RestAPI")
public class OauthClientController extends BaseController {

    private final OauthClientFunc oauthClientFunc;

    @GetMapping
    public void listAll() throws IOException {
        sendResponse(oauthClientFunc.findAll(false, parsePage(-1)));
    }

    @GetMapping("{id}")
    public void get(@PathVariable Long id) throws IOException {
        sendResponse(oauthClientFunc.getById(id));
    }

    @PostMapping
    public void add(@RequestBody CreateUpdateOauthClientRequest createRequest) throws IOException {
        sendResponse(oauthClientFunc.createOauthClient(createRequest));
    }

    @DeleteMapping("{id}")
    public void delete(@PathVariable Long id) throws IOException {
        oauthClientFunc.deleteById(id);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @PutMapping("{id}")
    public void update(
            @PathVariable Long id,
            @RequestBody CreateUpdateOauthClientRequest updateRequest) throws IOException {
        sendResponse(oauthClientFunc.updateOauthClient(id, updateRequest));
    }
}
