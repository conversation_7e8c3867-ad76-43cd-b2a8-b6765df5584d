package com.pax.market.api.rest.v1.vas;

import com.pax.core.exception.BusinessException;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.airlink.AirLinkTerminalStatus;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.admin.task.activity.ImportExportActivityInfo;
import com.pax.market.dto.request.vas.AirLinkDeletedTerminalRequest;
import com.pax.market.dto.request.vas.AirLinkTerminalBatchRequest;
import com.pax.market.dto.request.vas.AirLinkTerminalRequest;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.dto.airlink.AirLinkStatisticsInfo;
import com.pax.market.dto.airlink.AirLinkTerminalDetailInfo;
import com.pax.market.dto.airlink.AirLinkTerminalProfileInfo;
import com.pax.market.dto.airlink.AirLinkDashboardStatisticsInfo;
import com.pax.market.dto.airlink.AirLinkTerminalInfo;
import com.pax.market.functional.admin.management.airlink.AdminAirLinkTerminalFuncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12 11:17
 */
@RestController
@RequestMapping("v1/marketAdmin/vas/air-link/terminals")
@RequiredArgsConstructor
@Api(value = "【Service AirLink Terminals API】")
public class AdminAirLinkTerminalController extends BaseController {
    private final AdminAirLinkTerminalFuncService airLinkTerminalFuncService;


    @GetMapping("{id}/detail")
    @ApiOperation(value = "获取airLink终端详情", response = AirLinkTerminalDetailInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getAirLinkTerminalDetail(@PathVariable long id) throws IOException {
        sendResponse(airLinkTerminalFuncService.getAirLinkTerminalDetail(id));
    }

    @GetMapping("{id}/consumption-statistics")
    @ApiOperation(value = "获取airLink终端流量统计", response = AirLinkStatisticsInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getConsumptionStatistics(@PathVariable long id, @RequestParam String timeRangeType) throws IOException {
        sendResponse(airLinkTerminalFuncService.getConsumptionStatistics(id,timeRangeType));
    }

    @GetMapping("{id}/profiles")
    @ApiOperation(value = "获取airLink终端码号列表", response = AirLinkTerminalProfileInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getAirLinkTerminalProfileList(@PathVariable long id) throws IOException {
        sendResponse(airLinkTerminalFuncService.getAirLinkTerminalProfileList(id));
    }

    @PostMapping("{id}/switch-profile")
    @ApiOperation(value = "切换airLink终端码号", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.AIRLINK_SWITCH_ICCID, type = AuditTypes.AIR_LINK_TERMINAL, dataType = AuditDataTypes.AIR_LINK_SWITCH_PROFILE_AUDIT)
    public void switchAirLinkTerminalProfile(@PathVariable long id,@RequestParam String iccid) throws IOException {
        airLinkTerminalFuncService.switchAirLinkTerminalProfile(id,iccid);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("deleted")
    @ApiOperation(value = "获取airLink已删除终端列表", response = AirLinkTerminalDetailInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getDeletedAirLinkTerminalList(@RequestParam(required = false) String imei,
                                              @RequestParam(required = false) String iccid,
                                              @RequestParam(required = false) String serialNo,
                                              @RequestParam(required = false) String operator,
                                              @RequestParam(required = false) Long createdStartTime,
                                              @RequestParam(required = false) Long createdEndTime,
                                              @RequestParam(required = false) Long deletedStartTime,
                                              @RequestParam(required = false) Long deletedEndTime) throws IOException {
        AirLinkDeletedTerminalRequest request = AirLinkDeletedTerminalRequest.builder()
                .resellerId(getCurrentResellerId())
                .imei(StringUtils.trim(imei))
                .iccid(StringUtils.trim(iccid))
                .operator(StringUtils.trim(operator))
                .serialNo(StringUtils.trim(serialNo))
                .createdStartTime(DateUtils.parseTimeMills(createdStartTime))
                .createdEndTime(DateUtils.parseTimeMills(createdEndTime))
                .deletedStartTime(DateUtils.parseTimeMills(deletedStartTime))
                .deletedEndTime(DateUtils.parseTimeMills(deletedEndTime))
                .build();
        sendResponse(airLinkTerminalFuncService.getDeletedAirLinkTerminalList(request));
    }

    @GetMapping("operators")
    @ApiOperation(value = "获取airLink终端运营商", response = List.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = List.class)})
    public void getAirLinkTerminalOperatorList() throws IOException {
        sendResponse(airLinkTerminalFuncService.getAirLinkTerminalOperatorList());
    }

    @PostMapping("import/template/download")
    @ApiOperation(value = "创建airLink终端导入模板下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void createAirLinkTerminalImportTemplateDownloadTask(){
        String fileName = getLocaleMessageForExport("excel.airlink.terminals") +".xlsx";
        try  {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("terminalTemplate/"+ fileName);
            if (inputStream == null){
                logger.warn("Not found File({})", fileName);
                throw new BusinessException(ApiCodes.FILE_DOWNLOAD_FAILED);
            }
            Long downloadTaskId = airLinkTerminalFuncService.createAirLinkTerminalImportTemplateDownloadTask(inputStream);
            sendResponse(ApiUtils.getDownloadTaskResponse(downloadTaskId));
        } catch (Exception e) {
            logger.warn("Error create airlink terminal import template", e);
            throw new BusinessException(ApiCodes.FILE_DOWNLOAD_FAILED);
        }
    }

    @PostMapping("import")
    @ApiOperation(value = "导入airLink终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.AIR_LINK_TERMINAL, primaryAction = AuditActions.IMPORT, dataType = AuditDataTypes.AIR_LINK_TERMINAL_AUDIT)
    public void importAirLinkTerminal(MultipartHttpServletRequest request) throws IOException {
        MultipartFile file = request.getFile("file");
        String timezone = getParameterValue(request, "tz");
        sendImportActivityResponse(airLinkTerminalFuncService.importAirLinkTerminal(file, timezone));
    }

    @PostMapping("add")
    @ApiOperation(value = "添加airLink终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.AIR_LINK_TERMINAL, primaryAction = AuditActions.CREATE, dataType = AuditDataTypes.AIR_LINK_TERMINAL_AUDIT)
    public void addAirLinkTerminal(String imei) throws IOException {
        airLinkTerminalFuncService.addAirLinkTerminal(imei);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("/dashboard-statistics")
    @ApiOperation(value = "获取airLink仪表盘", response = AirLinkDashboardStatisticsInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AirLinkDashboardStatisticsInfo.class)})
    public void getDashboardStatistics() throws IOException {
        sendResponse(airLinkTerminalFuncService.getDashboardStatistics());
    }

    @GetMapping()
    @ApiOperation(value = "获取airLink终端列表", response = AirLinkTerminalInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getAirLinkTerminalList(
            @RequestParam(required = false) AirLinkTerminalStatus status,
            @RequestParam(required = false) String mobileCarrier,
            @RequestParam(required = false) String imei,
            @RequestParam(required = false) String iccid,
            @RequestParam(required = false) String serialNo,
            @RequestParam(required = false) Long fromDate,
            @RequestParam(required = false) Long toDate
    ) throws IOException {
        AirLinkTerminalRequest request = new AirLinkTerminalRequest();
        request.setStatus(status);
        request.setImei(StringUtils.trim(imei));
        request.setSerialNo(StringUtils.trim(serialNo));
        request.setIccid(StringUtils.trim(iccid));
        request.setOperator(StringUtils.trim(mobileCarrier));
        request.setFromDate(DateUtils.parseTimeMills(fromDate));
        request.setToDate(DateUtils.parseTimeMills(toDate));
        sendResponse(airLinkTerminalFuncService.getAirLinkTerminalList(parsePage(),request));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DELETE, type = AuditTypes.AIR_LINK_TERMINAL, dataType = AuditDataTypes.AIR_LINK_TERMINAL_AUDIT)
    public void delete(@RequestBody AirLinkTerminalBatchRequest request) throws IOException {
        airLinkTerminalFuncService.batchDeleteAirLinkTerminal(request);
        sendResponse(ApiUtils.getSuccessJson());
    }


    @PostMapping("/disable")
    @ApiOperation(value = "停用终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISABLE, type = AuditTypes.AIR_LINK_TERMINAL, dataType = AuditDataTypes.AIR_LINK_TERMINAL_AUDIT)
    public void disable(@RequestBody AirLinkTerminalBatchRequest request) throws IOException {
        airLinkTerminalFuncService.batchDisableAirLinkTerminal(request);
        sendResponse(ApiUtils.getSuccessJson());
    }


    @PostMapping("/resume")
    @ApiOperation(value = "恢复终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.REINSTATE, type = AuditTypes.AIR_LINK_TERMINAL, dataType = AuditDataTypes.AIR_LINK_TERMINAL_AUDIT)
    public void resume(@RequestBody AirLinkTerminalBatchRequest request) throws IOException {
        airLinkTerminalFuncService.batchResumeAirLinkTerminal(request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("/activate-histories")
    @ApiOperation(value = "获取airLink激活列表", response = AirLinkTerminalInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void getAirLinkTerminalActiveHistoryList() throws IOException {
        sendResponse(airLinkTerminalFuncService.getAirLinkTerminalActiveHistoryList(parsePage()));
    }

    @GetMapping(value = "/activate-histories/{id}/download")
    @ApiOperation(value = "下载激活任务详情", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = String.class)})
    public void exportTerminalActivateDetail(@PathVariable Long id, @RequestParam(required = false) String tz) throws IOException {
        ImportExportActivityInfo importExportActivityInfo = airLinkTerminalFuncService.exportTerminalActivateDetail(id,tz);
        sendExportActivityResponse(importExportActivityInfo);
    }
}
