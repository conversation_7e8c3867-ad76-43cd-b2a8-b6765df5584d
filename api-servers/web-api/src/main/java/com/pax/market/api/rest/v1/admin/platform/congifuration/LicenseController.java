/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.api.rest.v1.admin.platform.congifuration;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.request.admin.platform.configuration.LicenseUpdateRequest;
import com.pax.market.functional.admin.platform.configuration.LicenseFunctionService;
import com.pax.market.vo.admin.platform.configuration.LicenseVo;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.biz.annotation.Audit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * Apk参数相关RestAPI
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController("LicenseControllerV1")
@RequestMapping("v1/admin/platform/configuration/license")
@Api(value = "【许可证-License】", description = "许可证相关的RestAPI")
public class LicenseController extends BaseController {
    private final LicenseFunctionService licenseFunctionService;


    /**
     * Gets license.
     *
     * @throws IOException the io exception
     */
    @GetMapping
    @ApiOperation(value = "获取许可证信息", response = LicenseVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = LicenseVo.class)})
    public void getLicense() throws IOException {
        sendResponse(licenseFunctionService.getLicense(), ApiConstants.HTTP_STATUS_OK);
    }

    /**
     * Update license.
     *
     * @throws IOException the io exception
     */
    @PostMapping
    @ApiOperation(value = "更新许可证", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更新成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.UPDATE, type = AuditTypes.MARKET_SETTING, secondaryAction = AuditActions.LICENSE, dataType = AuditDataTypes.LICENSE)
    public void updateLicense(@RequestBody LicenseUpdateRequest licenseUpdateRequest) throws IOException {
        licenseFunctionService.updateLicense(licenseUpdateRequest);
        sendResponse(ApiUtils.getSuccessJson());

    }
}
