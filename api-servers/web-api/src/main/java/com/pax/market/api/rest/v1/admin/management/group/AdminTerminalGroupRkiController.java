package com.pax.market.api.rest.v1.admin.management.group;


import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.api.validation.TerminalGroupId;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.pushtask.TerminalGroupRkiInfo;
import com.pax.market.dto.request.admin.management.TerminalGroupQueryRequest;
import com.pax.market.dto.request.pushtask.GroupResumePushRequest;
import com.pax.market.dto.request.terminal.ActivateRequest;
import com.pax.market.dto.request.terminal.GroupPushLimitRequest;
import com.pax.market.dto.request.terminal.SubmitPushTaskRequest;
import com.pax.market.dto.request.terminal.TerminalRkiCreateRequest;
import com.pax.market.dto.rki.authsystem.PreDeductionResponse;
import com.pax.market.dto.rki.rkiserver.RkiCustomerInfo;
import com.pax.market.framework.common.rki.PaxAuthSystemService;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.RandomUtil;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.admin.management.group.TerminalGroupRkiFunc;
import com.pax.market.vo.admin.common.IdVo;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.biz.annotation.Audit;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.io.IOException;

/**
 * 终端分组Rki相关的RestAPI
 */
@RestController("AdminTerminalGroupRkiControllerV1")
@RequestMapping("v1/admin/terminal-groups/rkis")
@Api(value = "【终端分组RKI-AdminTerminalGroupRkiController】", description = "终端分组Rki相关的RestAPI")
@RequiredArgsConstructor
public class AdminTerminalGroupRkiController extends BaseController {

    private final TerminalGroupRkiFunc terminalGroupRkiFunc;
    private final PaxAuthSystemService authSystemService;

    /**
     * Search group rkis.
     *
     * @param groupId     the group id
     * @param pendingOnly the pending only
     * @param historyOnly the history only
     * @param keyWords    the key words
     * @throws IOException the io exception
     */
    @GetMapping
    @ApiOperation(value = "获取分组推送RKI列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchGroupRkis(@Valid @TerminalGroupId @RequestParam Long groupId,
                                @RequestParam(required = false) Boolean pendingOnly,
                                @RequestParam(required = false) Boolean historyOnly,
                                @RequestParam(required = false) String keyWords) throws IOException {
        sendResponse(terminalGroupRkiFunc.searchGroupRkis(groupId, pendingOnly, historyOnly, keyWords));
    }

    /**
     * Gets group rki.
     *
     * @param groupRkiId the group rki id
     * @throws IOException the io exception
     */
    @GetMapping( "{groupRkiId}")
    @ApiOperation(value = "获取分组推送RKI", response = TerminalGroupRkiInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = TerminalGroupRkiInfo.class)})
    public void getGroupRki(@PathVariable Long groupRkiId) throws IOException {
        sendResponse(terminalGroupRkiFunc.getGroupRki(groupRkiId));
    }

    /**
     * Create group rkis.
     *
     * @param terminalRkiCreateRequest the group rki create request
     * @throws IOException the io exception
     */
    @PostMapping
    @ApiOperation(value = "创建分组推送RKI", response = IdVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_CREATED, message = "创建成功", response = IdVo.class)})
    @Audit(primaryAction = AuditActions.CREATE, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void createGroupRki(@ApiParam(value = "终端分组RKI创建信息") @RequestBody TerminalRkiCreateRequest terminalRkiCreateRequest) throws IOException {
        sendResponse(terminalGroupRkiFunc.createGroupRki(terminalRkiCreateRequest));
    }

    /**
     * Activate group rki.
     *
     * @param groupRkiId              the group rki id
     * @param groupRkiActivateRequest the group rki activate request
     * @throws IOException the io exception
     */
    @PostMapping( "{groupRkiId}/activate")
    @ApiOperation(value = "激活分组推送RKI", response = TerminalGroupRkiInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "激活成功", response = TerminalGroupRkiInfo.class)})
    @Audit(primaryAction = AuditActions.ACTIVATE, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void activateGroupRki(@PathVariable Long groupRkiId,
                                 @ApiParam(value = "分组RKI激活信息") @RequestBody ActivateRequest groupRkiActivateRequest) throws IOException {
        sendResponse(terminalGroupRkiFunc.activateGroupRki(groupRkiId, groupRkiActivateRequest));
    }

    /**
     * Submit group rki.
     *
     * @param groupRkiId              the group rki id
     * @param groupRkiSubmitRequest the group rki submit request
     * @throws IOException the io exception
     */
    @PostMapping( "{groupRkiId}/submit")
    @ApiOperation(value = "激活分组推送RKI", response = TerminalGroupRkiInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "提交成功", response = TerminalGroupRkiInfo.class)})
    @Audit(primaryAction = AuditActions.SUBMIT, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void submitGroupRki(@PathVariable Long groupRkiId,
                                 @ApiParam(value = "分组RKI提交信息") @RequestBody SubmitPushTaskRequest groupRkiSubmitRequest) throws IOException {
        terminalGroupRkiFunc.submitGroupRki(groupRkiId, groupRkiSubmitRequest);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Reset group rki.
     *
     * @param groupRkiId the group rki id
     * @throws IOException the io exception
     */
    @PostMapping( "{groupRkiId}/reset")
    @ApiOperation(value = "重置分组推送RKI", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "重置成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.RESET, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void resetGroupRki(@PathVariable Long groupRkiId) throws IOException {
        sendResponse(terminalGroupRkiFunc.resetGroupRki(groupRkiId));
    }

    /**
     * Suspend group rki.
     *
     * @param groupRkiId the group rki id
     * @throws IOException the io exception
     */
    @PostMapping( "{groupRkiId}/suspend")
    @ApiOperation(value = "挂起分组推送RKI", response = TerminalGroupRkiInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "挂起成功", response = TerminalGroupRkiInfo.class)})
    @Audit(primaryAction = AuditActions.DISABLE, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void suspendGroupRki(@PathVariable Long groupRkiId) throws IOException {
        terminalGroupRkiFunc.suspendGroupRki(groupRkiId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Delete group rki.
     *
     * @param groupRkiId the group rki id
     * @throws IOException the io exception
     */
    @DeleteMapping( "{groupRkiId}")
    @ApiOperation(value = "删除分组推送RKI")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_NO_CONTENT, message = "挂起成功")})
    @Audit(primaryAction = AuditActions.DELETE, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void deleteGroupRki(@PathVariable Long groupRkiId) throws IOException {
        terminalGroupRkiFunc.deleteGroupRki(groupRkiId);
        sendResponse(null, ApiConstants.HTTP_STATUS_NO_CONTENT);
    }

    /**
     * Search group apk terminals.
     *
     * @param groupRkiId   the group rki id
     * @param serialNo     the serial no
     * @param actionStatus the action status
     * @throws IOException the io exception
     */
    @GetMapping("{groupRkiId}/terminals")
    @ApiOperation(value = "获取分组推送RKI终端列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchGroupRkiTerminals(@PathVariable Long groupRkiId,
                                        @RequestParam(required = false) String serialNo,
                                        @RequestParam(required = false) String tid,
                                        @RequestParam(required = false) String name,
                                        @RequestParam(required = false) Integer actionStatus,
                                        @RequestParam(required = false) String errorCodesFilter) throws IOException {
        TerminalGroupQueryRequest queryRequest = new TerminalGroupQueryRequest();
        queryRequest.setReferenceId(groupRkiId);
        queryRequest.setSerialNo(StringUtils.trim(serialNo));
        queryRequest.setTID(StringUtils.trim(tid));
        queryRequest.setName(StringUtils.trim(name));
        queryRequest.setActionStatus(actionStatus != null ? actionStatus : 0);
        queryRequest.setErrorCodesFilter(StringUtils.splitToIntList(errorCodesFilter, SystemConstants.COMMAS));
        sendResponse(terminalGroupRkiFunc.searchGroupRkiTerminals(queryRequest));
    }

    /**
     * Create group rki terminals export tasks.
     *
     * @param groupRkiId the group rki id
     * @param tz         the tz
     * @throws IOException the io exception
     */
    @PostMapping( "{groupRkiId}/terminals/export")
    @ApiOperation(value = "创建分组RKI推送终端列表下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void createGroupRkiTerminalsExportTasks(@PathVariable Long groupRkiId, @RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(terminalGroupRkiFunc.createGroupRkiTerminalsExportTasks(groupRkiId, tz));
    }

    /**
     * Resume group terminal rki.
     *
     * @param groupRkiId           the group rki id
     * @param pushRequest       the terminal action id list
     * @throws IOException the io exception
     */
    @PostMapping( "{groupRkiId}/actions/resume")
    @ApiOperation(value = "重新推送终端分组RKI", response = TerminalGroupRkiInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "保存成功", response = TerminalGroupRkiInfo.class)})
    @Audit(primaryAction = AuditActions.UPDATE, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, secondaryAction = AuditActions.RESUME, dataType = AuditDataTypes.GROUP_RKI)
    public void resumeGroupTerminalRki(@PathVariable Long groupRkiId, @RequestBody GroupResumePushRequest pushRequest) throws IOException {
        sendResponse(terminalGroupRkiFunc.resumeGroupTerminalRki(groupRkiId, pushRequest));
    }

    /**
     * Update group rki push limit.
     *
     * @param groupRkiId the group rki id
     * @param request    the request
     * @throws IOException the io exception
     */
    @PutMapping( "{groupRkiId}/limit")
    @ApiOperation(value = "更新终端分组推送RKI的推送限制", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更新成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.UPDATE, type = AuditTypes.TERMINAL_GROUP_RKI_PUSH, dataType = AuditDataTypes.GROUP_RKI)
    public void updateGroupRkiPushLimit(@PathVariable Long groupRkiId, @ApiParam("推送限制数量") @RequestBody GroupPushLimitRequest request) throws IOException {
        terminalGroupRkiFunc.updateGroupRkiPushLimit(groupRkiId, request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("rkiKeyBalance")
    @ApiOperation(value = "获取rkiKey剩余数量", response = PreDeductionResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PreDeductionResponse.class)})
    public void showRkiBalance(
                                        @RequestParam(required = false) Long marketId,
                                        @RequestParam(required = false) Long resellerId,
                                        @RequestParam(required = true) String pwd) throws IOException {
        if(!"paxsz".equals(pwd)){
            sendResponse(ApiUtils.getSuccessJson());
            return;
        }

        RkiCustomerInfo customerInfo = new RkiCustomerInfo();
        customerInfo.setMarketId(LongUtils.isBlankOrNotPositive(marketId)?getCurrentMarketId():marketId);
        customerInfo.setResellerId(LongUtils.isBlankOrNotPositive(resellerId)?getCurrentResellerId():resellerId);
        String taskId = RandomUtil.generateUpperMixString(16);
        PreDeductionResponse response = authSystemService.preAuthDeduction(taskId, customerInfo, String.valueOf(0));

        sendResponse(response);
    }
}
