/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2023 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */


package com.pax.market.api.rest.v1.developer;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.dto.BaseResponse;
import com.pax.market.functional.developer.solution.DeveloperSolutionAppFunc;
import com.pax.market.service.center.ServiceCenterFunc;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * solution app 沙箱相关api
 *
 * <AUTHOR>
 * @create 2023/7/24
 */
@RestController("DeveloperSandboxSolutionController")
@RequestMapping("v1/developer/sandbox/industry-solution")
@Api(value = "【开发者沙箱终端solution-DeveloperSandboxSolution】")
@AllArgsConstructor
public class DeveloperSandboxSolutionController extends BaseController {

    private final DeveloperSolutionAppFunc developerSolutionAppFunc;

    @GetMapping("{appId}/subscribe")
    @ApiOperation(value = "获取应用沙箱solution订阅状态", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void isSolutionSandboxSubscribe(@PathVariable Long appId) throws IOException {
        sendResponse(developerSolutionAppFunc.isSolutionSandboxSubscribe(appId));
    }


    @PutMapping("{appId}/subscribe")
    @ApiOperation(value = "更新应用沙箱solution订阅状态", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更新成功", response = BaseResponse.class)})
    public void updateSolutionSandboxSubscribe(@PathVariable Long appId, @RequestParam Boolean subscribe) throws IOException {
        developerSolutionAppFunc.updateSolutionSandboxSubscribe(appId, BooleanUtils.toBoolean(subscribe));
        sendResponse(ApiUtils.getSuccessJson());
    }

}
