/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1.admin.monitoring;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.constants.AlarmType;
import com.pax.market.constants.ApiCodes;
import com.pax.core.exception.BusinessException;
import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.request.admin.monitoring.alarm.AlarmSettingRequest;
import com.pax.market.dto.request.admin.monitoring.alarm.LockTerminalRequest;
import com.pax.market.framework.common.exception.http.ForbiddenException;
import com.pax.market.functional.admin.system.role.AdminRoleFunc;
import com.pax.market.vo.admin.monitoring.alarm.AlarmSettingVo;
import com.pax.market.dto.request.activity.BaseExportRequest;
import com.pax.market.dto.request.admin.monitoring.alarm.AlarmTerminalQuery;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.admin.monitoring.alarm.AlarmFuncService;
import com.pax.market.vo.admin.monitoring.alarm.AlarmDigitalVo;
import com.pax.market.vo.admin.monitoring.alarm.AlarmTypeVo;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.biz.annotation.Audit;
import io.swagger.annotations.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警Api
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@RestController("AdminAlarmController")
@RequestMapping("v1/admin/alarm")
@Api(value = "【Admin Alarm API】")
public class AdminAlarmController extends BaseController {

    @Autowired
    private AlarmFuncService alarmFuncService;
    @Autowired
    private AdminRoleFunc adminRoleFunc;


    @GetMapping("widgets/digital")
    @ApiOperation(value = "查看告警卡片", response = AlarmDigitalVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AlarmDigitalVo.class)})
    public void getAlarmWidgets() throws IOException {
        sendResponse(alarmFuncService.getAlarmDigitalVo());
    }


    @PostMapping("widgets/{type}/download-tasks")
    @ApiOperation(value = "创建告警中的数字卡片 数据下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void createExportAlarmDownloadTasks(@ApiParam(value = "导出数字卡片信息") @RequestBody BaseExportRequest exportRequest,
                                               @RequestParam(required = false) String tz, @PathVariable("type") String type) throws IOException {
        checkAlarmTypePermissions(type);
        sendExportActivityResponse(alarmFuncService.createExportAlarmDownloadTask(exportRequest, type, tz));
    }


    @GetMapping("types")
    @ApiOperation(value = "查询当前可见告警类型列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = PageInfo.class)})
    public void findAlarmTypeList() throws IOException {
        sendResponse(alarmFuncService.findAlarmTypes());
    }

    @GetMapping
    @ApiOperation(value = "查询告警列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchAlarm(@RequestParam String type,
                            @RequestParam(required = false) String serialNo,
                            @RequestParam(required = false) String tid,
                            @RequestParam(required = false) String name,
                            @RequestParam(required = false) String terminalResellerIds,
                            @RequestParam(required = false) String terminalMerchantIds,
                            @RequestParam(required = false) String modelIds,
                            @RequestParam(required = false) String lockedStatus) throws IOException {

        if (isNotAllowAlarmTypeAccess(type)){
            sendResponse(ApiUtils.getSuccessJson());
        }else if (AlarmType.TERMINAL_ALARM_TYPES.contains(type)) {
            AlarmTerminalQuery query = new AlarmTerminalQuery();
            query.setType(type);
            query.setSerialNo(StringUtils.trim(serialNo));
            query.setTid(StringUtils.trim(tid));
            query.setName(StringUtils.trim(name));
            query.setTerminalResellerIds(StringUtils.trim(terminalResellerIds));
            query.setTerminalMerchantIds(StringUtils.trim(terminalMerchantIds));
            query.setModelIds(StringUtils.trim(modelIds));
            query.setLockedStatus(lockedStatus);
            sendResponse(alarmFuncService.findAlarmTerminalPage(parsePage(), query));
        } else if (AlarmType.APP_SIGNATURE.equals(type)) {
            sendResponse(alarmFuncService.findAlarmApkPage(parsePage(), name));
        }
    }


    @GetMapping("setting")
    @ApiOperation(value = "查看告警配置", response = AlarmSettingVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AlarmSettingVo.class)})
    public void getAlarmSetting(@RequestParam String type) throws IOException {
        if (isNotAllowAlarmTypeAccess(type) ) {
            sendResponse(ApiUtils.getSuccessJson());
        } else {
            sendResponse(alarmFuncService.getAlarmSetting(type));
        }
    }

    /**
     * SAVE ALARM.
     *
     * @param request the alarm setting request
     * @throws IOException the io exception
     */
    @PostMapping("setting")
    @ApiOperation(value = "保存告警配置信息", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "保存成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.ALARM, primaryAction = AuditActions.UPDATE, dataType = AuditDataTypes.ALARM)
    public void saveAlarmSetting(@ApiParam(value = "告警配置") @RequestBody AlarmSettingRequest request) throws IOException {
        checkAlarmTypePermissions(request != null ? request.getType() : null);
        alarmFuncService.saveAlarmSetting(request);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Search roles for select user.
     *
     * @param name the name
     * @throws IOException the io exception
     */
    @GetMapping("setting/roles")
    @ApiOperation(value = "查询可见角色列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchRoles(@RequestParam(required = false) String name) throws IOException {
        sendResponse(adminRoleFunc.findRolePageForAlarm(parsePage(-1), name));
    }

    /**
     * Search role users.
     *
     * @param roleId   the role id
     * @param userName the user name
     * @throws IOException the io exception
     */
    @GetMapping("setting/roles/{roleId}/users")
    @ApiOperation(value = "获取角色用户列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchRoleUsers(@PathVariable Long roleId, @RequestParam(required = false) String userName) throws IOException {
        sendResponse(adminRoleFunc.findRoleUserPageForAlarm(parsePage(),roleId,userName));
    }

    private boolean isNotAllowAlarmTypeAccess(String type){
        PageInfo<AlarmTypeVo> alarmTypePage = alarmFuncService.findAlarmTypes();
        List<String> types = alarmTypePage.getList().stream().map(AlarmTypeVo::getType).collect(Collectors.toList());
        return CollectionUtils.isEmpty(types) || !types.contains(type);
    }

    /**
     * Lock Terminal.
     *
     * @param request the alarm setting request
     * @throws IOException the io exception
     */
    @PostMapping("terminals/lock")
    @ApiOperation(value = "锁定终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "保存成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.ALARM, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.ALARM_LOCK_TERMINAL, dataType = AuditDataTypes.ALARM_LOCK_TERMINAL)
    public void lockTerminals(@RequestBody LockTerminalRequest request) throws IOException {
        request.setValue(SystemConstants.OPT_VALUE_LOCK_TM);
        alarmFuncService.updateTerminalLockStatus(request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * UnLock Terminal.
     *
     * @param request the alarm setting request
     * @throws IOException the io exception
     */
    @PostMapping("terminals/unlock")
    @ApiOperation(value = "解锁终端", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "保存成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.ALARM, primaryAction = AuditActions.UPDATE, secondaryAction = AuditActions.ALARM_UNLOCK_TERMINAL, dataType = AuditDataTypes.ALARM_LOCK_TERMINAL)
    public void unLockTerminals(@RequestBody LockTerminalRequest request) throws IOException {
        request.setValue(SystemConstants.OPT_VALUE_UNLOCK_TM);
        alarmFuncService.updateTerminalLockStatus(request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    private void checkAlarmTypePermissions(String type){
         if (!AlarmType.ALARM_TYPES.contains(type)) {
            throw new BusinessException(ApiCodes.ALARM_TYPE_INVALID);
          }
          if(isNotAllowAlarmTypeAccess(type)){
              throw new ForbiddenException(ApiCodes.INSUFFICIENT_ACCESS_RIGHT);
          }
    }

}