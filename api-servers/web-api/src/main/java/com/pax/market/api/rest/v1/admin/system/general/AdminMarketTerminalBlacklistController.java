package com.pax.market.api.rest.v1.admin.system.general;

import com.pax.core.exception.BusinessException;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.constants.ApiCodes;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.functional.admin.system.general.AdminMarketTerminalBlacklistFunc;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2024/1/23
 */
@RestController("AdminTerminalBlacklistControllerV1")
@RequestMapping("v1/admin/market/terminal-blacklist")
@Api(value = "【终端黑名单】")
@RequiredArgsConstructor
public class AdminMarketTerminalBlacklistController extends BaseController {

    private final AdminMarketTerminalBlacklistFunc adminMarketTerminalBlacklistFunc;


    @GetMapping("")
    @ApiOperation(value = "查询终端黑名单", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "查询成功", response = PageInfo.class)})
    public void getTerminalBlacklist(@RequestParam(required = false) String serialNo) throws IOException {
        sendResponse(adminMarketTerminalBlacklistFunc.findTerminalBlacklist(serialNo));
    }


    @PostMapping("import")
    @ApiOperation(value = "导入终端黑名单", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "导入成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.TERMINAL_BLACKLIST, primaryAction = AuditActions.IMPORT, secondaryAction = AuditActions.TERMINAL_BLACKLIST, dataType = AuditDataTypes.TERMINAL)
    public void importTerminalBlacklist(MultipartHttpServletRequest request) throws IOException {
        MultipartFile file = request.getFile("file");
        sendImportActivityResponse(adminMarketTerminalBlacklistFunc.importTerminalBlacklist(file));
    }

    @DeleteMapping("{id}")
    @ApiOperation(value = "删除终端黑名单", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "删除成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.TERMINAL_BLACKLIST, primaryAction = AuditActions.DELETE, secondaryAction = AuditActions.TERMINAL_BLACKLIST, dataType = AuditDataTypes.TERMINAL)
    public void deleteTerminalBlacklist(@PathVariable Long id) throws IOException {
        adminMarketTerminalBlacklistFunc.deleteTerminalBlacklist(id);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @PostMapping("import/template/download-tasks")
    @ApiOperation(value = "下载终端黑名单导入模板", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void createTerminalImportTemplateDownloadTask() throws IOException {
        String fileName = getLocaleMessageForExport("excel.terminal.blacklist") + ".xlsx";
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("terminalTemplate/" + fileName);
            if (inputStream == null) {
                logger.warn("Not found File({})", fileName);
                throw new BusinessException(ApiCodes.FILE_DOWNLOAD_FAILED);
            }
            Long downloadTaskId = adminMarketTerminalBlacklistFunc.createTemplateDownloadTask(inputStream);
            sendResponse(ApiUtils.getDownloadTaskResponse(downloadTaskId));
        } catch (Exception e) {
            logger.warn("Error create terminal blacklist import template", e);
            throw new BusinessException(ApiCodes.FILE_DOWNLOAD_FAILED);
        }
    }
}
