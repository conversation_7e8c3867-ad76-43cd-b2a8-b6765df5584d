package com.pax.market.api.rest.v1.common;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.dto.BaseResponse;
import com.pax.market.functional.common.CommonFuncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;

@RestController("CommonParamDataUploadController")
@RequestMapping("v1/common/param/data")
@Api(value = "【Common Parameter Data Upload Rest API】")
@AllArgsConstructor
public class CommonParamDataUploadController extends BaseController {

    private final CommonFuncService commonFuncService;


    @PostMapping("upload")
    @ApiOperation(value = "上传参数模板里面的参数文件", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "上传成功", response = BaseResponse.class)})
    public void uploadTerminalGroupApkParamFile(MultipartHttpServletRequest request) throws IOException {
        sendResponse(ApiUtils.getSuccessJson(commonFuncService.uploadTerminalGroupApkParamDataFile(request.getFile("paramData"))));
    }
}
