package com.pax.market.api.rest.v1.admin.management.group;

import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.api.validation.TerminalGroupId;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.pushtask.TerminalGroupOperationInfo;
import com.pax.market.dto.request.admin.management.TerminalGroupQueryRequest;
import com.pax.market.dto.request.pushtask.GroupResumePushRequest;
import com.pax.market.dto.request.terminal.ActivateRequest;
import com.pax.market.dto.request.terminal.GroupOperationCreateRequest;
import com.pax.market.dto.request.terminal.GroupPushLimitRequest;
import com.pax.market.dto.request.terminal.SubmitPushTaskRequest;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.admin.management.group.TerminalGroupPukFunc;
import com.pax.market.vo.admin.management.group.puk.TerminalGroupFactoryPukVo;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.biz.annotation.Audit;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.io.IOException;

@RestController("AdminTerminalGroupPukControllerV1")
@RequestMapping("v1/admin/terminal-groups/puks")
@RequiredArgsConstructor
public class AdminTerminalGroupPukController extends BaseController {

    private final TerminalGroupPukFunc terminalGroupPukFunc;

    /**
     * Search group operation.
     *
     * @param groupId     the group id
     * @param pendingOnly the pending only
     * @param historyOnly the history only
     * @param key         the key
     * @param keyWords    the key words
     * @throws IOException the io exception
     */
    @GetMapping
    @ApiOperation(value = "获取分组推送Operation列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchGroupPuks(@Valid @TerminalGroupId @RequestParam Long groupId,
                                     @RequestParam(required = false) Boolean pendingOnly,
                                     @RequestParam(required = false) Boolean historyOnly,
                                     @RequestParam(required = false) String key,
                                     @RequestParam(required = false) String keyWords) throws IOException {
        sendResponse(terminalGroupPukFunc.searchGroupPuk(groupId, pendingOnly, historyOnly, key, keyWords));
    }

    /**
     * Gets group operation.
     *
     * @param groupOptId the group opt id
     * @throws IOException the io exception
     */
    @GetMapping( "{groupOptId}")
    @ApiOperation(value = "获取分组推送Operation", response = TerminalGroupOperationInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = TerminalGroupOperationInfo.class)})
    public void getGroupPuk(@PathVariable Long groupOptId) throws IOException {
        sendResponse(terminalGroupPukFunc.getGroupPuk(groupOptId));
    }

    /**
     * Create group operation.
     *
     * @param groupOperationCreateRequest the group operation create request
     * @throws IOException the io exception
     */
    @PostMapping
    @ApiOperation(value = "创建分组推送Operation", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_CREATED, message = "创建成功", response = TerminalGroupOperationInfo.class)})
    @Audit(primaryAction = AuditActions.CREATE, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void createGroupPuk(@ApiParam(value = "终端分组Operation创建信息") @RequestBody GroupOperationCreateRequest groupOperationCreateRequest) throws IOException {
        sendResponse(terminalGroupPukFunc.createGroupPuk(groupOperationCreateRequest), ApiConstants.HTTP_STATUS_CREATED);
    }

    /**
     * Activate group operation.
     *
     * @param groupOptId              the group opt id
     * @param groupOptActivateRequest the group opt activate request
     * @throws IOException the io exception
     */
    @PostMapping( "{groupOptId}/activate")
    @ApiOperation(value = "激活分组推送Operation", response = TerminalGroupOperationInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "激活成功", response = TerminalGroupOperationInfo.class)})
    @Audit(primaryAction = AuditActions.ACTIVATE, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void activateGroupPuk(@PathVariable Long groupOptId,
                                       @ApiParam(value = "分组Operation激活信息") @RequestBody ActivateRequest groupOptActivateRequest) throws IOException {
        terminalGroupPukFunc.activateGroupPuk(groupOptId, groupOptActivateRequest);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Submit group operation.
     *
     * @param groupOptId              the group opt id
     * @param groupOptSubmitRequest the group opt submit request
     * @throws IOException the io exception
     */
    @PostMapping( "{groupOptId}/submit")
    @ApiOperation(value = "提交分组推送Operation", response = TerminalGroupOperationInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "提交成功", response = TerminalGroupOperationInfo.class)})
    @Audit(primaryAction = AuditActions.SUBMIT, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void submitGroupPuk(@PathVariable Long groupOptId,
                                 @ApiParam(value = "分组Operation提交信息") @RequestBody SubmitPushTaskRequest groupOptSubmitRequest) throws IOException {
        terminalGroupPukFunc.submitGroupPuk(groupOptId, groupOptSubmitRequest);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Reset group operation.
     *
     * @param groupOptId the group opt id
     * @throws IOException the io exception
     */
    @PostMapping( "{groupOptId}/reset")
    @ApiOperation(value = "重置分组推送Operation", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "重置成功", response = TerminalGroupOperationInfo.class)})
    @Audit(primaryAction = AuditActions.RESET, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void resetGroupPuk(@PathVariable Long groupOptId) throws IOException {
        sendResponse(terminalGroupPukFunc.resetGroupPuk(groupOptId));
    }

    /**
     * Suspend group operation.
     *
     * @param groupOptId the group opt id
     * @throws IOException the io exception
     */
    @PostMapping( "{groupOptId}/suspend")
    @ApiOperation(value = "挂起分组推送Operation", response = TerminalGroupOperationInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "挂起成功", response = TerminalGroupOperationInfo.class)})
    @Audit(primaryAction = AuditActions.DISABLE, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void suspendGroupPuk(@PathVariable Long groupOptId) throws IOException {
        terminalGroupPukFunc.suspendGroupPuk(groupOptId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Delete group operation.
     *
     * @param groupOptId the group opt id
     * @throws IOException the io exception
     */
    @DeleteMapping( "{groupOptId}")
    @ApiOperation(value = "删除分组推送Operation")
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_NO_CONTENT, message = "挂起成功")})
    @Audit(primaryAction = AuditActions.DELETE, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void deleteGroupPuk(@PathVariable Long groupOptId) throws IOException {
        terminalGroupPukFunc.deleteGroupPuk(groupOptId);
        sendResponse(null, ApiConstants.HTTP_STATUS_NO_CONTENT);
    }

    /**
     * Search group operation terminals.
     *
     * @param groupOptId   the group opt id
     * @param serialNo     the serial no
     * @param actionStatus the action status
     * @throws IOException the io exception
     */
    @GetMapping("{groupOptId}/terminals")
    @ApiOperation(value = "获取分组推送Operation终端列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void searchGroupPukTerminals(@PathVariable Long groupOptId,
                                              @RequestParam(required = false) String serialNo,
                                              @RequestParam(required = false) String tid,
                                              @RequestParam(required = false) String name,
                                              @RequestParam(required = false) Integer actionStatus,
                                              @RequestParam(required = false) String errorCodesFilter) throws IOException {
        TerminalGroupQueryRequest queryRequest = new TerminalGroupQueryRequest();
        queryRequest.setReferenceId(groupOptId);
        queryRequest.setSerialNo(StringUtils.trim(serialNo));
        queryRequest.setTID(StringUtils.trim(tid));
        queryRequest.setName(StringUtils.trim(name));
        queryRequest.setActionStatus(actionStatus != null ? actionStatus : 0);
        queryRequest.setErrorCodesFilter(StringUtils.splitToIntList(errorCodesFilter, SystemConstants.COMMAS));
        sendResponse(terminalGroupPukFunc.searchGroupPukTerminals(queryRequest));
    }

    /**
     * Create group operation terminals export tasks.
     *
     * @param groupOptId the group opt id
     * @param tz         the tz
     * @throws IOException the io exception
     */
    @PostMapping( "{groupOptId}/terminals/export")
    @ApiOperation(value = "创建分组Operation推送终端列表下载任务", response = String.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = String.class)})
    public void createGroupPukTerminalsExportTasks(@PathVariable Long groupOptId, @RequestParam(required = false) String tz) throws IOException {
        sendExportActivityResponse(terminalGroupPukFunc.createGroupPukTerminalsExportTasks(groupOptId, tz));
    }

    /**
     * Resume group terminal operation.
     *
     * @param groupOptId           the group opt id
     * @param pushRequest          the groupResumePushRequest
     * @throws IOException         the io exception
     */
    @PostMapping( "{groupOptId}/actions/resume")
    @ApiOperation(value = "重新推送终端分组Operation", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "保存成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.UPDATE, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, secondaryAction = AuditActions.RESUME, dataType = AuditDataTypes.GROUP_PUK)
    public void resumeGroupTerminalPuk(@PathVariable Long groupOptId, @RequestBody GroupResumePushRequest pushRequest) throws IOException {
        terminalGroupPukFunc.resumeGroupTerminalPuk(groupOptId, pushRequest);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Update group operation push limit.
     *
     * @param groupOptId the group opt id
     * @param request    the request
     * @throws IOException the io exception
     */
    @PutMapping("{groupOptId}/limit")
    @ApiOperation(value = "更新终端分组Operation的推送限制", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更新成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.UPDATE, type = AuditTypes.TERMINAL_GROUP_PUK_PUSH, dataType = AuditDataTypes.GROUP_PUK)
    public void updateGroupPukPushLimit(@PathVariable Long groupOptId, @ApiParam("推送限制数量") @RequestBody GroupPushLimitRequest request) throws IOException {
        terminalGroupPukFunc.updateGroupPukPushLimit(groupOptId, request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Gets signature puk.
     *
     * @param groupId the group id
     * @throws IOException the io exception
     */
    @GetMapping( "{groupId}/signature")
    @ApiOperation(value = "根据groupId查询签名公钥", response = TerminalGroupFactoryPukVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = TerminalGroupFactoryPukVo.class)})
    public void getSignaturePuk(@PathVariable("groupId") Long groupId) throws IOException {
        sendResponse(terminalGroupPukFunc.findSignaturePukPage(groupId));
    }
}
