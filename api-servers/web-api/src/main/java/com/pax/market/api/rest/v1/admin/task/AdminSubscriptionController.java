/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.rest.v1.admin.task;

import com.google.common.collect.Sets;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.constants.AppChargeType;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.PageInfo;
import com.pax.market.dto.app.AppInfo;
import com.pax.market.dto.request.activity.AppSubscriptionExportRequest;
import com.pax.market.dto.request.activity.FirmwareSubscriptionExportRequest;
import com.pax.market.dto.request.admin.common.specific.SpecificMerchantCategoryRequest;
import com.pax.market.dto.request.admin.common.specific.SpecificResellerRequest;
import com.pax.market.dto.request.admin.task.subscription.app.AppQueryRequest;
import com.pax.market.dto.request.firmware.AdminSubscriptionFirmwareQueryRequest;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.admin.task.subscription.AdminSubscriptionFuncService;
import com.pax.market.functional.app.AppFunctionService;
import com.pax.market.notification.InternalNotificationFunc;
import com.pax.market.vo.admin.task.subscription.app.*;
import com.pax.market.vo.admin.task.subscription.firmware.FirmwareDetailVo;
import com.pax.market.vo.admin.task.subscription.firmware.FirmwarePageVo;
import com.zolon.saas.notification.func.dto.TopicInfo;
import com.zolon.saas.vas.func.goinsight.dto.DataQueryResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Set;

/**
 * 管理员中心订阅-API
 *
 * <AUTHOR>
 * @date 2022/6/24
 */
@RestController("AdminSubscriptionController")
@RequestMapping("v1/admin/subscription")
@Api(value = "【Admin Subscribe Application Or Firmware API】")
@RequiredArgsConstructor
public class AdminSubscriptionController extends BaseController {

    private final AdminSubscriptionFuncService subscriptionFuncService;
    private final InternalNotificationFunc notificationFunc;
    private final AppFunctionService appFunctionService;

    /**
     * Gets global publish app info.
     *
     * @param name             the name
     * @param osType           the os type
     * @param baseType         the base type
     * @param chargeType       the charge type
     * @param specificReseller the specific reseller
     * @param resellerIds      the reseller ids
     * @param isSubscribe      the is subscribe
     * @param supportCloudData the support cloud data
     * @param supportCloudMsg  the support cloud msg
     * @param factoryId        the factory id
     * @param modelId          the model id
     * @throws IOException the io exception
     */
    @GetMapping("apps")
    @ApiOperation(value = "获取Global应用市场应用列表", response = AppVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AppVo.class)})
    public void findGlobalPublishAppPage(@RequestParam(required = false) String name,
                                         @RequestParam(required = false) String osType,
                                         @RequestParam(required = false) String baseType,
                                         @RequestParam(required = false) Integer chargeType,
                                         @RequestParam(required = false) Boolean specificReseller,
                                         @RequestParam(required = false) Boolean specificMerchantCategory,
                                         @RequestParam(required = false) String resellerIds,
                                         @RequestParam(required = false) String merchantCategoryIds,
                                         @RequestParam(required = false) Boolean isSubscribe,
                                         @RequestParam(required = false) Boolean supportCloudData,
                                         @RequestParam(required = false) Boolean supportCloudMsg,
                                         @RequestParam(required = false) Boolean supportStackly,
                                         @RequestParam(required = false) String factoryId,
                                         @RequestParam(required = false) String modelId) throws IOException {
        AppQueryRequest appQueryRequest = new AppQueryRequest();
        appQueryRequest.setOsType(osType);
        appQueryRequest.setKeyWords(StringUtils.trim(name));
        appQueryRequest.setBaseType(baseType);
        appQueryRequest.setSubscribe(isSubscribe);
        appQueryRequest.setSpecificReseller(specificReseller);
        appQueryRequest.setSpecificMerchantCategory(specificMerchantCategory);
        appQueryRequest.setVasCloudDataSupported(supportCloudData);
        appQueryRequest.setVasCloudMsgSupported(supportCloudMsg);
        appQueryRequest.setVasStacklySupported(supportStackly);

        if (StringUtils.isNoneEmpty(factoryId)) {
            appQueryRequest.setFactoryId(Long.parseLong(factoryId));
        }
        if (StringUtils.isNoneEmpty(modelId)) {
            appQueryRequest.setModelId(Long.parseLong(modelId));
        }
        if (BooleanUtils.isFalse(getCurrentMarket().getAllowAppOfflinePurchase())) {
            appQueryRequest.setChargeType(AppChargeType.FREE);
        } else {
            appQueryRequest.setChargeType(chargeType);
        }

        if (BooleanUtils.isNotFalse(specificReseller) && StringUtils.isNotEmpty(resellerIds)) {
            Set<Long> queryResellerIds = Sets.newHashSet(LongUtils.split(resellerIds, ","));
            appQueryRequest.setResellerIds(queryResellerIds);
        }

        if (BooleanUtils.isNotFalse(specificMerchantCategory) && StringUtils.isNotEmpty(merchantCategoryIds)) {
            Set<Long> queryMerchantCategoryIds = Sets.newHashSet(LongUtils.split(merchantCategoryIds, ","));
            appQueryRequest.setMerchantCategoryIds(queryMerchantCategoryIds);
        }
        sendResponse(subscriptionFuncService.findGlobalPublishAppPage(parsePage(), appQueryRequest));
    }


    /**
     * Gets app detail vo.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping(value = "apps/{appId}")
    @ApiOperation(value = "获取global应用详细信息", response = AppDetailVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AppDetailVo.class)})
    public void getAppDetailVo(@PathVariable Long appId) throws IOException {
        sendResponse(subscriptionFuncService.getAppDetailVo(appId));
    }


    /**
     * Gets app setting vo.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping(value = "apps/{appId}/setting")
    @ApiOperation(value = "获取应用设置栏相关信息", response = AppSettingVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AppSettingVo.class)})
    public void getAppSettingVo(@PathVariable Long appId) throws IOException {
        sendResponse(subscriptionFuncService.getAppSettingVo(appId));
    }


    /**
     * Gets app vas setting vo.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping(value = "apps/{appId}/vas-setting")
    @ApiOperation(value = "获取应用vas相关权限信息", response = AppVasSettingVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = AppVasSettingVo.class)})
    public void getAppVasSettingVo(@PathVariable Long appId) throws IOException {
        sendResponse(appFunctionService.getAppVasSettingVo(appId));
    }

    @GetMapping("apps/{appId}/insight/sandbox/data")
    @ApiOperation(value = "Admin search insight sandbox app biz data", response = DataQueryResult.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = DataQueryResult.class)})
    public DataQueryResult getBizDataFromGoInsight(@PathVariable Long appId, @RequestParam(required = false) String tz) {
        return appFunctionService.getBizDataFromGoInsight(appId, tz);
    }

    /**
     * Gets app apk list.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping(value = "apps/{appId}/apks")
    @ApiOperation(value = "获取global应用所属版本列表", response = ApkPageVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = ApkPageVo.class)})
    public void findApkPage(@PathVariable Long appId) throws IOException {
        sendResponse(subscriptionFuncService.findApkPage(parsePage(), appId));
    }

    /**
     * Gets apk detail vo.
     *
     * @throws IOException the io exception
     */
    @GetMapping(value = "apps/apks/{apkId}")
    @ApiOperation(value = "获取global应用版本信息", response = ApkDetailVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = ApkDetailVo.class)})
    public void getApkDetailVo(@PathVariable Long apkId) throws IOException {
        sendResponse(subscriptionFuncService.getApkDetailVo(apkId));
    }


    /**
     * Subscription App.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @PostMapping("apps/{appId}")
    @ApiOperation(value = "全局应用订阅", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.SUBSCRIBE, type = AuditTypes.APP, dataType = AuditDataTypes.APP)
    public void subscriptionApp(@PathVariable Long appId) throws IOException {
        subscriptionFuncService.subscribeGlobalApp(getCurrentMarketId(), appId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * unSubscription App.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @PostMapping("apps/{appId}/cancel")
    @ApiOperation(value = "全局应用取消订阅", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.UNSUBSCRIBE, type = AuditTypes.APP, dataType = AuditDataTypes.APP)
    public void unSubscriptionApp(@PathVariable Long appId) throws IOException {
        subscriptionFuncService.unSubscribeGlobalApp(getCurrentMarketId(), appId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Gets global publish firmware info.
     *
     * @param fmStatus            the fm status
     * @param fmName              the fm name
     * @param subscribed          the subscribed
     * @param modelId             the model id
     * @param factoryId           the factory id
     * @param firmwareResellerIds the firmware reseller ids
     * @param specificReseller    the specific reseller
     * @param firmwareType        the firmware type
     * @throws IOException the io exception
     */
    @GetMapping("firmwares")
    @ApiOperation(value = "获取Global应用市场固件列表", response = FirmwarePageVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = FirmwarePageVo.class)})
    public void findGlobalPublishFirmware(@RequestParam(required = false) String fmStatus,
                                          @RequestParam(required = false) String fmName,
                                          @RequestParam(required = false) Boolean subscribed,
                                          @RequestParam(required = false) String modelId,
                                          @RequestParam(required = false) String factoryId,
                                          @RequestParam(required = false) String firmwareResellerIds,
                                          @RequestParam(required = false) Boolean specificReseller,
                                          @RequestParam(required = false) String firmwareType) throws IOException {
        AdminSubscriptionFirmwareQueryRequest queryRequest = new AdminSubscriptionFirmwareQueryRequest();
        if (StringUtils.isNotEmpty(modelId)) {
            queryRequest.setModelId(modelId);
            queryRequest.setModelIdsFilter(Sets.newHashSet(StringUtils.split(modelId, ",")));

        }
        if (StringUtils.isNoneEmpty(factoryId)) {
            queryRequest.setFactoryIdFilter(Long.parseLong(factoryId));
        }
        if (StringUtils.isNotEmpty(firmwareResellerIds)) {
            queryRequest.setResellerIdsFilter(Sets.newHashSet(StringUtils.splitToLongList(firmwareResellerIds, ",")));
        }
        queryRequest.setFmName(StringUtils.trim(fmName));
        queryRequest.setFmStatus(fmStatus);
        queryRequest.setSubscribed(subscribed);
        queryRequest.setSpecificReseller(specificReseller);
        queryRequest.setFirmwareType(firmwareType);
        sendResponse(subscriptionFuncService.findGlobalPublishFirmwarePage(parsePage(), queryRequest));
    }

    /**
     * Gets firmware vo.
     *
     * @param firmwareId the firmware id
     * @throws IOException the io exception
     */
    @GetMapping(value = "firmwares/{firmwareId}")
    @ApiOperation(value = "获取global固件详细信息", response = FirmwareDetailVo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = FirmwareDetailVo.class)})
    public void getFirmwareDetailVo(@PathVariable Long firmwareId) throws IOException {
        sendResponse(subscriptionFuncService.getFirmwareDetailVo(firmwareId));
    }


    /**
     * Subscribe firmware.
     *
     * @param firmwareId the firmware id
     * @throws IOException the io exception
     */
    @PostMapping("firmwares/{firmwareId}")
    @ApiOperation(value = "订阅Global应用市场的固件", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.SUBSCRIBE, type = AuditTypes.FIRMWARE, dataType = AuditDataTypes.FIRMWARE)
    public void subscribeFirmware(@PathVariable Long firmwareId) throws IOException {
        subscriptionFuncService.subscribeFirmware(getCurrentMarketId(), firmwareId);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Unsubscribe firmware.
     *
     * @param firmwareId the firmware id
     * @throws IOException the io exception
     */
    @DeleteMapping("firmwares/{firmwareId}/cancel")
    @ApiOperation(value = "取消订阅Global应用市场固件", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.UNSUBSCRIBE, type = AuditTypes.FIRMWARE, dataType = AuditDataTypes.FIRMWARE)
    public void unsubscribeFirmware(@PathVariable Long firmwareId) throws IOException {
        subscriptionFuncService.unSubscribeFirmware(getCurrentMarketId(), firmwareId);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * 获取机型-固件，应用是否关注
     * 应用只有订阅的前提才能关注
     *
     * @throws IOException
     */
    @GetMapping("topic/{topicCategory}/{topicExternalId}")
    @ApiOperation(value = "Get subscription info if subscribed (notification)", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "Return Success", response = BaseResponse.class)})
    public void getTopicSubscription(@Valid @PathVariable int topicCategory, @Valid @PathVariable Long topicExternalId) throws IOException {
        sendResponse(notificationFunc.getTopicSubscription(getCurrentUserId(), topicCategory, topicExternalId));
    }


    /**
     * 关注topic
     * action on page of a subject marked as a topic
     *
     * @param topicCategory
     * @param topicExternalId
     * @param topicInfo
     * @throws IOException
     */
    @PostMapping("topic/{topicCategory}/{topicExternalId}")
    @ApiOperation(value = "Subscribe a topic (notification)", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "Return Success", response = BaseResponse.class)})
    public void subscribeTopic(@Valid @PathVariable int topicCategory, @Valid @PathVariable Long topicExternalId,
                               @Valid @RequestBody TopicInfo topicInfo) throws IOException {
        topicInfo.setExternalId(topicExternalId);
        topicInfo.setCategory(topicCategory);
        notificationFunc.subscribeTopic(getCurrentUserId(), topicCategory, topicInfo);
        sendResponse(ApiUtils.getSuccessJson(), ApiConstants.HTTP_STATUS_OK);
    }

    /**
     * 取消关注topic
     *
     * @throws IOException
     */
    @DeleteMapping("topic/{topicCategory}/{topicExternalId}")
    @ApiOperation(value = "Unsubscribe a topic (notification)", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "Return Success", response = BaseResponse.class)})
    public void unsubscribeTopic(@Valid @PathVariable int topicCategory, @Valid @PathVariable Long topicExternalId) throws IOException {
        notificationFunc.unsubscribeTopic(getCurrentUserId(), topicExternalId, topicCategory);
        sendResponse(ApiUtils.getSuccessJson(), ApiConstants.HTTP_STATUS_OK);
    }




    /* ------------------------------------------------ 应用以及应用版本定向发布相关api  ----------------------------------------------------------------**/

    /**
     * Find app reseller specific page.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping("apps/{appId}/reseller/specific")
    @ApiOperation(value = "查询订阅的应用定向发布的代理商", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findSpecificAppResellerPage(@PathVariable Long appId) throws IOException {
        sendResponse(subscriptionFuncService.findSpecificAppResellerPage(parsePage(), appId));
    }


    /**
     * Find app reseller specific all list.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping("apps/{appId}/reseller/specific/all")
    @ApiOperation(value = "查询订阅的应用定向发布的代理商", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findSpecificAppResellerAllListPage(@PathVariable Long appId) throws IOException {
        sendResponse(subscriptionFuncService.findSpecificAppResellerAllList(appId));
    }


    /**
     * specific app to resellers.
     *
     * @throws IOException
     */
    @PostMapping("apps/{appId}/reseller/specific")
    @ApiOperation(value = "将订阅的应用定向发布给代理商", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.APP, dataType = AuditDataTypes.APP_APK_PUBLISH)
    public void specificAppReseller(@PathVariable Long appId,
                                    @RequestBody SpecificResellerRequest request) throws IOException {
        subscriptionFuncService.specificAppReseller(appId, request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Delete specific app.
     *
     * @throws IOException the io exception
     */
    @DeleteMapping("apps/{appId}/reseller/specific")
    @ApiOperation(value = "删除订阅的应用定向发布代理商", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_NO_CONTENT, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.APP, dataType = AuditDataTypes.APP_APK_PUBLISH)
    public void deleteSpecificAppReseller(@PathVariable Long appId,
                                          @RequestParam Long resellerId) throws IOException {
        subscriptionFuncService.deleteSpecificAppReseller(appId, resellerId);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Gets app merchant category.
     *
     * @param appId the app id
     * @throws IOException the io exception
     */
    @GetMapping("apps/{appId}/merchant/categories/specific")
    @ApiOperation(value = "获取订阅的应用定向发布的商户类型", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findAppMerchantCategoryPage(@PathVariable Long appId) throws IOException {
        sendResponse(subscriptionFuncService.findAppMerchantCategoryPage(parsePage(), appId));
    }

    /**
     * Specific app merchant category.
     *
     * @throws IOException the io exception
     */
    @PostMapping("apps/{appId}/merchant/categories/specific")
    @ApiOperation(value = "订阅的应用定向发布商户类型", response = AppInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.APP, secondaryAction = AuditActions.MERCHANT_TYPE, dataType = AuditDataTypes.APP_MERCHANT_TYPE)
    public void specificAppMerchantCategory(@PathVariable Long appId,
                                            @RequestBody SpecificMerchantCategoryRequest request) throws IOException {
        subscriptionFuncService.specificAppMerchantCategory(appId, request);
        sendResponse(ApiUtils.getSuccessJson());

    }

    /**
     * Specific app merchant category.
     *
     * @throws IOException the io exception
     */
    @DeleteMapping("apps/{appId}/merchant/categories/specific")
    @ApiOperation(value = "删除订阅的应用定向发布给的商户类型", response = AppInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_NO_CONTENT, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.APP, secondaryAction = AuditActions.MERCHANT_TYPE, dataType = AuditDataTypes.APP_MERCHANT_TYPE)
    public void deleteSpecificAppMerchantCategory(@PathVariable Long appId,
                                                  @RequestParam Long merchantCategoryId) throws IOException {
        subscriptionFuncService.deleteSpecificAppMerchantCategory(appId, merchantCategoryId);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Find apk reseller specific page.
     *
     * @param apkId the apk id
     * @throws IOException the io exception
     */
    @GetMapping("apps/apks/{apkId}/reseller/specific")
    @ApiOperation(value = "查询订阅的应用APK定向发布的代理商", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findSpecificApkResellerPage(@PathVariable Long apkId) throws IOException {
        sendResponse(subscriptionFuncService.findSpecificApkResellerPage(parsePage(), apkId));
    }


    /**
     * Find apk reseller specific all list.
     *
     * @param apkId the apk id
     * @throws IOException the io exception
     */
    @GetMapping("apps/apks/{apkId}/reseller/specific/all")
    @ApiOperation(value = "查询订阅的应用APK定向发布的全部代理商", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findSpecificApkResellerAllListPage(@PathVariable Long apkId) throws IOException {
        sendResponse(subscriptionFuncService.findSpecificApkResellerAllList(apkId));
    }

    /**
     * Specific apk.
     *
     * @throws IOException the io exception
     */
    @PostMapping("apps/apks/{apkId}/reseller/specific")
    @ApiOperation(value = "订阅的应用APK定向发布代理商", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.APP, secondaryAction = AuditActions.DISTRIBUTE_VERSION, dataType = AuditDataTypes.APP_APK_PUBLISH)
    public void specificApkReseller(@PathVariable Long apkId,
                                    @RequestBody SpecificResellerRequest request) throws IOException {
        subscriptionFuncService.specificApkReseller(apkId, request);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Delete specific apk.
     *
     * @throws IOException the io exception
     */
    @DeleteMapping("apps/apks/{apkId}/reseller/specific")
    @ApiOperation(value = "删除订阅的应用APK定向发布代理商", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_NO_CONTENT, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.APP, secondaryAction = AuditActions.DISTRIBUTE_VERSION, dataType = AuditDataTypes.APP_APK_PUBLISH)
    public void deleteSpecificApkReseller(@PathVariable Long apkId,
                                          @RequestParam Long resellerId) throws IOException {
        subscriptionFuncService.deleteSpecificApkReseller(apkId, resellerId);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Find firmware reseller specific all list.
     *
     * @param firmwareId the firmware Id
     * @throws IOException the io exception
     */
    @GetMapping("firmwares/{firmwareId}/reseller/specific/all")
    @ApiOperation(value = "查询订阅的固件定向发布的全部代理商", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findSpecificFirmwareResellerAllListPage(@PathVariable Long firmwareId) throws IOException {
        sendResponse(subscriptionFuncService.findSpecificFirmwareResellerAllList(firmwareId));
    }

    /**
     * Specific firmware.
     *
     * @throws IOException the io exception
     */
    @PostMapping("firmwares/{firmwareId}/reseller/specific")
    @ApiOperation(value = "订阅的固件定向发布代理商", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(primaryAction = AuditActions.DISTRIBUTE, type = AuditTypes.FIRMWARE, dataType = AuditDataTypes.FIRMWARE_PUBLISH)
    public void specificResellerFirmware(@PathVariable Long firmwareId,
                                         @RequestBody SpecificResellerRequest request) throws IOException {
        subscriptionFuncService.specificResellerFirmware(firmwareId, request);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /* ----------------------------------------------------------------------------------------------------------------**/

    /**
     * 查询应用APK签名列表
     *
     * @param apkId the apk id
     * @throws IOException the io exception
     */
    @GetMapping("apps/apks/{apkId}/signatures")
    @ApiOperation(value = "查询应用APK签名列表", response = PageInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = PageInfo.class)})
    public void findApkSignatureList(@PathVariable Long apkId) throws IOException {
        sendResponse(appFunctionService.findApkSignaturePage(apkId));
    }

    /**
     * Re sign apk.
     *
     * @param apkId the apk id
     * @throws IOException the io exception
     */
    @PostMapping("apps/apks/{apkId}/signature")
    @ApiOperation(value = "对于签名失败的apk重新签名", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.APP, primaryAction = AuditActions.RESIGN, dataType = AuditDataTypes.APP_RE_SIGN)
    public void reSignApk(@PathVariable Long apkId, @RequestParam Long factoryId, @RequestParam(required = false) String signType) throws IOException {
        appFunctionService.reSignApk(apkId, factoryId, signType);
        sendResponse(ApiUtils.getSuccessJson());
    }

    @GetMapping("apps/export")
    @ApiOperation(value = "导出Global的APP", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void exportGlobalPublishApp(@RequestParam(required = false) String name,
                                       @RequestParam(required = false) String osType,
                                       @RequestParam(required = false) String baseType,
                                       @RequestParam(required = false) Integer chargeType,
                                       @RequestParam(required = false) Boolean specificReseller,
                                       @RequestParam(required = false) Boolean specificMerchantCategory,
                                       @RequestParam(required = false) String resellerIds,
                                       @RequestParam(required = false) String merchantCategoryIds,
                                       @RequestParam(required = false) Boolean isSubscribe,
                                       @RequestParam(required = false) Boolean supportCloudData,
                                       @RequestParam(required = false) Boolean supportCloudMsg,
                                       @RequestParam(required = false) Boolean supportStackly,
                                       @RequestParam(required = false) String factoryId,
                                       @RequestParam(required = false) String modelId,
                                       @RequestParam(required = false) String tz,
                                       @RequestParam(required = false) String orderBy) throws IOException {
        AppSubscriptionExportRequest exportRequest = new AppSubscriptionExportRequest();
        exportRequest.setOsType(osType);
        exportRequest.setKeyWords(StringUtils.trim(name));
        exportRequest.setBaseType(baseType);
        exportRequest.setSubscribe(isSubscribe);
        exportRequest.setSpecificReseller(specificReseller);
        exportRequest.setSpecificMerchantCategory(specificMerchantCategory);
        exportRequest.setVasCloudDataSupported(supportCloudData);
        exportRequest.setVasCloudMsgSupported(supportCloudMsg);
        exportRequest.setVasStacklySupported(supportStackly);
        exportRequest.setOrderBy(orderBy);

        if (StringUtils.isNoneEmpty(factoryId)) {
            exportRequest.setFactoryId(Long.parseLong(factoryId));
        }
        if (StringUtils.isNoneEmpty(modelId)) {
            exportRequest.setModelId(Long.parseLong(modelId));
        }
        if (BooleanUtils.isFalse(getCurrentMarket().getAllowAppOfflinePurchase())) {
            exportRequest.setChargeType(AppChargeType.FREE);
        } else {
            exportRequest.setChargeType(chargeType);
        }

        if (BooleanUtils.isNotFalse(specificReseller) && StringUtils.isNotEmpty(resellerIds)) {
            Set<Long> queryResellerIds = Sets.newHashSet(LongUtils.split(resellerIds, ","));
            exportRequest.setResellerIds(queryResellerIds);
        }

        if (BooleanUtils.isNotFalse(specificMerchantCategory) && StringUtils.isNotEmpty(merchantCategoryIds)) {
            Set<Long> queryMerchantCategoryIds = Sets.newHashSet(LongUtils.split(merchantCategoryIds, ","));
            exportRequest.setMerchantCategoryIds(queryMerchantCategoryIds);
        }
        sendExportActivityResponse(subscriptionFuncService.exportGlobalPublishApp(exportRequest, tz));
    }

    @GetMapping("firmwares/export")
    @ApiOperation(value = "导出Global的固件", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void exportGlobalPublishFirmware(@RequestParam(required = false) String fmStatus,
                                            @RequestParam(required = false) String fmName,
                                            @RequestParam(required = false) Boolean subscribed,
                                            @RequestParam(required = false) String modelId,
                                            @RequestParam(required = false) String factoryId,
                                            @RequestParam(required = false) String firmwareResellerIds,
                                            @RequestParam(required = false) Boolean specificReseller,
                                            @RequestParam(required = false) String firmwareType,
                                            @RequestParam(required = false) String tz,
                                            @RequestParam(required = false) String orderBy) throws IOException {
        FirmwareSubscriptionExportRequest exportRequest = new FirmwareSubscriptionExportRequest();
        if (StringUtils.isNotEmpty(modelId)) {
            exportRequest.setModelId(modelId);
            exportRequest.setModelIdsFilter(Sets.newHashSet(StringUtils.split(modelId, ",")));

        }
        if (StringUtils.isNoneEmpty(factoryId)) {
            exportRequest.setFactoryIdFilter(Long.parseLong(factoryId));
        }
        if (StringUtils.isNotEmpty(firmwareResellerIds)) {
            exportRequest.setResellerIdsFilter(Sets.newHashSet(StringUtils.splitToLongList(firmwareResellerIds, ",")));
        }
        exportRequest.setFmName(StringUtils.trim(fmName));
        exportRequest.setFmStatus(fmStatus);
        exportRequest.setSubscribed(subscribed);
        exportRequest.setSpecificReseller(specificReseller);
        exportRequest.setFirmwareType(firmwareType);
        exportRequest.setOrderBy(orderBy);
        sendExportActivityResponse(subscriptionFuncService.exportGlobalPublishFirmware(exportRequest, tz));
    }
}