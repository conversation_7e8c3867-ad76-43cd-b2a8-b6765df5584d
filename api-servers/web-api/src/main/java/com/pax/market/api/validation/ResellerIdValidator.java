/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api.validation;

import com.pax.market.framework.common.exception.http.NotFoundException;
import com.pax.market.constants.ApiCodes;
import com.paxstore.market.domain.service.organization.ResellerService;
import com.pax.market.framework.common.utils.context.SpringContextHolder;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * The type Reseller id validator.
 */
public class ResellerIdValidator implements ConstraintValidator<ResellerId, Long> {

    @Override
    public void initialize(ResellerId resellerId) {

    }

    @Override
    public boolean isValid(Long s, ConstraintValidatorContext constraintValidatorContext) {


        if (s == null || Long.valueOf(0).equals(s) || Long.valueOf(-1).equals(s)) {
            throw new NotFoundException(ApiCodes.RESELLER_NOT_EXIST);
        }

        ResellerService resellerService = SpringContextHolder.getBean(ResellerService.class);

        if (resellerService.get(s) == null) {
            throw new NotFoundException(ApiCodes.RESELLER_NOT_EXIST);
        }

        return true;
    }
}
