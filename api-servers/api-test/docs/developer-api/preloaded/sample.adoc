[[sample]]
== Sample


Below is a sample of find reseller.
Assume the API key is P3JZPHF8TZERYI9WXR4O and the API secret is D4S5LG6GOWLVD47SQEEMJ9H96MHHMT19XZLKGYIO.

===== Request Parameters

[options="header", cols=".^2a,.^3a,.^5a"]
|===
|Type|Name|Value
|Header |signature    |DE529ED4BBC3FCA6BFCDE77F635A9B33
|Query  |appName         |testApp
|Query  |packageName           |
|Query  |devKey         |P3JZPHF8TZERYI9WXR4O
|Query  |timestamp      |1734515754440
|===


We can string above query parameters and get the query string devKey=P3JZPHF8TZERYI9WXR4O&appName=testApp&timestamp=1734515754440.

Generate a HmacMD5 Message Authentication Code (MAC) as a hex string (uppercase) for the given API secret and query string.

[source,java]
----
String signature = HmacUtils.hmacMd5Hex("D4S5LG6GOWLVD47SQEEMJ9H96MHHMT19XZLKGYIO", "devKey=P3JZPHF8TZERYI9WXR4O&appName=testApp&timestamp=1734515754440").toUpperCase();
//for this case, signature is DE529ED4BBC3FCA6BFCDE77F635A9B33
----


The whole request URL is https://api.whatspos.com/p-market-api/v1/3rd/developer/apps?devKey=P3JZPHF8TZERYI9WXR4O&appName=testApp&timestamp=1734515754440



===== Response Headers

[options="header", cols=".^2a,.^3a,.^6a"]
|===
|Name   |Value  |Remark
|Content-Type               |application/json;charset=UTF-8     |Value of Content-Type
|X-RateLimit-Limit          |3000                               |The maximum number of requests you're permitted to make per 10 minutes
|X-RateLimit-Remaining      |2997                               |The number of requests remaining in the current rate limit window
|X-RateLimit-Reset          |1606203733888                      |The time at which the current rate limit window resets in UTC epoch millisecond
|===

===== Response Body
|===
|{
    "businessCode": 0,
    "data": {
        "id": 1634804562919462,
        "name": "testApp",
        "type": "G",
        "status": "A"
    }
}
|===




