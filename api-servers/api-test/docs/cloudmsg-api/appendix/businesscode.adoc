[[businesscode]]
== Business Codes

|===
|Business Code|Message|Description
|2602|App key invalid|The parameter appKey is blank or invalid
|2603|Signature is invalid|Signature in header is blank or invalid
|2608|App is not active now|App is not active
|2606|Request parameter timestamp is empty|The parameter timestamp not found or is invalid
|2604|Request is expired|The timestamp parameter is not in effective time(30 minutes)
|1000|App not found|
|2017|Request parameter app key is mandatory|App's key is not set
|2018|App Secret is not found or mismatched|App's secret is not set
|2019|Request signature is mandatory|Signature not found
|2020|Invalid Request, signature is mismatched|


|1005|Marketplace not found|
|130|Market has not been activated|
|21237|CloudMessage service is not subscribed|
|2613|Message type is invalid|
|2630|Does not support send CloudMessage to sandbox terminal by TID|
|2609|Serial No is mandatory|
|2627|TID is mandatory|
|2610|Max terminal Id size is 1000|
|2628|Max TID size is 1000|
|2621|Duplicated serial numbers|
|2611|Message content is mandatory|
|2626|Title and Content is mandatory|
|2625|Data is not needed for notification message|
|21239|Message title too long|The max length of title is 32
|2631|No valid TIDs|Parameter tids is not valid or the related terminal not install the app
|2619|No valid serial numbers|Parameter serialNos is not valid or the related terminals not install the app
|2617|Message data is mandatory|The data property of content is mandatory for Data/Mixed type message
|21242|Message data format must be json|The property content.data must be a json
|2622|Title and content is not needed for data type message|The properties content.title and content.content is not needed for data type message
|2623|Title is mandatory|Title is mandatory for notification type message
|2624|Content is mandatory|The property content.content in request body is mandatory for notification/mixed type message
|2612|Message content is too long|The content property in reqeust is too long, the max size of content is 4k
|21263|The tag name not exist|
|21235|App does not support CloudMessage|
|2600|CloudMessage service is not enabled yet|
|21267|CloudMessage service is not subscribed|



|2615|Invalid message identifier|
|2620|Message sent failed|
|2616|Arrived rate is not available now, please try later|


|===