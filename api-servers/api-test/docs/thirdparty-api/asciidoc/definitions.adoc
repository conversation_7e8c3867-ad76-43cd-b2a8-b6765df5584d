
[[_definitions]]
== Definitions

[[_app_information]]
=== APP information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**apkList** +
__optional__|Different version apk of the app|< <<_apk_information,Apk information>> > array
|**chargeType** +
__optional__|The charge type, 0 is for free and 1 is for charging|integer (int32)
|**downloads** +
__optional__|Download times of the app|integer (int64)
|**id** +
__optional__|The unique id of app|integer (int64)
|**name** +
__optional__|The name of app|string
|**osType** +
__optional__|OS type of apk|enum (A(Android), T(Traditional))
|**packageName** +
__optional__|The package name|string
|**price** +
__optional__|The price|number
|**specificReseller** +
__optional__|Whether the app is distributed to reseller(s)|boolean
|**status** +
__optional__|The status|enum (D(Draft), P(Pending), PS(Pending for sign), SI(Is signing), SF(Sign fail), O(Online), R(Rejected), U(Offline))
|===


[[_apk_information]]
=== Apk information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**apkFile** +
__optional__|Apk file information|<<_apkfiledto,ApkFileDTO>>
|**apkFileType** +
__optional__|Apk file type|enum (A(Android type), P(Prolin), B(Broadpos))
|**apkType** +
__optional__|Apk type|enum (P(Parameter apk), N(Non-parameter apk))
|**fileSize** +
__optional__|Apk file size|integer (int64)
|**name** +
__optional__|Apk name|string
|**osType** +
__optional__|OS type of apk|enum (A(Android), T(Traditional))
|**status** +
__optional__|The status of apk|enum (P(Pending), O(Online), R(Rejected), U(Offline))
|**versionCode** +
__optional__|Version code of apk +
**Example** : `121`|integer (int64)
|**versionName** +
__optional__|Version name of apk +
**Example** : `"7.1.0"`|string
|===


[[_apkfiledto]]
=== ApkFileDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**paxPermission** +
__optional__||string
|**permissions** +
__optional__|Apk permission|string
|===


[[_apkparameterdto]]
=== ApkParameterDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**apk** +
__optional__|Apk information|<<_apk_information,Apk information>>
|**apkAvailable** +
__optional__||boolean
|**configuredParameters** +
__optional__||< string, string > map
|**createdDate** +
__optional__||string (date-time)
|**id** +
__optional__||integer (int64)
|**name** +
__optional__||string
|**paramTemplateName** +
__optional__||string
|**updatedDate** +
__optional__||string (date-time)
|===


[[_apkparameterpageresponse]]
=== ApkParameterPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_apkparameterdto,ApkParameterDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_apkparameterresponse]]
=== ApkParameterResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_apkparameterdto,ApkParameterDTO>>
|===


[[_apppageresponse]]
=== AppPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_app_information,APP information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_apppushhistorydto]]
=== AppPushHistoryDTO

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**appName** +
__optional__|string
|**appPushError** +
__optional__|string
|**appPushStatus** +
__optional__|string
|**appPushTime** +
__optional__|string (date-time)
|**parameterPushError** +
__optional__|string
|**parameterPushStatus** +
__optional__|string
|**parameterPushTime** +
__optional__|string (date-time)
|**parameterTemplateName** +
__optional__|string
|**parameterValues** +
__optional__|string
|**parameterVariables** +
__optional__|string
|**parameters** +
__optional__|< string, string > map
|**pushStartTime** +
__optional__|string (date-time)
|**pushType** +
__optional__|string
|**serialNo** +
__optional__|string
|**terminalId** +
__optional__|integer (int64)
|**versionName** +
__optional__|string
|===


[[_column]]
=== Column

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**colName** +
__optional__|string
|**displayName** +
__optional__|string
|**type** +
__optional__|string
|===


[[_dataqueryresult]]
=== DataQueryResult

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**columns** +
__optional__|< <<_column,Column>> > array
|**hasNext** +
__optional__|boolean
|**limit** +
__optional__|integer (int32)
|**offset** +
__optional__|integer (int64)
|**rows** +
__optional__|< < <<_row,Row>> > array > array
|**worksheetName** +
__optional__|string
|===


[[_entityattributedto]]
=== EntityAttributeDTO

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**defaultLabel** +
__optional__|string
|**entityAttributeLabelList** +
__optional__|< <<_entityattributelabelinfo,EntityAttributeLabelInfo>> > array
|**entityType** +
__optional__|string
|**id** +
__optional__|integer (int64)
|**index** +
__optional__|integer (int32)
|**inputType** +
__optional__|string
|**key** +
__optional__|string
|**maxLength** +
__optional__|integer (int32)
|**minLength** +
__optional__|integer (int32)
|**required** +
__optional__|boolean
|**selector** +
__optional__|string
|===


[[_entityattributelabelinfo]]
=== EntityAttributeLabelInfo

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**entityAttributeId** +
__optional__|integer (int64)
|**label** +
__optional__|string
|**locale** +
__optional__|string
|===


[[_entityattributepageresponse]]
=== EntityAttributePageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_entityattributedto,EntityAttributeDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_entityattributeresponse]]
=== EntityAttributeResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_entityattributedto,EntityAttributeDTO>>
|===


[[_error_response]]
=== Error Response

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**message** +
__optional__|Error response message|string
|===


[[_merchant_create_request_body]]
=== Merchant create request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**address** +
__optional__|address, max length is 255|string
|**city** +
__optional__|valid city , max length is 255|string
|**contact** +
__required__|contact name, max length is 255|string
|**country** +
__required__|Valid country code, please refer to &lt;&lt;_country-code,Country Code&gt;&gt; +
**Example** : `"USA"`|string
|**createUserFlag** +
__optional__|Indicate whether to create user when activate the merchant, the default value is false|boolean
|**description** +
__optional__|the description of merchant, max length is 3000|string
|**email** +
__required__|email address, max length is 255 +
**Example** : `"<EMAIL>"`|string
|**entityAttributeValues** +
__optional__|Dynamic attributes of merchant. Whether the attribute is required or not depend on the configuration of attribute.|< string, string > map
|**merchantCategoryNames** +
__optional__|merchant category names|< string > array
|**name** +
__required__|merchant name, max length is 64|string
|**phone** +
__required__|valid phone number, max length is 32|string
|**postcode** +
__optional__|valid postcode, max length is 16|string
|**province** +
__optional__|valid province id, max length is 64|string
|**resellerName** +
__required__|Valid reseller name of merchant belongs to, max length is 64 +
**Example** : `"Reseller A"`|string
|**status** +
__optional__|Status of merchant when create,if value is A the merchant will activated immediately after created, the default value is P +
**Example** : `"A"`|enum (A, P)
|===


[[_merchant_update_request_body]]
=== Merchant update request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**address** +
__optional__|address, max length is 255|string
|**city** +
__optional__|valid city, max length is 255|string
|**contact** +
__required__|contact name, max length is 255|string
|**country** +
__required__|Valid country code, please refer to &lt;&lt;_country-code,Country Code&gt;&gt; +
**Example** : `"USA"`|string
|**createUserFlag** +
__optional__|Indicate whether to create user when activate the merchant, the default value is false|boolean
|**description** +
__optional__|the description of merchant, max length is 65,535 bytes|string
|**email** +
__optional__|email address, max length is 255|string
|**entityAttributeValues** +
__optional__|Dynamic attributes of merchant. Whether the attribute is required or not depend on the configuration of attribute.|< string, string > map
|**merchantCategoryNames** +
__optional__|merchant category names|< string > array
|**name** +
__required__|merchant name, max length is 64|string
|**phone** +
__required__|valid phone number, max length is 32|string
|**postcode** +
__optional__|valid postcode, max length is 16|string
|**province** +
__optional__|valid province id, max length is 64|string
|**resellerName** +
__optional__|valid reseller name, max length is 64|string
|===


[[_merchantcategory_create_request_body]]
=== MerchantCategory create request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**name** +
__required__|Merchant category name, max length is 128 +
**Example** : `"Restaurant"`|string
|**remarks** +
__optional__|Merchant category remarks, max length is 255 +
**Example** : `"This is restaurant merchant category"`|string
|===


[[_merchantcategory_update_request_body]]
=== MerchantCategory update request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**name** +
__required__|Merchant category name, max length is 128 +
**Example** : `"Restaurant"`|string
|**remarks** +
__optional__|Merchant category remarks, max length is 255 +
**Example** : `"This is restaurant merchant category"`|string
|===


[[_merchantcategorydto]]
=== MerchantCategoryDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**id** +
__required__|Id of category +
**Example** : `1000000253`|integer (int64)
|**name** +
__required__|category name +
**Example** : `"category A"`|string
|===


[[_merchantcategoryinfo]]
=== MerchantCategoryInfo

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**id** +
__required__|Id of category +
**Example** : `1000000253`|integer (int64)
|**name** +
__required__|name of category +
**Example** : `"category A"`|string
|**remarks** +
__optional__|**Example** : `"category a description"`|string
|===


[[_merchantcategorylistresponse]]
=== MerchantCategoryListResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__||< <<_merchantcategoryinfo,MerchantCategoryInfo>> > array
|===


[[_merchantcategoryresponse]]
=== MerchantCategoryResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_merchantcategoryinfo,MerchantCategoryInfo>>
|===


[[_merchantpageresponse]]
=== MerchantPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_merchant_information,merchant information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_merchantresponse]]
=== MerchantResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_merchant_information,merchant information>>
|===


[[_merchantvariablecreaterequest]]
=== MerchantVariableCreateRequest

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**merchantId** +
__required__|the id of merchant +
**Example** : `***********`|integer (int64)
|**variableList** +
__optional__|the variable list|< <<_parametervariable,ParameterVariable>> > array
|===


[[_merchantvariabledto]]
=== MerchantVariableDTO

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**appName** +
__optional__|string
|**appPackageName** +
__optional__|string
|**createdDate** +
__optional__|string (date-time)
|**id** +
__optional__|integer (int64)
|**key** +
__optional__|string
|**remarks** +
__optional__|string
|**source** +
__optional__|string
|**type** +
__optional__|string
|**updatedDate** +
__optional__|string (date-time)
|**value** +
__optional__|string
|===


[[_merchantvariablepageresponse]]
=== MerchantVariablePageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_merchantvariabledto,MerchantVariableDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_merchantvariableupdaterequest]]
=== MerchantVariableUpdateRequest

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**key** +
__optional__|the key of the variable|string
|**packageName** +
__required__|package name of apk to push +
**Example** : `"com.by_syk.osbuild"`|string
|**remarks** +
__optional__|remarks|string
|**type** +
__optional__|the type of the variable +
**Example** : `"T,P"`|string
|**value** +
__optional__|the value of the variable, if the parameter type is P(Password) the value need encrypted, please refer to &lt;&lt;_encryption-of-parameter,Encryption of Password Type Parameter&gt;&gt;|string
|===


[[_parameterpushhistorypageresponse]]
=== ParameterPushHistoryPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_apppushhistorydto,AppPushHistoryDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_parametervariable]]
=== ParameterVariable

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**key** +
__optional__|the key of the variable|string
|**packageName** +
__required__|package name of apk to push +
**Example** : `"com.by_syk.osbuild"`|string
|**remarks** +
__optional__|remarks|string
|**type** +
__optional__|the type of the variable +
**Example** : `"T,P"`|string
|**value** +
__optional__|the value of the variable, if the parameter type is P(Password) the value need encrypted, please refer to &lt;&lt;_encryption-of-parameter,Encryption of Password Type Parameter&gt;&gt;|string
|===


[[_parametervariabledto]]
=== ParameterVariableDTO

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**appName** +
__optional__|string
|**appPackageName** +
__optional__|string
|**createdDate** +
__optional__|string (date-time)
|**id** +
__optional__|integer (int64)
|**key** +
__optional__|string
|**remarks** +
__optional__|string
|**source** +
__optional__|string
|**type** +
__optional__|string
|**updatedDate** +
__optional__|string (date-time)
|**value** +
__optional__|string
|===


[[_rki_key_template_information]]
=== RKI KEY Template Information

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**keyId** +
__optional__|string
|===


[[_replace_merchant_email_request]]
=== Replace merchant email request

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**createUser** +
__optional__|create user flag, if null won't create user|boolean
|**email** +
__required__|new email address|string
|===


[[_replace_reseller_email_request]]
=== Replace reseller email request

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**email** +
__required__|new email address, max length is 255|string
|===


[[_request_body_of_push_app_to_terminal]]
=== Request body of Push App to terminal

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**base64FileParameters** +
__optional__|The parameter of file type|< <<_83a328528e0d06ad05cae33308d94563,Request body of Push App's file param to terminal>> > array
|**effectiveTime** +
__optional__|The time when to start the push task|string (date-time)
|**expiredTime** +
__optional__|The time when to stop the push task|string (date-time)
|**forceUpdate** +
__optional__|Whether to force the app to update|boolean
|**inheritPushHistory** +
__optional__|Whether to inherit latest success push history|boolean
|**packageName** +
__required__|package name of apk to push +
**Example** : `"com.by_syk.osbuild"`|string
|**parameters** +
__optional__|The parameter key and value, the key the the PID in template|< string, string > map
|**pushTemplateName** +
__optional__|The push template name|string
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time, if serialNo is null then tid is required +
**Example** : `"DFGD324TID"`|string
|**templateName** +
__optional__|The template file name of paramter application. The template file name can be found in the detail of the parameter application. If user want to push more than one template the please use \| to concact the different template file names like tempate1.xml\|template2.xml\|template3.xml, the max size of template file names is 10.|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upcased, if tid is null then serialNo is required +
**Example** : `"OZTW2C0Y"`|string
|**version** +
__optional__|The version name of app which you want to push, if it is blank API will use the latest version +
**Example** : `"1.5.2"`|string
|**wifiOnly** +
__optional__|Whether to download over Wi-Fi or Cable network only, don’t allow to download over the cellular network|boolean
|===


[[_83a328528e0d06ad05cae33308d94563]]
=== Request body of Push App's file param to terminal

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**fileData** +
__optional__|The parameter of file type, file base64 data|string
|**fileName** +
__optional__|The parameter of file type, filename containing suffix|string
|**pid** +
__optional__|The PID in template|string
|===


[[_request_body_of_push_rki_key_task_to_group_for_create]]
=== Request body of Push Rki Key task to group for create

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**effectiveTime** +
__optional__|The time when to start the push task|string (date-time)
|**expiredTime** +
__optional__|The time when to stop the push task|string (date-time)
|**groupId** +
__optional__|The id of group|integer (int64)
|**rkiKey** +
__required__|Rki Key template to push|string
|===


[[_request_body_of_push_rki_key_task_to_terminal_for_create]]
=== Request body of Push Rki Key task to terminal for create

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**effectiveTime** +
__optional__|The time when to start the push task|string (date-time)
|**expiredTime** +
__optional__|The time when to stop the push task|string (date-time)
|**rkiKey** +
__required__|Rki Key template to push|string
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upcased|string
|===


[[_request_body_of_terminal_rki_push_task_for_disable]]
=== Request body of Terminal rki push task for disable

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**rkiKey** +
__required__|Rki Key template to push|string
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upcased|string
|===


[[_request_body_of_terminalfirmware_for_cancel]]
=== Request body of TerminalFirmware for cancel

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**fmName** +
__required__|firmware name of firmware to push|string
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upper cased|string
|===


[[_request_body_of_terminalfirmware_for_create]]
=== Request body of TerminalFirmware for create

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**effectiveTime** +
__optional__|The time when to start the push task|string (date-time)
|**expiredTime** +
__optional__|The time when to stop the push task|string (date-time)
|**fmName** +
__required__|firmware name of firmware to push|string
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upcased|string
|**wifiOnly** +
__optional__|Whether to download over Wi-Fi or Cable network only, don’t allow to download over the cellular network|boolean
|===


[[_request_body_of_batch_delete_merchant_variables]]
=== Request body of batch delete merchant variables

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**variableIds** +
__required__|variable id list|< integer (int64) > array
|===


[[_request_body_of_batch_delete_terminal_variables]]
=== Request body of batch delete terminal variables

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**variableIds** +
__required__|variable id list|< integer (int64) > array
|===


[[_request_body_of_create_apk_parameter]]
=== Request body of create apk parameter

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**base64FileParameters** +
__optional__|The parameter of file type|< <<_83a328528e0d06ad05cae33308d94563,Request body of Push App's file param to terminal>> > array
|**groupApkId** +
__optional__||integer (int64)
|**name** +
__optional__|The template name|string
|**packageName** +
__required__|package name of apk to push +
**Example** : `"com.by_syk.osbuild"`|string
|**paramTemplateName** +
__optional__|The template file name of paramter application. The template file name can be found in the detail of the parameter application. If user want to push more than one template the please use \| to concact the different template file names like tempate1.xml\|template2.xml\|template3.xml, the max size of template file names is 10.|string
|**parameters** +
__optional__|The parameter key and value, the key the the PID in template|< string, string > map
|**terminalApkId** +
__optional__||integer (int64)
|**version** +
__optional__|The version name of app which you want to push, if it is blank API will use the latest version +
**Example** : `"1.5.2"`|string
|===


[[_request_body_of_create_terminal_variable]]
=== Request body of create terminal variable

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time, if serialNo is null then tid is required +
**Example** : `"DFGD324TID"`|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upcased, if tid is null then serialNo is required +
**Example** : `"OZTW2C0Y"`|string
|**variableList** +
__optional__|the variable list|< <<_parametervariable,ParameterVariable>> > array
|===


[[_request_body_of_creating_terminal_group]]
=== Request body of creating terminal group

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**containSubResellerTerminal** +
__optional__|Whether to include sub-reseller's terminal, if it is null will use the default value 'false'|boolean
|**description** +
__optional__|Description of group|string
|**dynamic** +
__optional__|Indicate whether the group is dynamic or not, if it is null will use the default value 'false'|boolean
|**merchantNameList** +
__optional__|Merchant name list|< string > array
|**modelName** +
__required__|Model name|string
|**name** +
__required__|Group name|string
|**resellerName** +
__required__|Reseller name|string
|**status** +
__optional__|Group status|enum (A, P, (A is for Active and P is for Pending, if the value is null will create a group with pending status))
|===


[[_request_body_of_pushing_app_to_terminal_group]]
=== Request body of pushing app to terminal group

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**base64FileParameters** +
__optional__|The parameter of file type|< <<_83a328528e0d06ad05cae33308d94563,Request body of Push App's file param to terminal>> > array
|**effectiveTime** +
__optional__|The time when to start the push task|string (date-time)
|**expiredTime** +
__optional__|The time when to stop the push task|string (date-time)
|**forceUpdate** +
__optional__|Whether to force the app to update|boolean
|**groupId** +
__required__|Group id|integer (int64)
|**inheritPushHistory** +
__optional__|Whether to inherit latest success push history|boolean
|**packageName** +
__required__|package name of apk to push +
**Example** : `"com.by_syk.osbuild"`|string
|**parameters** +
__optional__|The parameter key and value, the key the the PID in template|< string, string > map
|**pushTemplateName** +
__optional__|The push template name|string
|**templateName** +
__optional__|The template file name of paramter application. The template file name can be found in the detail of the parameter application. If user want to push more than one template the please use \| to concact the different template file names like tempate1.xml\|template2.xml\|template3.xml, the max size of template file names is 10.|string
|**version** +
__optional__|The version name of app which you want to push, if it is blank API will use the latest version +
**Example** : `"1.5.2"`|string
|**wifiOnly** +
__optional__|Whether to download over Wi-Fi or Cable network only, don’t allow to download over the cellular network|boolean
|===


[[_request_body_of_suspend_or_uninstall_terminalapk]]
=== Request body of suspend or uninstall TerminalApk

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**packageName** +
__optional__|package name of apk to push|string
|**serialNo** +
__optional__|the serial number of terminal, tid and serialNo cannot be empty at same time|string
|**tid** +
__optional__|the tid of terminal, and alphanumeric, characters are upcased|string
|===


[[_request_body_of_update_apk_parameter]]
=== Request body of update apk parameter

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**base64FileParameters** +
__optional__|The parameter of file type|< <<_83a328528e0d06ad05cae33308d94563,Request body of Push App's file param to terminal>> > array
|**paramTemplateName** +
__optional__|The template file name of paramter application. The template file name can be found in the detail of the parameter application. If user want to push more than one template the please use \| to concact the different template file names like tempate1.xml\|template2.xml\|template3.xml, the max size of template file names is 10.|string
|**parameters** +
__optional__|The parameter key and value, the key the the PID in template|< string, string > map
|===


[[_request_body_of_update_terminal_variable]]
=== Request body of update terminal variable

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**key** +
__optional__|the key of the variable|string
|**packageName** +
__required__|package name of apk to push +
**Example** : `"com.by_syk.osbuild"`|string
|**remarks** +
__optional__|remarks|string
|**type** +
__optional__|the type of the variable +
**Example** : `"T,P"`|string
|**value** +
__optional__|the value of the variable, if the parameter type is P(Password) the value need encrypted, please refer to &lt;&lt;_encryption-of-parameter,Encryption of Password Type Parameter&gt;&gt;|string
|===


[[_request_body_of_updating_terminal_group]]
=== Request body of updating terminal group

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**description** +
__optional__|Group description|string
|**merchantNameList** +
__optional__|Merchant name list|< string > array
|**modelName** +
__optional__|Model name, if value is null API won't update it|string
|**name** +
__optional__|Group name, if value is null API wont' update it|string
|**resellerName** +
__optional__|Reseller name, if value is null API won't update it|string
|===


[[_request_of_update_key_and_secret]]
=== Request of update key and secret

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**apiKey** +
__required__|uptrillion API integration key|string
|**apiSecret** +
__required__|uptrillion API integration secret|string
|===


[[_reseller_information]]
=== Reseller Information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**id** +
__required__|the id of the reseller +
**Example** : `378812`|integer (int64)
|**name** +
__required__|the name of the reseller +
**Example** : `"Reseller A"`|string
|===


[[_reseller_create_request_body]]
=== Reseller create request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**address** +
__optional__|address of reseller, max length is 255 +
**Example** : `"Suzhou Park East Road, No. 88 2.5 Industrial Park"`|string
|**company** +
__optional__|company of reseller, max length is 255 +
**Example** : `"PAXSZ"`|string
|**contact** +
__required__|contact name, max length is 64 +
**Example** : `"Tom"`|string
|**country** +
__required__|Valid country code, please refer to &lt;&lt;_country-code,Country Code&gt;&gt; +
**Example** : `"USA"`|string
|**email** +
__required__|email address(login name), max length is 255 +
**Example** : `"<EMAIL>"`|string
|**entityAttributeValues** +
__optional__|Dynamic attributes. Whether the attributes is required or not depends on the attributes configuration.|< string, string > map
|**name** +
__required__|reseller name, max length is 64 +
**Example** : `"Reseller A"`|string
|**parentResellerName** +
__optional__|Parent reseller name, if it is empty will set the root reseller of current marketplace as the parent reseller +
**Example** : `"Reseller X"`|string
|**phone** +
__required__|phone number, max length is 32 +
**Example** : `"45184545"`|string
|**postcode** +
__optional__|post code, max length is 16 +
**Example** : `"215000"`|string
|**status** +
__optional__|status, A is for Active, P is for Pending +
**Example** : `"A"`|enum (A, P)
|===


[[_reseller_information]]
=== Reseller information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**address** +
__optional__|address +
**Example** : `"Suzhou Park East Road, No. 88 2.5 Industrial Park"`|string
|**company** +
__optional__|company +
**Example** : `"PAXSZ"`|string
|**contact** +
__required__|contact +
**Example** : `"Tom"`|string
|**country** +
__optional__|Country code, please refer to &lt;&lt;_country-code,Country Code&gt;&gt; +
**Example** : `"USA"`|string
|**email** +
__required__|email +
**Example** : `"<EMAIL>"`|string
|**entityAttributeValues** +
__optional__|customized attributes, it is null when do search operation|< string, string > map
|**id** +
__required__|Id of reseller +
**Example** : `1000000253`|integer (int64)
|**name** +
__required__|name of reseller +
**Example** : `"Reseller A"`|string
|**parent** +
__optional__||<<_reseller_information,Reseller Information>>
|**phone** +
__required__|phone +
**Example** : `"89894545"`|string
|**postcode** +
__optional__|postcode +
**Example** : `"215000"`|string
|**status** +
__required__|status of reseller +
**Example** : `"A"`|string
|===


[[_reseller_update_request_body]]
=== Reseller update request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**address** +
__optional__|address of reseller, max length is 255 +
**Example** : `"Suzhou Park East Road, No. 88 2.5 Industrial Park"`|string
|**company** +
__optional__|company of reseller, max length is 255 +
**Example** : `"PAXSZ"`|string
|**contact** +
__required__|contact name, max length is 64 +
**Example** : `"Tom"`|string
|**country** +
__required__|Valid country code, please refer to &lt;&lt;_country-code,Country Code&gt;&gt; +
**Example** : `"USA"`|string
|**email** +
__optional__|email address(login name), max length is 255 +
**Example** : `"<EMAIL>"`|string
|**entityAttributeValues** +
__optional__|Dynamic attributes. Whether the attributes is required or not depends on the attributes configuration.|< string, string > map
|**name** +
__required__|reseller name, max length is 64 +
**Example** : `"Reseller A"`|string
|**parentResellerName** +
__optional__|Do not suggest set value for this property. If set value please keep the parentResellerName same as the original parentResellerName. Otherwise API will return a 1830 business code., max length is 64 +
**Example** : `"Reseller X"`|string
|**phone** +
__required__|phone number, max length is 32 +
**Example** : `"45184545"`|string
|**postcode** +
__optional__|post code, max length is 16 +
**Example** : `"215000"`|string
|===


[[_resellerpageresponse]]
=== ResellerPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_reseller_information,Reseller information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_resellerresponse]]
=== ResellerResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_reseller_information,Reseller information>>
|===


[[_resellerrkikeypageresponse]]
=== ResellerRkiKeyPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_rki_key_template_information,RKI KEY Template Information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_0b592c5ca917dd7a3be50acaa40734df]]
=== Response«DataQueryResult»

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_dataqueryresult,DataQueryResult>>
|===


[[_row]]
=== Row

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**colName** +
__optional__|string
|**origin** +
__optional__|string
|**value** +
__optional__|string
|===


[[_successresponse]]
=== SuccessResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**message** +
__optional__||string
|===


[[_terminal_apk_parameter_push_information]]
=== Terminal APK parameter push information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**actionStatus** +
__optional__|Please refer to &lt;&lt;_action-status,Action Status&gt;&gt;|integer (int32)
|**actionTime** +
__optional__||string (date-time)
|**configuredParameters** +
__optional__||< string, string > map
|**errorCode** +
__optional__|Please refer to &lt;&lt;_action-error-code,Action Error Code&gt;&gt;|integer (int32)
|**paramTemplateName** +
__optional__||string
|===


[[_terminal_apk_push_information]]
=== Terminal APK push information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**actionStatus** +
__optional__|Please refer to &lt;&lt;_action-status,Action Status&gt;&gt;|integer (int32)
|**actionTime** +
__optional__||string (date-time)
|**activatedDate** +
__optional__||string (date-time)
|**apkPackageName** +
__optional__||string
|**apkVersionCode** +
__optional__||integer (int64)
|**apkVersionName** +
__optional__||string
|**effectiveTime** +
__optional__||string (date-time)
|**errorCode** +
__optional__|Please refer to &lt;&lt;_action-error-code,Action Error Code&gt;&gt;|integer (int32)
|**expiredTime** +
__optional__||string (date-time)
|**forceUpdate** +
__optional__||boolean
|**id** +
__optional__||integer (int64)
|**status** +
__optional__||string
|**terminalApkParam** +
__optional__||<<_terminal_apk_parameter_push_information,Terminal APK parameter push information>>
|**terminalSN** +
__optional__||string
|**wifiOnly** +
__optional__||boolean
|===


[[_terminal_group_apk_push_information]]
=== Terminal Group APK push information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**actionStatus** +
__optional__|Please refer to &lt;&lt;_action-status,Action Status&gt;&gt;, value can be '0' and '1'|integer (int32)
|**apkPackageName** +
__optional__||string
|**apkVersionCode** +
__optional__||integer (int64)
|**apkVersionName** +
__optional__||string
|**effectiveTime** +
__optional__||string (date-time)
|**expiredTime** +
__optional__||string (date-time)
|**failedCount** +
__optional__||integer (int32)
|**forceUpdate** +
__optional__||boolean
|**groupApkParam** +
__optional__||<<_terminalgroupapkparamdto,TerminalGroupApkParamDTO>>
|**id** +
__optional__||integer (int64)
|**pendingCount** +
__optional__||integer (int32)
|**status** +
__optional__||string
|**successCount** +
__optional__||integer (int32)
|**updatedDate** +
__optional__||string (date-time)
|**wifiOnly** +
__optional__||boolean
|===


[[_terminal_configuration_update_request]]
=== Terminal configuration update request

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**allowReplacement** +
__optional__|Whether allow replacement by API or input serial number on terminal|boolean
|===


[[_terminal_configurations]]
=== Terminal configurations

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**allowReplacement** +
__optional__|Indicate whether the terminal replacement is allowed by API or input serial number on terminal|boolean
|===


[[_terminal_copy_request_body]]
=== Terminal copy request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**name** +
__required__|Terminal Name, max length is 64 +
**Example** : `"terminal for test"`|string
|**serialNo** +
__optional__|Serial No, max length is 16, If the terminal is activated, Serial No is required +
**Example** : `"SN020212"`|string
|**status** +
__optional__|The status of terminal. value is 'A' or 'P' +
**Example** : `"A"`|string
|**terminalId** +
__required__|The original terminal ID +
**Example** : `1234567890`|integer (int64)
|**tid** +
__optional__|The tid of terminal. If it is empty system won't update the TID which is given or generated when create. And the length range is from 8 to 16. +
**Example** : `"7ECR2SVJ"`|string
|===


[[_terminal_create_request_body]]
=== Terminal create request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**location** +
__optional__|Location, max length is 64 +
**Example** : `"US"`|string
|**merchantName** +
__optional__|Merchant name, max length is 64, if terminal is active merchant name is mandatory +
**Example** : `"Merchant A"`|string
|**modelName** +
__required__|Model name, max length is 64 +
**Example** : `"A920"`|string
|**name** +
__required__|Terminal Name, max length is 64 +
**Example** : `"terminal for test"`|string
|**remark** +
__optional__|Remark, max length is 500 +
**Example** : `"Merchant A uses"`|string
|**resellerName** +
__required__|Reseller name, max length is 64 +
**Example** : `"Reseller A"`|string
|**serialNo** +
__optional__|Serial No, max length is 16, if terminal is Active serialNo is mandatory +
**Example** : `"SN020212"`|string
|**status** +
__optional__|Status, P(Pending), A(Active), default is P +
**Example** : `"P"`|enum (P(Pending), A(Active))
|**tid** +
__optional__|The tid of terminal. If it is empty system will generate a tid when creating. And the length range is from 8 to 16. +
**Example** : `"7ECR2SVJ"`|string
|===


[[_terminal_detail]]
=== Terminal detail

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**cellid** +
__optional__|Terminal Cellid|string
|**iccid** +
__optional__|Terminal Iccid|string
|**imei** +
__optional__|Terminal IMEI|string
|**ip** +
__optional__|Terminal NETWORK IP|string
|**language** +
__optional__|Terminal Language +
**Example** : `"English"`|string
|**macAddress** +
__optional__|Terminal MacAddress|string
|**osVersion** +
__optional__|Terminal Android Version +
**Example** : `"Android 5.1.1"`|string
|**pn** +
__optional__|Terminal PN +
**Example** : `"A920-3AE-RE5-21EE"`|string
|**screenResolution** +
__optional__|Terminal screenResolution +
**Example** : `"720px * 1280px"`|string
|**timeZone** +
__optional__|Terminal TimeZone +
**Example** : `"GMT +08:00"`|string
|===


[[_terminal_firmware_information]]
=== Terminal firmware information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**actionStatus** +
__optional__|Please refer to &lt;&lt;_action-status,Action Status&gt;&gt;|integer (int32)
|**activatedDate** +
__optional__||string (date-time)
|**effectiveTime** +
__optional__||string (date-time)
|**errorCode** +
__optional__|Please refer to &lt;&lt;_action-error-code,Action Error Code&gt;&gt;|integer (int32)
|**expiredTime** +
__optional__||string (date-time)
|**fmName** +
__optional__||string
|**id** +
__optional__||integer (int64)
|**status** +
__optional__||string
|**terminalSN** +
__optional__||string
|**wifiOnly** +
__optional__||boolean
|===


[[_terminal_group_request_body]]
=== Terminal group  request body

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**groupIds** +
__optional__|< integer (int64) > array
|**terminalIds** +
__optional__|< integer (int64) > array
|===


[[_terminal_move_request_body]]
=== Terminal move request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**merchantName** +
__required__|Merchant name, max length is 64 +
**Example** : `"Merchant A"`|string
|**resellerName** +
__required__|Reseller name, max length is 64 +
**Example** : `"Reseller A"`|string
|===


[[_terminal_update_request_body]]
=== Terminal update request body

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**location** +
__optional__|Location, max length is 32 +
**Example** : `"US"`|string
|**merchantName** +
__optional__|Merchant name, max length is 64, if terminal is active merchant name is mandatory +
**Example** : `"Merchant A"`|string
|**modelName** +
__required__|Model name, max length is 64 +
**Example** : `"A920"`|string
|**name** +
__required__|Terminal Name, max length is 64 +
**Example** : `"terminal for test"`|string
|**remark** +
__optional__|Remark, max length is 500 +
**Example** : `"Merchant A uses"`|string
|**resellerName** +
__required__|Reseller name, max length is 64 +
**Example** : `"Reseller A"`|string
|**serialNo** +
__optional__|Serial No, max length is 16, if terminal is Active serialNo is mandatory +
**Example** : `"SN020212"`|string
|**tid** +
__optional__|The tid of terminal. If it is empty system won't update the TID which is given or generated when create. And the length range is from 8 to 16. +
**Example** : `"7ECR2SVJ"`|string
|===


[[_terminalaccessorydto]]
=== TerminalAccessoryDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**basic** +
__optional__|The basic information of the accessory device|< <<_terminaldevicesimpledto,TerminalDeviceSimpleDTO>> > array
|**hardware** +
__optional__|The hardware information of the accessory device|< <<_terminaldevicesimpledto,TerminalDeviceSimpleDTO>> > array
|**history** +
__optional__|The history information of the accessory device|< <<_terminaldevicedto,TerminalDeviceDTO>> > array
|**installApps** +
__optional__|The installApps information of the accessory device|< <<_terminaldevicesimpledto,TerminalDeviceSimpleDTO>> > array
|**relatedTerminalName** +
__optional__|The accessory information terminal name|string
|===


[[_terminalapkpageresponse]]
=== TerminalApkPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminal_apk_push_information,Terminal APK push information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalapkresponse]]
=== TerminalApkResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminal_apk_push_information,Terminal APK push information>>
|===


[[_terminalconfigresponse]]
=== TerminalConfigResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminal_configurations,Terminal configurations>>
|===


[[_terminaldto]]
=== TerminalDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**createdDate** +
__optional__|The create time|string (date-time)
|**geoLocation** +
__optional__|The geo location of the terminal|<<_terminallocationdto,TerminalLocationDTO>>
|**id** +
__required__|Terminal Id +
**Example** : `1000000253`|integer (int64)
|**installedApks** +
__optional__|The installed applications of the terminal|< <<_terminalinstalledapkdto,TerminalInstalledApkDTO>> > array
|**installedFirmware** +
__optional__|The installed firmware of the terminal|<<_terminalinstalledfirmwaredto,TerminalInstalledFirmwareDTO>>
|**lastActiveTime** +
__optional__|The activation time|string (date-time)
|**location** +
__optional__|The location|string
|**merchantName** +
__optional__|Merchant name of terminal +
**Example** : `"Merchant_A"`|string
|**modelName** +
__required__|Model name of terminal +
**Example** : `"A920"`|string
|**name** +
__required__|Terminal name +
**Example** : `"termina A"`|string
|**remark** +
__optional__|The remark|string
|**resellerName** +
__required__|Reseller name of terminal +
**Example** : `"Reseller_A"`|string
|**serialNo** +
__optional__|Terminal SN +
**Example** : `"1640000069TID"`|string
|**status** +
__required__|Terminal status +
**Example** : `"A"`|string
|**terminalAccessory** +
__optional__|The terminal accessory info|<<_terminalaccessorydto,TerminalAccessoryDTO>>
|**terminalAccessoryList** +
__optional__|The terminal accessory info list|< <<_terminalaccessorydto,TerminalAccessoryDTO>> > array
|**terminalDetail** +
__optional__|The terminal detail info|<<_terminal_detail,Terminal detail>>
|**tid** +
__required__|Terminal TID +
**Example** : `"R15ET7F5"`|string
|===


[[_terminaldevicedto]]
=== TerminalDeviceDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**fileSize** +
__optional__|The size of the file pushed by the accessory device +
**Example** : `2165940`|integer (int64)
|**fileType** +
__optional__|The type of the file pushed by the accessory device +
**Example** : `"Private file"`|string
|**installTime** +
__optional__|The accessory information install time +
**Example** : `"1588053630000"`|string (date-time)
|**name** +
__optional__|The accessory information name +
**Example** : `"RAM"`|string
|**remarks** +
__optional__|The remarks information +
**Example** : `"sn"`|string
|**source** +
__optional__|The file source +
**Example** : `"Local Upgrade"`|string
|**status** +
__optional__|The status of the related historical push of the accessory device +
**Example** : `"Success"`|string
|**version** +
__optional__|The accessory information version +
**Example** : `"V1.00.08_201801"`|string
|===


[[_terminaldevicesimpledto]]
=== TerminalDeviceSimpleDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**content** +
__optional__|The accessory information content +
**Example** : `"246.50MB"`|string
|**name** +
__optional__|The accessory information name +
**Example** : `"RAM"`|string
|===


[[_terminalfirmwarepageresponse]]
=== TerminalFirmwarePageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminal_firmware_information,Terminal firmware information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalfirmwareresponse]]
=== TerminalFirmwareResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminal_firmware_information,Terminal firmware information>>
|===


[[_terminalgroupapkpageresponse]]
=== TerminalGroupApkPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminal_group_apk_push_information,Terminal Group APK push information>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalgroupapkparamdto]]
=== TerminalGroupApkParamDTO

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**configuredParameters** +
__optional__|< string, string > map
|**failedCount** +
__optional__|integer (int32)
|**paramTemplateName** +
__optional__|string
|**pendingCount** +
__optional__|integer (int32)
|**successCount** +
__optional__|integer (int32)
|===


[[_terminalgroupapkresponse]]
=== TerminalGroupApkResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminal_group_apk_push_information,Terminal Group APK push information>>
|===


[[_terminalgroupdto]]
=== TerminalGroupDTO

[options="header", cols=".^3a,.^4a"]
|===
|Name|Schema
|**containSubResellerTerminal** +
__optional__|boolean
|**createdByResellerId** +
__optional__|integer (int64)
|**createdDate** +
__optional__|string (date-time)
|**description** +
__optional__|string
|**dynamic** +
__optional__|boolean
|**id** +
__optional__|integer (int64)
|**merchantNames** +
__optional__|< string > array
|**modelName** +
__optional__|string
|**name** +
__optional__|string
|**resellerName** +
__optional__|string
|**status** +
__optional__|string
|**terminalCount** +
__optional__|integer (int32)
|**updatedDate** +
__optional__|string (date-time)
|===


[[_terminalgrouppageresponse]]
=== TerminalGroupPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminalgroupdto,TerminalGroupDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalgroupresponse]]
=== TerminalGroupResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminalgroupdto,TerminalGroupDTO>>
|===


[[_terminalgrouprkidto]]
=== TerminalGroupRkiDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**actionStatus** +
__optional__|Please refer to &lt;&lt;_action-status,Action Status&gt;&gt;|integer (int32)
|**actionTime** +
__optional__||string (date-time)
|**activatedDate** +
__optional__||string (date-time)
|**completed** +
__optional__||boolean
|**effectiveTime** +
__optional__||string (date-time)
|**errorCode** +
__optional__|Please refer to &lt;&lt;_action-error-code,Action Error Code&gt;&gt;|integer (int32)
|**expiredTime** +
__optional__||string (date-time)
|**failedCount** +
__optional__||integer (int32)
|**id** +
__optional__||integer (int64)
|**pendingCount** +
__optional__||integer (int32)
|**pushLimit** +
__optional__||integer (int32)
|**remarks** +
__optional__||string
|**rkiKey** +
__optional__||string
|**status** +
__optional__||string
|**successCount** +
__optional__||integer (int32)
|**terminalSN** +
__optional__||string
|===


[[_terminalgrouprkipageresponse]]
=== TerminalGroupRkiPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminalgrouprkidto,TerminalGroupRkiDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalgrouprkiresponse]]
=== TerminalGroupRkiResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminalgrouprkidto,TerminalGroupRkiDTO>>
|===


[[_terminalinstalledapkdto]]
=== TerminalInstalledApkDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**appName** +
__optional__|Application name|string
|**installTime** +
__optional__|Application installed time|string (date-time)
|**packageName** +
__optional__|Package name of application|string
|**versionCode** +
__optional__|Version code of application|integer (int64)
|**versionName** +
__optional__|Version name of application|string
|===


[[_terminalinstalledfirmwaredto]]
=== TerminalInstalledFirmwareDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**firmwareName** +
__optional__|Firmware name|string
|**installTime** +
__optional__|Firmware installed date|string (date-time)
|===


[[_terminallocationdto]]
=== TerminalLocationDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**lat** +
__optional__|The latitude of geo location|number (double)
|**lng** +
__optional__|The longitude of geo location|number (double)
|===


[[_terminalnetworkdto]]
=== TerminalNetworkDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**battery** +
__optional__|Terminal battery +
**Example** : `91.0`|number (float)
|**id** +
__required__|Terminal Id +
**Example** : `1000000253`|integer (int64)
|**macAddress** +
__optional__|Terminal mac address +
**Example** : `"08:00:20:0A:8C:6D"`|string
|**network** +
__optional__|Terminal network +
**Example** : `"NETWORK_WIFI"`|string
|**onlineStatus** +
__required__|Terminal online status +
**Example** : `2`|integer (int32)
|**serialNo** +
__optional__|Terminal SN +
**Example** : `"1640000069TID"`|string
|**status** +
__required__|Terminal status +
**Example** : `"A"`|string
|**tid** +
__required__|Terminal TID +
**Example** : `"R15ET7F5"`|string
|===


[[_terminalnetworkdtoresponse]]
=== TerminalNetworkDTOResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminalnetworkdto,TerminalNetworkDTO>>
|===


[[_terminalpageresponse]]
=== TerminalPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminaldto,TerminalDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalparametervariablepageresponse]]
=== TerminalParameterVariablePageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_parametervariabledto,ParameterVariableDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalpedinfodto]]
=== TerminalPedInfoDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**info** +
__optional__|JSON formatted PED information|string
|===


[[_terminalpedinforesponse]]
=== TerminalPedInfoResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminalpedinfodto,TerminalPedInfoDTO>>
|===


[[_terminalresponse]]
=== TerminalResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminaldto,TerminalDTO>>
|===


[[_terminalrkidto]]
=== TerminalRkiDTO

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**actionStatus** +
__optional__|Please refer to &lt;&lt;_action-status,Action Status&gt;&gt;|integer (int32)
|**actionTime** +
__optional__||string (date-time)
|**activatedDate** +
__optional__||string (date-time)
|**effectiveTime** +
__optional__||string (date-time)
|**errorCode** +
__optional__|Please refer to &lt;&lt;_action-error-code,Action Error Code&gt;&gt;|integer (int32)
|**expiredTime** +
__optional__||string (date-time)
|**id** +
__optional__||integer (int64)
|**remarks** +
__optional__||string
|**rkiKey** +
__optional__||string
|**status** +
__optional__||string
|**terminalSN** +
__optional__||string
|===


[[_terminalrkipageresponse]]
=== TerminalRkiPageResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**dataset** +
__optional__||< <<_terminalrkidto,TerminalRkiDTO>> > array
|**hasNext** +
__optional__||boolean
|**limit** +
__optional__||integer (int32)
|**pageNo** +
__optional__||integer (int32)
|**totalCount** +
__optional__||integer (int64)
|===


[[_terminalrkiresponse]]
=== TerminalRkiResponse

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**businessCode** +
__required__|Business Code, if request is success the businessCode is 0|integer (int32)
|**data** +
__optional__|Response data|<<_terminalrkidto,TerminalRkiDTO>>
|===


[[_the_request_body_of_creating_an_entity_attribute]]
=== The request body of creating an entity attribute

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**defaultLabel** +
__required__|The default label of entity attribute|string
|**entityType** +
__required__|Entity attribute type|enum (Merchant, Reseller)
|**inputType** +
__required__|Entity attribute input type|enum (Text, Selector)
|**key** +
__required__|The key of entity attribute|string
|**maxLength** +
__optional__|The maximal length for the value of entity attribute, only TEXT input type need this property|integer (int32)
|**minLength** +
__optional__|The minimal length for the value of entity attribute, only TEXT input type need this property|integer (int32)
|**required** +
__required__|Whether the value for this entity attribute is required or not|boolean
|**selector** +
__optional__|The selector for the SELECTOR input type entity attribute, only required for SELECTOR type entity attribute|string
|===


[[_the_request_body_of_updating_an_entity_attribute]]
=== The request body of updating an entity attribute

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**defaultLabel** +
__optional__|The default label of entity attribute, if it is null API won't update it|string
|**inputType** +
__required__|The input type of entity attribute|enum (Text, Selector)
|**maxLength** +
__optional__|The maximal length for the value of entity attribute, this property is for TEXT input type entity attribute, if it is null API won't update it|integer (int32)
|**minLength** +
__optional__|The minimal length for the value of entity attribute, this property is for TEXT input type entity attribute, if it is null API won't update it|integer (int32)
|**required** +
__required__|Whether the value for this entity attribute is required or not|boolean
|**selector** +
__optional__|The selector for the SELECTOR input type entity attribute, this property is for SELECTOR input type entity attribute, if the value is null API will not update the original value|string
|===


[[_the_request_body_of_updating_entity_attribute_label]]
=== The request body of updating entity attribute label

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**entityAttributeLabelList** +
__required__|Entity attribute label list|< <<_entityattributelabelinfo,EntityAttributeLabelInfo>> > array
|===


[[_merchant_information]]
=== merchant information

[options="header", cols=".^3a,.^11a,.^4a"]
|===
|Name|Description|Schema
|**address** +
__optional__|address +
**Example** : `"Suzhou Park East Road, No. 88 2.5 Industrial Park"`|string
|**city** +
__optional__|city +
**Example** : `"hei long jiang"`|string
|**contact** +
__optional__|contact +
**Example** : `"Tom"`|string
|**country** +
__optional__|Country code, please refer to &lt;&lt;_country-code,Country Code&gt;&gt; +
**Example** : `"USA"`|string
|**description** +
__optional__|description of merchant +
**Example** : `"This is merchant a"`|string
|**email** +
__required__|email +
**Example** : `"<EMAIL>"`|string
|**entityAttributeValues** +
__optional__|customized attributes, it is null when do search operation|< string, string > map
|**id** +
__required__|Id of merchant +
**Example** : `378805`|integer (int64)
|**merchantCategory** +
__optional__|merchant category list, it is null when do search operation|< <<_merchantcategorydto,MerchantCategoryDTO>> > array
|**name** +
__required__|name of merchant +
**Example** : `"Merchant A"`|string
|**phone** +
__optional__|phone +
**Example** : `"89894545"`|string
|**postcode** +
__optional__|POST code +
**Example** : `"215000"`|string
|**province** +
__optional__|province +
**Example** : `"CN_HLJ"`|string
|**reseller** +
__optional__||<<_reseller_information,Reseller Information>>
|**status** +
__required__|status of merchant +
**Example** : `"A"`|string
|===



