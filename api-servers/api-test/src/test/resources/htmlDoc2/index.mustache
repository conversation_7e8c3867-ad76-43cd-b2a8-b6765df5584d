<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>{{{appName}}}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta charset="UTF-8" />
    <style>
        li.nav-header {
            text-transform: none;
        }
    </style>
  {{>js_jquery}}
  {{>js_prettify}}
  {{>js_bootstrap}}
  {{>marked}}
  <script>
    $( document ).ready(function() {
      marked.setOptions({
        renderer: new marked.Renderer(),
        gfm: true,
        tables: true,
        breaks: false,
        pedantic: false,
        sanitize: false,
        smartLists: true,
        smartypants: false
      });

      var textFile = null;

      /// Function to be used to download a text json schema
      function makeTextFile(text) {

        var data = new Blob([text], {type: 'text/plain'});

        // If we are replacing a previously generated file we need to
        // manually revoke the object URL to avoid memory leaks.
        if (textFile !== null) {
          window.URL.revokeObjectURL(textFile);
        }

        textFile = window.URL.createObjectURL(data);

        var a = document.createElement("a");
        document.body.appendChild(a);
        a.style = "display: none";
        a.href = textFile;
        a.download = 'schema.txt';
        a.click();

        return textFile;
      };

      /// TODO: Implement resizing for expanding within iframe
      function callResize() {
        window.parent.postMessage('resize', "*");
      }

      function processMarked() {
        $(".marked").each(function() {
          $(this).html(marked($(this).html()));
        });
      }

      // Bootstrap Scrollspy
      $(this).scrollspy({ target: '#scrollingNav', offset: 18 });

      // Content-Scroll on Navigation click.
      $('.sidenav').find('a').on('click', function(e) {
          e.preventDefault();
          var id = $(this).attr('href');
          if ($(id).length > 0)
              $('html,body').animate({ scrollTop: parseInt($(id).offset().top) }, 400);
          window.location.hash = $(this).attr('href');
      });

      // Quickjump on Pageload to hash position.
      if(window.location.hash) {
          var id = window.location.hash;
          if ($(id).length > 0)
              $('html,body').animate({ scrollTop: parseInt($(id).offset().top) }, 0);
      }


      function initDynamic() {
        // tabs
        $('.nav-tabs-examples a').click(function (e) {
            e.preventDefault();
            $(this).tab('show');
        });


        $('.nav-tabs-examples').find('a:first').tab('show');

        // call scrollspy refresh method
        $(window).scrollspy('refresh');
      }

      initDynamic();

      // Pre- / Code-Format
      prettyPrint();

      //Convert elements with "marked" class to markdown
      processMarked();
    });
  </script>
  <style type="text/css">
  {{>css_bootstrap}}
  {{>css_prettify}}
  {{>styles}}
  </style>
</head>
<body>
  <script>
    // Script section to load models into a JS Var
    var defs = {}
        {{#models}}
        {{#model}}
            {{#isComposedModel}}
            defs["{{name}}"] = {};
            {{/isComposedModel}}
            {{^isComposedModel}}
            defs["{{name}}"] = {{{modelJson}}};
            {{/isComposedModel}}
        {{/model}}
    {{/models}}
  </script>

  <div class="container-fluid">
    <div class="row-fluid">
      <div id="sidenav" class="span2">
        <nav id="scrollingNav">
          <ul class="sidenav nav nav-list">
            <!-- Logo Area -->
              <!--<div style="width: 80%; background-color: #4c8eca; color: white; padding: 20px; text-align: center; margin-bottom: 20px; ">

              API Docs 2

              </div>

            <li class="nav-fixed nav-header active" data-group="_"><a href="#api-_">API Summary</a></li>
            -->
              <!-- 预览部分左侧菜单 start1 -->
              {{>overview_left}}
              <!-- 预览部分左侧菜单 end1 -->

            {{#apiInfo}}
              {{#apis}}
                {{#operations}}
                  <li class="nav-header" data-group="{{baseName}}"><a href="#api-{{baseName}}">API Methods - {{baseName}}</a></li>
                  {{#operation}}
                    <li data-group="{{baseName}}" data-name="{{nickname}}" class="">
                      <a href="#api-{{baseName}}-{{nickname}}">{{nickname}}</a>
                    </li>
                  {{/operation}}
                {{/operations}}
              {{/apis}}
            {{/apiInfo}}

              <!-- 附录部分左侧菜单 start-->
              {{>appendix_left}}
              <!-- 附录部分左侧菜单 end-->
          </ul>
        </nav>
      </div>
      <div id="content">
        <div id="project">
          <div class="pull-left">
            <h1>{{{appName}}}</h1>
          </div>
          <div class="clearfix"></div>
        </div>
        <div id="header">
          <div id="api-_">
            <h2 id="welcome-to-apidoc">API and SDK Documentation</h2>
          </div>
            <!-- 预览部分内容 start -->
            {{>overview_content}}
            <!-- 预览部分内容 end -->
        </div>
        <div id="sections">
          {{#apiInfo}}
            {{#apis}}
              {{#operations}}
                <section id="api-{{baseName}}">
                  <h1>{{baseName}}</h1>
                  {{#operation}}
                  {{#contents}}
                  {{#@first}}
                    <div id="api-{{baseName}}-{{nickname}}">
                      <article id="api-{{baseName}}-{{nickname}}-0" data-group="User" data-name="{{nickname}}" data-version="0">
                        <div class="pull-left">
                          <h1>{{nickname}}</h1>
                          <p>{{summary}}</p>
                        </div>
                        <div class="pull-right"></div>
                        <div class="clearfix"></div>
                        <p></p>
                        <p class="marked">{{notes}}</p>
                        <p></p>
                        <br />
                        <pre class="prettyprint language-html prettyprinted" data-type="{{httpMethod}}"><code><span class="pln">{{path}}</span></code></pre>
                        <p>
                          <h3>Usage and SDK Samples</h3>
                        </p>
                        <ul class="nav nav-tabs nav-tabs-examples">
                          <li class="active"><a href="#examples-{{baseName}}-{{nickname}}-0-curl">Curl</a></li>
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-java">Java</a></li>
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-android">Android</a></li>
                          <!--<li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-groovy">Groovy</a></li>-->
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-objc">Obj-C</a></li>
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-javascript">JavaScript</a></li>
                          <!--<li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-angular">Angular</a></li>-->
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-csharp">C#</a></li>
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-php">PHP</a></li>
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-perl">Perl</a></li>
                          <li class=""><a href="#examples-{{baseName}}-{{nickname}}-0-python">Python</a></li>
                        </ul>

                        <div class="tab-content">
                          <div class="tab-pane active" id="examples-{{baseName}}-{{nickname}}-0-curl">
                           <pre class="prettyprint"><code class="language-bsh">curl -X {{vendorExtensions.x-codegen-httpMethodUpperCase}}{{#authMethods}}\
{{#isApiKey}}{{#isKeyInHeader}}-H "{{keyParamName}}: [[apiKey]]"{{/isKeyInHeader}}{{/isApiKey}}{{^isBasicBearer}}{{#isBasic}} -H "Authorization: Basic [[basicHash]]"{{/isBasic}}{{/isBasicBearer}}{{#isBearer}} -H "Authorization: Bearer [[accessToken]]"{{/isBearer}}{{/authMethods}}{{#hasProduces}}\
-H "Accept: {{#produces}}{{{mediaType}}}{{#hasMore}},{{/hasMore}}{{/produces}}"{{/hasProduces}}{{#hasConsumes}}\
-H "Content-Type: {{#consumes}}{{{mediaType}}}{{#hasMore}},{{/hasMore}}{{/consumes}}"{{/hasConsumes}}\
"{{basePath}}{{path}}{{#hasQueryParams}}?{{#queryParams}}{{^@first}}&{{/@first}}{{baseName}}={{vendorExtensions.x-eg}}{{/queryParams}}{{/hasQueryParams}}"</code></pre>
                          </div>
                          <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-java">
                            <pre class="prettyprint"><code class="language-java">{{>sample_java}}</code></pre>
                          </div>

                          <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-android">
                            <pre class="prettyprint"><code class="language-java">{{>sample_android}}</code></pre>
                          </div>
  <!--
  <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-groovy">
  <pre class="prettyprint language-json prettyprinted" data-type="json"><code>Coming Soon!</code></pre>
  </div> -->
                            <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-objc">
                              <pre class="prettyprint"><code class="language-cpp">{{>sample_objc}}</code></pre>
                            </div>

                            <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-javascript">
                              <pre class="prettyprint"><code class="language-js">{{>sample_js}}</code></pre>
                            </div>

                            <!--<div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-angular">
              <pre class="prettyprint language-json prettyprinted" data-type="json"><code>Coming Soon!</code></pre>
            </div>-->
                            <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-csharp">
                              <pre class="prettyprint"><code class="language-cs">{{>sample_csharp}}</code></pre>
                            </div>

                            <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-php">
                              <pre class="prettyprint"><code class="language-php">{{>sample_php}}</code></pre>
                            </div>

                            <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-perl">
                              <pre class="prettyprint"><code class="language-perl">{{>sample_perl}}</code></pre>
                            </div>

                            <div class="tab-pane" id="examples-{{baseName}}-{{nickname}}-0-python">
                              <pre class="prettyprint"><code class="language-python">{{>sample_python}}</code></pre>
                            </div>
                          </div>

                          <h2>Parameters</h2>

                          {{#hasPathParams}}
                            <div class="methodsubtabletitle">Path parameters</div>
                            <table id="methodsubtable">
                                <tr>
                                  <th width="150px">Name</th>
                                  <th>Description</th>
                                </tr>
                                {{#pathParams}}
                                  {{>param}}
                                {{/pathParams}}
                            </table>
                          {{/hasPathParams}}

                          {{#hasHeaderParams}}
                            <div class="methodsubtabletitle">Header parameters</div>
                            <table id="methodsubtable">
                              <tr>
                                <th width="150px">Name</th>
                                <th>Description</th>
                              </tr>
                              {{#headerParams}}
                                  {{>param}}
                              {{/headerParams}}
                            </table>
                          {{/hasHeaderParams}}

                          {{#hasBodyParam}}
                            <div class="methodsubtabletitle">Body parameters</div>
                            <table id="methodsubtable">
                              <tr>
                                <th width="150px">Name</th>
                                <th>Description</th>
                              </tr>
                              {{#bodyParams}}
                                {{>paramB}}
                              {{/bodyParams}}
                            </table>
                          {{/hasBodyParam}}

                          {{#hasFormParams}}
                            <div class="methodsubtabletitle">Form parameters</div>
                            <table id="methodsubtable">
                              <tr>
                                <th width="150px">Name</th>
                                <th>Description</th>
                              </tr>
                              {{#formParams}}
                                  {{>param}}
                              {{/formParams}}
                            </table>
                          {{/hasFormParams}}

                          {{#hasQueryParams}}
                            <div class="methodsubtabletitle">Query parameters</div>
                            <table id="methodsubtable">
                              <tr>
                                <th width="150px">Name</th>
                                <th>Description</th>
                              </tr>
                              {{#queryParams}}
                                {{>param}}
                              {{/queryParams}}
                            </table>
                          {{/hasQueryParams}}

                          <h2>Responses</h2>
                          {{#responses}}
                            <h3> Status: {{code}} - {{message}} </h3>

                            <ul class="nav nav-tabs nav-tabs-examples" >
                              {{#schema}}
                                <li class="active">
                                  <a data-toggle="tab" href="#responses-{{nickname}}-{{code}}-schema">Schema</a>
                                </li>

                                {{#examples}}
                                  <li class="">
                                    <a data-toggle="tab" href="#responses-{{nickname}}-{{code}}-example-{{@index}}">Response Example</a>
                                  </li>
                                {{/examples}}
                              {{/schema}}
                              {{#hasHeaders}}
                                <li class="">
                                  <a data-toggle="tab" href="#responses-{{nickname}}-{{code}}-headers">Headers</a>
                                </li>
                              {{/hasHeaders}}
                            </ul>

                            <div class="tab-content" style='margin-bottom: 10px;'>
                              {{#schema}}
                                <div class="tab-pane active" id="responses-{{nickname}}-{{code}}-schema">
                                  <div id='responses-{{nickname}}-{{code}}-schema-{{code}}' style="padding: 30px; border-left: 1px solid #eee; border-right: 1px solid #eee; border-bottom: 1px solid #eee;">
                                    <script>
                                      $(document).ready(function() {
                                        var schemaWrapper = {{{../jsonSchema}}};
                                        var schema = schemaWrapper.content["{{extensions.x-content-type}}"].schema;
                                        if (schema.$ref != null) {
                                          schema = defsParser.$refs.get(schema.$ref);
                                        } else {
                                          schemaWrapper.components = {};
                                          schemaWrapper.components.schemas = Object.assign({}, defs);
                                          $RefParser.dereference(schemaWrapper).catch(function(err) {
                                            console.log(err);
                                          });
                                        }

                                        //console.log(JSON.stringify(schema));
                                        var view = new JSONSchemaView(schema, 3);
                                        $('#responses-{{nickname}}-{{code}}-schema-data').val(stringify(schema));
                                        var result = $('#responses-{{nickname}}-{{code}}-schema-{{code}}');
                                        result.empty();
                                        result.append(view.render());
                                      });
                                    </script>
                                  </div>
                                  <input id='responses-{{nickname}}-{{code}}-schema-data' type='hidden' value=''></input>
                                </div>
                                {{#examples}}
                                  <div class="tab-pane" id="responses-{{nickname}}-{{code}}-example-{{@index}}">
                                      <pre class="prettyprint"><code class="json">{{example}}</code></pre>
                                </div>
                                {{/examples}}
                              {{/schema}}
                              {{#hasHeaders}}
                                <div class="tab-pane" id="responses-{{nickname}}-{{code}}-headers">
                                    <table>
                                        <tr>
                                            <th width="150px">Name</th>
                                            <th width="100px">Type</th>
                                            <th width="100px">Format</th>
                                            <th>Description</th>
                                        </tr>
                                        {{#headers}}
                                        <tr>
                                            <td>{{#name}}{{name}}{{/name}}</td>
                                            <td>{{#datatype}}{{datatype}}{{/datatype}}</td>
                                            <td>{{#dataFormat}}{{dataFormat}}{{/dataFormat}}</td>
                                            <td>{{#description}}{{description}}{{/description}}</td>
                                        </tr>
                                        {{/headers}}
                                    </table>
                                </div>
                              {{/hasHeaders}}
                            </div>

                          {{/responses}}
                        </article>
                      </div>
                      <hr>
                    {{/@first}}
                    {{/contents}}
                    {{/operation}}
                  </section>
                {{/operations}}
              {{/apis}}
            {{/apiInfo}}
            <!-- 附录内容部分 start -->
            {{>appendix_content}}
            <!-- 附录内容部分 end -->
          </div>
 <!--         <div id="footer">
            <div id="api-_footer">
              <p>Suggestions, contact, support and error reporting;
                {{#infoUrl}}
                  <div class="app-desc">Information URL: <a href="{{{infoUrl}}}">{{{infoUrl}}}</a></div>
                {{/infoUrl}}
                {{#infoEmail}}
                  <div class="app-desc">Contact Info: <a href="{{{infoEmail}}}">{{{infoEmail}}}</a></div>
                {{/infoEmail}}
              </p>
              {{#licenseInfo}}
                <div class="license-info">{{{licenseInfo}}}</div>
              {{/licenseInfo}}
              {{#licenseUrl}}
                <div class="license-url">{{{licenseUrl}}}</div>
              {{/licenseUrl}}
            </div>
          </div>-->
{{^hideGenerationTimestamp}}          <div id="generator">
            <div class="content">
              Generated {{generatedDate}}
            </div>
          </div>
{{/hideGenerationTimestamp}}      </div>
    </div>
  </div>
  {{>js_jsonformatter}}
  {{>js_jsonschemaview}}
  {{>js_json_schema_ref_parser}}
  {{>js_json_stringify_safe}}
  {{>js_webfontloader}}
  <script>
    var schemaWrapper = {};
    schemaWrapper.components = {};
    schemaWrapper.components.schemas = Object.assign({}, defs);
    defsParser = new $RefParser();
    defsParser.dereference(schemaWrapper).catch(function(err) {
      console.log(err);
    });
  </script>
  <script>
  $(document).ready(function () {
    $('.nav-tabs-examples').find('a:first').tab('show');
    $(this).scrollspy({ target: '#scrollingNav', offset: 18 });
  });
  </script>
</body>
</html>
