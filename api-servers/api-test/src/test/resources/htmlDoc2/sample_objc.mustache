{{#hasAuthMethods}}
{{classPrefix}}Configuration *apiConfig = [{{classPrefix}}Configuration sharedConfig];
{{#authMethods}}{{#is this 'basic'}}// Configure HTTP basic authorization (authentication scheme: {{{name}}})
[apiConfig setUsername:@"YOUR_USERNAME"];
[apiConfig setPassword:@"YOUR_PASSWORD"];
{{/is}}{{#is this 'api-key'}}
// Configure API key authorization: (authentication scheme: {{{name}}})
[apiConfig setApiKey:@"YOUR_API_KEY" forApiKeyIdentifier:@"{{{keyParamName}}}"];
// Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
//[apiConfig setApiKeyPrefix:@"Bearer" forApiKeyIdentifier:@"{{{keyParamName}}}"];
{{/is}}{{#is this 'oauth'}}
// Configure OAuth2 access token for authorization: (authentication scheme: {{{name}}})
[apiConfig setAccessToken:@"YOUR_ACCESS_TOKEN"];
{{/is}}{{/authMethods}}
{{/hasAuthMethods}}
{{#parameters}}{{{dataType}}} *{{paramName}} = {{{example}}}; // {{{unescapedDescription}}}{{^required}} (optional){{/required}}{{#defaultValue}} (default to {{{.}}}){{/defaultValue}}
{{/parameters}}

{{classname}} *apiInstance = [[{{classname}} alloc] init];

{{#summary}}// {{{.}}}
{{/summary}}[apiInstance {{#vendorExtensions.x-objc-operationId}}{{vendorExtensions.x-objc-operationId}}{{/vendorExtensions.x-objc-operationId}}{{^vendorExtensions.x-objc-operationId}}{{nickname}}{{#hasParams}}With{{vendorExtensions.firstParamAltName}}{{/hasParams}}{{^hasParams}}WithCompletionHandler: {{/hasParams}}{{/vendorExtensions.x-objc-operationId}}{{#parameters}}{{#secondaryParam}}
    {{paramName}}{{/secondaryParam}}:{{paramName}}{{/parameters}}
              {{#hasParams}}completionHandler: {{/hasParams}}^({{#returnBaseType}}{{{returnType}}} output, {{/returnBaseType}}NSError* error) {
{{#returnType}}
                            if (output) {
                                NSLog(@"%@", output);
                            }
{{/returnType}}
                            if (error) {
                                NSLog(@"Error: %@", error);
                            }
                        }];
