DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'error', '/error', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10001', 'error_3', '/error', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10002', 'error_2', '/error', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10003', 'error_5', '/error', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10004', 'error_6', '/error', 'OPTIONS', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10005', 'error_1', '/error', 'HEAD', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10006', 'error_4', '/error', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10007', 'sendMessageToTerminal', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10008', 'sendMessageByTag', '/v1/3rd/cloudmsg/bytag', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10009', 'sendMessageToSingleTerminal', '/v1/3rd/cloudmsg/single', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10010', 'getMessageArrivalRate', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10011', 'submitApkInfo', '/v1/3rd/coverapp/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10012', 'categoryList', '/v1/3rd/coverapp/app/categoryList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10013', 'searchApps_4', '/v1/3rd/coverapp/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'findFactoryModelList_2', '/v1/3rd/coverapp/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10015', 'validateAppKey', '/v1/3rd/coverapp/validate/appKey', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'uploadApk', '/v1/3rd/developer/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'getApkById', '/v1/3rd/developer/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'editApk', '/v1/3rd/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'deleteApk_4', '/v1/3rd/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'offlineApk_3', '/v1/3rd/developer/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'submitApk_1', '/v1/3rd/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'getApp', '/v1/3rd/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'createApp_1', '/v1/3rd/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'deleteApp_4', '/v1/3rd/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10025', 'createApk', '/v1/3rd/developer/apps/{appId}/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'updateAppKeySecret', '/v1/3rd/developer/apps/{appId}/key-secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'getCodeByType', '/v1/3rd/developer/codes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'getApkVersionListByAppId', '/v1/3rd/developer/{appId}/apks/version-list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'rkiCallback', '/v1/3rd/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10030', 'findApkParameters', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'createApkTemplate', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'getApkParameterById', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'updateApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'deleteApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'searchApps_3', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'getAppCost', '/v1/3rdsys/apps/app-cost', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'searchEntityAttributes', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'createEntityAttribute_1', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'getEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'updateEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10041', 'deleteEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10042', 'updateEntityAttributeLabel_1', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10043', 'findEmmApps', '/v1/3rdsys/emm/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10044', 'createEmmApp_1', '/v1/3rdsys/emm/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10045', 'findEmmAppSubscriptionPage_1', '/v1/3rdsys/emm/apps/subscription', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10046', 'getEmmAppDetail_1', '/v1/3rdsys/emm/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10047', 'removeEmmApp_1', '/v1/3rdsys/emm/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10048', 'findAvailableTestTrackVersionList', '/v1/3rdsys/emm/apps/{appId}/available/test/versions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10049', 'findEmmAppPermissionList', '/v1/3rdsys/emm/apps/{appId}/permissions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10050', 'subscribeEmmApp', '/v1/3rdsys/emm/apps/{appId}/subscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10051', 'unSubscribeEmmApp', '/v1/3rdsys/emm/apps/{appId}/unsubscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10052', 'getEmmDeviceDashboardDetail', '/v1/3rdsys/emm/device/detail/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10053', 'findEmmDeviceInstalledAppPage_1', '/v1/3rdsys/emm/device/detail/{deviceId}/installed-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10054', 'getEmmDeviceDashboardMonitor_1', '/v1/3rdsys/emm/device/detail/{deviceId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10055', 'findEmmDevices', '/v1/3rdsys/emm/devices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10056', 'batchDeleteEmmDevices_1', '/v1/3rdsys/emm/devices/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10057', 'batchMoveEmmDevices', '/v1/3rdsys/emm/devices/batch/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10058', 'createRegisterQRCode_1', '/v1/3rdsys/emm/devices/register-qrcode', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10059', 'submitEmmZteQuickUploadRecord_1', '/v1/3rdsys/emm/devices/zte/quick-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10060', 'getEmmDevice_1', '/v1/3rdsys/emm/devices/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10061', 'updateEmmDevice_1', '/v1/3rdsys/emm/devices/{deviceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10062', 'deleteEmmDevice_1', '/v1/3rdsys/emm/devices/{deviceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10063', 'lockEmmDeviceScreen', '/v1/3rdsys/emm/devices/{deviceId}/lockscreen', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10064', 'rebootEmmDevice', '/v1/3rdsys/emm/devices/{deviceId}/reboot', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10065', 'resetEmmDevicePassword', '/v1/3rdsys/emm/devices/{deviceId}/resetpw', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10066', 'startEmmDeviceLostMode', '/v1/3rdsys/emm/devices/{deviceId}/startlost', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10067', 'stopEmmDeviceLostMode', '/v1/3rdsys/emm/devices/{deviceId}/stoplost', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10068', 'getMerchantEmmPolicy_1', '/v1/3rdsys/emm/policy/merchant', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10069', 'createMerchantEmmPolicy_1', '/v1/3rdsys/emm/policy/merchant', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10070', 'getResellerEmmPolicy_1', '/v1/3rdsys/emm/policy/reseller', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10071', 'createResellerEmmPolicy_1', '/v1/3rdsys/emm/policy/reseller', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10072', 'verifyEstateBySerialNo', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10073', 'findFactoryModelList_1', '/v1/3rdsys/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10074', 'findDataFromInsight', '/v1/3rdsys/goInsight/data/app-biz', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10075', 'findDataFromGoInsight', '/v1/3rdsys/goInsight/data/app-biz/{queryCode}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10076', 'deductMarketLastMonthActiveFee', '/v1/3rdsys/internal/airlink/deduct/lastmonth/active-fee', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10077', 'deductMarketLastMonthTrafficFee', '/v1/3rdsys/internal/airlink/deduct/lastmonth/overage-traffic-fee', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10078', 'deductMarketCurrentMonthFee', '/v1/3rdsys/internal/airlink/deduct/month/package-fee', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10079', 'createTables', '/v1/3rdsys/internal/audit-log/create-tables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10080', 'migrationAuditLog', '/v1/3rdsys/internal/audit-log/migration/audit-log', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10081', 'migrationAuditTrail', '/v1/3rdsys/internal/audit-log/migration/audit-trail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10082', 'refreshNodes', '/v1/3rdsys/internal/audit-log/refresh-nodes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10083', 'syncBillingListToZolonBillingCenter', '/v1/3rdsys/internal/billing', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10084', 'abandonBillings', '/v1/3rdsys/internal/billing/abandon', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10085', 'findAirLinkOrderSummary', '/v1/3rdsys/internal/billing/air-link/order/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10086', 'updateAirLinkOrderSummary', '/v1/3rdsys/internal/billing/air-link/order/summary/{summaryId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10087', 'updateAirLinkOrderStatus', '/v1/3rdsys/internal/billing/air-link/order/{orderNumber}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10088', 'cancelWriteOffMarketBillingSummary', '/v1/3rdsys/internal/billing/cancel/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10089', 'syncSolutionDeveloperList', '/v1/3rdsys/internal/billing/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10090', 'syncFineTerminalListToZolonBillingCenter', '/v1/3rdsys/internal/billing/fine/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10091', 'searchMarkets_3', '/v1/3rdsys/internal/billing/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10092', 'writeOffMarketBilling', '/v1/3rdsys/internal/billing/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10093', 'updateBillingServiceSyncedStatus', '/v1/3rdsys/internal/billing/{billingSummaryId}/callback', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10094', 'updateBillingServicePrice', '/v1/3rdsys/internal/billing/{billingSummaryId}/price/sync', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10095', 'lockSet', '/v1/3rdsys/internal/cache/set', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10096', 'clearCache', '/v1/3rdsys/internal/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10097', 'clearDeletedTerminals', '/v1/3rdsys/internal/clearDeletedTerminals', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10098', 'clearExpiredPendingTerminalActions', '/v1/3rdsys/internal/clearExpiredPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10099', 'clearGroupPendingTerminalActions', '/v1/3rdsys/internal/clearGroupPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10100', 'clearTerminalPendingTerminalActions', '/v1/3rdsys/internal/clearTerminalPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10101', 'clearDeletedWebHookMessageHistory', '/v1/3rdsys/internal/clearWebHookMessages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10102', 'delLock', '/v1/3rdsys/internal/delLock', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10103', 'getAllDisabledRequests', '/v1/3rdsys/internal/disabled-request', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10104', 'addDisabledRequest', '/v1/3rdsys/internal/disabled-request', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10105', 'delDisabledRequest', '/v1/3rdsys/internal/disabled-request', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10106', 'clearDisabledRequest', '/v1/3rdsys/internal/disabled-request/all', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10107', 'getEmmAppInfo', '/v1/3rdsys/internal/emm/app/{enterpriseId}/{packageName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10108', 'migrateDevice', '/v1/3rdsys/internal/emm/device/migration', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10109', 'finAllDeviceList', '/v1/3rdsys/internal/emm/device/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10110', 'getDeviceInfo', '/v1/3rdsys/internal/emm/device/{enterpriseId}/{originalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10111', 'deleteDeviceByDeviceName', '/v1/3rdsys/internal/emm/device/{enterpriseId}/{originalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10112', 'pageQueryEnrollmentToken', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10113', 'getEnrollmentTokenByName', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/{tokenId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10114', 'deleteEnrollmentToken', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/{tokenId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10115', 'updateEmmEnterpriseName', '/v1/3rdsys/internal/emm/enterprise/name', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10116', 'getEnterpriseByName', '/v1/3rdsys/internal/emm/enterprise/{enterpriseId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10117', 'updateEnterprise_1', '/v1/3rdsys/internal/emm/enterprise/{enterpriseId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10118', 'pageQueryEnterprise', '/v1/3rdsys/internal/emm/enterprise/{projectId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10119', 'findAllPolicyList', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10120', 'getPolicy', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/{policyName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10121', 'deletePolicyByPolicyName', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/{policyName}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10122', 'generateApkPatchMd5', '/v1/3rdsys/internal/generateApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10123', 'generateClientApkPatchMd5', '/v1/3rdsys/internal/generateClientApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10124', 'generateTerminalEnrollFiles', '/v1/3rdsys/internal/generateTerminalEnrollFiles', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10125', 'getCache', '/v1/3rdsys/internal/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10126', 'getLock', '/v1/3rdsys/internal/getLock', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10127', 'sendDictionaryDataToGoInsight', '/v1/3rdsys/internal/insight/sync/dictionary-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10128', 'sendMarketDataToGoInsight', '/v1/3rdsys/internal/insight/sync/market-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10129', 'loadCacheByCacheName', '/v1/3rdsys/internal/load/cache', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10130', 'lock', '/v1/3rdsys/internal/lock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10131', 'findMarketApiBlackList', '/v1/3rdsys/internal/marketBlackApi', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10132', 'delMarketBlackApi', '/v1/3rdsys/internal/marketBlackApi/{marketBlackApiId}/market/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10133', 'createMarketBlackApi', '/v1/3rdsys/internal/marketBlackApi/{marketId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10134', 'getAuthCode', '/v1/3rdsys/internal/maxsearch/auth-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10135', 'resumeBinlogConsumer', '/v1/3rdsys/internal/maxsearch/binlog-consumer/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10136', 'checkFullSyncProcess_1', '/v1/3rdsys/internal/maxsearch/full-sync', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10137', 'doFullSync', '/v1/3rdsys/internal/maxsearch/full-sync', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10138', 'doFullSyncAll', '/v1/3rdsys/internal/maxsearch/full-sync/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10139', 'getMaxSearchStats', '/v1/3rdsys/internal/maxsearch/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10140', 'checkFullSyncSummaryInfo', '/v1/3rdsys/internal/maxsearch/stats/full-sync', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10141', 'checkFullSyncProcess', '/v1/3rdsys/internal/maxsearch/stats/full-sync/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10142', 'configSyncCache', '/v1/3rdsys/internal/maxsearch/sync-cache-config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10143', 'migrationGroupFilteredAction', '/v1/3rdsys/internal/migrationGroupFilteredAction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10144', 'refreshGenResellerCertificate', '/v1/3rdsys/internal/refresh/reseller/certificate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10145', 'refreshApkParamTemplate', '/v1/3rdsys/internal/refreshApkParamTemplate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10146', 'refreshGroupActionCount', '/v1/3rdsys/internal/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10147', 'refreshPushTaskDownloadTime', '/v1/3rdsys/internal/refreshPushTaskDownloadTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10148', 'refreshResellerInstalledApks', '/v1/3rdsys/internal/refreshResellerInstalledApks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10149', 'refreshResellerPushedParamApk', '/v1/3rdsys/internal/refreshResellerPushedParamApk', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10150', 'refreshTerminalLastAccessTime', '/v1/3rdsys/internal/refreshTerminalLastAccessTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10151', 'refreshTerminalLastApkParam', '/v1/3rdsys/internal/refreshTerminalLastApkParam', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10152', 'refreshTerminalOnlineStatus', '/v1/3rdsys/internal/refreshTerminalOnlineStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10153', 'refreshTerminalStock', '/v1/3rdsys/internal/refreshTerminalStock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10154', 'refreshUserMarket', '/v1/3rdsys/internal/refreshUserMarket', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10155', 'repairActivityStatus', '/v1/3rdsys/internal/repairActivityStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10156', 'repairGroupPushTask', '/v1/3rdsys/internal/repairGroupPushTask', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10157', 'repairMerchantMigrationStatus', '/v1/3rdsys/internal/repairMerchantMigrationStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10158', 'repairReportExecutionContextStatus', '/v1/3rdsys/internal/repairReportExecutionContextStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10159', 'getSystemPropertyLog', '/v1/3rdsys/internal/system/property/log', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10160', 'findSystemProperties', '/v1/3rdsys/internal/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10161', 'createSystemProperty_1', '/v1/3rdsys/internal/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10162', 'deleteSystemProperty_1', '/v1/3rdsys/internal/systemProperty', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10163', 'resetSystemProperty', '/v1/3rdsys/internal/systemProperty/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10164', 'getTerminal_4', '/v1/3rdsys/internal/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10165', 'sendTerminalCommand', '/v1/3rdsys/internal/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10166', 'sendTerminalMessage', '/v1/3rdsys/internal/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10167', 'changeTerminalModel_1', '/v1/3rdsys/internal/terminal/model', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10168', 'getTerminalPushHistory', '/v1/3rdsys/internal/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10169', 'resetTerminal', '/v1/3rdsys/internal/terminal/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10170', 'findMerchantVariablePage_1', '/v1/3rdsys/merchant/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', 'createMerchantVariable_1', '/v1/3rdsys/merchant/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', 'batchDeleteMerchantVariables_1', '/v1/3rdsys/merchant/variables/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', 'updateMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', 'deleteMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', 'getMerchantCategories', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', 'createMerchantCategory_1', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', 'batchCreateMerchantCategories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', 'updateMerchantCategory_1', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10179', 'deleteCategory', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', 'searchMerchant', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', 'createMerchant_1', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', 'getMerchant_2', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', 'updateMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10184', 'deleteMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', 'activeMerchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', 'disableMerchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', 'replaceMerchantEmail_1', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', 'findParameterPushHistory', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', 'searchReseller_1', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', 'createReseller_1', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', 'getReseller_2', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', 'updateReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', 'deleteReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', 'activeReseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', 'disableReseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', 'replaceResellerEmail_1', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', 'searchResellerRkiKey', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', 'getTerminalBySN', '/v1/3rdsys/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', 'updateTerminalBySN', '/v1/3rdsys/terminal', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', 'deleteTerminalBySN', '/v1/3rdsys/terminal', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', 'activateTerminalBySN', '/v1/3rdsys/terminal/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', 'collectTerminalLogBySN', '/v1/3rdsys/terminal/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', 'getTerminalConfigBySN', '/v1/3rdsys/terminal/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', 'updateTerminalConfigBySN', '/v1/3rdsys/terminal/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', 'copyTerminalBySN', '/v1/3rdsys/terminal/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10206', 'disableTerminalBySN', '/v1/3rdsys/terminal/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10207', 'findTerminalGeoFenceWhiteList', '/v1/3rdsys/terminal/geofence/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10208', 'createTerminalGeoFenceWhiteList_1', '/v1/3rdsys/terminal/geofence/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', 'deleteTerminalGeoFenceWhiteList_1', '/v1/3rdsys/terminal/geofence/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', 'createTerminalsGroupBySN', '/v1/3rdsys/terminal/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', 'searchTerminalLogPageBySN', '/v1/3rdsys/terminal/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', 'getTerminalLogDownloadTaskBySN', '/v1/3rdsys/terminal/logs/{terminalLogId}/download-task', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10213', 'changeModelBySN', '/v1/3rdsys/terminal/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', 'moveTerminalBySN', '/v1/3rdsys/terminal/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10215', 'pushTerminalActionBySN', '/v1/3rdsys/terminal/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10216', 'getTerminalPedBySN', '/v1/3rdsys/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10217', 'pushTerminalMessageBySN', '/v1/3rdsys/terminal/push/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', 'getTerminalSystemUsageBySN', '/v1/3rdsys/terminal/system/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10219', 'searchTerminalApkPage', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10220', 'createTerminalApk', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10221', 'suspendTerminalApk_1', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10222', 'uninstallTerminalApk', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10223', 'getTerminalApk_1', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10224', 'deleteTerminalApk_2', '/v1/3rdsys/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10225', 'searchTerminalFmPage', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10226', 'createTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10227', 'suspendTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10228', 'deleteTerminalFm', '/v1/3rdsys/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10229', 'getTerminalFm', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10230', 'searchTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10231', 'createTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10232', 'getTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10233', 'deleteTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10234', 'suspendTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10235', 'searchTerminalGroupRkiPage', '/v1/3rdsys/terminalGroupRki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10236', 'createGroupRki_1', '/v1/3rdsys/terminalGroupRki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10237', 'getGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10238', 'suspendGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10239', 'searchGroups_2', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10240', 'createGroup_1', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10241', 'searchTerminal_2', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10242', 'getGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10243', 'updateGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10244', 'deleteGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10245', 'activeGroup', '/v1/3rdsys/terminalGroups/{groupId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10246', 'disableGroup', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10247', 'searchGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10248', 'removeGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10249', 'createGroupTerminals_2', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10250', 'searchTerminalRkiPage', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', 'createTerminalRki_1', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', 'suspendTerminalRki_1', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', 'getTerminalRki_1', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10254', 'deleteTerminalRki', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', 'findTerminalVariableList', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10256', 'createTerminalVariable_1', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', 'batchDeleteTerminalVariables_1', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', 'updateTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', 'deleteTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', 'findTerminals', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', 'createTerminal_1', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', 'activateTerminalInParam', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10263', 'copyTerminal_1', '/v1/3rdsys/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10264', 'createTerminalsGroup', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10265', 'getTerminalNetwork', '/v1/3rdsys/terminals/network', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10266', 'getTerminal_3', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10267', 'updateTerminal_2', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10268', 'deleteTerminal_4', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10269', 'activateTerminalInPath', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10270', 'collectTerminalLog', '/v1/3rdsys/terminals/{terminalId}/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', 'getTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', 'updateTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', 'disableTerminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', 'searchTerminalLogPage', '/v1/3rdsys/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', 'getTerminalLogDownloadTask', '/v1/3rdsys/terminals/{terminalId}/logs/{terminalLogId}/download-task', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', 'changeModelById', '/v1/3rdsys/terminals/{terminalId}/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10277', 'moveTerminal_1', '/v1/3rdsys/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10278', 'pushTerminalAction', '/v1/3rdsys/terminals/{terminalId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10279', 'getTerminalPed', '/v1/3rdsys/terminals/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10280', 'pushTerminalMessage', '/v1/3rdsys/terminals/{terminalId}/push/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10281', 'getTerminalSystemUsageById', '/v1/3rdsys/terminals/{terminalId}/system/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10282', 'transfRequest', '/v1/3rdsys/upt/route-request', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10283', 'updateUptrillionSecurityInfo', '/v1/3rdsys/upt/security', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10284', 'getMerchant_1', '/v1/3rdsys/uptrillion/merchant', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10285', 'getReseller_1', '/v1/3rdsys/uptrillion/reseller', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10286', 'getTerminal_2', '/v1/3rdsys/uptrillion/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10287', 'getServiceAgreement', '/v1/account/agreement/service', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10288', 'getSystemAgreement', '/v1/account/agreement/system', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10289', 'getUser_6', '/v1/account/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10290', 'getMarket_6', '/v1/account/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10291', 'resetUserPassword_2', '/v1/account/public/{userId}/reset-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10292', 'deleteAccount', '/v1/account/user', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10293', 'changeUserPwd', '/v1/account/user/change-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10294', 'sendDeleteAccountCode', '/v1/account/user/delete', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10295', 'listUserConfig', '/v1/account/user/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10296', 'updateUserConfig', '/v1/account/user/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10297', 'publishGlobalNotification', '/v1/account/user/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10298', 'listMessages_1', '/v1/account/user/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10299', 'readMessages', '/v1/account/user/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10300', 'deleteMessages', '/v1/account/user/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10301', 'readAllMessage_1', '/v1/account/user/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10302', 'deleteMessage_1', '/v1/account/user/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10303', 'downloadMessageAttachment', '/v1/account/user/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10304', 'viewMessageDetails_2', '/v1/account/user/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10305', 'listTopics', '/v1/account/user/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10306', 'unsubscribeAllTopics', '/v1/account/user/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10307', 'getTopicSubscription_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10308', 'subscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10309', 'unsubscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10310', 'getOTP', '/v1/account/user/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10311', 'activateOTP', '/v1/account/user/otp/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10312', 'disableOTP', '/v1/account/user/otp/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10313', 'bindOTP', '/v1/account/user/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10314', 'resetOtpBackupCode', '/v1/account/user/otp/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10315', 'getUserProfile', '/v1/account/user/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10316', 'updateUser', '/v1/account/user/profile', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10317', 'resetUserEmail', '/v1/account/user/reset-email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10318', 'updateAllowSendUsageData', '/v1/account/user/send-usage', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10319', 'findActivityPage', '/v1/admin/activities', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10320', 'batchDeleteActivities', '/v1/admin/activities/batch', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10321', 'getActivity', '/v1/admin/activities/{activityId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10322', 'deleteActivity', '/v1/admin/activities/{activityId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', 'searchAlarm_2', '/v1/admin/alarm', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', 'getAlarmSetting', '/v1/admin/alarm/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', 'saveAlarmSetting', '/v1/admin/alarm/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', 'searchRoles_2', '/v1/admin/alarm/setting/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', 'searchRoleUsers_1', '/v1/admin/alarm/setting/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', 'lockTerminals', '/v1/admin/alarm/terminals/lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', 'unLockTerminals', '/v1/admin/alarm/terminals/unlock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', 'findAlarmTypeList', '/v1/admin/alarm/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', 'getAlarmWidgets_1', '/v1/admin/alarm/widgets/digital', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', 'createExportAlarmDownloadTasks', '/v1/admin/alarm/widgets/{type}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', 'searchGroupPushTaskApproval', '/v1/admin/approval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', 'approvePushTask', '/v1/admin/approval/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', 'getPushTaskApprovalDetail', '/v1/admin/approval/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', 'rejectPushTask', '/v1/admin/approval/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', 'searchApps_2', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', 'getApkInfo_3', '/v1/admin/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', 'createApkDownloadTask_2', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', 'findSpecificApkMarketPage', '/v1/admin/apps/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', 'specificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', 'deleteSpecificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10343', 'findSpecificApkMarketAllListPage', '/v1/admin/apps/apks/{apkId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10344', 'createApkParamTemplateDownloadTask_2', '/v1/admin/apps/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10345', 'deleteApkParamTemplate_2', '/v1/admin/apps/apks/{apkId}/param-template', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10346', 'findSpecificApkResellerPage_3', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10347', 'specificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10348', 'deleteSpecificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10349', 'findSpecificApkResellerAllListPage_2', '/v1/admin/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', 'reSignApk_4', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', 'findApkSignatureList_4', '/v1/admin/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', 'getPendingApprovalAppCount', '/v1/admin/apps/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', 'searchAppPriceTemplates_1', '/v1/admin/apps/price/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', 'getTopicSubscription_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', 'subscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', 'unsubscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10357', 'getAppInfo_2', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10358', 'deleteApp_3', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10359', 'onlineApp', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10360', 'searchApk_3', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10361', 'deleteApk_3', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10362', 'approveApp_2', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10363', 'downloadApk', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', 'offlineApk_2', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', 'onlineApk_1', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', 'addApkParamTemplate', '/v1/admin/apps/{appId}/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', 'rejectApp_2', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', 'updateApkReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/release-note', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', 'updateApkModel', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', 'updateAppAutoUpdate', '/v1/admin/apps/{appId}/auto-update/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', 'findSpecificAppCostResellerPage', '/v1/admin/apps/{appId}/cost/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10372', 'updateSpecificAppCostReseller', '/v1/admin/apps/{appId}/cost/reseller/specific', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10373', 'specificAppCostReseller', '/v1/admin/apps/{appId}/cost/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10374', 'deleteSpecificAppCostReseller', '/v1/admin/apps/{appId}/cost/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10375', 'updateAppDeveloper', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10376', 'offlineApp', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10377', 'updateAppDownloadAuthentication', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10378', 'saveAppEntityAttributeValueList', '/v1/admin/apps/{appId}/entity-attribute-value', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10379', 'getBizDataFromGoInsight_3', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', 'updateAppInheritLocalParam', '/v1/admin/apps/{appId}/local-param/{inherit}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', 'findSpecificAppMarketPage', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', 'specificMarketApp', '/v1/admin/apps/{appId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', 'deleteSpecificAppMarket', '/v1/admin/apps/{appId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', 'findSpecificAppMarketAllListPage', '/v1/admin/apps/{appId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', 'findSpecificAppMerchantCategoryPage', '/v1/admin/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', 'specificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', 'deleteSpecificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10388', 'findSpecificAppResellerPage_2', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10389', 'specificAppReseller_2', '/v1/admin/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10390', 'deleteSpecificResellerApp_1', '/v1/admin/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10391', 'findSpecificAppResellerAllListPage_2', '/v1/admin/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10392', 'resumeApp', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10393', 'getAppSettingVo_2', '/v1/admin/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10394', 'getAppVasSettingVo_3', '/v1/admin/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10395', 'updateAppVisualScope', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10396', 'searchAuthLog', '/v1/admin/audit-log/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10397', 'exportAuthLog', '/v1/admin/audit-log/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10398', 'searchOperationLog', '/v1/admin/audit-log/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10399', 'exportAuditLog', '/v1/admin/audit-log/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10400', 'getAuditLogParamDetail', '/v1/admin/audit-log/operations/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', 'getExistAuditTypes', '/v1/admin/audit-log/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', 'findClientApp', '/v1/admin/client-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10403', 'findClientApk', '/v1/admin/client-apps-approval/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10404', 'findClientAppFactory', '/v1/admin/client-apps-approval/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10405', 'findFirmwares', '/v1/admin/client-apps-approval/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10406', 'approveClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10407', 'updateClientApkModel', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/model/update', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10408', 'offlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10409', 'onlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10410', 'rejectClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10411', 'getClientApk', '/v1/admin/client-apps-common/client-apks/{clientApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10412', 'createClientApkDownloadTask', '/v1/admin/client-apps-common/client-apks/{clientApkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10413', 'deleteClientApk', '/v1/admin/client-apps-common/{clientAppId}/client-apks/{clientApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10414', 'findClientApkFirmwarePage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10415', 'createClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10416', 'updateClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10417', 'removeClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10418', 'findClientMarketPage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10419', 'getClientMarketSummary', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10420', 'saveClientPublishAmount', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-amount', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10421', 'addGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10422', 'removeGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10423', 'updateClientApkPublishRange', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-range', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10424', 'findSpecificApkResellerPage_2', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10425', 'specificClientApkReseller', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10426', 'deleteSpecificClientApkReseller', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10427', 'findSpecificClientApkResellerAllListPage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10428', 'getClientApp', '/v1/admin/client-apps/{clientAppId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10429', 'findClientApkByAppId', '/v1/admin/client-apps/{clientAppId}/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10430', 'addNewClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10431', 'updateClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10432', 'updateClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10433', 'submitClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10434', 'addFistClientApkFile', '/v1/admin/client-apps/{factoryId}/client-apks/file/first', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10435', 'getIOTPlatformAccessUrl', '/v1/admin/cloudservice/iot/access/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10436', 'ping', '/v1/admin/cloudservice/refresh/ping', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10437', 'getUrl_1', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10438', 'findCountryProvinceCity', '/v1/admin/common/administrative/region', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10439', 'findApkParameterPage_1', '/v1/admin/common/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10440', 'createApkParameterDataFileDownloadTask_1', '/v1/admin/common/app/apk/parameters/{apkParameterId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10441', 'getApkParameterSchemaInfo', '/v1/admin/common/app/apk/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10442', 'downloadReleaseNoteTask', '/v1/admin/common/app/apk/{apkId}/release-note/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10443', 'getApkDetailVo_4', '/v1/admin/common/app/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10444', 'findOnlineAppPage_1', '/v1/admin/common/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10445', 'findEntityAttributePage_1', '/v1/admin/common/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10446', 'getBoundariesGeofencing', '/v1/admin/common/boundaries/geofencing', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10447', 'findLatestOnlineClientApkFactoryList', '/v1/admin/common/client-app/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10448', 'findFactoryPage_1', '/v1/admin/common/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10449', 'findFactoryModelTree', '/v1/admin/common/factory/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10450', 'checkFile', '/v1/admin/common/file/check-file', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10451', 'uploadPart', '/v1/admin/common/file/chunk-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10452', 'findFirmwarePage', '/v1/admin/common/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10453', 'createTerminalFirmwareFileDownloadTask', '/v1/admin/common/firmwares/file/{fmFileId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10454', 'getFirmwareDetailVo_2', '/v1/admin/common/firmwares/{firmwareId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10455', 'findGeofenceTemplatePage_1', '/v1/admin/common/geofence/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10456', 'searchMarkets_2', '/v1/admin/common/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10457', 'findMerchantCategoryPage_1', '/v1/admin/common/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10458', 'findMerchantPage', '/v1/admin/common/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10459', 'findModelPage_3', '/v1/admin/common/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10460', 'findRkiTemplateKeyPage', '/v1/admin/common/reseller/{resellerId}/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10461', 'findResellerPage', '/v1/admin/common/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10462', 'findResellerTreePage', '/v1/admin/common/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10463', 'getMarket_5', '/v1/admin/current-market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10464', 'getUser_5', '/v1/admin/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10465', 'searchPendingApps_1', '/v1/admin/dashboard/apps-pending', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10466', 'searchAppsTop10_1', '/v1/admin/dashboard/apps-top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10467', 'getDashboardLayout', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10468', 'saveDashboard', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10469', 'searchMarkers_2', '/v1/admin/dashboard/map-markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10470', 'searchTerminalsByPlace_1', '/v1/admin/dashboard/map-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10471', 'getResellerProfile_2', '/v1/admin/dashboard/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10472', 'getTerminalNumberStatisticData_2', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10473', 'getTerminalNumberOfResellerData_2', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10474', 'getFmTerminalForWidget_1', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10475', 'exportFmTerminalOrgWidget_1', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10476', 'getClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10477', 'exportClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10478', 'loadWidgetModelTerminal_2', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10479', 'exportModelTerminalWidget_1', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10480', 'loadWidgetTerminalOffline_2', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10481', 'exportTerminalOfflineWidget_1', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10482', 'getFmTerminalWidget_1', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10483', 'exportFmTerminalWidget_1', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10484', 'loadClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10485', 'downloadClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10486', 'createExportTerminalsDownloadTask_2', '/v1/admin/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10487', 'getWidgetCardNumberActive_1', '/v1/admin/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10488', 'getWidgetDigitalDisplaySetting_1', '/v1/admin/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10489', 'updateWidgetDigitalDisplay', '/v1/admin/dashboard/widgets/W20/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10490', 'getPukTerminalWidget_1', '/v1/admin/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10491', 'exportPUKTerminalWidget_1', '/v1/admin/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10492', 'findPageList_1', '/v1/admin/datasource/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10493', 'createDataSourceInfo', '/v1/admin/datasource/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10494', 'testConnection', '/v1/admin/datasource/info/testConnection', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10495', 'findById_1', '/v1/admin/datasource/info/{dataSourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10496', 'deleteById_1', '/v1/admin/datasource/info/{dataSourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10497', 'findPageList', '/v1/admin/datasource/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10498', 'createDataSourceMarket', '/v1/admin/datasource/market', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10499', 'findById', '/v1/admin/datasource/market/{configId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10500', 'deleteById', '/v1/admin/datasource/market/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10501', 'searchDevelopers', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10502', 'exportDevelopers', '/v1/admin/developers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10503', 'getDeveloper_2', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10504', 'updateDeveloper', '/v1/admin/developers/{developerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10505', 'deleteDeveloper_1', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10506', 'activeDeveloper3rdSysAccess_1', '/v1/admin/developers/{developerId}/3rd-system/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10507', 'disableDeveloper3rdSysAccess', '/v1/admin/developers/{developerId}/3rd-system/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10508', 'approveDeveloper_1', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10509', 'updateAllowIndustrySolution', '/v1/admin/developers/{developerId}/industry-solution', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10510', 'approveGlobalDeveloperPayment', '/v1/admin/developers/{developerId}/pay/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10511', 'rejectDeveloperPayment', '/v1/admin/developers/{developerId}/pay/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10512', 'rejectDeveloper_1', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10513', 'specificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10514', 'changeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10515', 'closeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10516', 'resumeDeveloper_1', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10517', 'findDeveloperSandboxTerminalPage', '/v1/admin/developers/{developerId}/sandbox/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10518', 'deleteSandboxTerminal', '/v1/admin/developers/{developerId}/sandbox/terminal/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10519', 'findDeveloperServices', '/v1/admin/developers/{developerId}/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10520', 'updateDeveloperSuperAdmin_2', '/v1/admin/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10521', 'suspendDeveloper_1', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10522', 'activeDeveloperUser', '/v1/admin/developers/{developerId}/user/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10523', 'findEnterpriseDeveloperUserPage', '/v1/admin/developers/{developerId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10524', 'findEmmAppPage', '/v1/admin/emm/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10525', 'getApkInfo_2', '/v1/admin/emm/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10526', 'createApkDownloadTask_1', '/v1/admin/emm/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10527', 'updateEmmAppConfig', '/v1/admin/emm/apps/config/{configId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10528', 'removeEmmAppConfig', '/v1/admin/emm/apps/config/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10529', 'createEmmApp', '/v1/admin/emm/apps/create', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10530', 'findEmmAppSubscriptionPage', '/v1/admin/emm/apps/subscription', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10531', 'createWebToken', '/v1/admin/emm/apps/webToken', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10532', 'getEmmAppDetail', '/v1/admin/emm/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10533', 'removeEmmApp', '/v1/admin/emm/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10534', 'searchApk_2', '/v1/admin/emm/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10535', 'approveApp_1', '/v1/admin/emm/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10536', 'promoteTestRelease2Production', '/v1/admin/emm/apps/{appId}/apks/{apkId}/promote', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10537', 'rejectApp_1', '/v1/admin/emm/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10538', 'updateAppKey_2', '/v1/admin/emm/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10539', 'listAvailableTestVersionApks', '/v1/admin/emm/apps/{appId}/availableTestVersions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10540', 'findEmmAppConfigPage', '/v1/admin/emm/apps/{appId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10541', 'createEmmAppConfig', '/v1/admin/emm/apps/{appId}/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10542', 'findEmmAppConfigList', '/v1/admin/emm/apps/{appId}/configList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10543', 'retrieveEmmAppPerms', '/v1/admin/emm/apps/{appId}/permissions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10544', 'getAppSettings', '/v1/admin/emm/apps/{appId}/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10545', 'updateAppSettings', '/v1/admin/emm/apps/{appId}/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10546', 'subscribeEmmApp_1', '/v1/admin/emm/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10547', 'unSubscribeEmmApp_1', '/v1/admin/emm/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10548', 'getMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10549', 'createMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10550', 'syncMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}/sync', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10551', 'getResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10552', 'createResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10553', 'syncResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}/sync', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10554', 'findFactoryPage', '/v1/admin/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10555', 'createFactory', '/v1/admin/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10556', 'getSignatureProviderList', '/v1/admin/factories/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10557', 'getFactoryDetail', '/v1/admin/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10558', 'updateFactory', '/v1/admin/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10559', 'deleteFactory', '/v1/admin/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10560', 'findSpecificFactoryMarketPage', '/v1/admin/factories/{factoryId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10561', 'specificFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10562', 'deleteFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10563', 'findSpecificFactoryMarketAllList', '/v1/admin/factories/{factoryId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10564', 'searchFirmware_2', '/v1/admin/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10565', 'searchFirmware_3', '/v1/admin/firmwares-approval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10566', 'listFirmwareFactory', '/v1/admin/firmwares-approval/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10567', 'findPendingStatistic', '/v1/admin/firmwares-approval/pendingStatistic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10568', 'getFirmware_1', '/v1/admin/firmwares-approval/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10569', 'approveFirmware', '/v1/admin/firmwares-approval/{firmwareId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10570', 'specificMarketFirmware', '/v1/admin/firmwares-approval/{firmwareId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10571', 'findSpecificFirmwareMarketAllPage', '/v1/admin/firmwares-approval/{firmwareId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10572', 'updateFirmwareModel', '/v1/admin/firmwares-approval/{firmwareId}/model/update', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10573', 'offlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10574', 'onlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10575', 'rejectFirmware', '/v1/admin/firmwares-approval/{firmwareId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10576', 'specificResellerFirmware_2', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10577', 'findFirmwareResellerSpecificAllPage', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10578', 'deleteFirmwareDiff', '/v1/admin/firmwares-common/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10579', 'deleteFirmware', '/v1/admin/firmwares-common/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10580', 'uploadFirmwareDiffFile', '/v1/admin/firmwares-common/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10581', 'uploadFirmwareFile', '/v1/admin/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10582', 'getFirmware', '/v1/admin/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10583', 'updateFirmware', '/v1/admin/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10584', 'getFirmwareForEditPage', '/v1/admin/firmwares/{firmwareId}/edit-page', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10585', 'submitFirmware', '/v1/admin/firmwares/{firmwareId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10586', 'searchMarkers_1', '/v1/admin/geo-location/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10587', 'searchMarket', '/v1/admin/geo-location/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10588', 'getMarket_4', '/v1/admin/geo-location/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10589', 'getResellerProfile_1', '/v1/admin/geo-location/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10590', 'searchReseller', '/v1/admin/geo-location/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10591', 'getReseller', '/v1/admin/geo-location/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10592', 'searchTerminalsByPlace', '/v1/admin/geo-location/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10593', 'exportTerminalsByPlace', '/v1/admin/geo-location/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10594', 'getTerminalDetail', '/v1/admin/geo-location/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10595', 'findGeofenceTemplatePage', '/v1/admin/geofence/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10596', 'createGeofenceTemplate', '/v1/admin/geofence/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10597', 'getGeofenceTemplate', '/v1/admin/geofence/templates/{geofenceTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10598', 'updateGeofenceTemplate', '/v1/admin/geofence/templates/{geofenceTemplateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10599', 'deleteLauncherTemplate_1', '/v1/admin/geofence/templates/{geofenceTemplateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10600', 'searchMarkets_1', '/v1/admin/global/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10601', 'createMarket', '/v1/admin/global/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10602', 'createMarketApiAccFreq', '/v1/admin/global/markets/api/access/frequency', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10603', 'updateMarketApiAccFreq', '/v1/admin/global/markets/api/access/frequency/{accFreqId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10604', 'findRkiServerList', '/v1/admin/global/markets/rki-servers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10605', 'getMarketInfoSummary', '/v1/admin/global/markets/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10606', 'findAppNumDetail', '/v1/admin/global/markets/statistics/app-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10607', 'findDeveloperNumDetail', '/v1/admin/global/markets/statistics/developer-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10608', 'createExportStatisticsDownloadTask', '/v1/admin/global/markets/statistics/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10609', 'findMarketNumDetail', '/v1/admin/global/markets/statistics/market-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10610', 'sendMarketTerminalReport', '/v1/admin/global/markets/terminal-report', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10611', 'getMarket_3', '/v1/admin/global/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10612', 'updateMarket', '/v1/admin/global/markets/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10613', 'deleteMarket', '/v1/admin/global/markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10614', 'findMarketApiAccFreq', '/v1/admin/global/markets/{marketId}/api/access/frequency', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10615', 'updateMarketBillingSetting', '/v1/admin/global/markets/{marketId}/billing', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10616', 'getMarketIconStatus', '/v1/admin/global/markets/{marketId}/icon-status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10617', 'updateOverdueMarket', '/v1/admin/global/markets/{marketId}/overdue', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10618', 'getMarketPermissions', '/v1/admin/global/markets/{marketId}/permissions/{functionType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10619', 'updateMarketBillingPriceSetting', '/v1/admin/global/markets/{marketId}/price-setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10620', 'findMarketBillingPriceSettings', '/v1/admin/global/markets/{marketId}/price-settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10621', 'replaceMarketResellerEmail', '/v1/admin/global/markets/{marketId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10622', 'sendMarketActivateEmail', '/v1/admin/global/markets/{marketId}/resend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10623', 'resumeMarket', '/v1/admin/global/markets/{marketId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10624', 'getMarketServiceSetting', '/v1/admin/global/markets/{marketId}/service-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10625', 'getMarketSummary', '/v1/admin/global/markets/{marketId}/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10626', 'suspendMarket', '/v1/admin/global/markets/{marketId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10627', 'getReportMetadataForCreateAndUpdate', '/v1/admin/global/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10628', 'getReport_1', '/v1/admin/global/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10629', 'updateReport', '/v1/admin/global/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10630', 'activateReport', '/v1/admin/global/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10631', 'createDownloadBandFileTask', '/v1/admin/global/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10632', 'disableReport', '/v1/admin/global/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10633', 'createDownloadTaskForTemplateFile', '/v1/admin/global/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10634', 'findLauncherTemplatePage', '/v1/admin/launcher/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10635', 'createLauncherTemplate', '/v1/admin/launcher/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10636', 'findResellerOnlineAppPage', '/v1/admin/launcher/templates/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10637', 'getResellerOnlineApkNameAndIcon_1', '/v1/admin/launcher/templates/reseller/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10638', 'getLauncherTemplate_2', '/v1/admin/launcher/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10639', 'updateLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10640', 'deleteLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10641', 'updateLauncherTemplateName', '/v1/admin/launcher/templates/{launcherTemplateId}/rename', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10642', 'activeMarket3rdSysAccess', '/v1/admin/market/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10643', 'getMarket3rdSysConfig', '/v1/admin/market/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10644', 'find3rdSysConfigIpPage_1', '/v1/admin/market/3rd-sys/config/ip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10645', 'deActiveMarket3rdSysAccess', '/v1/admin/market/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10646', 'add3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10647', 'update3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10648', 'delete3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10649', 'getMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10650', 'resetMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10651', 'find3rdSysWebHookPage_1', '/v1/admin/market/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10652', 'create3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10653', 'getWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10654', 'get3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', 'update3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', 'delete3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', 'findWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', 'test3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', 'findAnnouncementPage', '/v1/admin/market/advance/announcement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', 'createAnnouncementNotification', '/v1/admin/market/advance/announcement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', 'publishAnnouncementNotification', '/v1/admin/market/advance/announcement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', 'getAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', 'updateAnnouncementNotification', '/v1/admin/market/advance/announcement/{announcementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10664', 'deleteAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', 'findAppWhiteListPage_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', 'createAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', 'deleteAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', 'findPage', '/v1/admin/market/advance/ip-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', 'createAccessIp', '/v1/admin/market/advance/ip-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', 'getAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10671', 'closeAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10672', 'openAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/open', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', 'updateAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', 'deleteAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', 'getMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', 'createMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', 'deleteSensitiveWordId', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', 'createSensitiveWordFileDownloadTask', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', 'findAppBlackListPage_1', '/v1/admin/market/app-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', 'updateAppBlackList', '/v1/admin/market/app-blacklist', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10681', 'createAppBlackList', '/v1/admin/market/app-blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', 'deleteAppBlackList', '/v1/admin/market/app-blacklist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', 'findAppWhiteListPage', '/v1/admin/market/app-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', 'createAppWhiteList', '/v1/admin/market/app-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', 'deleteAppWhiteList', '/v1/admin/market/app-whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', 'findEntityAttributePage', '/v1/admin/market/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', 'createEntityAttribute', '/v1/admin/market/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10688', 'getEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10689', 'updateEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10690', 'deleteEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10691', 'updateEntityAttributeLabel', '/v1/admin/market/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', 'updateBillingServicePrice_1', '/v1/admin/market/billing/change/price/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10693', 'getCurrentBilling', '/v1/admin/market/billing/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', 'updateMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10695', 'getMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10696', 'downloadGlobalTimePeriodBilling', '/v1/admin/market/billing/global/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', 'loadGlobalSingleMonthBill', '/v1/admin/market/billing/global/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10698', 'loadTimePeriodBilling', '/v1/admin/market/billing/global/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10699', 'loadTotalBillItemTimePeriod', '/v1/admin/market/billing/global/total/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10700', 'getUnreceivedAmount', '/v1/admin/market/billing/global/unreceived/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', 'findGlobalUnresolvedInvoices', '/v1/admin/market/billing/global/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', 'reRunBillingJob', '/v1/admin/market/billing/job', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', 'findOperationLog', '/v1/admin/market/billing/log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', 'findBillingServiceDetailLog', '/v1/admin/market/billing/log/detail/{batchId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10705', 'downloadPaymentBillHistory', '/v1/admin/market/billing/market/{marketId}/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10706', 'getMarketSingleMonthBilling', '/v1/admin/market/billing/market/{marketId}/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10707', 'getMarketTimePeriodBilling', '/v1/admin/market/billing/market/{marketId}/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10708', 'searchPayment', '/v1/admin/market/billing/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', 'checkPaymentStatus', '/v1/admin/market/billing/payment/check', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10710', 'confirmPayBilling', '/v1/admin/market/billing/payment/confirm', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10711', 'initPayment', '/v1/admin/market/billing/payment/init', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10712', 'sendBillingInvoice', '/v1/admin/market/billing/send/invoice/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10713', 'getMarketInvoice', '/v1/admin/market/billing/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', 'updateMarketInvoice', '/v1/admin/market/billing/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', 'findBillingReceiveEmailList', '/v1/admin/market/billing/settings/email', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', 'createReceiveEmail', '/v1/admin/market/billing/settings/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', 'updateReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', 'deleteReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', 'getSingleMonthBilling', '/v1/admin/market/billing/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', 'updateBillingStatusToAudit', '/v1/admin/market/billing/status/audit/{billingSummaryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', 'loadSummaryBillItemTimePeriod', '/v1/admin/market/billing/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', 'getUnPaidAmountPayable', '/v1/admin/market/billing/unPaid/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', 'findHistoryUnresolvedInvoices', '/v1/admin/market/billing/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', 'findMerchantCategoryPage', '/v1/admin/market/merchant-categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', 'createMerchantCategory', '/v1/admin/market/merchant-categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10726', 'batchDeleteMerchantCategories', '/v1/admin/market/merchant-categories/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10727', 'importMerchantCategory', '/v1/admin/market/merchant-categories/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10728', 'createMerchantCategoryImportTemplateDownloadTask', '/v1/admin/market/merchant-categories/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10729', 'updateMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10730', 'deleteMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10731', 'specificModel2Market', '/v1/admin/market/model/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10732', 'findFactoryIncludeModelPage', '/v1/admin/market/model/settings/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10733', 'findModelPage_2', '/v1/admin/market/model/settings/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10734', 'getMarketSetting', '/v1/admin/market/settings', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10735', 'saveMarketSettings', '/v1/admin/market/settings', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10736', 'activateMarket', '/v1/admin/market/settings/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10737', 'findAgreementPage', '/v1/admin/market/settings/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10738', 'createAgreement', '/v1/admin/market/settings/agreement', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10739', 'findAgreementSettingPage', '/v1/admin/market/settings/agreement/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10740', 'saveAgreementSettings', '/v1/admin/market/settings/agreement/config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10741', 'publishAgreement', '/v1/admin/market/settings/agreement/publish', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10742', 'updateAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10743', 'deleteAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10744', 'exportAgreementAgreedRecords', '/v1/admin/market/settings/agreement/{agreementId}/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10745', 'findFooter_1', '/v1/admin/market/settings/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10746', 'createMarketFooter', '/v1/admin/market/settings/footer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10747', 'updateMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10748', 'deleteMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10749', 'sortMarketFooter', '/v1/admin/market/settings/footer/{footerId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10750', 'getMarketLimitConfig', '/v1/admin/market/settings/limitconfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10751', 'findRkiServerPage', '/v1/admin/market/settings/rki-servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10752', 'getTIDSettings', '/v1/admin/market/settings/tid', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10753', 'updateTIDSetting', '/v1/admin/market/settings/tid', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10754', 'getMarketUiSettings_1', '/v1/admin/market/settings/ui', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10755', 'saveMarketUiSettings', '/v1/admin/market/settings/ui', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10756', 'saveMarketUiAdvanceSettings', '/v1/admin/market/settings/ui/advance', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10757', 'deleteMarketUiAdvanceSettings', '/v1/admin/market/settings/ui/advance', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10758', 'findOnlineAppPageForFeatured', '/v1/admin/market/settings/ui/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10759', 'findFeaturedApp_1', '/v1/admin/market/settings/ui/apps/featured', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10760', 'updateFeaturedAppSort', '/v1/admin/market/settings/ui/apps/featured/{featuredAppId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10761', 'addFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10762', 'deleteFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10763', 'updateFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured/{featuredAppId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10764', 'getSignatureSetting', '/v1/admin/market/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10765', 'saveSignatureSetting', '/v1/admin/market/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10766', 'clearSignatureData', '/v1/admin/market/signature/data/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10767', 'findSignatureFactoryPage', '/v1/admin/market/signature/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10768', 'testSignatureConfigServer_1', '/v1/admin/market/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10769', 'getSsoSetting', '/v1/admin/market/sso/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10770', 'updateSsoSetting', '/v1/admin/market/sso/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10771', 'searchRoles_1', '/v1/admin/market/sso/settings/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10772', 'getTerminalBlacklist', '/v1/admin/market/terminal-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10773', 'importTerminalBlacklist', '/v1/admin/market/terminal-blacklist/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10774', 'createTerminalImportTemplateDownloadTask_2', '/v1/admin/market/terminal-blacklist/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10775', 'deleteTerminalBlacklist', '/v1/admin/market/terminal-blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10776', 'getTerminalWhiteList', '/v1/admin/market/terminal-whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10777', 'importAddTerminalWhiteList', '/v1/admin/market/terminal-whiteList/import/add', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10778', 'importDeleteTerminalWhiteList', '/v1/admin/market/terminal-whiteList/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10779', 'createTerminalImportTemplateDownloadTask_1', '/v1/admin/market/terminal-whiteList/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10780', 'deleteTerminalWhiteList', '/v1/admin/market/terminal-whiteList/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10781', 'findMarketVariablePage', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10782', 'createMarketVariable', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10783', 'batchDeleteTerminalVariables_2', '/v1/admin/market/variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10784', 'importMarketVariable', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10785', 'createMarketVariableImportTemplateDownloadTask', '/v1/admin/market/variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10786', 'findMarketVariableRelatedAppPage', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10787', 'findMarketVariableUsedAppPage', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10788', 'updateMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10789', 'deleteMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10790', 'searchUsers_2', '/v1/admin/merchant/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10791', 'createExportUserDownloadTask_1', '/v1/admin/merchant/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10792', 'activeUser_1', '/v1/admin/merchant/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10793', 'sendActivateUserEmail_1', '/v1/admin/merchant/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10794', 'getUserMerchants', '/v1/admin/merchant/users/{userId}/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10795', 'resetUserPassword_1', '/v1/admin/merchant/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10796', 'findModelPage_1', '/v1/admin/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10797', 'createModel', '/v1/admin/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10798', 'getModelDetail', '/v1/admin/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10799', 'updateModel', '/v1/admin/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10800', 'deleteModel', '/v1/admin/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10801', 'findProtectedOperations', '/v1/admin/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10802', 'getUser_4', '/v1/admin/operations/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10803', 'getProtectedOperation', '/v1/admin/operations/{key}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10804', 'closeOperation', '/v1/admin/operations/{key}/close', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10805', 'openOperation', '/v1/admin/operations/{key}/open', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10806', 'closeResellerOperationControl', '/v1/admin/operations/{key}/reseller/operation/close', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10807', 'openResellerOperationControl', '/v1/admin/operations/{key}/reseller/operation/open', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10808', 'getOperationUsers', '/v1/admin/operations/{key}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10809', 'addOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10810', 'removeOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10811', 'getCodeLangConfigs', '/v1/admin/platform/configuration/codes/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10812', 'saveCodeLangConfig', '/v1/admin/platform/configuration/codes/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10813', 'getCodeTypes', '/v1/admin/platform/configuration/codes/setting/codeTypes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10814', 'getCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10815', 'deleteCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10816', 'getLicense_1', '/v1/admin/platform/configuration/license', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10817', 'updateLicense', '/v1/admin/platform/configuration/license', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10818', 'loadLoginSettings', '/v1/admin/platform/configuration/login-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10819', 'savePwdPolicy_1', '/v1/admin/platform/configuration/login-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10820', 'loadMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10821', 'saveMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10822', 'testMailServiceConfig', '/v1/admin/platform/configuration/mail-config/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10823', 'listAll', '/v1/admin/platform/configuration/oauth-client', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10824', 'add', '/v1/admin/platform/configuration/oauth-client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10825', 'get', '/v1/admin/platform/configuration/oauth-client/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10826', 'update', '/v1/admin/platform/configuration/oauth-client/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10827', 'delete_1', '/v1/admin/platform/configuration/oauth-client/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10828', 'loadPwdPolicy', '/v1/admin/platform/configuration/password-policy', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10829', 'savePwdPolicy', '/v1/admin/platform/configuration/password-policy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10830', 'searchReleaseNoteInfos', '/v1/admin/platform/configuration/release-note', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10831', 'createReleaseNoteInfo', '/v1/admin/platform/configuration/release-note', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10832', 'createMailTemplateDownloadTask', '/v1/admin/platform/configuration/release-note/mail/template/{releaseNoteInfoId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10833', 'getReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10834', 'updateReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10835', 'downloadEmails', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/download-emails', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10836', 'sendMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/send-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10837', 'testMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/test-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10838', 'finsRkiServerList', '/v1/admin/platform/configuration/rki-server', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10839', 'createRkiServerSetting', '/v1/admin/platform/configuration/rki-server', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10840', 'testSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10841', 'getRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10842', 'updateRkiServerSetting', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10843', 'deleteRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10844', 'testExistSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/{rkiId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10845', 'searchDiscountTerminals', '/v1/admin/platform/internal/discount/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10846', 'importMerchant_1', '/v1/admin/platform/internal/discount/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10847', 'searchPredefinedRoles', '/v1/admin/platform/internal/predefined-roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10848', 'searchPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10849', 'removePredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10850', 'createPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10851', 'searchPrivileges', '/v1/admin/platform/internal/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10852', 'createPrivilege', '/v1/admin/platform/internal/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10853', 'getPrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10854', 'updatePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10855', 'deletePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10856', 'searchPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10857', 'removePrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10858', 'createPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10859', 'searchSystemProperties', '/v1/admin/platform/internal/properties', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10860', 'createSystemProperty', '/v1/admin/platform/internal/properties', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10861', 'getSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10862', 'updateSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10863', 'deleteSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10864', 'getPushDiagnosisResult', '/v1/admin/platform/internal/push-diagnosis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10865', 'sendPushDiagnosisTest', '/v1/admin/platform/internal/push-diagnosis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10866', 'searchResources', '/v1/admin/platform/internal/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10867', 'createResource', '/v1/admin/platform/internal/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10868', 'getResource', '/v1/admin/platform/internal/resources/{resourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10869', 'updateResource', '/v1/admin/platform/internal/resources/{resourceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10870', 'deleteResource', '/v1/admin/platform/internal/resources/{resourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', 'searchPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10872', 'removePrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', 'createPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', 'triggerScheduleJob', '/v1/admin/platform/internal/schedule-job', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10875', 'searchResellerMigrations', '/v1/admin/platform/migration/reseller-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10876', 'createResellerMigration', '/v1/admin/platform/migration/reseller-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10877', 'getResellerMigration', '/v1/admin/platform/migration/reseller-migrations/{resellerMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10878', 'searchAppPriceTemplates', '/v1/admin/price/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10879', 'createPriceTemplate', '/v1/admin/price/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10880', 'updatePriceTemplate', '/v1/admin/price/templates/{templateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10881', 'deletePriceTemplate', '/v1/admin/price/templates/{templateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10882', 'getProductList', '/v1/admin/products', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10883', 'findProfileSettingList', '/v1/admin/products/profiles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', 'saveProfileSettingList', '/v1/admin/products/profiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10885', 'getProductDetail', '/v1/admin/products/{codeValue}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10886', 'getProductProfile', '/v1/admin/products/{codeValue}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10887', 'getProductService', '/v1/admin/products/{codeValue}/service', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10888', 'getProductSetting', '/v1/admin/products/{codeValue}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10889', 'exportParameters', '/v1/admin/push/template/export/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10890', 'createExportApkParameterCompareDownloadTask_1', '/v1/admin/push/template/export/parameters/comparison', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10891', 'findParamAppPage', '/v1/admin/push/template/param-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10892', 'findParamSolutionAppPage', '/v1/admin/push/template/param-solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10893', 'findApkParametersPage', '/v1/admin/push/template/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10894', 'createApkParameter', '/v1/admin/push/template/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10895', 'findApkParameterAppPage', '/v1/admin/push/template/parameters/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10896', 'batchDeleteApkParameter', '/v1/admin/push/template/parameters/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10897', 'findApkParameterComparePage', '/v1/admin/push/template/parameters/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10898', 'getApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10899', 'updateApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10900', 'deleteApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10901', 'updateApkParameterFormData', '/v1/admin/push/template/parameters/{apkParameterId}/data', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10902', 'createApkParameterDataFileDownloadTask', '/v1/admin/push/template/parameters/{apkParameterId}/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10903', 'getApkParameterSchema', '/v1/admin/push/template/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10904', 'searchReport', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10905', 'getMerchantByResellerIds', '/v1/admin/report/data-source/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10906', 'searchInstalledPUKList', '/v1/admin/report/data-source/puk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10907', 'getResellersByMarketId', '/v1/admin/report/data-source/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10908', 'searchReportApkList', '/v1/admin/report/data-source/{reportId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10909', 'refreshParameterSourceItems', '/v1/admin/report/parameter/{parameterId}/source/refresh', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10910', 'deleteReportExecution_1', '/v1/admin/report/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10911', 'getReportJobHistoryPage', '/v1/admin/report/reportJobHistory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10912', 'createDownloadTaskForReport', '/v1/admin/report/reportTask/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10913', 'updateReportTasksStatus', '/v1/admin/report/reportTask/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10914', 'updateReportTaskStatus', '/v1/admin/report/reportTask/{reportExecutionContextId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10915', 'getReportTaskByPage', '/v1/admin/report/reportTask/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10916', 'deleteReportExecution', '/v1/admin/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10917', 'getReport', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10918', 'findReportDynamicFields', '/v1/admin/report/{reportId}/dynamic-fields', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10919', 'createImmediateReportExecution', '/v1/admin/report/{reportId}/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10920', 'createScheduledReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10921', 'getReportExecutionContext', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10922', 'updateReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10923', 'activeReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10924', 'getReseller3rdSysConfig', '/v1/admin/reseller/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10925', 'find3rdSysConfigIpPage', '/v1/admin/reseller/3rd-sys/config/ip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10926', 'deActiveReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10927', 'addReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10928', 'updateReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10929', 'deleteReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10930', 'getReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10931', 'resetReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10932', 'find3rdSysWebHookPage', '/v1/admin/reseller/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10933', 'create3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10934', 'getWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10935', 'get3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10936', 'update3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10937', 'delete3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10938', 'findWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10939', 'test3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10940', 'findResellerOnlineApps', '/v1/admin/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10941', 'getApkInfo_1', '/v1/admin/reseller/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10942', 'reSignApk_3', '/v1/admin/reseller/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10943', 'findApkSignatureList_3', '/v1/admin/reseller/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10944', 'findSpecificApkResellerPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10945', 'specificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10946', 'deleteSpecificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10947', 'findSpecificApkResellerAllListPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10948', 'getTopicSubscription_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10949', 'subscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10950', 'unsubscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10951', 'getAppInfo_1', '/v1/admin/reseller/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10952', 'searchApk_1', '/v1/admin/reseller/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10953', 'getBizDataFromGoInsight_2', '/v1/admin/reseller/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10954', 'getAppMerchantCategory', '/v1/admin/reseller/apps/{appId}/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10955', 'getAppSettingVo_1', '/v1/admin/reseller/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10956', 'findSpecificAppResellerPage_1', '/v1/admin/reseller/apps/{appId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10957', 'specificAppReseller_1', '/v1/admin/reseller/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10958', 'deleteSpecificResellerApp', '/v1/admin/reseller/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10959', 'findSpecificAppResellerAllListPage_1', '/v1/admin/reseller/apps/{appId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10960', 'getAppVasSettingVo_2', '/v1/admin/reseller/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10961', 'searchFirmware_1', '/v1/admin/reseller/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10962', 'getTopicSubscription_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10963', 'subscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10964', 'unsubscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10965', 'getFirmwareDetailVo_1', '/v1/admin/reseller/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10966', 'specificResellerFirmware_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10967', 'findSpecificFirmwareResellerAllListPage_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10968', 'getResellerRki_1', '/v1/admin/reseller/rki/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10969', 'refreshResellerRkiKeys_1', '/v1/admin/reseller/rki/settings/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10970', 'saveResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10971', 'deleteResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10972', 'getResellerTIDSettings', '/v1/admin/reseller/settings/tid', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10973', 'updateResellerTIDSetting', '/v1/admin/reseller/settings/tid', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10974', 'getMarketUiSettings', '/v1/admin/reseller/settings/ui', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10975', 'saveResellerUISettings', '/v1/admin/reseller/settings/ui', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10976', 'saveResellerUIAdvanceSettings', '/v1/admin/reseller/settings/ui/advance', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10977', 'deleteResellerUIAdvanceSettings', '/v1/admin/reseller/settings/ui/advance', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10978', 'saveResellerSignatureSetting', '/v1/admin/reseller/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10979', 'searchRoles', '/v1/admin/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10980', 'createRole', '/v1/admin/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10981', 'searchUsers_1', '/v1/admin/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10982', 'getRole', '/v1/admin/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10983', 'updateRole', '/v1/admin/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10984', 'deleteRole', '/v1/admin/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10985', 'searchRoleUsers', '/v1/admin/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10986', 'addRoleUsers', '/v1/admin/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10987', 'removeRoleUsers', '/v1/admin/roles/{roleId}/users', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10988', 'findSolutionAppPage', '/v1/admin/service/industry-solution/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10989', 'reSignApk_2', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10990', 'findApkSignatureList_2', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10991', 'findSolutionAppIntroductionPage', '/v1/admin/service/industry-solution/apps/introduction', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10992', 'getSolutionAppDetail', '/v1/admin/service/industry-solution/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10993', 'applyIndustrySolutionAppForSpecific', '/v1/admin/service/industry-solution/apps/{appId}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10994', 'findVasAppCurrentUsage', '/v1/admin/service/industry-solution/apps/{appId}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10995', 'exportVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10996', 'findVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10997', 'findVasAppMarkets', '/v1/admin/service/industry-solution/apps/{appId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10998', 'findSpecificSolutionResellerPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10999', 'specificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11000', 'deleteSpecificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11001', 'findSpecificSolutionResellerAllListPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11002', 'updateAppServicePrice', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11003', 'findVasAppServiceHistory', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11004', 'updateAppServiceStatus', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11005', 'getSolutionTrialPage', '/v1/admin/service/industry-solution/apps/{appId}/trial/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11006', 'getVasAppUsageDashBoard', '/v1/admin/service/industry-solution/apps/{appId}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11007', 'getAppVasSettingVo_1', '/v1/admin/service/industry-solution/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11008', 'findDeveloperSolutionApplyPage', '/v1/admin/service/industry-solution/developer/apply', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11009', 'getDeveloperSolutionApplyCount', '/v1/admin/service/industry-solution/developer/apply/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11010', 'updateDeveloperAllowIndustrySolution', '/v1/admin/service/industry-solution/developer/{developerId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11011', 'getResellerApplies', '/v1/admin/service/industry-solution/reseller/apply', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11012', 'getResellerSolutionApplyCount', '/v1/admin/service/industry-solution/reseller/apply/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11013', 'updateResellerApply', '/v1/admin/service/industry-solution/reseller/apply/{applyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11014', 'findGlobalPublishAppPage', '/v1/admin/subscription/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11015', 'getApkDetailVo_3', '/v1/admin/subscription/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11016', 'findSpecificApkResellerPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11017', 'specificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11018', 'deleteSpecificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11019', 'findSpecificApkResellerAllListPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11020', 'reSignApk_1', '/v1/admin/subscription/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11021', 'findApkSignatureList_1', '/v1/admin/subscription/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11022', 'exportGlobalPublishApp', '/v1/admin/subscription/apps/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11023', 'getAppDetailVo', '/v1/admin/subscription/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11024', 'subscriptionApp', '/v1/admin/subscription/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11025', 'findApkPage', '/v1/admin/subscription/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11026', 'unSubscriptionApp', '/v1/admin/subscription/apps/{appId}/cancel', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11027', 'getBizDataFromGoInsight_1', '/v1/admin/subscription/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11028', 'findAppMerchantCategoryPage', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11029', 'specificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11030', 'deleteSpecificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11031', 'findSpecificAppResellerPage', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11032', 'specificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11033', 'deleteSpecificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11034', 'findSpecificAppResellerAllListPage', '/v1/admin/subscription/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11035', 'getAppSettingVo', '/v1/admin/subscription/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11036', 'getAppVasSettingVo', '/v1/admin/subscription/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11037', 'findGlobalPublishFirmware', '/v1/admin/subscription/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11038', 'exportGlobalPublishFirmware', '/v1/admin/subscription/firmwares/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11039', 'getFirmwareDetailVo', '/v1/admin/subscription/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11040', 'subscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11041', 'unsubscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/cancel', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11042', 'specificResellerFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11043', 'findSpecificFirmwareResellerAllListPage', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11044', 'getTopicSubscription', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11045', 'subscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11046', 'unsubscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11047', 'searchGroups_1', '/v1/admin/terminal-groups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11048', 'createGroup', '/v1/admin/terminal-groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11049', 'searchTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11050', 'createTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11051', 'updateGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11052', 'deleteGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11053', 'getTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11054', 'deleteTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11055', 'resumeGroupTerminalApk', '/v1/admin/terminal-groups/apks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11056', 'activateTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11057', 'getApkDetailVo_2', '/v1/admin/terminal-groups/apks/{groupApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11058', 'createGroupApkDataFileDownloadTask', '/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11059', 'createGroupApkFilter', '/v1/admin/terminal-groups/apks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11060', 'updateGroupApkPushLimit', '/v1/admin/terminal-groups/apks/{groupApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11061', 'getTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11062', 'updateTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11063', 'updateTerminalGroupApkParam_1', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11064', 'resumeGroupTerminalApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11065', 'getGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11066', 'saveGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11067', 'searchGroupApkParamTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11068', 'getGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11069', 'saveGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11070', 'resetTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11071', 'submitTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11072', 'suspendTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11073', 'searchGroupApkTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11074', 'createGroupApkTerminalsExportTasks', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11075', 'searchGroupFirmwares', '/v1/admin/terminal-groups/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11076', 'createGroupFirmware', '/v1/admin/terminal-groups/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11077', 'searchFirmware', '/v1/admin/terminal-groups/firmwares/filter', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11078', 'updateGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11079', 'createGroupFirmwareFilter_1', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11080', 'getGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11081', 'deleteGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11082', 'resumeGroupTerminalFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11083', 'activateGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11084', 'createGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11085', 'updateGroupFirmwarePushLimit', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11086', 'resetGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11087', 'submitGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11088', 'suspendGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11089', 'searchGroupFirmwareTerminals', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11090', 'createGroupFirmwareTerminalsExportTasks', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11091', 'importGroupTerminal', '/v1/admin/terminal-groups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11092', 'createGroupTerminalImportTemplateDownloadTask', '/v1/admin/terminal-groups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11093', 'searchTerminalGroupLaunchers', '/v1/admin/terminal-groups/launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11094', 'createTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11095', 'getApks_1', '/v1/admin/terminal-groups/launchers/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11096', 'getResellerOnlineApkNameAndIcon', '/v1/admin/terminal-groups/launchers/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11097', 'searchLauncherTemplates_1', '/v1/admin/terminal-groups/launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11098', 'getLauncherTemplate_1', '/v1/admin/terminal-groups/launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11099', 'updateGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11100', 'deleteGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11101', 'getTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11102', 'deleteTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11103', 'resumeGroupTerminalLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11104', 'activateTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11105', 'getApkDetailVo_1', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11106', 'createGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11107', 'updateGroupLauncherPushLimit', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11108', 'getTerminalGroupLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11109', 'resumeGroupTerminalLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11110', 'searchGroupLauncherParamTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11111', 'resetTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11112', 'submitTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11113', 'suspendTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11114', 'searchGroupLauncherTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11115', 'createGroupLauncherTerminalsExportTasks', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11116', 'searchGroupOperation', '/v1/admin/terminal-groups/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11117', 'createGroupOperation', '/v1/admin/terminal-groups/operations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11118', 'getGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11119', 'deleteGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11120', 'resumeGroupTerminalOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11121', 'activateGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11122', 'updateGroupOperationPushLimit', '/v1/admin/terminal-groups/operations/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11123', 'resetGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11124', 'submitGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11125', 'suspendGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11126', 'searchGroupOperationTerminals', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11127', 'createGroupOperationTerminalsExportTasks', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11128', 'searchGroupPuks', '/v1/admin/terminal-groups/puks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11129', 'createGroupPuk', '/v1/admin/terminal-groups/puks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11130', 'getSignaturePuk_1', '/v1/admin/terminal-groups/puks/{groupId}/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11131', 'getGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11132', 'deleteGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11133', 'resumeGroupTerminalPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11134', 'activateGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11135', 'updateGroupPukPushLimit', '/v1/admin/terminal-groups/puks/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11136', 'resetGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11137', 'submitGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11138', 'suspendGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11139', 'searchGroupPukTerminals', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11140', 'createGroupPukTerminalsExportTasks', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11141', 'searchGroupRkis', '/v1/admin/terminal-groups/rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11142', 'createGroupRki', '/v1/admin/terminal-groups/rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11143', 'showRkiBalance', '/v1/admin/terminal-groups/rkis/rkiKeyBalance', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11144', 'getGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11145', 'deleteGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11146', 'resumeGroupTerminalRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11147', 'activateGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11148', 'updateGroupRkiPushLimit', '/v1/admin/terminal-groups/rkis/{groupRkiId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11149', 'resetGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11150', 'submitGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11151', 'suspendGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11152', 'searchGroupRkiTerminals', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11153', 'createGroupRkiTerminalsExportTasks', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11154', 'searchTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11155', 'createTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11156', 'findApkParameterPage', '/v1/admin/terminal-groups/solutions/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11157', 'findOnlineAppPage', '/v1/admin/terminal-groups/solutions/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11158', 'updateGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11159', 'deleteGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11160', 'getTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11161', 'deleteTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11162', 'resumeGroupTerminalSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11163', 'activateTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11164', 'getSolutionApkDetailVo', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11165', 'createGroupSolutionDataFileDownloadTask', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11166', 'createGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11167', 'updateGroupSolutionPushLimit', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11168', 'getTerminalGroupSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11169', 'updateTerminalGroupSolutionApkParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11170', 'updateTerminalGroupSolutionApkParamFormData', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11171', 'resumeGroupTerminalSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11172', 'getGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11173', 'saveGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11174', 'searchGroupSolutionParamTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11175', 'getGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11176', 'saveGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11177', 'resetTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11178', 'submitTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11179', 'suspendTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11180', 'searchGroupSolutionTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11181', 'createGroupSolutionTerminalsExportTasks', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11182', 'findSimOperator', '/v1/admin/terminal-groups/terminal/sim/operator', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11183', 'searchTerminal_1', '/v1/admin/terminal-groups/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11184', 'searchGroupUninstallApks', '/v1/admin/terminal-groups/uninstall-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11185', 'createGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11186', 'getGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11187', 'deleteGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11188', 'resumeGroupTerminalUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11189', 'activateGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11190', 'updateGroupUninstallApkPushLimit', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11191', 'resetGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11192', 'submitGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11193', 'suspendGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11194', 'searchGroupUninstallApkTerminals', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11195', 'createGroupUninstallApkTerminalsExportTasks', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11196', 'findTerminalGroupVariableList', '/v1/admin/terminal-groups/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11197', 'createTerminalGroupVariable', '/v1/admin/terminal-groups/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11198', 'batchDeleteTerminalGroupVariables', '/v1/admin/terminal-groups/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11199', 'importTerminalGroupVariable', '/v1/admin/terminal-groups/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11200', 'createTerminalGroupVariableImportTemplateDownloadTask', '/v1/admin/terminal-groups/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11201', 'findTerminalGroupVariableSupportedAppList', '/v1/admin/terminal-groups/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11202', 'findTerminalGroupVariableUsedAppList', '/v1/admin/terminal-groups/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11203', 'updateTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11204', 'deleteTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11205', 'getGroup', '/v1/admin/terminal-groups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11206', 'updateGroup', '/v1/admin/terminal-groups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11207', 'deleteGroup', '/v1/admin/terminal-groups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11208', 'activeGroup_1', '/v1/admin/terminal-groups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11209', 'disableGroup_1', '/v1/admin/terminal-groups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11210', 'removeGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11211', 'createGroupTerminals_1', '/v1/admin/terminal-groups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11212', 'searchGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11213', 'getTerminalNumberStatisticData_1', '/v1/admin/terminal-management/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11214', 'getTerminalNumberOfMerchantData', '/v1/admin/terminal-management/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11215', 'getTerminalNumberOfResellerData_1', '/v1/admin/terminal-management/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11216', 'findEmmDeviceDetailPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11217', 'findEmmDeviceAuditLogPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11218', 'findEmmDeviceInstalledAppPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/installed-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11219', 'getEmmDeviceLocation', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11220', 'getEmmDeviceDashboardMonitor', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11221', 'getEmmDeviceTraffic', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11222', 'findEmmDeviceVariablePage', '/v1/admin/terminal-management/emm-device-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11223', 'createEmmDeviceVariable', '/v1/admin/terminal-management/emm-device-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11224', 'batchDeleteEmmDeviceVariables', '/v1/admin/terminal-management/emm-device-variables/batch/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11225', 'createExportTerminalVariableDownloadTask_1', '/v1/admin/terminal-management/emm-device-variables/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11226', 'importTerminalVariable_1', '/v1/admin/terminal-management/emm-device-variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11227', 'createEmmDeviceVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/emm-device-variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11228', 'updateEmmDeviceVariable', '/v1/admin/terminal-management/emm-device-variables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11229', 'deleteEmmDeviceVariable', '/v1/admin/terminal-management/emm-device-variables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11230', 'searchEmmDevice', '/v1/admin/terminal-management/emm-devices', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11231', 'batchDeleteEmmDevices', '/v1/admin/terminal-management/emm-devices/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11232', 'batchMoveEmmDevices_1', '/v1/admin/terminal-management/emm-devices/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11233', 'createEmmDpcExtras', '/v1/admin/terminal-management/emm-devices/dpcExtrasToken', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11234', 'searchQRCodeEnrollmentRecords', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11235', 'getEmmRegisterQRCode', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode/{enrollmentTokenId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11236', 'disabledEmmRegisterQRCode', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode/{enrollmentTokenId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11237', 'findEmmEnrolledDevices', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode/{enrollmentTokenId}/enrolled-devices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11238', 'searchEmmZteEnrollmentRecords', '/v1/admin/terminal-management/emm-devices/enrollment-records/zte', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11239', 'searchEmmZteDeviceRecords_1', '/v1/admin/terminal-management/emm-devices/enrollment-records/zte/{zteRecordId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11240', 'getEmmZteRecordDetail_1', '/v1/admin/terminal-management/emm-devices/enrollment-records/zte/{zteRecordId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11241', 'createZteFileDownloadTask', '/v1/admin/terminal-management/emm-devices/enrollment-records/{zteRecordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11242', 'rejectEmmZteRecord_1', '/v1/admin/terminal-management/emm-devices/enrollment-records/{zteRecordId}/withdraw', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11243', 'createEmmDevicesExportDownloadTask', '/v1/admin/terminal-management/emm-devices/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11244', 'importEmmDeviceBatchMove', '/v1/admin/terminal-management/emm-devices/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11245', 'createEmmDeviceImportBatchOperationTemplateDownloadTask', '/v1/admin/terminal-management/emm-devices/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11246', 'findEmmDeviceModels', '/v1/admin/terminal-management/emm-devices/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11247', 'createRegisterQRCode', '/v1/admin/terminal-management/emm-devices/register-qrcode/create', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11248', 'submitEmmZteFileUploadRecord', '/v1/admin/terminal-management/emm-devices/zte/file-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11249', 'createEmmZteFileUploadTemplateDownloadTask', '/v1/admin/terminal-management/emm-devices/zte/file-upload/template/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11250', 'submitEmmZteQuickUploadRecord', '/v1/admin/terminal-management/emm-devices/zte/quick-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11251', 'getEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11252', 'updateEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11253', 'deleteEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11254', 'getEmmDeviceAirViewer', '/v1/admin/terminal-management/emm-devices/{deviceId}/air-viewer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11255', 'lockEmmDeviceScreen_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/lockscreen', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11256', 'rebootEmmDevice_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/reboot', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11257', 'resetEmmDevicePassword_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/resetpw', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11258', 'startEmmDeviceLostMode_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/startlost', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11259', 'stopEmmDeviceLostMode_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/stoplost', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11260', 'startAirViewer_1', '/v1/admin/terminal-management/emm-devices/{terminalId}/air-viewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11261', 'findMerchantVariablePage', '/v1/admin/terminal-management/merchant-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11262', 'createMerchantVariable', '/v1/admin/terminal-management/merchant-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11263', 'batchDeleteMerchantVariables', '/v1/admin/terminal-management/merchant-variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11264', 'importMerchantVariable', '/v1/admin/terminal-management/merchant-variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11265', 'createMerchantVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/merchant-variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11266', 'findMerchantVariableSupportedAppPage', '/v1/admin/terminal-management/merchant-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11267', 'findMerchantVariableUsedAppPage', '/v1/admin/terminal-management/merchant-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11268', 'updateMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11269', 'deleteMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11270', 'createMerchant', '/v1/admin/terminal-management/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11271', 'createExportMerchantsDownloadTask', '/v1/admin/terminal-management/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11272', 'importMerchant', '/v1/admin/terminal-management/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11273', 'createMerchantImportTemplateDownloadTask', '/v1/admin/terminal-management/merchants/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11274', 'findSubMerchantPageForOrganization', '/v1/admin/terminal-management/merchants/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11275', 'findSubMerchantPage', '/v1/admin/terminal-management/merchants/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11276', 'getMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11277', 'updateMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11278', 'deleteMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11279', 'activeMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11280', 'disableMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11281', 'moveMerchant', '/v1/admin/terminal-management/merchants/{merchantId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11282', 'getMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11283', 'createMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11284', 'replaceMerchantEmail', '/v1/admin/terminal-management/merchants/{merchantId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11285', 'activeMerchantResendEmail', '/v1/admin/terminal-management/merchants/{merchantId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11286', 'createReseller', '/v1/admin/terminal-management/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11287', 'createExportResellersDownloadTask', '/v1/admin/terminal-management/resellers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11288', 'findResellerPageForOrganization', '/v1/admin/terminal-management/resellers/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11289', 'createTmkImportTemplateDownloadTask', '/v1/admin/terminal-management/resellers/rki/tmk/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11290', 'findSubResellerPage', '/v1/admin/terminal-management/resellers/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11291', 'getResellerDetailVo', '/v1/admin/terminal-management/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11292', 'updateReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11293', 'deleteReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11294', 'activeReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11295', 'disableReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11296', 'moveReseller', '/v1/admin/terminal-management/resellers/{resellerId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11297', 'getResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11298', 'createResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11299', 'replaceResellerEmail', '/v1/admin/terminal-management/resellers/{resellerId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11300', 'activeResellerResendEmail', '/v1/admin/terminal-management/resellers/{resellerId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11301', 'getResellerRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11302', 'refreshResellerRkiKeys', '/v1/admin/terminal-management/resellers/{resellerId}/rki/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11303', 'verifyPushRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki/pre-deduction', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11304', 'findTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11305', 'importTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11306', 'saveResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11307', 'deleteResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11308', 'findTerminalApkPage', '/v1/admin/terminal-management/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11309', 'createTerminalApks', '/v1/admin/terminal-management/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11310', 'createExportApkParameterCompareDownloadTask', '/v1/admin/terminal-management/terminal-apks/export/history/param/comparison', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11311', 'findTerminalApkParamComparePage', '/v1/admin/terminal-management/terminal-apks/history/param/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11312', 'getTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11313', 'deleteTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11314', 'activateTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11315', 'getApkDetailVo', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11316', 'createTerminalApkDataFileDownloadTask', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11317', 'getTerminalApkParam', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11318', 'updateTerminalApkParam_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11319', 'updateTerminalApkParamFormData', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11320', 'findTerminalApkParamVariablePage', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11321', 'saveTerminalApkParamVariables', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11322', 'resetTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11323', 'suspendTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11324', 'findTerminalDetailPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11325', 'findTerminalAuditLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11326', 'getTerminalAuditLogDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log/{auditId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11327', 'getTerminalBatteryInfo', '/v1/admin/terminal-management/terminal-detail/{terminalId}/battery', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11328', 'getCheckUpDetailByTerminalId', '/v1/admin/terminal-management/terminal-detail/{terminalId}/check-up', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11329', 'findTerminalDownloadApkLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/download-apk-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11330', 'createTerminalGeoFenceWhiteList', '/v1/admin/terminal-management/terminal-detail/{terminalId}/geofence/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11331', 'deleteTerminalGeoFenceWhiteList', '/v1/admin/terminal-management/terminal-detail/{terminalId}/geofence/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11332', 'getTerminalInstalledApkStatistics', '/v1/admin/terminal-management/terminal-detail/{terminalId}/installed-apks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11333', 'getTerminalDashboardLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11334', 'refreshTerminalLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11335', 'getTerminalDashboardMonitor', '/v1/admin/terminal-management/terminal-detail/{terminalId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11336', 'getTerminalPedStatus_1', '/v1/admin/terminal-management/terminal-detail/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11337', 'findTerminalPushHistoryPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/push-history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11338', 'refreshTerminalDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11339', 'findTerminalReplacementLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/replace-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11340', 'getTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11341', 'saveTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11342', 'updateTerminalSafeRangeAutoLock', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/auto-lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11343', 'clearTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11344', 'getTerminalTraffic', '/v1/admin/terminal-management/terminal-detail/{terminalId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11345', 'findTerminalFirmwarePage', '/v1/admin/terminal-management/terminal-firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11346', 'createTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11347', 'getTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11348', 'deleteTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11349', 'activateTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11350', 'resetTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11351', 'suspendTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11352', 'findTerminalLauncherPage', '/v1/admin/terminal-management/terminal-launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11353', 'createTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11354', 'getApks', '/v1/admin/terminal-management/terminal-launchers/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11355', 'searchLauncherTemplates', '/v1/admin/terminal-management/terminal-launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11356', 'getLauncherTemplate', '/v1/admin/terminal-management/terminal-launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11357', 'getTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11358', 'deleteTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11359', 'activateTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11360', 'getTerminalLauncherParam', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11361', 'resetTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11362', 'suspendTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11363', 'findTerminalRkiPage', '/v1/admin/terminal-management/terminal-rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11364', 'createTerminalRki', '/v1/admin/terminal-management/terminal-rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11365', 'getTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11366', 'deleteTerminalRKI', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11367', 'activateTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11368', 'resetTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11369', 'suspendTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11370', 'findTerminalVariablePage', '/v1/admin/terminal-management/terminal-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11371', 'createTerminalVariable', '/v1/admin/terminal-management/terminal-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11372', 'batchDeleteTerminalVariables', '/v1/admin/terminal-management/terminal-variables/batch/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11373', 'findTerminalVariableSupportedAppPage', '/v1/admin/terminal-management/terminal-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11374', 'findTerminalVariableUsedAppPage', '/v1/admin/terminal-management/terminal-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11375', 'updateTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11376', 'deleteTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11377', 'createTerminal', '/v1/admin/terminal-management/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11378', 'searchAccessory', '/v1/admin/terminal-management/terminals/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11379', 'createExportTerminalAccessoryDownloadTask', '/v1/admin/terminal-management/terminals/accessories/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11380', 'cancelTerminalAccessoryOperation', '/v1/admin/terminal-management/terminals/accessories/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11381', 'findAccessoryQtyByType', '/v1/admin/terminal-management/terminals/accessories/widgets/W23', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11382', 'findAccessoryQtyByModel', '/v1/admin/terminal-management/terminals/accessories/widgets/W24', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11383', 'findTerminalAccessoryDetail', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11384', 'findTerminalAccessoryDetailPage', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/details', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11385', 'findTerminalAccessoryEvents', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/events', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11386', 'findTerminalAccessoryOperations', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11387', 'pushTerminalActions_1', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11388', 'refreshTerminalAccessory', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11389', 'removeAppointment', '/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11390', 'cancelAppointment', '/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}/canceled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11391', 'createTerminals_1', '/v1/admin/terminal-management/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11392', 'batchActiveTerminals', '/v1/admin/terminal-management/terminals/batch/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11393', 'createGroupTerminals', '/v1/admin/terminal-management/terminals/batch/add-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11394', 'batchDeleteTerminals_1', '/v1/admin/terminal-management/terminals/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11395', 'batchSuspendTerminals', '/v1/admin/terminal-management/terminals/batch/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11396', 'batchMoveTerminals', '/v1/admin/terminal-management/terminals/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11397', 'copyTerminal', '/v1/admin/terminal-management/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11398', 'createExportTerminalsDownloadTask_1', '/v1/admin/terminal-management/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11399', 'createExportTerminalStaticIpConfigDownloadTask', '/v1/admin/terminal-management/terminals/export/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11400', 'createExportTerminalVariableDownloadTask', '/v1/admin/terminal-management/terminals/export/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11401', 'searchGroups', '/v1/admin/terminal-management/terminals/group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11402', 'importTerminal', '/v1/admin/terminal-management/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11403', 'importTerminalBatchActive', '/v1/admin/terminal-management/terminals/import/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11404', 'importTerminalBatchDelete', '/v1/admin/terminal-management/terminals/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11405', 'importTerminalBatchSuspend', '/v1/admin/terminal-management/terminals/import/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11406', 'importTerminalBatchMove', '/v1/admin/terminal-management/terminals/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11407', 'createTerminalImportBatchOperationTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11408', 'importTerminalStaticIpConfig', '/v1/admin/terminal-management/terminals/import/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11409', 'createTerminalStaticIpConfigTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/static-ip/config/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11410', 'createTerminalImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11411', 'importTerminalVariable', '/v1/admin/terminal-management/terminals/import/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11412', 'createTerminalVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/variable/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11413', 'searchTerminal', '/v1/admin/terminal-management/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11414', 'findTerminalAccessTypeList', '/v1/admin/terminal-management/terminals/product-types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11415', 'getTerminalQuickBySn', '/v1/admin/terminal-management/terminals/quick/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11416', 'getTerminalStockBySerialNo', '/v1/admin/terminal-management/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11417', 'findModelPage', '/v1/admin/terminal-management/terminals/support-models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11418', 'getTerminalBasicVoByTidOrSerialNo', '/v1/admin/terminal-management/terminals/tid-sn/{tidOrSN}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11419', 'getTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11420', 'updateTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11421', 'deleteTerminal_3', '/v1/admin/terminal-management/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11422', 'findTerminalAccessoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11423', 'activeTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11424', 'getTerminalAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11425', 'getAppointmentPage', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11426', 'createAppointment', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11427', 'checkCheckUpVersion', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/check/checkup/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11428', 'pushInstallCheckup', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/checkup/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11429', 'pushInstallAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11430', 'startAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11431', 'disableTerminal_2', '/v1/admin/terminal-management/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11432', 'findTerminalInstalledApkPage', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11433', 'getTerminalInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11434', 'createParameterDataFileDownloadTask_1', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11435', 'getInstalledApkParam', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11436', 'uninstallInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11437', 'getTerminalInstalledFirmware', '/v1/admin/terminal-management/terminals/{terminalId}/installed-firmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11438', 'changeTerminalModel', '/v1/admin/terminal-management/terminals/{terminalId}/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11439', 'moveTerminal', '/v1/admin/terminal-management/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11440', 'getPukPushStatus', '/v1/admin/terminal-management/terminals/{terminalId}/puk/push/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11441', 'cancelTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/cancel/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11442', 'collectTerminalLogcat', '/v1/admin/terminal-management/terminals/{terminalId}/setting/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11443', 'findTerminalSystemConfigPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/configs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11444', 'disablePushPukTerminalAction', '/v1/admin/terminal-management/terminals/{terminalId}/setting/disable/puk/push', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11445', 'getTerminalLocationEnable', '/v1/admin/terminal-management/terminals/{terminalId}/setting/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11446', 'findTerminalLogPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11447', 'createTerminalLogDownloadTask', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs/{terminalLogId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11448', 'findTerminalNetworkConfigurationList', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11449', 'deleteTerminalNetworkConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11450', 'getProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11451', 'saveTerminalProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11452', 'deleteTerminalProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11453', 'findPukPushHistoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11454', 'getSignaturePuk', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11455', 'pushTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/push/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11456', 'updateTerminalRemoteConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11457', 'findTerminalStockPage', '/v1/admin/terminal-stocks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11458', 'createTerminals', '/v1/admin/terminal-stocks/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11459', 'assignTerminals', '/v1/admin/terminal-stocks/batch/assign', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11460', 'batchDeleteTerminals', '/v1/admin/terminal-stocks/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11461', 'createExportStockTerminalsDownloadTask', '/v1/admin/terminal-stocks/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11462', 'importStockTerminal', '/v1/admin/terminal-stocks/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11463', 'createStockTerminalImportTemplateDownloadTask', '/v1/admin/terminal-stocks/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11464', 'getTerminalStock', '/v1/admin/terminal-stocks/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11465', 'updateTerminal', '/v1/admin/terminal-stocks/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11466', 'deleteTerminal_2', '/v1/admin/terminal-stocks/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11467', 'searchUsers', '/v1/admin/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11468', 'createExportUserDownloadTask', '/v1/admin/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11469', 'searchRoleList', '/v1/admin/users/role-all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11470', 'getUser_3', '/v1/admin/users/{userId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11471', 'deleteUser', '/v1/admin/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11472', 'activeUser', '/v1/admin/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11473', 'sendActivateUserEmail', '/v1/admin/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11474', 'changeEmail', '/v1/admin/users/{userId}/change-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11475', 'disableUser', '/v1/admin/users/{userId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11476', 'findUserRoles', '/v1/admin/users/{userId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11477', 'resetUserPassword', '/v1/admin/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11478', 'deleteUserRoles', '/v1/admin/users/{userId}/roles', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11479', 'deleteUserRole', '/v1/admin/users/{userId}/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11480', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11481', 'getThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11482', 'updateThirdpartyAppSys', '/v1/admin/vas/3rdsys/app', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11483', 'createThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11484', 'exportDetectionSummary', '/v1/admin/vas/air-shield/attestation/history/{terminalId}/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11485', 'clearHistory', '/v1/admin/vas/air-shield/attestation/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11486', 'findDetectionHistoryPage_1', '/v1/admin/vas/air-shield/attestation/{terminalId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11487', 'listPosviewerFileTransferInfo', '/v1/admin/vas/air-viewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11488', 'getModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11489', 'updateModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11490', 'listPosviewerOperationInfo', '/v1/admin/vas/air-viewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11491', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11492', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11493', 'searchEmmZteRecords', '/v1/admin/zte', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11494', 'getPendingEmmZteRecordCount', '/v1/admin/zte/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11495', 'searchEmmZteDeviceRecords', '/v1/admin/zte/{zteRecordId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11496', 'approveEmmZteRecord', '/v1/admin/zte/{zteRecordId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11497', 'getEmmZteRecordDetail', '/v1/admin/zte/{zteRecordId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11498', 'downloadEmmZteRecordFile', '/v1/admin/zte/{zteRecordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11499', 'rejectEmmZteRecord', '/v1/admin/zte/{zteRecordId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11500', 'getDashBoard', '/v1/app_scan/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11501', 'getEngineBlacklist', '/v1/app_scan/engine/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11502', 'updateEngineBlacklist', '/v1/app_scan/engine/blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11503', 'getHistoricalUsage', '/v1/app_scan/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11504', 'getAvailableScanEngineList', '/v1/app_scan/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11505', 'rescan', '/v1/app_scan/rescan', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11506', 'getResultFile', '/v1/app_scan/resultZip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11507', 'getScanResult', '/v1/app_scan/results', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11508', 'isCreateTaskPermitted', '/v1/app_scan/scanned', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11509', 'getSetting', '/v1/app_scan/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11510', 'updateSetting', '/v1/app_scan/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11511', 'createScanTask', '/v1/app_scan/task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11512', 'deleteScanTask', '/v1/app_scan/task/{scanTaskId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11513', 'getUsage', '/v1/app_scan/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11514', 'createBuriedPoints_1', '/v1/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11515', 'queryNavigoAssistant', '/v1/common/assistant', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11516', 'evaluateData', '/v1/common/assistant/qa/evaluate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11517', 'activateUser', '/v1/common/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11518', 'validateActivate1', '/v1/common/auth/activation/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11519', 'destroySsoToken', '/v1/common/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11520', 'resetEmail', '/v1/common/auth/email-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11521', 'validateResetEmail1', '/v1/common/auth/email-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11522', 'validateExtraction', '/v1/common/auth/extraction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11523', 'validateDownloadLink', '/v1/common/auth/extraction/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11524', 'getMarketDc', '/v1/common/auth/market/dc', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11525', 'disableOtpByBackupCode', '/v1/common/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11526', 'validateDisableCode', '/v1/common/auth/otp/disable-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11527', 'sendDisableOtpMail', '/v1/common/auth/otp/reset-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11528', 'forgetPwd', '/v1/common/auth/password-forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11529', 'resetPwd', '/v1/common/auth/password-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11530', 'validateResetPwd1', '/v1/common/auth/password-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11531', 'checkTokenExpire', '/v1/common/auth/ping', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11532', 'reactivateUser', '/v1/common/auth/reactivate-user', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11533', 'sendReactivateUserMail', '/v1/common/auth/reactivate-user-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11534', 'registerUser', '/v1/common/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11535', 'findCodes', '/v1/common/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11536', 'getCodes', '/v1/common/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11537', 'getDocSetting', '/v1/common/doc-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11538', 'download2_1', '/v1/common/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11539', 'createLatestOnlineClientApkDownloadTask', '/v1/common/download/client-app/latest/client', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11540', 'download1_1', '/v1/common/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11541', 'getDownloadUrl_1', '/v1/common/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11542', 'getCurrentEnv', '/v1/common/env', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11543', 'getFile', '/v1/common/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11544', 'findFooter', '/v1/common/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11545', 'getFooter', '/v1/common/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11546', 'getExternalUser', '/v1/common/is-external-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11547', 'getLangList', '/v1/common/languages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11548', 'getLicense', '/v1/common/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11549', 'initMessageStats', '/v1/common/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11550', 'readTopXMessages', '/v1/common/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11551', 'readMessage_1', '/v1/common/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11552', 'viewMessageDetails_1', '/v1/common/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11553', 'uploadTerminalGroupApkParamFile', '/v1/common/param/data/upload', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11554', 'getAllPasswordValidatorPolicyFailureDetail', '/v1/common/password-rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11555', 'getSystemConfig', '/v1/common/system-config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11556', 'getUserAgreement_1', '/v1/common/user-agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11557', 'agreeUserAgreement_1', '/v1/common/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11558', 'getCurrentUserAgreementAgreed', '/v1/common/users/agreement/agreed', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11559', 'generateCaptcha_1', '/v1/common/users/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11560', 'verifyCaptcha', '/v1/common/users/captcha/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11561', 'getCurrentUserRouterSwitchList', '/v1/common/users/routers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11562', 'createDeveloper', '/v1/developer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11563', 'activeDeveloper3rdSysAccess', '/v1/developer/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11564', 'getDeveloper3rdSysConfig', '/v1/developer/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11565', 'deActiveDeveloper3rdSysAccess', '/v1/developer/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11566', 'getDeveloper3rdSysAccessSecret', '/v1/developer/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11567', 'resetDeveloper3rdSysAccess', '/v1/developer/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11568', 'getDeveloperAccountVo', '/v1/developer/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11569', 'agreeUserAgreement', '/v1/developer/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11570', 'updateApk_1', '/v1/developer/apks/emm/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11571', 'deleteApk_2', '/v1/developer/apks/emm/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11572', 'getApkEditDetail_1', '/v1/developer/apks/emm/{apkId}/apk-edit', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11573', 'updateApkFile_1', '/v1/developer/apks/emm/{apkId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11574', 'createOriginalApkDownloadTask_1', '/v1/developer/apks/emm/{apkId}/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11575', 'updateApk', '/v1/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11576', 'deleteApk_1', '/v1/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11577', 'getApkEditDetail', '/v1/developer/apks/{apkId}/apk-edit', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11578', 'updateApkFile', '/v1/developer/apks/{apkId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11579', 'uploadApkAttachment', '/v1/developer/apks/{apkId}/attachment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11580', 'createOriginalApkDownloadTask', '/v1/developer/apks/{apkId}/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11581', 'offlineApk_1', '/v1/developer/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11582', 'uploadApkParamTemplate', '/v1/developer/apks/{apkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11583', 'deleteApkParamTemplate_1', '/v1/developer/apks/{apkId}/param', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11584', 'findCustomParamTemplate', '/v1/developer/apks/{apkId}/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11585', 'analysisDevParamTemplate', '/v1/developer/apks/{apkId}/param-templates/analysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11586', 'createParameterDataFileDownloadTask', '/v1/developer/apks/{apkId}/param-templates/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11587', 'createApkParamTemplateDownloadTask_1', '/v1/developer/apks/{apkId}/param-templates/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11588', 'getApkParamTemplateSchema', '/v1/developer/apks/{apkId}/param-templates/schema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11589', 'submitApk', '/v1/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11590', 'searchApps_1', '/v1/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11591', 'createApp', '/v1/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11592', 'getAppDetail_2', '/v1/developer/apps/emm/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11593', 'deleteApp_2', '/v1/developer/apps/emm/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11594', 'addApkFile_1', '/v1/developer/apps/emm/{appId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11595', 'searchApks_1', '/v1/developer/apps/emm/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11596', 'getApkDetail_2', '/v1/developer/apps/emm/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11597', 'updateAppKey_1', '/v1/developer/apps/emm/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11598', 'getAppTracks', '/v1/developer/apps/emm/{appId}/tracks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11599', 'getDeveloperAppSummary', '/v1/developer/apps/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11600', 'getAppDetail_1', '/v1/developer/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11601', 'deleteApp_1', '/v1/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11602', 'addApkFile', '/v1/developer/apps/{appId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11603', 'searchApks', '/v1/developer/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11604', 'getApkDetail_1', '/v1/developer/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11605', 'updateAppKey', '/v1/developer/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11606', 'findSolutionAppUsage', '/v1/developer/apps/{appId}/industry-solution/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11607', 'findSolutionAppUsagePeriod', '/v1/developer/apps/{appId}/industry-solution/usage/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11608', 'getBizDataFromGoInsight', '/v1/developer/apps/{appId}/sandbox/insight-data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11609', 'getDeveloperBalance', '/v1/developer/balance', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11610', 'findDeveloperTransactionList', '/v1/developer/balance/transactions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11611', 'withdrawalDeveloperBalance', '/v1/developer/balance/withdrawal', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11612', 'getDeveloper_1', '/v1/developer/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11613', 'getUser_2', '/v1/developer/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11614', 'validateUserEmail', '/v1/developer/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11615', 'findFactoryNameList', '/v1/developer/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11616', 'findFactoryModelList', '/v1/developer/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11617', 'applyIndustrySolution', '/v1/developer/industry-solution/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11618', 'getMarket_2', '/v1/developer/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11619', 'findEnterpriseDevelopers', '/v1/developer/members', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11620', 'addEnterpriseDeveloper', '/v1/developer/members', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11621', 'deleteEnterpriseDeveloper', '/v1/developer/members/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11622', 'updateAdminDeveloper', '/v1/developer/members/{userId}/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11623', 'updateDeveloperSuperAdmin_1', '/v1/developer/members/{userId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11624', 'updateUserDeveloper', '/v1/developer/members/{userId}/user', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11625', 'searchCustomParamTemplate', '/v1/developer/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11626', 'createParamTemplate', '/v1/developer/param-templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11627', 'searchAppName', '/v1/developer/param-templates/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11628', 'searchCustomParamTemplate_1', '/v1/developer/param-templates/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11629', 'getDevParamTemplate', '/v1/developer/param-templates/{templateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11630', 'updateParameterSchema', '/v1/developer/param-templates/{templateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11631', 'deleteCustomParamTemplate', '/v1/developer/param-templates/{templateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11632', 'cloneParamTemplate', '/v1/developer/param-templates/{templateId}/clone', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11633', 'createApkParamTemplateDownloadPoFilesTask', '/v1/developer/param-templates/{templateId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11634', 'updateParamTemplateName', '/v1/developer/param-templates/{templateId}/name', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11635', 'getParamTemplateSchema', '/v1/developer/param-templates/{templateId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11636', 'uploadDevParamTemplate', '/v1/developer/param-templates/{templateId}/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11637', 'initDeveloperPayment', '/v1/developer/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11638', 'checkout', '/v1/developer/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11639', 'payDeveloperOffline', '/v1/developer/payment/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11640', 'findAppPageForSandBox', '/v1/developer/sandbox-data/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11641', 'searchSandboxTerminal', '/v1/developer/sandbox-terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11642', 'createSandboxTerminal', '/v1/developer/sandbox-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11643', 'searchFactory', '/v1/developer/sandbox-terminals/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11644', 'getSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11645', 'updateSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11646', 'deleteTerminal_1', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11647', 'isSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11648', 'updateSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11649', 'searchSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11650', 'createSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11651', 'getApkDetail', '/v1/developer/sandbox/terminal-apks/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11652', 'getSandboxTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11653', 'deleteTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11654', 'activateTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11655', 'createTerminalApkDataDownloadTask', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11656', 'updateTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/param-template-name', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11657', 'getSandboxTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11658', 'updateTerminalApkParam_2', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11659', 'resetTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11660', 'getUserAgreement', '/v1/developer/user/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11661', 'getValueAddServiceSummaryVo', '/v1/developer/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11662', 'findVasAgreedAgreements', '/v1/developer/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11663', 'findDeveloperAppForVas', '/v1/developer/vas/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11664', 'clearAppCloudMessagesData', '/v1/developer/vas/clear-data', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11665', 'confirmConnectDialog', '/v1/developer/vas/confirm/connect/dialog', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11666', 'listAppMsg', '/v1/developer/vas/msg', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11667', 'addAppMsg', '/v1/developer/vas/msg', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11668', 'getTerminalInstalledAppCount', '/v1/developer/vas/msg/app/{appId}/installed-terminal-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11669', 'findAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11670', 'createAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11671', 'deleteAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}/tags/{tagId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11672', 'uploadMsgTemplate', '/v1/developer/vas/msg/template/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11673', 'validateUrl', '/v1/developer/vas/msg/template/validation-img-url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11674', 'getMsgById', '/v1/developer/vas/msg/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11675', 'getMsgStatusById', '/v1/developer/vas/msg/{id}/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11676', 'logicDeleteMessage', '/v1/developer/vas/msg/{msgId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11677', 'disableMessage', '/v1/developer/vas/msg/{msgId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11678', 'getMsgReport', '/v1/developer/vas/msg/{msgId}/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11679', 'showConnectDialog', '/v1/developer/vas/show/connect/dialog', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11680', 'findStatisticsTypes', '/v1/developer/vas/statistics/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11681', 'getVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11682', 'agreeVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11683', 'serviceApply', '/v1/developer/vas/{serviceType}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11684', 'findHistoryUsageByServiceType', '/v1/developer/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11685', 'getUsageDashboardByServiceType', '/v1/developer/vas/{serviceType}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11686', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11687', 'getLatestOnlineApkList', '/v1/internal/appUpdate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11688', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11689', 'getApps_1', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11690', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11691', 'searchAdGroup', '/v1/marketAdmin/vas/adup/ad-group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11692', 'createAdGroup', '/v1/marketAdmin/vas/adup/ad-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11693', 'findAdGroupSlotPage', '/v1/marketAdmin/vas/adup/ad-group/ad-slot', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11694', 'updateAdGroupVisual', '/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adGroupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11695', 'findAdGroupVisualPage', '/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adSlotId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11696', 'getAdGroupDetail', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11697', 'updateAdGroup', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11698', 'removeAdGroup', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11699', 'updateAdGroupStatus', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11700', 'createAdSlot', '/v1/marketAdmin/vas/adup/ad-slot', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11701', 'findAdGroupModels', '/v1/marketAdmin/vas/adup/ad-slot/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11702', 'findAdSlotSpecList', '/v1/marketAdmin/vas/adup/ad-slot/specs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11703', 'deleteAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11704', 'activateAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/activate', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11705', 'disableAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11706', 'findAdSlots', '/v1/marketAdmin/vas/adup/ad-slots', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11707', 'createAdVisual', '/v1/marketAdmin/vas/adup/ad-visual', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11708', 'retrieveAdVisualMaxDuration', '/v1/marketAdmin/vas/adup/ad-visual/maxDuration', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11709', 'findAdVisualPage', '/v1/marketAdmin/vas/adup/ad-visuals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11710', 'createVasAgreement', '/v1/marketAdmin/vas/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11711', 'publishVasAgreement', '/v1/marketAdmin/vas/agreement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11712', 'updateVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11713', 'deleteVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11714', 'downloadVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11715', 'findVasAgreements', '/v1/marketAdmin/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11716', 'getEstateList', '/v1/marketAdmin/vas/air-link/estates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11717', 'createEstate', '/v1/marketAdmin/vas/air-link/estates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11718', 'deleteEstate', '/v1/marketAdmin/vas/air-link/estates', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11719', 'moveEstate', '/v1/marketAdmin/vas/air-link/estates/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11720', 'createAirLinkTerminalImportTemplateDownloadTask_1', '/v1/marketAdmin/vas/air-link/estates/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11721', 'updateEstate', '/v1/marketAdmin/vas/air-link/estates/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11722', 'exportAirLinkEstate', '/v1/marketAdmin/vas/air-link/export/estates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11723', 'importAirLinkEstate', '/v1/marketAdmin/vas/air-link/import/estates/{marketId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11724', 'readPlanById', '/v1/marketAdmin/vas/air-link/plan/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11725', 'getRechargeConfig', '/v1/marketAdmin/vas/air-link/recharge/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11726', 'updateRechargeConfig', '/v1/marketAdmin/vas/air-link/recharge/config/{minRecharge}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11727', 'getAirLinkTerminalList', '/v1/marketAdmin/vas/air-link/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11728', 'getAirLinkTerminalActiveHistoryList', '/v1/marketAdmin/vas/air-link/terminals/activate-histories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11729', 'exportTerminalActivateDetail_1', '/v1/marketAdmin/vas/air-link/terminals/activate-histories/{id}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11730', 'addAirLinkTerminal', '/v1/marketAdmin/vas/air-link/terminals/add', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11731', 'getDashboardStatistics', '/v1/marketAdmin/vas/air-link/terminals/dashboard-statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11732', 'delete', '/v1/marketAdmin/vas/air-link/terminals/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11733', 'getDeletedAirLinkTerminalList', '/v1/marketAdmin/vas/air-link/terminals/deleted', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11734', 'disable', '/v1/marketAdmin/vas/air-link/terminals/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11735', 'importAirLinkTerminal', '/v1/marketAdmin/vas/air-link/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11736', 'createAirLinkTerminalImportTemplateDownloadTask', '/v1/marketAdmin/vas/air-link/terminals/import/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11737', 'getAirLinkTerminalOperatorList', '/v1/marketAdmin/vas/air-link/terminals/operators', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11738', 'resume', '/v1/marketAdmin/vas/air-link/terminals/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11739', 'getConsumptionStatistics', '/v1/marketAdmin/vas/air-link/terminals/{id}/consumption-statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11740', 'getAirLinkTerminalDetail', '/v1/marketAdmin/vas/air-link/terminals/{id}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11741', 'getAirLinkTerminalProfileList', '/v1/marketAdmin/vas/air-link/terminals/{id}/profiles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11742', 'switchAirLinkTerminalProfile', '/v1/marketAdmin/vas/air-link/terminals/{id}/switch-profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11743', 'overdueOrResumeAirLinkByMarketId', '/v1/marketAdmin/vas/air-link/{marketId}/overdue', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11744', 'importAirLoadCards', '/v1/marketAdmin/vas/air-load/card/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11745', 'createAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11746', 'getAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11747', 'updateAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11748', 'deleteAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11749', 'getAirLoadCardPoolMaximumInfo', '/v1/marketAdmin/vas/air-load/card/pool/maximum', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11750', 'findAirLoadCardPoolResellers', '/v1/marketAdmin/vas/air-load/card/pool/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11751', 'updateAirLoadCardPoolMaximumInfo', '/v1/marketAdmin/vas/air-load/card/pool/{maximum}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11752', 'findAirLoadCardPools', '/v1/marketAdmin/vas/air-load/card/pools', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11753', 'downloadAirLoadCardTemplate', '/v1/marketAdmin/vas/air-load/card/template/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11754', 'deleteAirLoadCard', '/v1/marketAdmin/vas/air-load/card/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11755', 'removeAirLoadCard', '/v1/marketAdmin/vas/air-load/card/{id}/remove', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11756', 'batchDeleteAirLoadCard', '/v1/marketAdmin/vas/air-load/cards/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11757', 'batchRemoveAirLoadCards', '/v1/marketAdmin/vas/air-load/cards/batch/remove', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11758', 'findAirLoadCardsByPoolId', '/v1/marketAdmin/vas/air-load/cards/{cardPoolId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11759', 'getActiveTaskList', '/v1/marketAdmin/vas/air-load/terminals/active-tasks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11760', 'getAirLoadTerminalList', '/v1/marketAdmin/vas/air-load/terminals/active-tasks/{activeTaskId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11761', 'addAirLoadTerminal', '/v1/marketAdmin/vas/air-load/terminals/add', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11762', 'checkTerminalImport', '/v1/marketAdmin/vas/air-load/terminals/check-import', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11763', 'importAirLoadTerminal', '/v1/marketAdmin/vas/air-load/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11764', 'createAirLoadTerminalImportTemplateDownloadTask', '/v1/marketAdmin/vas/air-load/terminals/import/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11765', 'getTerminalImportStatus', '/v1/marketAdmin/vas/air-load/terminals/terminal-import-status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11766', 'exportTerminalActivateDetail', '/v1/marketAdmin/vas/air-load/terminals/{activeTaskId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11767', 'findAppBlackListPage', '/v1/marketAdmin/vas/airShield/app-black-list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11768', 'addAppBlackList', '/v1/marketAdmin/vas/airShield/app-black-list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11769', 'updateAppBlack', '/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11770', 'deleteBlackApp', '/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11771', 'getAttestationInterval', '/v1/marketAdmin/vas/airShield/interval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11772', 'changeInterval', '/v1/marketAdmin/vas/airShield/interval', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11773', 'findSysFileAccessPage', '/v1/marketAdmin/vas/airShield/sys-file-access', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11774', 'addRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11775', 'updateRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11776', 'deleteRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11777', 'getAirViewerCurrentUsage', '/v1/marketAdmin/vas/airViewer/currentUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11778', 'getAirViewerCurrentUsageDashBoard', '/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11779', 'getAirViewerHistoricalUsage', '/v1/marketAdmin/vas/airViewer/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11780', 'getOrderList', '/v1/marketAdmin/vas/airlink/orders', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11781', 'getRechargeInfo', '/v1/marketAdmin/vas/airlink/orders/recharge', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11782', 'recharge', '/v1/marketAdmin/vas/airlink/orders/recharge', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11783', 'getOrderDetail', '/v1/marketAdmin/vas/airlink/orders/{id}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11784', 'exportAirviewerUsage', '/v1/marketAdmin/vas/airviewer/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11785', 'exportAppScanUsage', '/v1/marketAdmin/vas/appscan/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11786', 'getMarketServiceBillingSetting', '/v1/marketAdmin/vas/billingSetting/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11787', 'findCyberLabTerminalBlacklistPage', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11788', 'deleteCyberLabTerminalBlacklist', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11789', 'getGoogleEmmConfig', '/v1/marketAdmin/vas/emm/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11790', 'updateEmmConfig', '/v1/marketAdmin/vas/emm/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11791', 'getEmmEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11792', 'updateEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11793', 'createEmmEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11794', 'getEmmPrivateApps', '/v1/marketAdmin/vas/emm/privateApps/top3', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11795', 'getEmmSignupUrl', '/v1/marketAdmin/vas/emm/signup/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11796', 'disableZTEAutoAudit4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/auto-audit/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11797', 'enableZTEAutoAudit4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/auto-audit/enable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11798', 'createZTECustomer4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/customer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11799', 'enableZTE4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/enabled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11800', 'exportDetailZipByServiceType', '/v1/marketAdmin/vas/export/detail/zip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11801', 'exportSummaryByServiceType', '/v1/marketAdmin/vas/export/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11802', 'getInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11803', 'changeInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11804', 'searchMarkets', '/v1/marketAdmin/vas/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11805', 'exportMarkets', '/v1/marketAdmin/vas/markets/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11806', 'getCloudMessageTrialCount', '/v1/marketAdmin/vas/markets/{marketId}/trial/msg/count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11807', 'getResellerVasByReseller', '/v1/marketAdmin/vas/reseller/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11808', 'findVasServices', '/v1/marketAdmin/vas/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11809', 'showCurrentMonthUsage', '/v1/marketAdmin/vas/services/show', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11810', 'updateActiveStatus', '/v1/marketAdmin/vas/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11811', 'unsubscribeService', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11812', 'subscribeService', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11813', 'findServiceResellerSpecificPage', '/v1/marketAdmin/vas/services/{serviceType}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11814', 'specificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11815', 'deleteSpecificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11816', 'findSubscribeHistoryPage', '/v1/marketAdmin/vas/subscriptionHistory/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11817', 'loadCurrentEnrollTerminalBill', '/v1/marketAdmin/vas/terminal/enroll/bill', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11818', 'loadCurrentEnrollTerminalDashBoard', '/v1/marketAdmin/vas/terminal/enroll/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11819', 'loadEnrollTerminalHistory', '/v1/marketAdmin/vas/terminal/enroll/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11820', 'loadEnrollTerminalHistoryDetail', '/v1/marketAdmin/vas/terminal/enroll/history/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11821', 'downloadCurrentEnrollTerminal', '/v1/marketAdmin/vas/terminal/enroll/{marketId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11822', 'updateMarketDevServiceStatus', '/v1/marketAdmin/vas/{developerId}/service/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11823', 'getVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11824', 'agreeVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11825', 'findCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11826', 'searchMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11827', 'exportCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/export/current/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11828', 'exportMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/export/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11829', 'exportHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11830', 'findHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11831', 'getUsageDashBoard', '/v1/marketAdmin/vas/{serviceType}/usage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11832', 'getUser_1', '/v1/merchant/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11833', 'findPurchaseAppList', '/v1/merchant/dashboard/purchased-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11834', 'loadWidgetModelTerminal_1', '/v1/merchant/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11835', 'loadWidgetTerminalOffline_1', '/v1/merchant/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11836', 'loadMerchantPortalWidget', '/v1/merchant/dashboard/widgets/W21', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11837', 'getMarket_1', '/v1/merchant/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11838', 'getAgreementSystem', '/v1/mobile/account/agreement/system', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11839', 'getAgreement', '/v1/mobile/account/agreement/{agreementId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11840', 'changeLoginType', '/v1/mobile/account/login/type/{operation}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11841', 'listMessages', '/v1/mobile/account/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11842', 'readAllMessage', '/v1/mobile/account/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11843', 'getMessagesStats', '/v1/mobile/account/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11844', 'readMessage', '/v1/mobile/account/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11845', 'deleteMessage', '/v1/mobile/account/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11846', 'viewMessageDetails', '/v1/mobile/account/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11847', 'mobileAppAdmin', '/v1/mobile/account/mobile-app-admin', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11848', 'synMobileMessageToken', '/v1/mobile/account/mobile-message-token', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11849', 'getAccountProfile', '/v1/mobile/account/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11850', 'createUserAgreement', '/v1/mobile/account/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11851', 'validateOneTimePassword', '/v1/mobile/account/verify-otp', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11852', 'currentUser', '/v1/mobile/admin/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11853', 'getUserMarkets', '/v1/mobile/admin/user/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11854', 'getUserMarketResellers', '/v1/mobile/admin/user/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11855', 'getWorkspace', '/v1/mobile/admin/workspace', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11856', 'searchAlarm', '/v1/mobile/alarm', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11857', 'searchAlarm_1', '/v1/mobile/alarm/app-sign-failed', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11858', 'getAlarmWidgets', '/v1/mobile/alarm/widgets/digital', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11859', 'getApps', '/v1/mobile/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11860', 'getApkInfo', '/v1/mobile/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11861', 'createApkDownloadTask', '/v1/mobile/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11862', 'createApkParamTemplateDownloadTask', '/v1/mobile/apps/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11863', 'deleteApkParamTemplate', '/v1/mobile/apps/apks/{apkId}/param-template', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11864', 'reSignApk', '/v1/mobile/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11865', 'findApkSignatureList', '/v1/mobile/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11866', 'getAppInfo', '/v1/mobile/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11867', 'deleteApp', '/v1/mobile/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11868', 'activeApp', '/v1/mobile/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11869', 'searchApk', '/v1/mobile/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11870', 'deleteApk', '/v1/mobile/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11871', 'approveApp', '/v1/mobile/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11872', 'offlineApk', '/v1/mobile/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11873', 'onlineApk', '/v1/mobile/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11874', 'rejectApp', '/v1/mobile/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11875', 'disableApp', '/v1/mobile/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11876', 'createBuriedPoints', '/v1/mobile/buried-points', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11877', 'getMobileAppVersion', '/v1/mobile/common/app-version', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11878', 'generateCaptcha', '/v1/mobile/common/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11879', 'forgetPassword', '/v1/mobile/common/forget-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11880', 'searchPendingApps', '/v1/mobile/dashboard/apps-pending', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11881', 'searchAppsTop10', '/v1/mobile/dashboard/apps-top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11882', 'getUrl', '/v1/mobile/dashboard/cloud_data/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11883', 'getLayout', '/v1/mobile/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11884', 'searchMarkers', '/v1/mobile/dashboard/map-markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11885', 'getTerminalNumberStatisticData', '/v1/mobile/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11886', 'getTerminalNumberOfResellerData', '/v1/mobile/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11887', 'getFmTerminalForWidget', '/v1/mobile/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11888', 'exportFmTerminalOrgWidget', '/v1/mobile/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11889', 'getClientTerminalWidget', '/v1/mobile/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11890', 'exportClientTerminalWidget', '/v1/mobile/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11891', 'loadWidgetModelTerminal', '/v1/mobile/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11892', 'exportModelTerminalWidget', '/v1/mobile/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11893', 'loadWidgetTerminalOffline', '/v1/mobile/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11894', 'exportTerminalOfflineWidget', '/v1/mobile/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11895', 'getFmTerminalWidget', '/v1/mobile/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11896', 'exportFmTerminalWidget', '/v1/mobile/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11897', 'loadClientTerminalWidget', '/v1/mobile/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11898', 'downloadClientTerminalWidget', '/v1/mobile/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11899', 'createExportTerminalsDownloadTask', '/v1/mobile/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11900', 'getWidgetCardNumberActive', '/v1/mobile/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11901', 'getWidgetDigitalDisplaySetting', '/v1/mobile/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11902', 'getPukTerminalWidget', '/v1/mobile/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11903', 'exportPUKTerminalWidget', '/v1/mobile/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11904', 'getDevelopers', '/v1/mobile/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11905', 'getDeveloper', '/v1/mobile/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11906', 'deleteDeveloper', '/v1/mobile/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11907', 'approveDeveloper', '/v1/mobile/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11908', 'rejectDeveloper', '/v1/mobile/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11909', 'resumeDeveloper', '/v1/mobile/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11910', 'updateDeveloperSuperAdmin', '/v1/mobile/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11911', 'suspendDeveloper', '/v1/mobile/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11912', 'download2', '/v1/mobile/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11913', 'download1', '/v1/mobile/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11914', 'getDownloadUrl', '/v1/mobile/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11915', 'searchList', '/v1/mobile/terminal/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11916', 'searchSingle', '/v1/mobile/terminal/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11917', 'getTerminal', '/v1/mobile/terminal/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11918', 'deleteTerminal', '/v1/mobile/terminal/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11919', 'activeTerminal', '/v1/mobile/terminal/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11920', 'disableTerminal_1', '/v1/mobile/terminal/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11921', 'lockTerminal', '/v1/mobile/terminal/{terminalId}/lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11922', 'getTerminalPedStatus', '/v1/mobile/terminal/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11923', 'unlockTerminal', '/v1/mobile/terminal/{terminalId}/unlock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11924', 'findDetectionHistoryPage', '/v1/mobile/vas/air-shield/attestation/{terminalId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11925', 'createNavigoBuriedPoints', '/v1/navigo/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11926', 'searchApps', '/v1/portal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11927', 'getAdvertisement', '/v1/portal/apps/advertisement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11928', 'searchOnlineAppCategories', '/v1/portal/apps/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11929', 'findDeveloperApp', '/v1/portal/apps/developer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11930', 'findFeaturedApp', '/v1/portal/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11931', 'getAppDetailByPackageName', '/v1/portal/apps/packageName', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11932', 'searchAppsRank', '/v1/portal/apps/rank', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11933', 'findRelatedApp', '/v1/portal/apps/related', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11934', 'getAppTypes', '/v1/portal/apps/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11935', 'getAppDetail', '/v1/portal/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11936', 'getUser', '/v1/portal/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11937', 'getMarket', '/v1/portal/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11938', 'sysVersion', '/v1/public/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '11555'),
  ('1', '11558'),
  ('1', '11560'),
  ('1', '11561'),

  ('2', '10447'),
  ('2', '11506'),
  ('2', '11507'),
  ('2', '11539'),
  ('2', '11553'),
  ('2', '11563'),
  ('2', '11564'),
  ('2', '11565'),
  ('2', '11566'),
  ('2', '11567'),
  ('2', '11568'),
  ('2', '11569'),
  ('2', '11570'),
  ('2', '11571'),
  ('2', '11572'),
  ('2', '11573'),
  ('2', '11574'),
  ('2', '11575'),
  ('2', '11576'),
  ('2', '11577'),
  ('2', '11578'),
  ('2', '11579'),
  ('2', '11580'),
  ('2', '11581'),
  ('2', '11582'),
  ('2', '11583'),
  ('2', '11584'),
  ('2', '11585'),
  ('2', '11586'),
  ('2', '11587'),
  ('2', '11588'),
  ('2', '11589'),
  ('2', '11590'),
  ('2', '11591'),
  ('2', '11592'),
  ('2', '11593'),
  ('2', '11594'),
  ('2', '11595'),
  ('2', '11596'),
  ('2', '11597'),
  ('2', '11598'),
  ('2', '11599'),
  ('2', '11600'),
  ('2', '11601'),
  ('2', '11602'),
  ('2', '11603'),
  ('2', '11604'),
  ('2', '11605'),
  ('2', '11606'),
  ('2', '11607'),
  ('2', '11608'),
  ('2', '11609'),
  ('2', '11610'),
  ('2', '11611'),
  ('2', '11615'),
  ('2', '11616'),
  ('2', '11617'),
  ('2', '11619'),
  ('2', '11620'),
  ('2', '11621'),
  ('2', '11622'),
  ('2', '11623'),
  ('2', '11624'),
  ('2', '11625'),
  ('2', '11626'),
  ('2', '11627'),
  ('2', '11628'),
  ('2', '11629'),
  ('2', '11630'),
  ('2', '11631'),
  ('2', '11632'),
  ('2', '11633'),
  ('2', '11634'),
  ('2', '11635'),
  ('2', '11636'),
  ('2', '11637'),
  ('2', '11638'),
  ('2', '11639'),
  ('2', '11640'),
  ('2', '11641'),
  ('2', '11642'),
  ('2', '11643'),
  ('2', '11644'),
  ('2', '11645'),
  ('2', '11646'),
  ('2', '11647'),
  ('2', '11648'),
  ('2', '11649'),
  ('2', '11650'),
  ('2', '11651'),
  ('2', '11652'),
  ('2', '11653'),
  ('2', '11654'),
  ('2', '11655'),
  ('2', '11656'),
  ('2', '11657'),
  ('2', '11658'),
  ('2', '11659'),
  ('2', '11660'),
  ('2', '11661'),
  ('2', '11662'),
  ('2', '11663'),
  ('2', '11664'),
  ('2', '11665'),
  ('2', '11666'),
  ('2', '11667'),
  ('2', '11668'),
  ('2', '11669'),
  ('2', '11670'),
  ('2', '11671'),
  ('2', '11672'),
  ('2', '11673'),
  ('2', '11674'),
  ('2', '11675'),
  ('2', '11676'),
  ('2', '11677'),
  ('2', '11678'),
  ('2', '11679'),
  ('2', '11680'),
  ('2', '11681'),
  ('2', '11682'),
  ('2', '11683'),
  ('2', '11684'),
  ('2', '11685'),

  ('3', '10845'),
  ('3', '10846'),
  ('3', '10847'),
  ('3', '10848'),
  ('3', '10849'),
  ('3', '10850'),
  ('3', '10851'),
  ('3', '10852'),
  ('3', '10853'),
  ('3', '10854'),
  ('3', '10855'),
  ('3', '10856'),
  ('3', '10857'),
  ('3', '10858'),
  ('3', '10859'),
  ('3', '10860'),
  ('3', '10861'),
  ('3', '10862'),
  ('3', '10863'),
  ('3', '10864'),
  ('3', '10865'),
  ('3', '10866'),
  ('3', '10867'),
  ('3', '10868'),
  ('3', '10869'),
  ('3', '10870'),
  ('3', '10871'),
  ('3', '10872'),
  ('3', '10873'),
  ('3', '10874'),

  ('20', '11833'),
  ('20', '11834'),
  ('20', '11835'),
  ('20', '11836'),

  ('51', '10447'),
  ('51', '10462'),
  ('51', '10465'),
  ('51', '10466'),
  ('51', '10467'),
  ('51', '10468'),
  ('51', '10469'),
  ('51', '10470'),
  ('51', '10471'),
  ('51', '10472'),
  ('51', '10473'),
  ('51', '10474'),
  ('51', '10475'),
  ('51', '10476'),
  ('51', '10477'),
  ('51', '10478'),
  ('51', '10479'),
  ('51', '10480'),
  ('51', '10481'),
  ('51', '10482'),
  ('51', '10483'),
  ('51', '10484'),
  ('51', '10485'),
  ('51', '10486'),
  ('51', '10487'),
  ('51', '10488'),
  ('51', '10489'),
  ('51', '10490'),
  ('51', '10491'),
  ('51', '11539'),
  ('51', '11880'),
  ('51', '11881'),
  ('51', '11882'),
  ('51', '11883'),
  ('51', '11884'),
  ('51', '11885'),
  ('51', '11886'),
  ('51', '11887'),
  ('51', '11888'),
  ('51', '11889'),
  ('51', '11890'),
  ('51', '11891'),
  ('51', '11892'),
  ('51', '11893'),
  ('51', '11894'),
  ('51', '11895'),
  ('51', '11896'),
  ('51', '11897'),
  ('51', '11898'),
  ('51', '11899'),
  ('51', '11900'),
  ('51', '11901'),
  ('51', '11902'),
  ('51', '11903'),

  ('53', '10586'),
  ('53', '10587'),
  ('53', '10588'),
  ('53', '10589'),
  ('53', '10590'),
  ('53', '10591'),
  ('53', '10592'),
  ('53', '10593'),
  ('53', '10594'),

  ('1041', '10323'),
  ('1041', '10324'),
  ('1041', '10326'),
  ('1041', '10327'),
  ('1041', '10330'),
  ('1041', '10331'),
  ('1041', '10332'),
  ('1041', '10449'),
  ('1041', '10461'),
  ('1041', '10462'),
  ('1041', '11856'),
  ('1041', '11857'),
  ('1041', '11858'),
  ('1041', '11924'),

  ('1042', '10323'),
  ('1042', '10324'),
  ('1042', '10325'),
  ('1042', '10326'),
  ('1042', '10327'),
  ('1042', '10328'),
  ('1042', '10329'),
  ('1042', '10330'),
  ('1042', '10331'),
  ('1042', '10332'),
  ('1042', '10449'),
  ('1042', '10461'),
  ('1042', '10462'),
  ('1042', '11856'),
  ('1042', '11857'),
  ('1042', '11858'),
  ('1042', '11924'),

  ('601', '10449'),
  ('601', '10453'),
  ('601', '10456'),
  ('601', '10457'),
  ('601', '10461'),
  ('601', '10462'),
  ('601', '11014'),
  ('601', '11015'),
  ('601', '11016'),
  ('601', '11019'),
  ('601', '11021'),
  ('601', '11022'),
  ('601', '11023'),
  ('601', '11025'),
  ('601', '11027'),
  ('601', '11028'),
  ('601', '11031'),
  ('601', '11034'),
  ('601', '11035'),
  ('601', '11036'),
  ('601', '11037'),
  ('601', '11038'),
  ('601', '11039'),
  ('601', '11043'),
  ('601', '11044'),
  ('601', '11045'),
  ('601', '11046'),
  ('601', '11504'),
  ('601', '11506'),
  ('601', '11507'),
  ('601', '11508'),

  ('602', '10449'),
  ('602', '10453'),
  ('602', '10456'),
  ('602', '10457'),
  ('602', '10461'),
  ('602', '10462'),
  ('602', '11014'),
  ('602', '11015'),
  ('602', '11016'),
  ('602', '11017'),
  ('602', '11018'),
  ('602', '11019'),
  ('602', '11020'),
  ('602', '11021'),
  ('602', '11022'),
  ('602', '11023'),
  ('602', '11024'),
  ('602', '11025'),
  ('602', '11026'),
  ('602', '11027'),
  ('602', '11028'),
  ('602', '11029'),
  ('602', '11030'),
  ('602', '11031'),
  ('602', '11032'),
  ('602', '11033'),
  ('602', '11034'),
  ('602', '11035'),
  ('602', '11036'),
  ('602', '11037'),
  ('602', '11038'),
  ('602', '11039'),
  ('602', '11040'),
  ('602', '11041'),
  ('602', '11042'),
  ('602', '11043'),
  ('602', '11044'),
  ('602', '11045'),
  ('602', '11046'),
  ('602', '11504'),
  ('602', '11505'),
  ('602', '11506'),
  ('602', '11507'),
  ('602', '11508'),
  ('602', '11511'),
  ('602', '11512'),

  ('611', '10337'),
  ('611', '10338'),
  ('611', '10339'),
  ('611', '10340'),
  ('611', '10343'),
  ('611', '10344'),
  ('611', '10346'),
  ('611', '10349'),
  ('611', '10351'),
  ('611', '10352'),
  ('611', '10353'),
  ('611', '10354'),
  ('611', '10355'),
  ('611', '10356'),
  ('611', '10357'),
  ('611', '10360'),
  ('611', '10371'),
  ('611', '10379'),
  ('611', '10381'),
  ('611', '10384'),
  ('611', '10385'),
  ('611', '10388'),
  ('611', '10391'),
  ('611', '10393'),
  ('611', '10394'),
  ('611', '10445'),
  ('611', '10449'),
  ('611', '10456'),
  ('611', '10457'),
  ('611', '10461'),
  ('611', '10462'),
  ('611', '10524'),
  ('611', '10525'),
  ('611', '10530'),
  ('611', '10532'),
  ('611', '10534'),
  ('611', '10539'),
  ('611', '10540'),
  ('611', '10542'),
  ('611', '10544'),
  ('611', '11504'),
  ('611', '11506'),
  ('611', '11507'),
  ('611', '11508'),
  ('611', '11859'),
  ('611', '11860'),
  ('611', '11861'),
  ('611', '11862'),
  ('611', '11865'),
  ('611', '11866'),
  ('611', '11869'),

  ('612', '10337'),
  ('612', '10338'),
  ('612', '10339'),
  ('612', '10340'),
  ('612', '10341'),
  ('612', '10342'),
  ('612', '10343'),
  ('612', '10344'),
  ('612', '10345'),
  ('612', '10346'),
  ('612', '10347'),
  ('612', '10348'),
  ('612', '10349'),
  ('612', '10350'),
  ('612', '10351'),
  ('612', '10352'),
  ('612', '10353'),
  ('612', '10354'),
  ('612', '10355'),
  ('612', '10356'),
  ('612', '10357'),
  ('612', '10358'),
  ('612', '10359'),
  ('612', '10360'),
  ('612', '10361'),
  ('612', '10362'),
  ('612', '10363'),
  ('612', '10364'),
  ('612', '10365'),
  ('612', '10366'),
  ('612', '10367'),
  ('612', '10368'),
  ('612', '10369'),
  ('612', '10370'),
  ('612', '10371'),
  ('612', '10372'),
  ('612', '10373'),
  ('612', '10374'),
  ('612', '10375'),
  ('612', '10376'),
  ('612', '10377'),
  ('612', '10378'),
  ('612', '10379'),
  ('612', '10380'),
  ('612', '10381'),
  ('612', '10382'),
  ('612', '10383'),
  ('612', '10384'),
  ('612', '10385'),
  ('612', '10386'),
  ('612', '10387'),
  ('612', '10388'),
  ('612', '10389'),
  ('612', '10390'),
  ('612', '10391'),
  ('612', '10392'),
  ('612', '10393'),
  ('612', '10394'),
  ('612', '10395'),
  ('612', '10445'),
  ('612', '10449'),
  ('612', '10456'),
  ('612', '10457'),
  ('612', '10461'),
  ('612', '10462'),
  ('612', '10524'),
  ('612', '10525'),
  ('612', '10526'),
  ('612', '10527'),
  ('612', '10528'),
  ('612', '10529'),
  ('612', '10530'),
  ('612', '10531'),
  ('612', '10532'),
  ('612', '10533'),
  ('612', '10534'),
  ('612', '10535'),
  ('612', '10536'),
  ('612', '10537'),
  ('612', '10538'),
  ('612', '10539'),
  ('612', '10540'),
  ('612', '10541'),
  ('612', '10542'),
  ('612', '10544'),
  ('612', '10545'),
  ('612', '10546'),
  ('612', '10547'),
  ('612', '11504'),
  ('612', '11505'),
  ('612', '11506'),
  ('612', '11507'),
  ('612', '11508'),
  ('612', '11511'),
  ('612', '11512'),
  ('612', '11859'),
  ('612', '11860'),
  ('612', '11861'),
  ('612', '11862'),
  ('612', '11863'),
  ('612', '11864'),
  ('612', '11865'),
  ('612', '11866'),
  ('612', '11867'),
  ('612', '11868'),
  ('612', '11869'),
  ('612', '11870'),
  ('612', '11871'),
  ('612', '11872'),
  ('612', '11873'),
  ('612', '11874'),
  ('612', '11875'),

  ('661', '10449'),
  ('661', '10453'),
  ('661', '10456'),
  ('661', '10461'),
  ('661', '10462'),
  ('661', '10961'),
  ('661', '10962'),
  ('661', '10963'),
  ('661', '10964'),
  ('661', '10965'),
  ('661', '10967'),

  ('662', '10449'),
  ('662', '10453'),
  ('662', '10456'),
  ('662', '10461'),
  ('662', '10462'),
  ('662', '10961'),
  ('662', '10962'),
  ('662', '10963'),
  ('662', '10964'),
  ('662', '10965'),
  ('662', '10966'),
  ('662', '10967'),

  ('621', '10337'),
  ('621', '10338'),
  ('621', '10339'),
  ('621', '10340'),
  ('621', '10343'),
  ('621', '10344'),
  ('621', '10346'),
  ('621', '10349'),
  ('621', '10351'),
  ('621', '10352'),
  ('621', '10353'),
  ('621', '10354'),
  ('621', '10355'),
  ('621', '10356'),
  ('621', '10357'),
  ('621', '10360'),
  ('621', '10371'),
  ('621', '10379'),
  ('621', '10381'),
  ('621', '10384'),
  ('621', '10385'),
  ('621', '10388'),
  ('621', '10391'),
  ('621', '10393'),
  ('621', '10394'),
  ('621', '10449'),
  ('621', '10456'),
  ('621', '10461'),
  ('621', '10462'),
  ('621', '10501'),
  ('621', '10502'),
  ('621', '10503'),
  ('621', '10517'),
  ('621', '10519'),
  ('621', '10523'),
  ('621', '10524'),
  ('621', '10525'),
  ('621', '10530'),
  ('621', '10531'),
  ('621', '10532'),
  ('621', '10534'),
  ('621', '10539'),
  ('621', '10540'),
  ('621', '10542'),
  ('621', '10543'),
  ('621', '10544'),
  ('621', '11504'),
  ('621', '11506'),
  ('621', '11507'),
  ('621', '11508'),
  ('621', '11904'),
  ('621', '11905'),

  ('622', '10337'),
  ('622', '10338'),
  ('622', '10339'),
  ('622', '10340'),
  ('622', '10343'),
  ('622', '10344'),
  ('622', '10346'),
  ('622', '10349'),
  ('622', '10351'),
  ('622', '10352'),
  ('622', '10353'),
  ('622', '10354'),
  ('622', '10355'),
  ('622', '10356'),
  ('622', '10357'),
  ('622', '10360'),
  ('622', '10371'),
  ('622', '10379'),
  ('622', '10381'),
  ('622', '10384'),
  ('622', '10385'),
  ('622', '10388'),
  ('622', '10391'),
  ('622', '10393'),
  ('622', '10394'),
  ('622', '10449'),
  ('622', '10456'),
  ('622', '10461'),
  ('622', '10462'),
  ('622', '10501'),
  ('622', '10502'),
  ('622', '10503'),
  ('622', '10504'),
  ('622', '10505'),
  ('622', '10506'),
  ('622', '10507'),
  ('622', '10508'),
  ('622', '10509'),
  ('622', '10510'),
  ('622', '10511'),
  ('622', '10512'),
  ('622', '10513'),
  ('622', '10514'),
  ('622', '10515'),
  ('622', '10516'),
  ('622', '10517'),
  ('622', '10518'),
  ('622', '10519'),
  ('622', '10520'),
  ('622', '10521'),
  ('622', '10522'),
  ('622', '10523'),
  ('622', '10524'),
  ('622', '10525'),
  ('622', '10530'),
  ('622', '10531'),
  ('622', '10532'),
  ('622', '10534'),
  ('622', '10539'),
  ('622', '10540'),
  ('622', '10542'),
  ('622', '10543'),
  ('622', '10544'),
  ('622', '11504'),
  ('622', '11506'),
  ('622', '11507'),
  ('622', '11508'),
  ('622', '11904'),
  ('622', '11905'),
  ('622', '11906'),
  ('622', '11907'),
  ('622', '11908'),
  ('622', '11909'),
  ('622', '11910'),
  ('622', '11911'),

  ('641', '11493'),
  ('641', '11494'),
  ('641', '11495'),
  ('641', '11497'),
  ('641', '11498'),

  ('642', '11493'),
  ('642', '11494'),
  ('642', '11495'),
  ('642', '11496'),
  ('642', '11497'),
  ('642', '11498'),
  ('642', '11499'),

  ('651', '10449'),
  ('651', '10457'),
  ('651', '10461'),
  ('651', '10462'),
  ('651', '10524'),
  ('651', '10525'),
  ('651', '10530'),
  ('651', '10532'),
  ('651', '10534'),
  ('651', '10539'),
  ('651', '10540'),
  ('651', '10542'),
  ('651', '10544'),
  ('651', '10940'),
  ('651', '10941'),
  ('651', '10943'),
  ('651', '10944'),
  ('651', '10947'),
  ('651', '10948'),
  ('651', '10949'),
  ('651', '10950'),
  ('651', '10951'),
  ('651', '10952'),
  ('651', '10953'),
  ('651', '10954'),
  ('651', '10955'),
  ('651', '10956'),
  ('651', '10959'),
  ('651', '10960'),
  ('651', '11507'),
  ('651', '11508'),

  ('652', '10449'),
  ('652', '10457'),
  ('652', '10461'),
  ('652', '10462'),
  ('652', '10524'),
  ('652', '10525'),
  ('652', '10526'),
  ('652', '10527'),
  ('652', '10528'),
  ('652', '10529'),
  ('652', '10530'),
  ('652', '10531'),
  ('652', '10532'),
  ('652', '10533'),
  ('652', '10534'),
  ('652', '10535'),
  ('652', '10536'),
  ('652', '10537'),
  ('652', '10538'),
  ('652', '10539'),
  ('652', '10540'),
  ('652', '10541'),
  ('652', '10542'),
  ('652', '10544'),
  ('652', '10545'),
  ('652', '10546'),
  ('652', '10547'),
  ('652', '10940'),
  ('652', '10941'),
  ('652', '10942'),
  ('652', '10943'),
  ('652', '10944'),
  ('652', '10945'),
  ('652', '10946'),
  ('652', '10947'),
  ('652', '10948'),
  ('652', '10949'),
  ('652', '10950'),
  ('652', '10951'),
  ('652', '10952'),
  ('652', '10953'),
  ('652', '10954'),
  ('652', '10955'),
  ('652', '10956'),
  ('652', '10957'),
  ('652', '10958'),
  ('652', '10959'),
  ('652', '10960'),
  ('652', '11507'),
  ('652', '11508'),

  ('671', '10333'),
  ('671', '10335'),
  ('671', '10453'),
  ('671', '10637'),
  ('671', '11057'),
  ('671', '11058'),
  ('671', '11061'),
  ('671', '11105'),
  ('671', '11108'),
  ('671', '11164'),
  ('671', '11165'),
  ('671', '11168'),
  ('671', '11212'),
  ('671', '11541'),

  ('672', '10333'),
  ('672', '10334'),
  ('672', '10335'),
  ('672', '10336'),
  ('672', '10453'),
  ('672', '10637'),
  ('672', '11057'),
  ('672', '11058'),
  ('672', '11061'),
  ('672', '11105'),
  ('672', '11108'),
  ('672', '11164'),
  ('672', '11165'),
  ('672', '11168'),
  ('672', '11212'),
  ('672', '11541'),

  ('711', '10438'),
  ('711', '10439'),
  ('711', '10441'),
  ('711', '10443'),
  ('711', '10444'),
  ('711', '10445'),
  ('711', '10446'),
  ('711', '10448'),
  ('711', '10449'),
  ('711', '10452'),
  ('711', '10453'),
  ('711', '10455'),
  ('711', '10457'),
  ('711', '10459'),
  ('711', '10461'),
  ('711', '10462'),
  ('711', '10524'),
  ('711', '10539'),
  ('711', '10543'),
  ('711', '10548'),
  ('711', '10551'),
  ('711', '10637'),
  ('711', '11057'),
  ('711', '11058'),
  ('711', '11061'),
  ('711', '11105'),
  ('711', '11108'),
  ('711', '11213'),
  ('711', '11214'),
  ('711', '11215'),
  ('711', '11216'),
  ('711', '11217'),
  ('711', '11218'),
  ('711', '11219'),
  ('711', '11220'),
  ('711', '11221'),
  ('711', '11222'),
  ('711', '11225'),
  ('711', '11230'),
  ('711', '11234'),
  ('711', '11235'),
  ('711', '11237'),
  ('711', '11238'),
  ('711', '11239'),
  ('711', '11240'),
  ('711', '11243'),
  ('711', '11246'),
  ('711', '11251'),
  ('711', '11254'),
  ('711', '11261'),
  ('711', '11266'),
  ('711', '11267'),
  ('711', '11271'),
  ('711', '11274'),
  ('711', '11275'),
  ('711', '11276'),
  ('711', '11282'),
  ('711', '11287'),
  ('711', '11288'),
  ('711', '11290'),
  ('711', '11291'),
  ('711', '11297'),
  ('711', '11301'),
  ('711', '11304'),
  ('711', '11308'),
  ('711', '11310'),
  ('711', '11311'),
  ('711', '11312'),
  ('711', '11315'),
  ('711', '11316'),
  ('711', '11317'),
  ('711', '11320'),
  ('711', '11324'),
  ('711', '11325'),
  ('711', '11326'),
  ('711', '11327'),
  ('711', '11328'),
  ('711', '11329'),
  ('711', '11332'),
  ('711', '11333'),
  ('711', '11334'),
  ('711', '11335'),
  ('711', '11336'),
  ('711', '11337'),
  ('711', '11338'),
  ('711', '11339'),
  ('711', '11340'),
  ('711', '11344'),
  ('711', '11345'),
  ('711', '11347'),
  ('711', '11352'),
  ('711', '11354'),
  ('711', '11355'),
  ('711', '11356'),
  ('711', '11357'),
  ('711', '11360'),
  ('711', '11363'),
  ('711', '11365'),
  ('711', '11370'),
  ('711', '11373'),
  ('711', '11374'),
  ('711', '11378'),
  ('711', '11379'),
  ('711', '11381'),
  ('711', '11382'),
  ('711', '11383'),
  ('711', '11384'),
  ('711', '11385'),
  ('711', '11386'),
  ('711', '11388'),
  ('711', '11398'),
  ('711', '11399'),
  ('711', '11400'),
  ('711', '11401'),
  ('711', '11413'),
  ('711', '11414'),
  ('711', '11415'),
  ('711', '11416'),
  ('711', '11417'),
  ('711', '11418'),
  ('711', '11419'),
  ('711', '11422'),
  ('711', '11424'),
  ('711', '11425'),
  ('711', '11427'),
  ('711', '11428'),
  ('711', '11429'),
  ('711', '11430'),
  ('711', '11432'),
  ('711', '11433'),
  ('711', '11434'),
  ('711', '11435'),
  ('711', '11437'),
  ('711', '11440'),
  ('711', '11443'),
  ('711', '11445'),
  ('711', '11446'),
  ('711', '11447'),
  ('711', '11448'),
  ('711', '11450'),
  ('711', '11453'),
  ('711', '11454'),
  ('711', '11484'),
  ('711', '11486'),
  ('711', '11487'),
  ('711', '11490'),
  ('711', '11915'),
  ('711', '11916'),
  ('711', '11917'),
  ('711', '11922'),

  ('712', '10438'),
  ('712', '10439'),
  ('712', '10441'),
  ('712', '10443'),
  ('712', '10444'),
  ('712', '10445'),
  ('712', '10446'),
  ('712', '10448'),
  ('712', '10449'),
  ('712', '10452'),
  ('712', '10453'),
  ('712', '10455'),
  ('712', '10457'),
  ('712', '10459'),
  ('712', '10461'),
  ('712', '10462'),
  ('712', '10524'),
  ('712', '10539'),
  ('712', '10543'),
  ('712', '10548'),
  ('712', '10551'),
  ('712', '10637'),
  ('712', '11057'),
  ('712', '11058'),
  ('712', '11061'),
  ('712', '11105'),
  ('712', '11108'),
  ('712', '11213'),
  ('712', '11214'),
  ('712', '11215'),
  ('712', '11216'),
  ('712', '11217'),
  ('712', '11218'),
  ('712', '11219'),
  ('712', '11220'),
  ('712', '11221'),
  ('712', '11222'),
  ('712', '11225'),
  ('712', '11230'),
  ('712', '11234'),
  ('712', '11235'),
  ('712', '11237'),
  ('712', '11238'),
  ('712', '11239'),
  ('712', '11240'),
  ('712', '11243'),
  ('712', '11246'),
  ('712', '11251'),
  ('712', '11254'),
  ('712', '11261'),
  ('712', '11266'),
  ('712', '11267'),
  ('712', '11271'),
  ('712', '11274'),
  ('712', '11275'),
  ('712', '11276'),
  ('712', '11282'),
  ('712', '11287'),
  ('712', '11288'),
  ('712', '11290'),
  ('712', '11291'),
  ('712', '11297'),
  ('712', '11301'),
  ('712', '11304'),
  ('712', '11308'),
  ('712', '11310'),
  ('712', '11311'),
  ('712', '11312'),
  ('712', '11315'),
  ('712', '11316'),
  ('712', '11317'),
  ('712', '11320'),
  ('712', '11324'),
  ('712', '11325'),
  ('712', '11326'),
  ('712', '11327'),
  ('712', '11328'),
  ('712', '11329'),
  ('712', '11332'),
  ('712', '11333'),
  ('712', '11334'),
  ('712', '11335'),
  ('712', '11336'),
  ('712', '11337'),
  ('712', '11338'),
  ('712', '11339'),
  ('712', '11340'),
  ('712', '11344'),
  ('712', '11345'),
  ('712', '11347'),
  ('712', '11352'),
  ('712', '11354'),
  ('712', '11355'),
  ('712', '11356'),
  ('712', '11357'),
  ('712', '11360'),
  ('712', '11363'),
  ('712', '11365'),
  ('712', '11370'),
  ('712', '11373'),
  ('712', '11374'),
  ('712', '11378'),
  ('712', '11379'),
  ('712', '11381'),
  ('712', '11382'),
  ('712', '11383'),
  ('712', '11384'),
  ('712', '11385'),
  ('712', '11386'),
  ('712', '11388'),
  ('712', '11389'),
  ('712', '11390'),
  ('712', '11398'),
  ('712', '11399'),
  ('712', '11400'),
  ('712', '11401'),
  ('712', '11413'),
  ('712', '11414'),
  ('712', '11415'),
  ('712', '11416'),
  ('712', '11417'),
  ('712', '11418'),
  ('712', '11419'),
  ('712', '11422'),
  ('712', '11424'),
  ('712', '11425'),
  ('712', '11427'),
  ('712', '11428'),
  ('712', '11429'),
  ('712', '11430'),
  ('712', '11432'),
  ('712', '11433'),
  ('712', '11434'),
  ('712', '11435'),
  ('712', '11437'),
  ('712', '11440'),
  ('712', '11443'),
  ('712', '11445'),
  ('712', '11446'),
  ('712', '11447'),
  ('712', '11448'),
  ('712', '11450'),
  ('712', '11453'),
  ('712', '11454'),
  ('712', '11484'),
  ('712', '11485'),
  ('712', '11486'),
  ('712', '11487'),
  ('712', '11490'),
  ('712', '11915'),
  ('712', '11916'),
  ('712', '11917'),
  ('712', '11922'),

  ('7124', '11345'),
  ('7124', '11346'),
  ('7124', '11347'),
  ('7124', '11348'),
  ('7124', '11349'),
  ('7124', '11350'),
  ('7124', '11351'),

  ('7126', '11352'),
  ('7126', '11353'),
  ('7126', '11354'),
  ('7126', '11355'),
  ('7126', '11356'),
  ('7126', '11357'),
  ('7126', '11358'),
  ('7126', '11359'),
  ('7126', '11360'),
  ('7126', '11361'),
  ('7126', '11362'),

  ('71211', '11286'),
  ('71211', '11287'),
  ('71211', '11288'),
  ('71211', '11289'),
  ('71211', '11290'),
  ('71211', '11291'),
  ('71211', '11292'),
  ('71211', '11293'),
  ('71211', '11294'),
  ('71211', '11295'),
  ('71211', '11296'),
  ('71211', '11297'),
  ('71211', '11299'),
  ('71211', '11300'),
  ('71211', '11301'),
  ('71211', '11302'),
  ('71211', '11303'),
  ('71211', '11304'),
  ('71211', '11305'),
  ('71211', '11306'),
  ('71211', '11307'),

  ('71213', '11270'),
  ('71213', '11271'),
  ('71213', '11272'),
  ('71213', '11273'),
  ('71213', '11274'),
  ('71213', '11275'),
  ('71213', '11276'),
  ('71213', '11277'),
  ('71213', '11278'),
  ('71213', '11279'),
  ('71213', '11280'),
  ('71213', '11281'),
  ('71213', '11282'),
  ('71213', '11284'),
  ('71213', '11285'),

  ('71212', '10552'),
  ('71212', '10553'),
  ('71212', '11298'),

  ('71214', '10549'),
  ('71214', '10550'),
  ('71214', '11283'),

  ('71221', '11230'),
  ('71221', '11231'),
  ('71221', '11232'),
  ('71221', '11233'),
  ('71221', '11234'),
  ('71221', '11235'),
  ('71221', '11236'),
  ('71221', '11237'),
  ('71221', '11238'),
  ('71221', '11239'),
  ('71221', '11240'),
  ('71221', '11241'),
  ('71221', '11242'),
  ('71221', '11243'),
  ('71221', '11244'),
  ('71221', '11245'),
  ('71221', '11246'),
  ('71221', '11247'),
  ('71221', '11248'),
  ('71221', '11249'),
  ('71221', '11250'),
  ('71221', '11251'),
  ('71221', '11252'),
  ('71221', '11253'),
  ('71221', '11254'),
  ('71221', '11255'),
  ('71221', '11256'),
  ('71221', '11257'),
  ('71221', '11258'),
  ('71221', '11259'),
  ('71221', '11260'),
  ('71221', '11377'),
  ('71221', '11378'),
  ('71221', '11379'),
  ('71221', '11380'),
  ('71221', '11381'),
  ('71221', '11382'),
  ('71221', '11383'),
  ('71221', '11384'),
  ('71221', '11385'),
  ('71221', '11386'),
  ('71221', '11387'),
  ('71221', '11388'),
  ('71221', '11389'),
  ('71221', '11390'),
  ('71221', '11391'),
  ('71221', '11392'),
  ('71221', '11393'),
  ('71221', '11394'),
  ('71221', '11395'),
  ('71221', '11396'),
  ('71221', '11397'),
  ('71221', '11398'),
  ('71221', '11399'),
  ('71221', '11400'),
  ('71221', '11401'),
  ('71221', '11402'),
  ('71221', '11403'),
  ('71221', '11404'),
  ('71221', '11405'),
  ('71221', '11406'),
  ('71221', '11407'),
  ('71221', '11408'),
  ('71221', '11409'),
  ('71221', '11410'),
  ('71221', '11411'),
  ('71221', '11412'),
  ('71221', '11413'),
  ('71221', '11414'),
  ('71221', '11415'),
  ('71221', '11416'),
  ('71221', '11417'),
  ('71221', '11418'),
  ('71221', '11419'),
  ('71221', '11420'),
  ('71221', '11421'),
  ('71221', '11422'),
  ('71221', '11423'),
  ('71221', '11424'),
  ('71221', '11425'),
  ('71221', '11426'),
  ('71221', '11427'),
  ('71221', '11428'),
  ('71221', '11429'),
  ('71221', '11430'),
  ('71221', '11431'),
  ('71221', '11432'),
  ('71221', '11433'),
  ('71221', '11434'),
  ('71221', '11435'),
  ('71221', '11436'),
  ('71221', '11437'),
  ('71221', '11438'),
  ('71221', '11439'),
  ('71221', '11440'),
  ('71221', '11441'),
  ('71221', '11442'),
  ('71221', '11443'),
  ('71221', '11444'),
  ('71221', '11445'),
  ('71221', '11446'),
  ('71221', '11447'),
  ('71221', '11448'),
  ('71221', '11449'),
  ('71221', '11450'),
  ('71221', '11451'),
  ('71221', '11452'),
  ('71221', '11453'),
  ('71221', '11454'),
  ('71221', '11455'),
  ('71221', '11456'),
  ('71221', '11915'),
  ('71221', '11916'),
  ('71221', '11917'),
  ('71221', '11918'),
  ('71221', '11919'),
  ('71221', '11920'),
  ('71221', '11922'),

  ('71222', '11324'),
  ('71222', '11325'),
  ('71222', '11326'),
  ('71222', '11327'),
  ('71222', '11328'),
  ('71222', '11329'),
  ('71222', '11330'),
  ('71222', '11331'),
  ('71222', '11332'),
  ('71222', '11333'),
  ('71222', '11334'),
  ('71222', '11335'),
  ('71222', '11336'),
  ('71222', '11337'),
  ('71222', '11338'),
  ('71222', '11339'),
  ('71222', '11340'),
  ('71222', '11341'),
  ('71222', '11342'),
  ('71222', '11343'),
  ('71222', '11344'),
  ('71222', '11408'),
  ('71222', '11409'),
  ('71222', '11441'),
  ('71222', '11442'),
  ('71222', '11443'),
  ('71222', '11444'),
  ('71222', '11445'),
  ('71222', '11446'),
  ('71222', '11447'),
  ('71222', '11448'),
  ('71222', '11449'),
  ('71222', '11450'),
  ('71222', '11451'),
  ('71222', '11452'),
  ('71222', '11453'),
  ('71222', '11454'),
  ('71222', '11455'),
  ('71222', '11456'),
  ('71222', '11921'),
  ('71222', '11923'),

  ('71231', '11308'),
  ('71231', '11309'),
  ('71231', '11310'),
  ('71231', '11311'),
  ('71231', '11312'),
  ('71231', '11313'),
  ('71231', '11314'),
  ('71231', '11315'),
  ('71231', '11316'),
  ('71231', '11317'),
  ('71231', '11320'),
  ('71231', '11322'),
  ('71231', '11323'),

  ('71232', '11222'),
  ('71232', '11223'),
  ('71232', '11224'),
  ('71232', '11225'),
  ('71232', '11226'),
  ('71232', '11227'),
  ('71232', '11228'),
  ('71232', '11229'),
  ('71232', '11261'),
  ('71232', '11262'),
  ('71232', '11263'),
  ('71232', '11264'),
  ('71232', '11265'),
  ('71232', '11266'),
  ('71232', '11267'),
  ('71232', '11268'),
  ('71232', '11269'),
  ('71232', '11308'),
  ('71232', '11309'),
  ('71232', '11310'),
  ('71232', '11311'),
  ('71232', '11312'),
  ('71232', '11313'),
  ('71232', '11314'),
  ('71232', '11315'),
  ('71232', '11316'),
  ('71232', '11317'),
  ('71232', '11318'),
  ('71232', '11319'),
  ('71232', '11320'),
  ('71232', '11321'),
  ('71232', '11322'),
  ('71232', '11323'),
  ('71232', '11370'),
  ('71232', '11371'),
  ('71232', '11372'),
  ('71232', '11373'),
  ('71232', '11374'),
  ('71232', '11375'),
  ('71232', '11376'),
  ('71232', '11411'),
  ('71232', '11412'),
  ('71232', '11553'),

  ('71233', '11436'),

  ('71251', '10460'),
  ('71251', '11363'),
  ('71251', '11364'),
  ('71251', '11365'),
  ('71251', '11366'),
  ('71251', '11367'),
  ('71251', '11368'),
  ('71251', '11369'),

  ('71252', '10460'),
  ('71252', '11301'),
  ('71252', '11302'),
  ('71252', '11303'),
  ('71252', '11304'),
  ('71252', '11305'),
  ('71252', '11306'),
  ('71252', '11307'),

  ('721', '10439'),
  ('721', '10441'),
  ('721', '10443'),
  ('721', '10444'),
  ('721', '10448'),
  ('721', '10449'),
  ('721', '10452'),
  ('721', '10453'),
  ('721', '10457'),
  ('721', '10459'),
  ('721', '10461'),
  ('721', '10462'),
  ('721', '10637'),
  ('721', '11047'),
  ('721', '11049'),
  ('721', '11053'),
  ('721', '11057'),
  ('721', '11058'),
  ('721', '11061'),
  ('721', '11065'),
  ('721', '11067'),
  ('721', '11068'),
  ('721', '11073'),
  ('721', '11074'),
  ('721', '11075'),
  ('721', '11077'),
  ('721', '11080'),
  ('721', '11089'),
  ('721', '11090'),
  ('721', '11093'),
  ('721', '11095'),
  ('721', '11096'),
  ('721', '11097'),
  ('721', '11098'),
  ('721', '11101'),
  ('721', '11105'),
  ('721', '11108'),
  ('721', '11110'),
  ('721', '11114'),
  ('721', '11115'),
  ('721', '11116'),
  ('721', '11118'),
  ('721', '11126'),
  ('721', '11127'),
  ('721', '11128'),
  ('721', '11130'),
  ('721', '11131'),
  ('721', '11139'),
  ('721', '11140'),
  ('721', '11141'),
  ('721', '11143'),
  ('721', '11144'),
  ('721', '11152'),
  ('721', '11153'),
  ('721', '11154'),
  ('721', '11156'),
  ('721', '11157'),
  ('721', '11160'),
  ('721', '11164'),
  ('721', '11165'),
  ('721', '11168'),
  ('721', '11172'),
  ('721', '11174'),
  ('721', '11175'),
  ('721', '11180'),
  ('721', '11181'),
  ('721', '11182'),
  ('721', '11184'),
  ('721', '11186'),
  ('721', '11194'),
  ('721', '11195'),
  ('721', '11196'),
  ('721', '11201'),
  ('721', '11202'),
  ('721', '11205'),
  ('721', '11212'),

  ('722', '10439'),
  ('722', '10441'),
  ('722', '10443'),
  ('722', '10444'),
  ('722', '10448'),
  ('722', '10449'),
  ('722', '10452'),
  ('722', '10453'),
  ('722', '10457'),
  ('722', '10459'),
  ('722', '10461'),
  ('722', '10462'),
  ('722', '10637'),
  ('722', '11047'),
  ('722', '11049'),
  ('722', '11053'),
  ('722', '11057'),
  ('722', '11058'),
  ('722', '11061'),
  ('722', '11065'),
  ('722', '11067'),
  ('722', '11068'),
  ('722', '11073'),
  ('722', '11074'),
  ('722', '11075'),
  ('722', '11077'),
  ('722', '11080'),
  ('722', '11089'),
  ('722', '11090'),
  ('722', '11093'),
  ('722', '11095'),
  ('722', '11096'),
  ('722', '11097'),
  ('722', '11098'),
  ('722', '11101'),
  ('722', '11105'),
  ('722', '11108'),
  ('722', '11110'),
  ('722', '11114'),
  ('722', '11115'),
  ('722', '11116'),
  ('722', '11118'),
  ('722', '11126'),
  ('722', '11127'),
  ('722', '11128'),
  ('722', '11130'),
  ('722', '11131'),
  ('722', '11139'),
  ('722', '11140'),
  ('722', '11141'),
  ('722', '11143'),
  ('722', '11144'),
  ('722', '11152'),
  ('722', '11153'),
  ('722', '11154'),
  ('722', '11156'),
  ('722', '11157'),
  ('722', '11160'),
  ('722', '11164'),
  ('722', '11165'),
  ('722', '11168'),
  ('722', '11172'),
  ('722', '11174'),
  ('722', '11175'),
  ('722', '11180'),
  ('722', '11181'),
  ('722', '11182'),
  ('722', '11184'),
  ('722', '11186'),
  ('722', '11194'),
  ('722', '11195'),
  ('722', '11196'),
  ('722', '11201'),
  ('722', '11202'),
  ('722', '11205'),
  ('722', '11212'),

  ('7221', '11047'),
  ('7221', '11048'),
  ('7221', '11049'),
  ('7221', '11050'),
  ('7221', '11051'),
  ('7221', '11052'),
  ('7221', '11053'),
  ('7221', '11054'),
  ('7221', '11055'),
  ('7221', '11056'),
  ('7221', '11057'),
  ('7221', '11058'),
  ('7221', '11059'),
  ('7221', '11060'),
  ('7221', '11061'),
  ('7221', '11062'),
  ('7221', '11063'),
  ('7221', '11064'),
  ('7221', '11065'),
  ('7221', '11066'),
  ('7221', '11067'),
  ('7221', '11068'),
  ('7221', '11069'),
  ('7221', '11070'),
  ('7221', '11071'),
  ('7221', '11072'),
  ('7221', '11073'),
  ('7221', '11074'),
  ('7221', '11075'),
  ('7221', '11076'),
  ('7221', '11077'),
  ('7221', '11078'),
  ('7221', '11079'),
  ('7221', '11080'),
  ('7221', '11081'),
  ('7221', '11082'),
  ('7221', '11083'),
  ('7221', '11084'),
  ('7221', '11085'),
  ('7221', '11086'),
  ('7221', '11087'),
  ('7221', '11088'),
  ('7221', '11089'),
  ('7221', '11090'),
  ('7221', '11091'),
  ('7221', '11092'),
  ('7221', '11093'),
  ('7221', '11094'),
  ('7221', '11095'),
  ('7221', '11096'),
  ('7221', '11097'),
  ('7221', '11098'),
  ('7221', '11099'),
  ('7221', '11100'),
  ('7221', '11101'),
  ('7221', '11102'),
  ('7221', '11103'),
  ('7221', '11104'),
  ('7221', '11105'),
  ('7221', '11106'),
  ('7221', '11107'),
  ('7221', '11108'),
  ('7221', '11109'),
  ('7221', '11110'),
  ('7221', '11111'),
  ('7221', '11112'),
  ('7221', '11113'),
  ('7221', '11114'),
  ('7221', '11115'),
  ('7221', '11116'),
  ('7221', '11117'),
  ('7221', '11118'),
  ('7221', '11119'),
  ('7221', '11120'),
  ('7221', '11121'),
  ('7221', '11122'),
  ('7221', '11123'),
  ('7221', '11124'),
  ('7221', '11125'),
  ('7221', '11126'),
  ('7221', '11127'),
  ('7221', '11128'),
  ('7221', '11129'),
  ('7221', '11130'),
  ('7221', '11131'),
  ('7221', '11132'),
  ('7221', '11133'),
  ('7221', '11134'),
  ('7221', '11135'),
  ('7221', '11136'),
  ('7221', '11137'),
  ('7221', '11138'),
  ('7221', '11139'),
  ('7221', '11140'),
  ('7221', '11141'),
  ('7221', '11142'),
  ('7221', '11143'),
  ('7221', '11144'),
  ('7221', '11145'),
  ('7221', '11146'),
  ('7221', '11147'),
  ('7221', '11148'),
  ('7221', '11149'),
  ('7221', '11150'),
  ('7221', '11151'),
  ('7221', '11152'),
  ('7221', '11153'),
  ('7221', '11154'),
  ('7221', '11155'),
  ('7221', '11156'),
  ('7221', '11157'),
  ('7221', '11158'),
  ('7221', '11159'),
  ('7221', '11160'),
  ('7221', '11161'),
  ('7221', '11162'),
  ('7221', '11163'),
  ('7221', '11164'),
  ('7221', '11165'),
  ('7221', '11166'),
  ('7221', '11167'),
  ('7221', '11168'),
  ('7221', '11169'),
  ('7221', '11170'),
  ('7221', '11171'),
  ('7221', '11172'),
  ('7221', '11173'),
  ('7221', '11174'),
  ('7221', '11175'),
  ('7221', '11176'),
  ('7221', '11177'),
  ('7221', '11178'),
  ('7221', '11179'),
  ('7221', '11180'),
  ('7221', '11181'),
  ('7221', '11182'),
  ('7221', '11183'),
  ('7221', '11184'),
  ('7221', '11185'),
  ('7221', '11186'),
  ('7221', '11187'),
  ('7221', '11188'),
  ('7221', '11189'),
  ('7221', '11190'),
  ('7221', '11191'),
  ('7221', '11192'),
  ('7221', '11193'),
  ('7221', '11194'),
  ('7221', '11195'),
  ('7221', '11196'),
  ('7221', '11197'),
  ('7221', '11198'),
  ('7221', '11199'),
  ('7221', '11200'),
  ('7221', '11201'),
  ('7221', '11202'),
  ('7221', '11203'),
  ('7221', '11204'),
  ('7221', '11205'),
  ('7221', '11206'),
  ('7221', '11207'),
  ('7221', '11208'),
  ('7221', '11209'),
  ('7221', '11210'),
  ('7221', '11211'),
  ('7221', '11212'),

  ('7223', '11075'),
  ('7223', '11076'),
  ('7223', '11077'),
  ('7223', '11078'),
  ('7223', '11079'),
  ('7223', '11080'),
  ('7223', '11081'),
  ('7223', '11082'),
  ('7223', '11083'),
  ('7223', '11084'),
  ('7223', '11085'),
  ('7223', '11086'),
  ('7223', '11087'),
  ('7223', '11088'),
  ('7223', '11089'),
  ('7223', '11090'),

  ('7224', '10460'),
  ('7224', '11141'),
  ('7224', '11142'),
  ('7224', '11143'),
  ('7224', '11144'),
  ('7224', '11145'),
  ('7224', '11146'),
  ('7224', '11147'),
  ('7224', '11148'),
  ('7224', '11149'),
  ('7224', '11150'),
  ('7224', '11151'),
  ('7224', '11152'),
  ('7224', '11153'),

  ('7225', '11116'),
  ('7225', '11117'),
  ('7225', '11118'),
  ('7225', '11119'),
  ('7225', '11120'),
  ('7225', '11121'),
  ('7225', '11122'),
  ('7225', '11123'),
  ('7225', '11124'),
  ('7225', '11125'),
  ('7225', '11126'),
  ('7225', '11127'),

  ('7226', '11128'),
  ('7226', '11129'),
  ('7226', '11130'),
  ('7226', '11131'),
  ('7226', '11132'),
  ('7226', '11133'),
  ('7226', '11134'),
  ('7226', '11135'),
  ('7226', '11136'),
  ('7226', '11137'),
  ('7226', '11138'),
  ('7226', '11139'),
  ('7226', '11140'),

  ('7227', '11093'),
  ('7227', '11094'),
  ('7227', '11095'),
  ('7227', '11096'),
  ('7227', '11097'),
  ('7227', '11098'),
  ('7227', '11099'),
  ('7227', '11100'),
  ('7227', '11101'),
  ('7227', '11102'),
  ('7227', '11103'),
  ('7227', '11104'),
  ('7227', '11105'),
  ('7227', '11106'),
  ('7227', '11107'),
  ('7227', '11108'),
  ('7227', '11109'),
  ('7227', '11110'),
  ('7227', '11111'),
  ('7227', '11112'),
  ('7227', '11113'),
  ('7227', '11114'),
  ('7227', '11115'),

  ('7228', '11154'),
  ('7228', '11155'),
  ('7228', '11156'),
  ('7228', '11157'),
  ('7228', '11158'),
  ('7228', '11159'),
  ('7228', '11160'),
  ('7228', '11161'),
  ('7228', '11162'),
  ('7228', '11163'),
  ('7228', '11164'),
  ('7228', '11165'),
  ('7228', '11166'),
  ('7228', '11167'),
  ('7228', '11168'),
  ('7228', '11169'),
  ('7228', '11170'),
  ('7228', '11171'),
  ('7228', '11172'),
  ('7228', '11173'),
  ('7228', '11174'),
  ('7228', '11175'),
  ('7228', '11176'),
  ('7228', '11177'),
  ('7228', '11178'),
  ('7228', '11179'),
  ('7228', '11180'),
  ('7228', '11181'),
  ('7228', '11196'),
  ('7228', '11197'),
  ('7228', '11198'),
  ('7228', '11199'),
  ('7228', '11200'),
  ('7228', '11201'),
  ('7228', '11202'),
  ('7228', '11203'),
  ('7228', '11204'),
  ('7228', '11553'),

  ('72221', '11049'),
  ('72221', '11050'),
  ('72221', '11051'),
  ('72221', '11052'),
  ('72221', '11053'),
  ('72221', '11054'),
  ('72221', '11055'),
  ('72221', '11056'),
  ('72221', '11057'),
  ('72221', '11058'),
  ('72221', '11059'),
  ('72221', '11060'),
  ('72221', '11061'),
  ('72221', '11065'),
  ('72221', '11067'),
  ('72221', '11068'),
  ('72221', '11070'),
  ('72221', '11071'),
  ('72221', '11072'),
  ('72221', '11073'),
  ('72221', '11074'),

  ('72222', '11049'),
  ('72222', '11050'),
  ('72222', '11051'),
  ('72222', '11052'),
  ('72222', '11053'),
  ('72222', '11054'),
  ('72222', '11055'),
  ('72222', '11056'),
  ('72222', '11057'),
  ('72222', '11058'),
  ('72222', '11059'),
  ('72222', '11060'),
  ('72222', '11061'),
  ('72222', '11062'),
  ('72222', '11063'),
  ('72222', '11064'),
  ('72222', '11065'),
  ('72222', '11066'),
  ('72222', '11067'),
  ('72222', '11068'),
  ('72222', '11069'),
  ('72222', '11070'),
  ('72222', '11071'),
  ('72222', '11072'),
  ('72222', '11073'),
  ('72222', '11074'),
  ('72222', '11196'),
  ('72222', '11197'),
  ('72222', '11198'),
  ('72222', '11199'),
  ('72222', '11200'),
  ('72222', '11201'),
  ('72222', '11202'),
  ('72222', '11203'),
  ('72222', '11204'),
  ('72222', '11553'),

  ('72223', '11184'),
  ('72223', '11185'),
  ('72223', '11186'),
  ('72223', '11187'),
  ('72223', '11188'),
  ('72223', '11189'),
  ('72223', '11190'),
  ('72223', '11191'),
  ('72223', '11192'),
  ('72223', '11193'),
  ('72223', '11194'),
  ('72223', '11195'),

  ('7311', '10443'),
  ('7311', '10889'),
  ('7311', '10890'),
  ('7311', '10891'),
  ('7311', '10892'),
  ('7311', '10893'),
  ('7311', '10895'),
  ('7311', '10897'),
  ('7311', '10898'),
  ('7311', '10902'),
  ('7311', '10903'),
  ('7311', '11553'),

  ('7312', '10443'),
  ('7312', '10889'),
  ('7312', '10890'),
  ('7312', '10891'),
  ('7312', '10892'),
  ('7312', '10893'),
  ('7312', '10894'),
  ('7312', '10895'),
  ('7312', '10896'),
  ('7312', '10897'),
  ('7312', '10898'),
  ('7312', '10899'),
  ('7312', '10900'),
  ('7312', '10901'),
  ('7312', '10902'),
  ('7312', '10903'),
  ('7312', '11553'),

  ('7321', '10634'),
  ('7321', '10636'),
  ('7321', '10637'),
  ('7321', '10638'),

  ('7322', '10634'),
  ('7322', '10635'),
  ('7322', '10636'),
  ('7322', '10637'),
  ('7322', '10638'),
  ('7322', '10639'),
  ('7322', '10640'),
  ('7322', '10641'),

  ('7331', '10438'),
  ('7331', '10446'),
  ('7331', '10595'),
  ('7331', '10597'),

  ('7332', '10438'),
  ('7332', '10446'),
  ('7332', '10595'),
  ('7332', '10596'),
  ('7332', '10597'),
  ('7332', '10598'),
  ('7332', '10599'),

  ('7341', '10878'),

  ('7342', '10878'),
  ('7342', '10879'),
  ('7342', '10880'),
  ('7342', '10881'),

  ('741', '10456'),
  ('741', '10554'),
  ('741', '10556'),
  ('741', '10557'),
  ('741', '10560'),
  ('741', '10563'),
  ('741', '10796'),
  ('741', '10798'),

  ('742', '10456'),
  ('742', '10554'),
  ('742', '10555'),
  ('742', '10556'),
  ('742', '10557'),
  ('742', '10558'),
  ('742', '10559'),
  ('742', '10560'),
  ('742', '10561'),
  ('742', '10562'),
  ('742', '10563'),
  ('742', '10796'),
  ('742', '10797'),
  ('742', '10798'),
  ('742', '10799'),
  ('742', '10800'),

  ('781', '10882'),
  ('781', '10883'),
  ('781', '10885'),
  ('781', '10886'),
  ('781', '10887'),
  ('781', '10888'),

  ('782', '10882'),
  ('782', '10883'),
  ('782', '10884'),
  ('782', '10885'),
  ('782', '10886'),
  ('782', '10887'),
  ('782', '10888'),

  ('79', '10492'),
  ('79', '10493'),
  ('79', '10494'),
  ('79', '10495'),
  ('79', '10496'),
  ('79', '10497'),
  ('79', '10498'),
  ('79', '10499'),
  ('79', '10500'),

  ('7511', '10448'),
  ('7511', '10449'),
  ('7511', '10450'),
  ('7511', '10453'),
  ('7511', '10564'),
  ('7511', '10565'),
  ('7511', '10566'),
  ('7511', '10567'),
  ('7511', '10568'),
  ('7511', '10571'),
  ('7511', '10577'),
  ('7511', '10582'),
  ('7511', '10584'),

  ('7512', '10448'),
  ('7512', '10449'),
  ('7512', '10450'),
  ('7512', '10451'),
  ('7512', '10453'),
  ('7512', '10564'),
  ('7512', '10565'),
  ('7512', '10566'),
  ('7512', '10567'),
  ('7512', '10568'),
  ('7512', '10569'),
  ('7512', '10570'),
  ('7512', '10571'),
  ('7512', '10572'),
  ('7512', '10573'),
  ('7512', '10574'),
  ('7512', '10575'),
  ('7512', '10576'),
  ('7512', '10577'),
  ('7512', '10578'),
  ('7512', '10579'),
  ('7512', '10580'),
  ('7512', '10581'),
  ('7512', '10582'),
  ('7512', '10583'),
  ('7512', '10584'),
  ('7512', '10585'),

  ('7521', '10448'),
  ('7521', '10449'),
  ('7521', '10450'),
  ('7521', '10453'),
  ('7521', '10456'),
  ('7521', '10461'),
  ('7521', '10462'),
  ('7521', '10565'),
  ('7521', '10566'),
  ('7521', '10567'),
  ('7521', '10568'),
  ('7521', '10571'),
  ('7521', '10577'),

  ('7522', '10448'),
  ('7522', '10449'),
  ('7522', '10450'),
  ('7522', '10451'),
  ('7522', '10453'),
  ('7522', '10456'),
  ('7522', '10461'),
  ('7522', '10462'),
  ('7522', '10565'),
  ('7522', '10566'),
  ('7522', '10567'),
  ('7522', '10568'),
  ('7522', '10569'),
  ('7522', '10570'),
  ('7522', '10571'),
  ('7522', '10572'),
  ('7522', '10573'),
  ('7522', '10574'),
  ('7522', '10575'),
  ('7522', '10576'),
  ('7522', '10577'),
  ('7522', '10578'),
  ('7522', '10579'),
  ('7522', '10580'),

  ('7611', '10402'),
  ('7611', '10403'),
  ('7611', '10404'),
  ('7611', '10405'),
  ('7611', '10411'),
  ('7611', '10412'),
  ('7611', '10414'),
  ('7611', '10418'),
  ('7611', '10419'),
  ('7611', '10424'),
  ('7611', '10427'),
  ('7611', '10428'),
  ('7611', '10429'),
  ('7611', '10447'),
  ('7611', '10448'),
  ('7611', '10449'),
  ('7611', '10459'),

  ('7612', '10402'),
  ('7612', '10403'),
  ('7612', '10404'),
  ('7612', '10405'),
  ('7612', '10406'),
  ('7612', '10407'),
  ('7612', '10408'),
  ('7612', '10409'),
  ('7612', '10410'),
  ('7612', '10411'),
  ('7612', '10412'),
  ('7612', '10413'),
  ('7612', '10414'),
  ('7612', '10415'),
  ('7612', '10416'),
  ('7612', '10417'),
  ('7612', '10418'),
  ('7612', '10419'),
  ('7612', '10420'),
  ('7612', '10421'),
  ('7612', '10422'),
  ('7612', '10423'),
  ('7612', '10424'),
  ('7612', '10425'),
  ('7612', '10426'),
  ('7612', '10427'),
  ('7612', '10428'),
  ('7612', '10429'),
  ('7612', '10430'),
  ('7612', '10431'),
  ('7612', '10432'),
  ('7612', '10433'),
  ('7612', '10434'),
  ('7612', '10447'),
  ('7612', '10448'),
  ('7612', '10449'),
  ('7612', '10459'),
  ('7612', '11539'),

  ('7621', '10403'),
  ('7621', '10404'),
  ('7621', '10405'),
  ('7621', '10411'),
  ('7621', '10412'),
  ('7621', '10414'),
  ('7621', '10418'),
  ('7621', '10419'),
  ('7621', '10424'),
  ('7621', '10427'),
  ('7621', '10447'),
  ('7621', '10448'),
  ('7621', '10449'),
  ('7621', '10462'),

  ('7622', '10403'),
  ('7622', '10404'),
  ('7622', '10405'),
  ('7622', '10406'),
  ('7622', '10407'),
  ('7622', '10408'),
  ('7622', '10409'),
  ('7622', '10410'),
  ('7622', '10411'),
  ('7622', '10412'),
  ('7622', '10413'),
  ('7622', '10414'),
  ('7622', '10415'),
  ('7622', '10416'),
  ('7622', '10417'),
  ('7622', '10418'),
  ('7622', '10419'),
  ('7622', '10420'),
  ('7622', '10421'),
  ('7622', '10422'),
  ('7622', '10423'),
  ('7622', '10424'),
  ('7622', '10425'),
  ('7622', '10426'),
  ('7622', '10427'),
  ('7622', '10447'),
  ('7622', '10448'),
  ('7622', '10449'),
  ('7622', '10462'),
  ('7622', '11539'),

  ('771', '10448'),
  ('771', '10449'),
  ('771', '10456'),
  ('771', '10459'),
  ('771', '11457'),
  ('771', '11461'),
  ('771', '11464'),

  ('772', '10448'),
  ('772', '10449'),
  ('772', '10456'),
  ('772', '10459'),
  ('772', '11457'),
  ('772', '11458'),
  ('772', '11459'),
  ('772', '11460'),
  ('772', '11461'),
  ('772', '11462'),
  ('772', '11463'),
  ('772', '11464'),
  ('772', '11465'),
  ('772', '11466'),

  ('911', '10448'),
  ('911', '10456'),
  ('911', '10459'),
  ('911', '10460'),
  ('911', '10643'),
  ('911', '10644'),
  ('911', '10649'),
  ('911', '10651'),
  ('911', '10653'),
  ('911', '10654'),
  ('911', '10657'),
  ('911', '10659'),
  ('911', '10662'),
  ('911', '10665'),
  ('911', '10668'),
  ('911', '10670'),
  ('911', '10675'),
  ('911', '10678'),
  ('911', '10679'),
  ('911', '10683'),
  ('911', '10686'),
  ('911', '10688'),
  ('911', '10713'),
  ('911', '10715'),
  ('911', '10724'),
  ('911', '10732'),
  ('911', '10733'),
  ('911', '10734'),
  ('911', '10737'),
  ('911', '10739'),
  ('911', '10744'),
  ('911', '10745'),
  ('911', '10750'),
  ('911', '10751'),
  ('911', '10752'),
  ('911', '10754'),
  ('911', '10758'),
  ('911', '10759'),
  ('911', '10764'),
  ('911', '10767'),
  ('911', '10769'),
  ('911', '10771'),
  ('911', '10772'),
  ('911', '10774'),
  ('911', '10781'),
  ('911', '10786'),
  ('911', '10787'),
  ('911', '10924'),
  ('911', '10925'),
  ('911', '10930'),
  ('911', '10932'),
  ('911', '10934'),
  ('911', '10935'),
  ('911', '10938'),
  ('911', '10968'),
  ('911', '10972'),
  ('911', '10974'),

  ('912', '10448'),
  ('912', '10456'),
  ('912', '10459'),
  ('912', '10460'),
  ('912', '10642'),
  ('912', '10643'),
  ('912', '10644'),
  ('912', '10645'),
  ('912', '10646'),
  ('912', '10647'),
  ('912', '10648'),
  ('912', '10649'),
  ('912', '10650'),
  ('912', '10651'),
  ('912', '10652'),
  ('912', '10653'),
  ('912', '10654'),
  ('912', '10655'),
  ('912', '10656'),
  ('912', '10657'),
  ('912', '10658'),
  ('912', '10659'),
  ('912', '10660'),
  ('912', '10661'),
  ('912', '10662'),
  ('912', '10663'),
  ('912', '10664'),
  ('912', '10665'),
  ('912', '10666'),
  ('912', '10667'),
  ('912', '10668'),
  ('912', '10669'),
  ('912', '10670'),
  ('912', '10671'),
  ('912', '10672'),
  ('912', '10673'),
  ('912', '10674'),
  ('912', '10675'),
  ('912', '10676'),
  ('912', '10677'),
  ('912', '10678'),
  ('912', '10679'),
  ('912', '10680'),
  ('912', '10681'),
  ('912', '10682'),
  ('912', '10683'),
  ('912', '10684'),
  ('912', '10685'),
  ('912', '10686'),
  ('912', '10687'),
  ('912', '10688'),
  ('912', '10689'),
  ('912', '10690'),
  ('912', '10691'),
  ('912', '10713'),
  ('912', '10714'),
  ('912', '10715'),
  ('912', '10716'),
  ('912', '10717'),
  ('912', '10718'),
  ('912', '10724'),
  ('912', '10725'),
  ('912', '10726'),
  ('912', '10727'),
  ('912', '10728'),
  ('912', '10729'),
  ('912', '10730'),
  ('912', '10731'),
  ('912', '10732'),
  ('912', '10733'),
  ('912', '10734'),
  ('912', '10735'),
  ('912', '10736'),
  ('912', '10737'),
  ('912', '10738'),
  ('912', '10739'),
  ('912', '10740'),
  ('912', '10741'),
  ('912', '10742'),
  ('912', '10743'),
  ('912', '10744'),
  ('912', '10745'),
  ('912', '10746'),
  ('912', '10747'),
  ('912', '10748'),
  ('912', '10749'),
  ('912', '10750'),
  ('912', '10751'),
  ('912', '10752'),
  ('912', '10753'),
  ('912', '10754'),
  ('912', '10755'),
  ('912', '10756'),
  ('912', '10757'),
  ('912', '10758'),
  ('912', '10759'),
  ('912', '10760'),
  ('912', '10761'),
  ('912', '10762'),
  ('912', '10763'),
  ('912', '10764'),
  ('912', '10765'),
  ('912', '10766'),
  ('912', '10767'),
  ('912', '10768'),
  ('912', '10769'),
  ('912', '10770'),
  ('912', '10771'),
  ('912', '10772'),
  ('912', '10773'),
  ('912', '10774'),
  ('912', '10775'),
  ('912', '10781'),
  ('912', '10782'),
  ('912', '10783'),
  ('912', '10784'),
  ('912', '10785'),
  ('912', '10786'),
  ('912', '10787'),
  ('912', '10788'),
  ('912', '10789'),
  ('912', '10923'),
  ('912', '10924'),
  ('912', '10925'),
  ('912', '10926'),
  ('912', '10927'),
  ('912', '10928'),
  ('912', '10929'),
  ('912', '10930'),
  ('912', '10931'),
  ('912', '10932'),
  ('912', '10933'),
  ('912', '10934'),
  ('912', '10935'),
  ('912', '10936'),
  ('912', '10937'),
  ('912', '10938'),
  ('912', '10939'),
  ('912', '10968'),
  ('912', '10969'),
  ('912', '10970'),
  ('912', '10971'),
  ('912', '10972'),
  ('912', '10973'),
  ('912', '10974'),
  ('912', '10975'),
  ('912', '10976'),
  ('912', '10977'),
  ('912', '10978'),

  ('92', '10801'),
  ('92', '10802'),
  ('92', '10803'),
  ('92', '10804'),
  ('92', '10805'),
  ('92', '10806'),
  ('92', '10807'),
  ('92', '10808'),
  ('92', '10809'),
  ('92', '10810'),
  ('92', '10979'),
  ('92', '10980'),
  ('92', '10981'),
  ('92', '10982'),
  ('92', '10983'),
  ('92', '10984'),
  ('92', '10985'),
  ('92', '10986'),
  ('92', '10987'),

  ('931', '10790'),
  ('931', '10791'),
  ('931', '10794'),
  ('931', '11467'),
  ('931', '11468'),
  ('931', '11469'),
  ('931', '11470'),
  ('931', '11476'),

  ('932', '10790'),
  ('932', '10791'),
  ('932', '10792'),
  ('932', '10793'),
  ('932', '10794'),
  ('932', '10795'),
  ('932', '11467'),
  ('932', '11468'),
  ('932', '11469'),
  ('932', '11470'),
  ('932', '11471'),
  ('932', '11472'),
  ('932', '11473'),
  ('932', '11474'),
  ('932', '11475'),
  ('932', '11476'),
  ('932', '11477'),
  ('932', '11478'),
  ('932', '11479'),

  ('95', '10396'),
  ('95', '10397'),
  ('95', '10398'),
  ('95', '10399'),
  ('95', '10400'),
  ('95', '10401'),
  ('95', '10438'),

  ('96', '10449'),
  ('96', '10456'),
  ('96', '10461'),
  ('96', '10462'),
  ('96', '10627'),
  ('96', '10628'),
  ('96', '10629'),
  ('96', '10630'),
  ('96', '10631'),
  ('96', '10632'),
  ('96', '10633'),
  ('96', '10904'),
  ('96', '10905'),
  ('96', '10906'),
  ('96', '10907'),
  ('96', '10908'),
  ('96', '10909'),
  ('96', '10910'),
  ('96', '10911'),
  ('96', '10912'),
  ('96', '10913'),
  ('96', '10914'),
  ('96', '10915'),
  ('96', '10916'),
  ('96', '10917'),
  ('96', '10918'),
  ('96', '10919'),
  ('96', '10920'),
  ('96', '10921'),
  ('96', '10922'),

  ('1011', '10448'),
  ('1011', '10600'),
  ('1011', '10604'),
  ('1011', '10605'),
  ('1011', '10606'),
  ('1011', '10607'),
  ('1011', '10608'),
  ('1011', '10609'),
  ('1011', '10611'),
  ('1011', '10614'),
  ('1011', '10616'),
  ('1011', '10618'),
  ('1011', '10620'),
  ('1011', '10624'),
  ('1011', '10625'),
  ('1011', '10695'),
  ('1011', '11806'),

  ('1012', '10448'),
  ('1012', '10600'),
  ('1012', '10601'),
  ('1012', '10602'),
  ('1012', '10603'),
  ('1012', '10604'),
  ('1012', '10605'),
  ('1012', '10606'),
  ('1012', '10607'),
  ('1012', '10608'),
  ('1012', '10609'),
  ('1012', '10610'),
  ('1012', '10611'),
  ('1012', '10612'),
  ('1012', '10613'),
  ('1012', '10614'),
  ('1012', '10615'),
  ('1012', '10616'),
  ('1012', '10617'),
  ('1012', '10618'),
  ('1012', '10619'),
  ('1012', '10620'),
  ('1012', '10621'),
  ('1012', '10622'),
  ('1012', '10623'),
  ('1012', '10624'),
  ('1012', '10625'),
  ('1012', '10626'),
  ('1012', '10694'),
  ('1012', '10695'),
  ('1012', '11806'),

  ('1021', '10695'),
  ('1021', '10811'),
  ('1021', '10813'),
  ('1021', '10814'),
  ('1021', '10816'),
  ('1021', '10818'),
  ('1021', '10820'),
  ('1021', '10823'),
  ('1021', '10825'),
  ('1021', '10828'),
  ('1021', '10830'),
  ('1021', '10833'),
  ('1021', '10835'),
  ('1021', '10838'),
  ('1021', '10841'),

  ('1022', '10694'),
  ('1022', '10695'),
  ('1022', '10811'),
  ('1022', '10812'),
  ('1022', '10813'),
  ('1022', '10814'),
  ('1022', '10815'),
  ('1022', '10816'),
  ('1022', '10817'),
  ('1022', '10818'),
  ('1022', '10819'),
  ('1022', '10820'),
  ('1022', '10821'),
  ('1022', '10822'),
  ('1022', '10823'),
  ('1022', '10824'),
  ('1022', '10825'),
  ('1022', '10826'),
  ('1022', '10827'),
  ('1022', '10828'),
  ('1022', '10829'),
  ('1022', '10830'),
  ('1022', '10831'),
  ('1022', '10832'),
  ('1022', '10833'),
  ('1022', '10834'),
  ('1022', '10835'),
  ('1022', '10836'),
  ('1022', '10837'),
  ('1022', '10838'),
  ('1022', '10839'),
  ('1022', '10840'),
  ('1022', '10841'),
  ('1022', '10842'),
  ('1022', '10843'),
  ('1022', '10844'),

  ('10511', '10456'),
  ('10511', '10875'),
  ('10511', '10877'),

  ('10512', '10456'),
  ('10512', '10875'),
  ('10512', '10876'),
  ('10512', '10877'),


  ('801', '10449'),
  ('801', '10456'),
  ('801', '10461'),
  ('801', '10462'),
  ('801', '10620'),
  ('801', '10624'),
  ('801', '10692'),
  ('801', '10693'),
  ('801', '10695'),
  ('801', '10696'),
  ('801', '10697'),
  ('801', '10698'),
  ('801', '10699'),
  ('801', '10700'),
  ('801', '10701'),
  ('801', '10702'),
  ('801', '10703'),
  ('801', '10704'),
  ('801', '10705'),
  ('801', '10706'),
  ('801', '10707'),
  ('801', '10708'),
  ('801', '10709'),
  ('801', '10710'),
  ('801', '10711'),
  ('801', '10712'),
  ('801', '10713'),
  ('801', '10714'),
  ('801', '10715'),
  ('801', '10716'),
  ('801', '10717'),
  ('801', '10718'),
  ('801', '10719'),
  ('801', '10720'),
  ('801', '10721'),
  ('801', '10722'),
  ('801', '10723'),
  ('801', '11480'),
  ('801', '11488'),
  ('801', '11500'),
  ('801', '11501'),
  ('801', '11503'),
  ('801', '11509'),
  ('801', '11513'),
  ('801', '11702'),
  ('801', '11706'),
  ('801', '11714'),
  ('801', '11715'),
  ('801', '11716'),
  ('801', '11720'),
  ('801', '11722'),
  ('801', '11724'),
  ('801', '11725'),
  ('801', '11746'),
  ('801', '11749'),
  ('801', '11750'),
  ('801', '11752'),
  ('801', '11753'),
  ('801', '11758'),
  ('801', '11767'),
  ('801', '11771'),
  ('801', '11773'),
  ('801', '11777'),
  ('801', '11778'),
  ('801', '11779'),
  ('801', '11780'),
  ('801', '11781'),
  ('801', '11782'),
  ('801', '11783'),
  ('801', '11784'),
  ('801', '11785'),
  ('801', '11786'),
  ('801', '11787'),
  ('801', '11789'),
  ('801', '11791'),
  ('801', '11795'),
  ('801', '11800'),
  ('801', '11801'),
  ('801', '11802'),
  ('801', '11804'),
  ('801', '11805'),
  ('801', '11806'),
  ('801', '11808'),
  ('801', '11809'),
  ('801', '11813'),
  ('801', '11816'),
  ('801', '11817'),
  ('801', '11818'),
  ('801', '11819'),
  ('801', '11820'),
  ('801', '11821'),
  ('801', '11823'),
  ('801', '11825'),
  ('801', '11826'),
  ('801', '11827'),
  ('801', '11828'),
  ('801', '11829'),
  ('801', '11830'),
  ('801', '11831'),

  ('802', '10449'),
  ('802', '10456'),
  ('802', '10461'),
  ('802', '10462'),
  ('802', '10619'),
  ('802', '10620'),
  ('802', '10624'),
  ('802', '10692'),
  ('802', '10693'),
  ('802', '10694'),
  ('802', '10695'),
  ('802', '10696'),
  ('802', '10697'),
  ('802', '10698'),
  ('802', '10699'),
  ('802', '10700'),
  ('802', '10701'),
  ('802', '10702'),
  ('802', '10703'),
  ('802', '10704'),
  ('802', '10705'),
  ('802', '10706'),
  ('802', '10707'),
  ('802', '10708'),
  ('802', '10709'),
  ('802', '10710'),
  ('802', '10711'),
  ('802', '10712'),
  ('802', '10713'),
  ('802', '10714'),
  ('802', '10715'),
  ('802', '10716'),
  ('802', '10717'),
  ('802', '10718'),
  ('802', '10719'),
  ('802', '10720'),
  ('802', '10721'),
  ('802', '10722'),
  ('802', '10723'),
  ('802', '11480'),
  ('802', '11488'),
  ('802', '11489'),
  ('802', '11492'),
  ('802', '11500'),
  ('802', '11501'),
  ('802', '11502'),
  ('802', '11503'),
  ('802', '11509'),
  ('802', '11510'),
  ('802', '11513'),
  ('802', '11700'),
  ('802', '11702'),
  ('802', '11703'),
  ('802', '11704'),
  ('802', '11705'),
  ('802', '11706'),
  ('802', '11710'),
  ('802', '11711'),
  ('802', '11712'),
  ('802', '11713'),
  ('802', '11714'),
  ('802', '11715'),
  ('802', '11716'),
  ('802', '11717'),
  ('802', '11718'),
  ('802', '11719'),
  ('802', '11720'),
  ('802', '11721'),
  ('802', '11722'),
  ('802', '11723'),
  ('802', '11724'),
  ('802', '11725'),
  ('802', '11726'),
  ('802', '11744'),
  ('802', '11745'),
  ('802', '11746'),
  ('802', '11747'),
  ('802', '11748'),
  ('802', '11749'),
  ('802', '11750'),
  ('802', '11751'),
  ('802', '11752'),
  ('802', '11753'),
  ('802', '11754'),
  ('802', '11755'),
  ('802', '11756'),
  ('802', '11757'),
  ('802', '11758'),
  ('802', '11767'),
  ('802', '11768'),
  ('802', '11769'),
  ('802', '11770'),
  ('802', '11771'),
  ('802', '11772'),
  ('802', '11773'),
  ('802', '11774'),
  ('802', '11775'),
  ('802', '11776'),
  ('802', '11777'),
  ('802', '11778'),
  ('802', '11779'),
  ('802', '11780'),
  ('802', '11781'),
  ('802', '11782'),
  ('802', '11783'),
  ('802', '11784'),
  ('802', '11785'),
  ('802', '11786'),
  ('802', '11787'),
  ('802', '11788'),
  ('802', '11789'),
  ('802', '11790'),
  ('802', '11791'),
  ('802', '11792'),
  ('802', '11793'),
  ('802', '11795'),
  ('802', '11800'),
  ('802', '11801'),
  ('802', '11802'),
  ('802', '11803'),
  ('802', '11804'),
  ('802', '11805'),
  ('802', '11806'),
  ('802', '11808'),
  ('802', '11809'),
  ('802', '11810'),
  ('802', '11811'),
  ('802', '11812'),
  ('802', '11813'),
  ('802', '11814'),
  ('802', '11815'),
  ('802', '11816'),
  ('802', '11817'),
  ('802', '11818'),
  ('802', '11819'),
  ('802', '11820'),
  ('802', '11821'),
  ('802', '11822'),
  ('802', '11823'),
  ('802', '11824'),
  ('802', '11825'),
  ('802', '11826'),
  ('802', '11827'),
  ('802', '11828'),
  ('802', '11829'),
  ('802', '11830'),
  ('802', '11831'),

  ('811', '10449'),
  ('811', '10461'),
  ('811', '10462'),
  ('811', '10988'),
  ('811', '10990'),
  ('811', '10991'),
  ('811', '10992'),
  ('811', '10994'),
  ('811', '10995'),
  ('811', '10996'),
  ('811', '10997'),
  ('811', '10998'),
  ('811', '11001'),
  ('811', '11003'),
  ('811', '11005'),
  ('811', '11006'),
  ('811', '11007'),
  ('811', '11008'),
  ('811', '11009'),
  ('811', '11011'),
  ('811', '11012'),

  ('812', '10449'),
  ('812', '10461'),
  ('812', '10462'),
  ('812', '10988'),
  ('812', '10989'),
  ('812', '10990'),
  ('812', '10991'),
  ('812', '10992'),
  ('812', '10993'),
  ('812', '10994'),
  ('812', '10995'),
  ('812', '10996'),
  ('812', '10997'),
  ('812', '10998'),
  ('812', '10999'),
  ('812', '11000'),
  ('812', '11001'),
  ('812', '11002'),
  ('812', '11003'),
  ('812', '11004'),
  ('812', '11005'),
  ('812', '11006'),
  ('812', '11007'),
  ('812', '11008'),
  ('812', '11009'),
  ('812', '11010'),
  ('812', '11011'),
  ('812', '11012'),
  ('812', '11013'),

  ('821', '11418'),
  ('821', '11424'),
  ('821', '11427'),
  ('821', '11487'),
  ('821', '11490'),
  ('821', '11808'),

  ('822', '11418'),
  ('822', '11424'),
  ('822', '11427'),
  ('822', '11428'),
  ('822', '11429'),
  ('822', '11430'),
  ('822', '11487'),
  ('822', '11490'),
  ('822', '11808'),

  ('831', '10634'),
  ('831', '10636'),
  ('831', '10637'),
  ('831', '10638'),
  ('831', '10641'),
  ('831', '11808'),

  ('832', '10634'),
  ('832', '10635'),
  ('832', '10636'),
  ('832', '10637'),
  ('832', '10638'),
  ('832', '10639'),
  ('832', '10640'),
  ('832', '10641'),
  ('832', '11808'),

  ('841', '10461'),
  ('841', '10462'),
  ('841', '11691'),
  ('841', '11693'),
  ('841', '11695'),
  ('841', '11696'),
  ('841', '11701'),
  ('841', '11702'),
  ('841', '11708'),
  ('841', '11709'),
  ('841', '11808'),

  ('842', '10461'),
  ('842', '10462'),
  ('842', '11691'),
  ('842', '11692'),
  ('842', '11693'),
  ('842', '11694'),
  ('842', '11695'),
  ('842', '11696'),
  ('842', '11697'),
  ('842', '11698'),
  ('842', '11699'),
  ('842', '11701'),
  ('842', '11702'),
  ('842', '11707'),
  ('842', '11708'),
  ('842', '11709'),
  ('842', '11808'),

  ('851', '11727'),
  ('851', '11728'),
  ('851', '11729'),
  ('851', '11731'),
  ('851', '11733'),
  ('851', '11737'),
  ('851', '11739'),
  ('851', '11740'),
  ('851', '11741'),
  ('851', '11807'),

  ('852', '11727'),
  ('852', '11728'),
  ('852', '11729'),
  ('852', '11730'),
  ('852', '11731'),
  ('852', '11732'),
  ('852', '11733'),
  ('852', '11734'),
  ('852', '11735'),
  ('852', '11736'),
  ('852', '11737'),
  ('852', '11738'),
  ('852', '11739'),
  ('852', '11740'),
  ('852', '11741'),
  ('852', '11742'),
  ('852', '11807'),

  ('861', '11759'),
  ('861', '11762'),
  ('861', '11765'),
  ('861', '11766'),

  ('862', '11759'),
  ('862', '11761'),
  ('862', '11762'),
  ('862', '11763'),
  ('862', '11764'),
  ('862', '11765'),
  ('862', '11766');
