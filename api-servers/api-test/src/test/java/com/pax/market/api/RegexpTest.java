/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.api;

import com.google.common.collect.Maps;
import com.pax.market.constants.SystemConstants;
import org.apache.commons.lang3.StringEscapeUtils;
import org.junit.Test;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexpTest {

    @Test
    public void test() {
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("#{33.}").matches());
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("#-_").matches());
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("#123!").matches());
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("#--~").matches());
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("1#--").matches());
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("a").matches());
        System.out.println(Pattern.compile(SystemConstants.VARIABLE_REG_EXPR).matcher("-#--").matches());


        String s = "AAA#{A.B.C}BBB";
        Map<String, String> params = Maps.newHashMap();
        params.put("#{A.B.C}", "!@#$%^&*()");
        for (String key : params.keySet()) {
            System.out.println(StringEscapeUtils.escapeJson(params.get(key)));
            s = s.replaceAll("(?i)" + Pattern.quote(key), Matcher.quoteReplacement(StringEscapeUtils.escapeJson(params.get(key))));
        }
        String s2 = "^.*\\(\\d+\\)$";
        System.out.println("1.0.0(2)".matches(s2)); //true
        System.out.println("1.0.0(".matches(s2)); //false
        System.out.println("1.0.0()".matches(s2)); //false
        System.out.println("1.0.0(b)".matches(s2)); //false
        System.out.println("1.0(21122)".matches(s2)); //true
    }


}
