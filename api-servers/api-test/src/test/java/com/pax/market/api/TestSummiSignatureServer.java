package com.pax.market.api;

import com.pax.core.json.JsonMapper;
import com.pax.market.dto.parameter.HeaderInfo;
import com.pax.market.dto.parameter.SchemaInfo;
import com.pax.market.framework.common.utils.security.Encodes;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;

import java.util.HashMap;
import java.util.Map;

@Ignore
public class TestSummiSignatureServer {
    private static final String data = "test";
    private static final String accessKey = "0gz4lcc29fka2h1w";
    private static final String accessSecret = "t0any3bgqfu6y47f2l4d1f8qc6j7z8go";

    @Test
    public void test() {
        TestRestTemplate restTemplate = new TestRestTemplate();

        LinkedMultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        HttpHeaders headers = new HttpHeaders();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("data", data);
        String param = Encodes.encodeBase64(JsonMapper.toJsonString(paramMap));
        String unix = String.valueOf(System.currentTimeMillis());
        unix = unix.substring(0, unix.length() - 3);
        String sign = DigestUtils.sha256Hex(accessKey + param + unix + accessSecret);


        params.add("params", param);
        params.add("sign", sign);
        params.add("unix", unix);
        params.add("app_id", accessKey);

        HttpEntity<LinkedMultiValueMap<?, ?>> requestEntity = new HttpEntity<>(params, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity("http://testapi-tms.sunmi.com/api/openapi/v1/appstore/sign", requestEntity, String.class);

        System.out.println(responseEntity);
    }

    @Test
    public void testGetSchemaInfo() {
        TestRestTemplate restTemplate = new TestRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Client-ID", "admin");
        headers.add("X-Market-Domain", "pax");
        headers.add("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cb_r-EZPX41zGRtJDZ6RmDh1vyVV7rsKZXqmIyHFrN8");
        for (int i = 0; i < 100; i++) {
            ResponseEntity<String> responseEntity = restTemplate.exchange("https://api.whatspos.com/p-market-web/v1/apk/parameters/1000184062/schema", HttpMethod.GET, new HttpEntity<>(headers), String.class);
            SchemaInfo schemaInfo = JsonMapper.fromJsonString(responseEntity.getBody(), SchemaInfo.class);
            for (HeaderInfo headerInfo : schemaInfo.getGroupList().get(5).getHeaderList()) {
                System.out.println(headerInfo.getParameterList().get(5).getDefaultValue());
            }
        }
    }
}
