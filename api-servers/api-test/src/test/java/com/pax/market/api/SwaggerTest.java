/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.api;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.pax.market.api.dto.SwaggerApi;
import com.pax.market.api.dto.SwaggerApiOperation;
import com.pax.market.api.utils.ResourceUtils;
import com.pax.market.constants.DashboardConstants;
import com.pax.market.constants.RequiredStatus;
import com.pax.market.domain.entity.global.role.Resource;
import com.pax.market.framework.common.utils.FileUtils;
import com.pax.market.framework.common.utils.Profiles;
import com.pax.market.framework.common.utils.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 * File Description
 *
 * <AUTHOR>
 * @date 23/02/2017
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = TestApiApplication.class, webEnvironment = WebEnvironment.RANDOM_PORT)
@ActiveProfiles(value = Profiles.UNIT_TEST)
@DirtiesContext
public class SwaggerTest {
    @Value("${local.server.port}")
    protected int port;
    private final List<String> NOT_ACTIVE_MARKET_REQUIRED_URLS = Lists.newArrayList(
            "/v1/account",
            "/v1/common",
            "/v1/common/auth",
            "GET/v1/common/codes",
            "GET/v1/common/files/{fileName}",
            "/v1/admin/current-market",
            "/v1/admin/current-user",
            "/v1/admin/market/settings/rki-servers",
            "/v1/admin/market/settings",
            "/v1/admin/market/activate",
            "/v1/3rd/cloudmsg",
            "/v1/3rd/rki",
            "/v1/3rdsys/internal",
            "/v1/internal",
            "/v1/mobile/admin",
            "/v1/mobile/account",
            "/v1/mobile/common",
            "/v1/mobile/download"
    );

    private final List<String> RESELLER_FIRMWARE_LIST_MENU_URLS = Lists.newArrayList(
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "/v1/admin/reseller/firmwares",
            "GET/v1/admin/common/markets",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/factory/model/tree"
    );

    private final List<String> RESELLER_FIRMWARE_LIST_READONLY_URLS = StringUtils.append(RESELLER_FIRMWARE_LIST_MENU_URLS,
            "+POST/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}",
            "+DELETE/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> APP_SCAN_FOR_APP_AND_SUBSCRIBE_URLS = Lists.newArrayList(
            "GET/v1/app_scan/operation",
            "GET/v1/app_scan/scanned",
            "POST/v1/app_scan/task",
            "GET/v1/app_scan/results",
            "GET/v1/app_scan/resultZip",
            "POST/v1/app_scan/rescan",
            "/v1/app_scan/task/{scanTaskId}"
    );

    private final List<String> APP_SCAN_FOR_DEVELOPER_FUNC_URLS = Lists.newArrayList(
            "GET/v1/app_scan/results",
            "GET/v1/app_scan/resultZip"
    );

    private final List<String> APP_SCAN_FOR_SERVICE_CENTER_URLS = Lists.newArrayList(
            "GET/v1/app_scan/usage",
            "GET/v1/app_scan/dashBoard",
            "GET/v1/app_scan/historicalUsage",
            "GET/v1/app_scan/setting",
            "POST/v1/app_scan/setting",
            "GET/v1/app_scan/engine/blacklist",
            "POST/v1/app_scan/engine/blacklist",
            "GET/v1/admin/common/markets"
    );

    private final List<String> APP_MENU_URLS = StringUtils.append(
            APP_SCAN_FOR_APP_AND_SUBSCRIBE_URLS,
            "/v1/admin/apps",
            "/v1/mobile/apps",
            "-/v1/admin/apps/whiteList",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/markets",
            "GET/v1/admin/common/markets",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/merchant/categories",
            "GET/v1/admin/common/attributes",

            "/v1/admin/emm/apps",
            "-GET/v1/admin/emm/apps/{appId}/permissions"
    );

    private final List<String> APP_MENU_READONLY_URLS = StringUtils.append(APP_MENU_URLS,
            "+POST/v1/admin/apps/apks/{apkId}/file",
            "+POST/v1/admin/apps/apks/{apkId}/param-template",
            "+POST/v1/mobile/apps/apks/{apkId}/file",
            "+POST/v1/mobile/apps/apks/{apkId}/param-template",
            "+POST/v1/admin/apps/topic/{topicCategory}/{topicExternalId}",
            "+DELETE/v1/admin/apps/topic/{topicCategory}/{topicExternalId}",
            "-GET/v1/admin/emm/apps/webToken",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );


    //app scan + app  readOnly
    private final List<String> APP_MENU_READONLY_FOR_DEVELOPER_MENU_URLS = Lists.newArrayList(

            "GET/v1/app_scan/operation",
            "GET/v1/app_scan/scanned",
            "GET/v1/app_scan/results",
            "GET/v1/app_scan/resultZip",
            "GET/v1/app_scan/rescan",
            "GET/v1/app_scan/task/{scanTaskId}",

            "GET*/v1/admin/apps",
            "GET*/v1/admin/emm/apps",
            "+POST/v1/admin/apps/apks/{apkId}/file",
            "+POST/v1/admin/apps/apks/{apkId}/param-template",
            "+POST/v1/admin/apps/topic/{topicCategory}/{topicExternalId}",
            "+DELETE/v1/admin/apps/topic/{topicCategory}/{topicExternalId}",

            "-/v1/admin/apps/whiteList",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/markets",
            "GET/v1/admin/common/factory/model/tree"
    );

    private final List<String> RESELLER_APP_LIST_URLS = Lists.newArrayList(
            "/v1/admin/reseller/apps",
            "GET/v1/app_scan/results",
            "GET/v1/app_scan/scanned",

            "GET/v1/admin/common/merchant/categories",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/factory/model/tree",

            "/v1/admin/emm/apps",
            "-GET/v1/admin/emm/apps/{appId}/permissions"
    );

    private final List<String> RESELLER_APP_LIST_READONLY_URLS = StringUtils.append(RESELLER_APP_LIST_URLS,
            "+POST/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}",
            "+DELETE/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}",
            "-GET/v1/admin/emm/apps/webToken",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> DEVELOPER_MENU_URLS = StringUtils.append(
            APP_MENU_READONLY_FOR_DEVELOPER_MENU_URLS,
            "/v1/admin/developers",
            "/v1/mobile/developers"
    );

    private final List<String> DEVELOPER_MENU_READONLY_URLS = StringUtils.append(DEVELOPER_MENU_URLS,
            "+POST/v1/admin/developers/export",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> ZTE_MENU_URLS = Lists.newArrayList("/v1/admin/zte");
    private final List<String> ZTE_MENU_READONLY_URLS = StringUtils.append(ZTE_MENU_URLS,
            "-PUT*/v1"
    );

    private final List<String> APPROVAL_CENTER_MENU_URLS = Lists.newArrayList("/v1/admin/approval",
            "GET/v1/admin/terminal-groups/apks/{groupApkId}/apk-detail",
            "POST/v1/admin/terminal-groups/{groupId}/terminals/list",
            "GET/v1/admin/terminal-groups/apks/{groupApkId}/param",
            "+POST/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download",
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "+POST/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download",
            "GET/v1/admin/terminal-groups/launchers/{groupLauncherId}/apk-detail",
            "GET/v1/admin/terminal-groups/launchers/{groupLauncherId}/param",
            "GET/v1/admin/launcher/templates/reseller/apps/**",
            "GET/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/apk-detail",
            "GET/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param",
            "GET/v1/common/download/url"
            );
    private final List<String> APPROVAL_CENTER_MENU_READONLY_URLS = StringUtils.append(APPROVAL_CENTER_MENU_URLS,
            "+POST/v1/admin/terminal-groups/{groupId}/terminals/list",
            "+POST/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download",
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "+POST/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );
    private final List<String> SUBSCRIBE_MENU_URLS = StringUtils.append(
            APP_SCAN_FOR_APP_AND_SUBSCRIBE_URLS,
            "/v1/admin/subscription",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/markets",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/merchant/categories",
            "POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks"
    );

    private final List<String> SUBSCRIBE_MENU_READONLY_URLS = StringUtils.append(SUBSCRIBE_MENU_URLS,
            "+POST/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}",
            "+DELETE/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}",
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> MODEL_MENU_URLS = Lists.newArrayList(
            "/v1/admin/factories",
            "/v1/admin/models",
            "GET/v1/admin/common/markets"
    );

    private final List<String> PRODUCT_MENU_URLS = Lists.newArrayList(
            "/v1/admin/products"
    );

    private final List<String> PRODUCT_MENU_READONLY_URLS = Lists.newArrayList(
            "/v1/admin/products",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> DATASOURCE_MENU_URLS = Lists.newArrayList(
            "POST/v1/admin/datasource/info",
            "GET/v1/admin/datasource/info",
            "GET/v1/admin/datasource/info/{dataSourceId}",
            "PUT/v1/admin/datasource/info/{dataSourceId}",
            "PUT/v1/admin/datasource/info/changeStatus",
            "DELETE/v1/admin/datasource/info/{dataSourceId}",
            "POST/v1/admin/datasource/info/testConnection",
            "POST/v1/admin/datasource/market",
            "GET/v1/admin/datasource/market",
            "GET/v1/admin/datasource/market/{configId}",
            "PUT/v1/admin/datasource/market/{configId}",
            "DELETE/v1/admin/datasource/market/{configId}"
    );

    private final List<String> MODEL_MENU_READONLY_URLS = StringUtils.append(MODEL_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );


    private final List<String> TERMINAL_MENU_READONLY_URLS = Lists.newArrayList(
            "GET*/v1/admin/terminal-management",
            "POST*/v1/admin/terminal-management/terminals/export",
            "POST*/v1/admin/terminal-management/merchants/export",
            "+POST/v1/admin/terminal-management/terminals/accessories/export",
            "+POST/v1/admin/terminal-management/terminal-detail/{terminalId}/refresh",
            "+POST/v1/admin/terminal-management/terminal-detail/{terminalId}/location/refresh",
            "+POST/v1/admin/terminal-management/terminal-apks/{terminalApkId}/data/file/download-tasks",
            "+POST/v1/admin/terminal-management/terminal-apks/export/history/param/comparison",
            "+POST/v1/admin/terminal-management/terminals/{terminalId}/setting/logs/{terminalLogId}/download-tasks",
            "+POST/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/data/file/download-tasks",
            "POST*/v1/admin/terminal-management/terminals/{terminalId}/air-viewer",
            "+POST/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download",
            "+POST/v1/admin/terminal-management/terminals/accessories/{accessoryId}/refresh",
            "+POST/v1/admin/terminal-management/terminals/list",
            "+POST/v1/admin/terminal-management/resellers/export",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/apps",
            "GET/v1/admin/common/app/apk/parameters",
            "GET/v1/admin/common/app/apks/{apkId}",
            "GET/v1/admin/common/app/apk/parameters/{apkParameterId}/schema",
            "+POST/v1/v1/admin/common/app/apk/parameters/{apkParameterId}/data/file/download-tasks",
            "GET/v1/admin/common/firmwares",
            "GET/v1/admin/common/firmwares/{firmwareId}",
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "GET/v1/admin/common/merchant/categories",
            "GET/v1/admin/terminal-groups/apks/{groupApkId}/param",
            "GET/v1/admin/terminal-groups/apks/{groupApkId}/apk-detail",
            "GET/v1/admin/terminal-groups/launchers/{groupLauncherId}/param",
            "GET/v1/admin/terminal-groups/launchers/{groupLauncherId}/apk-detail",

            "GET/v1/admin/vas/air-viewer/operationInfos",
            "GET/v1/admin/vas/air-viewer/fileTransferInfos",

            "GET/v1/admin/common/attributes",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/models",
            "GET/v1/admin/vas/air-shield/attestation/{terminalId}/history",
            "GET/v1/admin/vas/air-shield/attestation/{summaryId}/details",
            "GET/v1/admin/vas/air-shield/attestation/history/{terminalId}/export",

            "GET/v1/admin/emm/apps",
            "GET/v1/admin/emm/apps/{appId}/permissions",
            "GET/v1/admin/emm/apps/{appId}/availableTestVersions",

            "GET/v1/admin/terminal-management/emm-devices/models",
            "GET/v1/admin/terminal-management/emm-devices/{deviceId}",
            "POST/v1/admin/terminal-management/emm-devices",
            "POST/v1/admin/terminal-management/emm-devices/export",
            "POST/v1/admin/terminal-management/emm-device-variables/export",

            "GET*/v1/admin/emm/policy",
            "GET/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment",
            "-POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment",
            "GET/v1/admin/launcher/templates/reseller/apps/**",
            "GET/v1/admin/common/geofence/templates",
            "GET/v1/admin/common/boundaries/geofencing",
            "GET/v1/admin/common/administrative/region",
            "GET*/v1/mobile/terminal",
            "+POST/v1/mobile/terminal/list"
    );

    private final List<String> TERMINAL_MENU_URLS = StringUtils.append(TERMINAL_MENU_READONLY_URLS,
            "+DELETE/v1/admin/vas/air-shield/attestation/{terminalId}",
            "+PUT/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}/canceled",
            "+POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment",
            "+DELETE/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}");

    private final List<String> FUNC_TERMINAL_FM = Lists.newArrayList("/v1/admin/terminal-management/terminal-firmwares");
    private final List<String> FUNC_TERMINAL_RESELLER_CRUD = Lists.newArrayList(
            "/v1/admin/terminal-management/resellers",
            "-POST/v1/admin/terminal-management/resellers/{resellerId}/profile");
    private final List<String> FUNC_TERMINAL_MERCHANT_CRUD = Lists.newArrayList(
            "/v1/admin/terminal-management/merchants",
            "-POST/v1/admin/terminal-management/merchants/{merchantId}/profile");

    private final List<String> FUNC_TERMINAL_ORG_RESELLER_PROFILE = Lists.newArrayList(
            "+POST/v1/admin/terminal-management/resellers/{resellerId}/profile",
            "+POST/v1/admin/emm/policy/reseller/{resellerId}",
            "+POST/v1/admin/emm/policy/reseller/{resellerId}/sync");

    private final List<String> FUNC_TERMINAL_ORG_MERCHANT_PROFILE = Lists.newArrayList(
            "+POST/v1/admin/terminal-management/merchants/{merchantId}/profile",
            "+POST/v1/admin/emm/policy/merchant/{merchantId}",
            "+POST/v1/admin/emm/policy/merchant/{merchantId}/sync");

    private final List<String> FUNC_TERMINAL_CRUD = Lists.newArrayList(
            "/v1/admin/terminal-management/terminals",
            "/v1/mobile/terminal",
            "/v1/admin/terminal-management/emm-devices",
            "-POST/v1/mobile/terminal/{terminalId}/lock",
            "-POST/v1/mobile/terminal/{terminalId}/unlock"
    );
    private final List<String> FUNC_TERMINAL_SETTING = Lists.newArrayList(
            "/v1/admin/terminal-management/terminals/{terminalId}/setting",
            "/v1/admin/terminal-management/terminal-detail",
            "+POST/v1/admin/terminal-management/terminals/import/static-ip/config/template",
            "+POST/v1/admin/terminal-management/terminals/import/static-ip/config",
            "+POST/v1/mobile/terminal/{terminalId}/lock",
            "+POST/v1/mobile/terminal/{terminalId}/unlock"
    );
    private final List<String> FUNC_TERMINAL_APP_PUSH = Lists.newArrayList(
            "/v1/admin/terminal-management/terminal-apks",
            "-PUT/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param",
            "-POST/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param",
            "-POST/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables");
    private final List<String> FUNC_TERMINAL_APP_PARAM = Lists.newArrayList(
            "/v1/admin/terminal-management/terminal-apks",
            "/v1/admin/terminal-management/merchant-variables",
            "/v1/admin/terminal-management/terminal-variables",
            "/v1/admin/terminal-management/emm-device-variables",
            "+POST/v1/admin/terminal-management/terminals/import/variable/template",
            "+POST/v1/admin/terminal-management/terminals/import/variable",
            "+POST/v1/common/param/data/upload");
    private final List<String> FUNC_TERMINAL_APP_UNINSTALL = Lists.newArrayList(
            "+POST/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/uninstall");
    private final List<String> FUNC_TERMINAL_RKI_PUSH = Lists.newArrayList(
            "/v1/admin/terminal-management/terminal-rkis",
            "GET/v1/admin/common/reseller/{resellerId}/rki/template/keys");
    private final List<String> FUNC_TERMINAL_LAUNCHER_PUSH = Lists.newArrayList(
            "/v1/admin/terminal-management/terminal-launchers");
    private final List<String> FUNC_TERMINAL_RKI_SETTING = Lists.newArrayList(
            "/v1/admin/terminal-management/resellers/{resellerId}/rki",
            "GET/v1/admin/common/reseller/{resellerId}/rki/template/keys");


    private final List<String> GROUP_MENU_READONLY_URLS = Lists.newArrayList(
            "GET*/v1/admin/terminal-groups",
            "+POST/v1/admin/terminal-groups/{groupId}/terminals/list",
            "+POST/v1/admin/terminal-groups/apks/{groupApkId}/terminals/export",
            "+POST/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals/export",
            "+POST/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals/export",
            "+POST/v1/admin/terminal-groups/operations/{groupOptId}/terminals/export",
            "+POST/v1/admin/terminal-groups/puks/{groupOptId}/terminals/export",
            "+POST/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals/export",
            "+POST/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals/export",
            "+POST/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download",

            "+POST/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals/export",
            "+POST/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download",
            "+POST/v1/admin/terminal-groups/terminals",

            "+POST/v1/v1/admin/common/app/apk/parameters/{apkParameterId}/data/file/download-tasks",

            "GET/v1/admin/common/app/apk/parameters",
            "GET/v1/admin/common/app/apk/parameters/{apkParameterId}/schema",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/merchant/categories",
            "GET/v1/admin/common/apps",
            "GET/v1/admin/common/app/apks/{apkId}",
            "GET/v1/admin/common/firmwares",
            "GET/v1/admin/common/firmwares/{firmwareId}",
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/models",
            "GET/v1/admin/launcher/templates/reseller/apps/**"
    );

    private final List<String> GROUP_MENU_URLS = StringUtils.append(GROUP_MENU_READONLY_URLS);

    private final List<String> FUNC_GROUP_CRUD = Lists.newArrayList("/v1/admin/terminal-groups");

    private final List<String> FUNC_GROUP_APP_PUSH = Lists.newArrayList(
            "/v1/admin/terminal-groups/apks",
            "-PUT/v1/admin/terminal-groups/apks/{groupApkId}/param",
            "-POST/v1/admin/terminal-groups/apks/{groupApkId}/param",
            "-POST/v1/admin/terminal-groups/apks/{groupApkId}/param/variables",
            "-POST/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables",
            "-POST/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/resume");

    private final List<String> FUNC_GROUP_APP_PARAM = Lists.newArrayList(
            "/v1/admin/terminal-groups/apks",
            "/v1/admin/terminal-groups/variables",
            "+POST/v1/common/param/data/upload");

    private final List<String> FUNC_GROUP_APP_UNINSTALL = Lists.newArrayList("/v1/admin/terminal-groups/uninstall-apks");

    private final List<String> FUNC_GROUP_FM = Lists.newArrayList("/v1/admin/terminal-groups/firmwares");

    private final List<String> FUNC_GROUP_RKI = Lists.newArrayList(
            "/v1/admin/terminal-groups/rkis",
            "GET/v1/admin/common/reseller/{resellerId}/rki/template/keys");

    private final List<String> FUNC_GROUP_MSG = Lists.newArrayList("/v1/admin/terminal-groups/operations");

    private final List<String> FUNC_GROUP_PUK = Lists.newArrayList("/v1/admin/terminal-groups/puks");

    private final List<String> FUNC_GROUP_LAUNCHER = Lists.newArrayList("/v1/admin/terminal-groups/launchers");

    private final List<String> FUNC_GROUP_SOLUTION = Lists.newArrayList(
            "/v1/admin/terminal-groups/solutions",
            "/v1/admin/terminal-groups/variables",
            "+POST/v1/common/param/data/upload");

    private final List<String> APK_PARAMETER_MENU_URLS = Lists.newArrayList(
            "/v1/admin/push/template",
            "GET/v1/admin/common/app/apks/{apkId}",
            "+POST/v1/common/param/data/upload"
    );

    private final List<String> APK_PARAMETER_MENU_READONLY_URLS = StringUtils.append(APK_PARAMETER_MENU_URLS,
            "+POST/v1/admin/push/template/parameters/{apkParameterId}/data-file/download-tasks",
            "+POST/v1/admin/push/template/export/parameters/comparison",
            "+POST/v1/admin/push/template/export/parameters",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> LAUNCHER_TEMPLATE_MENU_URLS = Lists.newArrayList(
            "/v1/admin/launcher/templates"
    );

    private final List<String> LAUNCHER_TEMPLATE_MENU_READONLY_URLS = StringUtils.append(LAUNCHER_TEMPLATE_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> PRICE_TEMPLATE_MENU_URLS = Lists.newArrayList(
            "/v1/admin/price/templates"
    );

    private final List<String> PRICE_TEMPLATE_MENU_READONLY_URLS = StringUtils.append(PRICE_TEMPLATE_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> GEOFENCE_TEMPLATE_MENU_URLS = Lists.newArrayList(
            "/v1/admin/geofence/templates",
            "/v1/admin/common/boundaries/geofencing",
            "/v1/admin/common/administrative/region"
    );

    private final List<String> GEOFENCE_TEMPLATE_MENU_READONLY_URLS = StringUtils.append(GEOFENCE_TEMPLATE_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> MAP_MENU_URLS = Lists.newArrayList(
            "/v1/admin/geo-location",
            "/v1/terminal/location",
            "/v1/terminals/{terminalId}/location",
            "GET/v1/terminals",
            "GET/v1/terminals/{terminalId}",
            "GET/v1/terminals/{terminalId}/installedApks",
            "GET/v1/terminals/{terminalId}/details",
            "-GET/v1/admin/geo-location/markers/god-perspective",
            String.format("/v1/admin/dashboard/widgets/%s", DashboardConstants.WIDGET_MAP)
    );

    private final List<String> ALARM_MENU_URLS = Lists.newArrayList(
            "/v1/admin/alarm",
            "/v1/mobile/alarm",
            "GET/v1/mobile/vas/air-shield/attestation/{terminalId}/history",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree"
    );

    private final List<String> ALARM_MENU_READONLY_URLS = StringUtils.append(ALARM_MENU_URLS,
            "+POST/v1/admin/alarm/widgets/{type}/download-tasks",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> CLIENT_APP_MAINTAIN_MENU_URLS = Lists.newArrayList(
            "/v1/admin/client-apps",
            "/v1/admin/client-apps-common",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/models",
            "GET/v1/admin/common/client-app/factories",
            "POST/v1/common/download/client-app/latest/client"
    );

    private final List<String> CLIENT_APP_MAINTAIN_MENU_READONLY_URLS = StringUtils.append(CLIENT_APP_MAINTAIN_MENU_URLS,
            "+POST/v1/admin/client-apps-common/client-apks/{clientApkId}/download",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> CLIENT_APP_APPROVE_MENU_URLS = Lists.newArrayList(
            "/v1/admin/client-apps-approval",
            "/v1/admin/client-apps-publish/client-apks",
            "/v1/admin/client-apps-common",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/client-app/factories",
            "POST/v1/common/download/client-app/latest/client",
            "GET/v1/admin/common/resellers/tree"
            );

    private final List<String> CLIENT_APP_APPROVE_MENU_READONLY_URLS = StringUtils.append(CLIENT_APP_APPROVE_MENU_URLS,
            "+POST/v1/admin/client-apps-common/client-apks/{clientApkId}/download",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> TERMINAL_DEVICE_STOCK_MENU_URLS = Lists.newArrayList(
            "/v1/admin/terminal-stocks",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/models",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/markets"
    );


    private final List<String> TERMINAL_DEVICE_STOCK_MENU_READONLY_URLS = StringUtils.append(TERMINAL_DEVICE_STOCK_MENU_URLS,
            "+POST/v1/admin/terminal-stocks/export",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> MARKET_SETTING_MENU_URLS = Lists.newArrayList(
            "/v1/admin/market/settings",
            "/v1/admin/market/signature",
            "/v1/admin/market/app-whitelist",
            "/v1/admin/market/app-blacklist",
            "/v1/admin/reseller/settings/ui",
            "/v1/admin/reseller/settings/tid",
            "/v1/admin/reseller/signature",
            "/v1/admin/market/variables",
            "/v1/admin/market/merchant-categories",
            "/v1/admin/market/attributes",
            "/v1/admin/market/3rd-sys",
            "/v1/admin/reseller/3rd-sys",
            "/v1/admin/reseller/rki/settings",
            "/v1/admin/market/sso/settings",
            "/v1/admin/market/billing/settings",
            "/v1/admin/market/model/settings",
            "/v1/admin/market/advance",
            "/v1/admin/market/terminal-blacklist",
            "GET/v1/admin/common/reseller/{resellerId}/rki/template/keys",
            "GET/v1/admin/common/markets",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/models",
            "v1/admin/market/terminal-whiteList"
    );

    private final List<String> MARKET_SETTING_MENU_READONLY_URLS = StringUtils.append(MARKET_SETTING_MENU_URLS,
            "+POST/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}/download-task",
            "+POST/v1/admin/market/terminal-blacklist/import/template/download-tasks",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> FIRMWARE_MAINTAIN_MENU_URLS = Lists.newArrayList(
            "/v1/admin/firmwares",
            "/v1/admin/firmwares-common",
            "/v1/admin/common/file",
            "GET/v1/admin/common/factories",
            "GET/v1/admin/common/factory/model/tree",
            "POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks"
    );

    private final List<String> FIRMWARE_MAINTAIN_MENU_READONLY_URLS = StringUtils.append(FIRMWARE_MAINTAIN_MENU_URLS,
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> FIRMWARE_APPROVE_MENU_URLS = Lists.newArrayList(
            "/v1/admin/firmwares-approval",
            "/v1/admin/common/file",
            "/v1/admin/firmwares-common",
            "+POST/v1/admin/common/firmwares/file/{fmFileId}/download-tasks",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/markets",
            "GET/v1/admin/common/factories"
    );

    private final List<String> FIRMWARE_APPROVE_MENU_READONLY_URLS = StringUtils.append(FIRMWARE_APPROVE_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> ROLE_MENU_URLS = Lists.newArrayList(
            "/v1/admin/roles",
            "/v1/admin/operations"
    );

    private final List<String> USER_FUNC_URLS = Lists.newArrayList(
            "/v1/common/users/captcha/verify",
            "/v1/common/users/opt/verify",
            "GET/v1/common/users/routers",
            "GET/v1/common/users/agreement/agreed",
            "GET/v1/common/system-config",
            "GET/v1/admin/market"
    );

    private final List<String> REPORT_CENTER_URLS = Lists.newArrayList(
            "/v1/admin/report",
            "-GET*/v1/reports",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/factories",
            "GET/v1/markets",
            "GET/v1/models",
            "GET/v1/merchants",
            "GET/v1/apk/parameters/apps",
            "GET/v1/apk/parameters/apks",
            "/v1/admin/global/report",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/markets"
    );

    private final List<String> DEVELOPER_FUNC_URLS = StringUtils.append(
            APP_SCAN_FOR_DEVELOPER_FUNC_URLS,
            "/v1/developers",
            "/v1/developer",
            "/v1/guides",
            "GET/v1/users/{userId}",
            "+POST/v1/common/param/data/upload",
            "GET/v1/admin/common/client-app/factories",
            "POST/v1/common/download/client-app/latest/client",
            "-GET/v1/developer/current-user",
            "-GET/v1/developer/market",
            "-GET/v1/developer/current",
            "-POST/v1/developer/email",
            "-POST/v1/developer"
    );

    private final List<String> MERCHANT_PORTAL_URLS = Lists.newArrayList(
            "/v1/merchant/dashboard",
            "+POST/v1/admin/cloudservice/cloud_data/access/url"
    );

    private final List<String> SUPER_FUNC_URLS = Lists.newArrayList(
            "/v1/admin/platform/internal"
    );


    private final List<String> MENU_DASHBOARD_URLS = Lists.newArrayList(
            "/v1/admin/dashboard",
            "/v1/mobile/dashboard",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/client-app/factories",
            "POST/v1/common/download/client-app/latest/client"
    );


    private final List<String> MARKET_MENU_URLS = Lists.newArrayList(
            "/v1/admin/global/markets",
            "GET/v1/admin/market/billing/defaultSettings/{serviceType}",
            "PUT/v1/admin/market/billing/defaultSettings",
            "GET/v1/dcmgt",
            "GET/v1/admin/common/factories",
            "GET/v1/marketAdmin/vas/markets/{marketId}/trial/msg/count"
    );

    private final List<String> MARKET_MENU_READONLY_URLS = StringUtils.append(MARKET_MENU_URLS,
            "+POST/v1/admin/global/markets/statistics/export/{type}",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> DC_MENU_URLS = Lists.newArrayList(
            "/v1/dcmgt"
    );

    private final List<String> GLOBAL_CONFIG_MENU_URLS = Lists.newArrayList(
            "/v1/admin/platform/configuration",
            "/v1/admin/market/billing/defaultSettings"
    );

    private final List<String> GLOBAL_CONFIG_MENU_READONLY_URLS = StringUtils.append(GLOBAL_CONFIG_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> RESELLER_MIGRATION_MENU_URLS = Lists.newArrayList(
            "/v1/admin/common/markets",
            "/v1/admin/platform/migration/reseller-migrations"
    );

    private final List<String> RESELLER_MIGRATION_READONLY_URLS = StringUtils.append(RESELLER_MIGRATION_MENU_URLS,
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"
    );

    private final List<String> USER_LIST_MENU_URLS = Lists.newArrayList(
            "/v1/admin/users",
            "/v1/admin/merchant/users",
            "/v1/admin/merchant/users/{userId}/merchants"
    );

    private final List<String> USER_LIST_MENU_READONLY_URLS = StringUtils.append(USER_LIST_MENU_URLS,
            "+POST/v1/admin/users/export",
            "+POST/v1/admin/merchant/users/export",
            "-POST*/v1",
            "-PUT*/v1",
            "-DELETE*/v1"

    );

    private final List<String> AUDIT_TRAIL_MENU_URLS = Lists.newArrayList(
            "/v1/admin/audit-log",
            "GET/v1/admin/common/administrative/region"
    );

    private final List<String> SERVICE_CENTER_MENU_URLS = StringUtils.append(
            APP_SCAN_FOR_SERVICE_CENTER_URLS,
            "GET/v1/marketAdmin/vas/markets",
            "GET/v1/marketAdmin/vas/markets/export",
            "GET/v1/marketAdmin/vas/markets/{marketId}/trial/msg/count",
            "GET/v1/marketAdmin/vas/services",
            "GET/v1/marketAdmin/vas/services/show",
            "GET/v1/marketAdmin/vas/appscan/export",
            "GET/v1/marketAdmin/vas/airviewer/export",
            "GET/v1/marketAdmin/vas/airViewer/currentUsage",
            "GET/v1/marketAdmin/vas/airViewer/historicalUsage",
            "GET/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard",
            "GET/v1/marketAdmin/vas/terminal/enroll/{marketId}/download",
            "GET/v1/marketAdmin/vas/terminal/enroll/bill",
            "GET/v1/marketAdmin/vas/terminal/enroll/dashboard",
            "GET/v1/marketAdmin/vas/terminal/enroll/history",
            "GET/v1/marketAdmin/vas/terminal/enroll/history/{marketId}",
            "GET/v1/marketAdmin/vas/{serviceType}/usage/dashBoard",
            "GET/v1/marketAdmin/vas/{serviceType}/current/month/usage",
            "GET/v1/marketAdmin/vas/{serviceType}/history/usage",
            "GET/v1/marketAdmin/vas/{serviceType}/export/current/usage",
            "GET/v1/marketAdmin/vas/{serviceType}/export/history/usage",
            "GET/v1/marketAdmin/vas/export/summary",
            "GET/v1/marketAdmin/vas/export/detail/zip",
            "PUT/v1/marketAdmin/vas/services/{serviceType}/enable",
            "PUT/v1/marketAdmin/vas/services/{marketId}/status",
            "PUT/v1/marketAdmin/vas/services/{serviceType}/disable",
            "GET/v1/marketAdmin/vas/services/{serviceType}/reseller/specific",
            "POST/v1/marketAdmin/vas/services/{serviceType}/specific",
            "DELETE/v1/marketAdmin/vas/services/{serviceType}/specific",
            "PUT/v1/marketAdmin/vas/{developerId}/service/status",
            "GET/v1/marketAdmin/vas/{serviceType}/developers",
            "GET/v1/marketAdmin/vas/{serviceType}/export/developers",
            "GET/v1/marketAdmin/vas/subscriptionHistory/{marketId}",
            "POST/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}",
            "GET/v1/marketAdmin/vas/{serviceType}/agreement",
            "POST/v1/marketAdmin/vas/agreement",
            "PUT/v1/marketAdmin/vas/agreement/{agreementId}",
            "DELETE/v1/marketAdmin/vas/agreement/{agreementId}",
            "POST/v1/marketAdmin/vas/agreement/publish",
            "GET/v1/marketAdmin/vas/agreements",
            "GET/v1/marketAdmin/vas/agreement/{agreementId}/download",
            "GET/v1/marketAdmin/vas/cyberLab/terminal/blacklist",
            "DELETE/v1/marketAdmin/vas/cyberLab/terminal/blacklist/{id}",
            "GET/v1/marketAdmin/vas/billingSetting/{serviceType}",
            "PUT/v1/markets/{marketId}/billing",
            "PUT/v1/markets/{marketId}/billing/service",
            "GET/v1/markets/{marketId}",
            "GET/v1/admin/vas",
            "GET/v1/admin/vas/air-viewer/models/unattended",
            "PUT/v1/admin/vas/air-viewer/models/unattended",
            "DELETE/v1/admin/vas/service/{serviceType}",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree",
            "/v1/admin/market/billing",
            "GET/v1/admin/market/billing/defaultSettings/{serviceType}",
            "PUT/v1/admin/market/billing/defaultSettings",
            "GET/v1/admin/global/markets/{marketId}/price-settings",
            "GET/v1/admin/global/markets/{marketId}/service-setting",
            "PUT/v1/admin/global/markets/{marketId}/price-setting",
            "PUT/v1/marketAdmin/vas/insight/setting",
            "GET/v1/marketAdmin/vas/insight/setting",
            "POST/v1/marketAdmin/vas/adup/ad-slot",
            "GET/v1/marketAdmin/vas/adup/ad-slot/specs",
            "GET/v1/admin/common/factory/model/tree",
            "DELETE/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}",
            "PUT/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/activate",
            "PUT/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/disable",
            "GET/v1/marketAdmin/vas/adup/ad-slots",
            "GET/v1/marketAdmin/vas/airShield/app-black-list",
            "POST/v1/marketAdmin/vas/airShield/app-black-list",
            "PUT/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}",
            "DELETE/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}",
            "GET/v1/marketAdmin/vas/airShield/sys-file-access",
            "POST/v1/marketAdmin/vas/airShield/sys-file-access",
            "PUT/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}",
            "GET/v1/marketAdmin/vas/emm/signup/url",
            "GET/v1/marketAdmin/vas/emm/enterprise",
            "POST/v1/marketAdmin/vas/emm/enterprise",
            "PUT/v1/marketAdmin/vas/emm/enterprise",
            "DELETE/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}",
            "GET/v1/marketAdmin/vas/airShield/interval",
            "PUT/v1/marketAdmin/vas/airShield/interval",
            "GET/v1/marketAdmin/vas/emm/config",
            "PUT/v1/marketAdmin/vas/emm/config",
            "PUT/v1/marketAdmin/vas/air-link/plan/{id}",
            "POST/v1/marketAdmin/vas/airlink/orders/recharge",
            "PUT/v1/marketAdmin/vas/airlink/orders/{id}/approve",
            "PUT/v1/marketAdmin/vas/airlink/orders/{id}/reject",
            "GET/v1/marketAdmin/vas/air-link/recharge/config",
            "PUT/v1/marketAdmin/vas/air-link/recharge/config/{minRecharge}",
            "GET/v1/marketAdmin/vas/airlink/orders",
            "GET/v1/marketAdmin/vas/airlink/orders/{id}/detail",
            "GET/v1/marketAdmin/vas/airlink/orders/recharge",
            "GET/v1/marketAdmin/vas/air-link/estates",
            "POST/v1/marketAdmin/vas/air-link/estates",
            "PUT/v1/marketAdmin/vas/air-link/estates/{id}",
            "DELETE/v1/marketAdmin/vas/air-link/estates",
            "PUT/v1/marketAdmin/vas/air-link/estates/move",
            "POST/v1/marketAdmin/vas/air-link/estates/template/download",
            "POST/v1/marketAdmin/vas/air-link/import/estates/{marketId}",
            "POST/v1/marketAdmin/vas/air-link/export/estates",
            "GET/v1/marketAdmin/vas/air-load/card/pool/maximum",
            "PUT/v1/marketAdmin/vas/air-load/card/pool/{maximum}",
            "GET/v1/marketAdmin/vas/air-load/card/pools",
            "GET/v1/marketAdmin/vas/air-load/card/pool/resellers",
            "POST/v1/marketAdmin/vas/air-load/card/pool/info",
            "GET/v1/marketAdmin/vas/air-load/card/pool/info/{id}",
            "PUT/v1/marketAdmin/vas/air-load/card/pool/info/{id}",
            "DELETE/v1/marketAdmin/vas/air-load/card/pool/info/{id}",
            "GET/v1/marketAdmin/vas/air-load/card/template/download",
            "POST/v1/marketAdmin/vas/air-load/card/import",
            "GET/v1/marketAdmin/vas/air-load/cards/{cardPoolId}",
            "DELETE/v1/marketAdmin/vas/air-load/card/{id}",
            "PUT/v1/marketAdmin/vas/air-load/card/{id}/remove",
            "DELETE/v1/marketAdmin/vas/air-load/cards/batch/delete",
            "PUT/v1/marketAdmin/vas/air-load/cards/batch/remove"
    );

    private final List<String> SERVICE_CENTER_MENU_URLS_READONLY_URLS = StringUtils.append(SERVICE_CENTER_MENU_URLS,
            "-PUT/v1/marketAdmin/vas/services/{serviceType}/enable",
            "-PUT/v1/marketAdmin/vas/services/{marketId}/status",
            "-PUT/v1/marketAdmin/vas/services/{serviceType}/disable",
            "-POST/v1/marketAdmin/vas/services/{serviceType}/specific",
            "-PUT/v1/markets/{marketId}/billing",
            "-PUT/v1/markets/{marketId}/billing/service",
            "-DELETE/v1/marketAdmin/vas/services/{serviceType}/specific",
            "-POST/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}",
            "-PUT/v1/marketAdmin/vas/{developerId}/service/status",
            "-POST/v1/marketAdmin/vas/agreement",
            "-PUT/v1/marketAdmin/vas/agreement/{agreementId}",
            "-DELETE/v1/marketAdmin/vas/agreement/{agreementId}",
            "-POST/v1/marketAdmin/vas/agreement/publish",
            "-PUT/v1/admin/vas/air-viewer/models/unattended",
            "-DELETE/v1/admin/vas/service/{serviceType}",
            "-PUT/v1/admin/market/billing/defaultSettings",
            "-POST/v1/app_scan/setting",
            "-POST/v1/app_scan/engine/blacklist",
            "-PUT/v1/admin/global/markets/{marketId}/price-setting",
            "-DELETE/v1/marketAdmin/vas/cyberLab/terminal/blacklist/{id}",
            "-PUT/v1/marketAdmin/vas/insight/setting",
            "-POST/v1/marketAdmin/vas/adup/ad-slot",
            "-DELETE/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}",
            "-PUT/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/activate",
            "-PUT/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/disable",
            "-PUT/v1/marketAdmin/vas/insight/setting",
            "-POST/v1/marketAdmin/vas/airShield/app-black-list",
            "-PUT/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}",
            "-DELETE/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}",
            "-POST/v1/marketAdmin/vas/airShield/sys-file-access",
            "-PUT/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}",
            "-DELETE/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}",
            "-PUT/v1/marketAdmin/vas/airShield/interval",
            "-POST/v1/marketAdmin/vas/emm/enterprise",
            "-PUT/v1/marketAdmin/vas/emm/enterprise",
            "-PUT/v1/marketAdmin/vas/emm/config",
            "-PUT/v1/marketAdmin/vas/air-link/recharge/config/{minRecharge}",
            "-PUT/v1/marketAdmin/vas/air-load/card/pool/{maximum}",
            "-POST/v1/marketAdmin/vas/air-load/card/pool/info",
            "-PUT/v1/marketAdmin/vas/air-load/card/pool/info/{id}",
            "-DELETE/v1/marketAdmin/vas/air-load/card/pool/info/{id}",
            "-POST/v1/marketAdmin/vas/air-load/card/import",
            "-DELETE/v1/marketAdmin/vas/air-load/card/{id}",
            "-PUT/v1/marketAdmin/vas/air-load/card/{id}/remove",
            "-DELETE/v1/marketAdmin/vas/air-load/card/batch/delete",
            "-PUT/v1/marketAdmin/vas/air-load/card/batch/remove",
            "-POST/v1/marketAdmin/vas/air-link/estates",
            "-PUT/v1/marketAdmin/vas/air-link/estates/{id}",
            "-DELETE/v1/marketAdmin/vas/air-link/estates",
            "-PUT/v1/marketAdmin/vas/air-link/estates/move",
            "-POST/v1/marketAdmin/vas/air-link/import/estates/{marketId}",
            "-DELETE/v1/marketAdmin/vas/air-load/cards/batch/delete",
            "-PUT/v1/marketAdmin/vas/air-load/cards/batch/remove"

    );

    private final List<String> AD_UP_URLS = Lists.newArrayList("GET/v1/marketAdmin/vas/services",
            "GET/v1/marketAdmin/vas/adup/ad-group",
            "POST/v1/marketAdmin/vas/adup/ad-group",
            "GET/v1/marketAdmin/vas/adup/ad-group/ad-slot",
            "PUT/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adGroupId}",
            "GET/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adSlotId}",
            "GET/v1/marketAdmin/vas/adup/ad-group/{adGroupId}",
            "PUT/v1/marketAdmin/vas/adup/ad-group/{adGroupId}",
            "DELETE/v1/marketAdmin/vas/adup/ad-group/{adGroupId}",
            "PUT/v1/marketAdmin/vas/adup/ad-group/{adGroupId}/status",
            "GET/v1/marketAdmin/vas/adup/ad-slot/models",
            "POST/v1/marketAdmin/vas/adup/ad-visual",
            "GET/v1/marketAdmin/vas/adup/ad-visual/maxDuration",
            "GET/v1/marketAdmin/vas/adup/ad-slot/specs",
            "GET/v1/marketAdmin/vas/adup/ad-visuals",
            "GET/v1/admin/common/resellers",
            "GET/v1/admin/common/resellers/tree"
    );

    private final List<String> AD_UP_READONLY_URLS = StringUtils.append(AD_UP_URLS,
            "-POST/v1/marketAdmin/vas/adup/ad-group",
            "-PUT/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adGroupId}",
            "-PUT/v1/marketAdmin/vas/adup/ad-group/{adGroupId}",
            "-DELETE/v1/marketAdmin/vas/adup/ad-group/{adGroupId}",
            "-PUT/v1/marketAdmin/vas/adup/ad-group/{adGroupId}/status",
            "-POST/v1/marketAdmin/vas/adup/ad-visual"
    );

    private final List<String> AIR_LINK_URLS = Lists.newArrayList(
            "GET/v1/marketAdmin/vas/air-link/terminals/{id}/detail",
            "GET/v1/marketAdmin/vas/air-link/terminals/{id}/consumption-statistics",
            "GET/v1/marketAdmin/vas/air-link/terminals/{id}/profiles",
            "POST/v1/marketAdmin/vas/air-link/terminals/{id}/switch-profile",
            "GET/v1/marketAdmin/vas/air-link/terminals/deleted",
            "GET/v1/marketAdmin/vas/air-link/terminals/operators",
            "POST/v1/marketAdmin/vas/air-link/terminals/import/template/download",
            "POST/v1/marketAdmin/vas/air-link/terminals/import",
            "POST/v1/marketAdmin/vas/air-link/terminals/add",
            "GET/v1/marketAdmin/vas/air-link/terminals/dashboard-statistics",
            "GET/v1/marketAdmin/vas/air-link/terminals",
            "GET/v1/marketAdmin/vas/air-link/terminals/activate-histories",
            "GET/v1/marketAdmin/vas/air-link/terminals/activate-histories/{id}/download",
            "POST/v1/marketAdmin/vas/air-link/terminals/disable",
            "POST/v1/marketAdmin/vas/air-link/terminals/resume",
            "POST/v1/marketAdmin/vas/air-link/terminals/delete",
            "GET/v1/marketAdmin/vas/reseller/{serviceType}"
    );

    private final List<String> AIR_LINK_READONLY_URLS = StringUtils.append(AIR_LINK_URLS,
            "-POST/v1/marketAdmin/vas/air-link/terminals/{id}/switch-profile",
            "-POST/v1/marketAdmin/vas/air-link/terminals/import/template/download",
            "-POST/v1/marketAdmin/vas/air-link/terminals/import",
            "-POST/v1/marketAdmin/vas/air-link/terminals/add",
            "-POST/v1/marketAdmin/vas/air-link/terminals/disable",
            "-POST/v1/marketAdmin/vas/air-link/terminals/resume",
            "-POST/v1/marketAdmin/vas/air-link/terminals/delete"
    );

    private final List<String> AIR_LOAD_URLS = Lists.newArrayList(
            "GET/v1/marketAdmin/vas/air-load/terminals/active-tasks",
            "GET/v1/marketAdmin/vas/air-load/terminals/check-import",
            "POST/v1/marketAdmin/vas/air-load/terminals/import",
            "POST/v1/marketAdmin/vas/air-load/terminals/add",
            "POST/v1/marketAdmin/vas/air-load/terminals/import/template/download",
            "GET/v1/marketAdmin/vas/air-load/terminals/{activeTaskId}/download",
            "GET/v1/marketAdmin/vas/air-load/terminals/terminal-import-status"
    );

    private final List<String> AIR_LOAD_READONLY_URLS = StringUtils.append(AIR_LOAD_URLS,
            "-POST/v1/marketAdmin/vas/air-load/terminals/import",
            "-POST/v1/marketAdmin/vas/air-load/terminals/add",
            "-POST/v1/marketAdmin/vas/air-load/terminals/import/template/download"
    );

    private final List<String> AIR_LAUNCHER_URLS = Lists.newArrayList("GET/v1/marketAdmin/vas/services",
            "GET/v1/admin/launcher/templates/reseller/apps",
            "GET/v1/admin/launcher/templates/reseller/apps/**",
            "GET/v1/admin/launcher/templates",
            "GET/v1/admin/launcher/templates/{launcherTemplateId}",
            "POST/v1/admin/launcher/templates",
            "PUT/v1/admin/launcher/templates/{launcherTemplateId}",
            "DELETE/v1/admin/launcher/templates/{launcherTemplateId}",
            "PUT/v1/admin/launcher/templates/{launcherTemplateId}/rename"
    );

    private final List<String> AIR_LAUNCHER_READONLY_URLS = StringUtils.append(AIR_LAUNCHER_URLS,
            "-POST/v1/admin/launcher/templates",
            "-PUT/v1/admin/launcher/templates/{launcherTemplateId}",
            "-DELETE/v1/admin/launcher/templates/{launcherTemplateId}"
    );

    private final List<String> AIR_VIEWER_URLS = Lists.newArrayList("GET/v1/marketAdmin/vas/services",
            "GET/v1/admin/vas/air-viewer/operationInfos",
            "GET/v1/admin/vas/air-viewer/fileTransferInfos",
            "GET/v1/admin/terminal-management/terminals/tid-sn/{tidOrSN}",
            "GET/v1/admin/terminal-management/terminals/{terminalId}/air-viewer",
            "GET/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/check/checkup/version",
            "POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/start",
            "POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/install",
            "POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/checkup/install"
    );

    private final List<String> AIR_VIEWER_READONLY_URLS = StringUtils.append(AIR_VIEWER_URLS,
            "-POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/start",
            "-POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/install",
            "-POST/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/checkup/install"
    );

    private final List<String> INDUSTRY_SOLUTION_MENU_URLS = Lists.newArrayList(
            "/v1/admin/service/industry-solution",
            "GET/v1/admin/common/factory/model/tree",
            "GET/v1/admin/common/resellers/tree",
            "GET/v1/admin/common/resellers"
    );


    private final List<String> INDUSTRY_SOLUTION_MENU_READONLY_URLS = StringUtils.append(INDUSTRY_SOLUTION_MENU_URLS,
            "-POST*/v1/admin/service/industry-solution",
            "-PUT*/v1/admin/service/industry-solution",
            "-DELETE*/v1/admin/service/industry-solution"
    );

    @SuppressWarnings("unchecked")
    private SwaggerApi extractApi(Entry<String, ?> entry) {
        SwaggerApi api = new SwaggerApi();
        api.setPath(entry.getKey());
        api.setOperations(new ArrayList<>());
        LinkedTreeMap<String, ?> methods = (LinkedTreeMap<String, ?>) entry.getValue();
        for (Map.Entry<String, ?> method : methods.entrySet()) {
            SwaggerApiOperation operation = new SwaggerApiOperation();
            operation.setMethod(StringUtils.upperCase(method.getKey()));
            String summary = (String) ((LinkedTreeMap<String, ?>) method.getValue()).get("operationId");
            operation.setSummary(summary);
            api.getOperations().add(operation);
        }
        return api;
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testGetApiDocs() throws IOException {
        String mainApiDocsUrl = "http://localhost:" + port + "/p-market-api/v3/api-docs";
        String docString = new TestRestTemplate().getForObject(mainApiDocsUrl, String.class);
        Map<?, ?> data = new Gson().fromJson(docString, Map.class);

        List<SwaggerApi> allAPIs = new ArrayList<>();
        if (data != null) {
            LinkedTreeMap<String, ?> pathMap = (LinkedTreeMap<String, ?>) data.get("paths");
            for (Map.Entry<String, ?> entry : pathMap.entrySet()) {
                allAPIs.add(extractApi(entry));
            }
        }
        allAPIs.sort(Comparator.comparing(SwaggerApi::getPath).thenComparing(o -> o.getOperations().get(0).getMethod()));
        long i = 10000L;
        List<Resource> resourceList = new ArrayList<>();
        for (SwaggerApi api : allAPIs) {
            for (SwaggerApiOperation op : api.getOperations()) {
                Resource resource = new Resource();
                resource.setId(i);
                resource.setName(op.getSummary());
                resource.setUrl(api.getPath());
                resource.setMethod(op.getMethod().toUpperCase());
                resource.setMarketRequired(RequiredStatus.NONE);

                if (!ResourceUtils.isValidResource(resource, NOT_ACTIVE_MARKET_REQUIRED_URLS)) {
                    resource.setMarketRequired(RequiredStatus.ACTIVE);
                }

                resourceList.add(resource);
                i++;
            }
        }

        StringBuilder resourceInsertValues = new StringBuilder("DELETE FROM `PAX_PRIVILEGE_RESOURCE`;\nDELETE FROM `PAX_RESOURCE`;\n\n");

        resourceInsertValues.append("INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES\n");

        for (Resource resource : resourceList) {
            resourceInsertValues.append("  ('").append(resource.getId()).append("', '").append(resource.getName()).append("', '").append(resource.getUrl()).append("', '").append(resource.getMethod()).append("', null, '").append(resource.getMarketRequired()).append("', '").append(resource.getApplicationRequired()).append("', '1', NOW(), '1', NOW()),\n");
        }

        resourceInsertValues = new StringBuilder(resourceInsertValues.substring(0, resourceInsertValues.lastIndexOf(",")) + ";\n");

        Map<Integer, List<String>> privilegeMap = new LinkedHashMap<>();
        privilegeMap.put(1, USER_FUNC_URLS);
        privilegeMap.put(2, DEVELOPER_FUNC_URLS);
        privilegeMap.put(3, SUPER_FUNC_URLS);
        privilegeMap.put(20, MERCHANT_PORTAL_URLS);
        privilegeMap.put(51, MENU_DASHBOARD_URLS);
        privilegeMap.put(53, MAP_MENU_URLS);
        privilegeMap.put(1041, ALARM_MENU_READONLY_URLS);
        privilegeMap.put(1042, ALARM_MENU_URLS);
        privilegeMap.put(601, SUBSCRIBE_MENU_READONLY_URLS);
        privilegeMap.put(602, SUBSCRIBE_MENU_URLS);
        privilegeMap.put(611, APP_MENU_READONLY_URLS);
        privilegeMap.put(612, APP_MENU_URLS);
        privilegeMap.put(661, RESELLER_FIRMWARE_LIST_READONLY_URLS);
        privilegeMap.put(662, RESELLER_FIRMWARE_LIST_MENU_URLS);
        privilegeMap.put(621, DEVELOPER_MENU_READONLY_URLS);
        privilegeMap.put(622, DEVELOPER_MENU_URLS);
        privilegeMap.put(641, ZTE_MENU_READONLY_URLS);
        privilegeMap.put(642, ZTE_MENU_URLS);
        privilegeMap.put(651, RESELLER_APP_LIST_READONLY_URLS);
        privilegeMap.put(652, RESELLER_APP_LIST_URLS);
        privilegeMap.put(671, APPROVAL_CENTER_MENU_READONLY_URLS);
        privilegeMap.put(672, APPROVAL_CENTER_MENU_URLS);
        privilegeMap.put(711, TERMINAL_MENU_READONLY_URLS);
        privilegeMap.put(712, TERMINAL_MENU_URLS);
        privilegeMap.put(7124, FUNC_TERMINAL_FM);
        privilegeMap.put(7126, FUNC_TERMINAL_LAUNCHER_PUSH);
        privilegeMap.put(71211, FUNC_TERMINAL_RESELLER_CRUD);
        privilegeMap.put(71213, FUNC_TERMINAL_MERCHANT_CRUD);
        privilegeMap.put(71212, FUNC_TERMINAL_ORG_RESELLER_PROFILE);
        privilegeMap.put(71214, FUNC_TERMINAL_ORG_MERCHANT_PROFILE);
        privilegeMap.put(71221, FUNC_TERMINAL_CRUD);
        privilegeMap.put(71222, FUNC_TERMINAL_SETTING);
        privilegeMap.put(71231, FUNC_TERMINAL_APP_PUSH);
        privilegeMap.put(71232, FUNC_TERMINAL_APP_PARAM);
        privilegeMap.put(71233, FUNC_TERMINAL_APP_UNINSTALL);
        privilegeMap.put(71251, FUNC_TERMINAL_RKI_PUSH);
        privilegeMap.put(71252, FUNC_TERMINAL_RKI_SETTING);
        privilegeMap.put(721, GROUP_MENU_READONLY_URLS);
        privilegeMap.put(722, GROUP_MENU_URLS);
        privilegeMap.put(7221, FUNC_GROUP_CRUD);
        privilegeMap.put(7223, FUNC_GROUP_FM);
        privilegeMap.put(7224, FUNC_GROUP_RKI);
        privilegeMap.put(7225, FUNC_GROUP_MSG);
        privilegeMap.put(7226, FUNC_GROUP_PUK);
        privilegeMap.put(7227, FUNC_GROUP_LAUNCHER);
        privilegeMap.put(7228, FUNC_GROUP_SOLUTION);
        privilegeMap.put(72221, FUNC_GROUP_APP_PUSH);
        privilegeMap.put(72222, FUNC_GROUP_APP_PARAM);
        privilegeMap.put(72223, FUNC_GROUP_APP_UNINSTALL);
        privilegeMap.put(7311, APK_PARAMETER_MENU_READONLY_URLS);
        privilegeMap.put(7312, APK_PARAMETER_MENU_URLS);
        privilegeMap.put(7321, LAUNCHER_TEMPLATE_MENU_READONLY_URLS);
        privilegeMap.put(7322, LAUNCHER_TEMPLATE_MENU_URLS);
        privilegeMap.put(7331, GEOFENCE_TEMPLATE_MENU_READONLY_URLS);
        privilegeMap.put(7332, GEOFENCE_TEMPLATE_MENU_URLS);
        privilegeMap.put(7341, PRICE_TEMPLATE_MENU_READONLY_URLS);
        privilegeMap.put(7342, PRICE_TEMPLATE_MENU_URLS);
        privilegeMap.put(741, MODEL_MENU_READONLY_URLS);
        privilegeMap.put(742, MODEL_MENU_URLS);
        privilegeMap.put(781, PRODUCT_MENU_READONLY_URLS);
        privilegeMap.put(782, PRODUCT_MENU_URLS);
        privilegeMap.put(79, DATASOURCE_MENU_URLS);
        privilegeMap.put(7511, FIRMWARE_MAINTAIN_MENU_READONLY_URLS);
        privilegeMap.put(7512, FIRMWARE_MAINTAIN_MENU_URLS);
        privilegeMap.put(7521, FIRMWARE_APPROVE_MENU_READONLY_URLS);
        privilegeMap.put(7522, FIRMWARE_APPROVE_MENU_URLS);
        privilegeMap.put(7611, CLIENT_APP_MAINTAIN_MENU_READONLY_URLS);
        privilegeMap.put(7612, CLIENT_APP_MAINTAIN_MENU_URLS);
        privilegeMap.put(7621, CLIENT_APP_APPROVE_MENU_READONLY_URLS);
        privilegeMap.put(7622, CLIENT_APP_APPROVE_MENU_URLS);
        privilegeMap.put(771, TERMINAL_DEVICE_STOCK_MENU_READONLY_URLS);
        privilegeMap.put(772, TERMINAL_DEVICE_STOCK_MENU_URLS);
        privilegeMap.put(911, MARKET_SETTING_MENU_READONLY_URLS);
        privilegeMap.put(912, MARKET_SETTING_MENU_URLS);
        privilegeMap.put(92, ROLE_MENU_URLS);
        privilegeMap.put(931, USER_LIST_MENU_READONLY_URLS);
        privilegeMap.put(932, USER_LIST_MENU_URLS);
        /*privilegeMap.put(94, REPORT_MENU_URLS);*/
        privilegeMap.put(95, AUDIT_TRAIL_MENU_URLS);
        privilegeMap.put(96, REPORT_CENTER_URLS);
        privilegeMap.put(1011, MARKET_MENU_READONLY_URLS);
        privilegeMap.put(1012, MARKET_MENU_URLS);
        privilegeMap.put(1021, GLOBAL_CONFIG_MENU_READONLY_URLS);
        privilegeMap.put(1022, GLOBAL_CONFIG_MENU_URLS);
        privilegeMap.put(10511, RESELLER_MIGRATION_READONLY_URLS);
        privilegeMap.put(10512, RESELLER_MIGRATION_MENU_URLS);
        privilegeMap.put(103, DC_MENU_URLS);
        privilegeMap.put(801, SERVICE_CENTER_MENU_URLS_READONLY_URLS);
        privilegeMap.put(802, SERVICE_CENTER_MENU_URLS);
        privilegeMap.put(811, INDUSTRY_SOLUTION_MENU_READONLY_URLS);
        privilegeMap.put(812, INDUSTRY_SOLUTION_MENU_URLS);
        privilegeMap.put(821, AIR_VIEWER_READONLY_URLS);
        privilegeMap.put(822, AIR_VIEWER_URLS);
        privilegeMap.put(831, AIR_LAUNCHER_READONLY_URLS);
        privilegeMap.put(832, AIR_LAUNCHER_URLS);
        privilegeMap.put(841, AD_UP_READONLY_URLS);
        privilegeMap.put(842, AD_UP_URLS);
        privilegeMap.put(851, AIR_LINK_READONLY_URLS);
        privilegeMap.put(852, AIR_LINK_URLS);
        privilegeMap.put(861, AIR_LOAD_READONLY_URLS);
        privilegeMap.put(862, AIR_LOAD_URLS);

        StringBuilder privilegeResourceInsertValues = new StringBuilder("INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES\n");
        for (Map.Entry<Integer, List<String>> entry : privilegeMap.entrySet()) {
            for (Resource resource : resourceList) {
                if (ResourceUtils.isValidResource(resource, entry.getValue())) {
                    privilegeResourceInsertValues.append("  ('").append(entry.getKey()).append("', '").append(resource.getId()).append("'),\n");
                }
            }
            privilegeResourceInsertValues.append("\n");
        }

        privilegeResourceInsertValues = new StringBuilder(privilegeResourceInsertValues.substring(0, privilegeResourceInsertValues.lastIndexOf(",")) + ";\n");

        FileUtils.copyDataToFile((resourceInsertValues + "\n" + privilegeResourceInsertValues).getBytes(), new File("src/test/resources/db/migration/test/V10001__Update_Resources.sql"));
    }
}
