/**
 * ********************************************************************************
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * <p>
 * Copyright (C) 2018 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.api;

import com.pax.market.api.utils.CommPropertyResourceFactory;
import com.pax.market.config.DummyProducerConfig;
import com.pax.market.config.DummySaasApiConfig;
import com.pax.market.framework.common.utils.Profiles;
import org.springframework.boot.SpringApplication;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles(value = Profiles.UNIT_TEST)
@Import({DummyProducerConfig.class, DummySaasApiConfig.class})
@PropertySource(factory = CommPropertyResourceFactory.class, value={
        "file:../../config-files/src/main/resources/common/common-infra.yml",
        "file:../../config-files/src/main/resources/common/common-biz.yml",
        "file:../../config-files/src/main/resources/common/dynamic-ds.yml",
        "file:../../config-files/src/main/resources/common/pmarket-comm.yml",
        "file:../../config-files/src/main/resources/api/api-common.yml",
        "file:../../config-files/src/main/resources/common/mq.yml",
        "file:../../config-files/src/main/env/dev.properties"
})
public class TestApiApplication extends AbstractApiBootApplication {

    public static void main(String[] args) {
        new SpringApplication(TestApiApplication.class).run();
    }
}