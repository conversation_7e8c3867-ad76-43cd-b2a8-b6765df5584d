package com.pax.market.api.dto.terminal;



import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class TerminalInstalledApkDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    private String appName;
    private String packageName;
    private String versionName;
    private Long versionCode;
    private Date installTime;
}
