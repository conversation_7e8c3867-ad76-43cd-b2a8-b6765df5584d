package com.pax.market.api.rest.v1;

import com.pax.core.exception.BusinessException;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.dto.IotMarketInfo;
import com.pax.market.api.dto.IotUserInfo;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.MarketSettingKey;
import com.pax.market.constants.RoleID;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.util.FileDownloadUrlGenerator;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.request.user.UserRegisterRequest;
import com.pax.market.functional.common.CommonUserFunc;
import com.paxstore.domain.support.ResellerSettingSupport;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.role.RoleService;
import com.paxstore.global.domain.service.user.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.AuthenticationException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ChuanXi
 * @create 2023/3/20 16:12
 */

/**
 * The type terminal controller.
 */
@RestController("IotPlatformMarketUserController")
@RequestMapping("v1/internal/iot-platform")
@Api(value = "【IOT Platform内部调用】", description = "IOT Platform内部调用相关的RestAPI")
@RequiredArgsConstructor
public class IotPlatformMarketUserController extends BaseController {

    private final MarketService marketService;

    private final ResellerSettingSupport resellerSettingSupport;

    private final RoleService roleService;

    private final UserService userService;

    private final CommonUserFunc commonUserFunc;

    /**
     * Gets market.
     *
     * @throws IOException the io exception
     */
    @GetMapping(value = "market/{marketDomain}")
    @ApiOperation(value = "内部获取应用市场信息", response = MarketInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = MarketInfo.class)})
    public void getMarket(@PathVariable @NotNull String marketDomain, @RequestParam(required = false) Long userId) throws IOException {
        Market market = marketService.findMarketByDomain(marketDomain);
        if (null != market) {
            IotMarketInfo iotMarketInfo = new IotMarketInfo();
            iotMarketInfo.setId(market.getId()).setName(market.getName()).setDomain(market.getDomain()).setAdminEmail(market.getAdmin().getEmail())
                    .setExpireDate(market.getExpireDate()).setLogo(resellerSettingSupport.getRootResellerImageResourceUrl(market.getId(), MarketSettingKey.colorFullLogo));
            if (null != userId) {
                User user = userService.get(userId);
                IotUserInfo iotUserInfo = new IotUserInfo();
                iotUserInfo.setId(user.getId()).setName(user.getName()).setEmail(user.getEmail()).setLoginName(user.getLoginName()).setStatus(user.getStatus())
                        .setGender(user.getSex()).setAvatar(user.getPhoto()).setPhotoUrl(Objects.nonNull(user.getPhoto()) ? FileDownloadUrlGenerator.generateFileUrl(user.getPhoto()) : null);
                iotMarketInfo.setUserInfo(iotUserInfo);
            }
            sendResponse(iotMarketInfo);
        } else {
            throw new BusinessException(ApiCodes.MARKET_NOT_FOUND);
        }
    }

    /**
     * check market admin
     *
     * @param userId   the user id
     * @param marketId the market id
     * @throws IOException the io exception
     */
    @GetMapping("check/market/admin")
    @ApiOperation(value = "检查用户是否为市场管理员", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = BaseResponse.class)})
    public void checkMarketAdmin(@RequestParam @NotNull Long userId, @RequestParam @NotNull Long marketId) throws IOException {
        sendResponse(roleService.isUserRoleExist(userId, marketId, null, RoleID.MARKET_ADMIN));
    }

    /**
     * check market admin
     *
     * @param loginName the login name
     * @throws IOException the io exception
     */
    @GetMapping("check/exist-user")
    @ApiOperation(value = "检查用户是否存在", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = BaseResponse.class)})
    public void checkExistUser(@RequestParam @NotNull String loginName) throws IOException {
        sendResponse(Objects.isNull(userService.getByLoginName(loginName)) ? Boolean.FALSE : Boolean.TRUE);
    }

    /**
     * Register user.
     *
     * @param userRegisterRequest the user register request
     * @throws AuthenticationException the authentication exception
     * @throws IOException             the io exception
     */
    @PostMapping("user/register")
    @ApiOperation(value = "注册用户", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "注册成功", response = BaseResponse.class)})
    public void registerUser(@RequestBody UserRegisterRequest userRegisterRequest) throws IOException {
        commonUserFunc.registerUser(userRegisterRequest);
        sendResponse(ApiUtils.getSuccessJson());
    }


    /**
     * Gets market administrators.
     *
     * @throws IOException the io exception
     */
    @GetMapping(value = "market/{marketId}/administrators")
    @ApiOperation(value = "获取市场所有管理员", response = IotUserInfo.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = IotUserInfo.class)})
    public void getMarketAdministrators(@PathVariable @NotNull Long marketId) throws IOException {
        Market market = marketService.get(marketId);
        if (null != market) {
            List<IotUserInfo> iotUserInfoList = new ArrayList<>();
            Set<Long> userIds = roleService.findUserIdByMarketIdAndRoleId(marketId, Collections.singletonList(RoleID.MARKET_ADMIN), null);
            if (!CollectionUtils.isEmpty(userIds)) {
                iotUserInfoList = userIds.stream().map(id -> {
                    IotUserInfo iotUserInfo = new IotUserInfo();
                    User user = userService.get(id);
                    if (Objects.nonNull(user)) {
                        iotUserInfo.setId(user.getId()).setName(user.getName()).setLoginName(user.getLoginName()).setEmail(user.getEmail())
                                .setStatus(user.getStatus()).setGender(user.getSex()).setAvatar(user.getPhoto());
                        return iotUserInfo;
                    } else {
                        return null;
                    }

                }).filter(Objects::nonNull).collect(Collectors.toList());
            }
            sendResponse(iotUserInfoList);
        } else {
            throw new BusinessException(ApiCodes.MARKET_NOT_FOUND);
        }
    }
}
