/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.api.rest.v1;

import com.pax.core.exception.BusinessException;
import com.pax.market.api.ApiContextHolder;
import com.pax.market.api.constants.ApiConstants;
import com.pax.market.api.dto.TerminalRemoteInitRequest;
import com.pax.market.api.dto.TerminalRkiActionHistoryUpdateRequest;
import com.pax.market.api.dto.TerminalSubscribeAppRequest;
import com.pax.market.api.rest.BaseController;
import com.pax.market.api.utils.ApiUtils;
import com.pax.market.audit.biz.annotation.Audit;
import com.pax.market.audit.biz.utils.AuditLogChangeRecordUtils;
import com.pax.market.audit.common.constant.AuditActions;
import com.pax.market.audit.common.constant.AuditDataTypes;
import com.pax.market.audit.common.constant.AuditTypes;
import com.pax.market.audit.common.context.ApiAuditContext;
import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.TerminalActionStatus;
import com.pax.market.constants.TerminalStatus;
import com.pax.market.domain.entity.global.app.Apk;
import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.entity.global.app.AppCost;
import com.pax.market.domain.entity.global.app.SubscribedApp;
import com.pax.market.domain.entity.global.terminal.TerminalRegistry;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.domain.entity.market.pushtask.TerminalApkParam;
import com.pax.market.domain.entity.market.sandbox.SandboxTerminalApkParam;
import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.domain.entity.market.terminal.TerminalLog;
import com.pax.market.dto.BaseResponse;
import com.pax.market.dto.MerchantUserInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.request.terminal.TerminalMerchantLoginRequest;
import com.pax.market.dto.terminal.TerminalInfo;
import com.pax.market.framework.common.audit.AuditTrailContextHolder;
import com.pax.market.framework.common.mapper.BeanMapper;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.functional.app.AppPurchaseFunctionService;
import com.pax.market.functional.merchant.MerchantFunctionService;
import com.pax.market.functional.push.PushStub;
import com.pax.market.functional.support.AppEntitySupport;
import com.pax.market.functional.support.GroupTerminalApkParamSupport;
import com.pax.market.functional.support.TerminalApkParamSupport;
import com.pax.market.functional.support.TerminalRegistrySupport;
import com.pax.market.functional.terminal.TerminalFunctionService;
import com.pax.market.functional.utils.SchemaUtils;
import com.pax.market.functional.utils.TerminalModelHelper;
import com.pax.market.functional.vas.goinsight.GoInsightTerminalInstallAppDataSyncFunc;
import com.pax.market.notification.AppPurchaseNotificationService;
import com.paxstore.global.domain.cachable.LocalCacheMarketInfoService;
import com.paxstore.global.domain.service.app.ApkService;
import com.paxstore.global.domain.service.app.AppService;
import com.paxstore.global.domain.service.app.SubscribeAppService;
import com.paxstore.global.domain.service.market.MarketService;
import com.paxstore.global.domain.service.terminal.TerminalRegistryService;
import com.paxstore.global.domain.utils.EntityUtils;
import com.paxstore.market.domain.cachable.LocalMarketRootResellerService;
import com.paxstore.market.domain.service.organization.MerchantService;
import com.paxstore.market.domain.service.sandbox.SandboxTerminalApkService;
import com.paxstore.market.domain.service.terminal.TerminalActionHistoryService;
import com.paxstore.market.domain.service.terminal.TerminalActionService;
import com.paxstore.market.domain.service.terminal.TerminalLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * The type terminal controller.
 */
@RestController("InternalTerminalController")
@RequestMapping("v1/internal/terminal")
@Api(value = "【终端内部调用】", description = "终端内部调用相关的RestAPI")
@RequiredArgsConstructor
public class InternalTerminalController extends BaseController {
    private final TerminalFunctionService terminalFunctionService;
    private final TerminalRegistryService terminalRegistryService;
    private final TerminalRegistrySupport terminalRegistrySupport;
    private final MarketService marketService;
    private final TerminalLogService terminalLogService;
    private final TerminalActionHistoryService terminalActionHistoryService;
    private final MerchantFunctionService merchantFunctionService;
    private final MerchantService merchantService;
    private final AppService appService;
    private final ApkService apkService;
    private final SubscribeAppService subscribeAppService;
    private final AppPurchaseNotificationService appPurchaseNotificationService;
    private final AppPurchaseFunctionService appPurchaseFunctionService;
    private final GoInsightTerminalInstallAppDataSyncFunc goInsightSyncInstalledAppDataFunctionService;
    private final LocalCacheMarketInfoService localCacheMarketInfoService;
    private final LocalMarketRootResellerService localMarketRootResellerService;
    private final SandboxTerminalApkService sandboxTerminalApkService;
    private final TerminalActionService terminalActionService;
    private final PushStub pushStub;
    private final GroupTerminalApkParamSupport groupTerminalApkParamSupport;
    private final AppEntitySupport appEntitySupport;
    private final TerminalApkParamSupport terminalApkParamSupport;

    /**
     * Remote init.
     *
     * @param terminalRemoteInitRequest the terminal remote init request
     * @throws IOException the io exception
     */
    @PostMapping(value = "init")
    @ApiOperation(value = "终端内部初始化", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "初始化成功", response = BaseResponse.class)})
    @Audit(type = AuditTypes.TERMINAL, primaryAction = AuditActions.REMOTE_INIT, dataType = AuditDataTypes.TERMINAL)
    public void remoteInit(@RequestBody TerminalRemoteInitRequest terminalRemoteInitRequest) throws IOException {
        TerminalRegistry terminal = terminalRegistryService.getByTID(terminalRemoteInitRequest.getTid());
        if (terminal == null) {
            AuditTrailContextHolder.getInstance().clear();
            throw new BusinessException(ApiCodes.TERMINAL_NOT_FOUND);
        }
        RedisUtils.tryLock(String.format("distributionLock:terminalRemoteInit:%s", terminalRemoteInitRequest.getTid()), () -> {
            MarketInfo marketInfo = localCacheMarketInfoService.getMarketInfo(terminal.getMarketId());
            ApiContextHolder.setApiContext(marketInfo, BeanMapper.map(terminal, TerminalInfo.class));
            localMarketRootResellerService.loadMarketRootResellerId(marketInfo);
            EntityUtils.loadResellerSignatureSetting(marketInfo);
            fillAuditContext(terminal);
            terminalRegistrySupport.loadTerminalModelResellerMerchantDetails(terminal);
            TerminalModelHelper.validateForTerminalRemoteInit(terminal, terminalRemoteInitRequest.getModelName());
            terminal.setSerialNo(StringUtils.trimAndUpperCase(terminalRemoteInitRequest.getNewSerialNo()));
            boolean terminalNotActive = !TerminalStatus.ACTIVE.equals(terminal.getStatus());
            terminalFunctionService.remoteInit(terminal);
            if (terminalNotActive) {
                //PAXSTORE-28876终端初始化成功立刻验证分组变量，否则不能立刻拿到分组任务
                for (TerminalAction initTerminalAction : terminalActionService.findInitTerminalGroupParamActions(terminal.getId())) {
                    groupTerminalApkParamSupport.validateGroupTerminalApkParamVariables(initTerminalAction);
                }
                if (terminalRemoteInitRequest.isInitBy3rdApp()) {
                    pushStub.pushTerminalActivationMessage(terminal);
                }
            }
        });
        sendResponse(ApiUtils.getSuccessJson());
    }

    private void fillAuditContext(TerminalRegistry terminal) {
        ApiAuditContext apiAuditContext = (ApiAuditContext) AuditTrailContextHolder.getInstance().getCurrent();
        apiAuditContext.addEntityIdIfMissing(terminal.getId());
        apiAuditContext.setMarketId(terminal.getMarketId());
        apiAuditContext.setTerminalId(terminal.getId());
        apiAuditContext.setUserId(SystemConstants.UPDATED_BY_TERMINAL_ID);
        apiAuditContext.setUsername(SystemConstants.UPDATED_BY_TERMINAL_NAME);
        apiAuditContext.setUserLoginName(SystemConstants.UPDATED_BY_TERMINAL_LOGIN_NAME);
        apiAuditContext.setResellerId(terminal.getResellerId());
        if (StringUtils.isNotBlank(terminal.getSerialNo())) {
            apiAuditContext.setAction(AuditActions.REMOTE_REPLACE);
        }
        AuditLogChangeRecordUtils.resolveOldOrNewRecord(apiAuditContext, true);
    }

    /**
     * Upload terminal log.
     *
     * @throws IOException the io exception
     */
    @PostMapping(value = "logs")
    @ApiOperation(value = "终端内部上送日志", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "创建成功", response = BaseResponse.class)})
    public void uploadTerminalLog(HttpServletRequest request) throws IOException {
        TerminalLog terminalLog = getParameterValue(request, "terminalLog", TerminalLog.class);
        MultipartFile logFile = null;
        if (request instanceof MultipartHttpServletRequest multipartRequest) {
            logFile = multipartRequest.getFile("logFile");
        }
        terminalLogService.createTerminalLog(terminalLog, logFile);
        sendResponse(ApiUtils.getSuccessJson(), ApiConstants.HTTP_STATUS_OK);
    }


    /**
     * Merchant login.
     *
     * @param request the request
     * @throws IOException the io exception
     */
    @PostMapping(value = "merchant/login")
    @ApiOperation(value = "终端商户内部登录", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "登录成功", response = BaseResponse.class)})
    public void merchantLogin(@RequestBody TerminalMerchantLoginRequest request) throws IOException {
        merchantFunctionService.validateMerchantUserLogin(request);
        sendResponse(ApiUtils.getSuccessJson());
    }

    /**
     * Gets merchant user.
     *
     * @param merchantId the merchant id
     * @throws IOException the io exception
     */
    @GetMapping(value = "merchant/user")
    @ApiOperation(value = "内部获取终端商户用户信息", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = MerchantUserInfo.class)})
    public void getMerchantUser(@RequestParam Long merchantId) throws IOException {
        sendResponse(merchantFunctionService.getMerchantUserInfo(merchantId));
    }

    /**
     * Gets merchant user.
     *
     * @param request the request
     * @throws IOException the io exception
     */
    @PostMapping("rki/action/status")
    @ApiOperation(value = "终端内部更新RKI任务状态", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "更新成功", response = BaseResponse.class)})
    public void updateActionHistoryStatus(@RequestBody TerminalRkiActionHistoryUpdateRequest request) throws IOException {
        //RKI服务端如果已经更新结果为成功，终端又来更新失败（代表安装失败），替换成终端的结果
        TerminalAction historyAction = terminalActionHistoryService.getLatestTerminalActionHistory(request.getTerminalId(), request.getReferenceId(), request.getActionType());
        if (historyAction != null && historyAction.getStatus() == TerminalActionStatus.SUCCEED) {
            historyAction.setStatus(request.getDownloadStatus());
            historyAction.setErrorCode(request.getErrorCode());
            historyAction.setRemarks(request.getRemarks());
            terminalActionHistoryService.updateTerminalActionHistory(historyAction);
        }
        sendResponse(ApiUtils.getSuccessJson());
    }

    @PostMapping("subscribe")
    @ApiOperation(value = "终端内部订阅应用", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void subscribeApp(@RequestBody TerminalSubscribeAppRequest request) throws IOException {
        App app = appService.get(request.getAppId());
        SubscribedApp subscribedApp = subscribeAppService.subscribeApp(request.getMerchantId(), request.getTerminalId(), app);
        if (subscribedApp != null) {
            Merchant merchant = merchantService.get(request.getMerchantId());
            if (merchant != null && StringUtils.isNotEmpty(merchant.getEmail())) {
                Apk latestApk = apkService.getLatestOnlineOfflineApkByAppId(request.getAppId());
                apkService.loadApkDetail(latestApk);
                app.setLatestOnlineApk(latestApk);
                AppCost appCost = null;
                if (getCurrentMarket().getAllowAppCostSpecific() && getCurrentMarket().getAllowAppOfflinePurchase()) {
                    TerminalRegistry terminalRegistry = terminalRegistryService.get(request.getTerminalId());
                    appCost = appEntitySupport.getAppCostRecursion(request.getAppId(), terminalRegistry.getResellerId());
                }
                appPurchaseNotificationService.sendMerchantPurchaseAppNotification(request.getTerminalId(),
                        merchant.getEmail(),
                        subscribedApp.getSubscribeDate(),
                        app,
                        appCost,
                        request.getTerminalSN(),
                        BeanMapper.map(marketService.get(request.getMarketId()), MarketInfo.class),
                        RequestLocaleHolder.getLocale()
                );
            }
            goInsightSyncInstalledAppDataFunctionService.syncSubscribeAppMsgToGoInsight(Boolean.TRUE, subscribedApp);
        }
        sendResponse(ApiUtils.getSuccessJson());
    }

    @PutMapping("subscribe")
    @ApiOperation(value = "终端内部取消订阅应用", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void unsubscribeApp(@RequestBody TerminalSubscribeAppRequest request) throws IOException {
        App app = appService.getWithDeleted(request.getAppId());
        SubscribedApp subscribedApp = subscribeAppService.unsubscribeApp(request.getMerchantId(), request.getTerminalId(), app);
        if (Objects.nonNull(subscribedApp)) {
            subscribedApp.setApp(app);
            goInsightSyncInstalledAppDataFunctionService.syncSubscribeAppMsgToGoInsight(Boolean.FALSE, subscribedApp);
        }
        sendResponse(ApiUtils.getSuccessJson());
    }

    @PostMapping("purchases/payment/offline")
    @ApiOperation(value = "提交终端购买应用线下支付请求", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "返回成功", response = BaseResponse.class)})
    public void offlinePayApp(@Valid @RequestParam Long appId, @Valid @RequestParam Long terminalId) throws IOException {
        App app = appService.get(appId);
        TerminalRegistry terminalRegistry = terminalRegistryService.get(terminalId);

        Date purchaseDate = new Date();
        boolean isFirstDownload = appPurchaseFunctionService.savePurchasedApp(terminalRegistry.getMerchantId(), terminalId, app, purchaseDate);
        if (isFirstDownload) {
            appService.increaseDownloads(appId);
            Merchant merchant = merchantService.get(terminalRegistry.getMerchantId());
            if (merchant != null && StringUtils.isNotEmpty(merchant.getEmail())) {
                Apk latestApk = apkService.getLatestOnlineOfflineApkByAppId(appId);
                apkService.loadApkDetail(latestApk);
                app.setLatestOnlineApk(latestApk);
                AppCost appCost = null;
                if (getCurrentMarket().getAllowAppCostSpecific() && getCurrentMarket().getAllowAppOfflinePurchase()) {
                    appCost = appEntitySupport.getAppCostRecursion(appId, terminalRegistry.getResellerId());
                }
                appPurchaseNotificationService.sendMerchantPurchaseAppNotification(getCurrentTerminalId(),
                        merchant.getEmail(),
                        purchaseDate,
                        app,
                        appCost,
                        terminalRegistry.getSerialNo(),
                        BeanMapper.map(marketService.get(terminalRegistry.getMarketId()), MarketInfo.class),
                        RequestLocaleHolder.getLocale()
                );
            }
            syncAppPurchaseMsgToGoInsight(terminalRegistry.getMerchantId(), terminalId, app, purchaseDate);
        }
        sendResponse(ApiUtils.getSuccessJson());
    }

    private void syncAppPurchaseMsgToGoInsight(long merchantId, long terminalId, App app, Date purchaseDate) {
        //在线、离线购买，统一（同月付费APP订阅逻辑）字段上送到GoInsight
        SubscribedApp subscribedApp = new SubscribedApp();
        subscribedApp.setTerminalId(terminalId);
        subscribedApp.setMerchantId(merchantId);
        subscribedApp.setApp(app);
        subscribedApp.setSubscribeDate(purchaseDate);
        goInsightSyncInstalledAppDataFunctionService.syncPurchaseAppMsgToGoInsight(Boolean.TRUE, subscribedApp);
    }

    @GetMapping(value = "action")
    @ApiOperation(value = "内部获取终端最后一次参数成功推送记录", response = BaseResponse.class)
    @ApiResponses(value = {@ApiResponse(code = ApiConstants.HTTP_STATUS_OK, message = "获取成功", response = MerchantUserInfo.class)})
    public void getLastSuccessParamAction(@RequestParam Long terminalId, @RequestParam Long appId, @RequestParam(required = false) String paramTemplateName, @RequestParam Boolean isSandbox) throws IOException {
        if (BooleanUtils.isTrue(isSandbox)) {
            List<Long> apkIdList = apkService.findApkIdsByAppId(appId);
            SandboxTerminalApkParam sandboxTerminalApkParam = CollectionUtils.isEmpty(apkIdList)
                    ? null : sandboxTerminalApkService.getLatestHistoryApkParam(terminalId, apkIdList, paramTemplateName);
            if (sandboxTerminalApkParam == null) {
                sendResponse(getSuccessJson());
                return;
            }
            sendResponse(BeanMapper.map(sandboxTerminalApkParam, TerminalParamActionResponse.class));
        } else {
            List<Long> apkIdList = apkService.findApkIdsIncludeDeleteByAppId(appId);
            TerminalApkParam terminalApkParam = CollectionUtils.isEmpty(apkIdList) ? null : terminalApkParamSupport.getTerminalLastSuccessHistory(terminalId, appId, apkIdList, paramTemplateName);
            if (terminalApkParam == null) {
                sendResponse(getSuccessJson());
                return;
            }
            if (StringUtils.isNotBlank(terminalApkParam.getParamVariables())) {
                terminalApkParam.setParamVariables(SchemaUtils.convertParamVariables(null, terminalApkParam.getApkId(), terminalApkParam.getParamTemplateName(), terminalApkParam.getParam(), terminalApkParam.getParamVariables()));
            }
            sendResponse(BeanMapper.map(terminalApkParam, TerminalParamActionResponse.class));
        }
    }

    @Getter
    @Setter
    @ToString
    private static class TerminalParamActionResponse {
        private long apkId;
        private String paramFiles;
        private String paramFileMap;
        private String paramVariables;
        private Date actionTime;
        private String downloadUrl;
        private long fileSize;
        private String md5;
        private String sha256;
    }
}
