<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pax.market</groupId>
        <artifactId>p-market-database-partition-mq</artifactId>
        <version>9.8.0-SNAPSHOT</version>
    </parent>

    <artifactId>p-market-database-partition-mq-consumer-kafka</artifactId>
    <name>PAX Market :: Database Partition :: MQ :: Consumer Kafka</name>

    <dependencies>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-database-partition-mq-consumer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-database-partition-mq-shared</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
