package com.pax.market.clear.data.mq.contract;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
public class ClearTerminalAccessoryMessage extends AbstractMessage {
    @Serial
    private static final long serialVersionUID = 4318478100856875422L;

    private Long accessoryId;

    public ClearTerminalAccessoryMessage(Long marketId, Long accessoryId) {
        super.setMarketId(marketId);
        this.accessoryId = accessoryId;
    }
}
