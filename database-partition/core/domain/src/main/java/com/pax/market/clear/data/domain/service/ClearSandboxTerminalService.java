package com.pax.market.clear.data.domain.service;

import com.pax.market.clear.data.domain.dao.ClearSandboxTerminalDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ClearSandboxTerminalService {
    private final ClearSandboxTerminalDao dao;

    public List<Long> findDeletedSandboxTerminalIdList(Long marketId) {
        return dao.findDeletedSandboxTerminalIdList(marketId);
    }

    @Transactional
    public int deleteSandboxTerminal(Long terminalId) {
        return dao.deleteSandboxTerminal(terminalId);
    }

    @Transactional
    public int deleteSandboxTerminalApk(Long terminalId) {
        return dao.deleteSandboxTerminalApk(terminalId);
    }

    @Transactional
    public int deleteSandboxTerminalApkParam(Long terminalId) {
        return dao.deleteSandboxTerminalApkParam(terminalId);
    }
}
