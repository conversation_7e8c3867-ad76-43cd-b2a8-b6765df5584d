package com.pax.market.clear.data.domain.dao;

import com.pax.market.framework.common.persistence.BaseDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ClearTerminalAccessoryDao extends BaseDao {
    List<Long> findDeletedTerminalAccessoryIds(@Param("terminalId") Long terminalId);
    int deleteTerminalAccessory(Long id);
    int deleteTerminalAccessoryDetail(Long accessoryId);
    int deleteTerminalAccessoryEvent(Long accessoryId);
    int deleteTerminalAccessoryAction(Long accessoryId);
}
