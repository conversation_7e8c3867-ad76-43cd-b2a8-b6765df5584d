package com.pax.market.clear.data.server;

import com.pax.api.cache.config.CacheServiceConfig;
import com.pax.core.spring.SpringApplicationArgsUpdater;
import com.pax.core.utils.SystemPropertiesLoader;
import com.pax.market.clear.data.domain.config.DatabasePartitionDomainConfig;
import com.pax.market.clear.data.server.config.DatabasePartitionConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

import java.io.File;
import java.util.Arrays;
import java.util.List;

@SpringBootApplication
@Import(value = {
        CacheServiceConfig.class,
        DatabasePartitionDomainConfig.class,
        DatabasePartitionConfig.class
})
@ComponentScan(basePackages = {
        "com.pax.market.clear.data",
        "com.pax.market.clear.data.mq.producer",
        "com.pax.market.clear.data.mq.consumer",
        "com.pax.market.framework.common"
})
public class DatabasePartitionApp extends SpringBootServletInitializer {
    private static List<String> propFileList = Arrays.asList(
            "application.yml",
            "common-infra.yml",
            "common-biz.yml",
            "mq.yml",
            "pmarket-comm.yml",
            "dynamic-ds.yml"
    );


    public static void main(String[] args) {
        initPaxstoreConfigFileLocation4Dev(getConfigModuleResPath());
        SystemPropertiesLoader.loadConfPropsAsSystemProps();
        SpringApplication app = new SpringApplication(DatabasePartitionApp.class);
        app.addListeners(new ApplicationPidFileWriter("app.pid"));
        app.run(SpringApplicationArgsUpdater.updateArgs(args, propFileList));
    }

    private static String getConfigModuleResPath() {
        return DatabasePartitionApp.class.getResource("/").getFile() + "../../../../config-files/src/main/resources/";
    }


    protected static void initPaxstoreConfigFileLocation4Dev(String configModuleResPath) {
        String localDevConfigFilePath = configModuleResPath + "../env/dev.properties";
        File file = new File(localDevConfigFilePath);
        if (file.exists()) {
            System.setProperty(SystemPropertiesLoader.PAXSTORE_CONF_PROPS_LOCATION_KEY, localDevConfigFilePath);
        }
    }
}
