package com.pax.market.schedule;

import com.pax.market.constants.MarketStatus;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.framework.common.annotation.RedisLock;
import com.pax.market.functional.admin.service.airshield.ScheduleAirShieldFuncService;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.support.dynamic.datasource.tools.PaxDsUtils;
import com.paxstore.schedule.global.domain.service.datasource.ScheduleDataSourceMarketService;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/15
 */
@Component
@Configurable
@EnableScheduling
@RequiredArgsConstructor
@Slf4j
public class AirShieldScheduledTasks {

    private final ScheduleAirShieldFuncService airShieldFuncService;

    private final ScheduleDataSourceMarketService dataSourceMarketService;
    private final ScheduleMarketService marketService;

    //每天凌晨零点执行
    @Scheduled(cron = "${schedule.cron.expire-airshield-alarm:0 0 0 * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "expireAirShieldAlarm")
    public void expireAirShieldAlarm() {
        log.info("Executing job: expireAirShieldAlarm");
        for (String ds : PaxDsUtils.getAllDataSources()) {
            PaxDynamicDsThreadLocal.setPreferenceDatasource(ds);
            if(marketService.isMarketActive(dataSourceMarketService.getMarketIdBysDbInstanceName(ds))){
                airShieldFuncService.clearDetectionAlarm();
            }
            PaxDynamicDsThreadLocal.removePreferenceDatasource();
        }

        log.info("End of process expireAirShieldAlarm");
    }
    //每分钟执行一次
    @Scheduled(cron = "0 0/1 * * * ?")
    @RedisLock(prefix = "scheduleTask:", key = "delaySendAlarm")
    public void delaySendAlarm() {
        log.info("Executing job: delaySendAlarm");
        for (String ds : PaxDsUtils.getAllDataSources()) {
            PaxDynamicDsThreadLocal.setPreferenceDatasource(ds);
            Long marketId = dataSourceMarketService.getMarketIdBysDbInstanceName(ds);
            if (marketService.isMarketActive(marketId)) {
                airShieldFuncService.delaySendAlarm(marketId);
            }
            PaxDynamicDsThreadLocal.removePreferenceDatasource();
        }
        log.info("End of process delaySendAlarm");
    }
}
