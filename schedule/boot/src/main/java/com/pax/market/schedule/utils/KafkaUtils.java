/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2022 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.schedule.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class KafkaUtils {

    /**
     * 获取单个topic的消费量
     *
     * @param consumer the consumer
     * @param topic    the topic
     * @return the long
     */
    public long getTopicCommitOffset(Consumer<String, String> consumer, String topic) {
        long commitOffset = 0L;
        try {
            List<PartitionInfo> partitionInfos = consumer.partitionsFor(topic);
            if (CollectionUtils.isNotEmpty(partitionInfos)) {
                for (PartitionInfo partitionInfo : partitionInfos) {
                    TopicPartition topicPartition = new TopicPartition(partitionInfo.topic(), partitionInfo.partition());
                    OffsetAndMetadata committed = consumer.committed(topicPartition);
                    if (committed != null) {
                        commitOffset += committed.offset();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Get topic:{} commit offset error", topic);
        }
        return commitOffset;
    }


    /**
     * 获取单个topic接收量
     *
     * @param consumer the consumer
     * @param topic    the topic
     * @return the long
     */
    public Long getTopicEndOffset(Consumer<String, String> consumer, String topic) {
        Long endOffset = 0L;
        try {
            List<PartitionInfo> partitionInfos = consumer.partitionsFor(topic);
            if (CollectionUtils.isNotEmpty(partitionInfos)) {
                List<TopicPartition> partitions = new ArrayList<>();
                for (PartitionInfo partitionInfo : partitionInfos) {
                    TopicPartition topicPartition = new TopicPartition(partitionInfo.topic(), partitionInfo.partition());
                    partitions.add(topicPartition);
                }
                Collection<Long> values = consumer.endOffsets(partitions).values();
                if (CollectionUtils.isNotEmpty(values)) {
                    for (Long value : values) {
                        endOffset += value;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Get topic:{} end offset error", topic);
        }
        return endOffset;
    }
}
