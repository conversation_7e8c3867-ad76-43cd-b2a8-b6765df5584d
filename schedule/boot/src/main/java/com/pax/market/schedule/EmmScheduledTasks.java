/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.schedule;

import com.google.common.collect.Lists;
import com.pax.api.cache.CacheService;
import com.pax.core.json.JsonMapper;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.annotation.RedisLock;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.functional.emm.*;
import com.pax.market.mq.contract.emm.EmmDeviceSwitchPolicyMessage;
import com.pax.market.mq.contract.emm.EmmPolicyChangedMessage;
import com.pax.market.mq.contract.emm.zte.EmmZteAsyncOperationMessage;
import com.pax.market.mq.producer.gateway.emm.EmmDeviceSwitchPolicyGateway;
import com.pax.market.mq.producer.gateway.emm.EmmPolicyChangedGateway;
import com.pax.market.mq.producer.gateway.emm.EmmZteAsyncOperationGateway;
import com.pax.market.schedule.mq.contract.ScheduleEmmNoAuthDeviceSwitchPolicyMessage;
import com.pax.market.schedule.mq.contract.ScheduleEmmZteDeviceRecordChangedMessage;
import com.pax.market.schedule.mq.producer.gateway.vas.emm.ScheduleEmmNoAuthDeviceSwitchPolicyGateway;
import com.pax.market.schedule.mq.producer.gateway.vas.emm.ScheduleEmmZteDeviceRecordChangedGateway;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketService;
import com.paxstore.schedule.global.domain.service.vas.emm.ScheduleEmmEnterpriseService;
import com.paxstore.schedule.global.domain.service.vas.emm.ScheduleEmmZteRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * EMM service schedule tasks
 */
@Component
@Configurable
@EnableScheduling
@RequiredArgsConstructor
public class EmmScheduledTasks {

    private static final Logger logger = LoggerFactory.getLogger(EmmScheduledTasks.class);

    private final CacheService cacheService;
    private final ScheduleMarketService marketService;
    private final ScheduleEmmEnterpriseService enterpriseService;
    private final ScheduleEmmZteRecordService zteRecordService;
    private final ScheduleEmmUnsubscribeFunc emmUnsubscribeFunc;
    private final ScheduleEmmSyncPolicyFunc emmSyncPolicyFunc;
    private final ScheduleEmmSyncDataFunc emmSyncDataFunc;
    private final ScheduleEmmClearDataFunc emmClearDataFunc;
    private final ScheduleEmmDeviceFunc emmDeviceFunc;
    private final ScheduleEmmDeviceRegistryFunc emmDeviceRegistryFunc;
    private final EmmPolicyChangedGateway emmPolicyChangedGateway;
    private final EmmDeviceSwitchPolicyGateway emmDeviceSwitchPolicyGateway;
    private final EmmZteAsyncOperationGateway emmZteAsyncOperationGateway;
    private final ScheduleEmmNoAuthDeviceSwitchPolicyGateway emmNoAuthDeviceSwitchPolicyGateway;
    private final ScheduleEmmZteDeviceRecordChangedGateway emmZteDeviceRecordChangedGateway;

    /**
     * Send update policy message
     */
    @Scheduled(cron = "${schedule.cron.send-emm-policy-message:* * * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "sendEmmPolicyMessage", protectTime = 500)
    public void sendEmmPolicyMessage() {
        try {
            long policyIdSetSize = cacheService.sizeofSet(CacheNames.EMM_POLICY_IDS_OF_POLICY_UPDATE);
            if (policyIdSetSize > 0) {
                List<EmmPolicyChangedMessage> messages = Lists.newArrayList();
                List<Object> policyIdList = cacheService.popSet(CacheNames.EMM_POLICY_IDS_OF_POLICY_UPDATE, SystemPropertyHelper.getEmmPolicyMessageSendNumberPerBatch());
                if (CollectionUtils.isNotEmpty(policyIdList)) {
                    policyIdList.forEach(policyId -> {
                        String[] strings = StringUtils.split(String.valueOf(policyId), "_");
                        if (strings.length == 3 && StringUtils.isNotBlank(strings[0]) && StringUtils.isNotBlank(strings[1]) && StringUtils.isNotBlank(strings[2])) {
                            EmmPolicyChangedMessage message = new EmmPolicyChangedMessage();
                            message.setMarketId(LongUtils.parse(strings[0]));
                            message.setPolicyType(strings[1]);
                            message.setReferenceId(LongUtils.parse(strings[2]));
                            messages.add(message);
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(messages)) {
                    logger.info("[sendEmmPolicyMessage] Send emm policy size: {},message: {}", messages.size(), JsonMapper.toJsonString(messages));
                    messages.forEach(emmPolicyChangedGateway::send);
                }
            }
        } catch (Exception e) {
            logger.error("Encounter error when batch send emm policy message", e);
        }
    }

    /**
     * Send emm device switch policy message
     */
    @Scheduled(cron = "${schedule.cron.send-emm-device-switch-policy-message:* * * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "sendEmmDeviceSwitchPolicyMessage", protectTime = 500)
    public void sendEmmDeviceSwitchPolicyMessage() {
        try {
            long deviceIdSetSize = cacheService.sizeofSet(CacheNames.EMM_DEVICE_IDS_OF_POLICY_UPDATE);
            if (deviceIdSetSize > 0) {
                List<EmmDeviceSwitchPolicyMessage> messages = Lists.newArrayList();
                List<Object> deviceIdList = cacheService.popSet(CacheNames.EMM_DEVICE_IDS_OF_POLICY_UPDATE, SystemPropertyHelper.getEmmPolicyMessageSendNumberPerBatch());
                if (CollectionUtils.isNotEmpty(deviceIdList)) {
                    deviceIdList.forEach(deviceId -> {
                        String[] strings = StringUtils.split(String.valueOf(deviceId), "_");
                        if (strings.length == 2 && StringUtils.isNotBlank(strings[0]) && StringUtils.isNotBlank(strings[1])) {
                            EmmDeviceSwitchPolicyMessage message = new EmmDeviceSwitchPolicyMessage();
                            message.setMarketId(LongUtils.parse(strings[0]));
                            message.setDeviceId(LongUtils.parse(strings[1]));
                            messages.add(message);
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(messages)) {
                    logger.info("[sendEmmDeviceSwitchPolicyMessage] Send emm device switch  policy size: {},message: {}", messages.size(), JsonMapper.toJsonString(messages));
                    messages.forEach(emmDeviceSwitchPolicyGateway::send);
                }
            }
        } catch (Exception e) {
            logger.error("Encounter error when batch send emm device switch policy message", e);
        }
    }

    /**
     * Sync emm policy task
     */
    @Scheduled(cron = "${schedule.cron.sync-emm-policy-task:* * * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "syncEmmPolicyTask", protectTime = 500)
    public void syncEmmPolicyTask() {
        try {
            long policyIdSetSize = cacheService.sizeofSet(CacheNames.EMM_SYNC_POLICY_UPDATE);
            if (policyIdSetSize > 0) {
                List<Object> policyIdList = cacheService.popSet(CacheNames.EMM_SYNC_POLICY_UPDATE, 1L);
                if (CollectionUtils.isNotEmpty(policyIdList)) {
                    for (Object policyId : policyIdList) {
                        String[] strings = StringUtils.split(String.valueOf(policyId), "_");
                        if (strings.length == 3 && StringUtils.isNotBlank(strings[0]) && StringUtils.isNotBlank(strings[1]) && StringUtils.isNotBlank(strings[2])) {
                            Long marketId = LongUtils.parse(strings[0]);
                            String policyType = strings[1];
                            Long referenceId = LongUtils.parse(strings[2]);
                            logger.info("[syncEmmPolicyTask] Sync emm policy task marketId:{}, policyType:{}, referenceId:{}", marketId, policyType, referenceId);
                            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
                            emmSyncPolicyFunc.syncEmmPolicy(marketId, referenceId, policyType);
                            PaxDynamicDsThreadLocal.removePreferenceMarketId();
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Encounter error when sync emm policy task", e);
        }
    }

    /**
     * Sync emm data task
     */
    @Scheduled(cron = "${schedule.cron.sync-emm-data-task:0 0 1 * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "syncEmmDataTask")
    public void syncEmmDataTask() {
        List<Long> marketIds = enterpriseService.findEnterpriseMarketIds();
        for (Long marketId : marketIds) {
            Market market = marketService.get(marketId);
            if (Objects.nonNull(market)) {
                PaxDynamicDsThreadLocal.setPreferenceMarketId(market.getId());
                emmSyncDataFunc.syncEmmData(market.getId());
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
    }

    /**
     * Clear emm data task
     */
    @Scheduled(cron = "${schedule.cron.clear-emm-data-task:0 0 5 * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "clearEmmDataTask")
    public void clearEmmDataTask() {
        List<Long> marketIds = enterpriseService.findEnterpriseMarketIds();
        for (Long marketId : marketIds) {
            Market market = marketService.get(marketId);
            if (Objects.nonNull(market)) {
                PaxDynamicDsThreadLocal.setPreferenceMarketId(market.getId());
                emmClearDataFunc.clearEmmData(market.getId());
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
    }

    /**
     * Check no auth emm device task
     */
    @Scheduled(cron = "${schedule.cron.check-no-auth-emm-device-task:0 0/5 * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "checkNoAuthEmmDeviceTask")
    public void checkNoAuthEmmDeviceTask() {
        try {
            long deviceSnSetSize = cacheService.sizeofSet(CacheNames.EMM_DEVICE_SNS_OF_CERT_POLICY_APPLY_FAIL);
            if (deviceSnSetSize > 0) {
                List<ScheduleEmmNoAuthDeviceSwitchPolicyMessage> messages = Lists.newArrayList();
                List<Object> objectList = cacheService.popSet(CacheNames.EMM_DEVICE_SNS_OF_CERT_POLICY_APPLY_FAIL, SystemPropertyHelper.getEmmNoAuthDeviceMessageSendNumberPerBatch());
                if (CollectionUtils.isNotEmpty(objectList)) {
                    for (Object object : objectList) {
                        String[] strings = StringUtils.split(String.valueOf(object), "_");
                        if (strings.length == 2 && StringUtils.isNotBlank(strings[0]) && StringUtils.isNotBlank(strings[1])) {
                            ScheduleEmmNoAuthDeviceSwitchPolicyMessage message = new ScheduleEmmNoAuthDeviceSwitchPolicyMessage();
                            message.setMarketId(LongUtils.parse(strings[0]));
                            message.setDeviceSerialNo(strings[1]);
                            messages.add(message);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(messages)) {
                    logger.info("[checkNoAuthEmmDeviceTask] Check no auth emm device size: {},message: {}", messages.size(), JsonMapper.toJsonString(messages));
                    messages.forEach(emmNoAuthDeviceSwitchPolicyGateway::send);
                }
            }
        } catch (Exception e) {
            logger.error("Encounter error when check no auth emm device task", e);
        }
    }

    @Scheduled(cron = "${schedule.cron.check-emm-zte-async-operation:0 0/5 * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "checkEmmZteAsyncOperationTask")
    public void checkEmmZteAsyncOperationTask() {
        try {
            long zteOperationIdSetSize = cacheService.sizeofSet(CacheNames.EMM_ZTE_APPROVAL_IDS);
            if(zteOperationIdSetSize > 0) {
                List<EmmZteAsyncOperationMessage> messages = Lists.newArrayList();
                List<Object> zteRecordIdList = cacheService.popSet(CacheNames.EMM_ZTE_APPROVAL_IDS, SystemPropertyHelper.getEmmZteOperationMessageSendNumberPerBatch());
                if(CollectionUtils.isNotEmpty(zteRecordIdList)) {
                    zteRecordIdList.forEach(zteRecordId -> {
                        String[] strings = StringUtils.split(String.valueOf(zteRecordId), "_");
                        if (strings.length == 2 && StringUtils.isNotBlank(strings[0]) && StringUtils.isNotBlank(strings[1])) {
                            EmmZteAsyncOperationMessage message = new EmmZteAsyncOperationMessage();
                            message.setMarketId(LongUtils.parse(strings[0]));
                            message.setZteRecordId(LongUtils.parse(strings[1]));
                            messages.add(message);
                        }
                    });
                }
                if(CollectionUtils.isNotEmpty(messages)) {
                    logger.info("[checkEmmZteAsyncOperationTask] Check emm zte async operation size: {},message: {}", messages.size(), JsonMapper.toJsonString(messages));
                    messages.forEach(emmZteAsyncOperationGateway::send);
                }
            }
        } catch (Exception e) {
            logger.error("Encounter error when check emm zte async operation task", e);
        }
    }

    /**
     * <p>每天凌晨0点执行</p>
     * <p>找出符合条件下市场的设备列表，然后发送kafka给DeviceChange handler处理/p>
     */
    @Scheduled(cron = "${schedule.cron.emm-unsubscribe-markets-stats:0 0 0 * * ?}")
    @RedisLock(prefix = "scheduleTask:",key = "emmUnsubscribeJob")
    public void emmUnsubscribeJob() {
        emmUnsubscribeFunc.handleUnsubscribeMarketsDevices();
    }


    /**
     * 每天凌晨0：30执行 清理状态为拒绝或撤销的并且过期一个月的zte申请记录设备信息,写入文件
     *
     */
    @Scheduled(cron = "${schedule.cron.emm-clean-expired-zte-record:0 30 0 * * ?}")
    @RedisLock(prefix = "scheduleTask:",key = "selectExpiredZTERecordAndExport")
    public void selectExpiredZTERecordAndExport() {
        zteRecordService.findExpiredList()
                        .forEach(zteRecord -> {
                            ScheduleEmmZteDeviceRecordChangedMessage scheduleEmmZteDeviceRecordChangedMessage = new ScheduleEmmZteDeviceRecordChangedMessage();
                            scheduleEmmZteDeviceRecordChangedMessage.setMarketId(zteRecord.getMarketId());
                            scheduleEmmZteDeviceRecordChangedMessage.setZteRecordId(zteRecord.getId());
                            scheduleEmmZteDeviceRecordChangedMessage.setZteRecordStatus(zteRecord.getStatus());
                            emmZteDeviceRecordChangedGateway.send(scheduleEmmZteDeviceRecordChangedMessage);
                        });
    }

    /**
     * 每天凌晨4点自动删除超过24小时未认证的设备
     */
    @Scheduled(cron = "${schedule.cron.delete-no-auth-emm-device-task:0 0 4 * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "deleteNoAuthEmmDeviceTask")
    public void deleteNoAuthEmmDeviceTask() {
        List<Long> marketIds = enterpriseService.findEnterpriseMarketIds();
        for (Long marketId : marketIds) {
            Market market = marketService.get(marketId);
            if (Objects.nonNull(market)) {
                PaxDynamicDsThreadLocal.setPreferenceMarketId(market.getId());
                emmDeviceFunc.deleteNoAuthEmmDevice(marketId);
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
    }

    /**
     * Refresh global EMM device count
     * 每天3点45刷新
     */
    @Scheduled(cron = "${schedule.cron.calc-global-emm-device-count:0 45 3 * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "calcGlobalEmmDeviceCount")
    public void calcGlobalEmmDeviceCount() {
        emmDeviceRegistryFunc.refreshGlobalEmmDeviceTotalCount();
    }

}
