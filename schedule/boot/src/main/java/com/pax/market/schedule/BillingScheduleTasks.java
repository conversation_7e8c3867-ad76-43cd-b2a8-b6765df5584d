package com.pax.market.schedule;

import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.functional.billing.BillingFunc;
import com.pax.market.framework.common.annotation.RedisLock;
import com.paxstore.schedule.global.domain.service.setting.ScheduleSystemPropertyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configurable
@EnableScheduling
@RequiredArgsConstructor
@Slf4j
public class BillingScheduleTasks {

    private final BillingFunc billingFunc;
    private final ScheduleSystemPropertyService systemPropertyService;

    @Scheduled(cron = "${schedule.cron.migration-billing-data:0 0/1 * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "migrationBillingSetting")
    public void migrationBillingSetting() {
        if (SystemPropertyHelper.getMigrationBillingDataEnabled()) {
            systemPropertyService.delete(SystemPropertyHelper.MIGRATION_BILLING_DATA_ENABLE);
            billingFunc.migrationBillingData();
        }
    }

    @Scheduled(cron = "${schedule.cron.migration-billing-data:0 0/1 * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "migrationTerminalEnrollDetails")
    public void migrationTerminalEnrollDetails() {
        if (SystemPropertyHelper.getMigrationTerminalEnrollDetailsEnabled()) {
            systemPropertyService.delete(SystemPropertyHelper.MIGRATION_TERMINAL_ENROLL_DETAILS_ENABLE);
            billingFunc.migrationTerminalEnrollDetails();
        }
    }

    @Scheduled(cron = "${schedule.cron.migration-billing-data:0 0/1 * * * ?}")
    @RedisLock(prefix = "scheduleTask:", key = "migrationAirViewerUsageDetails")
    public void migrationAirViewerUsageDetails() {
        if (SystemPropertyHelper.getMigrationAirViewerUsageDetailsEnabled()) {
            systemPropertyService.delete(SystemPropertyHelper.MIGRATION_AIRVIEWER_USAGE_DETAILS_ENABLE);
            billingFunc.migrationAirViewerUsageDetail();
        }
    }
}
