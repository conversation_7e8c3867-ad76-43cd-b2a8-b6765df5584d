#
# ********************************************************************************
# COPYRIGHT
#               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
#   This software is supplied under the terms of a license agreement or
#   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
#   or disclosed except in accordance with the terms in that agreement.
#
#      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
# ********************************************************************************
#
# server settings
spring.jackson.serialization.INDENT_OUTPUT=true
spring.datasource.sqlScriptEncoding=UTF-8
auth-port=8082
web-api-port=8081
terminal-api-port=8090
thirdparty-api-port=8085
vas-api-port=8084
migration-api-port=8083
internal-api-port=8087
# disable spring boot strange behavior
spring.main.show-banner=false
spring.main.allow-bean-definition-overriding=true
# /info endpoint
info.app.name=P-MArket-API-DEV
#Profile defination
spring.profiles.active=test
#for spring mvc, use this one.
multipart.maxFileSize=104857600
apk.icon.size.maximum=307200
ehcache.configFile=cache/ehcache-local.xml
#=============================#
#===== Database settings =====#
#=============================#
jdbc.type=h2
jdbc.driver=org.h2.Driver
jdbc.url=jdbc:h2:mem:p-market;MODE=MYSQL
#jdbc.url=jdbc:h2:file:~/.h2/p-market;MODE=MYSQL;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
jdbc.username=sa
jdbc.password=

#pool settings
jdbc-driver=${jdbc.driver}
mysql-hosts=${jdbc.url}
mysql-user=${jdbc.username}
mysql-password=${jdbc.password}
#-----------------------------
jdbc-driver-2=${jdbc.driver}
mysql-hosts-2=jdbc:h2:mem:p-market-2;MODE=MYSQL
mysql-user-2=${jdbc.username}
mysql-password-2=${jdbc.password}
#----------------------------
jdbc-driver-3=${jdbc.driver}
mysql-hosts-3=jdbc:h2:mem:p-market-2;MODE=MYSQL
mysql-user-3=${jdbc.username}
mysql-password-3=${jdbc.password}
#---------------------------
######### Redis variables
redis-sentinel-enabled=false
redis-cluster-mode=single
redis-host=redis
redis-port=6379
redis-host-port="redis:6379"
redis-sentinel-nodes=
redis-password=
######### Kafka variables
kafka-broker-hosts=kafka:9092
######### Payment variables
stripe-secret-key=
stripe-publishable-key=
stripe-connect-clientId=
paxpay-app-id=
paxpay-sign-key=
paxpay-sign-secret=
######### Zookeeper variables
zk-hosts=zookeeper:2181
######### Push variables
security-public-key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCghPCWCobG8nTD24juwSVataW7iViRxcTkey/B792VZEhuHjQvA3cAJgx2Lv8GnX8NIoShZtoCg3Cx6ecs+VEPD2fBcg2L4JK7xldGpOJ3ONEAyVsLOttXZtNXvyDZRijiErQALMTorcgi79M5uVX9/jMv2Ggb2XAeZhlLD28fHwIDAQAB
mpush-redis-host-port=redis:6379
mpush-redis-cluster-mode=single
mpush-redis-password=
######### Global setting. globalProvinceFlag: Whether allow save province/city when create reseller/merchant.PMAR-5586
global-province-flag=false
######### Pre-defined roles and merchant types
pre-defined-reseller-roles=6,7,8,9,10
pre-defined-merchant-types=Retail,Restaurant,Lodging,Business to Business,Utility,MOTO,eCommerce,Public Sector,Petroleum,Fast Food,Convenience,QSR,Government
#Terminal Group
group-max-terminal-count=5000
#Terminal Map
map-max-terminal-count=400000
god-map-max-terminal-count=1000000
#Whether to enable captcha aspect
captcha-aspect-enabled=true
#Whether to export installed app info in monthly report
export-installed-app-in-monthly-report=false
######### posviewer alloc url
posviewer_alloc_url=https://vas.paxsit.com/posviewer
######### Thirdparty sys api request rate limit config
open-api-authed-limit-policy-enabled=true
dev-api-authed-limit-policy-enabled=true
#Whether to enable download logcat
logcat-download-enabled=true
#VAS Common configuration
vas-envCode=dev
#Vas auth support config
auth-vas-vasplatform-jwt-signing-key=uecDLIziVfPCXpTTGWseSolnUhatolYKrTWMllQNSagsiNhs
#PaxLanding config
pax-landing-accept-terminal-commands=5,8,15,18,22
######### Log variables
api-log-enabled=true
sql-log-enabled=true
###\u4E0B\u9762\u7684key\u8BF7\u52FF\u968F\u610F\u6539\u52A8
distribution.http-client.ssl-enable=false
distribution.global.auth-api-host-url=http://www.paxdev.com:8082
distribution.global.web-api-host-url=http://www.paxdev.com:8081
distribution.global.smartlanding-api-host-url=http://www.paxdev.com:8099/p-market-paxlanding
pmarket-comm-cfg.local-deploy=true
pmarket-comm-cfg.api-url=http://api.paxdev.com
pmarket-comm-cfg.api-main-host=http://api.${pmarket-comm-cfg.web-main-host}
pmarket-comm-cfg.admin-activate-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/activate
pmarket-comm-cfg.user-activate-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/activate
pmarket-comm-cfg.reseller-activate-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/activate
pmarket-comm-cfg.merchant-activate-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/activate
pmarket-comm-cfg.user-login-url=http://www.${pmarket-comm-cfg.web-main-host}:3003
pmarket-comm-cfg.developer-redirect-url=http://%s.${pmarket-comm-cfg.web-main-host}:3001/
pmarket-comm-cfg.developer-redirect-url-no-domain=${pmarket-comm-cfg.web-main-host}:3001/
pmarket-comm-cfg.reset-password-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/resetPwd
pmarket-comm-cfg.disable-user-otp-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/disableTowFactor2
pmarket-comm-cfg.admin-redirect-url=http://%s.${pmarket-comm-cfg.web-main-host}:3000/
pmarket-comm-cfg.admin-redirect-url-no-domain=${pmarket-comm-cfg.web-main-host}:3000/
pmarket-comm-cfg.portal-redirect-url=http://%s.${pmarket-comm-cfg.web-main-host}:3003/
pmarket-comm-cfg.portal-redirect-url-no-domain=${pmarket-comm-cfg.web-main-host}:3003/
pmarket-comm-cfg.reset-email-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/resetEmail
pmarket-comm-cfg.account-redirect-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/
pmarket-comm-cfg.sign-up-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/register
pmarket-comm-cfg.forget-password-redirect-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/forgetPwd
pmarket-comm-cfg.otp-backupcode-login-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/disableTowFactor
pmarket-comm-cfg.otp-mail-disable-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/disableTowFactorMail
pmarket-comm-cfg.merchant-redirect-url=http://%s.${pmarket-comm-cfg.web-main-host}:3005/
pmarket-comm-cfg.merchant-redirect-url-no-domain=${pmarket-comm-cfg.web-main-host}:3005/
pmarket-comm-cfg.auth-host-url=http://www.${pmarket-comm-cfg.web-main-host}:8082/${pmarket-comm-cfg.auth-context-path}
pmarket-comm-cfg.download-report-url=http://www.${pmarket-comm-cfg.web-main-host}:3004/#/extraction
tomcat.apr.enabled=false
vas-api.http-client.readtimeout=5000
logging.level.root=DEBUG