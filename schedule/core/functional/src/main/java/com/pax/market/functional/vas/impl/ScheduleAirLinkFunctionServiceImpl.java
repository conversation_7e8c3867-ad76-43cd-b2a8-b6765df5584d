package com.pax.market.functional.vas.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pax.core.json.JsonMapper;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.constants.RoleID;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.airlink.*;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.user.User;
import com.pax.market.domain.entity.global.vas.airlink.MarketAirLinkSetting;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminal;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalProfile;
import com.pax.market.domain.entity.market.vas.airlink.AirLinkTerminalTask;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.alarm.AlarmTerminalExceedUsageDto;
import com.pax.market.dto.billing.ServiceUsageDetailInfo;
import com.pax.market.dto.vas.AirLinkDataPoolSettingInfo;
import com.pax.market.dto.vas.MarketAirLinkSettingInfo;
import com.pax.market.dto.vas.MarketAirLinkSubscriptionPlanInfo;
import com.pax.market.framework.common.annotation.FunctionalService;
import com.pax.market.framework.common.utils.context.RequestLocaleHolder;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.vas.ScheduleAirLinkFunctionService;
import com.pax.market.mq.contract.airlink.AirLinkTerminalActiveHistoryDeductMessage;
import com.pax.market.mq.contract.airlink.AirLinkTerminalMonthDeductMessage;
import com.pax.market.mq.contract.airlink.AirLinkTerminalSuspendAndActivateMessage;
import com.pax.market.mq.contract.billing.SyncAirLinkUsageDetailMessage;
import com.pax.market.mq.producer.gateway.airlink.AirLinkTerminalDeductGateway;
import com.pax.market.mq.producer.gateway.airlink.AirLinkTerminalSuspendAndActivateGateway;
import com.pax.market.mq.producer.gateway.billing.SyncData2BillingGateway;
import com.pax.market.notification.EmailNotificationService;
import com.pax.market.notification.MiscNotificationService;
import com.pax.market.service.center.ServiceCenterFunc;
import com.pax.support.dynamic.datasource.threadlocal.PaxDynamicDsThreadLocal;
import com.pax.vas.common.VasConstants;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketService;
import com.paxstore.schedule.global.domain.service.role.ScheduleRoleService;
import com.paxstore.schedule.global.domain.service.vas.ScheduleMarket2ServiceService;
import com.paxstore.schedule.global.domain.service.vas.ScheduleServiceResellerService;
import com.paxstore.schedule.global.domain.service.vas.airlink.ScheduleAirLinkCacheService;
import com.paxstore.schedule.global.domain.service.vas.airlink.ScheduleAirLinkWarningService;
import com.paxstore.schedule.global.domain.service.vas.airlink.ScheduleMarketAirLinkSettingService;
import com.paxstore.schedule.market.domain.service.airlink.ScheduleAirLinkTerminalProfileService;
import com.paxstore.schedule.market.domain.service.airlink.ScheduleAirLinkTerminalService;
import com.paxstore.schedule.market.domain.service.airlink.ScheduleAirLinkTerminalTaskService;
import com.paxstore.schedule.market.domain.service.organization.ScheduleResellerService;
import com.zolon.saas.api.common.response.SingleResponse;
import com.zolon.saas.vas.func.platform.VasPlatformFunc;
import com.zolon.saas.vas.func.platform.request.CardsRequest;
import com.zolon.saas.vas.func.platform.request.ImportCardsRequest;
import com.zolon.saas.vas.func.platform.request.QueryCardUsageRequest;
import com.zolon.saas.vas.func.platform.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * author mengxiaoxian
 * Date   2025/1/17 13:43
 */
@Slf4j
@FunctionalService
@RequiredArgsConstructor
public class ScheduleAirLinkFunctionServiceImpl implements ScheduleAirLinkFunctionService {

    private final ScheduleMarketAirLinkSettingService scheduleMarketAirLinkSettingService;
    private final ScheduleRoleService scheduleRoleService;
    private final MiscNotificationService miscNotificationService;
    private final EmailNotificationService emailNotificationService;
    private final ScheduleMarketService scheduleMarketService;
    private final ScheduleMarket2ServiceService scheduleMarket2ServiceService;
    private final BillingUsageService billingUsageService;
    private final AirLinkTerminalDeductGateway airLinkTerminalDeductGateway;
    private final ScheduleAirLinkWarningService airLinkWarningService;
    private final ScheduleAirLinkTerminalService scheduleAirLinkTerminalService;
    private final ScheduleAirLinkTerminalProfileService scheduleAirLinkTerminalProfileService;
    private final VasPlatformFunc vasPlatformFunc;
    private final SyncData2BillingGateway syncData2BillingGateway;
    private final ScheduleResellerService scheduleResellerService;
    private final ScheduleAirLinkTerminalTaskService airLinkTerminalTaskService;
    private final ScheduleServiceResellerService serviceResellerService;
    private final ServiceCenterFunc serviceCenterFunc;
    private final AirLinkTerminalSuspendAndActivateGateway airLinkTerminalSuspendAndActivateGateway;
    private final ScheduleAirLinkCacheService scheduleAirLinkCacheService;

    @Override
    public void deductMarketAirLinkMonthFee() {
        Date deductTime = new Date();
        List<Long> marketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : marketIds) {
            AirLinkTerminalMonthDeductMessage message = new AirLinkTerminalMonthDeductMessage();
            message.setMarketId(marketId);
            message.setDeductTime(deductTime);
            airLinkTerminalDeductGateway.send(message);
        }
    }

private BigDecimal calculateExcessCharges(ServiceUsageDetailInfo airLinkTotalUsage, MarketAirLinkSubscriptionPlanInfo subscriptionPlan){
        if (Objects.nonNull(airLinkTotalUsage.getDataUsage())){
            long useTraffic = airLinkTotalUsage.getTerminalCount() * subscriptionPlan.getPackageLimit() * 1024;
            BigDecimal overageTraffic = airLinkTotalUsage.getDataUsage().subtract(BigDecimal.valueOf(useTraffic)).divide(BigDecimal.valueOf(1024*1024L), 0, RoundingMode.CEILING);
            if (overageTraffic.compareTo(BigDecimal.ZERO) > 0) {
                return overageTraffic.multiply(subscriptionPlan.getOveragePrice()).setScale(2, RoundingMode.HALF_UP);
            }
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal calculateMonthlyCharges(Long inUseTotalTerminalCount, MarketAirLinkSetting marketAirLinkSetting) {
        return marketAirLinkSetting.getPackageFee().multiply(new BigDecimal(inUseTotalTerminalCount));
    }

    private static boolean isInRange(BigDecimal number, BigDecimal min, BigDecimal max) {
        return number.compareTo(min) >= 0 && number.compareTo(max) < 0;
    }


    private void handleAirLinkEarlyWarningOfBalance(Long marketId) {
        MarketAirLinkSetting marketAirLinkSetting = scheduleMarketAirLinkSettingService.getByMarketId(marketId);
        if (marketAirLinkSetting == null || marketAirLinkSetting.getChargeDate() == null) {
            return;
        }
        if (Objects.isNull(marketAirLinkSetting.getBalance())) {
            marketAirLinkSetting.setBalance(BigDecimal.ZERO);
        }
        Market market = scheduleMarketService.get(marketAirLinkSetting.getMarketId());
        ServiceUsageDetailInfo airLinkTotalUsage = billingUsageService.getAirLinkTotalUsage(market.getId(), null, DateUtils.formatDate(new Date(), DateUtils.PERIOD_DATE_FORMAT));
        if (airLinkTotalUsage == null) {
            return;
        }
        MarketAirLinkSubscriptionPlanInfo subscriptionPlan = serviceCenterFunc.getSubscriptionPlanByPeriod(marketId, DateUtils.formatDate(new Date(), DateUtils.PERIOD_DATE_FORMAT));
        if (subscriptionPlan == null || subscriptionPlan.getPackageLimit() == null || subscriptionPlan.getOveragePrice() == null) {
            return;
        }
        BigDecimal excessCharges = calculateExcessCharges(airLinkTotalUsage, subscriptionPlan);
        Long inUseTotalTerminalCount = scheduleAirLinkTerminalService.getInUseTotalTerminalCount(marketId);
        BigDecimal oneTimesMonthlyCharges = calculateMonthlyCharges(inUseTotalTerminalCount, marketAirLinkSetting);
        BigDecimal onePointFiveTimesMonthlyCharges = oneTimesMonthlyCharges.multiply(new BigDecimal("1.5")).add(excessCharges);
        BigDecimal twoTimesMonthlyCharges = oneTimesMonthlyCharges.multiply(new BigDecimal("2.0")).add(excessCharges);
        boolean enableEmailSending = false;
        if (Objects.nonNull(marketAirLinkSetting.getTrialBalance()) && marketAirLinkSetting.getTrialBalance().compareTo(BigDecimal.ZERO) > 0) {
            marketAirLinkSetting.setBalance(marketAirLinkSetting.getBalance().add(marketAirLinkSetting.getTrialBalance()));
        }
        if (isInRange(marketAirLinkSetting.getBalance(), new BigDecimal(Integer.MIN_VALUE), oneTimesMonthlyCharges.add(excessCharges))) {
            enableEmailSending = true;
        } else if (isInRange(marketAirLinkSetting.getBalance(), oneTimesMonthlyCharges.add(excessCharges), onePointFiveTimesMonthlyCharges) && !airLinkWarningService.hasMonthlyChargesWarningOfBalance(market.getId(), AirLinkWarningType.ONE_POINT_FIVE_TIME)) {
            enableEmailSending = true;
            airLinkWarningService.saveWarningOfBalance(market.getId(), AirLinkWarningType.ONE_POINT_FIVE_TIME);
        } else if (isInRange(marketAirLinkSetting.getBalance(), onePointFiveTimesMonthlyCharges, twoTimesMonthlyCharges) && !airLinkWarningService.hasMonthlyChargesWarningOfBalance(market.getId(), AirLinkWarningType.TOW_TIME)) {
            enableEmailSending = true;
            airLinkWarningService.saveWarningOfBalance(market.getId(), AirLinkWarningType.TOW_TIME);
        }
       if (enableEmailSending) {
            //send notification to market admin and global admin
            BigDecimal currentBalance =  marketAirLinkSetting.getBalance().setScale(2, RoundingMode.HALF_UP);
            BigDecimal preDeductAmount = oneTimesMonthlyCharges.add(excessCharges).setScale(2, RoundingMode.HALF_UP);
            BigDecimal suggestedRechargeAmount = oneTimesMonthlyCharges.multiply(new BigDecimal("3.0")).add(excessCharges).subtract(marketAirLinkSetting.getBalance()).setScale(2, RoundingMode.HALF_UP);
            sendEarlyWarningOfBalanceNotification(market,currentBalance, preDeductAmount, suggestedRechargeAmount);
            //send email to support team
            sendEarlyWarningOfBalanceEmail(market, currentBalance, preDeductAmount, suggestedRechargeAmount);
        }
    }

    @Override
    public void airLinkEarlyWarningOfBalance() {
        List<Long> marketIds = scheduleMarket2ServiceService.findSubscribedMarketIds(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : marketIds) {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            handleAirLinkEarlyWarningOfBalance(marketId);
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    @Override
    public void deductTerminalCurrentMonthActiveFee(boolean needDeduct, boolean deductLastMonth) {
        Date date = new Date();
        if (deductLastMonth) {
            date = DateUtils.endOfMonth(DateUtils.lastMonth(date));
        }
        List<Long> marketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : marketIds) {
            AirLinkTerminalActiveHistoryDeductMessage message = new AirLinkTerminalActiveHistoryDeductMessage();
            message.setMarketId(marketId);
            message.setNeedDeduct(needDeduct);
            message.setCloseActiveHistory(false);
            message.setDeductTime(date);
            message.setTerminalOperateLevel(AirLinkTerminalOperateLevel.MARKET);
            //如果激活任务过期,激活失败原因
            message.setTerminalActiveFailReason(AirLinkTerminalActiveFailReason.EXPIRED);
            message.setDeductLastMonth(deductLastMonth);
            airLinkTerminalDeductGateway.send(message);
        }
    }

    @Override
    public void checkAirLinkTerminalSwitchProfile() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            try {
                List<AirLinkTerminalProfile> switchStatusProfileList = scheduleAirLinkTerminalProfileService.getSwitchStatusProfileList();
                if (CollectionUtils.isNotEmpty(switchStatusProfileList)) {
                    for (AirLinkTerminalProfile airLinkTerminalProfile : switchStatusProfileList) {
                        AirLinkTerminal airLinkTerminal = scheduleAirLinkTerminalService.get(airLinkTerminalProfile.getAirlinkTerminalId());
                        QueryCardDetailResponse queryCardDetailResponse = vasPlatformFunc.getCardDetail(airLinkTerminal.getImei()).getData();
                        if (Objects.nonNull(queryCardDetailResponse)) {
                            //切换的码号跟查询的当前码号相同 代表切换成功
                            if (queryCardDetailResponse.getCurrentProfile().getIccid().equals(airLinkTerminalProfile.getIccid())) {
                                airLinkTerminalProfile.setStatus(AirLinkTerminalProfileStatus.USE);
                                scheduleAirLinkTerminalProfileService.update(airLinkTerminalProfile);
                            }
                        }
                    }
                }
            }finally {
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }

    }

    @Override
    public void syncAirLinkTerminalUsage() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        if (DateUtils.beginOfMonth(new Date()).equals(DateUtils.beginOfDate(new Date()))) {
            scheduleAirLinkCacheService.clear();
        }
        List<AirLinkTerminal> overUsageTerminalList = new ArrayList<>();
        for (Long marketId : subscribedMarketIds) {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            Long lastId = null;
            try {
                while (true) {
                    List<AirLinkTerminal> airLinkTerminalList = scheduleAirLinkTerminalService.getAirLinkTerminalList(marketId, lastId, AirLinkConstants.MAX_COUNT);
                    if (CollectionUtils.isEmpty(airLinkTerminalList)) {
                        break;
                    }
                    Map<String, List<QueryCardUsageResponse.UsageDetail>> stringListMap = batchGetUsage(airLinkTerminalList);
                    for (AirLinkTerminal airLinkTerminal : airLinkTerminalList) {
                        List<QueryCardUsageResponse.UsageDetail> usageDetailList = stringListMap.get(airLinkTerminal.getEid());
                        BigDecimal currentUsage = BigDecimal.ZERO;
                        if (CollectionUtils.isNotEmpty(usageDetailList)) {
                            QueryCardUsageResponse.UsageDetail currentMonthUsage = usageDetailList.get(0);
                            if (Objects.nonNull(currentMonthUsage) && !"-1.0".equals(currentMonthUsage.getDataUsage())) {
                                currentUsage = new BigDecimal(currentMonthUsage.getDataUsage());
                            }
                        }
                        BigDecimal dataUsage = getAirLinkTerminalDataUsage(airLinkTerminal,DateUtils.formatDate(new Date(), "yyyy-MM"));
                        airLinkTerminal.setMonthUsage(currentUsage.subtract(dataUsage));
                        sendAirLinkTerminalUsage(airLinkTerminal);
                        if (Objects.nonNull(airLinkTerminal.getMonthUsage()) && airLinkTerminal.getMonthUsage().compareTo(new BigDecimal(SystemPropertyHelper.getAirLinkUsageExceedsWarningNumber() * 1024 * 1024 )) > 0){
                            String uniImei = airLinkTerminal.getId() + airLinkTerminal.getImei();
                            if (!scheduleAirLinkCacheService.isImeiInSet(uniImei)){
                                overUsageTerminalList.add(airLinkTerminal);
                            }
                        }
                    }
                    scheduleAirLinkTerminalService.batchUpdate(airLinkTerminalList);
                    lastId = airLinkTerminalList.get(airLinkTerminalList.size() - 1).getId();
                }
            } finally {
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
        if (CollectionUtils.isNotEmpty(overUsageTerminalList)) {
            sendEarlyWarningOfUsageExceedEmail(overUsageTerminalList);
            scheduleAirLinkCacheService.addImei2Set(overUsageTerminalList.stream().map(airLinkTerminal -> airLinkTerminal.getId() + airLinkTerminal.getImei()).toList());
        }
    }

    @Override
    public void syncAirLinkTerminalLastMonthUsage() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            Long lastId = null;
            try {
                while (true) {
                    List<AirLinkTerminal> airLinkTerminalList = scheduleAirLinkTerminalService.getAirLinkTerminalList(marketId, lastId, AirLinkConstants.MAX_COUNT);
                    if (CollectionUtils.isEmpty(airLinkTerminalList)) {
                        break;
                    }
                    for (AirLinkTerminal airLinkTerminal : airLinkTerminalList) {
                        BigDecimal lastMonthUsage = billingUsageService.getAirLinkTerminalTotalUsage(
                                        airLinkTerminal.getId(),
                                        DateUtils.formatDate(DateUtils.lastMonth(new Date()), "yyyy-MM")
                                ).getDataUsage();
                        airLinkTerminal.setTotalUsage(airLinkTerminal.getTotalUsage() == null
                                ? BigDecimal.ZERO.add(lastMonthUsage)
                                :
                                airLinkTerminal.getTotalUsage().add(lastMonthUsage));
                        airLinkTerminal.setLastMonthUsage(lastMonthUsage);
                    }
                    scheduleAirLinkTerminalService.batchUpdate(airLinkTerminalList);
                    OptionalLong optionalLong = airLinkTerminalList.stream().mapToLong(AirLinkTerminal::getId).max();
                    if (optionalLong.isPresent()) {
                        lastId = optionalLong.getAsLong();
                    }
                }
            } finally {
                PaxDynamicDsThreadLocal.removePreferenceMarketId();
            }
        }
    }

    private BigDecimal getAirLinkTerminalDataUsage(AirLinkTerminal airLinkTerminal,String period) {
        MarketAirLinkSettingInfo marketAirLinkSetting = serviceCenterFunc.getMarketAirLinkSetting(airLinkTerminal.getMarketId());
        if (Objects.isNull(marketAirLinkSetting)) {
            return BigDecimal.ZERO;
        }
        List<Long> marketIdsByPoolId = scheduleMarketAirLinkSettingService.findMarketIdsByPoolId(marketAirLinkSetting.getDataPoolId());
        String marketIds = String.join(",", Optional.of(marketIdsByPoolId)
                .orElseGet(org.apache.commons.compress.utils.Lists::newArrayList)
                .stream()
                .map(String::valueOf)
                .toList());
        ServiceUsageDetailInfo terminalTotalUsage = billingUsageService.getAirLinkTerminalOtherImportUsage(airLinkTerminal.getImei(),
                airLinkTerminal.getId(),
                marketIds,
                period);
        return terminalTotalUsage.getDataUsage();
    }

    private void sendAirLinkTerminalUsage(AirLinkTerminal airLinkTerminal) {
        Reseller reseller = scheduleResellerService.get(airLinkTerminal.getResellerId());
        SyncAirLinkUsageDetailMessage message = SyncAirLinkUsageDetailMessage.builder()
                .airlinkTerminalId(airLinkTerminal.getId())
                .dataUsage(airLinkTerminal.getMonthUsage())
                .esimActivateTime(airLinkTerminal.getActiveTime())
                .resellerId(airLinkTerminal.getResellerId())
                .resellerName(Objects.nonNull(reseller) ? reseller.getName() : null)
                .syncTime(new Date())
                .resellerParentIds(Objects.nonNull(reseller) ? reseller.getParentIds() : null)
                .serialNo(airLinkTerminal.getSerialNo())
                .imei(airLinkTerminal.getImei())
                .marketId(airLinkTerminal.getMarketId())
                .organization(scheduleResellerService.getResellerOrganization(reseller))
                .build();
        syncData2BillingGateway.sendAirLinkTerminalUsage(JsonMapper.toJsonString(message));
    }


    @Override
    public void reactiveAirLinkTerminal() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIds(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            if (!scheduleMarket2ServiceService.isActiveAndSubscribed(marketId, VasConstants.ServiceType.AIR_LINK)) {
                airLinkTerminalTaskService.deleteActiveTaskByMarketId(marketId);
                continue;
            }
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            List<Long> resellerIdList = airLinkTerminalTaskService.findActiveTaskResellerIdsByMarket(marketId);
            if (CollectionUtils.isNotEmpty(resellerIdList)) {
                List<Long> deleteByResellerIds = new ArrayList<>();
                MarketAirLinkSetting marketAirLinkSetting = scheduleMarketAirLinkSettingService.getByMarketId(marketId);
                AirLinkDataPoolSettingInfo dataPoolSetting = serviceCenterFunc.getAirLinkDataPoolSetting(marketAirLinkSetting.getDataPoolId());
                for (Long resellerId : resellerIdList) {
                    if (!serviceResellerService.checkServiceResellerSpecific(VasConstants.ServiceType.AIR_LINK, marketId, resellerId)) {
                        deleteByResellerIds.add(resellerId);
                        continue;
                    }
                    retryTerminalTask(marketId, resellerId, dataPoolSetting.getGroupId(), AirLinkTerminalTaskType.ACTIVE);
                }
                if (CollectionUtils.isNotEmpty(deleteByResellerIds)){
                    airLinkTerminalTaskService.deleteActiveTaskByResellerIds(deleteByResellerIds);
                }
            }
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    @Override
    public void cancelActiveAirLinkTerminal() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            retryTerminalTask(marketId, null, null, AirLinkTerminalTaskType.CANCEL_ACTIVE);
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    @Override
    public void syncAirLinkTerminalProfileList() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            handleAirLinkTerminalProfile(marketId);
        }
    }

    private void handleAirLinkTerminalProfile(Long marketId) {
        PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
        try {
            List<AirLinkTerminal> airLinkTerminalList = scheduleAirLinkTerminalService.getAirLinkTerminalListWithoutProfile(marketId);
            if (CollectionUtils.isNotEmpty(airLinkTerminalList)) {
                for (AirLinkTerminal airLinkTerminal : airLinkTerminalList) {
                    QueryCardProfilesResponse queryCardProfilesResponse = vasPlatformFunc.getCardProfiles(airLinkTerminal.getImei()).getData();
                    if (Objects.nonNull(queryCardProfilesResponse)) {
                        AtomicBoolean hasUsedProfile = new AtomicBoolean(false);
                        List<AirLinkTerminalProfile> airLinkTerminalProfileList = Arrays.stream(queryCardProfilesResponse.getInstalledProfiles())
                                .map(installedProfile -> {
                                    AirLinkTerminalProfile airLinkTerminalProfile = new AirLinkTerminalProfile();
                                    airLinkTerminalProfile.setAirlinkTerminalId(airLinkTerminal.getId());
                                    airLinkTerminalProfile.setIccid(installedProfile.getIccid());
                                    airLinkTerminalProfile.setMsisdn(installedProfile.getMsisdn());
                                    airLinkTerminalProfile.setOperator(installedProfile.getOperator());
                                    airLinkTerminalProfile.setStatus(installedProfile.getStatus().equals(AirLinkConstants.CARD_ENABLED_STATUS) ? AirLinkTerminalProfileStatus.USE : AirLinkTerminalProfileStatus.NOT_USE);
                                    if (installedProfile.getStatus().equals(AirLinkConstants.CARD_ENABLED_STATUS)) {
                                        hasUsedProfile.set(true);
                                    }
                                    return airLinkTerminalProfile;
                                }).collect(Collectors.toList());
                        if (hasUsedProfile.get()) {
                            scheduleAirLinkTerminalProfileService.saveAirLinkTerminalProfile(airLinkTerminalProfileList, airLinkTerminal);
                            airLinkTerminal.setMonthUsage(BigDecimal.ZERO);
                            sendAirLinkTerminalUsage(airLinkTerminal);
                        }
                    }
                }
            }
        } finally {
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    @Override
    public void syncActiveTerminalSN() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            MarketAirLinkSetting marketAirLinkSetting = scheduleMarketAirLinkSettingService.getByMarketId(marketId);
            if (Objects.nonNull(marketAirLinkSetting)) {
                AirLinkDataPoolSettingInfo dataPoolSetting = serviceCenterFunc.getAirLinkDataPoolSetting(marketAirLinkSetting.getDataPoolId());
                syncTerminalSN(marketId, dataPoolSetting.getGroupId());
            }
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }

    }

    @Override
    public void syncTerminalSuspendAndActivateStatus() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIdsBefore(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds) {
            if (!scheduleMarket2ServiceService.isActiveAndSubscribed(marketId, VasConstants.ServiceType.AIR_LINK)) {
                continue;
            }
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            List<Long> resellerIds = serviceResellerService.findServiceResellerIds(marketId, VasConstants.ServiceType.AIR_LINK);
            for (Long resellerId : resellerIds) {
                syncTerminalSuspendAndActivateStatus(marketId, resellerId);
            }
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    @Override
    public void retryOverdueTerminal() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIds(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds){
            if (!scheduleMarket2ServiceService.isActiveAndSubscribed(marketId, VasConstants.ServiceType.AIR_LINK)){
                airLinkTerminalTaskService.deleteActiveTaskByMarketId(marketId);
                continue;
            }
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            retryOverdueTerminalTask(marketId);
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }

    @Override
    public void syncOverdueTerminalSuspendAndActivateStatus() {
        List<Long> subscribedMarketIds = scheduleMarket2ServiceService.findSubscribedMarketIds(VasConstants.ServiceType.AIR_LINK);
        for (Long marketId : subscribedMarketIds){
            if (!scheduleMarket2ServiceService.isActiveAndSubscribed(marketId, VasConstants.ServiceType.AIR_LINK)){
                continue;
            }
            PaxDynamicDsThreadLocal.setPreferenceMarketId(marketId);
            syncOverdueTerminalSuspendAndActivateStatus(marketId);
            PaxDynamicDsThreadLocal.removePreferenceMarketId();
        }
    }


    private void syncOverdueTerminalSuspendAndActivateStatus(Long marketId){
        Long lastId = null;
        int size;
        do {
            Date lastHourDateTime = DateUtils.subHours(new Date(), 1);
            List<AirLinkTerminal> terminalList = scheduleAirLinkTerminalService.findOverdueAirLinkDisablingAndResumingTerminal(marketId, lastHourDateTime, lastId, AirLinkConstants.MAX_COUNT);
            if (CollectionUtils.isEmpty(terminalList)){
                break;
            }
            for (AirLinkTerminal terminal : terminalList){
                AirLinkTerminalSuspendAndActivateMessage activeResultMessage = AirLinkTerminalSuspendAndActivateMessage.builder()
                        .marketId(marketId)
                        .operationType(terminal.getOverdueStatus() == AirLinkTerminalOverdueStatus.DISABLING ? AirLinkConstants.SUSPEND_OPERATION : AirLinkConstants.ACTIVATE_OPERATION)
                        .airLinkTerminalId(terminal.getId())
                        .build();
                airLinkTerminalSuspendAndActivateGateway.send(activeResultMessage);
            }
            size = terminalList.size();
            lastId = terminalList.get(size - 1).getId();
        }while (size >= AirLinkConstants.MAX_COUNT);
    }

    private void retryOverdueTerminalTask(Long marketId) {
        int size;
        Long lastId = null;
        do{
            List<AirLinkTerminalTask> taskList = airLinkTerminalTaskService.findOverdueTasksByMarketAndType(marketId,lastId, AirLinkConstants.MAX_COUNT);
            if (CollectionUtils.isEmpty(taskList)){
                return;
            }
            size = taskList.size();
            AirLinkTerminalTask airLinkTerminalTask = taskList.get(size - 1);
            lastId = airLinkTerminalTask.getId();
            String type = airLinkTerminalTask.getType();
            Set<String> imeiList = new HashSet<>();
            List<Long> ids = new ArrayList<>();
            taskList.forEach(item -> {
                imeiList.add(item.getImei());
                ids.add(item.getId());
            });
            switch (type) {
                case AirLinkTerminalTaskType.DISABLE -> callDisableTerminalApi(ids,imeiList);
                case AirLinkTerminalTaskType.CANCEL_DISABLE -> callResumeTerminalApi(ids,imeiList);
            }
        }while (size >= AirLinkConstants.MAX_COUNT);
    }

    private void callDisableTerminalApi(List<Long> ids ,Set<String> imeiList){
        CardsRequest request = new CardsRequest();
        request.setImeiList(new ArrayList<>(imeiList));
        try {
            vasPlatformFunc.suspendCards(request);
            scheduleAirLinkTerminalService.batchUpdateOverdueStatus(new ArrayList<>(imeiList), null, AirLinkTerminalOverdueStatus.DISABLING);
            airLinkTerminalTaskService.deleteByIds(ids);
        }catch (Exception e){
            log.warn("disable overdue airLink terminal failure!", e);
        }
    }

    private void callResumeTerminalApi(List<Long> ids ,Set<String> imeiList){
        CardsRequest request = new CardsRequest();
        request.setImeiList(new ArrayList<>(imeiList));
        try {
            vasPlatformFunc.activateCards(request);
            scheduleAirLinkTerminalService.batchUpdateOverdueStatus(new ArrayList<>(imeiList), null, AirLinkTerminalOverdueStatus.RESUMING);
            airLinkTerminalTaskService.deleteByIds(ids);
        }catch (Exception e){
            log.warn("resume overdue airLink terminal failure!", e);
        }
    }




    private void syncTerminalSuspendAndActivateStatus(Long marketId, Long resellerId){
        Long lastId = null;
        int size;
        do {
            Date lastHourDateTime = DateUtils.subHours(new Date(), 1);
            List<AirLinkTerminal> terminalList = scheduleAirLinkTerminalService.findAirLinkDisablingAndResumingTerminal(resellerId, lastHourDateTime, lastId, AirLinkConstants.MAX_COUNT);
            if (CollectionUtils.isEmpty(terminalList)) {
                break;
            }
            for (AirLinkTerminal terminal : terminalList) {
                AirLinkTerminalSuspendAndActivateMessage activeResultMessage = AirLinkTerminalSuspendAndActivateMessage.builder()
                        .marketId(marketId)
                        .operationType(terminal.getStatus() == AirLinkTerminalStatus.DISABLING ? AirLinkConstants.SUSPEND_OPERATION : AirLinkConstants.ACTIVATE_OPERATION)
                        .airLinkTerminalId(terminal.getId())
                        .build();
                airLinkTerminalSuspendAndActivateGateway.send(activeResultMessage);
            }
            size = terminalList.size();
            lastId = terminalList.get(size - 1).getId();
        } while (size >= AirLinkConstants.MAX_COUNT);
    }


    private void retryTerminalTask(Long marketId, Long refId, String groupId, String type) {
        int size;
        Long lastId = null;
        do {
            List<AirLinkTerminalTask> taskList = null;
            if (AirLinkTerminalTaskType.ACTIVE.equals(type)) {
                taskList = airLinkTerminalTaskService.findActiveTasksByMarketAndReseller(marketId, refId, lastId, AirLinkConstants.MAX_COUNT);
            } else if (AirLinkTerminalTaskType.CANCEL_ACTIVE.equals(type)) {
                taskList = airLinkTerminalTaskService.findCancelTasksByMarketId(marketId, lastId, AirLinkConstants.MAX_COUNT);
            }
            if (CollectionUtils.isEmpty(taskList)) {
                return;
            }
            size = taskList.size();
            lastId = taskList.get(size - 1).getId();
            switch (type) {
                case AirLinkTerminalTaskType.ACTIVE -> callActiveApi(taskList, refId, groupId);
                case AirLinkTerminalTaskType.CANCEL_ACTIVE -> callDeleteApi(taskList);
            }
        } while (size >= AirLinkConstants.MAX_COUNT);

    }

    private void callActiveApi(List<AirLinkTerminalTask> taskList, Long resellerId, String groupId) {
        Set<String> imeiList = new HashSet<>();
        List<Long> idList = new ArrayList<>();
        taskList.forEach(item -> {
            imeiList.add(item.getImei());
            idList.add(item.getId());
        });

        ImportCardsRequest importCardsRequest = new ImportCardsRequest();
        importCardsRequest.setResellerId(resellerId);
        importCardsRequest.setGroupId(groupId);
        importCardsRequest.setImeiList(new ArrayList<>(imeiList));
        try {
            vasPlatformFunc.importCards(importCardsRequest);
            airLinkTerminalTaskService.deleteByIds(idList);
        } catch (Exception e) {
            log.warn("reactive airlink terminal failure!", e);
        }
    }

    private void callDeleteApi(List<AirLinkTerminalTask> taskList) {
        Map<String, Long> taskMap = taskList.stream().collect(Collectors.toMap(AirLinkTerminalTask::getImei, AirLinkTerminalTask::getId));
        CardsRequest cardsRequest = new CardsRequest();
        cardsRequest.setImeiList(new ArrayList<>(taskMap.keySet()));

        List<Long> idList = new ArrayList<>();
        try {
            SingleResponse<DeleteCardsResponse> response = vasPlatformFunc.deleteCards(cardsRequest);
            if (response.isSuccess()) {
                List<DeleteCardsResponse.DeleteCardsResultDetails> results = response.getData().getResults();
                for (DeleteCardsResponse.DeleteCardsResultDetails detail : results) {
                    if (AirLinkConstants.AIRLINK_DELETE_CARD_SUCCESS_CODES.contains(detail.getStatus())) {
                        idList.add(taskMap.get(detail.getImei()));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("retry cancel active terminal failure", e);
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            airLinkTerminalTaskService.deleteByIds(idList);
        }
    }

    private void syncTerminalSN(Long marketId, String dataPoolGroupId) {
        Long lastId = null;
        int size = 0;
        do {
            List<AirLinkTerminal> terminalList = scheduleAirLinkTerminalService.findNoSNActiveTerminalByMarket(marketId, lastId, AirLinkConstants.MAX_COUNT);
            if (CollectionUtils.isEmpty(terminalList)) {
                break;
            }
            List<List<AirLinkTerminal>> subTerminalList = Lists.partition(terminalList, AirLinkConstants.MAX_GET_COUNT);
            for (List<AirLinkTerminal> list : subTerminalList) {
                Map<String, Long> groupMap = list.stream().collect(Collectors.toMap(AirLinkTerminal::getImei, AirLinkTerminal::getId));
                updateTerminalSN(marketId, groupMap, dataPoolGroupId);
            }
            size = terminalList.size();
            lastId = terminalList.get(size - 1).getId();
        } while (size >= AirLinkConstants.MAX_COUNT);
    }

    private void updateTerminalSN(Long marketId, Map<String, Long> groupMap, String dataPoolGroupId) {
        CardsRequest cardsRequest = new CardsRequest();
        cardsRequest.setImeiList(Lists.newArrayList(groupMap.keySet()));
        SingleResponse<BatchQueryCardDetailResponse> cardDetailResponse = vasPlatformFunc.batchGetDetail(cardsRequest);
        if (cardDetailResponse.isSuccess()) {
            List<AirLinkTerminal> resultList = new ArrayList<>();
            List<QueryCardDetailResponse> results = cardDetailResponse.getData().getResults();
            for (QueryCardDetailResponse detail : results) {
                boolean success = dataPoolGroupId.equals(detail.getGroupId());
                if (!success) {
                    log.error("market [{}] and imei [{}] group id do not match!", marketId, detail.getImei());
                    continue;
                }
                AirLinkTerminal airLinkTerminal = new AirLinkTerminal();
                airLinkTerminal.setId(groupMap.get(detail.getImei()));
                airLinkTerminal.setEid(detail.getEid());
                airLinkTerminal.setSerialNo(detail.getSn());
                resultList.add(airLinkTerminal);
            }
            scheduleAirLinkTerminalService.updateTerminalSN(resultList);
        }
    }

    public Map<String, List<QueryCardUsageResponse.UsageDetail>> batchGetUsage(List<AirLinkTerminal> airLinkTerminals) {
        QueryCardUsageRequest queryCardUsageRequest = new QueryCardUsageRequest();
        queryCardUsageRequest.setMonths(List.of(
                DateUtils.formatDate(new Date(), "yyyyMM")
        ));
        queryCardUsageRequest.setImeiList(airLinkTerminals.stream().map(AirLinkTerminal::getImei).toList());
        QueryCardUsageResponse usageResponse = vasPlatformFunc.queryUsage(queryCardUsageRequest).getData();
        Map<String, List<QueryCardUsageResponse.UsageDetail>> map = new HashMap<>();
        if (Objects.nonNull(usageResponse) && CollectionUtils.isNotEmpty(usageResponse.getResults())) {
            for (QueryCardUsageResponse.Usage terminalUsage : usageResponse.getResults()) {
                map.put(terminalUsage.getCardId(), terminalUsage.getUsage());
            }
        }
        return map;
    }

    public void sendEarlyWarningOfBalanceNotification(Market market, BigDecimal currentBalance, BigDecimal preDeductAmount, BigDecimal suggestedRechargeAmount) {
        List<Long> receiveIds = new ArrayList<>();
        List<Long> marketReceiveIds = getMarketReceiveIds(market.getId(), RoleID.MARKET_ADMIN);
        List<Long> superReceiveIds = getMarketReceiveIds(SystemConstants.SUPER_MARKET_ID, RoleID.SUPER_ADMIN);
        receiveIds.addAll(marketReceiveIds);
        receiveIds.addAll(superReceiveIds);
        miscNotificationService.sendEarlyWarningOfBalanceNotification(receiveIds, market.getName(), currentBalance, preDeductAmount, suggestedRechargeAmount);
    }

    public void sendEarlyWarningOfBalanceEmail(Market market, BigDecimal currentBalance, BigDecimal preDeductAmount, BigDecimal suggestedRechargeAmount) {
        Set<String> emails = new HashSet<>();
        //获取支持团队email
        List<String> supportTeamEmails = SystemPropertyHelper.getSupportTeamEmails();
        if (CollectionUtils.isNotEmpty(supportTeamEmails)) {
            emails.addAll(supportTeamEmails);
        }
        emailNotificationService.sendEarlyWarningOfBalanceEmail(
                emails,
                market.getName(),
                RequestLocaleHolder.getLocale(), currentBalance, preDeductAmount, suggestedRechargeAmount);
    }

    public void sendEarlyWarningOfUsageExceedEmail(List<AirLinkTerminal> overUsageTerminalList){
        List<AlarmTerminalExceedUsageDto.TerminalExceedUsageDto> terminalExceedUsageDtoList = overUsageTerminalList.stream()
                .filter(airLinkTerminal-> airLinkTerminal.getMonthUsage().divide(BigDecimal.valueOf(1024*1024L), 2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) > 0)
                .map(
                        airLinkTerminal -> {
                            Market market = scheduleMarketService.get(airLinkTerminal.getMarketId());
                            AlarmTerminalExceedUsageDto.TerminalExceedUsageDto dto = new AlarmTerminalExceedUsageDto.TerminalExceedUsageDto();
                            dto.setMarketName(market.getName());
                            dto.setImei(airLinkTerminal.getImei());
                            dto.setUsage(String.valueOf(airLinkTerminal.getMonthUsage().divide(BigDecimal.valueOf(1024*1024L), 2, RoundingMode.HALF_UP)));
                            return dto;
                        }
                )
                .toList();
        if (CollectionUtils.isNotEmpty(terminalExceedUsageDtoList)) {
            Set<String> emails = new HashSet<>();
            List<String> supportTeamEmails = SystemPropertyHelper.getSupportTeamEmails();
            if (CollectionUtils.isNotEmpty(supportTeamEmails)) {
                emails.addAll(supportTeamEmails);
            }
            emailNotificationService.sendEarlyWarningOfUsageExceedEmail(emails,terminalExceedUsageDtoList);
            List<Long> superReceiveIds = getMarketReceiveIds(SystemConstants.SUPER_MARKET_ID, RoleID.SUPER_ADMIN);
            if (CollectionUtils.isNotEmpty(superReceiveIds)) {
                miscNotificationService.sendEarlyWarningOfUsageNotification(superReceiveIds,terminalExceedUsageDtoList);
            }
        }
    }

    private List<Long> getMarketReceiveIds(Long marketId, Long roleId) {
        User user = new User();
        user.setMarketIds(Sets.newHashSet(marketId));
        user.setRoleIds(Sets.newHashSet(roleId));
        return scheduleRoleService.findMarketUserIds(user, null);
    }


}
