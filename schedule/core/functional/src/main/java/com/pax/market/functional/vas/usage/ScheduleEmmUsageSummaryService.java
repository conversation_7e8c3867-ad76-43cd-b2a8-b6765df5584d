package com.pax.market.functional.vas.usage;


import com.pax.api.fs.SupportedFileTypes;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.excel.vas.EmmUsageForGlobalExport;
import com.pax.market.domain.entity.excel.vas.EmmUsageForMarketExport;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.vas.emm.EmmMarketSummary;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.framework.common.excel.ExportExcel;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketService;
import com.paxstore.schedule.global.domain.service.vas.emm.ScheduleEmmMarketSummaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR> Zuo
 * @since 9.5
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleEmmUsageSummaryService implements VasUsageSummaryService {
    private final ScheduleEmmMarketSummaryService scheduleEmmMarketSummaryService;
    private final BillingUsageService billingUsageService;
    private final ScheduleMarketService marketService;

    @Override
    public String serviceType() {
        return EMM;
    }

    @Override
    public void buildGenerationUsageHistory(Long marketId, Date startDate, Date endDate) {
        String period = DateUtils.formatDate(startDate, DateUtils.PERIOD_DATE_FORMAT);
        if (LongUtils.equals(SystemConstants.SUPER_MARKET_ID, marketId) || scheduleEmmMarketSummaryService.hasMarketUsageThisMonth(marketId, period)) {
            return;
        }
        long totalCount = billingUsageService.getTotalUsage(marketId, null, serviceType(), period);
        if (totalCount <= 0) {
            return;
        }
        EmmMarketSummary info = new EmmMarketSummary();
        info.setAmount(totalCount);
        info.setGlobalSnapshotFileId(buildUsageExcel(marketId, EmmUsageForGlobalExport.class, startDate));
        info.setSnapshotFileId(buildUsageExcel(marketId, EmmUsageForMarketExport.class, startDate));
        info.setPeriod(period);
        info.setCreatedDate(new Date());
        info.setMarketId(marketId);
        info.setMonthlyUsage(true);

        scheduleEmmMarketSummaryService.insertMarketUsage(info);
    }


    private String buildUsageExcel(Long marketId, Class<?> clazz, Date startDate) {
        String filedId = "";
        Locale locale = LocaleUtils.getEnvLocale();
        Market market = marketService.getWithDeleted(marketId);
        if (Objects.isNull(market)) {
            log.warn("buildUsageExcel in EMM failed for marketId:{},market not found.", marketId);
            return filedId;
        }
        String period = DateUtils.formatDate(startDate, DateUtils.PERIOD_DATE_FORMAT);

        String excelTitle = String.format("%s %s %s - %s/%s", market.getName(), MessageUtils.getLocaleMessage("label.emm", locale), MessageUtils.getLocaleMessage("title.usage.details", locale), DateUtils.getMonth(startDate), DateUtils.getYear(startDate));
        String sheetName = String.format("%s %s", MessageUtils.getLocaleMessage("label.emm", locale), MessageUtils.getLocaleMessage("title.usage.details", locale));

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ExportExcel exportExcel = new ExportExcel(excelTitle, sheetName, clazz, true, LocaleUtils.getEnvLocale())) {
            writeDataInExcel(marketId, period, exportExcel::setDataList);
            exportExcel.write(bos);
            filedId = FileUploader.uploadFile(
                    bos.toByteArray(),
                    MessageUtils.getLocaleMessage("label.emm", locale) + "_" + MessageUtils.getLocaleMessage("excel.usage.details", locale) + "_" + DateUtils.getMonth(startDate) + "_" + DateUtils.getYear(startDate),
                    ".xlsx",
                    SupportedFileTypes.REPORT);

        } catch (Exception e) {
            log.error("Generate EMM File for marketId:{} failed as exception:{}", marketId, e.getMessage());
        }
        return filedId;
    }

    public void writeDataInExcel(Long marketId, String period, Consumer<List<Terminal>> setDataList) {
        List<Terminal> terminals;
        Long terminalId = null;
        do {
            terminals = billingUsageService.findServiceUsageForExport(null, null, marketId, serviceType(), period, terminalId);
            if (CollectionUtils.isNotEmpty(terminals)) {
                setDataList.accept(terminals);
                terminalId = terminals.get(terminals.size() - 1).getId();
            }
        } while (CollectionUtils.isNotEmpty(terminals));
    }

}
