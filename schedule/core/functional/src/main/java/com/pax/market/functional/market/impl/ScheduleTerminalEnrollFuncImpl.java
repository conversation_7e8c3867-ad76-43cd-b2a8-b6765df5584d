package com.pax.market.functional.market.impl;

import com.pax.core.json.JsonMapper;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.billing.MarketBillingPriceSettingService;
import com.pax.market.billing.MarketBillingService;
import com.pax.market.constants.*;
import com.pax.market.domain.entity.global.billing.MarketBillingType;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.market.organization.Reseller;
import com.pax.market.domain.entity.market.terminal.Terminal;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.dto.LicenseInfo;
import com.pax.market.dto.MarketBillingPriceSettingInfo;
import com.pax.market.dto.billing.FineTerminalInfo;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.functional.market.ScheduleTerminalEnrollFunc;
import com.pax.market.functional.support.DiscountTerminalSupport;
import com.pax.market.functional.support.ScheduleTerminalRegistrySupport;
import com.pax.market.mq.contract.billing.SyncServiceTerminalCountMessage;
import com.pax.market.mq.contract.billing.SyncTerminalEnrollDetailMessage;
import com.pax.market.mq.producer.gateway.billing.SyncData2BillingGateway;
import com.paxstore.schedule.global.domain.service.app.ScheduleApkService;
import com.paxstore.schedule.global.domain.service.app.ScheduleAppService;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketService;
import com.paxstore.schedule.global.domain.service.setting.ScheduleLicenseService;
import com.paxstore.schedule.global.domain.service.vas.ScheduleServiceResellerService;
import com.paxstore.schedule.market.domain.service.terminal.ScheduleMarketTerminalService;
import com.paxstore.schedule.market.domain.service.terminal.ScheduleTerminal2ServiceService;
import com.paxstore.schedule.market.domain.service.terminal.ScheduleTerminalAccessTimeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.pax.market.constants.TerminalCountType.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleTerminalEnrollFuncImpl implements ScheduleTerminalEnrollFunc {

    private final ScheduleMarketService marketService;
    private final ScheduleMarketTerminalService marketTerminalService;
    private final ScheduleTerminalAccessTimeService terminalAccessTimeService;
    private final ScheduleTerminalRegistrySupport terminalRegistrySupport;
    private final DiscountTerminalSupport discountTerminalSupport;
    private final ScheduleLicenseService licenseService;
    private final SyncData2BillingGateway syncData2BillingGateway;
    private final MarketBillingPriceSettingService marketBillingPriceSettingService;
    private final BillingUsageService billingUsageService;
    private final ScheduleServiceResellerService serviceResellerService;
    private final MarketBillingService marketBillingService;
    private final ScheduleAppService appService;
    private final ScheduleApkService apkService;
    private final ScheduleTerminal2ServiceService terminal2ServiceService;


    /**
     * @param marketId
     * @param beginOfMonth
     * @param beginOfNextMonth
     */
    public void calcMarketTerminalEnroll(Long marketId, Date beginOfMonth, Date beginOfNextMonth) {
        int limit = 100000;
        Long lastTerminalId = null;
        Market market = marketService.get(marketId);
        if (market == null) return;
        Map<Reseller, Map<Integer, Integer>> map = new HashMap<>();
        Map<Long, Set<String>> modelMap = new HashMap<>();
        MarketBillingPriceSettingInfo info = null;
        boolean insightDistributionEnabled = false;
        boolean airViewerChargeTypeIsTerminal = false;
        List<Long> insightResellerIds = new ArrayList<>();
        List<Long> airViewerResellerIds= new ArrayList<>();
        List<Long> airViewerModelIds = new ArrayList<>();
        List<Long> airViewerSupportTerminalIds = new ArrayList<>();
        LicenseInfo licenseInfo = licenseService.getLicenseInfo();
        List<MarketBillingPriceSettingInfo> billingSettingList = marketBillingPriceSettingService.findMarketBillingPriceSettings(marketId, market.getBillingMode());
        if (CollectionUtils.isNotEmpty(billingSettingList)) {
            Map<String, MarketBillingPriceSettingInfo> billingSettingMap = billingSettingList.stream().collect(Collectors.toMap(MarketBillingPriceSettingInfo::getBillType, (s) -> s));
            if (BooleanUtils.isTrue(licenseInfo.isAllowMarketBilling())) {
                info =  billingSettingMap.get(MarketBillingType.TerminalEnrollment.getCode());
            }
            MarketBillingPriceSettingInfo insightBillingSetting = billingSettingMap.get(MarketBillingType.GoInsight.getCode());
            if (insightBillingSetting != null && BooleanUtils.isTrue(insightBillingSetting.getCharge()) && StringUtils.equals(insightBillingSetting.getChargeType(), VasChargeType.ENROLL_TERMINAL)) {
                insightDistributionEnabled = insightBillingSetting.isDistributionEnabled();
                if (insightDistributionEnabled) {
                    insightResellerIds = serviceResellerService.findServiceResellerIds(marketId, MarketBillingType.GoInsight.getCode());
                }
            }
            MarketBillingPriceSettingInfo airViewerBillingSetting = billingSettingMap.get(MarketBillingType.AirViewerUsage.getCode());
            if (airViewerBillingSetting != null && BooleanUtils.isTrue(airViewerBillingSetting.getCharge()) && StringUtils.equals(airViewerBillingSetting.getChargeType(), VasChargeType.ENROLL_TERMINAL)) {
                airViewerChargeTypeIsTerminal = true;
                if (airViewerBillingSetting.isDistributionEnabled()) {
                    airViewerResellerIds = serviceResellerService.findServiceResellerIds(marketId, MarketBillingType.AirViewerUsage.getCode());
                }
                Long appId = appService.getByPackageName(SystemConstants.SUPER_MARKET_ID, SystemConstants.AIR_VIEWER_PACKAGE_NAME);
                if (appId != null) {
                    airViewerModelIds = apkService.findApkModelIdList(appId);
                }
                airViewerSupportTerminalIds = terminal2ServiceService.findTerminalIdsByServiceType(marketId, MarketBillingType.AirViewerUsage.getCode());
            }
        }
        int insightTerminalCount = 0;
        int airViewerTerminalCount = 0;
        List<String> fineSerialNoList;
        if (!MarketBillingMode.NIGERIA_MODE.equals(market.getBillingMode()) && info != null &&
                info.getTerminalViolationPolicySetting() != null && info.getTerminalViolationPolicySetting().isViolationPolicyEnabled() &&
                licenseInfo.isAllowConnectionFeeViolationPolicy()) {
            fineSerialNoList = findFineTerminalList(marketId, DateUtils.getCurrentYear(beginOfMonth), DateUtils.getMonth(beginOfMonth));
        } else {
            fineSerialNoList = new ArrayList<>();
        }
        Set<String> uniqueSerialNos = new HashSet<>();
        boolean isGeideaMode = MarketBillingMode.GEIDEA_MODE.equals(market.getBillingMode());
        String airViewerTraditionalModels = SystemPropertyHelper.getAirViewerTraditionalModels();
        while (true) {
            List<Terminal> billableTerminals = marketTerminalService.findBillableTerminalList(market, beginOfMonth, lastTerminalId, limit);
            if (CollectionUtils.isEmpty(billableTerminals)) {
                break;
            }
            lastTerminalId = billableTerminals.get(billableTerminals.size() - 1).getId();
            billableTerminals = billableTerminals.stream().filter(terminal -> checkTerminalAccessTime(terminal, isGeideaMode, beginOfNextMonth)).collect(Collectors.toList());
            terminalRegistrySupport.loadTerminalModelResellerDetails(billableTerminals);

            billableTerminals = billableTerminals.parallelStream().filter(terminal -> ProductTypeUtils.isEnrollTerminal(terminal.getProductType())).collect(Collectors.toList());
            if (info != null && info.getDiscountSetting() != null && BooleanUtils.isTrue(info.getCharge())) {
                discountTerminalSupport.loadTerminalIsDiscount(billableTerminals, info.getDiscountSetting(), beginOfMonth, modelMap);
            }
            for (Terminal terminal : billableTerminals) {
                if (uniqueSerialNos.contains(terminal.getSerialNo())) continue;
                uniqueSerialNos.add(terminal.getSerialNo());

                map.computeIfAbsent(terminal.getReseller(), k -> new HashMap<>());
                Map<Integer, Integer> resellerCountMap = map.get(terminal.getReseller());

                int terminalCountType = getTerminalCountType(terminal, fineSerialNoList);
                resellerCountMap.putIfAbsent(terminalCountType, 0);
                resellerCountMap.put(terminalCountType, resellerCountMap.get(terminalCountType) + 1);
                map.put(terminal.getReseller(), resellerCountMap);

                if (insightDistributionEnabled && filterGoInsightTerminal(terminal, insightResellerIds)) {
                    insightTerminalCount += 1;
                }
                if (airViewerChargeTypeIsTerminal && CollectionUtils.isNotEmpty(airViewerModelIds)) {
                    if (filterAirViewerTerminal(terminal, airViewerResellerIds, airViewerModelIds, airViewerSupportTerminalIds)) {
                        airViewerTerminalCount += 1;
                    }
                }
            }
        }
        try {
            billingUsageService.deleteTerminalEnrollOfCurrentMonth(marketId);
        } catch (Exception e) {
            log.warn("delete current month usage error", e);
        }
        map.forEach((reseller, resellerCountMap) -> resellerCountMap.forEach((countType, count) -> {
            SyncTerminalEnrollDetailMessage message = SyncTerminalEnrollDetailMessage.builder().
                    marketId(marketId)
                    .resellerId(reseller.getId())
                    .resellerParentIds(reseller.getParentIds())
                    .year(DateUtils.getCurrentYear(beginOfMonth))
                    .month(0)
                    .chargeTerminalNum(count)
                    .activeTerminalNum(count)
                    .type(countType).build();
            syncData2BillingGateway.sendTerminalEnrollDetail(JsonMapper.toJsonString(message));
        }));
        if (insightTerminalCount > 0 || airViewerTerminalCount > 0) {
            SyncServiceTerminalCountMessage syncServiceTerminalCountMessage = SyncServiceTerminalCountMessage.builder()
                    .marketId(marketId)
                    .year(DateUtils.getCurrentYear(beginOfMonth))
                    .month(0)
                    .insightCount(insightTerminalCount)
                    .airViewerCount(airViewerTerminalCount).build();
            syncData2BillingGateway.sendServiceTerminalCount(JsonMapper.toJsonString(syncServiceTerminalCountMessage));
        }
    }
    private boolean filterGoInsightTerminal(Terminal terminal, List<Long> resellerIds) {
        if (CollectionUtils.isNotEmpty(resellerIds)) {
            return ProductType.ANDROID_DEVICE.contains(terminal.getProductType()) && resellerIds.contains(terminal.getResellerId());
        }
        return ProductType.ANDROID_DEVICE.contains(terminal.getProductType());
    }

    private boolean filterAirViewerTerminal(Terminal terminal, List<Long> resellerIds, List<Long> airViewerModelIds, List<Long> airViewerSupportTerminalIds) {
        boolean airViewerSpecific = true;
        if (CollectionUtils.isNotEmpty(resellerIds)) {
            airViewerSpecific = resellerIds.contains(terminal.getResellerId());
        }
        return airViewerSpecific && (airViewerModelIds.contains(terminal.getModelId()) || airViewerSupportTerminalIds.contains(terminal.getId()));
    }


    private int getTerminalCountType(Terminal terminal, List<String> fineSerialNoList) {
        int key;
        if (LongUtils.equals(SystemPropertyHelper.getPaxFactoryDefaultId(), terminal.getFactoryId()) || StringUtils.equalsIgnoreCase(SystemPropertyHelper.getZolonFactoryName(), terminal.getFactoryName())) {
            key = ProductType.ANDROID_DEVICE.contains(terminal.getProductType()) ? PAX_ANDROID : PAX_TRADITIONAL;
        } else {
            key = ProductType.ANDROID_DEVICE.contains(terminal.getProductType()) ? OTHER_ANDROID : OTHER_TRADITIONAL;
        }
        if (terminal.isDiscountTerminal()) {
            key = COUNT_TYPE_MAP.get(key);
        }
        if (fineSerialNoList.contains(terminal.getSerialNo())) {
            key = FINE_COUNT_TYPE_MAP.get(key);
        }
        return key;
    }

    private boolean checkTerminalAccessTime(Terminal terminal, boolean isGeideaMode, Date beginOfNextMonth) {
        terminalAccessTimeService.loadTerminalLastAccessTime(terminal);
        return isGeideaMode ?
                terminal.getLastAccessTime() != null && !DateUtils.addMonths(terminal.getLastAccessTime(), SystemPropertyHelper.getGeideaAccessMonths()).before(beginOfNextMonth) :
                terminal.getLastAccessTime() != null;
    }

    private List<String> findFineTerminalList(Long marketId, int year, int month) {
        Long lastId = null;
        List<String> fineSerialNoList = new ArrayList<>();
        while (true) {
            List<FineTerminalInfo> terminalInfos = marketBillingService.findFineTerminalList(marketId, lastId, year, month);
            if (CollectionUtils.isEmpty(terminalInfos)) {
                break;
            }
            lastId = terminalInfos.get(terminalInfos.size() - 1).getId();
            fineSerialNoList.addAll(terminalInfos.parallelStream().map(FineTerminalInfo::getSerialNo).toList());
        }
        return fineSerialNoList;
    }

}
