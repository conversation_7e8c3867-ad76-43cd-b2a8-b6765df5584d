package com.pax.market.functional.vas.usage;

import com.google.common.collect.Lists;
import com.pax.api.fs.SupportedFileTypes;
import com.pax.market.billing.BillingUsageService;
import com.pax.market.constants.SystemConstants;
import com.pax.market.domain.entity.excel.vas.AirLinkUsageForGlobalExport;
import com.pax.market.domain.entity.excel.vas.AirLinkUsageForMarketExport;
import com.pax.market.domain.entity.global.market.Market;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrder;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrderItem;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrderSummary;
import com.pax.market.domain.entity.global.vas.airlink.AirLinkOrderSummaryItem;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.dto.billing.ServiceUsageDetailInfo;
import com.pax.market.dto.billing.TerminalAirLinkImpressInfo;
import com.pax.market.dto.vas.AirLinkDataPoolSettingInfo;
import com.pax.market.dto.vas.AirLinkUsageInfo;
import com.pax.market.dto.vas.MarketAirLinkSettingInfo;
import com.pax.market.dto.vas.MarketAirLinkSubscriptionPlanInfo;
import com.pax.market.framework.common.excel.ExportExcel;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.market.mq.contract.airlink.AirLinkTerminalOverageTrafficDeductMessage;
import com.pax.market.mq.producer.gateway.airlink.AirLinkTerminalDeductGateway;
import com.pax.market.service.center.ServiceCenterFunc;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketService;
import com.paxstore.schedule.global.domain.service.vas.ScheduleAirLinkUsageDetailService;
import com.paxstore.schedule.global.domain.service.vas.airlink.ScheduleAirLinkOrderSummaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/15 13:51
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleAirLinkUsageSummaryService implements VasUsageSummaryService {
    private final BillingUsageService billingUsageService;
    private final ScheduleMarketService marketService;
    private final ScheduleAirLinkUsageDetailService airLinkUsageDetailService;
    private final ServiceCenterFunc serviceCenterFunc;
    private final AirLinkTerminalDeductGateway airLinkTerminalDeductGateway;
    private final ScheduleAirLinkOrderSummaryService airLinkOrderSummaryService;

    @Override
    public String serviceType() {
        return AIR_LINK;
    }

    @Override
    public void buildGenerationUsageHistory(Long marketId, Date startDate, Date endDate) {
        String period = DateUtils.formatDate(startDate, DateUtils.PERIOD_DATE_FORMAT);
        if (LongUtils.equals(SystemConstants.SUPER_MARKET_ID, marketId) || airLinkUsageDetailService.hasMarketUsageThisMonth(marketId, period)) {
            return;
        }
        ServiceUsageDetailInfo airLinkTotalUsage = billingUsageService.getAirLinkTotalUsage(marketId, null, period);
        if (Objects.isNull(airLinkTotalUsage)) {
            return;
        }
        AirLinkUsageInfo usageInfo = new AirLinkUsageInfo();
        usageInfo.setMarketId(marketId);
        usageInfo.setSnapshotFileId(buildUsageExcel(marketId, AirLinkUsageForMarketExport.class, startDate));
        usageInfo.setGlobalSnapshotFileId(buildUsageExcel(marketId, AirLinkUsageForGlobalExport.class, startDate));

        usageInfo.setDataUsage(airLinkTotalUsage.getDataUsage());
        usageInfo.setTerminalCount(airLinkTotalUsage.getTerminalCount());
        MarketAirLinkSubscriptionPlanInfo subscriptionPlanInfo = serviceCenterFunc.getSubscriptionPlanByPeriod(marketId, period);
        if (Objects.isNull(subscriptionPlanInfo) || Objects.isNull(subscriptionPlanInfo.getDataPoolId())) {
            return;
        }
        usageInfo.setPackageFee(subscriptionPlanInfo.getPackageFee());
        usageInfo.setOveragePrice(subscriptionPlanInfo.getOveragePrice());
        usageInfo.setPackageType(subscriptionPlanInfo.getPackageType());
        usageInfo.setPackageLimit(subscriptionPlanInfo.getPackageLimit());
        AirLinkDataPoolSettingInfo airLinkDataPoolSetting = serviceCenterFunc.getAirLinkDataPoolSetting(subscriptionPlanInfo.getDataPoolId());
        usageInfo.setServiceProvider(airLinkDataPoolSetting.getServiceProvider());
        usageInfo.setDataPoolName(airLinkDataPoolSetting.getName());
        usageInfo.setCreatedDate(new Date());
        usageInfo.setMonthlyUsage(true);
        usageInfo.setPeriod(period);
        airLinkUsageDetailService.insertMarketUsage(usageInfo);

        AirLinkTerminalOverageTrafficDeductMessage message = new AirLinkTerminalOverageTrafficDeductMessage();
        message.setMarketId(marketId);
        //要扣除的是哪个月的超额流量,这里是扣除上月
        message.setDeductTime(DateUtils.lastMonth(new Date()));
        airLinkTerminalDeductGateway.send(message);
    }

    private String buildUsageExcel(Long marketId, Class<?> clazz, Date startDate) {
        String filedId = "";
        Locale locale = LocaleUtils.getEnvLocale();
        Market market = marketService.getWithDeleted(marketId);
        if (Objects.isNull(market)) {
            log.warn("buildUsageExcel in AirLink failed for marketId:{}, market not found.", marketId);
            return filedId;
        }
        String period = DateUtils.formatDate(startDate, DateUtils.PERIOD_DATE_FORMAT);
        String excelTitle = String.format("%s %s %s - %s/%s", market.getName(), MessageUtils.getLocaleMessage("label.airlink", locale), MessageUtils.getLocaleMessage("title.usage.details", locale), DateUtils.getMonth(startDate), DateUtils.getYear(startDate));
        String sheetName = String.format("%s %s", MessageUtils.getLocaleMessage("label.airlink", locale), MessageUtils.getLocaleMessage("title.usage.details", locale));
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ExportExcel exportExcel = new ExportExcel(excelTitle, sheetName, clazz, true, LocaleUtils.getEnvLocale())) {
            writeDataInExcel(marketId, period, exportExcel::setDataList);
            exportExcel.write(bos);
            filedId = FileUploader.uploadFile(
                    bos.toByteArray(),
                    MessageUtils.getLocaleMessage("label.airlink", locale) + "_" + MessageUtils.getLocaleMessage("excel.usage.details", locale) + "_" + DateUtils.getMonth(startDate) + "_" + DateUtils.getYear(startDate),
                    ".xlsx",
                    SupportedFileTypes.REPORT);

        } catch (Exception e) {
            log.error("Generate AirLink File for marketId:{} failed as exception:{}", marketId, e.getMessage());
        }
        return filedId;
    }

    private void writeDataInExcel(Long marketId, String period, Consumer<List<?>> consumer) {
        List<TerminalAirLinkImpressInfo> airLinkImpressInfos;
        Long airlinkTerminalId = null;
        MarketAirLinkSettingInfo marketAirLinkSetting = serviceCenterFunc.getMarketAirLinkSetting(marketId);
        do {
            airLinkImpressInfos = billingUsageService.findAirLinkUsageForExport(marketId, serviceType(), period, airlinkTerminalId, Objects.nonNull(marketAirLinkSetting) ? marketAirLinkSetting.getDataPoolName() : null);
            if (CollectionUtils.isNotEmpty(airLinkImpressInfos)) {
                consumer.accept(airLinkImpressInfos);
                airlinkTerminalId = airLinkImpressInfos.get(airLinkImpressInfos.size() - 1).getId();
            }
        } while (CollectionUtils.isNotEmpty(airLinkImpressInfos));
    }


    public void summarizeOrders() {
        Date lastMonth = DateUtils.subMonths(new Date(), 1);
        String period = DateUtils.formatDate(lastMonth, DateUtils.PERIOD_DATE_FORMAT);
        int year = DateUtils.getCurrentYear(lastMonth);
        int month = DateUtils.getMonth(lastMonth);
        List<AirLinkOrder> orders = airLinkOrderSummaryService.findDeductionOrdersByPeriod(period);
        Map<Long, List<AirLinkOrder>> marketIdOrdersMapper = orders.stream().collect(Collectors.groupingBy(AirLinkOrder::getMarketId));
        marketIdOrdersMapper.forEach((marketId, orderList) -> {
            if (!airLinkOrderSummaryService.hasSummary(marketId, year, month)) {
                AirLinkOrderSummary airLinkOrderSummary = new AirLinkOrderSummary();
                airLinkOrderSummary.setMarketId(marketId);
                airLinkOrderSummary.setYear(year);
                airLinkOrderSummary.setMonth(month);
                airLinkOrderSummary.setTotalAmount(orderList.stream().map(AirLinkOrder::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                airLinkOrderSummary.setDiscountAmount(orderList.stream().map(AirLinkOrder::getDiscountAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                airLinkOrderSummaryService.save(airLinkOrderSummary);
                List<Long> orderIds = orderList.stream().map(AirLinkOrder::getId).toList();
                List<AirLinkOrderItem> itemsByOrderIds = Lists.partition(orderIds, 100)
                        .stream()
                        .flatMap(ids -> airLinkOrderSummaryService.findItemsByOrderIds(ids)
                                .stream())
                        .toList();
                itemsByOrderIds.stream().collect(Collectors.groupingBy(AirLinkOrderItem::getType, Collectors.groupingBy(AirLinkOrderItem::getPrice))).forEach(((airLinkOrderItemType, priceMap) -> {
                    AirLinkOrderSummaryItem airLinkOrderSummaryItem = new AirLinkOrderSummaryItem();
                    airLinkOrderSummaryItem.setMarketId(marketId);
                    airLinkOrderSummaryItem.setSummaryId(airLinkOrderSummary.getId());
                    airLinkOrderSummaryItem.setYear(year);
                    airLinkOrderSummaryItem.setMonth(month);
                    priceMap.forEach((unitPrice, item) -> {
                        airLinkOrderSummaryItem.setType(airLinkOrderItemType.getCode());
                        airLinkOrderSummaryItem.setUnitPrice(unitPrice);
                        long sum = item.stream().mapToLong(AirLinkOrderItem::getQuantity).sum();
                        airLinkOrderSummaryItem.setQuantity(sum);
                        airLinkOrderSummaryItem.setTotalAmount(unitPrice.multiply(BigDecimal.valueOf(sum)));
                        airLinkOrderSummaryService.saveItem(airLinkOrderSummaryItem);
                    });
                }));
            }
        });
    }

}
