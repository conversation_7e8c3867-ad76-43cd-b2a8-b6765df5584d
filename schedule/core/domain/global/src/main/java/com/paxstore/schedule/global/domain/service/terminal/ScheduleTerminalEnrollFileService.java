package com.paxstore.schedule.global.domain.service.terminal;

import com.pax.market.domain.entity.global.usage.TerminalEnrollFile;
import com.pax.market.framework.common.service.CrudService;
import com.paxstore.schedule.global.domain.dao.terminal.ScheduleTerminalEnrollFileDao;
import org.springframework.stereotype.Service;

@Service
public class ScheduleTerminalEnrollFileService extends CrudService<ScheduleTerminalEnrollFileDao, TerminalEnrollFile> {

    public TerminalEnrollFile getByMarketIdYearMonth(Long marketId, int year, int month) {
        return dao.getByMarketIdYearMonth(marketId, year, month);
    }
}
