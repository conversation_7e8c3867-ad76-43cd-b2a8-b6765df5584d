/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.paxstore.schedule.global.domain.dao.setting;


import com.pax.market.domain.entity.market.setting.AnnouncementContext;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ScheduleAnnouncementContextDao extends CrudDao<AnnouncementContext> {

    List<Long> findMarketIdsOfWaitingAnnouncement();

    /**
     * find wait announcement
     *
     * @return the list
     */
    List<AnnouncementContext> findWaitingAnnouncementByMarketId(@Param("marketId") Long marketId);

    void updateAnnouncement2CompletedByIds(List<Long> ids);
}
