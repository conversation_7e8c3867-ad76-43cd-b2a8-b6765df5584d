package com.paxstore.schedule.global.domain.dao.market;

import com.pax.market.domain.entity.global.market.MarketSetting;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ScheduleMarketSettingDao extends CrudDao<MarketSetting> {

    MarketSetting get(@Param("marketId") Long marketId, @Param("key") String key);

    List<MarketSetting> getMarketSettings(@Param("marketId") Long marketId);

    List<MarketSetting> getMarketSettingByKeys(@Param("marketId") Long marketId, @Param("keys") List<String> keys);
}
