package com.paxstore.schedule.global.domain.dao.vas;

import com.pax.market.domain.entity.global.vas.App2Service;
import com.pax.market.dto.vas.VasAppUsageInfo;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ScheduleApp2ServiceDao extends CrudDao<App2Service> {
    App2Service getByMarketIdAndAppId(@Param("marketId") Long marketId,
                                      @Param("appId") Long appId);

    List<Long> findEnabledVasAppIds(@Param("marketId") Long marketId);


    boolean hasMarketUsage(@Param("appId") Long appId,
                           @Param("marketId") Long marketId,
                           @Param("period") String period);

    void insertMarketLevUsage(VasAppUsageInfo info);


    String getAppCurrentName(@Param("appId") Long appId);
}
