package com.paxstore.schedule.global.domain.dao.vas.appscan;

import com.pax.market.domain.entity.global.vas.appscan.ScanTask;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

import java.util.List;

@MyBatisDao
public interface ScheduleScanTaskDao extends CrudDao<ScanTask> {

    /**
     * 获取Inprogress 的任务
     */
    List<Long> getTaskIdForInprogress();

    /**
     * 根据taskId获取ScanTask
     *
     * @param taskId
     * @return
     */
    ScanTask getScanTaskByTaskId(Long taskId);

    /**
     * 获取Fail 的任务
     */
    List<Long> getTaskIdForFail();

}
