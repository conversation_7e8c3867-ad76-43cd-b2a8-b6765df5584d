package com.paxstore.schedule.global.domain.service.vas;

import com.pax.market.domain.entity.market.terminal.TerminalAdUsageDetail;
import com.pax.market.dto.vas.AdUpUsageInfo;
import com.pax.market.framework.common.service.CrudService;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.schedule.global.domain.dao.vas.ScheduleAdUpUsageDetailDao;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/12/20
 */
@Service
public class ScheduleAdUpUsageDetailService extends CrudService<ScheduleAdUpUsageDetailDao, TerminalAdUsageDetail> {

    @MasterDs
    public void insertMarketUsage(AdUpUsageInfo adupUsageInfo) {
        dao.insertMarketUsage(adupUsageInfo);
    }

    public boolean hasMarketUsageThisMonth(Long marketId, String period) {
        return dao.hasMarketUsageThisMonth(marketId, period);
    }

}
