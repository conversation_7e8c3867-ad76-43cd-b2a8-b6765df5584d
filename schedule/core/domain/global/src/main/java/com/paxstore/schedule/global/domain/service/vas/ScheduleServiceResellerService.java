package com.paxstore.schedule.global.domain.service.vas;

import com.pax.market.domain.entity.global.vas.ServiceReseller;
import com.pax.market.framework.common.service.CrudService;
import com.paxstore.schedule.global.domain.dao.vas.ScheduleServiceResellerDao;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ScheduleServiceResellerService extends CrudService<ScheduleServiceResellerDao, ServiceReseller> {

    public List<Long> findServiceResellerIds(Long marketId, String serviceType) {
        return dao.findServiceResellerIds(marketId, serviceType);
    }

    /**
     * 判断该代理商的服务是否定向发布
     *
     * @param serviceType the service type
     * @param marketId    the market id
     * @param resellerId  the reseller id
     * @return the boolean
     */
    public boolean checkServiceResellerSpecific(String serviceType, Long marketId, Long resellerId) {
        return BooleanUtils.isTrue(dao.checkServiceResellerSpecific(serviceType, marketId, resellerId));
    }

}
