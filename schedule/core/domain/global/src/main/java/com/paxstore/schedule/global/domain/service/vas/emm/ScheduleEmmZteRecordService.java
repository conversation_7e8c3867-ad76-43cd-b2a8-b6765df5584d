/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.paxstore.schedule.global.domain.service.vas.emm;

import com.pax.market.constants.emm.EmmZteRecordStatus;
import com.pax.market.domain.entity.global.emm.EmmZteRecord;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.date.DateUtils;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.schedule.global.domain.dao.vas.emm.ScheduleEmmZteRecordDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>" rel="nofollow">suyunlong</a>
 * @date 2024/11/11
 */
@Service
@RequiredArgsConstructor
public class ScheduleEmmZteRecordService extends CrudService<ScheduleEmmZteRecordDao, EmmZteRecord> {

    public List<EmmZteRecord> findExpiredList() {
        return dao.findExpiredList(DateUtils.subMonths(new Date(), 1), List.of(EmmZteRecordStatus.REJECTED.getSign(), EmmZteRecordStatus.WITHDRAWN.getSign()));
    }

    @MasterDs
    public void updateSnapshotFileid(Long id, String globalFileId, String marketFileId) {
        EmmZteRecord emmZteRecord = new EmmZteRecord();
        emmZteRecord.setId(id);
        emmZteRecord.setSnapshotFileId(marketFileId);
        emmZteRecord.setGlobalSnapshotFileId(globalFileId);
        dao.updateSnapshotFileid(emmZteRecord);
    }
}
