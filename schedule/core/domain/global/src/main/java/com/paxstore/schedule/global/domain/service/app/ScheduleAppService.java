package com.paxstore.schedule.global.domain.service.app;

import com.pax.market.domain.entity.global.app.App;
import com.pax.market.domain.query.AppApkQuery;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.StringUtils;
import com.paxstore.schedule.global.domain.dao.app.ScheduleAppDao;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class ScheduleAppService extends CrudService<ScheduleAppDao, App> {

    /**
     * 查找已删除的应用需要清理cloudMessage的 appIds
     *
     * @param marketId the market id
     * @return the page
     */
    public List<Long> findDeletedAppIdListForClearCloudMessage(Long marketId) {
        AppApkQuery originAppApkQuery = new AppApkQuery();
        originAppApkQuery.setCurrentMarketId(marketId);
        originAppApkQuery.setAppDeleted(Boolean.TRUE);
        return dao.findAppIdListForClearCloudMessage(originAppApkQuery);
    }

    /**
     * Find ids for market developer disabled
     *
     * @param marketId the market id
     * @return the page
     */
    public List<Long> findAppIdListForClearMarketCloudMessage(Long marketId) {
        AppApkQuery originAppApkQuery = new AppApkQuery();
        originAppApkQuery.setCurrentMarketId(marketId);
        originAppApkQuery.setAppDeleted(Boolean.FALSE);
        return dao.findAppIdListForClearCloudMessage(originAppApkQuery);
    }

    /**
     * 查找禁用服务的开发者所属的cloudMessage AppIds
     *
     * @param marketId the market id
     * @return the page
     */
    public List<Long> findAppIdListForClearMarketDisableDeveloperCloudMessage(Long marketId, Long developerId) {
        AppApkQuery originAppApkQuery = new AppApkQuery();
        originAppApkQuery.setDeveloperId(developerId);
        originAppApkQuery.setCurrentMarketId(marketId);
        originAppApkQuery.setAppDeleted(Boolean.FALSE);
        return dao.findAppIdListForClearCloudMessage(originAppApkQuery);
    }

    /**
     * Gets with deleted.
     *
     * @param id the id
     * @return the with deleted
     */
    public BigDecimal getAppPrice(Long id) {
        return dao.getAppPrice(id);
    }

    public Long getByPackageName(Long marketId, String packageName) {
        if (StringUtils.isEmpty(packageName)) {
            return null;
        }
        return dao.getByPackageName(marketId, packageName);
    }

}
