<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.market.ScheduleMarketAppStatsDao">
    <sql id="col_without_id">
        <trim>
            `market_id`, `email`, `created_date`,
            `app_id`, `name`, `app_status`, `all_apk_status`, `os_type`, `latest_version_code`,`latest_version`,
            `all_versions`,
            `download_count`, `app_updated_time`
        </trim>
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into pax_app_stats(<include refid="col_without_id"/>)
        value (
        #{marketId}, #{email}, #{createdDate},
        #{appId}, #{name}, #{appStatus}, #{allApkStatus}, #{osType}, #{latestVersionCode}, #{latestVersion},
        #{allVersions},
        #{downloadCount}, #{appUpdatedTime}
        )
    </insert>

    <delete id="deleteByMarketId">
        DELETE
        FROM `pax_app_stats`
        WHERE market_id = #{marketId}
    </delete>
</mapper>