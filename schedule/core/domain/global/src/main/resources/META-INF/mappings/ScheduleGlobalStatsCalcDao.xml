<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.market.ScheduleGlobalStatsCalcDao">

    <select id="calcAppsCount" resultType="com.pax.market.domain.entity.global.market.MarketSummaryStats">
        SELECT app.market_id as marketId, count(DISTINCT (app.id)) as `count`
        FROM PAX_APP app
         LEFT JOIN PAX_APK apk ON apk.app_id = app.id
        WHERE app.market_id = #{marketId}
          AND(apk.STATUS = 'O' OR apk.STATUS = 'U')
          AND app.`status` != 'D'
          AND app.del_flag = 0
          AND apk.del_flag = 0
    </select>

    <select id="calcOnlineAppCount" resultType="com.pax.market.domain.entity.global.market.MarketSummaryStats">
        SELECT app.market_id as marketId, count(DISTINCT (app.id)) as `count`
         FROM PAX_APP app
         LEFT JOIN PAX_APK apk ON apk.app_id = app.id
        WHERE app.market_id = #{marketId}
          AND apk.STATUS = 'O'
          AND app.`status` = 'A'
          AND app.del_flag = 0
          AND apk.del_flag = 0
    </select>

    <select id="calcDevelopersCount" resultType="com.pax.market.domain.entity.global.market.MarketSummaryStats">
        SELECT dev.market_id AS `marketId`, COUNT(1) AS `count`
        FROM PAX_DEVELOPER dev
        WHERE dev.market_id = #{marketId}
        AND dev.del_flag = 0
        <if test="status != null and status !=''">
            AND dev.status = #{status}
        </if>
        <if test="time != null">
            AND dev.approve_date >= #{time}
        </if>
    </select>

    <select id="calcDeveloperStats" resultType="com.pax.market.domain.entity.global.market.MarketDeveloperStats">
        SELECT dev.market_id AS marketId,
        dev.email AS `email`,
        dev.dev_type AS `developerType`,
        dev.approve_date AS `registryTime`,
        sum(CASE WHEN app.id is NULL THEN 0 ELSE 1 END) AS appCount,
        sum(app.downloads) AS allAppDownloadCount
        FROM pax_developer dev
        JOIN pax_market market ON dev.market_id = market.id AND market.del_flag = 0
        LEFT JOIN pax_app app
        ON market.id = app.market_id AND dev.id = app.dev_id AND app.status != 'D' AND app.del_flag = 0
        WHERE dev.del_flag = 0
        <if test="status != null and status !=''">
            AND dev.status = #{status}
        </if>
        GROUP BY dev.market_id, dev.email, dev.dev_type, dev.approve_date
    </select>


    <select id="calcAppStats" resultType="com.pax.market.dto.market.MarketAppStatsInfo">
        SELECT app.id                   AS appId,
               app.name                 AS name,
               app.market_id            AS marketId,
               app.os_type              AS osType,
               dev.email                AS developerEmail,
               app.updated_date         AS updatedDate,
               app.downloads            AS downloads,
               MAX(apk.version_code)    AS latestVersionCode,
               COUNT(DISTINCT apk.id)   AS historyVersionNum,
               GROUP_CONCAT(apk.status) AS allApkStatus,
               app.status               AS appStatus
        FROM pax_app app
                 INNER JOIN pax_market market ON app.market_id = market.id AND market.del_flag = 0
                 INNER JOIN pax_developer dev ON app.dev_id = dev.id
                 LEFT JOIN pax_apk apk ON apk.app_id = app.id AND apk.del_flag = 0
        WHERE (apk.status = 'O' OR apk.status = 'U')
          AND app.status != 'D'
          AND app.del_flag = 0
        GROUP BY app.id, app.name, app.market_id, app.os_type, dev.email, app.updated_date, app.status
    </select>

    <select id="calcTotalTerminalsCount" resultType="com.pax.market.domain.entity.global.market.MarketSummaryStats">
        SELECT a.market_id AS marketId, COUNT(1) AS `count`
        FROM PAX_TERMINAL_REGISTRY a
        WHERE a.market_id = #{marketId}
        <if test="status != null and status != ''">
            AND a.status= #{status}
        </if>
    </select>
</mapper>