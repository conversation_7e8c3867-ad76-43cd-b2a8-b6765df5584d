<?xml version="1.0" encoding="UTF-8" ?>


<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.vas.cloudmsg.ScheduleCloudMsgCoreDao">
    <select id="getIdListByStatus" resultType="java.lang.Long">
        SELECT
        a.id
        FROM pax_3rd_app_msg a
        WHERE
        a.status IN
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </select>

    <select id="getScheduledMsgIdsNeedSend" resultType="java.lang.Long">
        SELECT a.id
        FROM pax_3rd_app_msg a
        WHERE a.status = 'C'
          AND a.push_type = 2
          AND effective_time &lt; #{effectiveTimeLessThan}
    </select>
    <select id="getBasicInfoByStatus" resultType="com.pax.market.domain.entity.global.vas.cloudmsg.CloudMsgBasicInfo">
        SELECT
        a.id AS `id`,
        a.terminal_num AS `terminalNum`,
        a.market_id_of_app AS `marketIdOfApp`,
        a.identifier AS `identifier`,
        a.msg_arrived_num AS `msgArrivedNum`,
        a.status AS `status`,
        a.expire_time AS `expireTime`,
        CASE WHEN serial_nos IS NULL THEN 0 ELSE 1 END AS fixedTerminal
        FROM `pax_3rd_app_msg` a
        WHERE
        a.status IN
        <foreach collection="statusList" item="status"
                 separator="," open="(" close=")">
            #{status}
        </foreach>
        AND a.del_flag = 0
        AND a.market_id_of_app IS NOT NULL
    </select>

    <update id="partialUpdate">
        UPDATE pax_3rd_app_msg SET
        <if test="terminalNum != null">terminal_num = #{terminalNum},</if>
        <if test="msgArrivedNum != null">msg_arrived_num = #{msgArrivedNum},</if>
        <if test="status != null">status = #{status},</if>
        <if test="terminateCode != null">terminate_code = #{terminateCode},</if>
        <if test="delFlag != null">del_flag = #{delFlag},</if>
        updated_date = #{updatedDate},
        updated_by = #{updatedBy.id}
        WHERE id = #{id}
    </update>

    <update id="batchUpdateStatus2Success">
        UPDATE pax_3rd_app_msg
        SET status = 'S'
        WHERE expire_time &lt; #{expireTimeLessThan}
          AND status != 'S'
          AND status != 'H'
          AND msg_arrived_num &gt; 0
    </update>

    <update id="batchUpdateStatus2Fail">
        UPDATE pax_3rd_app_msg
        SET status        = 'T',
            terminate_code=8
        WHERE expire_time &lt; #{expireTimeLessThan}
          AND status != 'T'
          AND status != 'H'
          AND (msg_arrived_num IS NULL OR msg_arrived_num = 0)
    </update>
</mapper>