<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.user.ScheduleUserDao">
    <sql id="userColumns">
        a.id AS "id",
		a.login_name AS "loginName",
		a.password AS "password",
		a.name AS "name",
		a.sex AS "sex",
		a.first_name AS "firstName",
		a.last_name AS "lastName",
		a.birthday AS "birthday",
		a.zipcode AS "zipcode",
		a.photo AS "photo",
		a.email AS "email",
		a.login_ip AS "loginIp",
		a.last_login_date AS "lastLoginDate",
		a.login_date AS "loginDate",
		a.remarks AS "remarks",
		a.status AS "status",
		a.send_usage_data AS "sendUsageData",
		a.super_admin AS "superAdmin",
		a.register_date AS "registerDate",
		a.reset_password_date AS "resetPasswordDate",
		a.reset_email_date AS "resetEmailDate",
        a.activate_date AS "activateDate",
        a.admin_activate_date AS "adminActivateDate",
		a.login_tried_times AS "loginTriedTimes",
		a.locked_time AS "lockedTime",
		a.expired_time AS "expiredTime",
		a.passwd_change_time AS "passwdChangeTime",
		a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate",
		a.del_flag AS "delFlag"
    </sql>

    <select id="get" resultType="com.pax.market.domain.entity.global.user.User">
        SELECT
        <include refid="userColumns"/>
        FROM PAX_USER a
        WHERE a.id = #{id}
        AND a.del_flag = 0
    </select>

    <select id="findUserListForAuditIncludeDeleted" resultType="com.pax.market.domain.entity.global.user.User">
        SELECT
        a.id AS "id",
        a.login_name AS "loginName",
        a.email AS "email",
        a.name AS "name",
        a.del_flag AS "delFlag"
        FROM PAX_USER a
        WHERE a.id IN
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="searchUsers" resultType="com.pax.market.domain.entity.global.user.User">
        SELECT
        <include refid="userColumns"/>
        FROM PAX_USER a
        WHERE a.del_flag = 0
        <if test="userIds !=null">
            AND a.id IN
            <foreach collection="userIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="loginNames !=null">
            AND a.login_name IN
            <foreach collection="loginNames" item="loginName" open="(" separator="," close=")">
                #{loginName}
            </foreach>
        </if>
        <if test="loginName != null and loginName != ''">
            AND a.login_name = #{loginName}
        </if>
        <if test="status != null and status != ''">
            AND a.status = #{status}
        </if>
        <if test="name != null and name != ''">
            <choose>
                <when test="exactNameQuery">
                    AND a.name = #{name}
                </when>
                <otherwise>
                    AND (INSTR(a.name, #{name}) > 0 OR INSTR(a.login_name, #{name}) > 0)
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.name
            </otherwise>
        </choose>
        <if test="page != null and page.limit > 0">
            LIMIT ${page.limit}
        </if>
    </select>
</mapper>