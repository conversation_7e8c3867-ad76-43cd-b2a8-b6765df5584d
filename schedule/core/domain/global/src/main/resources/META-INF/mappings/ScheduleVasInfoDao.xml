<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.vas.ScheduleVasInfoDao">

    <sql id="vasInfoColumns">
        a.id AS "id",
		a.service_type AS "serviceType",
		a.service_name AS "serviceName",
		a.support_free_mode AS "supportFreeMode",
		a.enabled AS "enabled",
		a.suspended AS "suspended",
		a.support_oauth AS "supportOauth",
		a.provide_api AS "provideApi",
		a.service_api_base_url AS "apiBaseUrl",
		a.ping_url AS "pingUrl",
		a.trd_service AS "trdService",
		a.support_billing AS "supportBilling",
		a.show_in_sub_market AS "showInSubMarket",
		a.usage_type AS "usageType",
		a.default_status AS "defaultStatus",
		a.free_trial AS "freeTrial",
		a.free_till AS "freeTill",
		a.trial_time AS "trialTime",
		a.created_by AS "createdBy.id",
		a.created_date AS "createdDate",
		a.updated_by AS "updatedBy.id",
		a.updated_date AS "updatedDate"
    </sql>

    <select id="getByServiceType" resultType="com.pax.market.domain.entity.global.vas.VasInfo">
        SELECT
        <include refid="vasInfoColumns"/>
        FROM pax_vas_service a
        <where>
            <if test="serviceType != null and serviceType != ''">
                a.service_type=#{serviceType}
            </if>
        </where>
    </select>

    <select id="findServiceTypes" resultType="java.lang.String">
        SELECT service_type FROM pax_vas_service
    </select>


</mapper>