<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.app.ScheduleApkDao">

    <select id="get" resultType="com.pax.market.domain.entity.global.app.Apk">
        SELECT
        <include refid="getApkColumns"/>
        FROM PAX_APK apk
        <include refid="apkJoins"/>
        WHERE apk.id = #{id}
        LIMIT 1
    </select>

    <select id="getApkVersionNameByAppIdAndVersionCode" resultType="java.lang.String">
        SELECT
        apk.version_name AS "versionName"
        FROM PAX_APK apk
        WHERE
        apk.del_flag = 0
        AND apk.app_id = #{appId}
        AND apk.version_code = #{versionCode}
        LIMIT 1
    </select>

    <sql id="getApkColumns">
        apk.id AS "id",
		apk.version_name AS "versionName",
		app.package_name AS "packageName",
		apkDetail.app_name AS "apkDetail.appName"
    </sql>

    <sql id="apkJoins">
        JOIN PAX_APK_DETAIL apkDetail ON apk.id = apkDetail.apk_id
		JOIN PAX_APP app ON app.id = apk.app_id
    </sql>
    <select id="getLatestOnlineOfflineApkIdByAppId" resultType="Long">
        SELECT
            apk.id AS "id"
        FROM PAX_APK apk
        JOIN PAX_APP app ON app.id = apk.app_id
        WHERE app.id = #{appId}
          AND apk.del_flag = 0
          AND (apk.status = '${@com.pax.market.constants.AppStatus@ONLINE}' OR apk.status = '${@com.pax.market.constants.AppStatus@UNAVAILABLE}')
        ORDER BY apk.version_code DESC
        LIMIT 1
    </select>

    <select id="findApkModelIdList" resultType="java.lang.Long">
        SELECT
            apkModel.model_id
        FROM PAX_APK_MODEL apkModel
        WHERE apkModel.apk_id = #{apkId}
    </select>

    <select id="findFileIdForDelete" resultType="com.paxstore.schedule.global.domain.entity.FileForDelete">
        SELECT
        file.id AS "id",
        f.id AS "referenceId",
        file.file_id AS "fileId"
        FROM pax_apk_file file
        JOIN pax_apk f ON file.apk_id = f.id
        JOIN pax_app a ON a.id = f.app_id
        WHERE file.file_id IS NOT NULL
        AND  ((f.del_flag = 1 AND f.updated_date &lt; #{beginDate}) OR (a.del_flag = 1 AND a.updated_date &lt; #{beginDate}))
        <if test="lastId != null and lastId != ''">
            AND file.id > #{lastId}
        </if>
        ORDER BY file.id
        LIMIT 10000
    </select>
    
    <update id="updateApkFileId">
        UPDATE pax_apk_file
        SET file_id = null
        WHERE id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="findApkPatchFileIdToDelete" resultType="java.lang.String">
        SELECT patch_url
        FROM pax_apk_patch
        WHERE new_apk_id IN
        <foreach collection="apkIds" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>