<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.global.domain.dao.vas.emm.ScheduleEmmZteRecordDao">

    <sql id="fieldMap">
        a.id AS id,
        a.market_id AS marketId,
        a.status AS status
    </sql>

    <update id="updateSnapshotFileid">
        UPDATE PAX_EMM_ZTE_RECORD a
        <set>
            <if test="snapshotFileId != null and snapshotFileId != ''">
                a.snapshot_fileid = #{snapshotFileId},
            </if>
            <if test="globalSnapshotFileId != null and globalSnapshotFileId != ''">
                a.global_snapshot_fileid = #{globalSnapshotFileId},
            </if>
        </set>
        WHERE a.id = #{id}
    </update>

    <select id="findExpiredList" resultType="com.pax.market.domain.entity.global.emm.EmmZteRecord">
        SELECT
        <include refid="fieldMap"/>
        FROM PAX_EMM_ZTE_RECORD a
        WHERE
        del_flag = 0 and snapshot_fileid IS NULL and global_snapshot_fileid IS NULL
        <if test="statuses != null and statuses.size > 0">
            AND status IN
            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="aMonthAgo != null">
            AND updated_date &lt; #{aMonthAgo}
        </if>
    </select>

</mapper>