package com.paxstore.schedule.market.domain.dao.terminal;


import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * The interface Schedule terminal action dao.
 */
@MyBatisDao
public interface ScheduleTerminalActionDao extends CrudDao<TerminalAction> {

    /**
     * Find init push tasks list.
     *
     * @return the list
     */
    List<TerminalAction> findInitGroupParamTasks();

    /**
     * Find init terminal rki reference ids set.
     *
     * @return the set
     */
    List<TerminalAction> findInitGroupRkiPushTasks();

    /**
     * Find init terminal rki push tasks list.
     *
     * @return the list
     */
    List<TerminalAction> findInitTerminalRkiPushTasks(@Param("marketId") Long marketId);

    /**
     * Find for convert param variables.
     *
     * @param handler the handler
     */
    void findForConvertParamVariables(ResultHandler<TerminalAction> handler);

    /**
     * Find limited push tasks list.
     *
     * @return the list
     */
    List<TerminalAction> findLimitedPushTasks(Long marketId);
    /**
     * Find pending push tasks list.
     *
     * @return the list
     */
    List<TerminalAction> findPendingPushTasks();
}
