package com.paxstore.schedule.market.domain.service.sandbox;

import com.pax.market.domain.entity.market.sandbox.SandboxTerminalApk;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.mq.contract.migration.SandboxTerminalApkParamMigrateMessage;
import com.paxstore.schedule.market.domain.dao.sandbox.ScheduleSandboxTerminalApkDao;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ScheduleSandboxTerminalApkService extends CrudService<ScheduleSandboxTerminalApkDao, SandboxTerminalApk> {

    /**
     * Find all param task ids list.
     *
     * @return the list
     */
    public List<Long> findPushTaskIdsForUpdateSha256() {
        final List<Long> list = new ArrayList<>();
        dao.findPushTaskIdsForUpdateSha256(resultContext -> list.add(resultContext.getResultObject()));
        return list;
    }

    public Long getMaxApkParamTemplateId() {
        return dao.getMaxApkParamTemplateId();
    }

    public List<SandboxTerminalApkParamMigrateMessage> findAllPushApkParamIds(Long startId, Long endId) {
        return dao.findAllPushApkParamIds(startId, endId);
    }
}
