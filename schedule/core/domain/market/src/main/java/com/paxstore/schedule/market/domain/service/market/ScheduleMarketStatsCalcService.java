package com.paxstore.schedule.market.domain.service.market;

import com.pax.market.constants.ResellerStatus;
import com.pax.market.constants.TerminalStatus;
import com.pax.market.domain.constants.MarketSummaryStatsCategory;
import com.pax.market.domain.entity.global.market.MarketSummaryStats;
import com.pax.market.framework.common.service.CrudService;
import com.paxstore.schedule.market.domain.dao.market.ScheduleMarketStatsCalcDao;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class ScheduleMarketStatsCalcService extends CrudService<ScheduleMarketStatsCalcDao, MarketSummaryStats> {
    public MarketSummaryStats calcTotalResellersCount(Long marketId) {
        MarketSummaryStats info = dao.calcResellersCount(marketId, null);
        return fillCategoryAndCreateTimeInfo(info, MarketSummaryStatsCategory.TOTAL_RESELLERS);
    }

    public MarketSummaryStats calcActiveResellersCount(Long marketId) {
        MarketSummaryStats info = dao.calcResellersCount(marketId, ResellerStatus.ACTIVE);
        return fillCategoryAndCreateTimeInfo(info, MarketSummaryStatsCategory.ACTIVE_RESELLERS);
    }

    public MarketSummaryStats calcTotalMerchantsCount(Long marketId) {
        MarketSummaryStats info = dao.calcMerchantsCount(marketId);
        return fillCategoryAndCreateTimeInfo(info, MarketSummaryStatsCategory.TOTAL_MERCHANTS);
    }

    public MarketSummaryStats calcActiveMerchantsCount(Long marketId) {
        MarketSummaryStats info = dao.calcActiveMerchantsCount(marketId);
        return fillCategoryAndCreateTimeInfo(info, MarketSummaryStatsCategory.ACTIVE_MERCHANTS);
    }

    public MarketSummaryStats calcOnlineTerminalsCount(Long marketId) {
        MarketSummaryStats info = dao.calcOnlineTerminalsCount(marketId);
        return fillCategoryAndCreateTimeInfo(info, MarketSummaryStatsCategory.ONLINE_TERMINALS);
    }

    private MarketSummaryStats fillCategoryAndCreateTimeInfo(MarketSummaryStats info, MarketSummaryStatsCategory category) {
        info.setCategory(category.code);
        info.setCreatedDate(new Date());
        return info;
    }
}
