package com.paxstore.schedule.market.domain.dao.organization;

import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ScheduleMerchantDao extends CrudDao<Merchant> {

    @PublishEntityChangedEvent(type = PublishEntityChangedEvent.EventType.MERCHANT_CHANGED)
    void updateStatus(Merchant merchant);

    /**
     * Get merchant
     *
     * @param id the id
     * @return merchant include deleted
     */
    Merchant getIncludeDeleted(Long id);

    /**
     * Gets merchants by reseller ids.
     *
     * @param resellerIds the reseller ids
     * @param activeOnly  the active only
     * @return the merchants by reseller ids
     */
    List<Merchant> getMerchantsByResellerIds(@Param("resellerIds") List<Long> resellerIds, @Param("activeOnly") boolean activeOnly);
}
