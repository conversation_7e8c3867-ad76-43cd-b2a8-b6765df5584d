package com.paxstore.schedule.market.domain.dao.market;

import com.pax.market.domain.entity.global.market.MarketSummaryStats;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;

@MyBatisDao
public interface ScheduleMarketStatsCalcDao extends CrudDao<MarketSummaryStats> {

    /**
     * 获得代理商
     *
     * @implNote need to set category
     */
    MarketSummaryStats calcResellersCount(@Param("marketId") Long marketId, @Param("status") String status);

    MarketSummaryStats calcMerchantsCount(@Param("marketId") Long marketId);

    MarketSummaryStats calcActiveMerchantsCount(@Param("marketId") Long marketId);

    MarketSummaryStats calcOnlineTerminalsCount(@Param("marketId") Long marketId);

}
