/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.paxstore.schedule.market.domain.dao.pushtask;

import com.pax.market.domain.entity.market.pushtask.TerminalGroupUninstallApk;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;

/**
 * The interface Terminal group uninstallApk dao.
 */
@MyBatisDao
public interface ScheduleTerminalGroupUninstallApkDao extends BasePushTaskDao<TerminalGroupUninstallApk> {

}
