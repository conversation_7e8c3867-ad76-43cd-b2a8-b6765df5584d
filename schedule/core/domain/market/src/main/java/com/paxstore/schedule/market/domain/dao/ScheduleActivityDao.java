package com.paxstore.schedule.market.domain.dao;

import com.pax.market.domain.entity.market.Activity;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@MyBatisDao
public interface ScheduleActivityDao extends CrudDao<Activity> {
    /**
     * Gets without data scope filter.
     *
     * @param activity the activity
     * @return the without data scope filter
     */
    Activity getWithoutDataScopeFilter(Activity activity);

    List<Activity> getByUpdatedDate(@Param("updatedDate") LocalDateTime updatedDate, @Param("startIndex") int startIndex, @Param("limit") int limit);

    void physicalBatchDelete(@Param("ids") Collection ids);
}
