package com.paxstore.schedule.market.domain.dao.terminal;

import com.pax.market.domain.entity.market.terminal.TerminalAction;
import com.pax.market.framework.common.persistence.CrudDao;
import com.pax.market.framework.common.persistence.annotation.MyBatisDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

@MyBatisDao
public interface ScheduleTerminalActionHistoryDao extends CrudDao<TerminalAction> {

    /**
     * Find partition list list.
     *
     * @return the list
     */
    List<String> findPartitionList();

    /**
     * Add partition.
     *
     * @param partitionName  the partition name
     * @param partitionValue the partition value
     */
    void addPartition(@Param("partitionName") String partitionName, @Param("partitionValue") int partitionValue);

    /**
     * Gets max id.
     *
     * @return the max id
     */
    Long getTerminalActionHistoryBackupMaxId();


    /**
     * Find backup terminal action history messages.
     *
     * @param startId the start id
     * @param endId   the end id
     * @param handler the handler
     */
    void findBackupTerminalActionHistoryList(Long startId, Long endId, ResultHandler<TerminalAction> handler);

    Integer getActionHistoryIdsCount(@Param("actionType") int actionType,
                                     @Param("referenceId") Long referenceId,
                                     @Param("status") int status,
                                     @Param("errorCode") int errorCode);

    /**
     * Update terminal action history status.
     *
     * @param id     the id
     * @param status the status
     */
    void updateTerminalActionHistoryStatus(@Param("id") Long id, @Param("status") int status);

    /**
     * Find action history ids.
     *
     * @param actionType  the action type
     * @param referenceId the reference id
     * @param status      the status
     * @param errorCode   the error code
     * @param limit       the limit
     */
    List<Long> findActionHistoryIds(@Param("actionType") int actionType,
                                    @Param("referenceId") Long referenceId,
                                    @Param("status") int status,
                                    @Param("errorCode") int errorCode,
                                    @Param("limit") int limit);
}
