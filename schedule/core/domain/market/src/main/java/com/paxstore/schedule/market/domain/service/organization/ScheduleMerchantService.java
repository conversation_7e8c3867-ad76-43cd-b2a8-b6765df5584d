package com.paxstore.schedule.market.domain.service.organization;

import com.pax.api.cache.CacheService;
import com.pax.market.constants.CacheNames;
import com.pax.market.domain.entity.market.organization.Merchant;
import com.pax.market.framework.common.service.CrudService;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import com.pax.support.dynamic.datasource.aspectj.MasterDs;
import com.paxstore.schedule.market.domain.dao.organization.ScheduleMerchantDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ScheduleMerchantService extends CrudService<ScheduleMerchantDao, Merchant> {

    /**
     * The constant MERCHANT_CACHE_ID_.
     */
    public static final String MERCHANT_CACHE_ID_ = "id_";
    private static final String MERCHANT_CACHE_NAME_ = "name_";



    private final CacheService cacheService;

    @MasterDs
    public void updateStatusWithoutCheck(Merchant merchant, String status) {
        merchant.setStatus(status);
        dao.updateStatus(merchant);
        clearMerchantCache(merchant);
    }


    /**
     * Clear merchant cache.
     *
     * @param merchant the merchant
     */
    public static void clearMerchantCache(Merchant merchant) {
        if (merchant == null) {
            return;
        }
        CacheService cacheService = SpringContextHolder.getApplicationContext().getBean(CacheService.class);
        List<String> removeKeys = new ArrayList<>();
        removeKeys.add(MERCHANT_CACHE_ID_ + merchant.getId());
        if (merchant.getReseller() != null && merchant.getReseller().getId() != null) {
            removeKeys.add(MERCHANT_CACHE_NAME_ + merchant.getReseller().getId() + "_" + StringUtils.upperCase(merchant.getName()));
        }
        cacheService.remove(CacheNames.MERCHANT_CACHE, removeKeys.toArray(new String[0]));
    }

    /**
     * Gets include deleted without data scope check.
     *
     * @param id the id
     * @return the include deleted without data scope check
     */
    public Merchant getIncludeDeletedWithoutDataScopeCheck(Long id) {
        if (LongUtils.isBlankOrNotPositive(id)) {
            return null;
        }
        Merchant result = (Merchant) cacheService.get(CacheNames.MERCHANT_CACHE, MERCHANT_CACHE_ID_ + id);
        if (result == null) {
            result = dao.getIncludeDeleted(id);
        }
        return result;
    }

    /**
     * Get merchants by reseller ids list.
     *
     * @param resellerIds the reseller ids
     * @param activeOnly  the active only
     * @return the list
     */
    public List<Merchant> getMerchantsByResellerIds(List<Long> resellerIds, boolean activeOnly) {
        if (CollectionUtils.isEmpty(resellerIds)) {
            return new ArrayList<>();
        } else {
            return dao.getMerchantsByResellerIds(resellerIds, activeOnly);
        }
    }

}
