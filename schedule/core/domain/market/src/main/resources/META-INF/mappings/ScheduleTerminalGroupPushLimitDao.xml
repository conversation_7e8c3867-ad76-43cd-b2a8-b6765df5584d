<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2019 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paxstore.schedule.market.domain.dao.pushtask.ScheduleTerminalGroupPushLimitDao">

    <select id="findGroupRecurringPushLimitIds" resultType="java.lang.Long">
        SELECT a.id
        FROM pax_tm_group_push_limit a
        JOIN pax_tm_group b ON a.group_id = b.id
        WHERE b.market_id = #{marketId}
        AND (a.type = '${@com.pax.market.constants.PushStrategyType@RECURRING_PUSH}' OR a.terminal_selection = '${@com.pax.market.constants.TerminalPushSelection@TARGETED}')
    </select>

</mapper>