package com.pax.market.schedule.domain.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class SkyhookAccessTime implements Serializable {
    @Serial
    private static final long serialVersionUID = 8096333105864888423L;

    private Long id;
    private Long terminalId;
    private String accessMonth;
    private Date syncTime;

}
