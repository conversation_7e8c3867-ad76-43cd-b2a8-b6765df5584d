package com.pax.market.schedule.domain.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class MonthlyPackageSettingForMigration implements Serializable {
    @Serial
    private static final long serialVersionUID = 8982419038401745351L;

    private Long id;
    private String billType;
    private BigDecimal packagePrice;
    private BigDecimal packageQuantity;
    private BigDecimal outOfPackagePrice;
    private Date updatedDate;
}
