/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.schedule.mq.contract;

import com.pax.support.mq.core.message.AbstractMessage;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>" rel="nofollow">suyunlong</a>
 * @date 2024/11/11
 */
@Getter
@Setter
public class ScheduleEmmZteDeviceRecordChangedMessage extends AbstractMessage {
    @Serial
    private static final long serialVersionUID = -115426652150386227L;

    private Long zteRecordId;

    private String zteRecordStatus;
}
