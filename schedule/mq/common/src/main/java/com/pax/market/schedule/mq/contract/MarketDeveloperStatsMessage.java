package com.pax.market.schedule.mq.contract;

import com.pax.market.dto.market.MarketDeveloperStatsInfo;
import com.pax.support.mq.core.message.AbstractMessage;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @since 9.1
 */
@Getter
@Setter
@NoArgsConstructor
public class MarketDeveloperStatsMessage extends AbstractMessage {
    @Serial
    private static final long serialVersionUID = -7642705605846261538L;
    private List<MarketDeveloperStatsInfo> infos;
}
