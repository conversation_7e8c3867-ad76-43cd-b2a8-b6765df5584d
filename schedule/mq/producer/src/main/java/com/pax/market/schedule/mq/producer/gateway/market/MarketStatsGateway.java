package com.pax.market.schedule.mq.producer.gateway.market;

import com.pax.market.schedule.mq.contract.MarketAppStatsMessage;
import com.pax.market.schedule.mq.contract.MarketDeveloperStatsMessage;
import com.pax.market.schedule.mq.contract.MarketStatsMessage;

/**
 * <AUTHOR> @since 9.1
 */
public interface MarketStatsGateway {

    void sendDeveloperStatsMessage(MarketDeveloperStatsMessage message);
    void sendAppStatsMessage(MarketAppStatsMessage message);

    void sendDashboardMessage(MarketStatsMessage message);

    void sendMarketSummaryMessage(MarketStatsMessage message);
}
