/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.schedule.mq.consumer.handler.vas.emm;

import com.pax.api.fs.SupportedFileTypes;
import com.pax.market.domain.entity.excel.EmmZteDeviceRecordForExport;
import com.pax.market.domain.entity.excel.EmmZteDeviceRecordGlobalForExport;
import com.pax.market.domain.entity.excel.EmmZteDeviceRecordMarketForExport;
import com.pax.market.domain.util.LocaleUtils;
import com.pax.market.framework.common.excel.ExportExcel;
import com.pax.market.framework.common.file.FileUploader;
import com.pax.market.framework.common.i18n.MessageUtils;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.RandomUtil;
import com.pax.market.framework.common.utils.StopWatch;
import com.pax.market.schedule.mq.consumer.handler.AbstractHandler;
import com.pax.market.schedule.mq.contract.ScheduleEmmZteDeviceRecordChangedMessage;
import com.paxstore.schedule.global.domain.service.vas.emm.ScheduleEmmZteRecordService;
import com.paxstore.schedule.market.domain.service.emm.ScheduleEmmZteDeviceRecordService;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>" rel="nofollow">suyunlong</a>
 * @date 2024/11/11
 */
@Component
@RequiredArgsConstructor
public class ScheduleEmmZteDeviceRecordHandler extends AbstractHandler<ScheduleEmmZteDeviceRecordChangedMessage> {

    private final ScheduleEmmZteDeviceRecordService zteDeviceRecordService;
    private final ScheduleEmmZteRecordService zteRecordService;

    @Override
    protected void handleInternal(ScheduleEmmZteDeviceRecordChangedMessage message) {
        StopWatch sw = new StopWatch("Process generate emm zte expired record devices file");
        sw.start();
        if( Objects.isNull(message)
                || LongUtils.isBlankOrNotPositive(message.getMarketId())
                || LongUtils.isBlankOrNotPositive(message.getZteRecordId())
                || StringUtils.isBlank(message.getZteRecordStatus())){
            logger.warn("[ScheduleEmmZteDeviceRecordHandler] handleInternal: ScheduleEmmZteDeviceRecordChangedMessage is not avaliable");
            return;
        }
        List<EmmZteDeviceRecordForExport> zteDeviceRecordsForExport = zteDeviceRecordService.findZteDeviceRecordsForExport(message.getZteRecordId(), message.getZteRecordStatus());
        String marketFileId = generateFile(zteDeviceRecordsForExport, EmmZteDeviceRecordMarketForExport.class);
        String globalFileId = generateFile(zteDeviceRecordsForExport, EmmZteDeviceRecordGlobalForExport.class);
        if(StringUtils.isNotBlank(marketFileId) && StringUtils.isNotBlank(globalFileId)){
            zteRecordService.updateSnapshotFileid(message.getZteRecordId(), globalFileId, marketFileId);
            zteDeviceRecordService.deleteListByZteRecordId(message.getZteRecordId());
        }
        sw.stop();
        if (sw.getTotalTimeMillis() > 30000L) {
            logger.warn("Process message {} spent {} milli seconds", message, sw.getTotalTimeMillis());
        }
    }

    private <T extends EmmZteDeviceRecordForExport> String generateFile(List<EmmZteDeviceRecordForExport> zteDeviceRecordsForExport, Class<T> clazz) {
        String fileId = "";
        String sheetTitle = MessageUtils.getLocaleMessage("title.emm.zte.device.record",LocaleUtils.getEnvLocale());
        try (ExportExcel emmZteDeviceRecordExcel = new ExportExcel(sheetTitle, sheetTitle, clazz, true, LocaleUtils.getEnvLocale());
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            emmZteDeviceRecordExcel.setDataList(zteDeviceRecordsForExport);
            emmZteDeviceRecordExcel.write(byteArrayOutputStream);
            fileId = FileUploader.uploadFile(
                    byteArrayOutputStream.toByteArray(),
                    RandomUtil.generateMixString(6) + System.currentTimeMillis(),
                    ".xlsx",
                    SupportedFileTypes.TEMP);
        } catch (Exception e){
            logger.error("[ScheduleEmmZteDeviceRecordHandler] generateFile: Error occurred when the file was generated.",e);
        }
        return fileId;
    }
}
