package com.pax.market.schedule.mq.consumer.handler.market;

import com.pax.market.domain.entity.global.market.MarketDashboardStats;
import com.pax.market.domain.util.SystemPropertyHelper;
import com.pax.market.framework.common.redis.RedisUtils;
import com.pax.market.schedule.mq.consumer.handler.AbstractHandler;
import com.pax.market.schedule.mq.contract.MarketStatsMessage;
import com.paxstore.schedule.global.domain.service.market.ScheduleMarketDashboardStatsService;
import com.paxstore.schedule.market.domain.service.dashboard.ScheduleDashboardStatsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class MarketDsStatsHandler extends AbstractHandler<MarketStatsMessage> {
    private final ScheduleMarketDashboardStatsService scheduleMarketDashboardStatsService;
    private final ScheduleDashboardStatsService scheduleDashboardStatsService;

    @Override
    protected void handleInternal(MarketStatsMessage message) {
        if (!SystemPropertyHelper.isMarketStatsJobEnabled()) {
            return;
        }
        String cacheKey = String.format("distributionLock:statsMarketDashboard:%d", message.getMarketId());
        RedisUtils.tryLock(cacheKey, String.format("Encounter error while doing dashboard stats for market %d", message.getMarketId()), () -> {
            List<MarketDashboardStats> infos = new ArrayList<>();
            infos.addAll(scheduleDashboardStatsService.genStats4StoreClientDistribution(message.getMarketId()));
            infos.addAll(scheduleDashboardStatsService.genStats4FirmwareDistribution(message.getMarketId()));
            infos.addAll(scheduleDashboardStatsService.genStats4TerminalModelDistribution(message.getMarketId()));
            infos.addAll(scheduleDashboardStatsService.genStats4TerminalOffline(message.getMarketId()));
            if (SystemPropertyHelper.isAllowGenStatsPUK()) {
                infos.addAll(scheduleDashboardStatsService.genStats4Puk(message.getMarketId()));
            }
            scheduleMarketDashboardStatsService.cleanStatsData(message.getMarketId());
            scheduleMarketDashboardStatsService.insertList(infos);
        });
    }
}
