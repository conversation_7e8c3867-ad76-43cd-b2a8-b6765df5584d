<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>p-market-schedule-mq</artifactId>
        <groupId>com.pax.market</groupId>
        <version>9.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>p-market-schedule-mq-consumer</artifactId>
    <name>PAX Market :: Schedule :: MQ :: Consumer</name>
    <dependencies>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-schedule-core-functional</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-schedule-mq-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-report-engine</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zolon.saas</groupId>
            <artifactId>vas-goinsight-api</artifactId>
        </dependency>
    </dependencies>


</project>