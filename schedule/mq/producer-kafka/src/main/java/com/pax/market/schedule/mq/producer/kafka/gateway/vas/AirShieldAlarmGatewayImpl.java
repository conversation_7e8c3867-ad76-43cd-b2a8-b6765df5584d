package com.pax.market.schedule.mq.producer.kafka.gateway.vas;

import com.pax.market.schedule.mq.contract.ScheduleAirShieldAlarmMessage;
import com.pax.market.schedule.mq.producer.gateway.vas.AirShieldAlarmGateway;
import com.pax.market.schedule.mq.producer.kafka.gateway.ScheduleKafkaGateWay;
import com.pax.market.schedule.mq.shared.kafka.ScheduleTopicNames;
import org.springframework.stereotype.Component;

@Component
public class AirShieldAlarmGatewayImpl extends ScheduleKafkaGateWay implements AirShieldAlarmGateway {
    @Override
    public void send(ScheduleAirShieldAlarmMessage message) {
        super.sendAsJSON(ScheduleTopicNames.T_VAS_AIRSHIELD_ALARM,message);
    }
}
