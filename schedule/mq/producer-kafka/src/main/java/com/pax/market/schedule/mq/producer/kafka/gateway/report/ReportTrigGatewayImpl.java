package com.pax.market.schedule.mq.producer.kafka.gateway.report;

import com.pax.market.schedule.mq.contract.ReportTrigMessage;
import com.pax.market.schedule.mq.producer.gateway.report.ReportTrigGateway;
import com.pax.market.schedule.mq.shared.kafka.ScheduleTopicNames;
import com.pax.support.mq.kafka.producer.gateway.AbstractKafkaGateway;
import org.springframework.stereotype.Component;

@Component
public class ReportTrigGatewayImpl extends AbstractKafkaGateway implements ReportTrigGateway {
    @Override
    public void trigReport(ReportTrigMessage message) {
        send(ScheduleTopicNames.T_SCHEDULE_REPORT, message);
    }
}
