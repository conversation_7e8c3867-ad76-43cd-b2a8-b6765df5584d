/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */


package com.pax.market.schedule.mq.producer.kafka.gateway.vas.emm;

import com.pax.market.schedule.mq.contract.ScheduleEmmZteDeviceRecordChangedMessage;
import com.pax.market.schedule.mq.producer.gateway.vas.emm.ScheduleEmmZteDeviceRecordChangedGateway;
import com.pax.market.schedule.mq.producer.kafka.gateway.ScheduleKafkaGateWay;
import com.pax.market.schedule.mq.shared.kafka.ScheduleTopicNames;
import org.springframework.stereotype.Component;

@Component
public class ScheduleEmmZteDeviceRecordChangedGatewayImpl extends ScheduleKafkaGateWay implements ScheduleEmmZteDeviceRecordChangedGateway {
    @Override
    public void send(ScheduleEmmZteDeviceRecordChangedMessage msg) {
        send(ScheduleTopicNames.T_EMM_ZTE_DEVICE_EXPIRED_RECORD_CLEAN, msg);
    }
}
