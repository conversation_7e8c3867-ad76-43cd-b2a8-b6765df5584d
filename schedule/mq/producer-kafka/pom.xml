<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>p-market-schedule-mq</artifactId>
        <groupId>com.pax.market</groupId>
        <version>9.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>p-market-schedule-mq-producer-kafka</artifactId>
    <name>PAX Market :: Schedule :: MQ :: Producer Kafka</name>
    <dependencies>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-schedule-mq-producer</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.pax.support.mq</groupId>
            <artifactId>pax-support-mq-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-schedule-mq-shared</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-dynamic-datasource</artifactId>
        </dependency>
    </dependencies>


</project>