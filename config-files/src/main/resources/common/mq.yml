#============================================#
#             MQ Configuration               #
#============================================#
# Available value: 'kafka', 'inmemory'
message:
  process:
    engine: kafka

kafka:
  broker:
    hosts: "${kafka-broker-hosts}"

  # NOTE: if single host configured for "kafka.broker.hosts", below 2 properties will be 1, see
  default:
    partitions-num: 2
    replication-factor: 3
    compression-type: "lz4"

  producer:
    retries: 0
    batch-size: 16384
    linger-ms: 1
    buffer-memory: 33554432
    acks: 0
    max-request-size: 15728640 # 15MB, The maximum size of a request in bytes

  customization:
    #注意：如果修改这边的配置，终端API那边发送消息的gateway配置也需要相应修改
    partitions:
      t_push_cmd: 12
      t_terminal_app_sync: 8
      t_terminal_config_sync: 8
      t_terminal_detail_sync: 8
      t_terminal_monitor_sync: 8
      t_terminal_protocol_sync: 8
      t_terminal_apk_param_sync: 8
      t_terminal_launcher_sync: 8
      t_terminal_apk_param_history: 8
      t_sync_terminal_checkup_data: 8
      t_internal_data_sync: 8
      t_build_audit_trail: 8
      t_save_api_audit: 6
      t_audit_trail: 6
      t_migration_auth_log: 8
      t_migration_audit_trail: 8
      t_terminal_action_update: 20
      t_apk_param_template_migration: 8
      t_traditional_terminal_action_update: 12
      t_after_app_download_action: 4
      t_terminal_client_download: 4
      t_google_loc_resolver: 8
      t_terminal_geofence_sync: 4
      t_terminal_geolocation_calibration: 4
      t_terminal_geo_alarm: 4
      t_terminal_location_refresh: 4
      t_push_event: 8
      t_apk_online: 4
      t_terminal_task: 20
      t_terminal_command: 8
      t_remove_terminal_task: 8
      t_market_mqtt: 2
      t_sync_terminal_devinfo: 4
      t_notification_request: 4
      t_notification_source_message: 4
      t_notification_console_message: 4
      t_notification_email_message: 4
      t_notification_mobile_message: 4
      t_validate_group_apk_param: 12
      t_validate_traditional_group_apk_param: 12
      t_push_pending_terminal_action: 12
      t_push_pending_traditional_terminal_action: 12
      t_push_profile_terminal: 12
      t_check_pending_terminal_action: 10
      t_terminal_history_info_sync: 14
      t_terminal_realtime_detail_sync: 8
      t_sync_biz_data: 12
      t_terminal_stock_sync: 2
      t_sync_terminal_info: 12
      t_collect_terminal_info_to_insight: 8
      t_collect_terminal_app_to_insight: 8
      t_send_dictionary_to_insight: 2
      t_create_pending_terminal_action: 12
      t_check_new_terminal_action: 12
      t_create_terminal_action: 8
      t_create_terminal_history_action: 20
      t_move_terminal_action: 8
      t_backup_terminal_action_history: 8
      t_resume_terminal_action: 8
      t_update_group_action_count: 8
      t_check_init_terminal_action: 8
      t_send_download_file_info_to_insight: 4
      t_reversal_terminal_rki_key: 4
      t_organization_changed: 2

      t_server_status_update: 2
      t_data_sync_event_in: 4
      t_data_sync_event_out: 4

      t_sync_storeclient_crashlog: 2

      t_cm_p: 2
      t_cm_sft: 2
      t_cm_snft: 4
      t_cm_ssm: 4
      t_sync_terminal_airshield_detect_data: 8
      t_sync_airshield_access_time: 8

      #the message sent to below topics need to be processed one by one, so set partition=1
      t_import_activity: 1
      t_reseller_app_sync: 1
      t_market_developer_stats: 1
      t_market_app_stats: 1
      
      #terminal service v2 consumed topics
      t_terminalv2_evict_cache: 3

      #airlink
      t_airlink_terminal_deduct: 2
      t_airlink_terminal_cancel_active: 2
      t_airlink_terminal_active_result: 4
      t_air_link_service_changed: 1
      t_air_link_overdue_resume: 1
      t_air_link_data_pool_changed: 1

    compression-enabled:
      t_save_api_audit: true
      t_migration_audit_trail: true
      t_audit_trail: true
      t_move_terminal_action: true
    max-message-bytes:
      t_save_api_audit: 5242880
      t_migration_audit_trail: 5242880
      t_audit_trail: 5242880
      t_move_terminal_action: 15728640

