#!/bin/sh

GC_LOG_FILE_NAME="$CONSOLE_LOG_PATH/paxstore-gc-migration-$NOW.log"
HEAP_DUMP_PATH="$CONSOLE_LOG_PATH/paxstore-migration-heapdump-$NOW.hprof"

JVM_OPTS=" \
      -server -Xms2048m -Xmx2048m -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8 \
      -XX:+IgnoreUnrecognizedVMOptions -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:${GC_LOG_FILE_NAME} \
      -XX:+PrintHeapAtGC -XX:+PrintTenuringDistribution -XX:+PrintGCApplicationStoppedTime -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 \
      -XX:GCLogFileSize=10M -XX:-UseBiasedLocking -XX:+UseTLAB -XX:+ResizeTLAB -XX:+PerfDisableSharedMem -XX:+UseCondCardMark \
      -XX:CMSWaitDuration=10000 -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled -XX:+CMSParallelInitialMarkEnabled \
      -XX:+CMSEdenChunksRecordAlways -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseCMSInitiatingOccupancyOnly \
      -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${HEAP_DUMP_PATH} \
      -Djava.io.tmpdir=${TMP_FOLDER_PATH}"

CONSOLE_LOG_FILE_NAME="paxstore-migration-console-$NOW.out"
APP_NAME="PAXSTORE-Migration-App"