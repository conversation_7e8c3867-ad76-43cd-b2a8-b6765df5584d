FROM openjdk:8u181-jre-alpine

ENV WK_PATH=/paxstore

RUN apk add --no-cache bash \
    && mkdir -p ${WK_PATH}

COPY pax-linktop-app.tar.gz ${WK_PATH}/

WORKDIR ${WK_PATH}

RUN tar -zxvf ./pax-linktop-app.tar.gz

ADD up.sh ${WK_PATH}/pax-linktop-app/bin/

RUN chmod +x ${WK_PATH}/pax-linktop-app/bin/*.sh

RUN rm -rf ${WK_PATH}/pax-linktop-app.tar.gz

EXPOSE 9080

ENTRYPOINT ["/paxstore/pax-linktop-app/bin/up.sh"]