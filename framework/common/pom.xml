<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ ********************************************************************************
  ~ COPYRIGHT
  ~               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or
  ~   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
  ~   or disclosed except in accordance with the terms in that agreement.
  ~
  ~      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
  ~ ********************************************************************************
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>p-market-framework</artifactId>
        <groupId>com.pax.market</groupId>
        <version>9.8.0-SNAPSHOT</version>
    </parent>

    <artifactId>p-market-framework-common</artifactId>
    <name>PAX Market :: Framework :: Common</name>

    <dependencies>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-fs-fastdfs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-fs-aws-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-cache-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-pubsub-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-eventbus-guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-monitoring</artifactId>
        </dependency>

        <!-- PAX market framework dependency begin -->
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-framework-utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-dto</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- PAX market framework dependency end -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
        </dependency>
        <!-- Spring framework dependency end -->

        <!-- upgrade springboot3 dependency begin -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>

        <!-- upgrade springboot3 dependency end -->

        <!-- bean validate begin -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <!-- bean validate end -->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- common utilities begin -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <!-- common utilities end  -->

        <!-- apk parser -->
        <dependency>
            <groupId>net.dongliu</groupId>
            <artifactId>apk-parser</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- poi office -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <!-- freemarker engine begin -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- freemarker engine end -->

        <!-- pax market - test framework -->
        <dependency>
            <groupId>com.pax.market</groupId>
            <artifactId>p-market-framework-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.android.tools.build</groupId>
            <artifactId>apksig</artifactId>
            <version>2.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${modelmapper.version}</version>
        </dependency>
        <!-- TEST end -->
        <dependency>
            <groupId>com.pax.support</groupId>
            <artifactId>pax-support-dynamic-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>