/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.framework.common.service;

import com.pax.market.constants.ApiCodes;
import com.pax.market.framework.common.exception.ServiceException;
import com.pax.market.framework.common.persistence.TreeDao;
import com.pax.market.framework.common.persistence.TreeEntity;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.Reflections;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service基类
 *
 * @param <D> the type parameter
 * @param <T> the type parameter
 * <AUTHOR>
 */
public abstract class TreeService<D extends TreeDao<T>, T extends TreeEntity<T>> extends CrudService<D, T> {

    @Transactional(readOnly = false)
    public void save(T entity) {
        T originalEntity = dao.get(entity);
        //如果是没有修改父节点，则不需要更新树的parentIds
        if (originalEntity != null && LongUtils.equals(originalEntity.getParentId(), entity.getParentId())) {
            super.save(entity);
        } else {
            @SuppressWarnings("unchecked")
            Class<T> entityClass = Reflections.getClassGenricType(getClass(), 1);

            // 如果没有设置父节点，则代表为跟节点，有则获取父节点实体
            if (LongUtils.isBlankOrNotPositive(entity.getParentId())) {
                entity.setParent(null);
            } else {
                T parent = this.get(entity.getParentId());
                if (parent == null) {
                    throw new ServiceException(ApiCodes.PARENT_ENTITY_NOT_FOUND);
                }

                if (LongUtils.equals(entity.getId(), entity.getParentId())) {
                    throw new ServiceException(ApiCodes.PARENT_ENTITY_INVALID);
                }

                entity.setParent(parent);
            }

            // 获取修改前的parentIds，用于更新子节点的parentIds
            String oldParentIds = entity.getParentIds();
            boolean isNewRecord = entity.getIsNewRecord();

            // 设置新的父节点串
            if (entity.getParent() != null) {
                entity.setParentIds(entity.getParent().getParentIds() + entity.getParent().getId() + TreeEntity.PARENT_IDS_DELEMETER);
            } else {
                entity.setParentIds(TreeEntity.PARENT_IDS_DELEMETER);
            }

            // 保存或更新实体
            super.save(entity);
            dao.updateParentIds(entity);

            if (!isNewRecord) {
                // 更新子节点 parentIds
                T o;
                try {
                    o = entityClass.newInstance();
                } catch (Exception e) { //NOSONAR
                    throw new ServiceException(ApiCodes.UNKNOWN);
                }
                o.setParentIds(oldParentIds + entity.getId() + TreeEntity.PARENT_IDS_DELEMETER + "%");
                List<T> list = dao.findByParentIdsLike(o);
                for (T e : list) {
                    e.setParentIds(e.getParentIds().replace(oldParentIds, entity.getParentIds()));
                    preUpdateChild(entity, e);
                    dao.updateParentIds(e);
                }
            }
        }
    }

    /**
     * 预留接口，用户更新子节前调用
     *
     * @param entity      the entity
     * @param childEntity the child entity
     */
    protected void preUpdateChild(T entity, T childEntity) {

    }

}
