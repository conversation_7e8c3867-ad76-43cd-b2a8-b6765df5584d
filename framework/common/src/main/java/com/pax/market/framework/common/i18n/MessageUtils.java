/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.framework.common.i18n;

import com.pax.market.constants.ApiCodes;
import com.pax.market.constants.SystemConstants;
import com.pax.market.constants.TerminalActionStatus;
import com.pax.market.constants.TerminalActionType;
import com.pax.market.framework.common.utils.StringUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import org.apache.commons.lang3.LocaleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;

import java.util.Locale;

/**
 * The type Message utils.
 */
public class MessageUtils {

    /**
     * The constant logger.
     */
    protected static Logger logger = LoggerFactory.getLogger(MessageUtils.class);

    private MessageUtils() {
    }

    private static MessageSource getMessageSource() {
        return SpringContextHolder.getBean("messageSource", MessageSource.class);
    }

    public static RequestLocaleResolver getRequestLocaleResolver() {
        try {
            return SpringContextHolder.getBean(RequestLocaleResolver.class);
        } catch (Exception e) {
            return new DefaultRequestLocaleResolver(LocaleUtils.toLocale(SystemConstants.DEFAULT_LOCALE));
        }
    }

    /**
     * Gets error message.
     *
     * @param resultCode the result code
     * @param locale     the locale
     * @param args       the args
     * @return the error message
     */
    public static String getErrorMessage(int resultCode, String locale, Object... args) {
        if (locale == null) {
            locale = SystemConstants.DEFAULT_LOCALE;
        }

        String errorMessage = getLocaleMessage(String.valueOf(resultCode), LocaleUtils.toLocale(locale), args);
        if (StringUtils.isBlank(errorMessage)) {
            errorMessage = getLocaleMessage(String.valueOf(ApiCodes.UNKNOWN), LocaleUtils.toLocale(locale), args);
        }

        return errorMessage;
    }

    /**
     * Gets locale message.
     *
     * @param code the code
     * @param args the args
     * @return the locale message
     */
    public static String getLocaleMessage(String code, Object... args) {
        return getLocaleMessage(code, getRequestLocaleResolver().get(), args);
    }

    /**
     * Gets locale message.
     *
     * @param code   the code
     * @param locale the locale
     * @param args   the args
     * @return the locale message
     */
    public static String getLocaleMessage(String code, Locale locale, Object... args) {
        if (StringUtils.isBlank(code)) {
            return code;
        }
        try {
            return getMessageSource().getMessage(code, args, locale == null ? getRequestLocaleResolver().get() : locale);
        } catch (Exception ex) {
            logger.warn("Error when getLocaleMessage for code: {}", code);
            return code;
        }
    }

    /**
     * Gets message.
     *
     * @param code   the code
     * @param locale the locale
     * @param args   the args
     * @return the message
     */
    public static String getMessage(String code, Locale locale, Object... args) {
        if (StringUtils.isBlank(code)) {
            return code;
        }
        try {
            return getMessageSource().getMessage(code, args, locale);
        } catch (Exception ex) {
            logger.warn("Error when getMessage for code: {}, locale: {}", code, locale);
            return code;
        }
    }

    /**
     * Gets english message.
     *
     * @param code the code
     * @param args the args
     * @return the english message
     */
    public static String getEnglishMessage(String code, Object... args) {
        try {
            return getMessageSource().getMessage(code, args, Locale.ENGLISH);
        } catch (Exception ex) {
            logger.warn("Error when getEnglishMessage for code: {}", code);
            return code;
        }
    }

    /**
     * Gets terminal action error message.
     *
     * @param errorCode the error code
     * @return the terminal action error message
     */
    public static String getTerminalActionErrorMessage(Integer errorCode, Locale locale) {
        if (errorCode == null || errorCode == 0) {
            return null;
        }
        return getLocaleMessage(String.format("terminal.action.error.%s", errorCode), locale);
    }

    /**
     * Gets terminal action error message.
     *
     * @param errorCode the error code
     * @return the terminal action error message
     */
    public static String getTerminalActionErrorMessage(Integer errorCode) {
        if (errorCode == null || errorCode == 0) {
            return null;
        }
        return getLocaleMessage(String.format("terminal.action.error.%s", errorCode));
    }

    /**
     * Gets push terminal message.
     *
     * @param code the code
     * @return the push terminal message
     */
    public static String getPushTerminalMessage(Integer code) {
        if (code == null || code < -1) {
            return null;
        }
        return getLocaleMessage(String.format("push.history.cmd.%s", code));
    }

    /**
     * Gets push cloud msg message.
     *
     * @param code the code
     * @return the push cloud message
     */
    public static String getPushCloudMsgMessage(Integer code) {
        if (code == null || code < -1) {
            return null;
        }
        return getLocaleMessage(String.format("push.cloud.msg.%s", code));
    }

    /**
     * Gets terminal action status message.
     *
     * @param status the status
     * @return the terminal action status message
     */
    public static String getTerminalActionStatusMessage(Integer status) {
       return getTerminalActionStatusMessage(status, null, getRequestLocaleResolver().get());
    }

    /**
     * Gets terminal action status message.
     *
     * @param status the status
     * @return the terminal action status message
     */
    public static String getTerminalActionStatusMessage(Integer status, Integer actionType, Locale locale) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case TerminalActionStatus.LIMITED, TerminalActionStatus.INIT -> {
                return ""; //导出LIMIT, INIT不显示状态，和页面一致
            }
            case TerminalActionStatus.PENDING, TerminalActionStatus.WAITING -> {
                String messageKey = (actionType != null && TerminalActionType.GROUP_UNINSTALL_APP == actionType) ?
                        "title.terminal.uninstall.action.status.pending" :
                        "title.terminal.action.status.pending";
                return getLocaleMessage(messageKey, locale);
            }
            case TerminalActionStatus.SUCCEED -> {
                return getLocaleMessage("title.terminal.action.status.succeed", locale);
            }
            case TerminalActionStatus.FAILED -> {
                return getLocaleMessage("title.terminal.action.status.failed", locale);
            }
            case TerminalActionStatus.FILTERED -> {
                return getLocaleMessage("title.terminal.action.status.filtered", locale);
            }
        }
        return "";
    }
}
