/**
* COPYRIGHT       
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION      
 *   This software is supplied under the terms of a license agreement or       
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied      
 *   or disclosed except in accordance with the terms in that agreement.
 *. All Rights Reserved.
 */
package com.pax.market.framework.common.persistence.dialect.db;

import com.pax.market.framework.common.persistence.dialect.Dialect;

/**
 * Sybase数据库分页方言实现。
 * 还未实现
 *
 * <AUTHOR>
 * @version 1.0 2010-10-10 下午12:31
 * @since JDK 1.5
 */
public class SybaseDialect implements Dialect {

    public boolean supportsLimit() {
        return false;
    }


    @Override
    public String getLimitString(String sql, int offset, int limit) {
        return null;
    }

    /**
     * 将sql变成分页sql语句,提供将offset及limit使用占位符号(placeholder)替换.
     * <pre>
     * 如mysql
     * dialect.getLimitString("select * from user", 12, ":offset",0,":limit") 将返回
     * select * from user limit :offset,:limit
     * </pre>
     *
     * @param sql               实际SQL语句
     * @param offset            分页开始纪录条数
     * @param offsetPlaceholder 分页开始纪录条数－占位符号
     * @param limit             分页每页显示纪录条数
     * @param limitPlaceholder  分页纪录条数占位符号
     * @return 包含占位符的分页sql limit string
     */
    public String getLimitString(String sql, int offset, String offsetPlaceholder, int limit, String limitPlaceholder) {
        throw new UnsupportedOperationException("paged queries not supported");
    }

}
