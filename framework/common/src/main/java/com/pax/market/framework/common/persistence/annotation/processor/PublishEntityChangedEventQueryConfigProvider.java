package com.pax.market.framework.common.persistence.annotation.processor;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.util.Assert;

import com.pax.market.framework.common.persistence.BaseEntity;
import com.pax.market.framework.common.persistence.annotation.PublishEntityChangedEvent;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PublishEntityChangedEventQueryConfigProvider {
	
	private static final PublishEntityChangedEventQueryConfigProvider instance = new PublishEntityChangedEventQueryConfigProvider();
	private final Set<PublishEntityChangedEventAnnotationInfo> configs = new HashSet<>();
	
	public static final PublishEntityChangedEventQueryConfigProvider getInstance() {
		return instance;
	}
	
	public void addIfAvailable(Class<?> type) {
		Assert.notNull(type, "missing required param");
		String mapperName = type.getName();
		for (Method method : type.getMethods()) {
			if(method.isAnnotationPresent(PublishEntityChangedEvent.class)) {
 				boolean validParam = method.getParameterCount() == 1
					&& (BaseEntity.class.isAssignableFrom(method.getParameterTypes()[0])
					|| IdAware.class.isAssignableFrom(method.getParameterTypes()[0])
					|| method.getParameterTypes()[0].equals(Long.class));
 				//TODO consider to improve this to support the query method with >1 parameters
 				Assert.isTrue(validParam,
					String.format("Query (%s.%s) must define 1 parameter (extend BaseEntity, implement IdAware or Long type value) if @PublishEntityChangedEvent present",
							mapperName, method.getName()));
				log.debug("found @PublishEntityChangedEvent query, mapperName: {}, method: {}", mapperName, method.getName());
				PublishEntityChangedEventQueryConfigProvider.getInstance().add(buildInfo(method, mapperName));
			}
		}
	}
	
	private PublishEntityChangedEventAnnotationInfo buildInfo(Method method, String mapperName) {
		PublishEntityChangedEvent annotationObj = method.getAnnotation(PublishEntityChangedEvent.class);
		return new PublishEntityChangedEventAnnotationInfo(
				mapperName, 
				method.getName(), 
				annotationObj.type(), 
				annotationObj.idExpr(),
				annotationObj.terminalSerialNoExpr(),
				annotationObj.terminalTidExpr());
	}
	
	public void add(PublishEntityChangedEventAnnotationInfo info) {
		Assert.notNull(info, "missing required param");
		Assert.hasText(info.getMapperName(), "mapperName is required");
		Assert.hasText(info.getQueryName(), "queryName is required");
		Assert.notNull(info.getEventType(), "EventType is required");
		Assert.hasText(info.getIdExpr(), "idExpr is required");
		configs.add(info);
	}
	
	public boolean isRequired(String mappedStatementId) {
		return isNotBlank(mappedStatementId) && configs.stream().anyMatch(cfg -> isMatched(mappedStatementId, cfg));
	}
	
	public List<PublishEntityChangedEventAnnotationInfo> get(String mappedStatementId) {
		return isNotBlank(mappedStatementId) 
				? configs.stream().filter(cfg -> isMatched(mappedStatementId, cfg)).collect(Collectors.toList())
				: Collections.emptyList();		
	}

	private boolean isMatched(String mappedStatementId, PublishEntityChangedEventAnnotationInfo cfg) {
		return String.format("%s.%s", cfg.getMapperName(), cfg.getQueryName()).equals(mappedStatementId);
	}
	
}
