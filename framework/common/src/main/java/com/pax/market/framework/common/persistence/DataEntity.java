/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.framework.common.persistence;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pax.market.dto.UserInfo;
import com.pax.market.framework.common.utils.*;
import org.hibernate.validator.constraints.Length;

import jakarta.xml.bind.annotation.XmlTransient;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * 数据Entity类
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
public class DataEntity<T extends Serializable> extends BaseEntity<T> {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * The Market id.
     */
    protected Long marketId;
    /**
     * The Remarks.
     */
    protected String remarks;
    /**
     * The Created by.
     */
    protected UserInfo createdBy;
    /**
     * The Created date.
     */
    protected Date createdDate;
    /**
     * The Updated by.
     */
    protected UserInfo updatedBy;
    /**
     * The Updated date.
     */
    protected Date updatedDate;
    /**
     * The Submit date.
     */
    protected Date submitDate;
    /**
     * The Updated date.
     */
    protected Date approveDate;
    /**
     * The Del flag.
     */
    protected String delFlag;    // 删除标记（0：正常；1：删除；2：审核）
    private Integer releaseVersion;
    private Integer reversion;
    private Long userId;

    private LinkedHashMap<String, String> entityAttributeValues;

    /**
     * Instantiates a new Data entity.
     */
    public DataEntity() {
        super();
        this.delFlag = DEL_FLAG_NORMAL;
    }

    /**
     * Instantiates a new Data entity.
     *
     * @param id the id
     */
    public DataEntity(Long id) {
        super(id);
    }

    /**
     * Gets remarks.
     *
     * @return the remarks
     */
    @Length(min = 0, max = 255)
    public String getRemarks() {
        return remarks;
    }

    /**
     * Sets remarks.
     *
     * @param remarks the remarks
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * Gets created by.
     *
     * @return the created by
     */
    public UserInfo getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets created by.
     *
     * @param createdBy the created by
     */
    public void setCreatedBy(UserInfo createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * Gets created date.
     *
     * @return the created date
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * Sets created date.
     *
     * @param createdDate the created date
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * Gets updated by.
     *
     * @return the updated by
     */
    public UserInfo getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets updated by.
     *
     * @param updatedBy the updated by
     */
    public void setUpdatedBy(UserInfo updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * Gets updated date.
     *
     * @return the updated date
     */
    public Date getUpdatedDate() {
        return updatedDate;
    }

    /**
     * Sets updated date.
     *
     * @param updatedDate the updated date
     */
    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    /**
     * Gets submit date.
     *
     * @return the submit date
     */
    public Date getSubmitDate() {
        return submitDate;
    }

    /**
     * Sets submit date.
     *
     * @param submitDate the submit date
     */
    public void setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
    }

    /**
     * Gets approve date.
     *
     * @return the approve date
     */
    public Date getApproveDate() {
        return approveDate;
    }

    /**
     * Sets approve date.
     *
     * @param approveDate the approve date
     */
    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    /**
     * Gets del flag.
     *
     * @return the del flag
     */
    @JsonIgnore
    @Length(min = 1, max = 1)
    public String getDelFlag() {
        return delFlag;
    }

    /**
     * Sets del flag.
     *
     * @param delFlag the del flag
     */
    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * Is deleted boolean.
     *
     * @return the boolean
     */
    @JsonIgnore
    public boolean isDeleted() {
        return StringUtils.equals(delFlag, DEL_FLAG_DELETE);
    }

    /**
     * Gets market id.
     *
     * @return the market id
     */
    public Long getMarketId() {
        return marketId;
    }

    /**
     * Sets market id.
     *
     * @param marketId the market id
     */
    public void setMarketId(Long marketId) {
        this.marketId = marketId;
    }

    /**
     * Gets release version.
     *
     * @return the release version
     */
    @JsonIgnore
    @XmlTransient
    public Integer getReleaseVersion() {
        return releaseVersion;
    }

    /**
     * Sets release version.
     *
     * @param releaseVersion the release version
     */
    public void setReleaseVersion(Integer releaseVersion) {
        this.releaseVersion = releaseVersion;
    }

    /**
     * Gets reversion.
     *
     * @return the reversion
     */
    @JsonIgnore
    @XmlTransient
    public Integer getReversion() {
        return reversion;
    }

    /**
     * Sets reversion.
     *
     * @param reversion the reversion
     */
    public void setReversion(Integer reversion) {
        this.reversion = reversion;
    }

    /**
     * Gets user id.
     *
     * @return the user id
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * Sets user id.
     *
     * @param userId the user id
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * Gets entity attribute values.
     *
     * @return the entity attribute values
     */
    public LinkedHashMap<String, String> getEntityAttributeValues() {
        return entityAttributeValues;
    }

    /**
     * Sets entity attribute values.
     *
     * @param entityAttributeValues the entity attribute values
     */
    public void setEntityAttributeValues(LinkedHashMap<String, String> entityAttributeValues) {
        this.entityAttributeValues = entityAttributeValues;
    }
}
