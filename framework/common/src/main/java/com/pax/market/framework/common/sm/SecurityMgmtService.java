/*
 * ******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ******************************************************************************
 */

package com.pax.market.framework.common.sm;

/**
 * The interface Security mgmt handler.
 */
public interface SecurityMgmtService {

    void loadKeyStore(String keyName, KeyStoreCallback callback);

    /**
     * Get public key byte [ ].
     *
     * @param name the name
     * @return the byte [ ]
     */
    byte[] getPublicKey(String name, KeyStoreCallback callback);

    /**
     * Sign byte [ ].
     *
     * @param name the name
     * @param data the data
     * @return the byte [ ]
     */
    byte[] sign(String name, byte[] data, KeyStoreCallback callback);
}
