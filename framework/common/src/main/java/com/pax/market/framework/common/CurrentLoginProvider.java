/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.framework.common;

import com.pax.market.dto.DeveloperInfo;
import com.pax.market.dto.MobileApiSysInfo;
import com.pax.market.dto.ThirdPartySysInfo;
import com.pax.market.dto.UserInfo;
import com.pax.market.dto.app.AppInfo;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.dto.terminal.TerminalInfo;

/**
 * 当前登录信息提供者, 主要是提供用户与租户相关数据给领域层做权限控制与Audit等. <p>
 * 对于Web应用而言,当前登录信息一般是指存于当前Session里的用户相关信息, 对于RESTFul API模块来说,一般是指每个API请求的Ticket或Token所对应的用户信息.
 *
 * <AUTHOR>
 */
public interface CurrentLoginProvider {

    /**
     * Gets MarketInfo for all API call
     *
     * @return the current market info
     */
    MarketInfo getCurrentMarketInfo();

    /**
     * Gets UserInfo if access from web.
     *
     * @return the current user info
     */
    UserInfo getCurrentUserInfo();

    /**
     * Gets TerminalInfo if terminal API call
     *
     * @return the current terminal info
     */
    TerminalInfo getCurrentTerminalInfo();

    /**
     * get AppInfo if 3rd app API call
     * 
     * @return the current app info
     */
    AppInfo getCurrentAppInfo();
    
    /**
     * get ThirdPartySysInfo if 3rd system API call
     * @return
     */
    ThirdPartySysInfo getCurrentThirdPartySysInfo();

    default MobileApiSysInfo getCurrentMobileSysInfo() {
        return null;
    }

    /**
     * get DeveloperInfo id 3rd developer api call
     * @return
     */
    DeveloperInfo getCurrentDeveloperInfo();

    void initUser(Long userId);

    void clearInitializedUser();
}
