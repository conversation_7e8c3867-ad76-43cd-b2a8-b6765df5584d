/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.framework.common.service;

import com.pax.api.eventbus.Event;
import com.pax.api.eventbus.EventPublisher;
import com.pax.api.pubsub.PubSubService;
import com.pax.market.constants.SystemConstants;
import com.pax.market.dto.*;
import com.pax.market.dto.market.MarketInfo;
import com.pax.market.framework.common.CurrentLoginProvider;
import com.pax.market.framework.common.persistence.Page;
import com.pax.market.framework.common.utils.LongUtils;
import com.pax.market.framework.common.utils.context.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractService {

    /**
     * The Logger.
     */
    protected Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * The Current login provider.
     */
    @Autowired
    protected transient CurrentLoginProvider currentLoginProvider;

    /**
     * Fire event.
     *
     * @param event the event
     */
    protected void fireEvent(Event event) {
        event.setSource(this);
        getEventPublisher().publish(event);
    }

    /**
     * Fire Async event
     *
     * @param event the event
     */
    protected void fireAsyncEvent(Event event) {
        log.debug("Fire event {}", event);
        event.setSource(this);
        getEventPublisher().publishAsyn(event);
    }

    /**
     * Pub message.
     *
     * @param message the message
     */
    protected void pubMessage(Object message) {
        SpringContextHolder.getBean(PubSubService.class).pubMessage(SystemConstants.PAX_DOMAIN_PUBSUB_TOPIC, message);
    }

    /**
     * Gets current market info.
     *
     * @return the current market info
     */
    protected MarketInfo getCurrentMarket() {
        return currentLoginProvider.getCurrentMarketInfo();
    }

    /**
     * Gets current user info.
     *
     * @return the current user info
     */
    protected UserInfo getCurrentUser() {
        return currentLoginProvider.getCurrentUserInfo();
    }

    /**
     * Gets current reseller.
     *
     * @return the current reseller
     */
    protected ResellerInfo getCurrentReseller() {
        if (currentLoginProvider.getCurrentThirdPartySysInfo() != null) {
            return currentLoginProvider.getCurrentThirdPartySysInfo().getReseller();
        } else if(currentLoginProvider.getCurrentMobileSysInfo() != null) {
            return currentLoginProvider.getCurrentMobileSysInfo().getReseller();
        }else if (currentLoginProvider.getCurrentUserInfo() != null) {
            return currentLoginProvider.getCurrentUserInfo().getCurrentReseller();
        }
        return null;
    }

    /**
     * Gets current merchant
     *
     * @return current merchant
     */
    protected MerchantInfo getCurrentMerchant() {
        if (currentLoginProvider.getCurrentUserInfo() != null) {
            return currentLoginProvider.getCurrentUserInfo().getCurrentMerchant();
        }
        return null;
    }

    /**
     * Gets current third party sys.
     *
     * @return the current third party sys
     */
    protected ThirdPartySysInfo getCurrentThirdPartySys() {
        return currentLoginProvider.getCurrentThirdPartySysInfo();
    }

    /**
     * Gets current developer info
     *
     * @return current developer info
     */
    protected DeveloperInfo getCurrentDeveloperInfo() {
        return currentLoginProvider.getCurrentDeveloperInfo();
    }

    /**
     * Some service call from API has no current UserInfo (e.g. pulic API call), so must build default UserInfo manually
     *
     * @return UserInfo user info
     */
    protected UserInfo buildUpdatedBy() {
        UserInfo userInfo = currentLoginProvider.getCurrentUserInfo();
        if(userInfo != null) {
            userInfo = new UserInfo(userInfo.getId());
        } else if (currentLoginProvider.getCurrentThirdPartySysInfo() != null) {
            userInfo = new UserInfo(SystemConstants.UPDATED_BY_THIRD_PARTY_SYS_ID);
        } else if (currentLoginProvider.getCurrentTerminalInfo() != null) {
            userInfo = new UserInfo(SystemConstants.UPDATED_BY_TERMINAL_ID);
        } else {
            userInfo = new UserInfo(SystemConstants.UPDATED_BY_SYSTEM_ID);
        }
        return userInfo;
    }

    /**
     * Gets current market id.
     *
     * @return the current market id
     */
    protected Long getCurrentMarketId() {
        return Optional.ofNullable(getCurrentMarket()).map(MarketInfo::getId).orElse(0L);
    }

    /**
     * Is super market boolean.
     *
     * @return the boolean
     */
    protected boolean isSuperMarket() {
        return getCurrentUser() != null && LongUtils.equals(SystemConstants.SUPER_MARKET_ID, getCurrentMarketId());
    }

    /**
     * Gets current domain.
     *
     * @return the current domain
     */
    protected String getCurrentDomain() {
        return Optional.ofNullable(getCurrentMarket()).map(MarketInfo::getDomain).orElse(null);
    }

    /**
     * Gets current user id.
     *
     * @return the current user id
     */
    protected Long getCurrentUserId() {
        return Optional.ofNullable(getCurrentUser()).map(UserInfo::getId).orElse(-1L);
    }

    /**
     * Gets current reseller id.
     *
     * @return the current reseller id
     */
    protected Long getCurrentResellerId() {
        return Optional.ofNullable(getCurrentReseller()).map(ResellerInfo::getId).orElse(-1L);
    }

    /**
     * Gets current merchant id
     *
     * @return current merchant id
     */
    protected long getCurrentMerchantId() {
        return Optional.ofNullable(getCurrentMerchant()).map(MerchantInfo::getId).orElse(-1L);
    }

    /**
     * Gets event publisher.
     *
     * @return the event publisher
     */
    protected EventPublisher getEventPublisher() {
        return SpringContextHolder.getBean(EventPublisher.class);
    }

    /**
     * Is current user in root reseller boolean.
     *
     * @return the boolean
     */
    protected boolean isCurrentUserInRootReseller() {
        return getCurrentReseller() != null && LongUtils.isBlankOrNotPositive(getCurrentReseller().getParentId());
    }

    /**
     * Is current user in super root reseller boolean.
     *
     * @return the boolean
     */
    protected boolean isCurrentUserInSuperRootReseller() {
        return hasValidCurrentUserInfo() && getCurrentReseller() != null && LongUtils.isBlankOrNotPositive(getCurrentReseller().getParentId()) && LongUtils.equals(getCurrentMarketId(), SystemConstants.SUPER_MARKET_ID);
    }


    /**
     * Is current user in current market admin boolean.
     *
     * @return the boolean
     */
    protected boolean isCurrentUserInMarketAdmin() {
        return hasValidCurrentUserInfo() && (getCurrentUser().isMarketAdmin() || getCurrentUser().isSuperAdmin());
    }


    /**
     * Is current user in  root market admin boolean.
     *
     * @return the boolean
     */
    protected boolean isCurrentUserInRootMarketAdmin() {
        Long rootAdminUserId = getCurrentMarket().getAdmin()!= null ? getCurrentMarket().getAdmin().getId() : null;
        return isCurrentUserInMarketAdmin() && getCurrentUser().getId().equals(rootAdminUserId);
    }

    /**
     * Has valid current user info boolean.
     *
     * @return the boolean
     */
    protected boolean hasValidCurrentUserInfo() {
        return getCurrentUserId() > 0;
    }

    /**
     * Do pagination.
     *
     * @param <T>  the type parameter
     * @param page the page
     * @param list the list
     */
    protected <T extends Serializable> void doPagination(Page<T> page, List<T> list) {
        page.setCount(list.size());
        page.setList(getPageList(page, list));
    }

    /**
     * Gets page list.
     *
     * @param <T>  the type parameter
     * @param page the page
     * @param list the list
     * @return the page list
     */
    protected <T extends Serializable> List<T> getPageList(Page<?> page, Collection<T> list) {
        if (page.getFirstResult() < list.size()) {
            return list.stream().skip(page.getFirstResult()).limit(page.getMaxResults()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
