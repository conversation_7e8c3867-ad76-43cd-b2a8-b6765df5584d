/**
 * ********************************************************************************
 * COPYRIGHT      
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION     
 *   This software is supplied under the terms of a license agreement or      
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied     
 *   or disclosed except in accordance with the terms in that agreement.
 *         
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * ********************************************************************************
 */

package com.pax.market.framework.common.persistence.vfs;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import org.apache.ibatis.io.VFS;

import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;


/**
 * 
 *
 * <AUTHOR>
 * @date Mar 25, 2017
 */
public class SpringBootVFS extends VFS {

	  private final ResourcePatternResolver resourceResolver;

	  public SpringBootVFS() {
	    this.resourceResolver = new PathMatchingResourcePatternResolver(getClass().getClassLoader());
	  }

	  @Override
	  public boolean isValid() {
	    return true;
	  }

	  @Override
	  protected List<String> list(URL url, String path) throws IOException {
	    Resource[] resources = resourceResolver.getResources("classpath*:" + path + "/**/*.class");
	    List<String> resourcePaths = new ArrayList<>();
	    for (Resource resource : resources) {
	      resourcePaths.add(preserveSubpackageName(resource.getURI(), path));
	    }
	    return resourcePaths;
	  }

	  private static String preserveSubpackageName(final URI uri, final String rootPath) {
	    final String uriStr = uri.toString();
	    final int start = uriStr.indexOf(rootPath);
	    return uriStr.substring(start);
	  }

	}