/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2018. PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.framework.common.rki;

/**
 * The type Rki exception.
 *
 * <AUTHOR>
 * @date 2018 /10/11
 */
public class RKIException extends RuntimeException {
	private static final long serialVersionUID = -3275656961551922001L;
	private String errorCode;

    /**
     * Instantiates a new Rki exception.
     *
     * @param message the message
     */
    public RKIException(String message){
        super(message);
    }

    /**
     * Instantiates a new Rki exception.
     *
     * @param errorCode the error code
     * @param message   the message
     */
    public RKIException(String errorCode, String message){
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * Instantiates a new Rki exception.
     *
     * @param throwable the throwable
     */
    public RKIException(Throwable throwable){
        super(throwable);
    }

    /**
     * Gets error code.
     *
     * @return the error code
     */
    public String getErrorCode() {
        return errorCode;
    }
}
