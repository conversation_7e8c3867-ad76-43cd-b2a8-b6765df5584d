/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */
package com.pax.market.framework.common.persistence;

import java.util.List;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.pax.market.framework.common.utils.Reflections;

/**
 * 数据Entity类
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
public abstract class TreeEntity<T extends DataEntity<T>> extends DataEntity<T> {

    private static final long serialVersionUID = 1L;

    public static final String PARENT_IDS_DELEMETER = ",";

    /**
     * The Parent.
     */
    protected T parent;    // 父级编号
    /**
     * The Parent ids.
     */
    private String parentIds; // 所有父级编号
    /**
     * The Name.
     */
    private String name;    // 机构名称
    /**
     * The Sort.
     */
    private Integer sort;        // 排序

    /**
     * The Children.
     */
    private List<T> children;

    /**
     * Instantiates a new Tree entity.
     */
    public TreeEntity() {
        super();
        this.sort = 30;
    }

    /**
     * Instantiates a new Tree entity.
     *
     * @param id the id
     */
    public TreeEntity(Long id) {
        super(id);
    }

    /**
     * 父对象，只能通过子类实现，父类实现mybatis无法读取
     *
     * @return parent parent
     */
    @JsonBackReference
    public abstract T getParent();

    /**
     * 父对象，只能通过子类实现，父类实现mybatis无法读取
     *
     * @param parent the parent
     * @return
     */
    public abstract void setParent(T parent);

    /**
     * Gets parent ids.
     *
     * @return the parent ids
     */
    public String getParentIds() {
        return parentIds;
    }

    /**
     * Sets parent ids.
     *
     * @param parentIds the parent ids
     */
    public void setParentIds(String parentIds) {
        this.parentIds = parentIds;
    }

    /**
     * Gets name.
     *
     * @return the name
     */
    @Length(min = 1, max = 100)
    public String getName() {
        return name;
    }

    /**
     * Sets name.
     *
     * @param name the name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets sort.
     *
     * @return the sort
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * Sets sort.
     *
     * @param sort the sort
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * Gets parent id.
     *
     * @return the parent id
     */
    @JsonIgnore
    public Long getParentId() {
        if (parent != null) {
            return parent.getId();
        }
        return null;
    }

    /**
     * Gets parent name.
     *
     * @return the parent name
     */
    @JsonIgnore
    public String getParentName() {
        if (parent != null) {
            return (String)Reflections.getFieldValue(parent, "name");
        }
        return null;
    }

    /**
     * Gets parent code.
     *
     * @return the parent code
     */
    @JsonIgnore
    public String getParentCode() {
        if (parent != null) {
            return (String)Reflections.getFieldValue(parent, "code");
        }
        return null;
    }

    /**
     * Gets parent id string.
     *
     * @return the parent id string
     */
    @JsonProperty("parentId")
    public String getParentIdString() {
        return String.valueOf(getParentId());
    }

    /**
     * Gets children.
     *
     * @return the children
     */
    public List<T> getChildren() {
        return children;
    }

    /**
     * Sets children.
     *
     * @param children the children
     */
    public void setChildren(List<T> children) {
        this.children = children;
    }

    /**
     * Add child.
     *
     * @param child the child
     */
    public void addChild(T child) {
        if (this.children == null) this.children = Lists.newArrayList();
        this.children.add(child);
    }
}
