/**
 * COPYRIGHT
 * PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 * This software is supplied under the terms of a license agreement or
 * nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 * or disclosed except in accordance with the terms in that agreement.
 * . All Rights Reserved.
 */
package com.pax.market.framework.common.persistence;



import java.util.List;

/**
 * DAO支持类实现
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
public interface CrudDao<T extends BaseEntity<?>> extends BaseDao {

    /**
     * 获取单条数据
     *
     * @param id the id
     * @return t t
     */
    T get(Long id);

    /**
     * 获取单条数据
     *
     * @param entity the entity
     * @return t t
     */
    T get(T entity);

    /**
     * 查询数据列表，如果需要分页，请设置分页对象，如：entity.setPage(new Page<T>());
     *
     * @param entity the entity
     * @return list list
     */
    List<T> findList(T entity);

    /**
     * 查询所有数据列表
     *
     * @param entity the entity
     * @return list list
     */
    List<T> findAllList(T entity);

    /**
     * 插入数据
     *
     * @param entity the entity
     * @return int int
     */
    int insert(T entity);

    /**
     * 更新数据
     *
     * @param entity the entity
     * @return int int
     */
    int update(T entity);

    /**
     * 删除数据（一般为逻辑删除，更新del_flag字段为1）
     *
     * @param id the id
     * @return int int
     */
    int delete(Long id);

    /**
     * 删除数据（一般为逻辑删除，更新del_flag字段为1）
     *
     * @param entity the entity
     * @return int int
     */
    int delete(T entity);

    /**
     * Physical delete int.
     *
     * @param id the id
     * @return the int
     */
    int physicalDelete(Long id);

    /**
     * Physical delete int.
     *
     * @param entity the entity
     * @return the int
     */
    int physicalDelete(T entity);


    /**
     * Gets biz count.
     *
     * @param entity the entity
     * @return the biz count
     */
    int getBizCount(Object entity);

}