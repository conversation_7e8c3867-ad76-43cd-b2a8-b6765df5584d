package com.pax.market.framework.common.excel;

import com.pax.market.framework.common.utils.RandomUtil;
import lombok.Cleanup;
import org.junit.Ignore;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Ignore
public class ExcelImportExportTest {
    private static Logger log = LoggerFactory.getLogger(ExcelImportExportTest.class);

    @Test
    public void testExcelExport() throws Exception {
        List<ExcelEntity> list = new ArrayList<>();
        log.info("Start prepare data...");
        ExcelEntity excelEntity = new ExcelEntity();
        excelEntity.setKey(RandomUtil.generateMixString(16));
        excelEntity.setValue(123456789);
        excelEntity.setRemarks(RandomUtil.generateMixString(255));
        excelEntity.setCreatedTime(new Date());
        for (int i = 0; i < 5000; i++) {
            list.add(excelEntity);
        }
        log.info("Start export data...");
        @Cleanup FileOutputStream fos = new FileOutputStream("src/test/resources/excel/test.xlsx", false);
        @Cleanup ExportExcel exportExcel = new ExportExcel("Test", ExcelEntity.class);
        exportExcel.setDataList(list).write(fos);
        log.info("Export data finished");
    }

    @Test
    public void testExcelImport() throws Exception {
        File importFile = new File("src/test/resources/excel/test.xlsx");

        log.info("Start import data...");
        @Cleanup ImportExcel importExcel = new ImportExcel(importFile, 1);
        log.info("Import dataList size: " + importExcel.getLastDataRowNum());
        List<ExcelEntity> dataList = importExcel.getDataList(ExcelEntity.class);
        log.info("Import finished: " + dataList.size());
    }

    @Test
    public void testEasyExcelImport() throws Exception {
        File importFile = new File("src/test/resources/excel/test.xlsx");
        log.info("Start easyexcel import data...");
        @Cleanup EasyImportExcel easyImportExcel = new EasyImportExcel(importFile, 1);
        log.info("Easyexcel import dataList size: " + easyImportExcel.getLastDataRowNum());
        List<ExcelEntity> dataList = easyImportExcel.getDataList(ExcelEntity.class);
        log.info("Easyexcel import finished: " + dataList.size());
    }
}
