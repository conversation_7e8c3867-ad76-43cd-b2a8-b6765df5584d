package com.pax.market.signature.resttemplate.paxrhinosign.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/13
 */
@Getter
@Setter
@ToString
public class PaxRhinoWorkKeyDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -922703480448982027L;
    private String puk;   // 公钥数据
    private String pvkName; //私钥名称

}
