/*
 *
 *  * *******************************************************************************
 *  * COPYRIGHT
 *  *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *  *   This software is supplied under the terms of a license agreement or
 *  *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *  *   or disclosed except in accordance with the terms in that agreement.
 *  *
 *  *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 *  * *******************************************************************************
 *
 */

package com.pax.market.framework.common.alg;

import org.junit.Test;
import com.pax.market.framework.common.utils.alg.CryptoUtils;
import static org.junit.Assert.assertEquals;

/**
 * Created by fanjun on 2017/8/22.
 */
public class CryptoUtilsTest {

    @Test
    public void aesTest() {
        String data = "我就是我不一样的自己";
        String encryptData = "945b3f566bcf0959227cba2756e9124cf839250d337acda57e6823865a8d508a";
        String secret = "**********";
        String decryptData = CryptoUtils.aesDecrypt(encryptData, secret);
        System.out.println("解密后：" + decryptData);
        assertEquals(data, decryptData);

        System.out.println("1：" + CryptoUtils.aesEncrypt("1", "password"));
        System.out.println("111111：" + CryptoUtils.aesEncrypt("111111", "password"));
        System.out.println("123123123123123：" + CryptoUtils.aesEncrypt("123123123123123", "password"));
        System.out.println("3232323：" + CryptoUtils.aesEncrypt("3232323", "password"));

    }
}
