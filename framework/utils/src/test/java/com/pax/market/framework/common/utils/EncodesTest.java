/*
 * *******************************************************************************
 * COPYRIGHT
 *               PAX TECHNOLOGY, Inc. PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or
 *   nondisclosure agreement with PAX  Technology, Inc. and may not be copied
 *   or disclosed except in accordance with the terms in that agreement.
 *
 *      Copyright (C) 2017 PAX Technology, Inc. All rights reserved.
 * *******************************************************************************
 */

package com.pax.market.framework.common.utils;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.Test;

import com.pax.market.framework.common.utils.security.Encodes;

/**
 * 封装各种格式的编码解码工具类测试用例.
 *
 * <AUTHOR>
 * @date 16/05/15
 */
public class EncodesTest {

    @Test
    public void hexEncode() {
        String input = "haha,i am a very long message";
        String result = Encodes.encodeHex(input.getBytes());
        assertThat(new String(Encodes.decodeHex(result))).isEqualTo(input);
    }

    @Test
    public void base64Encode() {
        String input = "haha,i am a very long message";
        String result = Encodes.encodeBase64(input.getBytes());
        assertThat(new String(Encodes.decodeBase64(result))).isEqualTo(input);
    }

    @Test
    public void base64UrlSafeEncode() {
        String input = "haha,i am a very long message";
        String result = Encodes.encodeUrlSafeBase64(input.getBytes());
        assertThat(new String(Encodes.decodeBase64(result))).isEqualTo(input);
    }

    @Test
    public void urlEncode() {
        String input = "http://locahost/?q=中文&t=1";
        String result = Encodes.urlEncode(input);
        System.out.println(result);

        assertThat(Encodes.urlDecode(result)).isEqualTo(input);
    }

    @Test
    public void xmlEncode() {
        String input = "1>2";
        String result = Encodes.escapeXml(input);
        assertThat(result).isEqualTo("1&gt;2");
        assertThat(Encodes.unescapeXml(result)).isEqualTo(input);
    }

    @Test
    public void html() {
        String input = "1>2";
        String result = Encodes.escapeHtml(input);
        assertThat(result).isEqualTo("1&gt;2");
        assertThat(Encodes.unescapeHtml(result)).isEqualTo(input);
    }
}
