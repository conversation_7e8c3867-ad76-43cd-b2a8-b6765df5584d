package com.pax.market.framework.common.utils;

import com.pax.market.framework.common.utils.alg.CryptoUtils;
import org.junit.Assert;
import org.junit.Test;

import java.nio.charset.StandardCharsets;

/**
 * Created by gubin_ on 2017/11/8.
 */
public class AESUtilsTest {

    @Test
    public void testEncryptAndDecrypt() throws Exception{
        String key = "P@xsz2017";
        String originalContent = "pax2017";

        String encryptedContent = AESUtils.encrypt(originalContent, key);
        //String expected = "je2h/vmgQB1v0UJZeiS7uw==";
        String decryptedContext = AESUtils.decrypt(encryptedContent, key);

        System.out.println(key);
        System.out.println(originalContent);
        System.out.println(encryptedContent);
        System.out.println(decryptedContext);
        System.out.println(AESUtils.silentDecrypt(originalContent, key));
        System.out.println(AESUtils.silentDecrypt(encryptedContent, key));

        Assert.assertEquals(originalContent, decryptedContext);
    }

    @Test
    public void testParameterEncrypt() throws Exception {
        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fErMOsl5AcwUQU-RwW8x1tGDaHKWN4VN5gxPsLjFfyc";
        String text = "signature=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJmaWxlTmFtZSI6IuWptOavjeefpV8xLjYuMS5hcGsiLCJpc3MiOiJhdXRoMCIsInVzZU9yaWdpbmFsIjpmYWxzZSwiZXhwIjoxNzE1MTM0NjIwLCJmaWxlSWQiOiJncm91cDEvTTAwLzAwL0VEL3dLaklHV1RVZDllQUtvYV9BSlZ6T1VXWS12RTIxNi5hcGsifQ.zTiWp0eTe0PpRnPHjRuqSrKC_1g8oXjloU1HuPAm1OEfUQxSFzG_cPHQXAcAVanXvPREjja6a1bBJEF85QmMMA";
        String key = DigestUtils.md5Hex(token);
        System.out.println("key:" + key);
        byte[] encryptedData = CryptoUtils.aesEncrypt(text.getBytes(), CryptoUtils.hexStr2Bytes(key));
        String encryptedParameter = CryptoUtils.byte2Hex(encryptedData);
        System.out.println("encStr: " + encryptedParameter);
        byte[] decryptData = CryptoUtils.aesDecrypt(CryptoUtils.hexStringToBytes(encryptedParameter), CryptoUtils.hexStr2Bytes(key));
        System.out.println("decStr: " + new String(decryptData, StandardCharsets.UTF_8));
    }


    @Test
    public void validateIsEncrypted() throws Exception {
        String content = "APPSECRET0APPSECRET0APPSECRET0APPSECRET0";
        String encryptKey = "DEVSECRET";

        String encryptedString = AESUtils.silentEncrypt(content, encryptKey);
        String decryptedString = AESUtils.silentDecrypt(encryptedString, encryptKey);
        System.out.println("origin String: " + content);
        System.out.println("Encrypted String: " + encryptedString);
        System.out.println("Decrypted String: " + decryptedString);

        System.out.println("Is encryptedString encrypted? " + AESUtils.isNotSilentEncrypted(encryptedString, decryptedString));
    }
}
