DROP TABLE IF EXISTS `pax_emm_zte_device_record`;
CREATE TABLE `pax_emm_zte_device_record` (
        `id` bigint NOT NULL COMMENT '编号',
        `zte_record_id` bigint NOT NULL COMMENT 'zte记录编号',
        `reseller_id` int NOT NULL COMMENT '代理商id',
        `merchant_id` int NOT NULL COMMENT '商户id',
        `identifier_type` char(1) NOT NULL COMMENT 'I: IMEI; S: SerialNo',
        `manufacturer` varchar(64) COMMENT '制造商',
        `model` varchar(64) COMMENT '机型',
        `number` varchar(64) NOT NULL COMMENT '设备IMEI或者序列号',
        `status` char(1) COMMENT '状态',
        `failed_reason` varchar(255) COMMENT 'claim失败原因',
        PRIMARY KEY (`id`)
) COMMENT 'EMM ZTE审核设备记录表';