DROP TABLE IF EXISTS `pax_market_air_link_setting`;
CREATE TABLE `pax_market_air_link_setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `market_id` int NOT NULL,
  `data_pool_id` int NOT NULL,
  `package_type` char(1)  NOT NULL COMMENT '套餐类型',
  `package_limit` int NOT NULL COMMENT '套餐内最大限制',
  `free_usage` int NOT NULL COMMENT '免费使用数',
  `activation_fee` decimal(10,2) NOT NULL COMMENT '激活费',
  `package_fee` decimal(10,2) DEFAULT NULL COMMENT '套餐费',
  `overage_price` decimal(10,2) DEFAULT NULL COMMENT '套餐外价格',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `trial_balance` decimal(10,2) DEFAULT 0.00 COMMENT '试用余额',
  `reset_flag` bit(1) DEFAULT 0 COMMENT '重置标识',
  `read_by` int DEFAULT NULL COMMENT '同意者',
  `price_changed_date` datetime NOT NULL COMMENT '价格更新时间',
  `end_free_time` datetime DEFAULT NULL COMMENT '试用结束时间',
  `charge_date` datetime DEFAULT NULL COMMENT '收费时间',
  `overdue_date` datetime DEFAULT NULL COMMENT '过期时间',
  `created_by` int NOT NULL COMMENT '创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `updated_by` int NOT NULL COMMENT '更新者',
  `updated_date` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mal_market_id` (`market_id`)
) COMMENT='应用市场AirLink配置表';