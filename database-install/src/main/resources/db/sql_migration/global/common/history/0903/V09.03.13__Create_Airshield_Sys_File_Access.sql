DROP TABLE IF EXISTS `pax_airshield_sys_file_access`;

CREATE TABLE `pax_airshield_sys_file_access` (
                                                 `id` int NOT NULL AUTO_INCREMENT,
                                                 `market_id` int NOT NULL COMMENT '市场id',
                                                 `file_path` varchar(1000) NOT NULL COMMENT '文件路径',
                                                 `description` varchar(255) DEFAULT NULL COMMENT '描述',
                                                 `created_date` datetime NOT NULL COMMENT '创建时间',
                                                 `created_by` int NOT NULL COMMENT '创建人',
                                                 `updated_date` datetime NOT NULL COMMENT '更新时间',
                                                 `updated_by` int NOT NULL COMMENT '更新人',
                                                 `del_flag` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志',
                                                 PRIMARY KEY (`id`)
) COMMENT='AirShield限制访问路径配置表';