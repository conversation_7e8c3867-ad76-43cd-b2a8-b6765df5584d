DROP TABLE IF EXISTS `pax_app_price_template`;
CREATE TABLE `pax_app_price_template`
(
    `id`            INT   NOT NULL AUTO_INCREMENT  COMMENT '编号',
    `market_id`     INT          NOT NULL COMMENT '市场id',
    `name`          VARCHAR(128) NOT NULL COMMENT '模板名称',
    `paid`          TINYINT      NOT NULL COMMENT '是否收费',
    `charge_type`   INT          NOT NULL COMMENT '收费类型',
    `text`          VARCHAR(128) DEFAULT NULL COMMENT 'text',
    `currency`      VARCHAR(5)   DEFAULT NULL COMMENT '币种',
    `price`         DECIMAL(14,2) DEFAULT NULL COMMENT '价格',
    `free_trial_day` INT          DEFAULT NULL COMMENT '免费试用期',
    `created_by`      INT(11) NOT NULL,
    `created_date`    DATETIME NOT NULL,
    `updated_by`      INT(11) NOT NULL,
    `updated_date`    DATETIME NOT NULL,
    PRIMARY KEY (id)
) COMMENT='应用价格模板';


DROP TABLE IF EXISTS `pax_app_cost_reseller`;
CREATE TABLE `pax_app_cost_reseller`
(
    `market_id`     INT          NOT NULL COMMENT '市场id',
    `app_id`        BIGINT       NOT NULL COMMENT '应用id',
    `reseller_id`   INT          NOT NULL COMMENT '代理商id',
    `template_id`   INT          NOT NULL COMMENT '模板id',
    `created_date`    DATETIME NOT NULL,
    `updated_date`    DATETIME NOT NULL,
    PRIMARY KEY (`market_id`, `app_id`, `reseller_id`),
    INDEX IDX_APP_COST_APP_ID (`app_id`),
    INDEX IDX_APP_COST_MARKET_ID (`market_id`),
    INDEX IDX_APP_COST_RESELLER_ID (`reseller_id`)
) COMMENT='应用价格定向发布表';


