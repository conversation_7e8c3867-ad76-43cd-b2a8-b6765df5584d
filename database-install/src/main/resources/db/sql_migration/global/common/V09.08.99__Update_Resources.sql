DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'error', '/error', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10001', 'error_3', '/error', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10002', 'error_2', '/error', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10003', 'error_5', '/error', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10004', 'error_6', '/error', 'OPTIONS', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10005', 'error_1', '/error', 'HEAD', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10006', 'error_4', '/error', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10007', 'sendMessageToTerminal', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10008', 'sendMessageByTag', '/v1/3rd/cloudmsg/bytag', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10009', 'sendMessageToSingleTerminal', '/v1/3rd/cloudmsg/single', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10010', 'getMessageArrivalRate', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10011', 'submitApkInfo', '/v1/3rd/coverapp/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10012', 'categoryList', '/v1/3rd/coverapp/app/categoryList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10013', 'searchApps_4', '/v1/3rd/coverapp/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'findFactoryModelList_2', '/v1/3rd/coverapp/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10015', 'validateAppKey', '/v1/3rd/coverapp/validate/appKey', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'uploadApk', '/v1/3rd/developer/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'getApkById', '/v1/3rd/developer/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'editApk', '/v1/3rd/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'deleteApk_4', '/v1/3rd/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'offlineApk_3', '/v1/3rd/developer/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'submitApk_1', '/v1/3rd/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'getApp', '/v1/3rd/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'createApp_1', '/v1/3rd/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'deleteApp_4', '/v1/3rd/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10025', 'createApk', '/v1/3rd/developer/apps/{appId}/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'updateAppKeySecret', '/v1/3rd/developer/apps/{appId}/key-secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'getCodeByType', '/v1/3rd/developer/codes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'getApkVersionListByAppId', '/v1/3rd/developer/{appId}/apks/version-list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'rkiCallback', '/v1/3rd/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10030', 'findApkParameters', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'createApkTemplate', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'getApkParameterById', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'updateApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'deleteApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'searchApps_3', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'getAppCost', '/v1/3rdsys/apps/app-cost', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'searchEntityAttributes', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'createEntityAttribute_1', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'getEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'updateEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10041', 'deleteEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10042', 'updateEntityAttributeLabel_1', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10043', 'findEmmApps', '/v1/3rdsys/emm/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10044', 'createEmmApp_1', '/v1/3rdsys/emm/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10045', 'findEmmAppSubscriptionPage_1', '/v1/3rdsys/emm/apps/subscription', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10046', 'getEmmAppDetail_1', '/v1/3rdsys/emm/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10047', 'removeEmmApp_1', '/v1/3rdsys/emm/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10048', 'findAvailableTestTrackVersionList', '/v1/3rdsys/emm/apps/{appId}/available/test/versions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10049', 'findEmmAppPermissionList', '/v1/3rdsys/emm/apps/{appId}/permissions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10050', 'subscribeEmmApp', '/v1/3rdsys/emm/apps/{appId}/subscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10051', 'unSubscribeEmmApp', '/v1/3rdsys/emm/apps/{appId}/unsubscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10052', 'getEmmDeviceDashboardDetail', '/v1/3rdsys/emm/device/detail/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10053', 'findEmmDeviceInstalledAppPage_1', '/v1/3rdsys/emm/device/detail/{deviceId}/installed-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10054', 'getEmmDeviceDashboardMonitor_1', '/v1/3rdsys/emm/device/detail/{deviceId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10055', 'findEmmDevices', '/v1/3rdsys/emm/devices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10056', 'batchDeleteEmmDevices_1', '/v1/3rdsys/emm/devices/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10057', 'batchMoveEmmDevices', '/v1/3rdsys/emm/devices/batch/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10058', 'createRegisterQRCode_1', '/v1/3rdsys/emm/devices/register-qrcode', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10059', 'submitEmmZteQuickUploadRecord_1', '/v1/3rdsys/emm/devices/zte/quick-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10060', 'getEmmDevice_1', '/v1/3rdsys/emm/devices/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10061', 'updateEmmDevice_1', '/v1/3rdsys/emm/devices/{deviceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10062', 'deleteEmmDevice_1', '/v1/3rdsys/emm/devices/{deviceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10063', 'lockEmmDeviceScreen', '/v1/3rdsys/emm/devices/{deviceId}/lockscreen', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10064', 'rebootEmmDevice', '/v1/3rdsys/emm/devices/{deviceId}/reboot', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10065', 'resetEmmDevicePassword', '/v1/3rdsys/emm/devices/{deviceId}/resetpw', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10066', 'startEmmDeviceLostMode', '/v1/3rdsys/emm/devices/{deviceId}/startlost', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10067', 'stopEmmDeviceLostMode', '/v1/3rdsys/emm/devices/{deviceId}/stoplost', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10068', 'getMerchantEmmPolicy_1', '/v1/3rdsys/emm/policy/merchant', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10069', 'createMerchantEmmPolicy_1', '/v1/3rdsys/emm/policy/merchant', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10070', 'getResellerEmmPolicy_1', '/v1/3rdsys/emm/policy/reseller', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10071', 'createResellerEmmPolicy_1', '/v1/3rdsys/emm/policy/reseller', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10072', 'verifyEstateBySerialNo', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10073', 'findFactoryModelList_1', '/v1/3rdsys/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10074', 'findDataFromInsight', '/v1/3rdsys/goInsight/data/app-biz', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10075', 'findDataFromGoInsight', '/v1/3rdsys/goInsight/data/app-biz/{queryCode}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10076', 'deductMarketLastMonthActiveFee', '/v1/3rdsys/internal/airlink/deduct/lastmonth/active-fee', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10077', 'deductMarketLastMonthTrafficFee', '/v1/3rdsys/internal/airlink/deduct/lastmonth/overage-traffic-fee', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10078', 'deductMarketCurrentMonthFee', '/v1/3rdsys/internal/airlink/deduct/month/package-fee', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10079', 'createTables', '/v1/3rdsys/internal/audit-log/create-tables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10080', 'migrationAuditLog', '/v1/3rdsys/internal/audit-log/migration/audit-log', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10081', 'migrationAuditTrail', '/v1/3rdsys/internal/audit-log/migration/audit-trail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10082', 'refreshNodes', '/v1/3rdsys/internal/audit-log/refresh-nodes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10083', 'syncBillingListToZolonBillingCenter', '/v1/3rdsys/internal/billing', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10084', 'abandonBillings', '/v1/3rdsys/internal/billing/abandon', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10085', 'findAirLoadOrderSummary', '/v1/3rdsys/internal/billing/air-link/order/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10086', 'updateAirLoadOrderSummary', '/v1/3rdsys/internal/billing/air-link/order/summary/{summaryId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10087', 'updateAirLinkOrderStatus', '/v1/3rdsys/internal/billing/air-link/order/{orderNumber}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10088', 'cancelWriteOffMarketBillingSummary', '/v1/3rdsys/internal/billing/cancel/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10089', 'syncSolutionDeveloperList', '/v1/3rdsys/internal/billing/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10090', 'syncFineTerminalListToZolonBillingCenter', '/v1/3rdsys/internal/billing/fine/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10091', 'searchMarkets_3', '/v1/3rdsys/internal/billing/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10092', 'writeOffMarketBilling', '/v1/3rdsys/internal/billing/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10093', 'updateBillingServiceSyncedStatus', '/v1/3rdsys/internal/billing/{billingSummaryId}/callback', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10094', 'updateBillingServicePrice', '/v1/3rdsys/internal/billing/{billingSummaryId}/price/sync', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10095', 'lockSet', '/v1/3rdsys/internal/cache/set', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10096', 'clearCache', '/v1/3rdsys/internal/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10097', 'clearDeletedTerminals', '/v1/3rdsys/internal/clearDeletedTerminals', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10098', 'clearExpiredPendingTerminalActions', '/v1/3rdsys/internal/clearExpiredPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10099', 'clearGroupPendingTerminalActions', '/v1/3rdsys/internal/clearGroupPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10100', 'clearTerminalPendingTerminalActions', '/v1/3rdsys/internal/clearTerminalPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10101', 'clearDeletedWebHookMessageHistory', '/v1/3rdsys/internal/clearWebHookMessages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10102', 'delLock', '/v1/3rdsys/internal/delLock', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10103', 'getAllDisabledRequests', '/v1/3rdsys/internal/disabled-request', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10104', 'addDisabledRequest', '/v1/3rdsys/internal/disabled-request', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10105', 'delDisabledRequest', '/v1/3rdsys/internal/disabled-request', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10106', 'clearDisabledRequest', '/v1/3rdsys/internal/disabled-request/all', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10107', 'getEmmAppInfo', '/v1/3rdsys/internal/emm/app/{enterpriseId}/{packageName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10108', 'migrateDevice', '/v1/3rdsys/internal/emm/device/migration', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10109', 'finAllDeviceList', '/v1/3rdsys/internal/emm/device/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10110', 'getDeviceInfo', '/v1/3rdsys/internal/emm/device/{enterpriseId}/{originalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10111', 'deleteDeviceByDeviceName', '/v1/3rdsys/internal/emm/device/{enterpriseId}/{originalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10112', 'pageQueryEnrollmentToken', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10113', 'getEnrollmentTokenByName', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/{tokenId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10114', 'deleteEnrollmentToken', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/{tokenId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10115', 'updateEmmEnterpriseName', '/v1/3rdsys/internal/emm/enterprise/name', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10116', 'getEnterpriseByName', '/v1/3rdsys/internal/emm/enterprise/{enterpriseId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10117', 'updateEnterprise_1', '/v1/3rdsys/internal/emm/enterprise/{enterpriseId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10118', 'pageQueryEnterprise', '/v1/3rdsys/internal/emm/enterprise/{projectId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10119', 'findAllPolicyList', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10120', 'getPolicy', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/{policyName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10121', 'deletePolicyByPolicyName', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/{policyName}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10122', 'generateApkPatchMd5', '/v1/3rdsys/internal/generateApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10123', 'generateClientApkPatchMd5', '/v1/3rdsys/internal/generateClientApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10124', 'generateTerminalEnrollFiles', '/v1/3rdsys/internal/generateTerminalEnrollFiles', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10125', 'getCache', '/v1/3rdsys/internal/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10126', 'getLock', '/v1/3rdsys/internal/getLock', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10127', 'sendDictionaryDataToGoInsight', '/v1/3rdsys/internal/insight/sync/dictionary-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10128', 'sendMarketDataToGoInsight', '/v1/3rdsys/internal/insight/sync/market-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10129', 'loadCacheByCacheName', '/v1/3rdsys/internal/load/cache', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10130', 'lock', '/v1/3rdsys/internal/lock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10131', 'findMarketApiBlackList', '/v1/3rdsys/internal/marketBlackApi', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10132', 'delMarketBlackApi', '/v1/3rdsys/internal/marketBlackApi/{marketBlackApiId}/market/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10133', 'createMarketBlackApi', '/v1/3rdsys/internal/marketBlackApi/{marketId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10134', 'getAuthCode', '/v1/3rdsys/internal/maxsearch/auth-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10135', 'resumeBinlogConsumer', '/v1/3rdsys/internal/maxsearch/binlog-consumer/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10136', 'checkFullSyncProcess_1', '/v1/3rdsys/internal/maxsearch/full-sync', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10137', 'doFullSync', '/v1/3rdsys/internal/maxsearch/full-sync', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10138', 'doFullSyncAll', '/v1/3rdsys/internal/maxsearch/full-sync/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10139', 'getMaxSearchStats', '/v1/3rdsys/internal/maxsearch/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10140', 'checkFullSyncSummaryInfo', '/v1/3rdsys/internal/maxsearch/stats/full-sync', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10141', 'checkFullSyncProcess', '/v1/3rdsys/internal/maxsearch/stats/full-sync/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10142', 'configSyncCache', '/v1/3rdsys/internal/maxsearch/sync-cache-config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10143', 'migrationGroupFilteredAction', '/v1/3rdsys/internal/migrationGroupFilteredAction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10144', 'refreshGenResellerCertificate', '/v1/3rdsys/internal/refresh/reseller/certificate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10145', 'refreshApkParamTemplate', '/v1/3rdsys/internal/refreshApkParamTemplate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10146', 'refreshGroupActionCount', '/v1/3rdsys/internal/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10147', 'refreshPushTaskDownloadTime', '/v1/3rdsys/internal/refreshPushTaskDownloadTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10148', 'refreshResellerInstalledApks', '/v1/3rdsys/internal/refreshResellerInstalledApks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10149', 'refreshResellerPushedParamApk', '/v1/3rdsys/internal/refreshResellerPushedParamApk', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10150', 'refreshTerminalLastAccessTime', '/v1/3rdsys/internal/refreshTerminalLastAccessTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10151', 'refreshTerminalLastApkParam', '/v1/3rdsys/internal/refreshTerminalLastApkParam', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10152', 'refreshTerminalOnlineStatus', '/v1/3rdsys/internal/refreshTerminalOnlineStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10153', 'refreshTerminalStock', '/v1/3rdsys/internal/refreshTerminalStock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10154', 'refreshUserMarket', '/v1/3rdsys/internal/refreshUserMarket', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10155', 'repairActivityStatus', '/v1/3rdsys/internal/repairActivityStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10156', 'repairGroupPushTask', '/v1/3rdsys/internal/repairGroupPushTask', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10157', 'repairMerchantMigrationStatus', '/v1/3rdsys/internal/repairMerchantMigrationStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10158', 'repairReportExecutionContextStatus', '/v1/3rdsys/internal/repairReportExecutionContextStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10159', 'getSystemPropertyLog', '/v1/3rdsys/internal/system/property/log', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10160', 'findSystemProperties', '/v1/3rdsys/internal/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10161', 'createSystemProperty_1', '/v1/3rdsys/internal/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10162', 'deleteSystemProperty_1', '/v1/3rdsys/internal/systemProperty', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10163', 'resetSystemProperty', '/v1/3rdsys/internal/systemProperty/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10164', 'getTerminal_4', '/v1/3rdsys/internal/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10165', 'sendTerminalCommand', '/v1/3rdsys/internal/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10166', 'sendTerminalMessage', '/v1/3rdsys/internal/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10167', 'changeTerminalModel_1', '/v1/3rdsys/internal/terminal/model', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10168', 'getTerminalPushHistory', '/v1/3rdsys/internal/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10169', 'resetTerminal', '/v1/3rdsys/internal/terminal/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10170', 'findMerchantVariablePage_1', '/v1/3rdsys/merchant/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', 'createMerchantVariable_1', '/v1/3rdsys/merchant/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', 'batchDeleteMerchantVariables_1', '/v1/3rdsys/merchant/variables/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', 'updateMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', 'deleteMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', 'getMerchantCategories', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', 'createMerchantCategory_1', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', 'batchCreateMerchantCategories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', 'updateMerchantCategory_1', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10179', 'deleteCategory', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', 'searchMerchant', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', 'createMerchant_1', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', 'getMerchant_2', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', 'updateMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10184', 'deleteMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', 'activeMerchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', 'disableMerchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', 'replaceMerchantEmail_1', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', 'findParameterPushHistory', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', 'searchReseller_1', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', 'createReseller_1', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', 'getReseller_2', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', 'updateReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', 'deleteReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', 'activeReseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', 'disableReseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', 'replaceResellerEmail_1', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', 'searchResellerRkiKey', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', 'getTerminalBySN', '/v1/3rdsys/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', 'updateTerminalBySN', '/v1/3rdsys/terminal', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', 'deleteTerminalBySN', '/v1/3rdsys/terminal', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', 'activateTerminalBySN', '/v1/3rdsys/terminal/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', 'collectTerminalLogBySN', '/v1/3rdsys/terminal/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', 'getTerminalConfigBySN', '/v1/3rdsys/terminal/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', 'updateTerminalConfigBySN', '/v1/3rdsys/terminal/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', 'copyTerminalBySN', '/v1/3rdsys/terminal/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10206', 'disableTerminalBySN', '/v1/3rdsys/terminal/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10207', 'findTerminalGeoFenceWhiteList', '/v1/3rdsys/terminal/geofence/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10208', 'createTerminalGeoFenceWhiteList_1', '/v1/3rdsys/terminal/geofence/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', 'deleteTerminalGeoFenceWhiteList_1', '/v1/3rdsys/terminal/geofence/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', 'createTerminalsGroupBySN', '/v1/3rdsys/terminal/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', 'searchTerminalLogPageBySN', '/v1/3rdsys/terminal/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', 'getTerminalLogDownloadTaskBySN', '/v1/3rdsys/terminal/logs/{terminalLogId}/download-task', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10213', 'changeModelBySN', '/v1/3rdsys/terminal/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', 'moveTerminalBySN', '/v1/3rdsys/terminal/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10215', 'pushTerminalActionBySN', '/v1/3rdsys/terminal/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10216', 'getTerminalPedBySN', '/v1/3rdsys/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10217', 'pushTerminalMessageBySN', '/v1/3rdsys/terminal/push/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', 'getTerminalSystemUsageBySN', '/v1/3rdsys/terminal/system/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10219', 'searchTerminalApkPage', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10220', 'createTerminalApk', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10221', 'suspendTerminalApk_1', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10222', 'uninstallTerminalApk', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10223', 'getTerminalApk_1', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10224', 'deleteTerminalApk_2', '/v1/3rdsys/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10225', 'searchTerminalFmPage', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10226', 'createTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10227', 'suspendTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10228', 'deleteTerminalFm', '/v1/3rdsys/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10229', 'getTerminalFm', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10230', 'searchTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10231', 'createTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10232', 'getTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10233', 'deleteTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10234', 'suspendTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10235', 'searchTerminalGroupRkiPage', '/v1/3rdsys/terminalGroupRki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10236', 'createGroupRki_1', '/v1/3rdsys/terminalGroupRki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10237', 'getGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10238', 'suspendGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10239', 'searchGroups_2', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10240', 'createGroup_1', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10241', 'searchTerminal_2', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10242', 'getGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10243', 'updateGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10244', 'deleteGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10245', 'activeGroup', '/v1/3rdsys/terminalGroups/{groupId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10246', 'disableGroup', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10247', 'searchGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10248', 'removeGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10249', 'createGroupTerminals_2', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10250', 'searchTerminalRkiPage', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', 'createTerminalRki_1', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', 'suspendTerminalRki_1', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', 'getTerminalRki_1', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10254', 'deleteTerminalRki', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', 'findTerminalVariableList', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10256', 'createTerminalVariable_1', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', 'batchDeleteTerminalVariables_1', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', 'updateTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', 'deleteTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', 'findTerminals', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', 'createTerminal_1', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', 'activateTerminalInParam', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10263', 'copyTerminal_1', '/v1/3rdsys/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10264', 'createTerminalsGroup', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10265', 'getTerminalNetwork', '/v1/3rdsys/terminals/network', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10266', 'getTerminal_3', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10267', 'updateTerminal_2', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10268', 'deleteTerminal_4', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10269', 'activateTerminalInPath', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10270', 'collectTerminalLog', '/v1/3rdsys/terminals/{terminalId}/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', 'getTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', 'updateTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', 'disableTerminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', 'searchTerminalLogPage', '/v1/3rdsys/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', 'getTerminalLogDownloadTask', '/v1/3rdsys/terminals/{terminalId}/logs/{terminalLogId}/download-task', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', 'changeModelById', '/v1/3rdsys/terminals/{terminalId}/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10277', 'moveTerminal_1', '/v1/3rdsys/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10278', 'pushTerminalAction', '/v1/3rdsys/terminals/{terminalId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10279', 'getTerminalPed', '/v1/3rdsys/terminals/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10280', 'pushTerminalMessage', '/v1/3rdsys/terminals/{terminalId}/push/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10281', 'getTerminalSystemUsageById', '/v1/3rdsys/terminals/{terminalId}/system/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10282', 'transfRequest', '/v1/3rdsys/upt/route-request', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10283', 'updateUptrillionSecurityInfo', '/v1/3rdsys/upt/security', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10284', 'getMerchant_1', '/v1/3rdsys/uptrillion/merchant', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10285', 'getReseller_1', '/v1/3rdsys/uptrillion/reseller', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10286', 'getTerminal_2', '/v1/3rdsys/uptrillion/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10287', 'getServiceAgreement', '/v1/account/agreement/service', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10288', 'getSystemAgreement', '/v1/account/agreement/system', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10289', 'getUser_6', '/v1/account/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10290', 'getMarket_6', '/v1/account/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10291', 'resetUserPassword_2', '/v1/account/public/{userId}/reset-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10292', 'deleteAccount', '/v1/account/user', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10293', 'changeUserPwd', '/v1/account/user/change-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10294', 'sendDeleteAccountCode', '/v1/account/user/delete', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10295', 'listUserConfig', '/v1/account/user/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10296', 'updateUserConfig', '/v1/account/user/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10297', 'publishGlobalNotification', '/v1/account/user/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10298', 'listMessages_1', '/v1/account/user/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10299', 'readMessages', '/v1/account/user/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10300', 'deleteMessages', '/v1/account/user/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10301', 'readAllMessage_1', '/v1/account/user/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10302', 'deleteMessage_1', '/v1/account/user/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10303', 'downloadMessageAttachment', '/v1/account/user/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10304', 'viewMessageDetails_2', '/v1/account/user/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10305', 'listTopics', '/v1/account/user/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10306', 'unsubscribeAllTopics', '/v1/account/user/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10307', 'getTopicSubscription_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10308', 'subscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10309', 'unsubscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10310', 'getOTP', '/v1/account/user/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10311', 'activateOTP', '/v1/account/user/otp/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10312', 'disableOTP', '/v1/account/user/otp/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10313', 'bindOTP', '/v1/account/user/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10314', 'resetOtpBackupCode', '/v1/account/user/otp/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10315', 'getUserProfile', '/v1/account/user/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10316', 'updateUser', '/v1/account/user/profile', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10317', 'resetUserEmail', '/v1/account/user/reset-email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10318', 'updateAllowSendUsageData', '/v1/account/user/send-usage', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10319', 'findActivityPage', '/v1/admin/activities', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10320', 'batchDeleteActivities', '/v1/admin/activities/batch', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10321', 'getActivity', '/v1/admin/activities/{activityId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10322', 'deleteActivity', '/v1/admin/activities/{activityId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', 'searchAlarm_2', '/v1/admin/alarm', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', 'getAlarmSetting', '/v1/admin/alarm/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', 'saveAlarmSetting', '/v1/admin/alarm/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', 'searchRoles_2', '/v1/admin/alarm/setting/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', 'searchRoleUsers_1', '/v1/admin/alarm/setting/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', 'lockTerminals', '/v1/admin/alarm/terminals/lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', 'unLockTerminals', '/v1/admin/alarm/terminals/unlock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', 'findAlarmTypeList', '/v1/admin/alarm/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', 'getAlarmWidgets_1', '/v1/admin/alarm/widgets/digital', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', 'createExportAlarmDownloadTasks', '/v1/admin/alarm/widgets/{type}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', 'searchGroupPushTaskApproval', '/v1/admin/approval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', 'approvePushTask', '/v1/admin/approval/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', 'getPushTaskApprovalDetail', '/v1/admin/approval/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', 'rejectPushTask', '/v1/admin/approval/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', 'searchApps_2', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', 'getApkInfo_3', '/v1/admin/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', 'createApkDownloadTask_2', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', 'findSpecificApkMarketPage', '/v1/admin/apps/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', 'specificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', 'deleteSpecificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10343', 'findSpecificApkMarketAllListPage', '/v1/admin/apps/apks/{apkId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10344', 'createApkParamTemplateDownloadTask_2', '/v1/admin/apps/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10345', 'deleteApkParamTemplate_2', '/v1/admin/apps/apks/{apkId}/param-template', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10346', 'findSpecificApkResellerPage_3', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10347', 'specificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10348', 'deleteSpecificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10349', 'findSpecificApkResellerAllListPage_2', '/v1/admin/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', 'reSignApk_4', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', 'findApkSignatureList_4', '/v1/admin/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', 'getPendingApprovalAppCount', '/v1/admin/apps/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', 'searchAppPriceTemplates_1', '/v1/admin/apps/price/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', 'getTopicSubscription_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', 'subscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', 'unsubscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10357', 'getAppInfo_2', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10358', 'deleteApp_3', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10359', 'onlineApp', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10360', 'searchApk_3', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10361', 'deleteApk_3', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10362', 'approveApp_2', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10363', 'downloadApk', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', 'offlineApk_2', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', 'onlineApk_1', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', 'addApkParamTemplate', '/v1/admin/apps/{appId}/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', 'rejectApp_2', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', 'updateApkReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/release-note', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', 'updateApkModel', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', 'updateAppAutoUpdate', '/v1/admin/apps/{appId}/auto-update/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', 'findSpecificAppCostResellerPage', '/v1/admin/apps/{appId}/cost/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10372', 'updateSpecificAppCostReseller', '/v1/admin/apps/{appId}/cost/reseller/specific', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10373', 'specificAppCostReseller', '/v1/admin/apps/{appId}/cost/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10374', 'deleteSpecificAppCostReseller', '/v1/admin/apps/{appId}/cost/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10375', 'updateAppDeveloper', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10376', 'offlineApp', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10377', 'updateAppDownloadAuthentication', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10378', 'saveAppEntityAttributeValueList', '/v1/admin/apps/{appId}/entity-attribute-value', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10379', 'getBizDataFromGoInsight_3', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', 'findSpecificAppMarketPage', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', 'specificMarketApp', '/v1/admin/apps/{appId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', 'deleteSpecificAppMarket', '/v1/admin/apps/{appId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', 'findSpecificAppMarketAllListPage', '/v1/admin/apps/{appId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', 'findSpecificAppMerchantCategoryPage', '/v1/admin/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', 'specificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', 'deleteSpecificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', 'findSpecificAppResellerPage_2', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10388', 'specificAppReseller_2', '/v1/admin/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10389', 'deleteSpecificResellerApp_1', '/v1/admin/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10390', 'findSpecificAppResellerAllListPage_2', '/v1/admin/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10391', 'resumeApp', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10392', 'getAppSettingVo_2', '/v1/admin/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10393', 'getAppVasSettingVo_3', '/v1/admin/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10394', 'updateAppVisualScope', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10395', 'searchAuthLog', '/v1/admin/audit-log/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10396', 'exportAuthLog', '/v1/admin/audit-log/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10397', 'searchOperationLog', '/v1/admin/audit-log/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10398', 'exportAuditLog', '/v1/admin/audit-log/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10399', 'getAuditLogParamDetail', '/v1/admin/audit-log/operations/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10400', 'getExistAuditTypes', '/v1/admin/audit-log/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', 'findClientApp', '/v1/admin/client-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', 'findClientApk', '/v1/admin/client-apps-approval/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10403', 'findClientAppFactory', '/v1/admin/client-apps-approval/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10404', 'findFirmwares', '/v1/admin/client-apps-approval/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10405', 'approveClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10406', 'updateClientApkModel', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/model/update', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10407', 'offlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10408', 'onlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10409', 'rejectClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10410', 'getClientApk', '/v1/admin/client-apps-common/client-apks/{clientApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10411', 'createClientApkDownloadTask', '/v1/admin/client-apps-common/client-apks/{clientApkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10412', 'deleteClientApk', '/v1/admin/client-apps-common/{clientAppId}/client-apks/{clientApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10413', 'findClientApkFirmwarePage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10414', 'createClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10415', 'updateClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10416', 'removeClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10417', 'findClientMarketPage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10418', 'getClientMarketSummary', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10419', 'saveClientPublishAmount', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-amount', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10420', 'addGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10421', 'removeGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10422', 'updateClientApkPublishRange', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-range', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10423', 'findSpecificApkResellerPage_2', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10424', 'specificClientApkReseller', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10425', 'deleteSpecificClientApkReseller', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10426', 'findSpecificClientApkResellerAllListPage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/resellers/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10427', 'getClientApp', '/v1/admin/client-apps/{clientAppId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10428', 'findClientApkByAppId', '/v1/admin/client-apps/{clientAppId}/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10429', 'addNewClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10430', 'updateClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10431', 'updateClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10432', 'submitClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10433', 'addFistClientApkFile', '/v1/admin/client-apps/{factoryId}/client-apks/file/first', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10434', 'getIOTPlatformAccessUrl', '/v1/admin/cloudservice/iot/access/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10435', 'ping', '/v1/admin/cloudservice/refresh/ping', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10436', 'getUrl_1', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10437', 'findCountryProvinceCity', '/v1/admin/common/administrative/region', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10438', 'findApkParameterPage_1', '/v1/admin/common/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10439', 'createApkParameterDataFileDownloadTask_1', '/v1/admin/common/app/apk/parameters/{apkParameterId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10440', 'getApkParameterSchemaInfo', '/v1/admin/common/app/apk/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10441', 'downloadReleaseNoteTask', '/v1/admin/common/app/apk/{apkId}/release-note/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10442', 'getApkDetailVo_4', '/v1/admin/common/app/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10443', 'findOnlineAppPage_1', '/v1/admin/common/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10444', 'findEntityAttributePage_1', '/v1/admin/common/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10445', 'getBoundariesGeofencing', '/v1/admin/common/boundaries/geofencing', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10446', 'findLatestOnlineClientApkFactoryList', '/v1/admin/common/client-app/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10447', 'findFactoryPage_1', '/v1/admin/common/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10448', 'findFactoryModelTree', '/v1/admin/common/factory/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10449', 'checkFile', '/v1/admin/common/file/check-file', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10450', 'uploadPart', '/v1/admin/common/file/chunk-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10451', 'findFirmwarePage', '/v1/admin/common/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10452', 'createTerminalFirmwareFileDownloadTask', '/v1/admin/common/firmwares/file/{fmFileId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10453', 'getFirmwareDetailVo_2', '/v1/admin/common/firmwares/{firmwareId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10454', 'findGeofenceTemplatePage_1', '/v1/admin/common/geofence/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10455', 'searchMarkets_2', '/v1/admin/common/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10456', 'findMerchantCategoryPage_1', '/v1/admin/common/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10457', 'findMerchantPage', '/v1/admin/common/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10458', 'findModelPage_3', '/v1/admin/common/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10459', 'findRkiTemplateKeyPage', '/v1/admin/common/reseller/{resellerId}/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10460', 'findResellerPage', '/v1/admin/common/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10461', 'findResellerTreePage', '/v1/admin/common/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10462', 'getMarket_5', '/v1/admin/current-market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10463', 'getUser_5', '/v1/admin/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10464', 'searchPendingApps_1', '/v1/admin/dashboard/apps-pending', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10465', 'searchAppsTop10_1', '/v1/admin/dashboard/apps-top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10466', 'getDashboardLayout', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10467', 'saveDashboard', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10468', 'searchMarkers_2', '/v1/admin/dashboard/map-markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10469', 'searchTerminalsByPlace_1', '/v1/admin/dashboard/map-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10470', 'getResellerProfile_2', '/v1/admin/dashboard/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10471', 'getTerminalNumberStatisticData_2', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10472', 'getTerminalNumberOfResellerData_2', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10473', 'getFmTerminalForWidget_1', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10474', 'exportFmTerminalOrgWidget_1', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10475', 'getClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10476', 'exportClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10477', 'loadWidgetModelTerminal_2', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10478', 'exportModelTerminalWidget_1', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10479', 'loadWidgetTerminalOffline_2', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10480', 'exportTerminalOfflineWidget_1', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10481', 'getFmTerminalWidget_1', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10482', 'exportFmTerminalWidget_1', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10483', 'loadClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10484', 'downloadClientTerminalWidget_1', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10485', 'createExportTerminalsDownloadTask_2', '/v1/admin/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10486', 'getWidgetCardNumberActive_1', '/v1/admin/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10487', 'getWidgetDigitalDisplaySetting_1', '/v1/admin/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10488', 'updateWidgetDigitalDisplay', '/v1/admin/dashboard/widgets/W20/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10489', 'getPukTerminalWidget_1', '/v1/admin/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10490', 'exportPUKTerminalWidget_1', '/v1/admin/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10491', 'findPageList_1', '/v1/admin/datasource/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10492', 'createDataSourceInfo', '/v1/admin/datasource/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10493', 'testConnection', '/v1/admin/datasource/info/testConnection', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10494', 'findById_1', '/v1/admin/datasource/info/{dataSourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10495', 'deleteById_1', '/v1/admin/datasource/info/{dataSourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10496', 'findPageList', '/v1/admin/datasource/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10497', 'createDataSourceMarket', '/v1/admin/datasource/market', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10498', 'findById', '/v1/admin/datasource/market/{configId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10499', 'deleteById', '/v1/admin/datasource/market/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10500', 'searchDevelopers', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10501', 'exportDevelopers', '/v1/admin/developers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10502', 'getDeveloper_2', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10503', 'updateDeveloper', '/v1/admin/developers/{developerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10504', 'deleteDeveloper_1', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10505', 'activeDeveloper3rdSysAccess_1', '/v1/admin/developers/{developerId}/3rd-system/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10506', 'disableDeveloper3rdSysAccess', '/v1/admin/developers/{developerId}/3rd-system/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10507', 'approveDeveloper_1', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10508', 'updateAllowIndustrySolution', '/v1/admin/developers/{developerId}/industry-solution', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10509', 'approveGlobalDeveloperPayment', '/v1/admin/developers/{developerId}/pay/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10510', 'rejectDeveloperPayment', '/v1/admin/developers/{developerId}/pay/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10511', 'rejectDeveloper_1', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10512', 'specificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10513', 'changeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10514', 'closeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10515', 'resumeDeveloper_1', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10516', 'findDeveloperSandboxTerminalPage', '/v1/admin/developers/{developerId}/sandbox/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10517', 'deleteSandboxTerminal', '/v1/admin/developers/{developerId}/sandbox/terminal/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10518', 'findDeveloperServices', '/v1/admin/developers/{developerId}/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10519', 'updateDeveloperSuperAdmin_2', '/v1/admin/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10520', 'suspendDeveloper_1', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10521', 'activeDeveloperUser', '/v1/admin/developers/{developerId}/user/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10522', 'findEnterpriseDeveloperUserPage', '/v1/admin/developers/{developerId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10523', 'findEmmAppPage', '/v1/admin/emm/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10524', 'getApkInfo_2', '/v1/admin/emm/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10525', 'createApkDownloadTask_1', '/v1/admin/emm/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10526', 'updateEmmAppConfig', '/v1/admin/emm/apps/config/{configId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10527', 'removeEmmAppConfig', '/v1/admin/emm/apps/config/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10528', 'createEmmApp', '/v1/admin/emm/apps/create', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10529', 'findEmmAppSubscriptionPage', '/v1/admin/emm/apps/subscription', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10530', 'createWebToken', '/v1/admin/emm/apps/webToken', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10531', 'getEmmAppDetail', '/v1/admin/emm/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10532', 'removeEmmApp', '/v1/admin/emm/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10533', 'searchApk_2', '/v1/admin/emm/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10534', 'approveApp_1', '/v1/admin/emm/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10535', 'promoteTestRelease2Production', '/v1/admin/emm/apps/{appId}/apks/{apkId}/promote', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10536', 'rejectApp_1', '/v1/admin/emm/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10537', 'updateAppKey_2', '/v1/admin/emm/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10538', 'listAvailableTestVersionApks', '/v1/admin/emm/apps/{appId}/availableTestVersions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10539', 'findEmmAppConfigPage', '/v1/admin/emm/apps/{appId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10540', 'createEmmAppConfig', '/v1/admin/emm/apps/{appId}/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10541', 'findEmmAppConfigList', '/v1/admin/emm/apps/{appId}/configList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10542', 'retrieveEmmAppPerms', '/v1/admin/emm/apps/{appId}/permissions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10543', 'getAppSettings', '/v1/admin/emm/apps/{appId}/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10544', 'updateAppSettings', '/v1/admin/emm/apps/{appId}/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10545', 'subscribeEmmApp_1', '/v1/admin/emm/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10546', 'unSubscribeEmmApp_1', '/v1/admin/emm/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10547', 'getMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10548', 'createMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10549', 'syncMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}/sync', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10550', 'getResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10551', 'createResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10552', 'syncResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}/sync', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10553', 'findFactoryPage', '/v1/admin/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10554', 'createFactory', '/v1/admin/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10555', 'getSignatureProviderList', '/v1/admin/factories/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10556', 'getFactoryDetail', '/v1/admin/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10557', 'updateFactory', '/v1/admin/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10558', 'deleteFactory', '/v1/admin/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10559', 'findSpecificFactoryMarketPage', '/v1/admin/factories/{factoryId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10560', 'specificFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10561', 'deleteFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10562', 'findSpecificFactoryMarketAllList', '/v1/admin/factories/{factoryId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10563', 'searchFirmware_2', '/v1/admin/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10564', 'searchFirmware_3', '/v1/admin/firmwares-approval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10565', 'listFirmwareFactory', '/v1/admin/firmwares-approval/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10566', 'findPendingStatistic', '/v1/admin/firmwares-approval/pendingStatistic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10567', 'getFirmware_1', '/v1/admin/firmwares-approval/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10568', 'approveFirmware', '/v1/admin/firmwares-approval/{firmwareId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10569', 'specificMarketFirmware', '/v1/admin/firmwares-approval/{firmwareId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10570', 'findSpecificFirmwareMarketAllPage', '/v1/admin/firmwares-approval/{firmwareId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10571', 'updateFirmwareModel', '/v1/admin/firmwares-approval/{firmwareId}/model/update', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10572', 'offlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10573', 'onlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10574', 'rejectFirmware', '/v1/admin/firmwares-approval/{firmwareId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10575', 'specificResellerFirmware_2', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10576', 'findFirmwareResellerSpecificAllPage', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10577', 'deleteFirmwareDiff', '/v1/admin/firmwares-common/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10578', 'deleteFirmware', '/v1/admin/firmwares-common/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10579', 'uploadFirmwareDiffFile', '/v1/admin/firmwares-common/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10580', 'uploadFirmwareFile', '/v1/admin/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10581', 'getFirmware', '/v1/admin/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10582', 'updateFirmware', '/v1/admin/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10583', 'getFirmwareForEditPage', '/v1/admin/firmwares/{firmwareId}/edit-page', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10584', 'submitFirmware', '/v1/admin/firmwares/{firmwareId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10585', 'searchMarkers_1', '/v1/admin/geo-location/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10586', 'searchMarket', '/v1/admin/geo-location/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10587', 'getMarket_4', '/v1/admin/geo-location/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10588', 'getResellerProfile_1', '/v1/admin/geo-location/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10589', 'searchReseller', '/v1/admin/geo-location/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10590', 'getReseller', '/v1/admin/geo-location/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10591', 'searchTerminalsByPlace', '/v1/admin/geo-location/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10592', 'exportTerminalsByPlace', '/v1/admin/geo-location/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10593', 'getTerminalDetail', '/v1/admin/geo-location/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10594', 'findGeofenceTemplatePage', '/v1/admin/geofence/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10595', 'createGeofenceTemplate', '/v1/admin/geofence/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10596', 'getGeofenceTemplate', '/v1/admin/geofence/templates/{geofenceTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10597', 'updateGeofenceTemplate', '/v1/admin/geofence/templates/{geofenceTemplateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10598', 'deleteLauncherTemplate_1', '/v1/admin/geofence/templates/{geofenceTemplateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10599', 'searchMarkets_1', '/v1/admin/global/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10600', 'createMarket', '/v1/admin/global/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10601', 'createMarketApiAccFreq', '/v1/admin/global/markets/api/access/frequency', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10602', 'updateMarketApiAccFreq', '/v1/admin/global/markets/api/access/frequency/{accFreqId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10603', 'findRkiServerList', '/v1/admin/global/markets/rki-servers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10604', 'getMarketInfoSummary', '/v1/admin/global/markets/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10605', 'findAppNumDetail', '/v1/admin/global/markets/statistics/app-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10606', 'findDeveloperNumDetail', '/v1/admin/global/markets/statistics/developer-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10607', 'createExportStatisticsDownloadTask', '/v1/admin/global/markets/statistics/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10608', 'findMarketNumDetail', '/v1/admin/global/markets/statistics/market-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10609', 'sendMarketTerminalReport', '/v1/admin/global/markets/terminal-report', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10610', 'getMarket_3', '/v1/admin/global/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10611', 'updateMarket', '/v1/admin/global/markets/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10612', 'deleteMarket', '/v1/admin/global/markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10613', 'findMarketApiAccFreq', '/v1/admin/global/markets/{marketId}/api/access/frequency', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10614', 'updateMarketBillingSetting', '/v1/admin/global/markets/{marketId}/billing', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10615', 'getMarketIconStatus', '/v1/admin/global/markets/{marketId}/icon-status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10616', 'updateOverdueMarket', '/v1/admin/global/markets/{marketId}/overdue', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10617', 'getMarketPermissions', '/v1/admin/global/markets/{marketId}/permissions/{functionType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10618', 'updateMarketBillingPriceSetting', '/v1/admin/global/markets/{marketId}/price-setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10619', 'findMarketBillingPriceSettings', '/v1/admin/global/markets/{marketId}/price-settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10620', 'replaceMarketResellerEmail', '/v1/admin/global/markets/{marketId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10621', 'sendMarketActivateEmail', '/v1/admin/global/markets/{marketId}/resend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10622', 'resumeMarket', '/v1/admin/global/markets/{marketId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10623', 'getMarketServiceSetting', '/v1/admin/global/markets/{marketId}/service-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10624', 'getMarketSummary', '/v1/admin/global/markets/{marketId}/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10625', 'suspendMarket', '/v1/admin/global/markets/{marketId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10626', 'getReportMetadataForCreateAndUpdate', '/v1/admin/global/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10627', 'getReport_1', '/v1/admin/global/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10628', 'updateReport', '/v1/admin/global/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10629', 'activateReport', '/v1/admin/global/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10630', 'createDownloadBandFileTask', '/v1/admin/global/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10631', 'disableReport', '/v1/admin/global/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10632', 'createDownloadTaskForTemplateFile', '/v1/admin/global/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10633', 'findLauncherTemplatePage', '/v1/admin/launcher/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10634', 'createLauncherTemplate', '/v1/admin/launcher/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10635', 'findResellerOnlineAppPage', '/v1/admin/launcher/templates/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10636', 'getResellerOnlineApkNameAndIcon_1', '/v1/admin/launcher/templates/reseller/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10637', 'getLauncherTemplate_2', '/v1/admin/launcher/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10638', 'updateLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10639', 'deleteLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10640', 'updateLauncherTemplateName', '/v1/admin/launcher/templates/{launcherTemplateId}/rename', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10641', 'activeMarket3rdSysAccess', '/v1/admin/market/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10642', 'getMarket3rdSysConfig', '/v1/admin/market/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10643', 'find3rdSysConfigIpPage_1', '/v1/admin/market/3rd-sys/config/ip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10644', 'deActiveMarket3rdSysAccess', '/v1/admin/market/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10645', 'add3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10646', 'update3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10647', 'delete3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10648', 'getMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10649', 'resetMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10650', 'find3rdSysWebHookPage_1', '/v1/admin/market/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10651', 'create3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10652', 'getWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10653', 'get3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10654', 'update3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', 'delete3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', 'findWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', 'test3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', 'findAnnouncementPage', '/v1/admin/market/advance/announcement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', 'createAnnouncementNotification', '/v1/admin/market/advance/announcement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', 'publishAnnouncementNotification', '/v1/admin/market/advance/announcement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', 'getAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', 'updateAnnouncementNotification', '/v1/admin/market/advance/announcement/{announcementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', 'deleteAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10664', 'findAppWhiteListPage_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', 'createAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', 'deleteAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', 'findPage', '/v1/admin/market/advance/ip-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', 'createAccessIp', '/v1/admin/market/advance/ip-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', 'getAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', 'closeAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10671', 'openAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/open', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10672', 'updateAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', 'deleteAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', 'getMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', 'createMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', 'deleteSensitiveWordId', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', 'createSensitiveWordFileDownloadTask', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', 'findAppBlackListPage_1', '/v1/admin/market/app-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', 'updateAppBlackList', '/v1/admin/market/app-blacklist', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', 'createAppBlackList', '/v1/admin/market/app-blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10681', 'deleteAppBlackList', '/v1/admin/market/app-blacklist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', 'findAppWhiteListPage', '/v1/admin/market/app-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', 'createAppWhiteList', '/v1/admin/market/app-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', 'deleteAppWhiteList', '/v1/admin/market/app-whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', 'findEntityAttributePage', '/v1/admin/market/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', 'createEntityAttribute', '/v1/admin/market/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', 'getEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10688', 'updateEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10689', 'deleteEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10690', 'updateEntityAttributeLabel', '/v1/admin/market/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10691', 'updateBillingServicePrice_1', '/v1/admin/market/billing/change/price/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', 'getCurrentBilling', '/v1/admin/market/billing/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10693', 'updateMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', 'getMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10695', 'downloadGlobalTimePeriodBilling', '/v1/admin/market/billing/global/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10696', 'loadGlobalSingleMonthBill', '/v1/admin/market/billing/global/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', 'loadTimePeriodBilling', '/v1/admin/market/billing/global/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10698', 'loadTotalBillItemTimePeriod', '/v1/admin/market/billing/global/total/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10699', 'getUnreceivedAmount', '/v1/admin/market/billing/global/unreceived/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10700', 'findGlobalUnresolvedInvoices', '/v1/admin/market/billing/global/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', 'reRunBillingJob', '/v1/admin/market/billing/job', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', 'findOperationLog', '/v1/admin/market/billing/log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', 'findBillingServiceDetailLog', '/v1/admin/market/billing/log/detail/{batchId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', 'downloadPaymentBillHistory', '/v1/admin/market/billing/market/{marketId}/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10705', 'getMarketSingleMonthBilling', '/v1/admin/market/billing/market/{marketId}/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10706', 'getMarketTimePeriodBilling', '/v1/admin/market/billing/market/{marketId}/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10707', 'searchPayment', '/v1/admin/market/billing/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10708', 'checkPaymentStatus', '/v1/admin/market/billing/payment/check', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', 'confirmPayBilling', '/v1/admin/market/billing/payment/confirm', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10710', 'initPayment', '/v1/admin/market/billing/payment/init', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10711', 'sendBillingInvoice', '/v1/admin/market/billing/send/invoice/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10712', 'getMarketInvoice', '/v1/admin/market/billing/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10713', 'updateMarketInvoice', '/v1/admin/market/billing/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', 'findBillingReceiveEmailList', '/v1/admin/market/billing/settings/email', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', 'createReceiveEmail', '/v1/admin/market/billing/settings/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', 'updateReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', 'deleteReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', 'getSingleMonthBilling', '/v1/admin/market/billing/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', 'updateBillingStatusToAudit', '/v1/admin/market/billing/status/audit/{billingSummaryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', 'loadSummaryBillItemTimePeriod', '/v1/admin/market/billing/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', 'getUnPaidAmountPayable', '/v1/admin/market/billing/unPaid/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', 'findHistoryUnresolvedInvoices', '/v1/admin/market/billing/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', 'findMerchantCategoryPage', '/v1/admin/market/merchant-categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', 'createMerchantCategory', '/v1/admin/market/merchant-categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', 'batchDeleteMerchantCategories', '/v1/admin/market/merchant-categories/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10726', 'importMerchantCategory', '/v1/admin/market/merchant-categories/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10727', 'createMerchantCategoryImportTemplateDownloadTask', '/v1/admin/market/merchant-categories/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10728', 'updateMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10729', 'deleteMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10730', 'specificModel2Market', '/v1/admin/market/model/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10731', 'findFactoryIncludeModelPage', '/v1/admin/market/model/settings/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10732', 'findModelPage_2', '/v1/admin/market/model/settings/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10733', 'getMarketSetting', '/v1/admin/market/settings', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10734', 'saveMarketSettings', '/v1/admin/market/settings', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10735', 'activateMarket', '/v1/admin/market/settings/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10736', 'findAgreementPage', '/v1/admin/market/settings/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10737', 'createAgreement', '/v1/admin/market/settings/agreement', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10738', 'findAgreementSettingPage', '/v1/admin/market/settings/agreement/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10739', 'saveAgreementSettings', '/v1/admin/market/settings/agreement/config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10740', 'publishAgreement', '/v1/admin/market/settings/agreement/publish', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10741', 'updateAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10742', 'deleteAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10743', 'exportAgreementAgreedRecords', '/v1/admin/market/settings/agreement/{agreementId}/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10744', 'findFooter_1', '/v1/admin/market/settings/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10745', 'createMarketFooter', '/v1/admin/market/settings/footer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10746', 'updateMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10747', 'deleteMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10748', 'sortMarketFooter', '/v1/admin/market/settings/footer/{footerId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10749', 'getMarketLimitConfig', '/v1/admin/market/settings/limitconfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10750', 'findRkiServerPage', '/v1/admin/market/settings/rki-servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10751', 'getTIDSettings', '/v1/admin/market/settings/tid', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10752', 'updateTIDSetting', '/v1/admin/market/settings/tid', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10753', 'getMarketUiSettings_1', '/v1/admin/market/settings/ui', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10754', 'saveMarketUiSettings', '/v1/admin/market/settings/ui', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10755', 'saveMarketUiAdvanceSettings', '/v1/admin/market/settings/ui/advance', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10756', 'deleteMarketUiAdvanceSettings', '/v1/admin/market/settings/ui/advance', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10757', 'findOnlineAppPageForFeatured', '/v1/admin/market/settings/ui/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10758', 'findFeaturedApp_1', '/v1/admin/market/settings/ui/apps/featured', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10759', 'updateFeaturedAppSort', '/v1/admin/market/settings/ui/apps/featured/{featuredAppId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10760', 'addFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10761', 'deleteFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10762', 'updateFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured/{featuredAppId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10763', 'getSignatureSetting', '/v1/admin/market/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10764', 'saveSignatureSetting', '/v1/admin/market/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10765', 'clearSignatureData', '/v1/admin/market/signature/data/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10766', 'findSignatureFactoryPage', '/v1/admin/market/signature/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10767', 'testSignatureConfigServer_1', '/v1/admin/market/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10768', 'getSsoSetting', '/v1/admin/market/sso/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10769', 'updateSsoSetting', '/v1/admin/market/sso/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10770', 'searchRoles_1', '/v1/admin/market/sso/settings/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10771', 'getTerminalBlacklist', '/v1/admin/market/terminal-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10772', 'importTerminalBlacklist', '/v1/admin/market/terminal-blacklist/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10773', 'createTerminalImportTemplateDownloadTask_2', '/v1/admin/market/terminal-blacklist/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10774', 'deleteTerminalBlacklist', '/v1/admin/market/terminal-blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10775', 'getTerminalWhiteList', '/v1/admin/market/terminal-whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10776', 'importAddTerminalWhiteList', '/v1/admin/market/terminal-whiteList/import/add', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10777', 'importDeleteTerminalWhiteList', '/v1/admin/market/terminal-whiteList/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10778', 'createTerminalImportTemplateDownloadTask_1', '/v1/admin/market/terminal-whiteList/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10779', 'deleteTerminalWhiteList', '/v1/admin/market/terminal-whiteList/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10780', 'findMarketVariablePage', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10781', 'createMarketVariable', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10782', 'batchDeleteTerminalVariables_2', '/v1/admin/market/variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10783', 'importMarketVariable', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10784', 'createMarketVariableImportTemplateDownloadTask', '/v1/admin/market/variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10785', 'findMarketVariableRelatedAppPage', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10786', 'findMarketVariableUsedAppPage', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10787', 'updateMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10788', 'deleteMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10789', 'searchUsers_2', '/v1/admin/merchant/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10790', 'createExportUserDownloadTask_1', '/v1/admin/merchant/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10791', 'activeUser_1', '/v1/admin/merchant/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10792', 'sendActivateUserEmail_1', '/v1/admin/merchant/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10793', 'getUserMerchants', '/v1/admin/merchant/users/{userId}/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10794', 'resetUserPassword_1', '/v1/admin/merchant/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10795', 'findModelPage_1', '/v1/admin/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10796', 'createModel', '/v1/admin/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10797', 'getModelDetail', '/v1/admin/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10798', 'updateModel', '/v1/admin/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10799', 'deleteModel', '/v1/admin/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10800', 'findProtectedOperations', '/v1/admin/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10801', 'getUser_4', '/v1/admin/operations/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10802', 'getProtectedOperation', '/v1/admin/operations/{key}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10803', 'closeOperation', '/v1/admin/operations/{key}/close', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10804', 'openOperation', '/v1/admin/operations/{key}/open', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10805', 'closeResellerOperationControl', '/v1/admin/operations/{key}/reseller/operation/close', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10806', 'openResellerOperationControl', '/v1/admin/operations/{key}/reseller/operation/open', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10807', 'getOperationUsers', '/v1/admin/operations/{key}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10808', 'addOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10809', 'removeOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10810', 'getCodeLangConfigs', '/v1/admin/platform/configuration/codes/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10811', 'saveCodeLangConfig', '/v1/admin/platform/configuration/codes/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10812', 'getCodeTypes', '/v1/admin/platform/configuration/codes/setting/codeTypes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10813', 'getCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10814', 'deleteCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10815', 'getLicense_1', '/v1/admin/platform/configuration/license', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10816', 'updateLicense', '/v1/admin/platform/configuration/license', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10817', 'loadLoginSettings', '/v1/admin/platform/configuration/login-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10818', 'savePwdPolicy_1', '/v1/admin/platform/configuration/login-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10819', 'loadMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10820', 'saveMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10821', 'testMailServiceConfig', '/v1/admin/platform/configuration/mail-config/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10822', 'listAll', '/v1/admin/platform/configuration/oauth-client', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10823', 'add', '/v1/admin/platform/configuration/oauth-client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10824', 'get', '/v1/admin/platform/configuration/oauth-client/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10825', 'update', '/v1/admin/platform/configuration/oauth-client/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10826', 'delete_1', '/v1/admin/platform/configuration/oauth-client/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10827', 'loadPwdPolicy', '/v1/admin/platform/configuration/password-policy', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10828', 'savePwdPolicy', '/v1/admin/platform/configuration/password-policy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10829', 'searchReleaseNoteInfos', '/v1/admin/platform/configuration/release-note', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10830', 'createReleaseNoteInfo', '/v1/admin/platform/configuration/release-note', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10831', 'createMailTemplateDownloadTask', '/v1/admin/platform/configuration/release-note/mail/template/{releaseNoteInfoId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10832', 'getReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10833', 'updateReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10834', 'downloadEmails', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/download-emails', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10835', 'sendMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/send-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10836', 'testMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/test-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10837', 'finsRkiServerList', '/v1/admin/platform/configuration/rki-server', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10838', 'createRkiServerSetting', '/v1/admin/platform/configuration/rki-server', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10839', 'testSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10840', 'getRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10841', 'updateRkiServerSetting', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10842', 'deleteRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10843', 'testExistSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/{rkiId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10844', 'searchDiscountTerminals', '/v1/admin/platform/internal/discount/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10845', 'importMerchant_1', '/v1/admin/platform/internal/discount/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10846', 'searchPredefinedRoles', '/v1/admin/platform/internal/predefined-roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10847', 'searchPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10848', 'removePredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10849', 'createPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10850', 'searchPrivileges', '/v1/admin/platform/internal/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10851', 'createPrivilege', '/v1/admin/platform/internal/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10852', 'getPrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10853', 'updatePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10854', 'deletePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10855', 'searchPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10856', 'removePrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10857', 'createPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10858', 'searchSystemProperties', '/v1/admin/platform/internal/properties', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10859', 'createSystemProperty', '/v1/admin/platform/internal/properties', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10860', 'getSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10861', 'updateSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10862', 'deleteSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10863', 'getPushDiagnosisResult', '/v1/admin/platform/internal/push-diagnosis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10864', 'sendPushDiagnosisTest', '/v1/admin/platform/internal/push-diagnosis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10865', 'searchResources', '/v1/admin/platform/internal/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10866', 'createResource', '/v1/admin/platform/internal/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10867', 'getResource', '/v1/admin/platform/internal/resources/{resourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10868', 'updateResource', '/v1/admin/platform/internal/resources/{resourceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10869', 'deleteResource', '/v1/admin/platform/internal/resources/{resourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10870', 'searchPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', 'removePrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10872', 'createPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', 'triggerScheduleJob', '/v1/admin/platform/internal/schedule-job', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', 'searchResellerMigrations', '/v1/admin/platform/migration/reseller-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10875', 'createResellerMigration', '/v1/admin/platform/migration/reseller-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10876', 'getResellerMigration', '/v1/admin/platform/migration/reseller-migrations/{resellerMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10877', 'searchAppPriceTemplates', '/v1/admin/price/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10878', 'createPriceTemplate', '/v1/admin/price/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10879', 'updatePriceTemplate', '/v1/admin/price/templates/{templateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10880', 'deletePriceTemplate', '/v1/admin/price/templates/{templateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10881', 'getProductList', '/v1/admin/products', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10882', 'findProfileSettingList', '/v1/admin/products/profiles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10883', 'saveProfileSettingList', '/v1/admin/products/profiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', 'getProductDetail', '/v1/admin/products/{codeValue}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10885', 'getProductProfile', '/v1/admin/products/{codeValue}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10886', 'getProductService', '/v1/admin/products/{codeValue}/service', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10887', 'getProductSetting', '/v1/admin/products/{codeValue}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10888', 'exportParameters', '/v1/admin/push/template/export/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10889', 'createExportApkParameterCompareDownloadTask_1', '/v1/admin/push/template/export/parameters/comparison', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10890', 'findParamAppPage', '/v1/admin/push/template/param-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10891', 'findParamSolutionAppPage', '/v1/admin/push/template/param-solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10892', 'findApkParametersPage', '/v1/admin/push/template/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10893', 'createApkParameter', '/v1/admin/push/template/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10894', 'findApkParameterAppPage', '/v1/admin/push/template/parameters/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10895', 'batchDeleteApkParameter', '/v1/admin/push/template/parameters/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10896', 'findApkParameterComparePage', '/v1/admin/push/template/parameters/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10897', 'getApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10898', 'updateApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10899', 'deleteApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10900', 'updateApkParameterFormData', '/v1/admin/push/template/parameters/{apkParameterId}/data', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10901', 'createApkParameterDataFileDownloadTask', '/v1/admin/push/template/parameters/{apkParameterId}/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10902', 'getApkParameterSchema', '/v1/admin/push/template/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10903', 'searchReport', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10904', 'getMerchantByResellerIds', '/v1/admin/report/data-source/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10905', 'searchInstalledPUKList', '/v1/admin/report/data-source/puk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10906', 'getResellersByMarketId', '/v1/admin/report/data-source/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10907', 'searchReportApkList', '/v1/admin/report/data-source/{reportId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10908', 'refreshParameterSourceItems', '/v1/admin/report/parameter/{parameterId}/source/refresh', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10909', 'deleteReportExecution_1', '/v1/admin/report/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10910', 'getReportJobHistoryPage', '/v1/admin/report/reportJobHistory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10911', 'createDownloadTaskForReport', '/v1/admin/report/reportTask/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10912', 'updateReportTasksStatus', '/v1/admin/report/reportTask/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10913', 'updateReportTaskStatus', '/v1/admin/report/reportTask/{reportExecutionContextId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10914', 'getReportTaskByPage', '/v1/admin/report/reportTask/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10915', 'deleteReportExecution', '/v1/admin/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10916', 'getReport', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10917', 'findReportDynamicFields', '/v1/admin/report/{reportId}/dynamic-fields', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10918', 'createImmediateReportExecution', '/v1/admin/report/{reportId}/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10919', 'createScheduledReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10920', 'getReportExecutionContext', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10921', 'updateReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10922', 'activeReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10923', 'getReseller3rdSysConfig', '/v1/admin/reseller/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10924', 'find3rdSysConfigIpPage', '/v1/admin/reseller/3rd-sys/config/ip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10925', 'deActiveReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10926', 'addReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10927', 'updateReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10928', 'deleteReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10929', 'getReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10930', 'resetReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10931', 'find3rdSysWebHookPage', '/v1/admin/reseller/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10932', 'create3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10933', 'getWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10934', 'get3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10935', 'update3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10936', 'delete3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10937', 'findWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10938', 'test3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10939', 'findResellerOnlineApps', '/v1/admin/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10940', 'getApkInfo_1', '/v1/admin/reseller/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10941', 'reSignApk_3', '/v1/admin/reseller/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10942', 'findApkSignatureList_3', '/v1/admin/reseller/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10943', 'findSpecificApkResellerPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10944', 'specificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10945', 'deleteSpecificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10946', 'findSpecificApkResellerAllListPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10947', 'getTopicSubscription_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10948', 'subscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10949', 'unsubscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10950', 'getAppInfo_1', '/v1/admin/reseller/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10951', 'searchApk_1', '/v1/admin/reseller/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10952', 'getBizDataFromGoInsight_2', '/v1/admin/reseller/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10953', 'getAppMerchantCategory', '/v1/admin/reseller/apps/{appId}/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10954', 'getAppSettingVo_1', '/v1/admin/reseller/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10955', 'findSpecificAppResellerPage_1', '/v1/admin/reseller/apps/{appId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10956', 'specificAppReseller_1', '/v1/admin/reseller/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10957', 'deleteSpecificResellerApp', '/v1/admin/reseller/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10958', 'findSpecificAppResellerAllListPage_1', '/v1/admin/reseller/apps/{appId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10959', 'getAppVasSettingVo_2', '/v1/admin/reseller/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10960', 'searchFirmware_1', '/v1/admin/reseller/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10961', 'getTopicSubscription_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10962', 'subscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10963', 'unsubscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10964', 'getFirmwareDetailVo_1', '/v1/admin/reseller/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10965', 'specificResellerFirmware_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10966', 'findSpecificFirmwareResellerAllListPage_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10967', 'getResellerRki_1', '/v1/admin/reseller/rki/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10968', 'refreshResellerRkiKeys_1', '/v1/admin/reseller/rki/settings/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10969', 'saveResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10970', 'deleteResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10971', 'getResellerTIDSettings', '/v1/admin/reseller/settings/tid', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10972', 'updateResellerTIDSetting', '/v1/admin/reseller/settings/tid', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10973', 'getMarketUiSettings', '/v1/admin/reseller/settings/ui', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10974', 'saveResellerUISettings', '/v1/admin/reseller/settings/ui', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10975', 'saveResellerUIAdvanceSettings', '/v1/admin/reseller/settings/ui/advance', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10976', 'deleteResellerUIAdvanceSettings', '/v1/admin/reseller/settings/ui/advance', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10977', 'saveResellerSignatureSetting', '/v1/admin/reseller/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10978', 'searchRoles', '/v1/admin/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10979', 'createRole', '/v1/admin/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10980', 'searchUsers_1', '/v1/admin/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10981', 'getRole', '/v1/admin/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10982', 'updateRole', '/v1/admin/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10983', 'deleteRole', '/v1/admin/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10984', 'searchRoleUsers', '/v1/admin/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10985', 'addRoleUsers', '/v1/admin/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10986', 'removeRoleUsers', '/v1/admin/roles/{roleId}/users', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10987', 'findSolutionAppPage', '/v1/admin/service/industry-solution/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10988', 'reSignApk_2', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10989', 'findApkSignatureList_2', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10990', 'findSolutionAppIntroductionPage', '/v1/admin/service/industry-solution/apps/introduction', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10991', 'getSolutionAppDetail', '/v1/admin/service/industry-solution/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10992', 'applyIndustrySolutionAppForSpecific', '/v1/admin/service/industry-solution/apps/{appId}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10993', 'findVasAppCurrentUsage', '/v1/admin/service/industry-solution/apps/{appId}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10994', 'exportVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10995', 'findVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10996', 'findVasAppMarkets', '/v1/admin/service/industry-solution/apps/{appId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10997', 'findSpecificSolutionResellerPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10998', 'specificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10999', 'deleteSpecificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11000', 'findSpecificSolutionResellerAllListPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11001', 'updateAppServicePrice', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11002', 'findVasAppServiceHistory', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11003', 'updateAppServiceStatus', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11004', 'getSolutionTrialPage', '/v1/admin/service/industry-solution/apps/{appId}/trial/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11005', 'getVasAppUsageDashBoard', '/v1/admin/service/industry-solution/apps/{appId}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11006', 'getAppVasSettingVo_1', '/v1/admin/service/industry-solution/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11007', 'findDeveloperSolutionApplyPage', '/v1/admin/service/industry-solution/developer/apply', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11008', 'getDeveloperSolutionApplyCount', '/v1/admin/service/industry-solution/developer/apply/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11009', 'updateDeveloperAllowIndustrySolution', '/v1/admin/service/industry-solution/developer/{developerId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11010', 'getResellerApplies', '/v1/admin/service/industry-solution/reseller/apply', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11011', 'getResellerSolutionApplyCount', '/v1/admin/service/industry-solution/reseller/apply/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11012', 'updateResellerApply', '/v1/admin/service/industry-solution/reseller/apply/{applyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11013', 'findGlobalPublishAppPage', '/v1/admin/subscription/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11014', 'getApkDetailVo_3', '/v1/admin/subscription/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11015', 'findSpecificApkResellerPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11016', 'specificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11017', 'deleteSpecificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11018', 'findSpecificApkResellerAllListPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11019', 'reSignApk_1', '/v1/admin/subscription/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11020', 'findApkSignatureList_1', '/v1/admin/subscription/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11021', 'exportGlobalPublishApp', '/v1/admin/subscription/apps/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11022', 'getAppDetailVo', '/v1/admin/subscription/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11023', 'subscriptionApp', '/v1/admin/subscription/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11024', 'findApkPage', '/v1/admin/subscription/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11025', 'unSubscriptionApp', '/v1/admin/subscription/apps/{appId}/cancel', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11026', 'getBizDataFromGoInsight_1', '/v1/admin/subscription/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11027', 'findAppMerchantCategoryPage', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11028', 'specificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11029', 'deleteSpecificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11030', 'findSpecificAppResellerPage', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11031', 'specificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11032', 'deleteSpecificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11033', 'findSpecificAppResellerAllListPage', '/v1/admin/subscription/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11034', 'getAppSettingVo', '/v1/admin/subscription/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11035', 'getAppVasSettingVo', '/v1/admin/subscription/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11036', 'findGlobalPublishFirmware', '/v1/admin/subscription/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11037', 'exportGlobalPublishFirmware', '/v1/admin/subscription/firmwares/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11038', 'getFirmwareDetailVo', '/v1/admin/subscription/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11039', 'subscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11040', 'unsubscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/cancel', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11041', 'specificResellerFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11042', 'findSpecificFirmwareResellerAllListPage', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11043', 'getTopicSubscription', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11044', 'subscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11045', 'unsubscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11046', 'searchGroups_1', '/v1/admin/terminal-groups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11047', 'createGroup', '/v1/admin/terminal-groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11048', 'searchTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11049', 'createTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11050', 'updateGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11051', 'deleteGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11052', 'getTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11053', 'deleteTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11054', 'resumeGroupTerminalApk', '/v1/admin/terminal-groups/apks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11055', 'activateTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11056', 'getApkDetailVo_2', '/v1/admin/terminal-groups/apks/{groupApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11057', 'createGroupApkDataFileDownloadTask', '/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11058', 'createGroupApkFilter', '/v1/admin/terminal-groups/apks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11059', 'updateGroupApkPushLimit', '/v1/admin/terminal-groups/apks/{groupApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11060', 'getTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11061', 'updateTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11062', 'updateTerminalGroupApkParam_1', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11063', 'resumeGroupTerminalApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11064', 'getGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11065', 'saveGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11066', 'searchGroupApkParamTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11067', 'getGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11068', 'saveGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11069', 'resetTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11070', 'submitTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11071', 'suspendTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11072', 'searchGroupApkTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11073', 'createGroupApkTerminalsExportTasks', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11074', 'searchGroupFirmwares', '/v1/admin/terminal-groups/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11075', 'createGroupFirmware', '/v1/admin/terminal-groups/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11076', 'searchFirmware', '/v1/admin/terminal-groups/firmwares/filter', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11077', 'updateGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11078', 'createGroupFirmwareFilter_1', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11079', 'getGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11080', 'deleteGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11081', 'resumeGroupTerminalFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11082', 'activateGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11083', 'createGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11084', 'updateGroupFirmwarePushLimit', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11085', 'resetGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11086', 'submitGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11087', 'suspendGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11088', 'searchGroupFirmwareTerminals', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11089', 'createGroupFirmwareTerminalsExportTasks', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11090', 'importGroupTerminal', '/v1/admin/terminal-groups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11091', 'createGroupTerminalImportTemplateDownloadTask', '/v1/admin/terminal-groups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11092', 'searchTerminalGroupLaunchers', '/v1/admin/terminal-groups/launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11093', 'createTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11094', 'getApks_1', '/v1/admin/terminal-groups/launchers/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11095', 'getResellerOnlineApkNameAndIcon', '/v1/admin/terminal-groups/launchers/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11096', 'searchLauncherTemplates_1', '/v1/admin/terminal-groups/launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11097', 'getLauncherTemplate_1', '/v1/admin/terminal-groups/launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11098', 'updateGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11099', 'deleteGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11100', 'getTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11101', 'deleteTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11102', 'resumeGroupTerminalLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11103', 'activateTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11104', 'getApkDetailVo_1', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11105', 'createGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11106', 'updateGroupLauncherPushLimit', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11107', 'getTerminalGroupLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11108', 'resumeGroupTerminalLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11109', 'searchGroupLauncherParamTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11110', 'resetTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11111', 'submitTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11112', 'suspendTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11113', 'searchGroupLauncherTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11114', 'createGroupLauncherTerminalsExportTasks', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11115', 'searchGroupOperation', '/v1/admin/terminal-groups/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11116', 'createGroupOperation', '/v1/admin/terminal-groups/operations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11117', 'getGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11118', 'deleteGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11119', 'resumeGroupTerminalOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11120', 'activateGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11121', 'updateGroupOperationPushLimit', '/v1/admin/terminal-groups/operations/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11122', 'resetGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11123', 'submitGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11124', 'suspendGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11125', 'searchGroupOperationTerminals', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11126', 'createGroupOperationTerminalsExportTasks', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11127', 'searchGroupPuks', '/v1/admin/terminal-groups/puks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11128', 'createGroupPuk', '/v1/admin/terminal-groups/puks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11129', 'getSignaturePuk_1', '/v1/admin/terminal-groups/puks/{groupId}/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11130', 'getGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11131', 'deleteGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11132', 'resumeGroupTerminalPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11133', 'activateGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11134', 'updateGroupPukPushLimit', '/v1/admin/terminal-groups/puks/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11135', 'resetGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11136', 'submitGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11137', 'suspendGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11138', 'searchGroupPukTerminals', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11139', 'createGroupPukTerminalsExportTasks', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11140', 'searchGroupRkis', '/v1/admin/terminal-groups/rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11141', 'createGroupRki', '/v1/admin/terminal-groups/rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11142', 'showRkiBalance', '/v1/admin/terminal-groups/rkis/rkiKeyBalance', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11143', 'getGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11144', 'deleteGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11145', 'resumeGroupTerminalRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11146', 'activateGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11147', 'updateGroupRkiPushLimit', '/v1/admin/terminal-groups/rkis/{groupRkiId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11148', 'resetGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11149', 'submitGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11150', 'suspendGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11151', 'searchGroupRkiTerminals', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11152', 'createGroupRkiTerminalsExportTasks', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11153', 'searchTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11154', 'createTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11155', 'findApkParameterPage', '/v1/admin/terminal-groups/solutions/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11156', 'findOnlineAppPage', '/v1/admin/terminal-groups/solutions/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11157', 'updateGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11158', 'deleteGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11159', 'getTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11160', 'deleteTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11161', 'resumeGroupTerminalSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11162', 'activateTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11163', 'getSolutionApkDetailVo', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11164', 'createGroupSolutionDataFileDownloadTask', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11165', 'createGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11166', 'updateGroupSolutionPushLimit', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11167', 'getTerminalGroupSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11168', 'updateTerminalGroupSolutionApkParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11169', 'updateTerminalGroupSolutionApkParamFormData', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11170', 'resumeGroupTerminalSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11171', 'getGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11172', 'saveGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11173', 'searchGroupSolutionParamTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11174', 'getGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11175', 'saveGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11176', 'resetTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11177', 'submitTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11178', 'suspendTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11179', 'searchGroupSolutionTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11180', 'createGroupSolutionTerminalsExportTasks', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11181', 'findSimOperator', '/v1/admin/terminal-groups/terminal/sim/operator', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11182', 'searchTerminal_1', '/v1/admin/terminal-groups/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11183', 'searchGroupUninstallApks', '/v1/admin/terminal-groups/uninstall-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11184', 'createGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11185', 'getGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11186', 'deleteGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11187', 'resumeGroupTerminalUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11188', 'activateGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11189', 'updateGroupUninstallApkPushLimit', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11190', 'resetGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11191', 'submitGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11192', 'suspendGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11193', 'searchGroupUninstallApkTerminals', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11194', 'createGroupUninstallApkTerminalsExportTasks', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11195', 'findTerminalGroupVariableList', '/v1/admin/terminal-groups/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11196', 'createTerminalGroupVariable', '/v1/admin/terminal-groups/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11197', 'batchDeleteTerminalGroupVariables', '/v1/admin/terminal-groups/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11198', 'importTerminalGroupVariable', '/v1/admin/terminal-groups/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11199', 'createTerminalGroupVariableImportTemplateDownloadTask', '/v1/admin/terminal-groups/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11200', 'findTerminalGroupVariableSupportedAppList', '/v1/admin/terminal-groups/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11201', 'findTerminalGroupVariableUsedAppList', '/v1/admin/terminal-groups/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11202', 'updateTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11203', 'deleteTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11204', 'getGroup', '/v1/admin/terminal-groups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11205', 'updateGroup', '/v1/admin/terminal-groups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11206', 'deleteGroup', '/v1/admin/terminal-groups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11207', 'activeGroup_1', '/v1/admin/terminal-groups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11208', 'disableGroup_1', '/v1/admin/terminal-groups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11209', 'removeGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11210', 'createGroupTerminals_1', '/v1/admin/terminal-groups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11211', 'searchGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11212', 'getTerminalNumberStatisticData_1', '/v1/admin/terminal-management/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11213', 'getTerminalNumberOfMerchantData', '/v1/admin/terminal-management/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11214', 'getTerminalNumberOfResellerData_1', '/v1/admin/terminal-management/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11215', 'findEmmDeviceDetailPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11216', 'findEmmDeviceAuditLogPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11217', 'findEmmDeviceInstalledAppPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/installed-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11218', 'getEmmDeviceLocation', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11219', 'getEmmDeviceDashboardMonitor', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11220', 'getEmmDeviceTraffic', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11221', 'findEmmDeviceVariablePage', '/v1/admin/terminal-management/emm-device-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11222', 'createEmmDeviceVariable', '/v1/admin/terminal-management/emm-device-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11223', 'batchDeleteEmmDeviceVariables', '/v1/admin/terminal-management/emm-device-variables/batch/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11224', 'createExportTerminalVariableDownloadTask_1', '/v1/admin/terminal-management/emm-device-variables/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11225', 'importTerminalVariable_1', '/v1/admin/terminal-management/emm-device-variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11226', 'createEmmDeviceVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/emm-device-variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11227', 'updateEmmDeviceVariable', '/v1/admin/terminal-management/emm-device-variables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11228', 'deleteEmmDeviceVariable', '/v1/admin/terminal-management/emm-device-variables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11229', 'searchEmmDevice', '/v1/admin/terminal-management/emm-devices', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11230', 'batchDeleteEmmDevices', '/v1/admin/terminal-management/emm-devices/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11231', 'batchMoveEmmDevices_1', '/v1/admin/terminal-management/emm-devices/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11232', 'createEmmDpcExtras', '/v1/admin/terminal-management/emm-devices/dpcExtrasToken', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11233', 'searchQRCodeEnrollmentRecords', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11234', 'getEmmRegisterQRCode', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode/{enrollmentTokenId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11235', 'disabledEmmRegisterQRCode', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode/{enrollmentTokenId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11236', 'findEmmEnrolledDevices', '/v1/admin/terminal-management/emm-devices/enrollment-records/qrCode/{enrollmentTokenId}/enrolled-devices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11237', 'searchEmmZteEnrollmentRecords', '/v1/admin/terminal-management/emm-devices/enrollment-records/zte', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11238', 'searchEmmZteDeviceRecords_1', '/v1/admin/terminal-management/emm-devices/enrollment-records/zte/{zteRecordId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11239', 'getEmmZteRecordDetail_1', '/v1/admin/terminal-management/emm-devices/enrollment-records/zte/{zteRecordId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11240', 'createZteFileDownloadTask', '/v1/admin/terminal-management/emm-devices/enrollment-records/{zteRecordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11241', 'rejectEmmZteRecord_1', '/v1/admin/terminal-management/emm-devices/enrollment-records/{zteRecordId}/withdraw', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11242', 'createEmmDevicesExportDownloadTask', '/v1/admin/terminal-management/emm-devices/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11243', 'importEmmDeviceBatchMove', '/v1/admin/terminal-management/emm-devices/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11244', 'createEmmDeviceImportBatchOperationTemplateDownloadTask', '/v1/admin/terminal-management/emm-devices/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11245', 'findEmmDeviceModels', '/v1/admin/terminal-management/emm-devices/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11246', 'createRegisterQRCode', '/v1/admin/terminal-management/emm-devices/register-qrcode/create', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11247', 'submitEmmZteFileUploadRecord', '/v1/admin/terminal-management/emm-devices/zte/file-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11248', 'createEmmZteFileUploadTemplateDownloadTask', '/v1/admin/terminal-management/emm-devices/zte/file-upload/template/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11249', 'submitEmmZteQuickUploadRecord', '/v1/admin/terminal-management/emm-devices/zte/quick-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11250', 'getEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11251', 'updateEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11252', 'deleteEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11253', 'getEmmDeviceAirViewer', '/v1/admin/terminal-management/emm-devices/{deviceId}/air-viewer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11254', 'lockEmmDeviceScreen_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/lockscreen', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11255', 'rebootEmmDevice_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/reboot', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11256', 'resetEmmDevicePassword_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/resetpw', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11257', 'startEmmDeviceLostMode_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/startlost', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11258', 'stopEmmDeviceLostMode_1', '/v1/admin/terminal-management/emm-devices/{deviceId}/stoplost', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11259', 'startAirViewer_1', '/v1/admin/terminal-management/emm-devices/{terminalId}/air-viewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11260', 'findMerchantVariablePage', '/v1/admin/terminal-management/merchant-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11261', 'createMerchantVariable', '/v1/admin/terminal-management/merchant-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11262', 'batchDeleteMerchantVariables', '/v1/admin/terminal-management/merchant-variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11263', 'importMerchantVariable', '/v1/admin/terminal-management/merchant-variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11264', 'createMerchantVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/merchant-variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11265', 'findMerchantVariableSupportedAppPage', '/v1/admin/terminal-management/merchant-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11266', 'findMerchantVariableUsedAppPage', '/v1/admin/terminal-management/merchant-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11267', 'updateMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11268', 'deleteMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11269', 'createMerchant', '/v1/admin/terminal-management/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11270', 'createExportMerchantsDownloadTask', '/v1/admin/terminal-management/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11271', 'importMerchant', '/v1/admin/terminal-management/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11272', 'createMerchantImportTemplateDownloadTask', '/v1/admin/terminal-management/merchants/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11273', 'findSubMerchantPageForOrganization', '/v1/admin/terminal-management/merchants/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11274', 'findSubMerchantPage', '/v1/admin/terminal-management/merchants/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11275', 'getMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11276', 'updateMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11277', 'deleteMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11278', 'activeMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11279', 'disableMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11280', 'moveMerchant', '/v1/admin/terminal-management/merchants/{merchantId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11281', 'getMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11282', 'createMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11283', 'replaceMerchantEmail', '/v1/admin/terminal-management/merchants/{merchantId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11284', 'activeMerchantResendEmail', '/v1/admin/terminal-management/merchants/{merchantId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11285', 'createReseller', '/v1/admin/terminal-management/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11286', 'createExportResellersDownloadTask', '/v1/admin/terminal-management/resellers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11287', 'findResellerPageForOrganization', '/v1/admin/terminal-management/resellers/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11288', 'createTmkImportTemplateDownloadTask', '/v1/admin/terminal-management/resellers/rki/tmk/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11289', 'findSubResellerPage', '/v1/admin/terminal-management/resellers/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11290', 'getResellerDetailVo', '/v1/admin/terminal-management/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11291', 'updateReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11292', 'deleteReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11293', 'activeReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11294', 'disableReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11295', 'moveReseller', '/v1/admin/terminal-management/resellers/{resellerId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11296', 'getResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11297', 'createResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11298', 'replaceResellerEmail', '/v1/admin/terminal-management/resellers/{resellerId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11299', 'activeResellerResendEmail', '/v1/admin/terminal-management/resellers/{resellerId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11300', 'getResellerRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11301', 'refreshResellerRkiKeys', '/v1/admin/terminal-management/resellers/{resellerId}/rki/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11302', 'verifyPushRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki/pre-deduction', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11303', 'findTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11304', 'importTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11305', 'saveResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11306', 'deleteResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11307', 'findTerminalApkPage', '/v1/admin/terminal-management/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11308', 'createTerminalApks', '/v1/admin/terminal-management/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11309', 'createExportApkParameterCompareDownloadTask', '/v1/admin/terminal-management/terminal-apks/export/history/param/comparison', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11310', 'findTerminalApkParamComparePage', '/v1/admin/terminal-management/terminal-apks/history/param/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11311', 'getTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11312', 'deleteTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11313', 'activateTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11314', 'getApkDetailVo', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11315', 'createTerminalApkDataFileDownloadTask', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11316', 'getTerminalApkParam', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11317', 'updateTerminalApkParam_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11318', 'updateTerminalApkParamFormData', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11319', 'findTerminalApkParamVariablePage', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11320', 'saveTerminalApkParamVariables', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11321', 'resetTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11322', 'suspendTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11323', 'findTerminalDetailPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11324', 'findTerminalAuditLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11325', 'getTerminalAuditLogDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log/{auditId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11326', 'getTerminalBatteryInfo', '/v1/admin/terminal-management/terminal-detail/{terminalId}/battery', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11327', 'getCheckUpDetailByTerminalId', '/v1/admin/terminal-management/terminal-detail/{terminalId}/check-up', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11328', 'findTerminalDownloadApkLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/download-apk-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11329', 'createTerminalGeoFenceWhiteList', '/v1/admin/terminal-management/terminal-detail/{terminalId}/geofence/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11330', 'deleteTerminalGeoFenceWhiteList', '/v1/admin/terminal-management/terminal-detail/{terminalId}/geofence/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11331', 'getTerminalInstalledApkStatistics', '/v1/admin/terminal-management/terminal-detail/{terminalId}/installed-apks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11332', 'getTerminalDashboardLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11333', 'refreshTerminalLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11334', 'getTerminalDashboardMonitor', '/v1/admin/terminal-management/terminal-detail/{terminalId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11335', 'getTerminalPedStatus_1', '/v1/admin/terminal-management/terminal-detail/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11336', 'findTerminalPushHistoryPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/push-history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11337', 'refreshTerminalDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11338', 'findTerminalReplacementLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/replace-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11339', 'getTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11340', 'saveTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11341', 'updateTerminalSafeRangeAutoLock', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/auto-lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11342', 'clearTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11343', 'getTerminalTraffic', '/v1/admin/terminal-management/terminal-detail/{terminalId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11344', 'findTerminalFirmwarePage', '/v1/admin/terminal-management/terminal-firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11345', 'createTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11346', 'getTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11347', 'deleteTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11348', 'activateTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11349', 'resetTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11350', 'suspendTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11351', 'findTerminalLauncherPage', '/v1/admin/terminal-management/terminal-launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11352', 'createTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11353', 'getApks', '/v1/admin/terminal-management/terminal-launchers/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11354', 'searchLauncherTemplates', '/v1/admin/terminal-management/terminal-launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11355', 'getLauncherTemplate', '/v1/admin/terminal-management/terminal-launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11356', 'getTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11357', 'deleteTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11358', 'activateTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11359', 'getTerminalLauncherParam', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11360', 'resetTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11361', 'suspendTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11362', 'findTerminalRkiPage', '/v1/admin/terminal-management/terminal-rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11363', 'createTerminalRki', '/v1/admin/terminal-management/terminal-rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11364', 'getTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11365', 'deleteTerminalRKI', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11366', 'activateTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11367', 'resetTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11368', 'suspendTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11369', 'findTerminalVariablePage', '/v1/admin/terminal-management/terminal-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11370', 'createTerminalVariable', '/v1/admin/terminal-management/terminal-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11371', 'batchDeleteTerminalVariables', '/v1/admin/terminal-management/terminal-variables/batch/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11372', 'findTerminalVariableSupportedAppPage', '/v1/admin/terminal-management/terminal-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11373', 'findTerminalVariableUsedAppPage', '/v1/admin/terminal-management/terminal-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11374', 'updateTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11375', 'deleteTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11376', 'createTerminal', '/v1/admin/terminal-management/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11377', 'searchAccessory', '/v1/admin/terminal-management/terminals/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11378', 'createExportTerminalAccessoryDownloadTask', '/v1/admin/terminal-management/terminals/accessories/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11379', 'cancelTerminalAccessoryOperation', '/v1/admin/terminal-management/terminals/accessories/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11380', 'findAccessoryQtyByType', '/v1/admin/terminal-management/terminals/accessories/widgets/W23', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11381', 'findAccessoryQtyByModel', '/v1/admin/terminal-management/terminals/accessories/widgets/W24', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11382', 'findTerminalAccessoryDetail', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11383', 'findTerminalAccessoryDetailPage', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/details', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11384', 'findTerminalAccessoryEvents', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/events', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11385', 'findTerminalAccessoryOperations', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11386', 'pushTerminalActions_1', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11387', 'refreshTerminalAccessory', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11388', 'removeAppointment', '/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11389', 'cancelAppointment', '/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}/canceled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11390', 'createTerminals_1', '/v1/admin/terminal-management/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11391', 'batchActiveTerminals', '/v1/admin/terminal-management/terminals/batch/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11392', 'createGroupTerminals', '/v1/admin/terminal-management/terminals/batch/add-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11393', 'batchDeleteTerminals_1', '/v1/admin/terminal-management/terminals/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11394', 'batchSuspendTerminals', '/v1/admin/terminal-management/terminals/batch/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11395', 'batchMoveTerminals', '/v1/admin/terminal-management/terminals/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11396', 'copyTerminal', '/v1/admin/terminal-management/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11397', 'createExportTerminalsDownloadTask_1', '/v1/admin/terminal-management/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11398', 'createExportTerminalStaticIpConfigDownloadTask', '/v1/admin/terminal-management/terminals/export/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11399', 'createExportTerminalVariableDownloadTask', '/v1/admin/terminal-management/terminals/export/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11400', 'searchGroups', '/v1/admin/terminal-management/terminals/group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11401', 'importTerminal', '/v1/admin/terminal-management/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11402', 'importTerminalBatchActive', '/v1/admin/terminal-management/terminals/import/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11403', 'importTerminalBatchDelete', '/v1/admin/terminal-management/terminals/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11404', 'importTerminalBatchSuspend', '/v1/admin/terminal-management/terminals/import/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11405', 'importTerminalBatchMove', '/v1/admin/terminal-management/terminals/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11406', 'createTerminalImportBatchOperationTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11407', 'importTerminalStaticIpConfig', '/v1/admin/terminal-management/terminals/import/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11408', 'createTerminalStaticIpConfigTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/static-ip/config/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11409', 'createTerminalImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11410', 'importTerminalVariable', '/v1/admin/terminal-management/terminals/import/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11411', 'createTerminalVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/variable/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11412', 'searchTerminal', '/v1/admin/terminal-management/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11413', 'findTerminalAccessTypeList', '/v1/admin/terminal-management/terminals/product-types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11414', 'getTerminalQuickBySn', '/v1/admin/terminal-management/terminals/quick/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11415', 'getTerminalStockBySerialNo', '/v1/admin/terminal-management/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11416', 'findModelPage', '/v1/admin/terminal-management/terminals/support-models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11417', 'getTerminalBasicVoByTidOrSerialNo', '/v1/admin/terminal-management/terminals/tid-sn/{tidOrSN}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11418', 'getTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11419', 'updateTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11420', 'deleteTerminal_3', '/v1/admin/terminal-management/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11421', 'findTerminalAccessoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11422', 'activeTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11423', 'getTerminalAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11424', 'getAppointmentPage', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11425', 'createAppointment', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11426', 'checkCheckUpVersion', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/check/checkup/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11427', 'pushInstallCheckup', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/checkup/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11428', 'pushInstallAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11429', 'startAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11430', 'disableTerminal_2', '/v1/admin/terminal-management/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11431', 'findTerminalInstalledApkPage', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11432', 'getTerminalInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11433', 'createParameterDataFileDownloadTask_1', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11434', 'getInstalledApkParam', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11435', 'uninstallInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11436', 'getTerminalInstalledFirmware', '/v1/admin/terminal-management/terminals/{terminalId}/installed-firmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11437', 'changeTerminalModel', '/v1/admin/terminal-management/terminals/{terminalId}/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11438', 'moveTerminal', '/v1/admin/terminal-management/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11439', 'getPukPushStatus', '/v1/admin/terminal-management/terminals/{terminalId}/puk/push/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11440', 'cancelTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/cancel/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11441', 'collectTerminalLogcat', '/v1/admin/terminal-management/terminals/{terminalId}/setting/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11442', 'findTerminalSystemConfigPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/configs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11443', 'disablePushPukTerminalAction', '/v1/admin/terminal-management/terminals/{terminalId}/setting/disable/puk/push', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11444', 'getTerminalLocationEnable', '/v1/admin/terminal-management/terminals/{terminalId}/setting/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11445', 'findTerminalLogPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11446', 'createTerminalLogDownloadTask', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs/{terminalLogId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11447', 'findTerminalNetworkConfigurationList', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11448', 'deleteTerminalNetworkConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11449', 'getProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11450', 'saveTerminalProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11451', 'deleteTerminalProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11452', 'findPukPushHistoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11453', 'getSignaturePuk', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11454', 'pushTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/push/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11455', 'updateTerminalRemoteConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11456', 'findTerminalStockPage', '/v1/admin/terminal-stocks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11457', 'createTerminals', '/v1/admin/terminal-stocks/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11458', 'assignTerminals', '/v1/admin/terminal-stocks/batch/assign', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11459', 'batchDeleteTerminals', '/v1/admin/terminal-stocks/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11460', 'createExportStockTerminalsDownloadTask', '/v1/admin/terminal-stocks/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11461', 'importStockTerminal', '/v1/admin/terminal-stocks/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11462', 'createStockTerminalImportTemplateDownloadTask', '/v1/admin/terminal-stocks/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11463', 'getTerminalStock', '/v1/admin/terminal-stocks/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11464', 'updateTerminal', '/v1/admin/terminal-stocks/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11465', 'deleteTerminal_2', '/v1/admin/terminal-stocks/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11466', 'searchUsers', '/v1/admin/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11467', 'createExportUserDownloadTask', '/v1/admin/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11468', 'searchRoleList', '/v1/admin/users/role-all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11469', 'getUser_3', '/v1/admin/users/{userId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11470', 'deleteUser', '/v1/admin/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11471', 'activeUser', '/v1/admin/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11472', 'sendActivateUserEmail', '/v1/admin/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11473', 'changeEmail', '/v1/admin/users/{userId}/change-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11474', 'disableUser', '/v1/admin/users/{userId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11475', 'findUserRoles', '/v1/admin/users/{userId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11476', 'resetUserPassword', '/v1/admin/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11477', 'deleteUserRoles', '/v1/admin/users/{userId}/roles', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11478', 'deleteUserRole', '/v1/admin/users/{userId}/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11479', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11480', 'getThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11481', 'updateThirdpartyAppSys', '/v1/admin/vas/3rdsys/app', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11482', 'createThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11483', 'exportDetectionSummary', '/v1/admin/vas/air-shield/attestation/history/{terminalId}/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11484', 'clearHistory', '/v1/admin/vas/air-shield/attestation/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11485', 'findDetectionHistoryPage_1', '/v1/admin/vas/air-shield/attestation/{terminalId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11486', 'listPosviewerFileTransferInfo', '/v1/admin/vas/air-viewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11487', 'getModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11488', 'updateModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11489', 'listPosviewerOperationInfo', '/v1/admin/vas/air-viewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11490', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11491', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11492', 'searchEmmZteRecords', '/v1/admin/zte', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11493', 'getPendingEmmZteRecordCount', '/v1/admin/zte/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11494', 'searchEmmZteDeviceRecords', '/v1/admin/zte/{zteRecordId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11495', 'approveEmmZteRecord', '/v1/admin/zte/{zteRecordId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11496', 'getEmmZteRecordDetail', '/v1/admin/zte/{zteRecordId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11497', 'downloadEmmZteRecordFile', '/v1/admin/zte/{zteRecordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11498', 'rejectEmmZteRecord', '/v1/admin/zte/{zteRecordId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11499', 'getDashBoard', '/v1/app_scan/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11500', 'getEngineBlacklist', '/v1/app_scan/engine/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11501', 'updateEngineBlacklist', '/v1/app_scan/engine/blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11502', 'getHistoricalUsage', '/v1/app_scan/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11503', 'getAvailableScanEngineList', '/v1/app_scan/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11504', 'rescan', '/v1/app_scan/rescan', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11505', 'getResultFile', '/v1/app_scan/resultZip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11506', 'getScanResult', '/v1/app_scan/results', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11507', 'isCreateTaskPermitted', '/v1/app_scan/scanned', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11508', 'getSetting', '/v1/app_scan/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11509', 'updateSetting', '/v1/app_scan/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11510', 'createScanTask', '/v1/app_scan/task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11511', 'deleteScanTask', '/v1/app_scan/task/{scanTaskId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11512', 'getUsage', '/v1/app_scan/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11513', 'createBuriedPoints_1', '/v1/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11514', 'queryNavigoAssistant', '/v1/common/assistant', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11515', 'evaluateData', '/v1/common/assistant/qa/evaluate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11516', 'activateUser', '/v1/common/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11517', 'validateActivate1', '/v1/common/auth/activation/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11518', 'destroySsoToken', '/v1/common/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11519', 'resetEmail', '/v1/common/auth/email-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11520', 'validateResetEmail1', '/v1/common/auth/email-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11521', 'validateExtraction', '/v1/common/auth/extraction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11522', 'validateDownloadLink', '/v1/common/auth/extraction/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11523', 'getMarketDc', '/v1/common/auth/market/dc', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11524', 'disableOtpByBackupCode', '/v1/common/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11525', 'validateDisableCode', '/v1/common/auth/otp/disable-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11526', 'sendDisableOtpMail', '/v1/common/auth/otp/reset-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11527', 'forgetPwd', '/v1/common/auth/password-forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11528', 'resetPwd', '/v1/common/auth/password-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11529', 'validateResetPwd1', '/v1/common/auth/password-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11530', 'checkTokenExpire', '/v1/common/auth/ping', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11531', 'reactivateUser', '/v1/common/auth/reactivate-user', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11532', 'sendReactivateUserMail', '/v1/common/auth/reactivate-user-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11533', 'registerUser', '/v1/common/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11534', 'findCodes', '/v1/common/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11535', 'getCodes', '/v1/common/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11536', 'getDocSetting', '/v1/common/doc-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11537', 'download2_1', '/v1/common/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11538', 'createLatestOnlineClientApkDownloadTask', '/v1/common/download/client-app/latest/client', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11539', 'download1_1', '/v1/common/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11540', 'getDownloadUrl_1', '/v1/common/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11541', 'getCurrentEnv', '/v1/common/env', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11542', 'getFile', '/v1/common/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11543', 'findFooter', '/v1/common/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11544', 'getFooter', '/v1/common/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11545', 'getExternalUser', '/v1/common/is-external-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11546', 'getLangList', '/v1/common/languages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11547', 'getLicense', '/v1/common/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11548', 'initMessageStats', '/v1/common/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11549', 'readTopXMessages', '/v1/common/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11550', 'readMessage_1', '/v1/common/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11551', 'viewMessageDetails_1', '/v1/common/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11552', 'uploadTerminalGroupApkParamFile', '/v1/common/param/data/upload', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11553', 'getAllPasswordValidatorPolicyFailureDetail', '/v1/common/password-rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11554', 'getSystemConfig', '/v1/common/system-config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11555', 'getUserAgreement_1', '/v1/common/user-agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11556', 'agreeUserAgreement_1', '/v1/common/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11557', 'getCurrentUserAgreementAgreed', '/v1/common/users/agreement/agreed', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11558', 'generateCaptcha_1', '/v1/common/users/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11559', 'verifyCaptcha', '/v1/common/users/captcha/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11560', 'getCurrentUserRouterSwitchList', '/v1/common/users/routers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11561', 'createDeveloper', '/v1/developer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11562', 'activeDeveloper3rdSysAccess', '/v1/developer/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11563', 'getDeveloper3rdSysConfig', '/v1/developer/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11564', 'deActiveDeveloper3rdSysAccess', '/v1/developer/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11565', 'getDeveloper3rdSysAccessSecret', '/v1/developer/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11566', 'resetDeveloper3rdSysAccess', '/v1/developer/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11567', 'getDeveloperAccountVo', '/v1/developer/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11568', 'agreeUserAgreement', '/v1/developer/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11569', 'updateApk_1', '/v1/developer/apks/emm/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11570', 'deleteApk_2', '/v1/developer/apks/emm/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11571', 'getApkEditDetail_1', '/v1/developer/apks/emm/{apkId}/apk-edit', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11572', 'updateApkFile_1', '/v1/developer/apks/emm/{apkId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11573', 'createOriginalApkDownloadTask_1', '/v1/developer/apks/emm/{apkId}/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11574', 'updateApk', '/v1/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11575', 'deleteApk_1', '/v1/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11576', 'getApkEditDetail', '/v1/developer/apks/{apkId}/apk-edit', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11577', 'updateApkFile', '/v1/developer/apks/{apkId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11578', 'uploadApkAttachment', '/v1/developer/apks/{apkId}/attachment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11579', 'createOriginalApkDownloadTask', '/v1/developer/apks/{apkId}/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11580', 'offlineApk_1', '/v1/developer/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11581', 'uploadApkParamTemplate', '/v1/developer/apks/{apkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11582', 'deleteApkParamTemplate_1', '/v1/developer/apks/{apkId}/param', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11583', 'findCustomParamTemplate', '/v1/developer/apks/{apkId}/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11584', 'analysisDevParamTemplate', '/v1/developer/apks/{apkId}/param-templates/analysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11585', 'createParameterDataFileDownloadTask', '/v1/developer/apks/{apkId}/param-templates/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11586', 'createApkParamTemplateDownloadTask_1', '/v1/developer/apks/{apkId}/param-templates/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11587', 'getApkParamTemplateSchema', '/v1/developer/apks/{apkId}/param-templates/schema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11588', 'submitApk', '/v1/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11589', 'searchApps_1', '/v1/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11590', 'createApp', '/v1/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11591', 'getAppDetail_2', '/v1/developer/apps/emm/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11592', 'deleteApp_2', '/v1/developer/apps/emm/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11593', 'addApkFile_1', '/v1/developer/apps/emm/{appId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11594', 'searchApks_1', '/v1/developer/apps/emm/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11595', 'getApkDetail_2', '/v1/developer/apps/emm/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11596', 'updateAppKey_1', '/v1/developer/apps/emm/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11597', 'getAppTracks', '/v1/developer/apps/emm/{appId}/tracks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11598', 'getDeveloperAppSummary', '/v1/developer/apps/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11599', 'getAppDetail_1', '/v1/developer/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11600', 'deleteApp_1', '/v1/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11601', 'addApkFile', '/v1/developer/apps/{appId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11602', 'searchApks', '/v1/developer/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11603', 'getApkDetail_1', '/v1/developer/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11604', 'updateAppKey', '/v1/developer/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11605', 'findSolutionAppUsage', '/v1/developer/apps/{appId}/industry-solution/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11606', 'findSolutionAppUsagePeriod', '/v1/developer/apps/{appId}/industry-solution/usage/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11607', 'getBizDataFromGoInsight', '/v1/developer/apps/{appId}/sandbox/insight-data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11608', 'getDeveloperBalance', '/v1/developer/balance', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11609', 'findDeveloperTransactionList', '/v1/developer/balance/transactions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11610', 'withdrawalDeveloperBalance', '/v1/developer/balance/withdrawal', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11611', 'getDeveloper_1', '/v1/developer/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11612', 'getUser_2', '/v1/developer/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11613', 'validateUserEmail', '/v1/developer/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11614', 'findFactoryNameList', '/v1/developer/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11615', 'findFactoryModelList', '/v1/developer/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11616', 'applyIndustrySolution', '/v1/developer/industry-solution/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11617', 'getMarket_2', '/v1/developer/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11618', 'findEnterpriseDevelopers', '/v1/developer/members', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11619', 'addEnterpriseDeveloper', '/v1/developer/members', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11620', 'deleteEnterpriseDeveloper', '/v1/developer/members/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11621', 'updateAdminDeveloper', '/v1/developer/members/{userId}/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11622', 'updateDeveloperSuperAdmin_1', '/v1/developer/members/{userId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11623', 'updateUserDeveloper', '/v1/developer/members/{userId}/user', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11624', 'searchCustomParamTemplate', '/v1/developer/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11625', 'createParamTemplate', '/v1/developer/param-templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11626', 'searchAppName', '/v1/developer/param-templates/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11627', 'searchCustomParamTemplate_1', '/v1/developer/param-templates/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11628', 'getDevParamTemplate', '/v1/developer/param-templates/{templateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11629', 'updateParameterSchema', '/v1/developer/param-templates/{templateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11630', 'deleteCustomParamTemplate', '/v1/developer/param-templates/{templateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11631', 'cloneParamTemplate', '/v1/developer/param-templates/{templateId}/clone', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11632', 'createApkParamTemplateDownloadPoFilesTask', '/v1/developer/param-templates/{templateId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11633', 'updateParamTemplateName', '/v1/developer/param-templates/{templateId}/name', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11634', 'getParamTemplateSchema', '/v1/developer/param-templates/{templateId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11635', 'uploadDevParamTemplate', '/v1/developer/param-templates/{templateId}/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11636', 'initDeveloperPayment', '/v1/developer/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11637', 'checkout', '/v1/developer/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11638', 'payDeveloperOffline', '/v1/developer/payment/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11639', 'findAppPageForSandBox', '/v1/developer/sandbox-data/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11640', 'searchSandboxTerminal', '/v1/developer/sandbox-terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11641', 'createSandboxTerminal', '/v1/developer/sandbox-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11642', 'searchFactory', '/v1/developer/sandbox-terminals/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11643', 'getSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11644', 'updateSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11645', 'deleteTerminal_1', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11646', 'isSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11647', 'updateSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11648', 'searchSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11649', 'createSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11650', 'getApkDetail', '/v1/developer/sandbox/terminal-apks/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11651', 'getSandboxTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11652', 'deleteTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11653', 'activateTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11654', 'createTerminalApkDataDownloadTask', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11655', 'updateTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/param-template-name', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11656', 'getSandboxTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11657', 'updateTerminalApkParam_2', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11658', 'resetTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11659', 'getUserAgreement', '/v1/developer/user/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11660', 'getValueAddServiceSummaryVo', '/v1/developer/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11661', 'findVasAgreedAgreements', '/v1/developer/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11662', 'findDeveloperAppForVas', '/v1/developer/vas/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11663', 'clearAppCloudMessagesData', '/v1/developer/vas/clear-data', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11664', 'confirmConnectDialog', '/v1/developer/vas/confirm/connect/dialog', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11665', 'listAppMsg', '/v1/developer/vas/msg', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11666', 'addAppMsg', '/v1/developer/vas/msg', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11667', 'getTerminalInstalledAppCount', '/v1/developer/vas/msg/app/{appId}/installed-terminal-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11668', 'findAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11669', 'createAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11670', 'deleteAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}/tags/{tagId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11671', 'uploadMsgTemplate', '/v1/developer/vas/msg/template/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11672', 'validateUrl', '/v1/developer/vas/msg/template/validation-img-url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11673', 'getMsgById', '/v1/developer/vas/msg/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11674', 'getMsgStatusById', '/v1/developer/vas/msg/{id}/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11675', 'logicDeleteMessage', '/v1/developer/vas/msg/{msgId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11676', 'disableMessage', '/v1/developer/vas/msg/{msgId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11677', 'getMsgReport', '/v1/developer/vas/msg/{msgId}/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11678', 'showConnectDialog', '/v1/developer/vas/show/connect/dialog', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11679', 'findStatisticsTypes', '/v1/developer/vas/statistics/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11680', 'getVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11681', 'agreeVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11682', 'serviceApply', '/v1/developer/vas/{serviceType}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11683', 'findHistoryUsageByServiceType', '/v1/developer/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11684', 'getUsageDashboardByServiceType', '/v1/developer/vas/{serviceType}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11685', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11686', 'getLatestOnlineApkList', '/v1/internal/appUpdate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11687', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11688', 'getApps_1', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11689', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11690', 'searchAdGroup', '/v1/marketAdmin/vas/adup/ad-group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11691', 'createAdGroup', '/v1/marketAdmin/vas/adup/ad-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11692', 'findAdGroupSlotPage', '/v1/marketAdmin/vas/adup/ad-group/ad-slot', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11693', 'updateAdGroupVisual', '/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adGroupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11694', 'findAdGroupVisualPage', '/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adSlotId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11695', 'getAdGroupDetail', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11696', 'updateAdGroup', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11697', 'removeAdGroup', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11698', 'updateAdGroupStatus', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11699', 'createAdSlot', '/v1/marketAdmin/vas/adup/ad-slot', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11700', 'findAdGroupModels', '/v1/marketAdmin/vas/adup/ad-slot/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11701', 'findAdSlotSpecList', '/v1/marketAdmin/vas/adup/ad-slot/specs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11702', 'deleteAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11703', 'activateAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/activate', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11704', 'disableAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11705', 'findAdSlots', '/v1/marketAdmin/vas/adup/ad-slots', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11706', 'createAdVisual', '/v1/marketAdmin/vas/adup/ad-visual', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11707', 'retrieveAdVisualMaxDuration', '/v1/marketAdmin/vas/adup/ad-visual/maxDuration', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11708', 'findAdVisualPage', '/v1/marketAdmin/vas/adup/ad-visuals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11709', 'createVasAgreement', '/v1/marketAdmin/vas/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11710', 'publishVasAgreement', '/v1/marketAdmin/vas/agreement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11711', 'updateVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11712', 'deleteVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11713', 'downloadVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11714', 'findVasAgreements', '/v1/marketAdmin/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11715', 'getEstateList', '/v1/marketAdmin/vas/air-link/estates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11716', 'createEstate', '/v1/marketAdmin/vas/air-link/estates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11717', 'deleteEstate', '/v1/marketAdmin/vas/air-link/estates', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11718', 'moveEstate', '/v1/marketAdmin/vas/air-link/estates/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11719', 'createAirLinkTerminalImportTemplateDownloadTask_1', '/v1/marketAdmin/vas/air-link/estates/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11720', 'updateEstate', '/v1/marketAdmin/vas/air-link/estates/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11721', 'exportAirLinkEstate', '/v1/marketAdmin/vas/air-link/export/estates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11722', 'importAirLinkEstate', '/v1/marketAdmin/vas/air-link/import/estates/{marketId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11723', 'readPlanById', '/v1/marketAdmin/vas/air-link/plan/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11724', 'getRechargeConfig', '/v1/marketAdmin/vas/air-link/recharge/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11725', 'updateRechargeConfig', '/v1/marketAdmin/vas/air-link/recharge/config/{minRecharge}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11726', 'getAirLinkTerminalList', '/v1/marketAdmin/vas/air-link/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11727', 'getAirLinkTerminalActiveHistoryList', '/v1/marketAdmin/vas/air-link/terminals/activate-histories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11728', 'exportTerminalActivateDetail_1', '/v1/marketAdmin/vas/air-link/terminals/activate-histories/{id}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11729', 'addAirLinkTerminal', '/v1/marketAdmin/vas/air-link/terminals/add', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11730', 'getDashboardStatistics', '/v1/marketAdmin/vas/air-link/terminals/dashboard-statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11731', 'delete', '/v1/marketAdmin/vas/air-link/terminals/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11732', 'getDeletedAirLinkTerminalList', '/v1/marketAdmin/vas/air-link/terminals/deleted', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11733', 'disable', '/v1/marketAdmin/vas/air-link/terminals/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11734', 'importAirLinkTerminal', '/v1/marketAdmin/vas/air-link/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11735', 'createAirLinkTerminalImportTemplateDownloadTask', '/v1/marketAdmin/vas/air-link/terminals/import/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11736', 'getAirLinkTerminalOperatorList', '/v1/marketAdmin/vas/air-link/terminals/operators', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11737', 'resume', '/v1/marketAdmin/vas/air-link/terminals/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11738', 'getConsumptionStatistics', '/v1/marketAdmin/vas/air-link/terminals/{id}/consumption-statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11739', 'getAirLinkTerminalDetail', '/v1/marketAdmin/vas/air-link/terminals/{id}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11740', 'getAirLinkTerminalProfileList', '/v1/marketAdmin/vas/air-link/terminals/{id}/profiles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11741', 'switchAirLinkTerminalProfile', '/v1/marketAdmin/vas/air-link/terminals/{id}/switch-profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11742', 'overdueOrResumeAirLinkByMarketId', '/v1/marketAdmin/vas/air-link/{marketId}/overdue', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11743', 'importAirLoadCards', '/v1/marketAdmin/vas/air-load/card/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11744', 'createAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11745', 'getAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11746', 'updateAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11747', 'deleteAirLoadCardPool', '/v1/marketAdmin/vas/air-load/card/pool/info/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11748', 'getAirLoadCardPoolMaximumInfo', '/v1/marketAdmin/vas/air-load/card/pool/maximum', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11749', 'findAirLoadCardPoolResellers', '/v1/marketAdmin/vas/air-load/card/pool/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11750', 'updateAirLoadCardPoolMaximumInfo', '/v1/marketAdmin/vas/air-load/card/pool/{maximum}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11751', 'findAirLoadCardPools', '/v1/marketAdmin/vas/air-load/card/pools', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11752', 'downloadAirLoadCardTemplate', '/v1/marketAdmin/vas/air-load/card/template/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11753', 'deleteAirLoadCard', '/v1/marketAdmin/vas/air-load/card/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11754', 'removeAirLoadCard', '/v1/marketAdmin/vas/air-load/card/{id}/remove', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11755', 'batchDeleteAirLoadCard', '/v1/marketAdmin/vas/air-load/cards/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11756', 'batchRemoveAirLoadCards', '/v1/marketAdmin/vas/air-load/cards/batch/remove', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11757', 'findAirLoadCardsByPoolId', '/v1/marketAdmin/vas/air-load/cards/{cardPoolId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11758', 'getActiveTaskList', '/v1/marketAdmin/vas/air-load/terminals/active-tasks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11759', 'getAirLoadTerminalList', '/v1/marketAdmin/vas/air-load/terminals/active-tasks/{activeTaskId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11760', 'addAirLoadTerminal', '/v1/marketAdmin/vas/air-load/terminals/add', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11761', 'checkTerminalImport', '/v1/marketAdmin/vas/air-load/terminals/check-import', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11762', 'importAirLoadTerminal', '/v1/marketAdmin/vas/air-load/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11763', 'createAirLoadTerminalImportTemplateDownloadTask', '/v1/marketAdmin/vas/air-load/terminals/import/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11764', 'getTerminalImportStatus', '/v1/marketAdmin/vas/air-load/terminals/terminal-import-status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11765', 'exportTerminalActivateDetail', '/v1/marketAdmin/vas/air-load/terminals/{activeTaskId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11766', 'findAppBlackListPage', '/v1/marketAdmin/vas/airShield/app-black-list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11767', 'addAppBlackList', '/v1/marketAdmin/vas/airShield/app-black-list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11768', 'updateAppBlack', '/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11769', 'deleteBlackApp', '/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11770', 'getAttestationInterval', '/v1/marketAdmin/vas/airShield/interval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11771', 'changeInterval', '/v1/marketAdmin/vas/airShield/interval', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11772', 'findSysFileAccessPage', '/v1/marketAdmin/vas/airShield/sys-file-access', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11773', 'addRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11774', 'updateRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11775', 'deleteRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11776', 'getAirViewerCurrentUsage', '/v1/marketAdmin/vas/airViewer/currentUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11777', 'getAirViewerCurrentUsageDashBoard', '/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11778', 'getAirViewerHistoricalUsage', '/v1/marketAdmin/vas/airViewer/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11779', 'getOrderList', '/v1/marketAdmin/vas/airlink/orders', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11780', 'getRechargeInfo', '/v1/marketAdmin/vas/airlink/orders/recharge', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11781', 'recharge', '/v1/marketAdmin/vas/airlink/orders/recharge', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11782', 'getOrderDetail', '/v1/marketAdmin/vas/airlink/orders/{id}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11783', 'exportAirviewerUsage', '/v1/marketAdmin/vas/airviewer/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11784', 'exportAppScanUsage', '/v1/marketAdmin/vas/appscan/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11785', 'getMarketServiceBillingSetting', '/v1/marketAdmin/vas/billingSetting/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11786', 'findCyberLabTerminalBlacklistPage', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11787', 'deleteCyberLabTerminalBlacklist', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11788', 'getGoogleEmmConfig', '/v1/marketAdmin/vas/emm/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11789', 'updateEmmConfig', '/v1/marketAdmin/vas/emm/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11790', 'getEmmEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11791', 'updateEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11792', 'createEmmEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11793', 'getEmmPrivateApps', '/v1/marketAdmin/vas/emm/privateApps/top3', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11794', 'getEmmSignupUrl', '/v1/marketAdmin/vas/emm/signup/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11795', 'disableZTEAutoAudit4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/auto-audit/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11796', 'enableZTEAutoAudit4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/auto-audit/enable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11797', 'createZTECustomer4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/customer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11798', 'enableZTE4Enterprise', '/v1/marketAdmin/vas/emm/zte/{marketId}/enabled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11799', 'exportDetailZipByServiceType', '/v1/marketAdmin/vas/export/detail/zip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11800', 'exportSummaryByServiceType', '/v1/marketAdmin/vas/export/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11801', 'getInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11802', 'changeInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11803', 'searchMarkets', '/v1/marketAdmin/vas/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11804', 'exportMarkets', '/v1/marketAdmin/vas/markets/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11805', 'getCloudMessageTrialCount', '/v1/marketAdmin/vas/markets/{marketId}/trial/msg/count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11806', 'getResellerVasByReseller', '/v1/marketAdmin/vas/reseller/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11807', 'findVasServices', '/v1/marketAdmin/vas/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11808', 'showCurrentMonthUsage', '/v1/marketAdmin/vas/services/show', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11809', 'updateActiveStatus', '/v1/marketAdmin/vas/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11810', 'unsubscribeService', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11811', 'subscribeService', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11812', 'findServiceResellerSpecificPage', '/v1/marketAdmin/vas/services/{serviceType}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11813', 'specificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11814', 'deleteSpecificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11815', 'findSubscribeHistoryPage', '/v1/marketAdmin/vas/subscriptionHistory/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11816', 'loadCurrentEnrollTerminalBill', '/v1/marketAdmin/vas/terminal/enroll/bill', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11817', 'loadCurrentEnrollTerminalDashBoard', '/v1/marketAdmin/vas/terminal/enroll/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11818', 'loadEnrollTerminalHistory', '/v1/marketAdmin/vas/terminal/enroll/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11819', 'loadEnrollTerminalHistoryDetail', '/v1/marketAdmin/vas/terminal/enroll/history/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11820', 'downloadCurrentEnrollTerminal', '/v1/marketAdmin/vas/terminal/enroll/{marketId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11821', 'updateMarketDevServiceStatus', '/v1/marketAdmin/vas/{developerId}/service/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11822', 'getVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11823', 'agreeVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11824', 'findCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11825', 'searchMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11826', 'exportCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/export/current/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11827', 'exportMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/export/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11828', 'exportHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11829', 'findHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11830', 'getUsageDashBoard', '/v1/marketAdmin/vas/{serviceType}/usage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11831', 'getUser_1', '/v1/merchant/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11832', 'findPurchaseAppList', '/v1/merchant/dashboard/purchased-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11833', 'loadWidgetModelTerminal_1', '/v1/merchant/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11834', 'loadWidgetTerminalOffline_1', '/v1/merchant/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11835', 'loadMerchantPortalWidget', '/v1/merchant/dashboard/widgets/W21', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11836', 'getMarket_1', '/v1/merchant/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11837', 'getAgreementSystem', '/v1/mobile/account/agreement/system', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11838', 'getAgreement', '/v1/mobile/account/agreement/{agreementId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11839', 'changeLoginType', '/v1/mobile/account/login/type/{operation}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11840', 'listMessages', '/v1/mobile/account/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11841', 'readAllMessage', '/v1/mobile/account/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11842', 'getMessagesStats', '/v1/mobile/account/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11843', 'readMessage', '/v1/mobile/account/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11844', 'deleteMessage', '/v1/mobile/account/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11845', 'viewMessageDetails', '/v1/mobile/account/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11846', 'mobileAppAdmin', '/v1/mobile/account/mobile-app-admin', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11847', 'synMobileMessageToken', '/v1/mobile/account/mobile-message-token', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11848', 'getAccountProfile', '/v1/mobile/account/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11849', 'createUserAgreement', '/v1/mobile/account/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11850', 'validateOneTimePassword', '/v1/mobile/account/verify-otp', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11851', 'currentUser', '/v1/mobile/admin/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11852', 'getUserMarkets', '/v1/mobile/admin/user/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11853', 'getUserMarketResellers', '/v1/mobile/admin/user/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11854', 'getWorkspace', '/v1/mobile/admin/workspace', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11855', 'searchAlarm', '/v1/mobile/alarm', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11856', 'searchAlarm_1', '/v1/mobile/alarm/app-sign-failed', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11857', 'getAlarmWidgets', '/v1/mobile/alarm/widgets/digital', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11858', 'getApps', '/v1/mobile/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11859', 'getApkInfo', '/v1/mobile/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11860', 'createApkDownloadTask', '/v1/mobile/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11861', 'createApkParamTemplateDownloadTask', '/v1/mobile/apps/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11862', 'deleteApkParamTemplate', '/v1/mobile/apps/apks/{apkId}/param-template', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11863', 'reSignApk', '/v1/mobile/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11864', 'findApkSignatureList', '/v1/mobile/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11865', 'getAppInfo', '/v1/mobile/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11866', 'deleteApp', '/v1/mobile/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11867', 'activeApp', '/v1/mobile/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11868', 'searchApk', '/v1/mobile/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11869', 'deleteApk', '/v1/mobile/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11870', 'approveApp', '/v1/mobile/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11871', 'offlineApk', '/v1/mobile/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11872', 'onlineApk', '/v1/mobile/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11873', 'rejectApp', '/v1/mobile/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11874', 'disableApp', '/v1/mobile/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11875', 'createBuriedPoints', '/v1/mobile/buried-points', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11876', 'getMobileAppVersion', '/v1/mobile/common/app-version', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11877', 'generateCaptcha', '/v1/mobile/common/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11878', 'forgetPassword', '/v1/mobile/common/forget-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11879', 'searchPendingApps', '/v1/mobile/dashboard/apps-pending', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11880', 'searchAppsTop10', '/v1/mobile/dashboard/apps-top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11881', 'getUrl', '/v1/mobile/dashboard/cloud_data/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11882', 'getLayout', '/v1/mobile/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11883', 'searchMarkers', '/v1/mobile/dashboard/map-markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11884', 'getTerminalNumberStatisticData', '/v1/mobile/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11885', 'getTerminalNumberOfResellerData', '/v1/mobile/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11886', 'getFmTerminalForWidget', '/v1/mobile/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11887', 'exportFmTerminalOrgWidget', '/v1/mobile/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11888', 'getClientTerminalWidget', '/v1/mobile/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11889', 'exportClientTerminalWidget', '/v1/mobile/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11890', 'loadWidgetModelTerminal', '/v1/mobile/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11891', 'exportModelTerminalWidget', '/v1/mobile/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11892', 'loadWidgetTerminalOffline', '/v1/mobile/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11893', 'exportTerminalOfflineWidget', '/v1/mobile/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11894', 'getFmTerminalWidget', '/v1/mobile/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11895', 'exportFmTerminalWidget', '/v1/mobile/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11896', 'loadClientTerminalWidget', '/v1/mobile/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11897', 'downloadClientTerminalWidget', '/v1/mobile/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11898', 'createExportTerminalsDownloadTask', '/v1/mobile/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11899', 'getWidgetCardNumberActive', '/v1/mobile/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11900', 'getWidgetDigitalDisplaySetting', '/v1/mobile/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11901', 'getPukTerminalWidget', '/v1/mobile/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11902', 'exportPUKTerminalWidget', '/v1/mobile/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11903', 'getDevelopers', '/v1/mobile/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11904', 'getDeveloper', '/v1/mobile/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11905', 'deleteDeveloper', '/v1/mobile/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11906', 'approveDeveloper', '/v1/mobile/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11907', 'rejectDeveloper', '/v1/mobile/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11908', 'resumeDeveloper', '/v1/mobile/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11909', 'updateDeveloperSuperAdmin', '/v1/mobile/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11910', 'suspendDeveloper', '/v1/mobile/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11911', 'download2', '/v1/mobile/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11912', 'download1', '/v1/mobile/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11913', 'getDownloadUrl', '/v1/mobile/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11914', 'searchList', '/v1/mobile/terminal/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11915', 'searchSingle', '/v1/mobile/terminal/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11916', 'getTerminal', '/v1/mobile/terminal/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11917', 'deleteTerminal', '/v1/mobile/terminal/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11918', 'activeTerminal', '/v1/mobile/terminal/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11919', 'disableTerminal_1', '/v1/mobile/terminal/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11920', 'lockTerminal', '/v1/mobile/terminal/{terminalId}/lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11921', 'getTerminalPedStatus', '/v1/mobile/terminal/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11922', 'unlockTerminal', '/v1/mobile/terminal/{terminalId}/unlock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11923', 'findDetectionHistoryPage', '/v1/mobile/vas/air-shield/attestation/{terminalId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11924', 'createNavigoBuriedPoints', '/v1/navigo/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11925', 'searchApps', '/v1/portal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11926', 'getAdvertisement', '/v1/portal/apps/advertisement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11927', 'searchOnlineAppCategories', '/v1/portal/apps/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11928', 'findDeveloperApp', '/v1/portal/apps/developer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11929', 'findFeaturedApp', '/v1/portal/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11930', 'getAppDetailByPackageName', '/v1/portal/apps/packageName', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11931', 'searchAppsRank', '/v1/portal/apps/rank', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11932', 'findRelatedApp', '/v1/portal/apps/related', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11933', 'getAppTypes', '/v1/portal/apps/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11934', 'getAppDetail', '/v1/portal/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11935', 'getUser', '/v1/portal/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11936', 'getMarket', '/v1/portal/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11937', 'sysVersion', '/v1/public/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '11554'),
  ('1', '11557'),
  ('1', '11559'),
  ('1', '11560'),

  ('2', '10446'),
  ('2', '11505'),
  ('2', '11506'),
  ('2', '11538'),
  ('2', '11552'),
  ('2', '11562'),
  ('2', '11563'),
  ('2', '11564'),
  ('2', '11565'),
  ('2', '11566'),
  ('2', '11567'),
  ('2', '11568'),
  ('2', '11569'),
  ('2', '11570'),
  ('2', '11571'),
  ('2', '11572'),
  ('2', '11573'),
  ('2', '11574'),
  ('2', '11575'),
  ('2', '11576'),
  ('2', '11577'),
  ('2', '11578'),
  ('2', '11579'),
  ('2', '11580'),
  ('2', '11581'),
  ('2', '11582'),
  ('2', '11583'),
  ('2', '11584'),
  ('2', '11585'),
  ('2', '11586'),
  ('2', '11587'),
  ('2', '11588'),
  ('2', '11589'),
  ('2', '11590'),
  ('2', '11591'),
  ('2', '11592'),
  ('2', '11593'),
  ('2', '11594'),
  ('2', '11595'),
  ('2', '11596'),
  ('2', '11597'),
  ('2', '11598'),
  ('2', '11599'),
  ('2', '11600'),
  ('2', '11601'),
  ('2', '11602'),
  ('2', '11603'),
  ('2', '11604'),
  ('2', '11605'),
  ('2', '11606'),
  ('2', '11607'),
  ('2', '11608'),
  ('2', '11609'),
  ('2', '11610'),
  ('2', '11614'),
  ('2', '11615'),
  ('2', '11616'),
  ('2', '11618'),
  ('2', '11619'),
  ('2', '11620'),
  ('2', '11621'),
  ('2', '11622'),
  ('2', '11623'),
  ('2', '11624'),
  ('2', '11625'),
  ('2', '11626'),
  ('2', '11627'),
  ('2', '11628'),
  ('2', '11629'),
  ('2', '11630'),
  ('2', '11631'),
  ('2', '11632'),
  ('2', '11633'),
  ('2', '11634'),
  ('2', '11635'),
  ('2', '11636'),
  ('2', '11637'),
  ('2', '11638'),
  ('2', '11639'),
  ('2', '11640'),
  ('2', '11641'),
  ('2', '11642'),
  ('2', '11643'),
  ('2', '11644'),
  ('2', '11645'),
  ('2', '11646'),
  ('2', '11647'),
  ('2', '11648'),
  ('2', '11649'),
  ('2', '11650'),
  ('2', '11651'),
  ('2', '11652'),
  ('2', '11653'),
  ('2', '11654'),
  ('2', '11655'),
  ('2', '11656'),
  ('2', '11657'),
  ('2', '11658'),
  ('2', '11659'),
  ('2', '11660'),
  ('2', '11661'),
  ('2', '11662'),
  ('2', '11663'),
  ('2', '11664'),
  ('2', '11665'),
  ('2', '11666'),
  ('2', '11667'),
  ('2', '11668'),
  ('2', '11669'),
  ('2', '11670'),
  ('2', '11671'),
  ('2', '11672'),
  ('2', '11673'),
  ('2', '11674'),
  ('2', '11675'),
  ('2', '11676'),
  ('2', '11677'),
  ('2', '11678'),
  ('2', '11679'),
  ('2', '11680'),
  ('2', '11681'),
  ('2', '11682'),
  ('2', '11683'),
  ('2', '11684'),

  ('3', '10844'),
  ('3', '10845'),
  ('3', '10846'),
  ('3', '10847'),
  ('3', '10848'),
  ('3', '10849'),
  ('3', '10850'),
  ('3', '10851'),
  ('3', '10852'),
  ('3', '10853'),
  ('3', '10854'),
  ('3', '10855'),
  ('3', '10856'),
  ('3', '10857'),
  ('3', '10858'),
  ('3', '10859'),
  ('3', '10860'),
  ('3', '10861'),
  ('3', '10862'),
  ('3', '10863'),
  ('3', '10864'),
  ('3', '10865'),
  ('3', '10866'),
  ('3', '10867'),
  ('3', '10868'),
  ('3', '10869'),
  ('3', '10870'),
  ('3', '10871'),
  ('3', '10872'),
  ('3', '10873'),

  ('20', '11832'),
  ('20', '11833'),
  ('20', '11834'),
  ('20', '11835'),

  ('51', '10446'),
  ('51', '10461'),
  ('51', '10464'),
  ('51', '10465'),
  ('51', '10466'),
  ('51', '10467'),
  ('51', '10468'),
  ('51', '10469'),
  ('51', '10470'),
  ('51', '10471'),
  ('51', '10472'),
  ('51', '10473'),
  ('51', '10474'),
  ('51', '10475'),
  ('51', '10476'),
  ('51', '10477'),
  ('51', '10478'),
  ('51', '10479'),
  ('51', '10480'),
  ('51', '10481'),
  ('51', '10482'),
  ('51', '10483'),
  ('51', '10484'),
  ('51', '10485'),
  ('51', '10486'),
  ('51', '10487'),
  ('51', '10488'),
  ('51', '10489'),
  ('51', '10490'),
  ('51', '11538'),
  ('51', '11879'),
  ('51', '11880'),
  ('51', '11881'),
  ('51', '11882'),
  ('51', '11883'),
  ('51', '11884'),
  ('51', '11885'),
  ('51', '11886'),
  ('51', '11887'),
  ('51', '11888'),
  ('51', '11889'),
  ('51', '11890'),
  ('51', '11891'),
  ('51', '11892'),
  ('51', '11893'),
  ('51', '11894'),
  ('51', '11895'),
  ('51', '11896'),
  ('51', '11897'),
  ('51', '11898'),
  ('51', '11899'),
  ('51', '11900'),
  ('51', '11901'),
  ('51', '11902'),

  ('53', '10585'),
  ('53', '10586'),
  ('53', '10587'),
  ('53', '10588'),
  ('53', '10589'),
  ('53', '10590'),
  ('53', '10591'),
  ('53', '10592'),
  ('53', '10593'),

  ('1041', '10323'),
  ('1041', '10324'),
  ('1041', '10326'),
  ('1041', '10327'),
  ('1041', '10330'),
  ('1041', '10331'),
  ('1041', '10332'),
  ('1041', '10448'),
  ('1041', '10460'),
  ('1041', '10461'),
  ('1041', '11855'),
  ('1041', '11856'),
  ('1041', '11857'),
  ('1041', '11923'),

  ('1042', '10323'),
  ('1042', '10324'),
  ('1042', '10325'),
  ('1042', '10326'),
  ('1042', '10327'),
  ('1042', '10328'),
  ('1042', '10329'),
  ('1042', '10330'),
  ('1042', '10331'),
  ('1042', '10332'),
  ('1042', '10448'),
  ('1042', '10460'),
  ('1042', '10461'),
  ('1042', '11855'),
  ('1042', '11856'),
  ('1042', '11857'),
  ('1042', '11923'),

  ('601', '10448'),
  ('601', '10452'),
  ('601', '10455'),
  ('601', '10456'),
  ('601', '10460'),
  ('601', '10461'),
  ('601', '11013'),
  ('601', '11014'),
  ('601', '11015'),
  ('601', '11018'),
  ('601', '11020'),
  ('601', '11021'),
  ('601', '11022'),
  ('601', '11024'),
  ('601', '11026'),
  ('601', '11027'),
  ('601', '11030'),
  ('601', '11033'),
  ('601', '11034'),
  ('601', '11035'),
  ('601', '11036'),
  ('601', '11037'),
  ('601', '11038'),
  ('601', '11042'),
  ('601', '11043'),
  ('601', '11044'),
  ('601', '11045'),
  ('601', '11503'),
  ('601', '11505'),
  ('601', '11506'),
  ('601', '11507'),

  ('602', '10448'),
  ('602', '10452'),
  ('602', '10455'),
  ('602', '10456'),
  ('602', '10460'),
  ('602', '10461'),
  ('602', '11013'),
  ('602', '11014'),
  ('602', '11015'),
  ('602', '11016'),
  ('602', '11017'),
  ('602', '11018'),
  ('602', '11019'),
  ('602', '11020'),
  ('602', '11021'),
  ('602', '11022'),
  ('602', '11023'),
  ('602', '11024'),
  ('602', '11025'),
  ('602', '11026'),
  ('602', '11027'),
  ('602', '11028'),
  ('602', '11029'),
  ('602', '11030'),
  ('602', '11031'),
  ('602', '11032'),
  ('602', '11033'),
  ('602', '11034'),
  ('602', '11035'),
  ('602', '11036'),
  ('602', '11037'),
  ('602', '11038'),
  ('602', '11039'),
  ('602', '11040'),
  ('602', '11041'),
  ('602', '11042'),
  ('602', '11043'),
  ('602', '11044'),
  ('602', '11045'),
  ('602', '11503'),
  ('602', '11504'),
  ('602', '11505'),
  ('602', '11506'),
  ('602', '11507'),
  ('602', '11510'),
  ('602', '11511'),

  ('611', '10337'),
  ('611', '10338'),
  ('611', '10339'),
  ('611', '10340'),
  ('611', '10343'),
  ('611', '10344'),
  ('611', '10346'),
  ('611', '10349'),
  ('611', '10351'),
  ('611', '10352'),
  ('611', '10353'),
  ('611', '10354'),
  ('611', '10355'),
  ('611', '10356'),
  ('611', '10357'),
  ('611', '10360'),
  ('611', '10371'),
  ('611', '10379'),
  ('611', '10380'),
  ('611', '10383'),
  ('611', '10384'),
  ('611', '10387'),
  ('611', '10390'),
  ('611', '10392'),
  ('611', '10393'),
  ('611', '10444'),
  ('611', '10448'),
  ('611', '10455'),
  ('611', '10456'),
  ('611', '10460'),
  ('611', '10461'),
  ('611', '10523'),
  ('611', '10524'),
  ('611', '10529'),
  ('611', '10531'),
  ('611', '10533'),
  ('611', '10538'),
  ('611', '10539'),
  ('611', '10541'),
  ('611', '10543'),
  ('611', '11503'),
  ('611', '11505'),
  ('611', '11506'),
  ('611', '11507'),
  ('611', '11858'),
  ('611', '11859'),
  ('611', '11860'),
  ('611', '11861'),
  ('611', '11864'),
  ('611', '11865'),
  ('611', '11868'),

  ('612', '10337'),
  ('612', '10338'),
  ('612', '10339'),
  ('612', '10340'),
  ('612', '10341'),
  ('612', '10342'),
  ('612', '10343'),
  ('612', '10344'),
  ('612', '10345'),
  ('612', '10346'),
  ('612', '10347'),
  ('612', '10348'),
  ('612', '10349'),
  ('612', '10350'),
  ('612', '10351'),
  ('612', '10352'),
  ('612', '10353'),
  ('612', '10354'),
  ('612', '10355'),
  ('612', '10356'),
  ('612', '10357'),
  ('612', '10358'),
  ('612', '10359'),
  ('612', '10360'),
  ('612', '10361'),
  ('612', '10362'),
  ('612', '10363'),
  ('612', '10364'),
  ('612', '10365'),
  ('612', '10366'),
  ('612', '10367'),
  ('612', '10368'),
  ('612', '10369'),
  ('612', '10370'),
  ('612', '10371'),
  ('612', '10372'),
  ('612', '10373'),
  ('612', '10374'),
  ('612', '10375'),
  ('612', '10376'),
  ('612', '10377'),
  ('612', '10378'),
  ('612', '10379'),
  ('612', '10380'),
  ('612', '10381'),
  ('612', '10382'),
  ('612', '10383'),
  ('612', '10384'),
  ('612', '10385'),
  ('612', '10386'),
  ('612', '10387'),
  ('612', '10388'),
  ('612', '10389'),
  ('612', '10390'),
  ('612', '10391'),
  ('612', '10392'),
  ('612', '10393'),
  ('612', '10394'),
  ('612', '10444'),
  ('612', '10448'),
  ('612', '10455'),
  ('612', '10456'),
  ('612', '10460'),
  ('612', '10461'),
  ('612', '10523'),
  ('612', '10524'),
  ('612', '10525'),
  ('612', '10526'),
  ('612', '10527'),
  ('612', '10528'),
  ('612', '10529'),
  ('612', '10530'),
  ('612', '10531'),
  ('612', '10532'),
  ('612', '10533'),
  ('612', '10534'),
  ('612', '10535'),
  ('612', '10536'),
  ('612', '10537'),
  ('612', '10538'),
  ('612', '10539'),
  ('612', '10540'),
  ('612', '10541'),
  ('612', '10543'),
  ('612', '10544'),
  ('612', '10545'),
  ('612', '10546'),
  ('612', '11503'),
  ('612', '11504'),
  ('612', '11505'),
  ('612', '11506'),
  ('612', '11507'),
  ('612', '11510'),
  ('612', '11511'),
  ('612', '11858'),
  ('612', '11859'),
  ('612', '11860'),
  ('612', '11861'),
  ('612', '11862'),
  ('612', '11863'),
  ('612', '11864'),
  ('612', '11865'),
  ('612', '11866'),
  ('612', '11867'),
  ('612', '11868'),
  ('612', '11869'),
  ('612', '11870'),
  ('612', '11871'),
  ('612', '11872'),
  ('612', '11873'),
  ('612', '11874'),

  ('661', '10448'),
  ('661', '10452'),
  ('661', '10455'),
  ('661', '10460'),
  ('661', '10461'),
  ('661', '10960'),
  ('661', '10961'),
  ('661', '10962'),
  ('661', '10963'),
  ('661', '10964'),
  ('661', '10966'),

  ('662', '10448'),
  ('662', '10452'),
  ('662', '10455'),
  ('662', '10460'),
  ('662', '10461'),
  ('662', '10960'),
  ('662', '10961'),
  ('662', '10962'),
  ('662', '10963'),
  ('662', '10964'),
  ('662', '10965'),
  ('662', '10966'),

  ('621', '10337'),
  ('621', '10338'),
  ('621', '10339'),
  ('621', '10340'),
  ('621', '10343'),
  ('621', '10344'),
  ('621', '10346'),
  ('621', '10349'),
  ('621', '10351'),
  ('621', '10352'),
  ('621', '10353'),
  ('621', '10354'),
  ('621', '10355'),
  ('621', '10356'),
  ('621', '10357'),
  ('621', '10360'),
  ('621', '10371'),
  ('621', '10379'),
  ('621', '10380'),
  ('621', '10383'),
  ('621', '10384'),
  ('621', '10387'),
  ('621', '10390'),
  ('621', '10392'),
  ('621', '10393'),
  ('621', '10448'),
  ('621', '10455'),
  ('621', '10460'),
  ('621', '10461'),
  ('621', '10500'),
  ('621', '10501'),
  ('621', '10502'),
  ('621', '10516'),
  ('621', '10518'),
  ('621', '10522'),
  ('621', '10523'),
  ('621', '10524'),
  ('621', '10529'),
  ('621', '10530'),
  ('621', '10531'),
  ('621', '10533'),
  ('621', '10538'),
  ('621', '10539'),
  ('621', '10541'),
  ('621', '10542'),
  ('621', '10543'),
  ('621', '11503'),
  ('621', '11505'),
  ('621', '11506'),
  ('621', '11507'),
  ('621', '11903'),
  ('621', '11904'),

  ('622', '10337'),
  ('622', '10338'),
  ('622', '10339'),
  ('622', '10340'),
  ('622', '10343'),
  ('622', '10344'),
  ('622', '10346'),
  ('622', '10349'),
  ('622', '10351'),
  ('622', '10352'),
  ('622', '10353'),
  ('622', '10354'),
  ('622', '10355'),
  ('622', '10356'),
  ('622', '10357'),
  ('622', '10360'),
  ('622', '10371'),
  ('622', '10379'),
  ('622', '10380'),
  ('622', '10383'),
  ('622', '10384'),
  ('622', '10387'),
  ('622', '10390'),
  ('622', '10392'),
  ('622', '10393'),
  ('622', '10448'),
  ('622', '10455'),
  ('622', '10460'),
  ('622', '10461'),
  ('622', '10500'),
  ('622', '10501'),
  ('622', '10502'),
  ('622', '10503'),
  ('622', '10504'),
  ('622', '10505'),
  ('622', '10506'),
  ('622', '10507'),
  ('622', '10508'),
  ('622', '10509'),
  ('622', '10510'),
  ('622', '10511'),
  ('622', '10512'),
  ('622', '10513'),
  ('622', '10514'),
  ('622', '10515'),
  ('622', '10516'),
  ('622', '10517'),
  ('622', '10518'),
  ('622', '10519'),
  ('622', '10520'),
  ('622', '10521'),
  ('622', '10522'),
  ('622', '10523'),
  ('622', '10524'),
  ('622', '10529'),
  ('622', '10530'),
  ('622', '10531'),
  ('622', '10533'),
  ('622', '10538'),
  ('622', '10539'),
  ('622', '10541'),
  ('622', '10542'),
  ('622', '10543'),
  ('622', '11503'),
  ('622', '11505'),
  ('622', '11506'),
  ('622', '11507'),
  ('622', '11903'),
  ('622', '11904'),
  ('622', '11905'),
  ('622', '11906'),
  ('622', '11907'),
  ('622', '11908'),
  ('622', '11909'),
  ('622', '11910'),

  ('641', '11492'),
  ('641', '11493'),
  ('641', '11494'),
  ('641', '11496'),
  ('641', '11497'),

  ('642', '11492'),
  ('642', '11493'),
  ('642', '11494'),
  ('642', '11495'),
  ('642', '11496'),
  ('642', '11497'),
  ('642', '11498'),

  ('651', '10448'),
  ('651', '10456'),
  ('651', '10460'),
  ('651', '10461'),
  ('651', '10523'),
  ('651', '10524'),
  ('651', '10529'),
  ('651', '10531'),
  ('651', '10533'),
  ('651', '10538'),
  ('651', '10539'),
  ('651', '10541'),
  ('651', '10543'),
  ('651', '10939'),
  ('651', '10940'),
  ('651', '10942'),
  ('651', '10943'),
  ('651', '10946'),
  ('651', '10947'),
  ('651', '10948'),
  ('651', '10949'),
  ('651', '10950'),
  ('651', '10951'),
  ('651', '10952'),
  ('651', '10953'),
  ('651', '10954'),
  ('651', '10955'),
  ('651', '10958'),
  ('651', '10959'),
  ('651', '11506'),
  ('651', '11507'),

  ('652', '10448'),
  ('652', '10456'),
  ('652', '10460'),
  ('652', '10461'),
  ('652', '10523'),
  ('652', '10524'),
  ('652', '10525'),
  ('652', '10526'),
  ('652', '10527'),
  ('652', '10528'),
  ('652', '10529'),
  ('652', '10530'),
  ('652', '10531'),
  ('652', '10532'),
  ('652', '10533'),
  ('652', '10534'),
  ('652', '10535'),
  ('652', '10536'),
  ('652', '10537'),
  ('652', '10538'),
  ('652', '10539'),
  ('652', '10540'),
  ('652', '10541'),
  ('652', '10543'),
  ('652', '10544'),
  ('652', '10545'),
  ('652', '10546'),
  ('652', '10939'),
  ('652', '10940'),
  ('652', '10941'),
  ('652', '10942'),
  ('652', '10943'),
  ('652', '10944'),
  ('652', '10945'),
  ('652', '10946'),
  ('652', '10947'),
  ('652', '10948'),
  ('652', '10949'),
  ('652', '10950'),
  ('652', '10951'),
  ('652', '10952'),
  ('652', '10953'),
  ('652', '10954'),
  ('652', '10955'),
  ('652', '10956'),
  ('652', '10957'),
  ('652', '10958'),
  ('652', '10959'),
  ('652', '11506'),
  ('652', '11507'),

  ('671', '10333'),
  ('671', '10335'),
  ('671', '10452'),
  ('671', '10636'),
  ('671', '11056'),
  ('671', '11057'),
  ('671', '11060'),
  ('671', '11104'),
  ('671', '11107'),
  ('671', '11163'),
  ('671', '11164'),
  ('671', '11167'),
  ('671', '11211'),

  ('672', '10333'),
  ('672', '10334'),
  ('672', '10335'),
  ('672', '10336'),
  ('672', '10452'),
  ('672', '10636'),
  ('672', '11056'),
  ('672', '11057'),
  ('672', '11060'),
  ('672', '11104'),
  ('672', '11107'),
  ('672', '11163'),
  ('672', '11164'),
  ('672', '11167'),
  ('672', '11211'),

  ('711', '10437'),
  ('711', '10438'),
  ('711', '10440'),
  ('711', '10442'),
  ('711', '10443'),
  ('711', '10444'),
  ('711', '10445'),
  ('711', '10447'),
  ('711', '10448'),
  ('711', '10451'),
  ('711', '10452'),
  ('711', '10454'),
  ('711', '10456'),
  ('711', '10458'),
  ('711', '10460'),
  ('711', '10461'),
  ('711', '10523'),
  ('711', '10538'),
  ('711', '10542'),
  ('711', '10547'),
  ('711', '10550'),
  ('711', '10636'),
  ('711', '11056'),
  ('711', '11057'),
  ('711', '11060'),
  ('711', '11104'),
  ('711', '11107'),
  ('711', '11212'),
  ('711', '11213'),
  ('711', '11214'),
  ('711', '11215'),
  ('711', '11216'),
  ('711', '11217'),
  ('711', '11218'),
  ('711', '11219'),
  ('711', '11220'),
  ('711', '11221'),
  ('711', '11224'),
  ('711', '11229'),
  ('711', '11233'),
  ('711', '11234'),
  ('711', '11236'),
  ('711', '11237'),
  ('711', '11238'),
  ('711', '11239'),
  ('711', '11242'),
  ('711', '11245'),
  ('711', '11250'),
  ('711', '11253'),
  ('711', '11260'),
  ('711', '11265'),
  ('711', '11266'),
  ('711', '11270'),
  ('711', '11273'),
  ('711', '11274'),
  ('711', '11275'),
  ('711', '11281'),
  ('711', '11286'),
  ('711', '11287'),
  ('711', '11289'),
  ('711', '11290'),
  ('711', '11296'),
  ('711', '11300'),
  ('711', '11303'),
  ('711', '11307'),
  ('711', '11309'),
  ('711', '11310'),
  ('711', '11311'),
  ('711', '11314'),
  ('711', '11315'),
  ('711', '11316'),
  ('711', '11319'),
  ('711', '11323'),
  ('711', '11324'),
  ('711', '11325'),
  ('711', '11326'),
  ('711', '11327'),
  ('711', '11328'),
  ('711', '11331'),
  ('711', '11332'),
  ('711', '11333'),
  ('711', '11334'),
  ('711', '11335'),
  ('711', '11336'),
  ('711', '11337'),
  ('711', '11338'),
  ('711', '11339'),
  ('711', '11343'),
  ('711', '11344'),
  ('711', '11346'),
  ('711', '11351'),
  ('711', '11353'),
  ('711', '11354'),
  ('711', '11355'),
  ('711', '11356'),
  ('711', '11359'),
  ('711', '11362'),
  ('711', '11364'),
  ('711', '11369'),
  ('711', '11372'),
  ('711', '11373'),
  ('711', '11377'),
  ('711', '11378'),
  ('711', '11380'),
  ('711', '11381'),
  ('711', '11382'),
  ('711', '11383'),
  ('711', '11384'),
  ('711', '11385'),
  ('711', '11387'),
  ('711', '11388'),
  ('711', '11389'),
  ('711', '11397'),
  ('711', '11398'),
  ('711', '11399'),
  ('711', '11400'),
  ('711', '11412'),
  ('711', '11413'),
  ('711', '11414'),
  ('711', '11415'),
  ('711', '11416'),
  ('711', '11417'),
  ('711', '11418'),
  ('711', '11421'),
  ('711', '11423'),
  ('711', '11424'),
  ('711', '11425'),
  ('711', '11426'),
  ('711', '11427'),
  ('711', '11428'),
  ('711', '11429'),
  ('711', '11431'),
  ('711', '11432'),
  ('711', '11433'),
  ('711', '11434'),
  ('711', '11436'),
  ('711', '11439'),
  ('711', '11442'),
  ('711', '11444'),
  ('711', '11445'),
  ('711', '11446'),
  ('711', '11447'),
  ('711', '11449'),
  ('711', '11452'),
  ('711', '11453'),
  ('711', '11483'),
  ('711', '11485'),
  ('711', '11486'),
  ('711', '11489'),
  ('711', '11914'),
  ('711', '11915'),
  ('711', '11916'),
  ('711', '11921'),

  ('712', '10437'),
  ('712', '10438'),
  ('712', '10440'),
  ('712', '10442'),
  ('712', '10443'),
  ('712', '10444'),
  ('712', '10445'),
  ('712', '10447'),
  ('712', '10448'),
  ('712', '10451'),
  ('712', '10452'),
  ('712', '10454'),
  ('712', '10456'),
  ('712', '10458'),
  ('712', '10460'),
  ('712', '10461'),
  ('712', '10523'),
  ('712', '10538'),
  ('712', '10542'),
  ('712', '10547'),
  ('712', '10550'),
  ('712', '10636'),
  ('712', '11056'),
  ('712', '11057'),
  ('712', '11060'),
  ('712', '11104'),
  ('712', '11107'),
  ('712', '11212'),
  ('712', '11213'),
  ('712', '11214'),
  ('712', '11215'),
  ('712', '11216'),
  ('712', '11217'),
  ('712', '11218'),
  ('712', '11219'),
  ('712', '11220'),
  ('712', '11221'),
  ('712', '11224'),
  ('712', '11229'),
  ('712', '11233'),
  ('712', '11234'),
  ('712', '11236'),
  ('712', '11237'),
  ('712', '11238'),
  ('712', '11239'),
  ('712', '11242'),
  ('712', '11245'),
  ('712', '11250'),
  ('712', '11253'),
  ('712', '11260'),
  ('712', '11265'),
  ('712', '11266'),
  ('712', '11270'),
  ('712', '11273'),
  ('712', '11274'),
  ('712', '11275'),
  ('712', '11281'),
  ('712', '11286'),
  ('712', '11287'),
  ('712', '11289'),
  ('712', '11290'),
  ('712', '11296'),
  ('712', '11300'),
  ('712', '11303'),
  ('712', '11307'),
  ('712', '11309'),
  ('712', '11310'),
  ('712', '11311'),
  ('712', '11314'),
  ('712', '11315'),
  ('712', '11316'),
  ('712', '11319'),
  ('712', '11323'),
  ('712', '11324'),
  ('712', '11325'),
  ('712', '11326'),
  ('712', '11327'),
  ('712', '11328'),
  ('712', '11331'),
  ('712', '11332'),
  ('712', '11333'),
  ('712', '11334'),
  ('712', '11335'),
  ('712', '11336'),
  ('712', '11337'),
  ('712', '11338'),
  ('712', '11339'),
  ('712', '11343'),
  ('712', '11344'),
  ('712', '11346'),
  ('712', '11351'),
  ('712', '11353'),
  ('712', '11354'),
  ('712', '11355'),
  ('712', '11356'),
  ('712', '11359'),
  ('712', '11362'),
  ('712', '11364'),
  ('712', '11369'),
  ('712', '11372'),
  ('712', '11373'),
  ('712', '11377'),
  ('712', '11378'),
  ('712', '11380'),
  ('712', '11381'),
  ('712', '11382'),
  ('712', '11383'),
  ('712', '11384'),
  ('712', '11385'),
  ('712', '11387'),
  ('712', '11388'),
  ('712', '11389'),
  ('712', '11397'),
  ('712', '11398'),
  ('712', '11399'),
  ('712', '11400'),
  ('712', '11412'),
  ('712', '11413'),
  ('712', '11414'),
  ('712', '11415'),
  ('712', '11416'),
  ('712', '11417'),
  ('712', '11418'),
  ('712', '11421'),
  ('712', '11423'),
  ('712', '11424'),
  ('712', '11425'),
  ('712', '11426'),
  ('712', '11427'),
  ('712', '11428'),
  ('712', '11429'),
  ('712', '11431'),
  ('712', '11432'),
  ('712', '11433'),
  ('712', '11434'),
  ('712', '11436'),
  ('712', '11439'),
  ('712', '11442'),
  ('712', '11444'),
  ('712', '11445'),
  ('712', '11446'),
  ('712', '11447'),
  ('712', '11449'),
  ('712', '11452'),
  ('712', '11453'),
  ('712', '11483'),
  ('712', '11484'),
  ('712', '11485'),
  ('712', '11486'),
  ('712', '11489'),
  ('712', '11914'),
  ('712', '11915'),
  ('712', '11916'),
  ('712', '11921'),

  ('7124', '11344'),
  ('7124', '11345'),
  ('7124', '11346'),
  ('7124', '11347'),
  ('7124', '11348'),
  ('7124', '11349'),
  ('7124', '11350'),

  ('7126', '11351'),
  ('7126', '11352'),
  ('7126', '11353'),
  ('7126', '11354'),
  ('7126', '11355'),
  ('7126', '11356'),
  ('7126', '11357'),
  ('7126', '11358'),
  ('7126', '11359'),
  ('7126', '11360'),
  ('7126', '11361'),

  ('71211', '11285'),
  ('71211', '11286'),
  ('71211', '11287'),
  ('71211', '11288'),
  ('71211', '11289'),
  ('71211', '11290'),
  ('71211', '11291'),
  ('71211', '11292'),
  ('71211', '11293'),
  ('71211', '11294'),
  ('71211', '11295'),
  ('71211', '11296'),
  ('71211', '11298'),
  ('71211', '11299'),
  ('71211', '11300'),
  ('71211', '11301'),
  ('71211', '11302'),
  ('71211', '11303'),
  ('71211', '11304'),
  ('71211', '11305'),
  ('71211', '11306'),

  ('71213', '11269'),
  ('71213', '11270'),
  ('71213', '11271'),
  ('71213', '11272'),
  ('71213', '11273'),
  ('71213', '11274'),
  ('71213', '11275'),
  ('71213', '11276'),
  ('71213', '11277'),
  ('71213', '11278'),
  ('71213', '11279'),
  ('71213', '11280'),
  ('71213', '11281'),
  ('71213', '11283'),
  ('71213', '11284'),

  ('71212', '10551'),
  ('71212', '10552'),
  ('71212', '11297'),

  ('71214', '10548'),
  ('71214', '10549'),
  ('71214', '11282'),

  ('71221', '11229'),
  ('71221', '11230'),
  ('71221', '11231'),
  ('71221', '11232'),
  ('71221', '11233'),
  ('71221', '11234'),
  ('71221', '11235'),
  ('71221', '11236'),
  ('71221', '11237'),
  ('71221', '11238'),
  ('71221', '11239'),
  ('71221', '11240'),
  ('71221', '11241'),
  ('71221', '11242'),
  ('71221', '11243'),
  ('71221', '11244'),
  ('71221', '11245'),
  ('71221', '11246'),
  ('71221', '11247'),
  ('71221', '11248'),
  ('71221', '11249'),
  ('71221', '11250'),
  ('71221', '11251'),
  ('71221', '11252'),
  ('71221', '11253'),
  ('71221', '11254'),
  ('71221', '11255'),
  ('71221', '11256'),
  ('71221', '11257'),
  ('71221', '11258'),
  ('71221', '11259'),
  ('71221', '11376'),
  ('71221', '11377'),
  ('71221', '11378'),
  ('71221', '11379'),
  ('71221', '11380'),
  ('71221', '11381'),
  ('71221', '11382'),
  ('71221', '11383'),
  ('71221', '11384'),
  ('71221', '11385'),
  ('71221', '11386'),
  ('71221', '11387'),
  ('71221', '11388'),
  ('71221', '11389'),
  ('71221', '11390'),
  ('71221', '11391'),
  ('71221', '11392'),
  ('71221', '11393'),
  ('71221', '11394'),
  ('71221', '11395'),
  ('71221', '11396'),
  ('71221', '11397'),
  ('71221', '11398'),
  ('71221', '11399'),
  ('71221', '11400'),
  ('71221', '11401'),
  ('71221', '11402'),
  ('71221', '11403'),
  ('71221', '11404'),
  ('71221', '11405'),
  ('71221', '11406'),
  ('71221', '11407'),
  ('71221', '11408'),
  ('71221', '11409'),
  ('71221', '11410'),
  ('71221', '11411'),
  ('71221', '11412'),
  ('71221', '11413'),
  ('71221', '11414'),
  ('71221', '11415'),
  ('71221', '11416'),
  ('71221', '11417'),
  ('71221', '11418'),
  ('71221', '11419'),
  ('71221', '11420'),
  ('71221', '11421'),
  ('71221', '11422'),
  ('71221', '11423'),
  ('71221', '11424'),
  ('71221', '11425'),
  ('71221', '11426'),
  ('71221', '11427'),
  ('71221', '11428'),
  ('71221', '11429'),
  ('71221', '11430'),
  ('71221', '11431'),
  ('71221', '11432'),
  ('71221', '11433'),
  ('71221', '11434'),
  ('71221', '11435'),
  ('71221', '11436'),
  ('71221', '11437'),
  ('71221', '11438'),
  ('71221', '11439'),
  ('71221', '11440'),
  ('71221', '11441'),
  ('71221', '11442'),
  ('71221', '11443'),
  ('71221', '11444'),
  ('71221', '11445'),
  ('71221', '11446'),
  ('71221', '11447'),
  ('71221', '11448'),
  ('71221', '11449'),
  ('71221', '11450'),
  ('71221', '11451'),
  ('71221', '11452'),
  ('71221', '11453'),
  ('71221', '11454'),
  ('71221', '11455'),
  ('71221', '11914'),
  ('71221', '11915'),
  ('71221', '11916'),
  ('71221', '11917'),
  ('71221', '11918'),
  ('71221', '11919'),
  ('71221', '11921'),

  ('71222', '11323'),
  ('71222', '11324'),
  ('71222', '11325'),
  ('71222', '11326'),
  ('71222', '11327'),
  ('71222', '11328'),
  ('71222', '11329'),
  ('71222', '11330'),
  ('71222', '11331'),
  ('71222', '11332'),
  ('71222', '11333'),
  ('71222', '11334'),
  ('71222', '11335'),
  ('71222', '11336'),
  ('71222', '11337'),
  ('71222', '11338'),
  ('71222', '11339'),
  ('71222', '11340'),
  ('71222', '11341'),
  ('71222', '11342'),
  ('71222', '11343'),
  ('71222', '11407'),
  ('71222', '11408'),
  ('71222', '11440'),
  ('71222', '11441'),
  ('71222', '11442'),
  ('71222', '11443'),
  ('71222', '11444'),
  ('71222', '11445'),
  ('71222', '11446'),
  ('71222', '11447'),
  ('71222', '11448'),
  ('71222', '11449'),
  ('71222', '11450'),
  ('71222', '11451'),
  ('71222', '11452'),
  ('71222', '11453'),
  ('71222', '11454'),
  ('71222', '11455'),
  ('71222', '11920'),
  ('71222', '11922'),

  ('71231', '11307'),
  ('71231', '11308'),
  ('71231', '11309'),
  ('71231', '11310'),
  ('71231', '11311'),
  ('71231', '11312'),
  ('71231', '11313'),
  ('71231', '11314'),
  ('71231', '11315'),
  ('71231', '11316'),
  ('71231', '11319'),
  ('71231', '11321'),
  ('71231', '11322'),

  ('71232', '11221'),
  ('71232', '11222'),
  ('71232', '11223'),
  ('71232', '11224'),
  ('71232', '11225'),
  ('71232', '11226'),
  ('71232', '11227'),
  ('71232', '11228'),
  ('71232', '11260'),
  ('71232', '11261'),
  ('71232', '11262'),
  ('71232', '11263'),
  ('71232', '11264'),
  ('71232', '11265'),
  ('71232', '11266'),
  ('71232', '11267'),
  ('71232', '11268'),
  ('71232', '11307'),
  ('71232', '11308'),
  ('71232', '11309'),
  ('71232', '11310'),
  ('71232', '11311'),
  ('71232', '11312'),
  ('71232', '11313'),
  ('71232', '11314'),
  ('71232', '11315'),
  ('71232', '11316'),
  ('71232', '11317'),
  ('71232', '11318'),
  ('71232', '11319'),
  ('71232', '11320'),
  ('71232', '11321'),
  ('71232', '11322'),
  ('71232', '11369'),
  ('71232', '11370'),
  ('71232', '11371'),
  ('71232', '11372'),
  ('71232', '11373'),
  ('71232', '11374'),
  ('71232', '11375'),
  ('71232', '11410'),
  ('71232', '11411'),
  ('71232', '11552'),

  ('71233', '11435'),

  ('71251', '10459'),
  ('71251', '11362'),
  ('71251', '11363'),
  ('71251', '11364'),
  ('71251', '11365'),
  ('71251', '11366'),
  ('71251', '11367'),
  ('71251', '11368'),

  ('71252', '10459'),
  ('71252', '11300'),
  ('71252', '11301'),
  ('71252', '11302'),
  ('71252', '11303'),
  ('71252', '11304'),
  ('71252', '11305'),
  ('71252', '11306'),

  ('721', '10438'),
  ('721', '10440'),
  ('721', '10442'),
  ('721', '10443'),
  ('721', '10447'),
  ('721', '10448'),
  ('721', '10451'),
  ('721', '10452'),
  ('721', '10456'),
  ('721', '10458'),
  ('721', '10460'),
  ('721', '10461'),
  ('721', '10636'),
  ('721', '11046'),
  ('721', '11048'),
  ('721', '11052'),
  ('721', '11056'),
  ('721', '11057'),
  ('721', '11060'),
  ('721', '11064'),
  ('721', '11066'),
  ('721', '11067'),
  ('721', '11072'),
  ('721', '11073'),
  ('721', '11074'),
  ('721', '11076'),
  ('721', '11079'),
  ('721', '11088'),
  ('721', '11089'),
  ('721', '11092'),
  ('721', '11094'),
  ('721', '11095'),
  ('721', '11096'),
  ('721', '11097'),
  ('721', '11100'),
  ('721', '11104'),
  ('721', '11107'),
  ('721', '11109'),
  ('721', '11113'),
  ('721', '11114'),
  ('721', '11115'),
  ('721', '11117'),
  ('721', '11125'),
  ('721', '11126'),
  ('721', '11127'),
  ('721', '11129'),
  ('721', '11130'),
  ('721', '11138'),
  ('721', '11139'),
  ('721', '11140'),
  ('721', '11142'),
  ('721', '11143'),
  ('721', '11151'),
  ('721', '11152'),
  ('721', '11153'),
  ('721', '11155'),
  ('721', '11156'),
  ('721', '11159'),
  ('721', '11163'),
  ('721', '11164'),
  ('721', '11167'),
  ('721', '11171'),
  ('721', '11173'),
  ('721', '11174'),
  ('721', '11179'),
  ('721', '11180'),
  ('721', '11181'),
  ('721', '11183'),
  ('721', '11185'),
  ('721', '11193'),
  ('721', '11194'),
  ('721', '11195'),
  ('721', '11200'),
  ('721', '11201'),
  ('721', '11204'),
  ('721', '11211'),

  ('722', '10438'),
  ('722', '10440'),
  ('722', '10442'),
  ('722', '10443'),
  ('722', '10447'),
  ('722', '10448'),
  ('722', '10451'),
  ('722', '10452'),
  ('722', '10456'),
  ('722', '10458'),
  ('722', '10460'),
  ('722', '10461'),
  ('722', '10636'),
  ('722', '11046'),
  ('722', '11048'),
  ('722', '11052'),
  ('722', '11056'),
  ('722', '11057'),
  ('722', '11060'),
  ('722', '11064'),
  ('722', '11066'),
  ('722', '11067'),
  ('722', '11072'),
  ('722', '11073'),
  ('722', '11074'),
  ('722', '11076'),
  ('722', '11079'),
  ('722', '11088'),
  ('722', '11089'),
  ('722', '11092'),
  ('722', '11094'),
  ('722', '11095'),
  ('722', '11096'),
  ('722', '11097'),
  ('722', '11100'),
  ('722', '11104'),
  ('722', '11107'),
  ('722', '11109'),
  ('722', '11113'),
  ('722', '11114'),
  ('722', '11115'),
  ('722', '11117'),
  ('722', '11125'),
  ('722', '11126'),
  ('722', '11127'),
  ('722', '11129'),
  ('722', '11130'),
  ('722', '11138'),
  ('722', '11139'),
  ('722', '11140'),
  ('722', '11142'),
  ('722', '11143'),
  ('722', '11151'),
  ('722', '11152'),
  ('722', '11153'),
  ('722', '11155'),
  ('722', '11156'),
  ('722', '11159'),
  ('722', '11163'),
  ('722', '11164'),
  ('722', '11167'),
  ('722', '11171'),
  ('722', '11173'),
  ('722', '11174'),
  ('722', '11179'),
  ('722', '11180'),
  ('722', '11181'),
  ('722', '11183'),
  ('722', '11185'),
  ('722', '11193'),
  ('722', '11194'),
  ('722', '11195'),
  ('722', '11200'),
  ('722', '11201'),
  ('722', '11204'),
  ('722', '11211'),

  ('7221', '11046'),
  ('7221', '11047'),
  ('7221', '11048'),
  ('7221', '11049'),
  ('7221', '11050'),
  ('7221', '11051'),
  ('7221', '11052'),
  ('7221', '11053'),
  ('7221', '11054'),
  ('7221', '11055'),
  ('7221', '11056'),
  ('7221', '11057'),
  ('7221', '11058'),
  ('7221', '11059'),
  ('7221', '11060'),
  ('7221', '11061'),
  ('7221', '11062'),
  ('7221', '11063'),
  ('7221', '11064'),
  ('7221', '11065'),
  ('7221', '11066'),
  ('7221', '11067'),
  ('7221', '11068'),
  ('7221', '11069'),
  ('7221', '11070'),
  ('7221', '11071'),
  ('7221', '11072'),
  ('7221', '11073'),
  ('7221', '11074'),
  ('7221', '11075'),
  ('7221', '11076'),
  ('7221', '11077'),
  ('7221', '11078'),
  ('7221', '11079'),
  ('7221', '11080'),
  ('7221', '11081'),
  ('7221', '11082'),
  ('7221', '11083'),
  ('7221', '11084'),
  ('7221', '11085'),
  ('7221', '11086'),
  ('7221', '11087'),
  ('7221', '11088'),
  ('7221', '11089'),
  ('7221', '11090'),
  ('7221', '11091'),
  ('7221', '11092'),
  ('7221', '11093'),
  ('7221', '11094'),
  ('7221', '11095'),
  ('7221', '11096'),
  ('7221', '11097'),
  ('7221', '11098'),
  ('7221', '11099'),
  ('7221', '11100'),
  ('7221', '11101'),
  ('7221', '11102'),
  ('7221', '11103'),
  ('7221', '11104'),
  ('7221', '11105'),
  ('7221', '11106'),
  ('7221', '11107'),
  ('7221', '11108'),
  ('7221', '11109'),
  ('7221', '11110'),
  ('7221', '11111'),
  ('7221', '11112'),
  ('7221', '11113'),
  ('7221', '11114'),
  ('7221', '11115'),
  ('7221', '11116'),
  ('7221', '11117'),
  ('7221', '11118'),
  ('7221', '11119'),
  ('7221', '11120'),
  ('7221', '11121'),
  ('7221', '11122'),
  ('7221', '11123'),
  ('7221', '11124'),
  ('7221', '11125'),
  ('7221', '11126'),
  ('7221', '11127'),
  ('7221', '11128'),
  ('7221', '11129'),
  ('7221', '11130'),
  ('7221', '11131'),
  ('7221', '11132'),
  ('7221', '11133'),
  ('7221', '11134'),
  ('7221', '11135'),
  ('7221', '11136'),
  ('7221', '11137'),
  ('7221', '11138'),
  ('7221', '11139'),
  ('7221', '11140'),
  ('7221', '11141'),
  ('7221', '11142'),
  ('7221', '11143'),
  ('7221', '11144'),
  ('7221', '11145'),
  ('7221', '11146'),
  ('7221', '11147'),
  ('7221', '11148'),
  ('7221', '11149'),
  ('7221', '11150'),
  ('7221', '11151'),
  ('7221', '11152'),
  ('7221', '11153'),
  ('7221', '11154'),
  ('7221', '11155'),
  ('7221', '11156'),
  ('7221', '11157'),
  ('7221', '11158'),
  ('7221', '11159'),
  ('7221', '11160'),
  ('7221', '11161'),
  ('7221', '11162'),
  ('7221', '11163'),
  ('7221', '11164'),
  ('7221', '11165'),
  ('7221', '11166'),
  ('7221', '11167'),
  ('7221', '11168'),
  ('7221', '11169'),
  ('7221', '11170'),
  ('7221', '11171'),
  ('7221', '11172'),
  ('7221', '11173'),
  ('7221', '11174'),
  ('7221', '11175'),
  ('7221', '11176'),
  ('7221', '11177'),
  ('7221', '11178'),
  ('7221', '11179'),
  ('7221', '11180'),
  ('7221', '11181'),
  ('7221', '11182'),
  ('7221', '11183'),
  ('7221', '11184'),
  ('7221', '11185'),
  ('7221', '11186'),
  ('7221', '11187'),
  ('7221', '11188'),
  ('7221', '11189'),
  ('7221', '11190'),
  ('7221', '11191'),
  ('7221', '11192'),
  ('7221', '11193'),
  ('7221', '11194'),
  ('7221', '11195'),
  ('7221', '11196'),
  ('7221', '11197'),
  ('7221', '11198'),
  ('7221', '11199'),
  ('7221', '11200'),
  ('7221', '11201'),
  ('7221', '11202'),
  ('7221', '11203'),
  ('7221', '11204'),
  ('7221', '11205'),
  ('7221', '11206'),
  ('7221', '11207'),
  ('7221', '11208'),
  ('7221', '11209'),
  ('7221', '11210'),
  ('7221', '11211'),

  ('7223', '11074'),
  ('7223', '11075'),
  ('7223', '11076'),
  ('7223', '11077'),
  ('7223', '11078'),
  ('7223', '11079'),
  ('7223', '11080'),
  ('7223', '11081'),
  ('7223', '11082'),
  ('7223', '11083'),
  ('7223', '11084'),
  ('7223', '11085'),
  ('7223', '11086'),
  ('7223', '11087'),
  ('7223', '11088'),
  ('7223', '11089'),

  ('7224', '10459'),
  ('7224', '11140'),
  ('7224', '11141'),
  ('7224', '11142'),
  ('7224', '11143'),
  ('7224', '11144'),
  ('7224', '11145'),
  ('7224', '11146'),
  ('7224', '11147'),
  ('7224', '11148'),
  ('7224', '11149'),
  ('7224', '11150'),
  ('7224', '11151'),
  ('7224', '11152'),

  ('7225', '11115'),
  ('7225', '11116'),
  ('7225', '11117'),
  ('7225', '11118'),
  ('7225', '11119'),
  ('7225', '11120'),
  ('7225', '11121'),
  ('7225', '11122'),
  ('7225', '11123'),
  ('7225', '11124'),
  ('7225', '11125'),
  ('7225', '11126'),

  ('7226', '11127'),
  ('7226', '11128'),
  ('7226', '11129'),
  ('7226', '11130'),
  ('7226', '11131'),
  ('7226', '11132'),
  ('7226', '11133'),
  ('7226', '11134'),
  ('7226', '11135'),
  ('7226', '11136'),
  ('7226', '11137'),
  ('7226', '11138'),
  ('7226', '11139'),

  ('7227', '11092'),
  ('7227', '11093'),
  ('7227', '11094'),
  ('7227', '11095'),
  ('7227', '11096'),
  ('7227', '11097'),
  ('7227', '11098'),
  ('7227', '11099'),
  ('7227', '11100'),
  ('7227', '11101'),
  ('7227', '11102'),
  ('7227', '11103'),
  ('7227', '11104'),
  ('7227', '11105'),
  ('7227', '11106'),
  ('7227', '11107'),
  ('7227', '11108'),
  ('7227', '11109'),
  ('7227', '11110'),
  ('7227', '11111'),
  ('7227', '11112'),
  ('7227', '11113'),
  ('7227', '11114'),

  ('7228', '11153'),
  ('7228', '11154'),
  ('7228', '11155'),
  ('7228', '11156'),
  ('7228', '11157'),
  ('7228', '11158'),
  ('7228', '11159'),
  ('7228', '11160'),
  ('7228', '11161'),
  ('7228', '11162'),
  ('7228', '11163'),
  ('7228', '11164'),
  ('7228', '11165'),
  ('7228', '11166'),
  ('7228', '11167'),
  ('7228', '11168'),
  ('7228', '11169'),
  ('7228', '11170'),
  ('7228', '11171'),
  ('7228', '11172'),
  ('7228', '11173'),
  ('7228', '11174'),
  ('7228', '11175'),
  ('7228', '11176'),
  ('7228', '11177'),
  ('7228', '11178'),
  ('7228', '11179'),
  ('7228', '11180'),
  ('7228', '11195'),
  ('7228', '11196'),
  ('7228', '11197'),
  ('7228', '11198'),
  ('7228', '11199'),
  ('7228', '11200'),
  ('7228', '11201'),
  ('7228', '11202'),
  ('7228', '11203'),
  ('7228', '11552'),

  ('72221', '11048'),
  ('72221', '11049'),
  ('72221', '11050'),
  ('72221', '11051'),
  ('72221', '11052'),
  ('72221', '11053'),
  ('72221', '11054'),
  ('72221', '11055'),
  ('72221', '11056'),
  ('72221', '11057'),
  ('72221', '11058'),
  ('72221', '11059'),
  ('72221', '11060'),
  ('72221', '11064'),
  ('72221', '11066'),
  ('72221', '11067'),
  ('72221', '11069'),
  ('72221', '11070'),
  ('72221', '11071'),
  ('72221', '11072'),
  ('72221', '11073'),

  ('72222', '11048'),
  ('72222', '11049'),
  ('72222', '11050'),
  ('72222', '11051'),
  ('72222', '11052'),
  ('72222', '11053'),
  ('72222', '11054'),
  ('72222', '11055'),
  ('72222', '11056'),
  ('72222', '11057'),
  ('72222', '11058'),
  ('72222', '11059'),
  ('72222', '11060'),
  ('72222', '11061'),
  ('72222', '11062'),
  ('72222', '11063'),
  ('72222', '11064'),
  ('72222', '11065'),
  ('72222', '11066'),
  ('72222', '11067'),
  ('72222', '11068'),
  ('72222', '11069'),
  ('72222', '11070'),
  ('72222', '11071'),
  ('72222', '11072'),
  ('72222', '11073'),
  ('72222', '11195'),
  ('72222', '11196'),
  ('72222', '11197'),
  ('72222', '11198'),
  ('72222', '11199'),
  ('72222', '11200'),
  ('72222', '11201'),
  ('72222', '11202'),
  ('72222', '11203'),
  ('72222', '11552'),

  ('72223', '11183'),
  ('72223', '11184'),
  ('72223', '11185'),
  ('72223', '11186'),
  ('72223', '11187'),
  ('72223', '11188'),
  ('72223', '11189'),
  ('72223', '11190'),
  ('72223', '11191'),
  ('72223', '11192'),
  ('72223', '11193'),
  ('72223', '11194'),

  ('7311', '10442'),
  ('7311', '10888'),
  ('7311', '10889'),
  ('7311', '10890'),
  ('7311', '10891'),
  ('7311', '10892'),
  ('7311', '10894'),
  ('7311', '10896'),
  ('7311', '10897'),
  ('7311', '10901'),
  ('7311', '10902'),
  ('7311', '11552'),

  ('7312', '10442'),
  ('7312', '10888'),
  ('7312', '10889'),
  ('7312', '10890'),
  ('7312', '10891'),
  ('7312', '10892'),
  ('7312', '10893'),
  ('7312', '10894'),
  ('7312', '10895'),
  ('7312', '10896'),
  ('7312', '10897'),
  ('7312', '10898'),
  ('7312', '10899'),
  ('7312', '10900'),
  ('7312', '10901'),
  ('7312', '10902'),
  ('7312', '11552'),

  ('7321', '10633'),
  ('7321', '10635'),
  ('7321', '10636'),
  ('7321', '10637'),

  ('7322', '10633'),
  ('7322', '10634'),
  ('7322', '10635'),
  ('7322', '10636'),
  ('7322', '10637'),
  ('7322', '10638'),
  ('7322', '10639'),
  ('7322', '10640'),

  ('7331', '10437'),
  ('7331', '10445'),
  ('7331', '10594'),
  ('7331', '10596'),

  ('7332', '10437'),
  ('7332', '10445'),
  ('7332', '10594'),
  ('7332', '10595'),
  ('7332', '10596'),
  ('7332', '10597'),
  ('7332', '10598'),

  ('7341', '10877'),

  ('7342', '10877'),
  ('7342', '10878'),
  ('7342', '10879'),
  ('7342', '10880'),

  ('741', '10455'),
  ('741', '10553'),
  ('741', '10555'),
  ('741', '10556'),
  ('741', '10559'),
  ('741', '10562'),
  ('741', '10795'),
  ('741', '10797'),

  ('742', '10455'),
  ('742', '10553'),
  ('742', '10554'),
  ('742', '10555'),
  ('742', '10556'),
  ('742', '10557'),
  ('742', '10558'),
  ('742', '10559'),
  ('742', '10560'),
  ('742', '10561'),
  ('742', '10562'),
  ('742', '10795'),
  ('742', '10796'),
  ('742', '10797'),
  ('742', '10798'),
  ('742', '10799'),

  ('781', '10881'),
  ('781', '10882'),
  ('781', '10884'),
  ('781', '10885'),
  ('781', '10886'),
  ('781', '10887'),

  ('782', '10881'),
  ('782', '10882'),
  ('782', '10883'),
  ('782', '10884'),
  ('782', '10885'),
  ('782', '10886'),
  ('782', '10887'),

  ('79', '10491'),
  ('79', '10492'),
  ('79', '10493'),
  ('79', '10494'),
  ('79', '10495'),
  ('79', '10496'),
  ('79', '10497'),
  ('79', '10498'),
  ('79', '10499'),

  ('7511', '10447'),
  ('7511', '10448'),
  ('7511', '10449'),
  ('7511', '10452'),
  ('7511', '10563'),
  ('7511', '10564'),
  ('7511', '10565'),
  ('7511', '10566'),
  ('7511', '10567'),
  ('7511', '10570'),
  ('7511', '10576'),
  ('7511', '10581'),
  ('7511', '10583'),

  ('7512', '10447'),
  ('7512', '10448'),
  ('7512', '10449'),
  ('7512', '10450'),
  ('7512', '10452'),
  ('7512', '10563'),
  ('7512', '10564'),
  ('7512', '10565'),
  ('7512', '10566'),
  ('7512', '10567'),
  ('7512', '10568'),
  ('7512', '10569'),
  ('7512', '10570'),
  ('7512', '10571'),
  ('7512', '10572'),
  ('7512', '10573'),
  ('7512', '10574'),
  ('7512', '10575'),
  ('7512', '10576'),
  ('7512', '10577'),
  ('7512', '10578'),
  ('7512', '10579'),
  ('7512', '10580'),
  ('7512', '10581'),
  ('7512', '10582'),
  ('7512', '10583'),
  ('7512', '10584'),

  ('7521', '10447'),
  ('7521', '10448'),
  ('7521', '10449'),
  ('7521', '10452'),
  ('7521', '10455'),
  ('7521', '10460'),
  ('7521', '10461'),
  ('7521', '10564'),
  ('7521', '10565'),
  ('7521', '10566'),
  ('7521', '10567'),
  ('7521', '10570'),
  ('7521', '10576'),

  ('7522', '10447'),
  ('7522', '10448'),
  ('7522', '10449'),
  ('7522', '10450'),
  ('7522', '10452'),
  ('7522', '10455'),
  ('7522', '10460'),
  ('7522', '10461'),
  ('7522', '10564'),
  ('7522', '10565'),
  ('7522', '10566'),
  ('7522', '10567'),
  ('7522', '10568'),
  ('7522', '10569'),
  ('7522', '10570'),
  ('7522', '10571'),
  ('7522', '10572'),
  ('7522', '10573'),
  ('7522', '10574'),
  ('7522', '10575'),
  ('7522', '10576'),
  ('7522', '10577'),
  ('7522', '10578'),
  ('7522', '10579'),

  ('7611', '10401'),
  ('7611', '10402'),
  ('7611', '10403'),
  ('7611', '10404'),
  ('7611', '10410'),
  ('7611', '10411'),
  ('7611', '10413'),
  ('7611', '10417'),
  ('7611', '10418'),
  ('7611', '10423'),
  ('7611', '10426'),
  ('7611', '10427'),
  ('7611', '10428'),
  ('7611', '10446'),
  ('7611', '10447'),
  ('7611', '10448'),
  ('7611', '10458'),

  ('7612', '10401'),
  ('7612', '10402'),
  ('7612', '10403'),
  ('7612', '10404'),
  ('7612', '10405'),
  ('7612', '10406'),
  ('7612', '10407'),
  ('7612', '10408'),
  ('7612', '10409'),
  ('7612', '10410'),
  ('7612', '10411'),
  ('7612', '10412'),
  ('7612', '10413'),
  ('7612', '10414'),
  ('7612', '10415'),
  ('7612', '10416'),
  ('7612', '10417'),
  ('7612', '10418'),
  ('7612', '10419'),
  ('7612', '10420'),
  ('7612', '10421'),
  ('7612', '10422'),
  ('7612', '10423'),
  ('7612', '10424'),
  ('7612', '10425'),
  ('7612', '10426'),
  ('7612', '10427'),
  ('7612', '10428'),
  ('7612', '10429'),
  ('7612', '10430'),
  ('7612', '10431'),
  ('7612', '10432'),
  ('7612', '10433'),
  ('7612', '10446'),
  ('7612', '10447'),
  ('7612', '10448'),
  ('7612', '10458'),
  ('7612', '11538'),

  ('7621', '10402'),
  ('7621', '10403'),
  ('7621', '10404'),
  ('7621', '10410'),
  ('7621', '10411'),
  ('7621', '10413'),
  ('7621', '10417'),
  ('7621', '10418'),
  ('7621', '10423'),
  ('7621', '10426'),
  ('7621', '10446'),
  ('7621', '10447'),
  ('7621', '10448'),
  ('7621', '10461'),

  ('7622', '10402'),
  ('7622', '10403'),
  ('7622', '10404'),
  ('7622', '10405'),
  ('7622', '10406'),
  ('7622', '10407'),
  ('7622', '10408'),
  ('7622', '10409'),
  ('7622', '10410'),
  ('7622', '10411'),
  ('7622', '10412'),
  ('7622', '10413'),
  ('7622', '10414'),
  ('7622', '10415'),
  ('7622', '10416'),
  ('7622', '10417'),
  ('7622', '10418'),
  ('7622', '10419'),
  ('7622', '10420'),
  ('7622', '10421'),
  ('7622', '10422'),
  ('7622', '10423'),
  ('7622', '10424'),
  ('7622', '10425'),
  ('7622', '10426'),
  ('7622', '10446'),
  ('7622', '10447'),
  ('7622', '10448'),
  ('7622', '10461'),
  ('7622', '11538'),

  ('771', '10447'),
  ('771', '10448'),
  ('771', '10455'),
  ('771', '10458'),
  ('771', '11456'),
  ('771', '11460'),
  ('771', '11463'),

  ('772', '10447'),
  ('772', '10448'),
  ('772', '10455'),
  ('772', '10458'),
  ('772', '11456'),
  ('772', '11457'),
  ('772', '11458'),
  ('772', '11459'),
  ('772', '11460'),
  ('772', '11461'),
  ('772', '11462'),
  ('772', '11463'),
  ('772', '11464'),
  ('772', '11465'),

  ('911', '10447'),
  ('911', '10455'),
  ('911', '10458'),
  ('911', '10459'),
  ('911', '10642'),
  ('911', '10643'),
  ('911', '10648'),
  ('911', '10650'),
  ('911', '10652'),
  ('911', '10653'),
  ('911', '10656'),
  ('911', '10658'),
  ('911', '10661'),
  ('911', '10664'),
  ('911', '10667'),
  ('911', '10669'),
  ('911', '10674'),
  ('911', '10677'),
  ('911', '10678'),
  ('911', '10682'),
  ('911', '10685'),
  ('911', '10687'),
  ('911', '10712'),
  ('911', '10714'),
  ('911', '10723'),
  ('911', '10731'),
  ('911', '10732'),
  ('911', '10733'),
  ('911', '10736'),
  ('911', '10738'),
  ('911', '10743'),
  ('911', '10744'),
  ('911', '10749'),
  ('911', '10750'),
  ('911', '10751'),
  ('911', '10753'),
  ('911', '10757'),
  ('911', '10758'),
  ('911', '10763'),
  ('911', '10766'),
  ('911', '10768'),
  ('911', '10770'),
  ('911', '10771'),
  ('911', '10773'),
  ('911', '10780'),
  ('911', '10785'),
  ('911', '10786'),
  ('911', '10923'),
  ('911', '10924'),
  ('911', '10929'),
  ('911', '10931'),
  ('911', '10933'),
  ('911', '10934'),
  ('911', '10937'),
  ('911', '10967'),
  ('911', '10971'),
  ('911', '10973'),

  ('912', '10447'),
  ('912', '10455'),
  ('912', '10458'),
  ('912', '10459'),
  ('912', '10641'),
  ('912', '10642'),
  ('912', '10643'),
  ('912', '10644'),
  ('912', '10645'),
  ('912', '10646'),
  ('912', '10647'),
  ('912', '10648'),
  ('912', '10649'),
  ('912', '10650'),
  ('912', '10651'),
  ('912', '10652'),
  ('912', '10653'),
  ('912', '10654'),
  ('912', '10655'),
  ('912', '10656'),
  ('912', '10657'),
  ('912', '10658'),
  ('912', '10659'),
  ('912', '10660'),
  ('912', '10661'),
  ('912', '10662'),
  ('912', '10663'),
  ('912', '10664'),
  ('912', '10665'),
  ('912', '10666'),
  ('912', '10667'),
  ('912', '10668'),
  ('912', '10669'),
  ('912', '10670'),
  ('912', '10671'),
  ('912', '10672'),
  ('912', '10673'),
  ('912', '10674'),
  ('912', '10675'),
  ('912', '10676'),
  ('912', '10677'),
  ('912', '10678'),
  ('912', '10679'),
  ('912', '10680'),
  ('912', '10681'),
  ('912', '10682'),
  ('912', '10683'),
  ('912', '10684'),
  ('912', '10685'),
  ('912', '10686'),
  ('912', '10687'),
  ('912', '10688'),
  ('912', '10689'),
  ('912', '10690'),
  ('912', '10712'),
  ('912', '10713'),
  ('912', '10714'),
  ('912', '10715'),
  ('912', '10716'),
  ('912', '10717'),
  ('912', '10723'),
  ('912', '10724'),
  ('912', '10725'),
  ('912', '10726'),
  ('912', '10727'),
  ('912', '10728'),
  ('912', '10729'),
  ('912', '10730'),
  ('912', '10731'),
  ('912', '10732'),
  ('912', '10733'),
  ('912', '10734'),
  ('912', '10735'),
  ('912', '10736'),
  ('912', '10737'),
  ('912', '10738'),
  ('912', '10739'),
  ('912', '10740'),
  ('912', '10741'),
  ('912', '10742'),
  ('912', '10743'),
  ('912', '10744'),
  ('912', '10745'),
  ('912', '10746'),
  ('912', '10747'),
  ('912', '10748'),
  ('912', '10749'),
  ('912', '10750'),
  ('912', '10751'),
  ('912', '10752'),
  ('912', '10753'),
  ('912', '10754'),
  ('912', '10755'),
  ('912', '10756'),
  ('912', '10757'),
  ('912', '10758'),
  ('912', '10759'),
  ('912', '10760'),
  ('912', '10761'),
  ('912', '10762'),
  ('912', '10763'),
  ('912', '10764'),
  ('912', '10765'),
  ('912', '10766'),
  ('912', '10767'),
  ('912', '10768'),
  ('912', '10769'),
  ('912', '10770'),
  ('912', '10771'),
  ('912', '10772'),
  ('912', '10773'),
  ('912', '10774'),
  ('912', '10780'),
  ('912', '10781'),
  ('912', '10782'),
  ('912', '10783'),
  ('912', '10784'),
  ('912', '10785'),
  ('912', '10786'),
  ('912', '10787'),
  ('912', '10788'),
  ('912', '10922'),
  ('912', '10923'),
  ('912', '10924'),
  ('912', '10925'),
  ('912', '10926'),
  ('912', '10927'),
  ('912', '10928'),
  ('912', '10929'),
  ('912', '10930'),
  ('912', '10931'),
  ('912', '10932'),
  ('912', '10933'),
  ('912', '10934'),
  ('912', '10935'),
  ('912', '10936'),
  ('912', '10937'),
  ('912', '10938'),
  ('912', '10967'),
  ('912', '10968'),
  ('912', '10969'),
  ('912', '10970'),
  ('912', '10971'),
  ('912', '10972'),
  ('912', '10973'),
  ('912', '10974'),
  ('912', '10975'),
  ('912', '10976'),
  ('912', '10977'),

  ('92', '10800'),
  ('92', '10801'),
  ('92', '10802'),
  ('92', '10803'),
  ('92', '10804'),
  ('92', '10805'),
  ('92', '10806'),
  ('92', '10807'),
  ('92', '10808'),
  ('92', '10809'),
  ('92', '10978'),
  ('92', '10979'),
  ('92', '10980'),
  ('92', '10981'),
  ('92', '10982'),
  ('92', '10983'),
  ('92', '10984'),
  ('92', '10985'),
  ('92', '10986'),

  ('931', '10789'),
  ('931', '10790'),
  ('931', '10793'),
  ('931', '11466'),
  ('931', '11467'),
  ('931', '11468'),
  ('931', '11469'),
  ('931', '11475'),

  ('932', '10789'),
  ('932', '10790'),
  ('932', '10791'),
  ('932', '10792'),
  ('932', '10793'),
  ('932', '10794'),
  ('932', '11466'),
  ('932', '11467'),
  ('932', '11468'),
  ('932', '11469'),
  ('932', '11470'),
  ('932', '11471'),
  ('932', '11472'),
  ('932', '11473'),
  ('932', '11474'),
  ('932', '11475'),
  ('932', '11476'),
  ('932', '11477'),
  ('932', '11478'),

  ('95', '10395'),
  ('95', '10396'),
  ('95', '10397'),
  ('95', '10398'),
  ('95', '10399'),
  ('95', '10400'),
  ('95', '10437'),

  ('96', '10448'),
  ('96', '10455'),
  ('96', '10460'),
  ('96', '10461'),
  ('96', '10626'),
  ('96', '10627'),
  ('96', '10628'),
  ('96', '10629'),
  ('96', '10630'),
  ('96', '10631'),
  ('96', '10632'),
  ('96', '10903'),
  ('96', '10904'),
  ('96', '10905'),
  ('96', '10906'),
  ('96', '10907'),
  ('96', '10908'),
  ('96', '10909'),
  ('96', '10910'),
  ('96', '10911'),
  ('96', '10912'),
  ('96', '10913'),
  ('96', '10914'),
  ('96', '10915'),
  ('96', '10916'),
  ('96', '10917'),
  ('96', '10918'),
  ('96', '10919'),
  ('96', '10920'),
  ('96', '10921'),

  ('1011', '10447'),
  ('1011', '10599'),
  ('1011', '10603'),
  ('1011', '10604'),
  ('1011', '10605'),
  ('1011', '10606'),
  ('1011', '10607'),
  ('1011', '10608'),
  ('1011', '10610'),
  ('1011', '10613'),
  ('1011', '10615'),
  ('1011', '10617'),
  ('1011', '10619'),
  ('1011', '10623'),
  ('1011', '10624'),
  ('1011', '10694'),
  ('1011', '11805'),

  ('1012', '10447'),
  ('1012', '10599'),
  ('1012', '10600'),
  ('1012', '10601'),
  ('1012', '10602'),
  ('1012', '10603'),
  ('1012', '10604'),
  ('1012', '10605'),
  ('1012', '10606'),
  ('1012', '10607'),
  ('1012', '10608'),
  ('1012', '10609'),
  ('1012', '10610'),
  ('1012', '10611'),
  ('1012', '10612'),
  ('1012', '10613'),
  ('1012', '10614'),
  ('1012', '10615'),
  ('1012', '10616'),
  ('1012', '10617'),
  ('1012', '10618'),
  ('1012', '10619'),
  ('1012', '10620'),
  ('1012', '10621'),
  ('1012', '10622'),
  ('1012', '10623'),
  ('1012', '10624'),
  ('1012', '10625'),
  ('1012', '10693'),
  ('1012', '10694'),
  ('1012', '11805'),

  ('1021', '10694'),
  ('1021', '10810'),
  ('1021', '10812'),
  ('1021', '10813'),
  ('1021', '10815'),
  ('1021', '10817'),
  ('1021', '10819'),
  ('1021', '10822'),
  ('1021', '10824'),
  ('1021', '10827'),
  ('1021', '10829'),
  ('1021', '10832'),
  ('1021', '10834'),
  ('1021', '10837'),
  ('1021', '10840'),

  ('1022', '10693'),
  ('1022', '10694'),
  ('1022', '10810'),
  ('1022', '10811'),
  ('1022', '10812'),
  ('1022', '10813'),
  ('1022', '10814'),
  ('1022', '10815'),
  ('1022', '10816'),
  ('1022', '10817'),
  ('1022', '10818'),
  ('1022', '10819'),
  ('1022', '10820'),
  ('1022', '10821'),
  ('1022', '10822'),
  ('1022', '10823'),
  ('1022', '10824'),
  ('1022', '10825'),
  ('1022', '10826'),
  ('1022', '10827'),
  ('1022', '10828'),
  ('1022', '10829'),
  ('1022', '10830'),
  ('1022', '10831'),
  ('1022', '10832'),
  ('1022', '10833'),
  ('1022', '10834'),
  ('1022', '10835'),
  ('1022', '10836'),
  ('1022', '10837'),
  ('1022', '10838'),
  ('1022', '10839'),
  ('1022', '10840'),
  ('1022', '10841'),
  ('1022', '10842'),
  ('1022', '10843'),

  ('10511', '10455'),
  ('10511', '10874'),
  ('10511', '10876'),

  ('10512', '10455'),
  ('10512', '10874'),
  ('10512', '10875'),
  ('10512', '10876'),


  ('801', '10448'),
  ('801', '10455'),
  ('801', '10460'),
  ('801', '10461'),
  ('801', '10619'),
  ('801', '10623'),
  ('801', '10691'),
  ('801', '10692'),
  ('801', '10694'),
  ('801', '10695'),
  ('801', '10696'),
  ('801', '10697'),
  ('801', '10698'),
  ('801', '10699'),
  ('801', '10700'),
  ('801', '10701'),
  ('801', '10702'),
  ('801', '10703'),
  ('801', '10704'),
  ('801', '10705'),
  ('801', '10706'),
  ('801', '10707'),
  ('801', '10708'),
  ('801', '10709'),
  ('801', '10710'),
  ('801', '10711'),
  ('801', '10712'),
  ('801', '10713'),
  ('801', '10714'),
  ('801', '10715'),
  ('801', '10716'),
  ('801', '10717'),
  ('801', '10718'),
  ('801', '10719'),
  ('801', '10720'),
  ('801', '10721'),
  ('801', '10722'),
  ('801', '11479'),
  ('801', '11487'),
  ('801', '11499'),
  ('801', '11500'),
  ('801', '11502'),
  ('801', '11508'),
  ('801', '11512'),
  ('801', '11701'),
  ('801', '11705'),
  ('801', '11713'),
  ('801', '11714'),
  ('801', '11715'),
  ('801', '11719'),
  ('801', '11721'),
  ('801', '11723'),
  ('801', '11724'),
  ('801', '11745'),
  ('801', '11748'),
  ('801', '11749'),
  ('801', '11751'),
  ('801', '11752'),
  ('801', '11757'),
  ('801', '11766'),
  ('801', '11770'),
  ('801', '11772'),
  ('801', '11776'),
  ('801', '11777'),
  ('801', '11778'),
  ('801', '11779'),
  ('801', '11780'),
  ('801', '11781'),
  ('801', '11782'),
  ('801', '11783'),
  ('801', '11784'),
  ('801', '11785'),
  ('801', '11786'),
  ('801', '11788'),
  ('801', '11790'),
  ('801', '11794'),
  ('801', '11799'),
  ('801', '11800'),
  ('801', '11801'),
  ('801', '11803'),
  ('801', '11804'),
  ('801', '11805'),
  ('801', '11807'),
  ('801', '11808'),
  ('801', '11812'),
  ('801', '11815'),
  ('801', '11816'),
  ('801', '11817'),
  ('801', '11818'),
  ('801', '11819'),
  ('801', '11820'),
  ('801', '11822'),
  ('801', '11824'),
  ('801', '11825'),
  ('801', '11826'),
  ('801', '11827'),
  ('801', '11828'),
  ('801', '11829'),
  ('801', '11830'),

  ('802', '10448'),
  ('802', '10455'),
  ('802', '10460'),
  ('802', '10461'),
  ('802', '10618'),
  ('802', '10619'),
  ('802', '10623'),
  ('802', '10691'),
  ('802', '10692'),
  ('802', '10693'),
  ('802', '10694'),
  ('802', '10695'),
  ('802', '10696'),
  ('802', '10697'),
  ('802', '10698'),
  ('802', '10699'),
  ('802', '10700'),
  ('802', '10701'),
  ('802', '10702'),
  ('802', '10703'),
  ('802', '10704'),
  ('802', '10705'),
  ('802', '10706'),
  ('802', '10707'),
  ('802', '10708'),
  ('802', '10709'),
  ('802', '10710'),
  ('802', '10711'),
  ('802', '10712'),
  ('802', '10713'),
  ('802', '10714'),
  ('802', '10715'),
  ('802', '10716'),
  ('802', '10717'),
  ('802', '10718'),
  ('802', '10719'),
  ('802', '10720'),
  ('802', '10721'),
  ('802', '10722'),
  ('802', '11479'),
  ('802', '11487'),
  ('802', '11488'),
  ('802', '11491'),
  ('802', '11499'),
  ('802', '11500'),
  ('802', '11501'),
  ('802', '11502'),
  ('802', '11508'),
  ('802', '11509'),
  ('802', '11512'),
  ('802', '11699'),
  ('802', '11701'),
  ('802', '11702'),
  ('802', '11703'),
  ('802', '11704'),
  ('802', '11705'),
  ('802', '11709'),
  ('802', '11710'),
  ('802', '11711'),
  ('802', '11712'),
  ('802', '11713'),
  ('802', '11714'),
  ('802', '11715'),
  ('802', '11716'),
  ('802', '11717'),
  ('802', '11718'),
  ('802', '11719'),
  ('802', '11720'),
  ('802', '11721'),
  ('802', '11722'),
  ('802', '11723'),
  ('802', '11724'),
  ('802', '11725'),
  ('802', '11743'),
  ('802', '11744'),
  ('802', '11745'),
  ('802', '11746'),
  ('802', '11747'),
  ('802', '11748'),
  ('802', '11749'),
  ('802', '11750'),
  ('802', '11751'),
  ('802', '11752'),
  ('802', '11753'),
  ('802', '11754'),
  ('802', '11755'),
  ('802', '11756'),
  ('802', '11757'),
  ('802', '11766'),
  ('802', '11767'),
  ('802', '11768'),
  ('802', '11769'),
  ('802', '11770'),
  ('802', '11771'),
  ('802', '11772'),
  ('802', '11773'),
  ('802', '11774'),
  ('802', '11775'),
  ('802', '11776'),
  ('802', '11777'),
  ('802', '11778'),
  ('802', '11779'),
  ('802', '11780'),
  ('802', '11781'),
  ('802', '11782'),
  ('802', '11783'),
  ('802', '11784'),
  ('802', '11785'),
  ('802', '11786'),
  ('802', '11787'),
  ('802', '11788'),
  ('802', '11789'),
  ('802', '11790'),
  ('802', '11791'),
  ('802', '11792'),
  ('802', '11794'),
  ('802', '11799'),
  ('802', '11800'),
  ('802', '11801'),
  ('802', '11802'),
  ('802', '11803'),
  ('802', '11804'),
  ('802', '11805'),
  ('802', '11807'),
  ('802', '11808'),
  ('802', '11809'),
  ('802', '11810'),
  ('802', '11811'),
  ('802', '11812'),
  ('802', '11813'),
  ('802', '11814'),
  ('802', '11815'),
  ('802', '11816'),
  ('802', '11817'),
  ('802', '11818'),
  ('802', '11819'),
  ('802', '11820'),
  ('802', '11821'),
  ('802', '11822'),
  ('802', '11823'),
  ('802', '11824'),
  ('802', '11825'),
  ('802', '11826'),
  ('802', '11827'),
  ('802', '11828'),
  ('802', '11829'),
  ('802', '11830'),

  ('811', '10448'),
  ('811', '10460'),
  ('811', '10461'),
  ('811', '10987'),
  ('811', '10989'),
  ('811', '10990'),
  ('811', '10991'),
  ('811', '10993'),
  ('811', '10994'),
  ('811', '10995'),
  ('811', '10996'),
  ('811', '10997'),
  ('811', '11000'),
  ('811', '11002'),
  ('811', '11004'),
  ('811', '11005'),
  ('811', '11006'),
  ('811', '11007'),
  ('811', '11008'),
  ('811', '11010'),
  ('811', '11011'),

  ('812', '10448'),
  ('812', '10460'),
  ('812', '10461'),
  ('812', '10987'),
  ('812', '10988'),
  ('812', '10989'),
  ('812', '10990'),
  ('812', '10991'),
  ('812', '10992'),
  ('812', '10993'),
  ('812', '10994'),
  ('812', '10995'),
  ('812', '10996'),
  ('812', '10997'),
  ('812', '10998'),
  ('812', '10999'),
  ('812', '11000'),
  ('812', '11001'),
  ('812', '11002'),
  ('812', '11003'),
  ('812', '11004'),
  ('812', '11005'),
  ('812', '11006'),
  ('812', '11007'),
  ('812', '11008'),
  ('812', '11009'),
  ('812', '11010'),
  ('812', '11011'),
  ('812', '11012'),

  ('821', '11417'),
  ('821', '11423'),
  ('821', '11426'),
  ('821', '11486'),
  ('821', '11489'),
  ('821', '11807'),

  ('822', '11417'),
  ('822', '11423'),
  ('822', '11426'),
  ('822', '11427'),
  ('822', '11428'),
  ('822', '11429'),
  ('822', '11486'),
  ('822', '11489'),
  ('822', '11807'),

  ('831', '10633'),
  ('831', '10635'),
  ('831', '10636'),
  ('831', '10637'),
  ('831', '10640'),
  ('831', '11807'),

  ('832', '10633'),
  ('832', '10634'),
  ('832', '10635'),
  ('832', '10636'),
  ('832', '10637'),
  ('832', '10638'),
  ('832', '10639'),
  ('832', '10640'),
  ('832', '11807'),

  ('841', '10460'),
  ('841', '10461'),
  ('841', '11690'),
  ('841', '11692'),
  ('841', '11694'),
  ('841', '11695'),
  ('841', '11700'),
  ('841', '11701'),
  ('841', '11707'),
  ('841', '11708'),
  ('841', '11807'),

  ('842', '10460'),
  ('842', '10461'),
  ('842', '11690'),
  ('842', '11691'),
  ('842', '11692'),
  ('842', '11693'),
  ('842', '11694'),
  ('842', '11695'),
  ('842', '11696'),
  ('842', '11697'),
  ('842', '11698'),
  ('842', '11700'),
  ('842', '11701'),
  ('842', '11706'),
  ('842', '11707'),
  ('842', '11708'),
  ('842', '11807'),

  ('851', '11726'),
  ('851', '11727'),
  ('851', '11728'),
  ('851', '11730'),
  ('851', '11732'),
  ('851', '11736'),
  ('851', '11738'),
  ('851', '11739'),
  ('851', '11740'),
  ('851', '11806'),

  ('852', '11726'),
  ('852', '11727'),
  ('852', '11728'),
  ('852', '11729'),
  ('852', '11730'),
  ('852', '11731'),
  ('852', '11732'),
  ('852', '11733'),
  ('852', '11734'),
  ('852', '11735'),
  ('852', '11736'),
  ('852', '11737'),
  ('852', '11738'),
  ('852', '11739'),
  ('852', '11740'),
  ('852', '11741'),
  ('852', '11806'),

  ('861', '11758'),
  ('861', '11761'),
  ('861', '11764'),
  ('861', '11765'),

  ('862', '11758'),
  ('862', '11760'),
  ('862', '11761'),
  ('862', '11762'),
  ('862', '11763'),
  ('862', '11764'),
  ('862', '11765');
