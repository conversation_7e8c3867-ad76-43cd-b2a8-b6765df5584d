ALTER TABLE `pax_skyhook_market_summary_detail` ADD COLUMN `global_snapshot_fileid` VARCHAR(255) DEFAULT NULL COMMENT 'Global数据文件ID' AFTER `snapshot_fileid`;
ALTER TABLE `pax_skyhook_market_summary_detail` CHANGE COLUMN `record_time` `created_date` datetime NOT NULL COMMENT '创建时间';
ALTER TABLE `pax_skyhook_market_summary_detail` CHANGE COLUMN `access_month` `period`  VARCHAR(10) DEFAULT NULL COMMENT '期数';

ALTER TABLE `pax_skyhook_reseller_summary_detail` <PERSON>AN<PERSON> COLUMN `record_time` `created_date` datetime NOT NULL COMMENT '创建时间';
ALTER TABLE `pax_skyhook_reseller_summary_detail` CHANGE COLUMN `access_month` `period`  VARCHAR(10) DEFAULT NULL COMMENT '期数';