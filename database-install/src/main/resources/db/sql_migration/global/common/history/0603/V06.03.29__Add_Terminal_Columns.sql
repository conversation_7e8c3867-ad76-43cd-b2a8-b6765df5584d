ALTER TABLE pax_terminal ADD COLUMN debug_mode varchar(32) DEFAULT 'UNKNOWN' COMMENT '运行模式' AFTER online_status_change_date;
ALTER TABLE pax_terminal ADD COLUMN network varchar(32) DEFAULT 'UNKNOWN' COMMENT '网络模式' AFTER debug_mode;

ALTER TABLE pax_terminal ADD INDEX IDX_TERMINAL_DM_DEL (`debug_mode`, `del_flag`);
ALTER TABLE pax_terminal ADD INDEX IDX_TERMINAL_MARKET_DM_DEL (`market_id`, `debug_mode`, `del_flag`);
ALTER TABLE pax_terminal ADD INDEX IDX_TERMINAL_RESELLER_DM_DEL (`reseller_id`, `debug_mode`, `del_flag`);

ALTER TABLE pax_terminal ADD INDEX IDX_TERMINAL_NW_DEL (`network`, `del_flag`);
ALTER TABLE pax_terminal ADD INDEX IDX_TERMINAL_MARKET_NW_DEL (`market_id`, `network`, `del_flag`);
ALTER TABLE pax_terminal ADD INDEX IDX_TERMINAL_RESELLER_NW_DEL (`reseller_id`, `network`, `del_flag`);

ALTER TABLE pax_terminal_detail ADD INDEX IDX_TERMINAL_DETAIL_KEY (`terminal_id`, `key`);

DELETE FROM pax_terminal_detail WHERE terminal_id
IN (SELECT terminal_id FROM (SELECT terminal_id FROM pax_terminal_detail GROUP BY terminal_id, `key` HAVING count(1) > 1) AS tempTable)
AND id NOT IN (SELECT id FROM (SELECT max(id) AS "id" FROM pax_terminal_detail GROUP BY terminal_id, `key` having count(1) > 1) AS tempTable);

UPDATE pax_terminal a SET a.debug_mode = (SELECT `value` FROM pax_terminal_detail WHERE terminal_id = a.id AND `key` = 'TERMINAL_ISDEBUG');
UPDATE pax_terminal a SET a.network = (SELECT `network` FROM pax_terminal_monitor WHERE terminal_id = a.id);

UPDATE pax_terminal a SET a.debug_mode = 'UNKNOWN' WHERE a.debug_mode IS NULL;
UPDATE pax_terminal a SET a.network = 'UNKNOWN' WHERE a.network IS NULL;