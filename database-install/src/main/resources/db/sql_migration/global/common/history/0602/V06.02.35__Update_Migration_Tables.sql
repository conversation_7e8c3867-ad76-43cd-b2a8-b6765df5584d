ALTER TABLE `pax_migration_merchant` CHANGE COLUMN `is_ignore` `validate_status` INT(1) NOT NULL DEFAULT 0;
ALTER TABLE `pax_migration_apk_template` CHANGE COLUMN `is_ignore` `validate_status` INT(1) NOT NULL DEFAULT 0;
ALTER TABLE `pax_migration_terminal_apk` CHANGE COLUMN `is_ignore` `validate_status` INT(1) NOT NULL DEFAULT 0;
ALTER TABLE `pax_migration_terminal_group` CHANGE COLUMN `is_ignore` `validate_status` INT(1) NOT NULL DEFAULT 0;
ALTER TABLE `pax_migration_terminal` CHANGE COLUMN `is_ignore` `validate_status` INT(1) NOT NULL DEFAULT 0;
ALTER TABLE `pax_migration_user` CHANGE COLUMN `is_ignore` `validate_status` INT(1) NOT NULL DEFAULT 0;

ALTER TABLE `pax_migration_merchant` ADD COLUMN `execute_status` INT(1) NOT NULL DEFAULT 0 AFTER validate_status;
<PERSON>TER TABLE `pax_migration_apk_template` ADD COLUMN `execute_status` INT(1) NOT NULL DEFAULT 0 AFTER validate_status;
ALTER TABLE `pax_migration_terminal_apk` ADD COLUMN `execute_status` INT(1) NOT NULL DEFAULT 0 AFTER validate_status;
ALTER TABLE `pax_migration_terminal_group` ADD COLUMN `execute_status` INT(1) NOT NULL DEFAULT 0 AFTER validate_status;
ALTER TABLE `pax_migration_terminal` ADD COLUMN `execute_status` INT(1) NOT NULL DEFAULT 0 AFTER validate_status;
ALTER TABLE `pax_migration_user` ADD COLUMN `execute_status` INT(1) NOT NULL DEFAULT 0 AFTER validate_status;

ALTER TABLE `pax_migration_merchant` ADD COLUMN `rollback_status` INT(1) NOT NULL DEFAULT 0 AFTER execute_status;
ALTER TABLE `pax_migration_apk_template` ADD COLUMN `rollback_status` INT(1) NOT NULL DEFAULT 0 AFTER execute_status;
ALTER TABLE `pax_migration_terminal_apk` ADD COLUMN `rollback_status` INT(1) NOT NULL DEFAULT 0 AFTER execute_status;
ALTER TABLE `pax_migration_terminal_group` ADD COLUMN `rollback_status` INT(1) NOT NULL DEFAULT 0 AFTER execute_status;
ALTER TABLE `pax_migration_terminal` ADD COLUMN `rollback_status` INT(1) NOT NULL DEFAULT 0 AFTER execute_status;
ALTER TABLE `pax_migration_user` ADD COLUMN `rollback_status` INT(1) NOT NULL DEFAULT 0 AFTER execute_status;
