DROP TABLE IF EXISTS `pax_merchant_migrate_history`;

CREATE TABLE `pax_merchant_migrate_history` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '迁移编号',
`merchant_id` int(11) NOT NULL COMMENT '商户ID',
`merchant_status` char(1) NOT NULL COMMENT '商户状态',
`target_reseller_id` int(11) NOT NULL COMMENT '目标代理商ID',
`status` char(1) NOT NULL COMMENT '迁移状态',
`result_message` longtext DEFAULT NULL COMMENT '迁移执行消息',
`created_date` datetime NOT NULL COMMENT '迁移创建者',
`created_by` int(11) NOT NULL COMMENT '迁移创建时间',
`updated_date` datetime NOT NULL COMMENT '迁移更新时间',
`updated_by` int(11) NOT NULL COMMENT '迁移更新者',
PRIMARY KEY (`id`),
KEY `IDX_MERCHANT_MIGRATE` (`merchant_id`)
)  COMMENT='商户迁移记录表' AUTO_INCREMENT=1000000000;