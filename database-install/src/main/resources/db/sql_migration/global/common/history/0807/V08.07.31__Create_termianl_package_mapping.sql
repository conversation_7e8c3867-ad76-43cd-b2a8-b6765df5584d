DROP TABLE IF EXISTS `pax_package_terminal_mapping`;
CREATE TABLE `pax_package_terminal_mapping`(
    `id`                  BIGINT(20) NOT NULL AUTO_INCREMENT,
    `package_name`        VARCHAR(128) NOT NULL COMMENT '应用包名',
    `market_id`           int(11) NOT NULL COMMENT '市场id',
    `terminal_id` BIGINT(20) NOT NULL COMMENT '终端id',
    PRIMARY KEY (`id`),
    INDEX `IDX_PACKAGE_MARKET_TERMINAL`(`package_name`, `market_id`,`terminal_id`) USING BTREE,
    INDEX `IDX_TERMINAL_ID`(`terminal_id`) USING BTREE,
    INDEX `IDX_MARKET_ID`(`market_id`) USING BTREE
) COMMENT ='终端已安装应用mapping，CloudMessage服务使用' AUTO_INCREMENT = 1000000000;