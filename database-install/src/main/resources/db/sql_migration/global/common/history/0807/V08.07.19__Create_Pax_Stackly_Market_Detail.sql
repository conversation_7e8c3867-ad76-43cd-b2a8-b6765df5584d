DROP TABLE IF EXISTS `pax_stackly_market_detail`;
CREATE TABLE `pax_stackly_market_detail`(
    `id`                  int(11) NOT NULL AUTO_INCREMENT,
    `market_id`           int(11) NOT NULL COMMENT '市场id',
    `developer_count`     int(11) unsigned NOT NULL DEFAULT 0 COMMENT '开发者使用数',
    `vas_supported_count` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'vas集成数',
    `period`              varchar(10)  DEFAULT NULL COMMENT '期数',
    `snapshot_fileid`     varchar(255) DEFAULT NULL COMMENT '数据文件id',
    `created_date`        datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) COMMENT ='stackly市场用量明细' AUTO_INCREMENT = 1000000000;

DROP TABLE IF EXISTS `pax_stackly_developer_detail`;
CREATE TABLE `pax_stackly_developer_detail`(
    `id`                  int(11) NOT NULL AUTO_INCREMENT,
    `market_id`           int(11) NOT NULL COMMENT '市场id',
    `dev_id`              bigint(20) NOT NULL COMMENT '开发者id',
    `vas_supported_count` int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'vas集成数',
    `period`              varchar(10) DEFAULT NULL COMMENT '期数',
    `created_date`        datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) COMMENT ='stackly开发者用量明细' AUTO_INCREMENT = 1000000000;