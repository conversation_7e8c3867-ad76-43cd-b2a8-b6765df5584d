DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'Create push message to terminal request', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10001', 'Get arrive rate of message', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10002', '批量更新参数下载的操作状态', '/v1/3rdApps/actions', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10003', '更新参数下载的操作状态', '/v1/3rdApps/actions/{actionId}/status', 'PUT', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10004', '同步终端商业数据', '/v1/3rdApps/bizData', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10005', '第三方应用同步信息', '/v1/3rdApps/info', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10006', '获取终端参数下载信息', '/v1/3rdApps/param', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10007', '获取应用是否存在版本更新', '/v1/3rdApps/upgrade', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10008', 'Find apk parameter list', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10009', 'Create Apk Parameter', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10010', 'Get apk parameter details', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10011', 'Update Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10012', 'delete apk parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10013', 'Search App in Marketplace', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'Find entity attribute', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10015', 'Create entity attribute', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'Get entity attribute by id', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'Update entity attribute', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'Delete entity attribute', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'Update entity attribute label', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'Verify estate', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'Get merchant category list', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'Create a single merchant category', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'Batch create merchant categories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'Update merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10025', 'Delete merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'Get merchant list by search criterias', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'Create a merchant', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'Get merchant by id', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'Update a merchant', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10030', 'Delete a merchant', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'Activate a merchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'Disable a merchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'Replace merchant email', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'Find parameter push history by page', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'Find resellers', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'Create a reseller', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'Get reseller', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'Update a reseller', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'Delete a reseller', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'Activate a reseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10041', 'Disable a reseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10042', 'Replace reseller email', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10043', 'Search push app list by terminal', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10044', 'Push app (with parameter if parameter app) to terminal', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10045', 'suspend terminal push app', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10046', 'uninstall terminal app', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10047', 'Get Terminal Apk', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10048', 'Search push firmware list by terminal', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10049', 'Push firmware to terminal', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10050', 'suspend terminal push firmware', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10051', 'Get Terminal Firmware', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10052', 'Get terminal group push application list', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10053', 'Push app (with parameter if parameter app) to terminal group ', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10054', 'Get terminal group push apk', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10055', 'Delete terminal group push app', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10056', 'Suspend terminal group push app', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10057', 'Get terminal group list', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10058', 'Create terminal groups', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10059', 'Get terminal list', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10060', 'Get terminal group', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10061', 'Update terminal groups', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10062', 'Delete terminal group', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10063', 'Activate terminal group', '/v1/3rdsys/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10064', 'Disable terminal group', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10065', 'get terminal list in group', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10066', 'Create group terminals', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10067', 'Remove group terminals', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10068', 'Get terminal variable list', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10069', 'Create terminal parameter variables in batch', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10070', 'Batch deletion of terminal variables', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10071', 'Update terminal variables', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10072', 'Delete terminal variable', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10073', 'Find terminal list by page', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10074', 'Create terminal', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10075', 'Activate a terminal by query parameter', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10076', 'Batch add terminal to group', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10077', 'Get terminal by id', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10078', 'Update terminal', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10079', 'Delete a terminal', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10080', 'Activate a terminal by path parameter', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10081', 'Disable a terminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10082', '查询活动列表', '/v1/activities', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10083', '批量删除活动', '/v1/activities/batch/deletion', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10084', '获取活动信息', '/v1/activities/{activityId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10085', '删除活动', '/v1/activities/{activityId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10086', '管理员查询应用列表', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10087', '创建管理员Apk下载任务', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10088', '创建管理员下载apk参数模版下载任务', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10089', '管理员删除apk参数模版', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10090', '对于签名失败的apk重新签名', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10091', '获取白名单', '/v1/admin/apps/whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10092', '创建白名单', '/v1/admin/apps/whiteList', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10093', '删除白名单', '/v1/admin/apps/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10094', '管理员获取开发者应用详细信息', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10095', '删除应用APP', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10096', '应用上线', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10097', '管理员获取APK列表', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10098', '删除应用APK', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10099', '通过应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10100', '管理员下载APK', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10101', '查询应用APK定向发布的应用市场', '/v1/admin/apps/{appId}/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10102', '应用Apk下线', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10103', '应用Apk上线', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10104', '管理员添加参数模板', '/v1/admin/apps/{appId}/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10105', '拒绝应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10106', '管理员更新App ReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/releaseNote', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10107', '查询应用APK定向发布的代理商', '/v1/admin/apps/{appId}/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10108', 'Apk specific reseller or global APK publish to the normal market', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10109', '删除应用APK定向发布', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10110', '管理员更新APK机型', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10111', '管理员更新APP自动更新配置', '/v1/admin/apps/{appId}/autoUpdate/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10112', '更新应用的开发者', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10113', '应用下线', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10114', '更新app下载权限', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10115', 'Admin search insight sandbox app biz data', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10116', '获取已安装该应用的APK列表', '/v1/admin/apps/{appId}/installed/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10117', '获取已安装该应用的终端列表', '/v1/admin/apps/{appId}/installed/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10118', '创建已安装终端列表下载任务', '/v1/admin/apps/{appId}/installedTerminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10119', '查询应用定向发布的应用市场', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10120', 'App specific merchant categories', '/v1/admin/apps/{appId}/merchant/categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10121', '更新应用的消息服务调用权限', '/v1/admin/apps/{appId}/msgServiceEnabled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10122', '查询应用定向发布的代理商', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10123', '应用恢复', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10124', 'App specific reseller or global APP publish to the normal market', '/v1/admin/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10125', '删除应用定向发布', '/v1/admin/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10126', '更新app可是范围', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10127', 'Refresh AccessToken via CloudServiceGateway', '/v1/admin/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10128', 'Get access url and token via CloudServiceGateway', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10129', '获取应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10130', '保存应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10131', '获取Dashboard里终端信息统计部件(W03)的数据', '/v1/admin/dashboard/widgets/W03', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10132', '获取Dashboard里终端数量部件(W09)的数据', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10133', '获取Dashboard里代理商商户终端汇总部件(W10)的数据', '/v1/admin/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10134', '获取Dashboard里代理商级别的操作日志(W11)的数据', '/v1/admin/dashboard/widgets/W11', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10135', '获取Dashboard里代理商终端汇总部件(W12)的数据', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10136', '获取Dashboard里 固件版本-终端数量-组织 (W13)的数据', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10137', '创建Dashboard里 FM-Terminal_Org(W13) 数据下载任务', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10138', '获取Dashboard里 Client-终端数量-组织 (W14)的数据', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10139', '创建Dashboard里 Client-终端数量-组织(W14) 数据下载任务', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10140', '获取Dashboard里 机型-终端数量 (W15)的数据', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10141', '创建Dashboard里 MODEL-Terminal_Org(W15) 数据下载任务', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10142', '获取Dashboard 长时间 offline (W16)的数据', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10143', '创建Dashboard里 offline-terminal(W16) 数据下载任务', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10144', '获取Dashboard里 hardware error(W17) 数据', '/v1/admin/dashboard/widgets/W17', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10145', '创建Dashboard里 hardware error(W17) 数据下载任务', '/v1/admin/dashboard/widgets/W17/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10146', '获取Dashboard里 FM-Terminal(W18) 数据', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10147', '创建Dashboard里 FM-Terminal(W18) 数据下载任务', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10148', '获取Dashboard里 Client-Terminal(W19) 数据', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10149', '创建Dashboard里 Client-Terminal(W19) 数据下载任务', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10150', '管理员查询开发者列表', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10151', '管理员获取开发者详细信息', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10152', '删除开发者', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10153', '通过开发者审核', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10154', '拒绝开发者审核', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10155', '定向发布开发者到代理商', '/v1/admin/developers/{developerId}/reseller', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10156', '更改定向发布开发者的代理商', '/v1/admin/developers/{developerId}/reseller/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10157', '关闭/删除开发者定向发布到代理商', '/v1/admin/developers/{developerId}/reseller/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10158', '恢复开发者帐号', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10159', '停用开发者帐号', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10160', '更新开发者账户', '/v1/admin/developers/{developerId}/user', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10161', '获取Global应用市场应用列表', '/v1/admin/global/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10162', '全局应用订阅', '/v1/admin/global/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10163', '全局应用取消订阅', '/v1/admin/global/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10164', '获取Global应用市场固件列表', '/v1/admin/global/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10165', '获取Global固件发布的应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10166', '发布全球应用市场固件到其他应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10167', '订阅Global应用市场的固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10168', '取消订阅Global应用市场固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10169', '发布Global应用市场POS Client应用到指定应用市场', '/v1/admin/global/selfApks/{selfApkId}/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10170', '获取地图当前边界内的标记', '/v1/admin/map/markers/bound', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', '获取当前环境所有终端标记', '/v1/admin/map/markers/god/perspective', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', '获取某地点内的POS机', '/v1/admin/map/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', '查询应用市场第三方系统API访问配置', '/v1/admin/market/3rdsys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', '允许应用市场第三方系统API访问', '/v1/admin/market/3rdsys/config/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', '禁止应用市场第三方系统API访问', '/v1/admin/market/3rdsys/config/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', '获取应用市场第三方系统API访问密钥', '/v1/admin/market/3rdsys/config/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', '重置应用市场第三方系统API访问密钥', '/v1/admin/market/3rdsys/config/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', '激活应用市场', '/v1/admin/market/activate', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10179', '查询登陆日志', '/v1/admin/market/audit/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', '导出登陆日志', '/v1/admin/market/audit/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', '查询操作日志参数详情', '/v1/admin/market/audit/operation/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', '查询操作日志', '/v1/admin/market/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', '导出操作日志', '/v1/admin/market/audit/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10184', '获取应用市场年度账单列表', '/v1/admin/market/billing', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', '获取AirViewer当前用量', '/v1/admin/market/billing/airViewer/currentUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', '获取AirViewer历史用量', '/v1/admin/market/billing/airViewer/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', '当前周期的账单详情', '/v1/admin/market/billing/current/billItem', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', '查询账单默认设置', '/v1/admin/market/billing/defaultSettings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', '更新账单默认设置', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', '获取账单概要', '/v1/admin/market/billing/summary/overview', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', '下载指定应用市场账单', '/v1/admin/market/billing/summary/{billingSummaryId}/file/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', '获取当前应用市场的终端接入量详情', '/v1/admin/market/billing/terminal/enroll/bill', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', '获取当前应用市场的终端接入量统计', '/v1/admin/market/billing/terminal/enroll/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', '获取当前应用市场的终端接入量统计', '/v1/admin/market/billing/terminal/enroll/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', '获取当前应用市场的终端接入量统计', '/v1/admin/market/billing/terminal/enroll/history/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', '下载指定应用市场账单数据文件', '/v1/admin/market/billing/{billingId}/datafile/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', '根据账单概要获取详细信息', '/v1/admin/market/billing/{summaryBillId}/billItems', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', '更新应用市场操作系统配置', '/v1/admin/market/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', '创建页脚', '/v1/admin/market/footer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', '更新页脚', '/v1/admin/market/footer/{footerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', '删除页脚', '/v1/admin/market/footer/{footerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', '更改应用市场页脚顺序', '/v1/admin/market/footer/{footerId}/sort', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', '查询邮件模板', '/v1/admin/market/mailTemplates/{templateName}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', '保存营销配置', '/v1/admin/market/sales', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', '查询所有应用市场配置', '/v1/admin/market/settings', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10206', '更新应用市场配置', '/v1/admin/market/settings', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10207', '查询应用市场配置', '/v1/admin/market/settings/{key}', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10208', '查询TID生成策略配置', '/v1/admin/market/tid/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', '更新TID生成策略配置', '/v1/admin/market/tid/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', '查询代理商应用市场配置', '/v1/admin/market/ui/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', '更新代理商应用市场配置', '/v1/admin/market/ui/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', '查询用户协议设置', '/v1/admin/market/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10213', '更新用户协议', '/v1/admin/market/user/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', '获取应用市场变量列表', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10215', '创建应用市场变量', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10216', '批量删除应用市场变量', '/v1/admin/market/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10217', '导入应用市场变量', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', '创建终应用市场变量导入模板下载任务', '/v1/admin/market/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10219', '查找应用市场变量支持的应用列表', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10220', '查找应用市场变量已使用的的应用列表', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10221', '更新应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10222', '删除应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10223', '根据serviceType获取已定阅的应用市场', '/v1/admin/market/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10224', '提交重新生成购买结算记录', '/v1/admin/purchase/clr', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10225', '(重新)提交购买结算的转帐请求', '/v1/admin/purchase/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10226', 'Admin Search Report List', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10227', 'Get Report Metadata', '/v1/admin/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10228', 'Get report by id', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10229', 'Update Report', '/v1/admin/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10230', 'Delete Report', '/v1/admin/report/{reportId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10231', 'Activate Report', '/v1/admin/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10232', 'Download Report Band File', '/v1/admin/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10233', 'Disable Report', '/v1/admin/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10234', 'Download Report Template File', '/v1/admin/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10235', 'List All Fields of Report', '/v1/admin/reportField', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10236', 'Update Report Field', '/v1/admin/reportField/{reportFieldId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10237', 'Get All Parameters of Report', '/v1/admin/reportParam', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10238', 'Update Report Parameter', '/v1/admin/reportParam/{reportParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10239', '创建应用市场RKI配置', '/v1/admin/rki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10240', '获取RKI服务列表', '/v1/admin/rki/servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10241', '删除RKI密钥模板KEY', '/v1/admin/rki/template/key', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10242', '获取RKI密钥模板KEY', '/v1/admin/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10243', '测试未保存应用市场RKI服务', '/v1/admin/rki/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10244', '测试已保存应用市场RKI服务', '/v1/admin/rki/test/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10245', '查询应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10246', '更新应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10247', '删除RKI服务配置', '/v1/admin/rki/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10248', '查询应用市场签名配置', '/v1/admin/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10249', '保存应用市场签名配置', '/v1/admin/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10250', '清除签名数据', '/v1/admin/signature/clearData', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', '获取应用市场签名提供商列表', '/v1/admin/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', '查询签名公钥', '/v1/admin/signature/signaturePuk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', '测试应用市场签名服务', '/v1/admin/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10254', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', 'disableVas', '/v1/admin/vas', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10256', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', 'listPosviewerFileTransferInfo', '/v1/admin/vas/posviewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', 'listPosvieweOperationInfo', '/v1/admin/vas/posviewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', 'listService', '/v1/admin/vas/service', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', 'enableService', '/v1/admin/vas/service/{serviceType}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', '获取应用参数列表', '/v1/apk/parameters', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10263', '创建应用参数', '/v1/apk/parameters', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10264', '查询参数APK列表', '/v1/apk/parameters/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10265', '查询参数应用列表', '/v1/apk/parameters/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10266', '获取应用参数详情', '/v1/apk/parameters/{apkParameterId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10267', '更新应用参数', '/v1/apk/parameters/{apkParameterId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10268', '删除应用参数', '/v1/apk/parameters/{apkParameterId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10269', '获取应用参数Schema', '/v1/apk/parameters/{apkParameterId}/schema', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10270', '查询应用列表', '/v1/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', '获取app统计的详细信息', '/v1/apps/appNumDetail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', '获取应用详情', '/v1/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', '获取应用APK列表', '/v1/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', '获取应用APK详情', '/v1/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', '获取应用评论列表', '/v1/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', '查询Entity属性', '/v1/attributes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10277', '创建Entity属性', '/v1/attributes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10278', '获取Entity属性信息', '/v1/attributes/{attributeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10279', '更新Entity属性', '/v1/attributes/{attributeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10280', '删除Entity属性', '/v1/attributes/{attributeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10281', '更新Entity属性标签', '/v1/attributes/{attributeId}/label', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10282', '验证激活验证码', '/v1/auth/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10283', '激活用户', '/v1/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10284', 'generateCaptcha', '/v1/auth/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10285', 'currentTokenLogin', '/v1/auth/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10286', 'destroySsoToken', '/v1/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10287', '验证重置邮箱验证码', '/v1/auth/email/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10288', '用户更改邮箱', '/v1/auth/email/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10289', '获取应用市场的相关信息', '/v1/auth/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10290', '通过backup code关闭用户OTP', '/v1/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10291', '验证关闭用户OTP Code是否有效', '/v1/auth/otp/disableCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10292', '发送关闭用户OTP邮件', '/v1/auth/otp/resetMail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10293', '忘记密码发送邮件(未激活的重发激活邮件)', '/v1/auth/password/forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10294', '验证重置密码验证码', '/v1/auth/password/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10295', '重置密码（用于忘记密码）', '/v1/auth/password/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10296', '获取当前的密码规则', '/v1/auth/password/rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10297', '注册用户', '/v1/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10298', '获取字典列表', '/v1/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10299', '获取语言列表', '/v1/codes/lang', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10300', 'super管理员获取Code配置列表', '/v1/codes/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10301', 'super管理员查询CodeType类型列表', '/v1/codes/setting/codeTypes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10302', 'super管理员保存Code配置', '/v1/codes/setting/save', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10303', 'super管理员获取Code配置', '/v1/codes/setting/{type}/{value}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10304', 'super管理员删除Code配置', '/v1/codes/setting/{type}/{value}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10305', '根据类型获取字典列表', '/v1/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10306', '创建开发者原始Apk下载任务', '/v1/developers/apks/{apkId}/originFile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10307', '创建开发者下载apk参数模版下载任务', '/v1/developers/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10308', '获取开发者应用参数Schema', '/v1/developers/apks/{apkId}/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10309', '下载自定义参数模板最终生成的.p文件-内容为默认值', '/v1/developers/app/paramFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10310', '获取开发者在线自定义应用参数Schema-预览', '/v1/developers/app/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10311', '查询开发者应用列表', '/v1/developers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10312', '创建用户应用', '/v1/developers/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10313', '获取开发者应用详细信息', '/v1/developers/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10314', '删除开发者应用', '/v1/developers/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10315', '获取APK列表', '/v1/developers/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10316', '添加apk', '/v1/developers/apps/{appId}/apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10317', '获取APK', '/v1/developers/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10318', '覆盖更新APK信息、参数模板,图标与截图', '/v1/developers/apps/{appId}/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10319', '删除开发者应用版本', '/v1/developers/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10320', '更新APK文件', '/v1/developers/apps/{appId}/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10321', '提交APK', '/v1/developers/apps/{appId}/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10322', '覆盖更新APP Key, APP Secret', '/v1/developers/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', 'developer search insight sandbox app biz data', '/v1/developers/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', '查询开发者证书', '/v1/developers/cert', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', '开发者证书下载', '/v1/developers/cert', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', '开发者获取上次自定义模板编辑内容', '/v1/developers/custom/param/template/{paramId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', '添加企业开发者', '/v1/developers/enterprise', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', '查询企业开发者列表', '/v1/developers/enterprise/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', '刪除企业开发者用户', '/v1/developers/enterprise/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', '将开发者设为管理员', '/v1/developers/enterprise/{userId}/admin', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', '查询开发者的应用销售列表', '/v1/developers/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', '查询开发者的应用所销售的市场列表', '/v1/developers/purchases/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', '开发者应用结算查询', '/v1/developers/report/clearence', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', '开发者获取代理商列表', '/v1/developers/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', '开发者获取代理商树', '/v1/developers/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', '获取应用沙箱测试列表', '/v1/developers/sandbox/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', '创建沙箱终端推送应用', '/v1/developers/sandbox/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', '获取沙箱终端推送APK', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', '删除沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', '激活沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', '获取沙箱终端推送APK参数', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', '更新沙箱终端推送参数', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10343', '重置沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10344', '获取开发者终端列表', '/v1/developers/sandbox/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10345', '创建开发者终端', '/v1/developers/sandbox/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10346', '获取开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10347', '更新开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10348', '删除开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10349', '查询开发者概况', '/v1/developers/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', '上传自定义参数模板并进行解析Xml2Json', '/v1/developers/{devId}/app/{appId}/paramAnalysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', '创建参数模板', '/v1/developers/{devId}/app/{appId}/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', '删除自定义参数模板', '/v1/developers/{devId}/app/{appId}/paramTemplateSchema', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', '获取自定义参数模板列表', '/v1/developers/{devId}/custom/param/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', '检查参数模板名称', '/v1/developers/{devId}/paramTemplateName', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', '更新参数模板', '/v1/developers/{paramId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', '文档中心-文档列表', '/v1/doc-center', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10357', '文档中心-文档类别', '/v1/doc-center/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10358', '文档中心-读取指导文件信息', '/v1/doc-center/{guideId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10359', '文档中心-读取文档-docId', '/v1/doc-center/{guideId}/docs/{docId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10360', '文档中心-读取文档-fileName', '/v1/doc-center/{guideId}/file/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10361', '下载Fastdfs服务器上的文件', '/v1/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10362', '根据下载号获得下载地址', '/v1/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10363', '获取制造商列表', '/v1/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', '创建制造商', '/v1/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', '获取制造商机型树', '/v1/factories/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', '获取制造商', '/v1/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', '更新制造商', '/v1/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', '删除制造商', '/v1/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', '激活制造商', '/v1/factories/{factoryId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', '停用制造商', '/v1/factories/{factoryId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', '查询反馈列表', '/v1/feedbacks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10372', '创建反馈', '/v1/feedbacks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10373', '获取反馈信息', '/v1/feedbacks/{feedbackId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10374', '更新反馈', '/v1/feedbacks/{feedbackId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10375', '删除反馈', '/v1/feedbacks/{feedbackId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10376', '获取固件列表', '/v1/firmwares', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10377', '创建资源包客户类型', '/v1/firmwares/customerType', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10378', '删除资源包客户类型', '/v1/firmwares/customerType/code', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10379', '查看固件的factory列表', '/v1/firmwares/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', '上传固件', '/v1/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', '删除固件差分包', '/v1/firmwares/firmware/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', '获取固件', '/v1/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', '更新固件', '/v1/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', '删除固件', '/v1/firmwares/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', '固件审核通过', '/v1/firmwares/{firmwareId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', '上传固件差分包', '/v1/firmwares/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', '下线固件', '/v1/firmwares/{firmwareId}/offline', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10388', '上线固件', '/v1/firmwares/{firmwareId}/online', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10389', '固件审核拒绝', '/v1/firmwares/{firmwareId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10390', '提交固件更新', '/v1/firmwares/{firmwareId}/submit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10391', '获取终端分组变量列表', '/v1/groupVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10392', '创建终端分组变量', '/v1/groupVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10393', '批量删除终端分组变量', '/v1/groupVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10394', '导入终端分组变量', '/v1/groupVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10395', '创建终端分组变量导入模板下载任务', '/v1/groupVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10396', '查找终端分组变量支持的应用列表', '/v1/groupVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10397', '查找终端分组变量已使用的应用列表', '/v1/groupVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10398', '更新终端分组变量', '/v1/groupVariables/{groupVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10399', '删除终端分组变量', '/v1/groupVariables/{groupVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10400', '读取指导文件信息', '/v1/guides/{guideId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', '读取文档', '/v1/guides/{guideId}/docs/{docId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', 'Clear Cache', '/v1/internal/3rdsys/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10403', 'Get Cache', '/v1/internal/3rdsys/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10404', 'Refresh Group Action Count', '/v1/internal/3rdsys/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10405', 'Get system properties', '/v1/internal/3rdsys/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10406', 'Save System Property', '/v1/internal/3rdsys/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10407', 'Get Terminal', '/v1/internal/3rdsys/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10408', 'Send terminal command', '/v1/internal/3rdsys/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10409', 'Send terminal message', '/v1/internal/3rdsys/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10410', 'Administrator view push history list', '/v1/internal/3rdsys/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10411', 'Expire terminal token', '/v1/internal/3rdsys/terminal/token/expire', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10412', 'Get Filter Terminals', '/v1/internal/3rdsys/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10413', 'Post Filter Terminals', '/v1/internal/3rdsys/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10414', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10415', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10416', 'getApps', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10417', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10418', '获取许可证信息', '/v1/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10419', '更新许可证', '/v1/license', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10420', '获取市场可见服务列表', '/v1/marketAdmin/vas/services', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10421', '市场取消订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10422', '市场订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10423', '查询应用市场列表', '/v1/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10424', '创建应用市场', '/v1/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10425', '获取应用市场下app数量', '/v1/markets/applicationNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10426', '统计开发者数量', '/v1/markets/developerNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10427', '获取开发者统计信息详情', '/v1/markets/developerNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10428', '获取应用市场数/快过期的数量', '/v1/markets/marketNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10429', '获取应用市场详细统计信息', '/v1/markets/marketNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10430', '终端数量', '/v1/markets/terminalNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10431', '生成应用市场终端报表', '/v1/markets/terminalReport', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10432', '获取应用市场信息', '/v1/markets/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10433', '更新应用市场', '/v1/markets/{marketId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10434', '删除应用市场', '/v1/markets/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10435', '手动激活应用市场（根据输入邮箱创建管理员和开发者并激活）', '/v1/markets/{marketId}/activateManuallyMarket', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10436', '替换应用市场管理员（邮箱）', '/v1/markets/{marketId}/replaceEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10437', '重新发送应用市场激活邮件', '/v1/markets/{marketId}/resend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10438', '重置应用市场code', '/v1/markets/{marketId}/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10439', '解挂应用市场', '/v1/markets/{marketId}/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10440', '挂起应用市场', '/v1/markets/{marketId}/suspend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10441', '获取商户分类列表', '/v1/merchantCategories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10442', '创建商户分类', '/v1/merchantCategories', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10443', '批量删除商户分类', '/v1/merchantCategories/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10444', '更新商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10445', '删除商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10446', '获取商户变量列表', '/v1/merchantVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10447', '创建商户变量', '/v1/merchantVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10448', '批量删除商户变量', '/v1/merchantVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10449', '导入商户变量', '/v1/merchantVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10450', '创建商户变量导入模板下载任务', '/v1/merchantVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10451', '查找商户变量支持的应用列表', '/v1/merchantVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10452', '查找商户变量已使用的应用列表', '/v1/merchantVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10453', '更新商户变量', '/v1/merchantVariables/{merchantVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10454', '删除商户变量', '/v1/merchantVariables/{merchantVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10455', '获取商户列表', '/v1/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10456', '创建商户', '/v1/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10457', '创建商户导出下载任务', '/v1/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10458', '导入商户', '/v1/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10459', '创建商户导入模板下载任务', '/v1/merchants/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10460', '获取商户', '/v1/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10461', '更新商户', '/v1/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10462', '删除商户', '/v1/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10463', '激活商户', '/v1/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10464', '停用商户', '/v1/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10465', '商户发送消息到终端', '/v1/merchants/{merchantId}/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10466', '获取商户配置文件', '/v1/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10467', '创建商户配置文件', '/v1/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10468', '替换商户管理员（邮箱）', '/v1/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10469', '重发商户激活邮件', '/v1/merchants/{merchantId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10470', '查询Migrations', '/v1/migrations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10471', '创建Migration', '/v1/migrations', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10472', '获取Migration', '/v1/migrations/{migrationId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10473', '删除Migration', '/v1/migrations/{migrationId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10474', '查询Migration Apk Template', '/v1/migrations/{migrationId}/apkTemplates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10475', '执行Migration', '/v1/migrations/{migrationId}/execute', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10476', '导出Migration结果', '/v1/migrations/{migrationId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10477', '查询Migration Merchant', '/v1/migrations/{migrationId}/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10478', '通知Migration新创建的用户', '/v1/migrations/{migrationId}/notify', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10479', '回滚Migration', '/v1/migrations/{migrationId}/rollback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10480', '查询Migration Terminal Apk', '/v1/migrations/{migrationId}/terminalApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10481', '查询Migration Terminal Group', '/v1/migrations/{migrationId}/terminalGroups', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10482', '查询Migration Terminal', '/v1/migrations/{migrationId}/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10483', '查询Migration User', '/v1/migrations/{migrationId}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10484', '验证Migration', '/v1/migrations/{migrationId}/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10485', '获取机型列表', '/v1/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10486', '创建机型', '/v1/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10487', '获取机型', '/v1/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10488', '更新机型', '/v1/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10489', '删除机型', '/v1/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10490', '激活机型', '/v1/models/{modelId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10491', '查询机型可用的应用列表', '/v1/models/{modelId}/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10492', '停用机型', '/v1/models/{modelId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10493', '添加AppMsg', '/v1/msg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10494', '删除AppMsg', '/v1/msg', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10495', '检查当前应用市场或自应用市场是否有终端安装该app', '/v1/msg/app/installed/check/{packageName}/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10496', '获取appMsg', '/v1/msg/appMsgList', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10497', '获取appMsg统计数据', '/v1/msg/report/{msgId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10498', '上传msgTemplateFile', '/v1/msg/template/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10499', '获取app的vasSupport', '/v1/msg/vas/support/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10500', '更新AppMsg状态', '/v1/msg/{appMsgId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10501', '根据id 获取msg', '/v1/msg/{id}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10502', '获取所有受保护的操作列表', '/v1/operations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10503', '获取所有受保护的操作', '/v1/operations/{key}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10504', '关闭操作权限', '/v1/operations/{key}/close', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10505', '打开操作权限', '/v1/operations/{key}/open', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10506', '查询操作用户', '/v1/operations/{key}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10507', '添加操作用户', '/v1/operations/{key}/users/{userId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10508', '删除操作用户', '/v1/operations/{key}/users/{userId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10509', '查询权限列表', '/v1/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10510', '创建权限', '/v1/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10511', '获取权限信息', '/v1/privileges/{privilegeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10512', '更新权限', '/v1/privileges/{privilegeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10513', '删除权限', '/v1/privileges/{privilegeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10514', '获取权限资源列表', '/v1/privileges/{privilegeId}/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10515', '添加权限资源', '/v1/privileges/{privilegeId}/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10516', '删除权限资源', '/v1/privileges/{privilegeId}/resources', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10517', 'getEnvCode', '/v1/pub/vas/envCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10518', '查询当前用户的应用购买列表', '/v1/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10519', '为当前用户创建应用购买记录', '/v1/purchases', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10520', '查询当前用户指定应用的购买记录详情', '/v1/purchases/app/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10521', '查询当前用户指定应用的待付款购买记录详情', '/v1/purchases/app/{appId}/status/pending', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10522', '查询当前用户可购买该应用的终端列表(注:已成功购买过的终端除外)', '/v1/purchases/app/{appId}/terminals/purchasable', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10523', '提交支付请求', '/v1/purchases/payment', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10524', '获取支付服务的client配置信息', '/v1/purchases/payment/clientConfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10525', '查询可用的支付方式类型', '/v1/purchases/payments', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10526', '查询当前用户指定的购买记录详情', '/v1/purchases/{purchaseId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10527', '更新当前用户或终端的应用购买记录', '/v1/purchases/{purchaseId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10528', '删除当前用户或终端的应用购买记录', '/v1/purchases/{purchaseId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10529', '绑定购买应用至终端', '/v1/purchases/{purchaseId}/bind', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10530', '查询当前用户指定的购买记录列表', '/v1/purchases/{purchaseId}/items', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10531', '获得请求的支付类型的初始化参数(用户购买)', '/v1/purchases/{purchaseId}/payment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10532', '获取指定购买的终端推送状态', '/v1/purchases/{purchaseId}/push', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10533', '推送购买应用至终端', '/v1/purchases/{purchaseId}/push', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10534', 'User Search Report', '/v1/report', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10535', 'Get Report Categories', '/v1/report/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10536', 'GET merchants by resellerIds', '/v1/report/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10537', 'Refresh Report Parameter Sources', '/v1/report/parameter/{parameterId}/source/refresh', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10538', 'Delete Report Execution Tasks', '/v1/report/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10539', 'Search Report Job History', '/v1/report/reportJobHistory', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10540', 'Search Report Task', '/v1/report/reportTask', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10541', 'Update Report Tasks Status', '/v1/report/reportTask/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10542', 'Update Report Task Status', '/v1/report/reportTask/{reportTaskId}/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10543', 'GET resellers by marketId', '/v1/report/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10544', 'Delete Report Execution Task', '/v1/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10545', 'User View Report', '/v1/report/{reportId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10546', 'View Report Online By Chart', '/v1/report/{reportId}/chart', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10547', 'Export Report', '/v1/report/{reportId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10548', 'Create Report Execution Task', '/v1/report/{reportId}/reportExecutionContext', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10549', 'Get Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10550', 'Update Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10551', 'View Report Online', '/v1/report/{reportId}/view', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10552', '查询开发者应用结算相关数据', '/v1/reports/clearence/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10553', '查询应用市场应用结算相关数据', '/v1/reports/clearence/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10554', '查询平台应用结算相关数据', '/v1/reports/clearence/platform', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10555', '查询报表所需的应用市场列表', '/v1/reports/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10556', '查询应用购买明细列表', '/v1/reports/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10557', '按应用查询销售汇总', '/v1/reports/purchases/app/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10558', '按开发者查询销售汇总', '/v1/reports/purchases/developer/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10559', '获取代理商列表', '/v1/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10560', '创建代理商', '/v1/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10561', '代理商查询应用列表', '/v1/resellers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10562', '获取当前用户代理商树', '/v1/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10563', '获取代理商', '/v1/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10564', '更新代理商', '/v1/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10565', '删除代理商', '/v1/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10566', '激活代理商', '/v1/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10567', '停用代理商', '/v1/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10568', '获取当前代理商终端已安装应用列表', '/v1/resellers/{resellerId}/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10569', '代理商发送消息到终端', '/v1/resellers/{resellerId}/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10570', '获取代理商配置文件', '/v1/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10571', '创建代理商配置文件', '/v1/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10572', '替换代理商管理员（邮箱）', '/v1/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10573', '重发激活邮件', '/v1/resellers/{resellerId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10574', '同步代理商的RKI密钥列表', '/v1/resellers/{resellerId}/rki/keys/collect', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10575', '保存代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10576', '删除代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10577', '查询资源列表', '/v1/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10578', '创建资源', '/v1/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10579', '获取资源信息', '/v1/resources/{resourceId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10580', '更新资源', '/v1/resources/{resourceId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10581', '删除资源', '/v1/resources/{resourceId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10582', '获取资源权限列表', '/v1/resources/{resourceId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10583', '添加资源权限', '/v1/resources/{resourceId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10584', '删除资源权限', '/v1/resources/{resourceId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10585', '查询角色列表', '/v1/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10586', '创建角色', '/v1/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10587', '查询角色列表包含开发者和商户', '/v1/roles/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10588', '获取角色的用户', '/v1/roles/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10589', '查询所有角色的用户列表', '/v1/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10590', '获取角色信息', '/v1/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10591', '更新角色', '/v1/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10592', '删除角色', '/v1/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10593', '获取角色权限列表', '/v1/roles/{roleId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10594', '添加角色权限', '/v1/roles/{roleId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10595', '删除角色权限', '/v1/roles/{roleId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10596', '获取角色用户列表', '/v1/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10597', '添加角色用户', '/v1/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10598', '删除角色用户', '/v1/roles/{roleId}/users', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10599', '获取所有的定时任务信息', '/v1/schedule', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10600', '手动调用定时任务', '/v1/schedule/active', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10601', '管理员查看POS Client App列表', '/v1/selfApps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10602', '查看POS Client App的factory列表', '/v1/selfApps/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10603', '创建最新PAXSTORE客户端下载任务', '/v1/selfApps/latest/client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10604', '查看POS Client Apk列表for approval', '/v1/selfApps/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10605', '查看POS Client Apk', '/v1/selfApps/selfApks/{selfApkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10606', '首次上传POS Client Apk', '/v1/selfApps/{factoryId}/selfApk/file/first', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10607', '获取POS Client App详细信息', '/v1/selfApps/{selfAppId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10608', '删除POS Client App', '/v1/selfApps/{selfAppId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10609', '上传新版本POS Client Apk', '/v1/selfApps/{selfAppId}/selfApk/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10610', '查看某个POS Client App的Apk列表', '/v1/selfApps/{selfAppId}/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10611', '更新SelfApk(更新日志，强制更新)，SelfApp name', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10612', '删除POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10613', '通过POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/approval', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10614', '创建PAXSTORE客户端下载任务', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10615', '重新上传POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10616', '下线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10617', '上线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/online', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10618', '拒绝POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/rejection', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10619', '提交POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/submit', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10620', '获取系统配置', '/v1/system/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10621', '查询页脚列表', '/v1/system/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10622', '获取页脚', '/v1/system/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10623', '查询系统属性', '/v1/system/properties', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10624', '创建系统属性', '/v1/system/properties', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10625', '获取系统属性信息', '/v1/system/properties/{systemPropertyId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10626', '更新系统属性', '/v1/system/properties/{systemPropertyId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10627', '删除系统属性', '/v1/system/properties/{systemPropertyId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10628', '查询推送检测结果', '/v1/system/push/diagnosis', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10629', '发送推送检测消息', '/v1/system/push/diagnosis', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10630', '查询预定义角色列表', '/v1/system/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10631', '查询登录相关配置', '/v1/system/security/loginCfg', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10632', '保存登录相关配置', '/v1/system/security/loginCfg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10633', '查询密码策略', '/v1/system/security/password/policy', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10634', '保存密码策略', '/v1/system/security/password/policy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10635', '查询用户协议', '/v1/system/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10636', '根据登录名查找用户信息', '/v1/system/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10637', '查询终端推送记录', '/v1/terminal/actions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10638', '批量更新终端操作状态', '/v1/terminal/actions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10639', '更新终端操作状态', '/v1/terminal/actions/{actionId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10640', 'POS端获取终端广告配置', '/v1/terminal/advertisements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10641', '获取终端应用参数下载列表', '/v1/terminal/app/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10642', '获取终端应用参数下载地址', '/v1/terminal/app/params/{actionId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10643', '获取终端应用下载列表', '/v1/terminal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10644', '根据服务器的信息获取终端应用自动更新列表', '/v1/terminal/apps/autoUpdate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10645', '根据终端上送的信息获取终端应用自动更新列表', '/v1/terminal/apps/autoUpdate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10646', '终端查询应用列表', '/v1/terminal/apps/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10647', '终端获取应用APK列表', '/v1/terminal/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10648', '终端获取应用APK详情', '/v1/terminal/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10649', '下载应用APK', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10650', '<s>下载应用APK</s> - deprecated', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10651', '获取APK下载信息', '/v1/terminal/apps/{appId}/apks/{apkId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10652', '验证应用认证信息', '/v1/terminal/apps/{appId}/auth/verify', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10653', '终端获取应用评论', '/v1/terminal/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10654', '终端保存应用评论', '/v1/terminal/apps/{appId}/comments', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', '终端删除应用评论', '/v1/terminal/apps/{appId}/comments', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', '终端获取应用详情', '/v1/terminal/apps/{appId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', '<s>下载应用最新的APK</s> - deprecated', '/v1/terminal/apps/{appId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', 'Get push channel in terminal table', '/v1/terminal/cloud/msg/pushchannel', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', 'Update push channel in terminal table', '/v1/terminal/cloud/msg/pushchannel', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', 'Refresh AccessToken via CloudServiceGateway for terminal', '/v1/terminal/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', 'Get access url and token via CloudServiceGateway for terminal', '/v1/terminal/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', '获取终端命令列表', '/v1/terminal/commands', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', '获取终端激活命令', '/v1/terminal/commands/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10664', '终端上送终端配置-Deprecated', '/v1/terminal/configurations', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', '终端上送详情信息-Deprecated', '/v1/terminal/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', '终端下载固件', '/v1/terminal/firmware/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', '获取终端固件下载列表', '/v1/terminal/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', 'POS端获取终端硬件状态列表', '/v1/terminal/hardware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', '终端上传终端位置-Deprecated', '/v1/terminal/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', '终端登录', '/v1/terminal/login', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10671', '终端登录验证', '/v1/terminal/login/validate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10672', '终端上送日志', '/v1/terminal/logs', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', '获取应用市场APP最新的更新信息', '/v1/terminal/market/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', '下载应用市场APP', '/v1/terminal/market/update', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', '终端商户登录', '/v1/terminal/merchant/login', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', '获取终端商户用户信息', '/v1/terminal/merchant/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', '终端上送监控信息-Deprecated', '/v1/terminal/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', '获取终端操作任务列表', '/v1/terminal/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', '获取需要更新参数的应用Apk列表', '/v1/terminal/param/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', '获取需要更新参数的应用列表', '/v1/terminal/param/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10681', '查询ped设置', '/v1/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', '获取终端配置文件', '/v1/terminal/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', '查询当前终端的应用购买列表', '/v1/terminal/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', '查询当前终端指定应用的购买记录详情', '/v1/terminal/purchases/app/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', '获得终端购买应用请求的支付类型的初始化参数', '/v1/terminal/purchases/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', '提交终端购买应用支付请求', '/v1/terminal/purchases/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', '远程初始化终端', '/v1/terminal/remote/init', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10688', '根据SN获取终端信息', '/v1/terminal/remote/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10689', '根据序列号检查库存终端是否合法', '/v1/terminal/remote/stock/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10690', '根据TID获取终端信息', '/v1/terminal/remote/tid/{tid}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10691', 'POS端获取终端代理商证书', '/v1/terminal/reseller/certificate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', 'RKI服务器回调方法', '/v1/terminal/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10693', '获取RKI服务器配置信息', '/v1/terminal/rki/server/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', '传统终端libpaxstore.so获取RKI任务接口', '/v1/terminal/rki/task', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10695', '获取终端RKI任务列表', '/v1/terminal/rki/task/action', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10696', '获取当前终端状态', '/v1/terminal/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', '同步已安装应用列表', '/v1/terminal/sync/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10698', '同步已安装(非PAXSTORE)应用列表ICON信息', '/v1/terminal/sync/apps/icons', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10699', '终端上送手动安装的应用', '/v1/terminal/sync/apps/manual', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10700', '同步已安装应用列表,返回需同步ICON的APP', '/v1/terminal/sync/apps/new', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', '终端上送终端配置', '/v1/terminal/sync/configurations', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', '终端上送单个终端配置', '/v1/terminal/sync/configurations/{key}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', '终端上送详情信息', '/v1/terminal/sync/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', '同步已安装固件', '/v1/terminal/sync/firmware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10705', '同步硬件状态信息', '/v1/terminal/sync/hardware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10706', '同步应用信息', '/v1/terminal/sync/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10707', '终端上传终端位置', '/v1/terminal/sync/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10708', '终端上送监控信息', '/v1/terminal/sync/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', '同步终端协议', '/v1/terminal/sync/protocol', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10710', '终端推送测试', '/v1/terminal/test/push', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10711', '获取需要卸载的应用Apk列表', '/v1/terminal/uninstall/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10712', '下载DEX', '/v1/terminal/{terminalId}/dex/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10713', '获取终端推送应用列表', '/v1/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', '创建终端推送应用', '/v1/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', '获取终端推送APK', '/v1/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', '删除终端推送应用', '/v1/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', '激活终端推送应用', '/v1/terminalApks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', '获取终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', '更新终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', '获取终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', '保存终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', '重置终端推送应用', '/v1/terminalApks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', '挂起终端推送应用', '/v1/terminalApks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', '获取终端推送固件列表', '/v1/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', '创建终端推送固件', '/v1/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10726', '获取终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10727', '删除终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10728', '激活终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10729', '重置终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10730', '挂起终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10731', '获取终端分组推送应用列表', '/v1/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10732', '创建终端分组推送应用', '/v1/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10733', '更新终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10734', '删除终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10735', '获取终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10736', '删除终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10737', '重新推送终端分组应用', '/v1/terminalGroupApks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10738', '激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10739', '创建终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10740', '获取终端分组推送应用参数', '/v1/terminalGroupApks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10741', '更新终端分组推送应用参数参数', '/v1/terminalGroupApks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10742', '重新推送终端分组应用参数', '/v1/terminalGroupApks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10743', '获取终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10744', '修改终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10745', '获取终端分组推送应用参数终端列表', '/v1/terminalGroupApks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10746', '获取终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10747', '保存终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10748', '重新激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10749', '挂起终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10750', '获取终端分组推送应用终端列表', '/v1/terminalGroupApks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10751', '创建分组应用推送终端列表下载任务', '/v1/terminalGroupApks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10752', '获取分组推送固件列表', '/v1/terminalGroupFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10753', '创建分组推送固件', '/v1/terminalGroupFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10754', '获取分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10755', '删除分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10756', '重新推送终端分组固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10757', '激活分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10758', '重置分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10759', '挂起分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10760', '获取分组推送固件终端列表', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10761', '创建分组固件推送终端列表下载任务', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10762', '获取分组推送Operation列表', '/v1/terminalGroupOpts', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10763', '创建分组推送Operation', '/v1/terminalGroupOpts', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10764', '获取分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10765', '删除分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10766', '重新推送终端分组Operation', '/v1/terminalGroupOpts/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10767', '激活分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10768', '重置分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10769', '挂起分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10770', '获取分组推送Operation终端列表', '/v1/terminalGroupOpts/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10771', '创建分组Operation推送终端列表下载任务', '/v1/terminalGroupOpts/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10772', '获取分组推送RKI列表', '/v1/terminalGroupRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10773', '创建分组推送RKI', '/v1/terminalGroupRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10774', '获取分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10775', '删除分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10776', '重新推送终端分组RKI', '/v1/terminalGroupRkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10777', '激活分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10778', '重置分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10779', '挂起分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10780', '获取分组推送RKI终端列表', '/v1/terminalGroupRkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10781', '创建分组RKI推送终端列表下载任务', '/v1/terminalGroupRkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10782', '获取分组推送卸载应用列表', '/v1/terminalGroupUninstallApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10783', '创建分组推送卸载应用', '/v1/terminalGroupUninstallApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10784', '获取分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10785', '删除分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10786', '重新推送终端分组卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10787', '激活分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10788', '重置分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10789', '挂起分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10790', '获取分组推送卸载应用终端列表', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10791', '创建分组卸载应用推送终端列表下载任务', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10792', '获取终端分组列表', '/v1/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10793', '创建终端分组', '/v1/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10794', '导入终端到终端分组', '/v1/terminalGroups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10795', '创建分组导入终端模板下载任务', '/v1/terminalGroups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10796', '获取终端分组', '/v1/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10797', '更新终端分组', '/v1/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10798', '删除终端分组', '/v1/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10799', '激活终端分组', '/v1/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10800', '停用终端分组', '/v1/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10801', '获取分组终端列表', '/v1/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10802', '添加分组终端', '/v1/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10803', '删除分组终端', '/v1/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10804', '获取分组终端数量', '/v1/terminalGroups/{groupId}/terminals/count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10805', '获取终端推送RKI列表', '/v1/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10806', '创建终端推送RKI', '/v1/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10807', '获取终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10808', '删除终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10809', '激活终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10810', '重置终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10811', '挂起终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10812', '获取终端变量列表', '/v1/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10813', '创建终端变量', '/v1/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10814', '批量删除终端变量', '/v1/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10815', '导入终端变量', '/v1/terminalVariables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10816', '创建终端变量导入模板下载任务', '/v1/terminalVariables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10817', '查找终端变量支持的应用列表', '/v1/terminalVariables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10818', '查找终端变量已使用的应用列表', '/v1/terminalVariables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10819', '更新终端变量', '/v1/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10820', '删除终端变量', '/v1/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10821', '获取终端列表', '/v1/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10822', '创建终端', '/v1/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10823', '批量创建终端', '/v1/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10824', '批量激活终端', '/v1/terminals/batch/activation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10825', '批量删除终端', '/v1/terminals/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10826', '批量停用终端', '/v1/terminals/batch/suspension', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10827', '复制终端', '/v1/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10828', '创建终端导出下载任务', '/v1/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10829', '批量添加终端到分组', '/v1/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10830', '导入终端', '/v1/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10831', '创建终端导入模板下载任务', '/v1/terminals/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10832', '获取后端给终端推送命令记录列表', '/v1/terminals/push/terminal/history/api', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10833', '根据序列号获取终端', '/v1/terminals/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10834', '获取终端设备库存列表', '/v1/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10835', '批量创建终端设备库存', '/v1/terminals/stock/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10836', '批量分配库存终端', '/v1/terminals/stock/batch/assign', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10837', '批量删除库存终端', '/v1/terminals/stock/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10838', '创建库存终端导出下载任务', '/v1/terminals/stock/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10839', '导入库存终端', '/v1/terminals/stock/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10840', '创建库存终端导入模板下载任务', '/v1/terminals/stock/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10841', '根据序列号获取库存终端', '/v1/terminals/stock/serialNo/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10842', '获取库存终端', '/v1/terminals/stock/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10843', '更新库存终端', '/v1/terminals/stock/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10844', '删除库存终端', '/v1/terminals/stock/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10845', '获取终端', '/v1/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10846', '更新终端', '/v1/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10847', '删除终端', '/v1/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10848', '激活终端', '/v1/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10849', '获取终端的操作日志', '/v1/terminals/{terminalId}/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10850', '创建终端商业数据下载任务', '/v1/terminals/{terminalId}/bizData/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10851', '获取终端商业数据', '/v1/terminals/{terminalId}/bizDatas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10852', '收集终端logcat', '/v1/terminals/{terminalId}/collect/logcat', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10853', 'web查询终端配置', '/v1/terminals/{terminalId}/configurations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10854', '获取终端详情信息', '/v1/terminals/{terminalId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10855', '停用终端', '/v1/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10856', '获取终端外接详情信息', '/v1/terminals/{terminalId}/extra/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10857', '获取终端硬件状态列表', '/v1/terminals/{terminalId}/hardware', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10858', '获取终端历史在线时长', '/v1/terminals/{terminalId}/history/online', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10859', '获取终端已安装应用列表', '/v1/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10860', '获取终端已安装应用列表统计', '/v1/terminals/{terminalId}/installedApks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10861', '查看终端已安装应用参数', '/v1/terminals/{terminalId}/installedApks/{apkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10862', '卸载终端已安装应用', '/v1/terminals/{terminalId}/installedApks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10863', '获取终端已安装固件', '/v1/terminals/{terminalId}/installedFirmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10864', '获取终端位置信息', '/v1/terminals/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10865', '获取终端日志列表', '/v1/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10866', '获取终端手动安装应用列表', '/v1/terminals/{terminalId}/manual/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10867', '获取终端监控信息', '/v1/terminals/{terminalId}/monitors', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10868', '推送终端命令', '/v1/terminals/{terminalId}/operation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10869', '获取终端操作状态', '/v1/terminals/{terminalId}/optStatus', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10870', '向终端推送CheckUp应用', '/v1/terminals/{terminalId}/push/checkup', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', '收集终端详情(硬件等)', '/v1/terminals/{terminalId}/refresh/terminalDetail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10872', '更新终端是否允许远程换机配置', '/v1/terminals/{terminalId}/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', '获取终端的换机日志', '/v1/terminals/{terminalId}/replace/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', '更新终端代理商', '/v1/terminals/{terminalId}/reseller', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10875', '获取终端流量', '/v1/terminals/{terminalId}/traffic', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10876', '向终端推送POSVIEWER或者启动POSVIEWER', '/v1/terminals/{terminalId}/{installedPosviewer}/remote/assistance', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10877', '查询所有用户列表', '/v1/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10878', '更新用户信息', '/v1/users', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10879', '查询用户有开发者权限的应用市场', '/v1/users/developer/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10880', '获取开发者的收款帐户是否设置', '/v1/users/developer/receivable/account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10881', '设置开发者收款帐户', '/v1/users/developer/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10882', '删除开发者的收款帐户', '/v1/users/developer/receivable/account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10883', '申请成为开发者', '/v1/users/developers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', '获取当前用户开发者信息', '/v1/users/developers/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10885', '创建用户导出下载任务', '/v1/users/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10886', '获取应用市场收款是否设置', '/v1/users/market/receivable/account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10887', '设置应用市场收款帐户', '/v1/users/market/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10888', '删除应用市场收款帐户', '/v1/users/market/receivable/account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10889', 'List all configuration (notification)', '/v1/users/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10890', 'Update a configuration (notification)', '/v1/users/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10891', 'Publish global notification', '/v1/users/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10892', 'List messages (notification)', '/v1/users/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10893', 'Update a set of messages as read (notification)', '/v1/users/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10894', 'Delete a set of messages (notification)', '/v1/users/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10895', 'Update all messages as read (notification)', '/v1/users/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10896', 'Get message statistics (notification)', '/v1/users/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10897', 'Read specific messages in top reminder (notification)', '/v1/users/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10898', 'Read a message (notification)', '/v1/users/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10899', 'Delete a message (notification)', '/v1/users/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10900', 'Download message attachment (notification)', '/v1/users/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10901', 'View message details (notification)', '/v1/users/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10902', 'List all subscribed topics (notification)', '/v1/users/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10903', 'Unsubscribe all topics (notification)', '/v1/users/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10904', 'Get subscription info if subscribed (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10905', 'Subscribe a topic (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10906', 'Unsubscribe a topic (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10907', '获取当前用户OTP', '/v1/users/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10908', '关闭当前用户OTP', '/v1/users/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10909', '启用当前用户OTP', '/v1/users/otp/activation', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10910', '重置用户OTP的backupCode', '/v1/users/otp/backupCode', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10911', '获取当前用户OTP二维码图片', '/v1/users/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10912', '更改密码', '/v1/users/password/change', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10913', '验证当前用户密码', '/v1/users/password/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10914', '获取当前用户信息', '/v1/users/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10915', '查询用户有代理商权限的应用市场', '/v1/users/reseller/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10916', '发送用户更改邮箱邮件', '/v1/users/resetEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10917', '查询用户绑定的设备厂商列表', '/v1/users/terminal/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10918', '查询用户绑定的设备所属的应用市场列表', '/v1/users/terminal/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10919', '查询用户绑定的设备商户列表', '/v1/users/terminal/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10920', '查询用户绑定的设备机型列表', '/v1/users/terminal/models', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10921', '查询用户设备列表', '/v1/users/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10922', '获取用户设备信息', '/v1/users/terminals/{terminalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10923', '获取用户终端已安装应用详细信息', '/v1/users/terminals/{terminalId}/apks/{apkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10924', '获取用户终端已安装应用列表', '/v1/users/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10925', '用户同意协议', '/v1/users/user/agreement', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10926', '获取用户信息', '/v1/users/{userId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10927', '全局市场激活用户', '/v1/users/{userId}/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10928', '更改邮箱', '/v1/users/{userId}/changeEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10929', '全局市场停用用户', '/v1/users/{userId}/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10930', '用户更改密码', '/v1/users/{userId}/resetPassword', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10931', '移除用户所有角色', '/v1/users/{userId}/roles', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10932', '移除用户角色', '/v1/users/{userId}/roles/{roleId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10933', 'Update push server list', '/v1/vas/cloudmsg/pushServer', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10934', 'Notify terminal online/offline', '/v1/vas/cloudmsg/terminalOnlineStatus', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10935', 'Batch Notify terminal online/offline', '/v1/vas/cloudmsg/terminalOnlineStatus/batch', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10936', 'Search app list by user market admin role', '/v1/vas/insight/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10937', 'Search app list by user developer role', '/v1/vas/insight/apps/develop', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10938', 'allow app sync bizData', '/v1/vas/insight/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10939', 'control app sync bizData', '/v1/vas/insight/apps/{appId}/bizdata/control', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10940', 'Search market page by app', '/v1/vas/insight/apps/{appId}/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10941', 'Search market page by user', '/v1/vas/insight/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10942', 'Search merchant page by user', '/v1/vas/insight/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10943', 'Search reseller page by user', '/v1/vas/insight/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10944', 'Search market', '/v1/vas/insight/sync/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10945', 'Search merchant', '/v1/vas/insight/sync/merchants', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10946', 'Search reseller', '/v1/vas/insight/sync/resellers', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10947', 'Search terminal', '/v1/vas/insight/sync/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10948', 'Search terminal page by user', '/v1/vas/insight/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10949', 'Get app(self&subscribe) market user list', '/v1/vas/insight/users/dataset/app/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10950', 'Get user detail info', '/v1/vas/insight/users/detail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10951', 'Get market admin user list', '/v1/vas/insight/users/{marketId}/admin', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10952', 'Enable service callback', '/v1/vas/platform/service', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10953', 'Disable service callback', '/v1/vas/platform/service', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10954', 'enableVasGlobally', '/v1/vas/platform/service/global', 'PUT', null, '0', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '10082'),
  ('1', '10083'),
  ('1', '10084'),
  ('1', '10085'),
  ('1', '10207'),
  ('1', '10636'),
  ('1', '10833'),
  ('1', '10859'),
  ('1', '10878'),
  ('1', '10879'),
  ('1', '10880'),
  ('1', '10881'),
  ('1', '10882'),
  ('1', '10883'),
  ('1', '10884'),
  ('1', '10885'),
  ('1', '10886'),
  ('1', '10887'),
  ('1', '10888'),
  ('1', '10889'),
  ('1', '10890'),
  ('1', '10891'),
  ('1', '10892'),
  ('1', '10893'),
  ('1', '10894'),
  ('1', '10895'),
  ('1', '10896'),
  ('1', '10897'),
  ('1', '10898'),
  ('1', '10899'),
  ('1', '10900'),
  ('1', '10901'),
  ('1', '10902'),
  ('1', '10903'),
  ('1', '10904'),
  ('1', '10905'),
  ('1', '10906'),
  ('1', '10907'),
  ('1', '10908'),
  ('1', '10909'),
  ('1', '10910'),
  ('1', '10911'),
  ('1', '10912'),
  ('1', '10913'),
  ('1', '10914'),
  ('1', '10915'),
  ('1', '10916'),
  ('1', '10917'),
  ('1', '10918'),
  ('1', '10919'),
  ('1', '10920'),
  ('1', '10921'),
  ('1', '10922'),
  ('1', '10923'),
  ('1', '10924'),
  ('1', '10925'),
  ('1', '10930'),

  ('2', '10207'),
  ('2', '10306'),
  ('2', '10307'),
  ('2', '10308'),
  ('2', '10309'),
  ('2', '10310'),
  ('2', '10311'),
  ('2', '10312'),
  ('2', '10313'),
  ('2', '10314'),
  ('2', '10315'),
  ('2', '10316'),
  ('2', '10317'),
  ('2', '10318'),
  ('2', '10319'),
  ('2', '10320'),
  ('2', '10321'),
  ('2', '10322'),
  ('2', '10323'),
  ('2', '10324'),
  ('2', '10325'),
  ('2', '10326'),
  ('2', '10327'),
  ('2', '10328'),
  ('2', '10329'),
  ('2', '10330'),
  ('2', '10331'),
  ('2', '10332'),
  ('2', '10333'),
  ('2', '10334'),
  ('2', '10335'),
  ('2', '10336'),
  ('2', '10337'),
  ('2', '10338'),
  ('2', '10339'),
  ('2', '10340'),
  ('2', '10341'),
  ('2', '10342'),
  ('2', '10343'),
  ('2', '10344'),
  ('2', '10345'),
  ('2', '10346'),
  ('2', '10347'),
  ('2', '10348'),
  ('2', '10349'),
  ('2', '10350'),
  ('2', '10351'),
  ('2', '10352'),
  ('2', '10353'),
  ('2', '10354'),
  ('2', '10355'),
  ('2', '10363'),
  ('2', '10485'),
  ('2', '10926'),

  ('3', '10171'),
  ('3', '10470'),
  ('3', '10471'),
  ('3', '10472'),
  ('3', '10473'),
  ('3', '10474'),
  ('3', '10475'),
  ('3', '10476'),
  ('3', '10477'),
  ('3', '10478'),
  ('3', '10479'),
  ('3', '10480'),
  ('3', '10481'),
  ('3', '10482'),
  ('3', '10483'),
  ('3', '10484'),
  ('3', '10509'),
  ('3', '10510'),
  ('3', '10511'),
  ('3', '10512'),
  ('3', '10513'),
  ('3', '10514'),
  ('3', '10515'),
  ('3', '10516'),
  ('3', '10577'),
  ('3', '10578'),
  ('3', '10579'),
  ('3', '10580'),
  ('3', '10581'),
  ('3', '10582'),
  ('3', '10583'),
  ('3', '10584'),
  ('3', '10593'),
  ('3', '10594'),
  ('3', '10595'),
  ('3', '10623'),
  ('3', '10624'),
  ('3', '10625'),
  ('3', '10626'),
  ('3', '10627'),
  ('3', '10628'),
  ('3', '10629'),
  ('3', '10630'),

  ('4', '10002'),
  ('4', '10003'),
  ('4', '10004'),
  ('4', '10005'),
  ('4', '10006'),
  ('4', '10007'),
  ('4', '10637'),
  ('4', '10638'),
  ('4', '10639'),
  ('4', '10640'),
  ('4', '10641'),
  ('4', '10642'),
  ('4', '10643'),
  ('4', '10644'),
  ('4', '10645'),
  ('4', '10646'),
  ('4', '10647'),
  ('4', '10648'),
  ('4', '10649'),
  ('4', '10650'),
  ('4', '10651'),
  ('4', '10652'),
  ('4', '10653'),
  ('4', '10654'),
  ('4', '10655'),
  ('4', '10656'),
  ('4', '10657'),
  ('4', '10658'),
  ('4', '10659'),
  ('4', '10660'),
  ('4', '10661'),
  ('4', '10662'),
  ('4', '10663'),
  ('4', '10664'),
  ('4', '10665'),
  ('4', '10666'),
  ('4', '10667'),
  ('4', '10668'),
  ('4', '10669'),
  ('4', '10671'),
  ('4', '10672'),
  ('4', '10673'),
  ('4', '10674'),
  ('4', '10675'),
  ('4', '10676'),
  ('4', '10677'),
  ('4', '10678'),
  ('4', '10679'),
  ('4', '10680'),
  ('4', '10681'),
  ('4', '10682'),
  ('4', '10683'),
  ('4', '10684'),
  ('4', '10685'),
  ('4', '10686'),
  ('4', '10691'),
  ('4', '10692'),
  ('4', '10693'),
  ('4', '10694'),
  ('4', '10695'),
  ('4', '10696'),
  ('4', '10697'),
  ('4', '10698'),
  ('4', '10699'),
  ('4', '10700'),
  ('4', '10701'),
  ('4', '10702'),
  ('4', '10703'),
  ('4', '10704'),
  ('4', '10705'),
  ('4', '10706'),
  ('4', '10707'),
  ('4', '10708'),
  ('4', '10709'),
  ('4', '10710'),
  ('4', '10711'),
  ('4', '10712'),

  ('30', '10470'),
  ('30', '10471'),
  ('30', '10472'),
  ('30', '10473'),
  ('30', '10474'),
  ('30', '10475'),
  ('30', '10476'),
  ('30', '10477'),
  ('30', '10478'),
  ('30', '10479'),
  ('30', '10480'),
  ('30', '10481'),
  ('30', '10482'),
  ('30', '10483'),
  ('30', '10484'),

  ('53', '10170'),
  ('53', '10172'),
  ('53', '10669'),
  ('53', '10821'),
  ('53', '10845'),
  ('53', '10854'),
  ('53', '10859'),
  ('53', '10864'),

  ('601', '10087'),
  ('601', '10088'),
  ('601', '10094'),
  ('601', '10097'),
  ('601', '10101'),
  ('601', '10107'),
  ('601', '10117'),
  ('601', '10118'),
  ('601', '10119'),
  ('601', '10122'),
  ('601', '10161'),
  ('601', '10164'),
  ('601', '10165'),
  ('601', '10363'),
  ('601', '10365'),
  ('601', '10382'),
  ('601', '10441'),
  ('601', '10559'),
  ('601', '10562'),

  ('602', '10087'),
  ('602', '10088'),
  ('602', '10090'),
  ('602', '10094'),
  ('602', '10097'),
  ('602', '10101'),
  ('602', '10107'),
  ('602', '10108'),
  ('602', '10109'),
  ('602', '10117'),
  ('602', '10118'),
  ('602', '10119'),
  ('602', '10120'),
  ('602', '10122'),
  ('602', '10124'),
  ('602', '10125'),
  ('602', '10161'),
  ('602', '10162'),
  ('602', '10163'),
  ('602', '10164'),
  ('602', '10165'),
  ('602', '10166'),
  ('602', '10167'),
  ('602', '10168'),
  ('602', '10169'),
  ('602', '10363'),
  ('602', '10365'),
  ('602', '10382'),
  ('602', '10441'),
  ('602', '10559'),
  ('602', '10562'),

  ('611', '10086'),
  ('611', '10087'),
  ('611', '10088'),
  ('611', '10091'),
  ('611', '10094'),
  ('611', '10097'),
  ('611', '10101'),
  ('611', '10107'),
  ('611', '10115'),
  ('611', '10116'),
  ('611', '10117'),
  ('611', '10118'),
  ('611', '10119'),
  ('611', '10122'),
  ('611', '10365'),
  ('611', '10423'),
  ('611', '10441'),
  ('611', '10485'),
  ('611', '10559'),
  ('611', '10562'),

  ('612', '10086'),
  ('612', '10087'),
  ('612', '10088'),
  ('612', '10089'),
  ('612', '10090'),
  ('612', '10091'),
  ('612', '10092'),
  ('612', '10093'),
  ('612', '10094'),
  ('612', '10095'),
  ('612', '10096'),
  ('612', '10097'),
  ('612', '10098'),
  ('612', '10099'),
  ('612', '10100'),
  ('612', '10101'),
  ('612', '10102'),
  ('612', '10103'),
  ('612', '10104'),
  ('612', '10105'),
  ('612', '10106'),
  ('612', '10107'),
  ('612', '10108'),
  ('612', '10109'),
  ('612', '10110'),
  ('612', '10111'),
  ('612', '10112'),
  ('612', '10113'),
  ('612', '10114'),
  ('612', '10115'),
  ('612', '10116'),
  ('612', '10117'),
  ('612', '10118'),
  ('612', '10119'),
  ('612', '10120'),
  ('612', '10121'),
  ('612', '10122'),
  ('612', '10123'),
  ('612', '10124'),
  ('612', '10125'),
  ('612', '10126'),
  ('612', '10365'),
  ('612', '10423'),
  ('612', '10441'),
  ('612', '10485'),
  ('612', '10559'),
  ('612', '10562'),

  ('661', '10165'),
  ('661', '10365'),
  ('661', '10376'),
  ('661', '10379'),
  ('661', '10382'),
  ('661', '10423'),
  ('661', '10559'),
  ('661', '10562'),

  ('662', '10165'),
  ('662', '10166'),
  ('662', '10365'),
  ('662', '10376'),
  ('662', '10377'),
  ('662', '10378'),
  ('662', '10379'),
  ('662', '10380'),
  ('662', '10381'),
  ('662', '10382'),
  ('662', '10383'),
  ('662', '10384'),
  ('662', '10385'),
  ('662', '10386'),
  ('662', '10387'),
  ('662', '10388'),
  ('662', '10389'),
  ('662', '10390'),
  ('662', '10423'),
  ('662', '10559'),
  ('662', '10562'),

  ('621', '10086'),
  ('621', '10150'),
  ('621', '10151'),

  ('622', '10086'),
  ('622', '10150'),
  ('622', '10151'),
  ('622', '10152'),
  ('622', '10153'),
  ('622', '10154'),
  ('622', '10155'),
  ('622', '10156'),
  ('622', '10157'),
  ('622', '10158'),
  ('622', '10159'),
  ('622', '10160'),

  ('651', '10086'),
  ('651', '10087'),
  ('651', '10088'),
  ('651', '10091'),
  ('651', '10094'),
  ('651', '10097'),
  ('651', '10101'),
  ('651', '10107'),
  ('651', '10108'),
  ('651', '10109'),
  ('651', '10115'),
  ('651', '10116'),
  ('651', '10117'),
  ('651', '10118'),
  ('651', '10119'),
  ('651', '10122'),
  ('651', '10124'),
  ('651', '10125'),
  ('651', '10365'),
  ('651', '10423'),
  ('651', '10559'),
  ('651', '10561'),
  ('651', '10562'),

  ('652', '10086'),
  ('652', '10087'),
  ('652', '10088'),
  ('652', '10091'),
  ('652', '10094'),
  ('652', '10097'),
  ('652', '10101'),
  ('652', '10107'),
  ('652', '10108'),
  ('652', '10109'),
  ('652', '10115'),
  ('652', '10116'),
  ('652', '10117'),
  ('652', '10118'),
  ('652', '10119'),
  ('652', '10122'),
  ('652', '10124'),
  ('652', '10125'),
  ('652', '10365'),
  ('652', '10423'),
  ('652', '10559'),
  ('652', '10561'),
  ('652', '10562'),

  ('711', '10087'),
  ('711', '10088'),
  ('711', '10094'),
  ('711', '10101'),
  ('711', '10107'),
  ('711', '10119'),
  ('711', '10122'),
  ('711', '10181'),
  ('711', '10252'),
  ('711', '10262'),
  ('711', '10264'),
  ('711', '10265'),
  ('711', '10266'),
  ('711', '10269'),
  ('711', '10276'),
  ('711', '10363'),
  ('711', '10365'),
  ('711', '10376'),
  ('711', '10382'),
  ('711', '10441'),
  ('711', '10446'),
  ('711', '10451'),
  ('711', '10452'),
  ('711', '10455'),
  ('711', '10457'),
  ('711', '10460'),
  ('711', '10466'),
  ('711', '10485'),
  ('711', '10491'),
  ('711', '10559'),
  ('711', '10561'),
  ('711', '10562'),
  ('711', '10563'),
  ('711', '10568'),
  ('711', '10570'),
  ('711', '10574'),
  ('711', '10636'),
  ('711', '10713'),
  ('711', '10715'),
  ('711', '10718'),
  ('711', '10720'),
  ('711', '10724'),
  ('711', '10726'),
  ('711', '10735'),
  ('711', '10740'),
  ('711', '10743'),
  ('711', '10745'),
  ('711', '10746'),
  ('711', '10750'),
  ('711', '10751'),
  ('711', '10754'),
  ('711', '10760'),
  ('711', '10761'),
  ('711', '10771'),
  ('711', '10774'),
  ('711', '10780'),
  ('711', '10781'),
  ('711', '10791'),
  ('711', '10792'),
  ('711', '10796'),
  ('711', '10801'),
  ('711', '10804'),
  ('711', '10805'),
  ('711', '10807'),
  ('711', '10812'),
  ('711', '10817'),
  ('711', '10818'),
  ('711', '10821'),
  ('711', '10828'),
  ('711', '10832'),
  ('711', '10833'),
  ('711', '10841'),
  ('711', '10845'),
  ('711', '10849'),
  ('711', '10851'),
  ('711', '10852'),
  ('711', '10853'),
  ('711', '10854'),
  ('711', '10856'),
  ('711', '10857'),
  ('711', '10858'),
  ('711', '10859'),
  ('711', '10860'),
  ('711', '10861'),
  ('711', '10863'),
  ('711', '10864'),
  ('711', '10865'),
  ('711', '10866'),
  ('711', '10867'),
  ('711', '10869'),
  ('711', '10871'),
  ('711', '10873'),
  ('711', '10875'),

  ('712', '10087'),
  ('712', '10088'),
  ('712', '10094'),
  ('712', '10101'),
  ('712', '10107'),
  ('712', '10119'),
  ('712', '10122'),
  ('712', '10181'),
  ('712', '10252'),
  ('712', '10262'),
  ('712', '10263'),
  ('712', '10264'),
  ('712', '10265'),
  ('712', '10266'),
  ('712', '10267'),
  ('712', '10268'),
  ('712', '10269'),
  ('712', '10276'),
  ('712', '10363'),
  ('712', '10365'),
  ('712', '10376'),
  ('712', '10382'),
  ('712', '10441'),
  ('712', '10446'),
  ('712', '10447'),
  ('712', '10448'),
  ('712', '10449'),
  ('712', '10450'),
  ('712', '10451'),
  ('712', '10452'),
  ('712', '10453'),
  ('712', '10454'),
  ('712', '10455'),
  ('712', '10456'),
  ('712', '10457'),
  ('712', '10458'),
  ('712', '10459'),
  ('712', '10460'),
  ('712', '10461'),
  ('712', '10462'),
  ('712', '10463'),
  ('712', '10464'),
  ('712', '10465'),
  ('712', '10466'),
  ('712', '10467'),
  ('712', '10468'),
  ('712', '10469'),
  ('712', '10485'),
  ('712', '10491'),
  ('712', '10559'),
  ('712', '10560'),
  ('712', '10561'),
  ('712', '10562'),
  ('712', '10563'),
  ('712', '10564'),
  ('712', '10565'),
  ('712', '10566'),
  ('712', '10567'),
  ('712', '10568'),
  ('712', '10569'),
  ('712', '10570'),
  ('712', '10571'),
  ('712', '10572'),
  ('712', '10573'),
  ('712', '10574'),
  ('712', '10575'),
  ('712', '10576'),
  ('712', '10636'),
  ('712', '10713'),
  ('712', '10714'),
  ('712', '10715'),
  ('712', '10716'),
  ('712', '10717'),
  ('712', '10718'),
  ('712', '10719'),
  ('712', '10720'),
  ('712', '10721'),
  ('712', '10722'),
  ('712', '10723'),
  ('712', '10724'),
  ('712', '10725'),
  ('712', '10726'),
  ('712', '10727'),
  ('712', '10728'),
  ('712', '10729'),
  ('712', '10730'),
  ('712', '10735'),
  ('712', '10740'),
  ('712', '10743'),
  ('712', '10745'),
  ('712', '10746'),
  ('712', '10750'),
  ('712', '10751'),
  ('712', '10754'),
  ('712', '10760'),
  ('712', '10761'),
  ('712', '10771'),
  ('712', '10774'),
  ('712', '10780'),
  ('712', '10781'),
  ('712', '10791'),
  ('712', '10792'),
  ('712', '10793'),
  ('712', '10794'),
  ('712', '10795'),
  ('712', '10796'),
  ('712', '10797'),
  ('712', '10798'),
  ('712', '10799'),
  ('712', '10800'),
  ('712', '10801'),
  ('712', '10802'),
  ('712', '10803'),
  ('712', '10804'),
  ('712', '10805'),
  ('712', '10806'),
  ('712', '10807'),
  ('712', '10808'),
  ('712', '10809'),
  ('712', '10810'),
  ('712', '10811'),
  ('712', '10812'),
  ('712', '10813'),
  ('712', '10814'),
  ('712', '10815'),
  ('712', '10816'),
  ('712', '10817'),
  ('712', '10818'),
  ('712', '10819'),
  ('712', '10820'),
  ('712', '10821'),
  ('712', '10822'),
  ('712', '10823'),
  ('712', '10824'),
  ('712', '10825'),
  ('712', '10826'),
  ('712', '10827'),
  ('712', '10828'),
  ('712', '10829'),
  ('712', '10830'),
  ('712', '10831'),
  ('712', '10832'),
  ('712', '10833'),
  ('712', '10841'),
  ('712', '10845'),
  ('712', '10846'),
  ('712', '10847'),
  ('712', '10848'),
  ('712', '10849'),
  ('712', '10850'),
  ('712', '10851'),
  ('712', '10852'),
  ('712', '10853'),
  ('712', '10854'),
  ('712', '10855'),
  ('712', '10856'),
  ('712', '10857'),
  ('712', '10858'),
  ('712', '10859'),
  ('712', '10860'),
  ('712', '10861'),
  ('712', '10862'),
  ('712', '10863'),
  ('712', '10864'),
  ('712', '10865'),
  ('712', '10866'),
  ('712', '10867'),
  ('712', '10868'),
  ('712', '10869'),
  ('712', '10870'),
  ('712', '10871'),
  ('712', '10872'),
  ('712', '10873'),
  ('712', '10874'),
  ('712', '10875'),
  ('712', '10876'),

  ('721', '10087'),
  ('721', '10088'),
  ('721', '10242'),
  ('721', '10252'),
  ('721', '10262'),
  ('721', '10264'),
  ('721', '10265'),
  ('721', '10266'),
  ('721', '10269'),
  ('721', '10363'),
  ('721', '10365'),
  ('721', '10376'),
  ('721', '10382'),
  ('721', '10455'),
  ('721', '10485'),
  ('721', '10491'),
  ('721', '10559'),
  ('721', '10562'),
  ('721', '10731'),
  ('721', '10735'),
  ('721', '10740'),
  ('721', '10743'),
  ('721', '10745'),
  ('721', '10746'),
  ('721', '10750'),
  ('721', '10751'),
  ('721', '10752'),
  ('721', '10754'),
  ('721', '10760'),
  ('721', '10761'),
  ('721', '10762'),
  ('721', '10764'),
  ('721', '10770'),
  ('721', '10771'),
  ('721', '10772'),
  ('721', '10774'),
  ('721', '10780'),
  ('721', '10781'),
  ('721', '10782'),
  ('721', '10784'),
  ('721', '10790'),
  ('721', '10791'),
  ('721', '10792'),
  ('721', '10796'),
  ('721', '10801'),
  ('721', '10804'),
  ('721', '10821'),

  ('722', '10087'),
  ('722', '10088'),
  ('722', '10242'),
  ('722', '10252'),
  ('722', '10262'),
  ('722', '10263'),
  ('722', '10264'),
  ('722', '10265'),
  ('722', '10266'),
  ('722', '10267'),
  ('722', '10268'),
  ('722', '10269'),
  ('722', '10363'),
  ('722', '10365'),
  ('722', '10376'),
  ('722', '10382'),
  ('722', '10455'),
  ('722', '10485'),
  ('722', '10491'),
  ('722', '10559'),
  ('722', '10562'),
  ('722', '10731'),
  ('722', '10732'),
  ('722', '10733'),
  ('722', '10734'),
  ('722', '10735'),
  ('722', '10736'),
  ('722', '10737'),
  ('722', '10738'),
  ('722', '10739'),
  ('722', '10740'),
  ('722', '10741'),
  ('722', '10742'),
  ('722', '10743'),
  ('722', '10744'),
  ('722', '10745'),
  ('722', '10746'),
  ('722', '10747'),
  ('722', '10748'),
  ('722', '10749'),
  ('722', '10750'),
  ('722', '10751'),
  ('722', '10752'),
  ('722', '10753'),
  ('722', '10754'),
  ('722', '10755'),
  ('722', '10756'),
  ('722', '10757'),
  ('722', '10758'),
  ('722', '10759'),
  ('722', '10760'),
  ('722', '10761'),
  ('722', '10762'),
  ('722', '10763'),
  ('722', '10764'),
  ('722', '10765'),
  ('722', '10766'),
  ('722', '10767'),
  ('722', '10768'),
  ('722', '10769'),
  ('722', '10770'),
  ('722', '10771'),
  ('722', '10772'),
  ('722', '10773'),
  ('722', '10774'),
  ('722', '10775'),
  ('722', '10776'),
  ('722', '10777'),
  ('722', '10778'),
  ('722', '10779'),
  ('722', '10780'),
  ('722', '10781'),
  ('722', '10782'),
  ('722', '10783'),
  ('722', '10784'),
  ('722', '10785'),
  ('722', '10786'),
  ('722', '10787'),
  ('722', '10788'),
  ('722', '10789'),
  ('722', '10790'),
  ('722', '10791'),
  ('722', '10792'),
  ('722', '10793'),
  ('722', '10794'),
  ('722', '10795'),
  ('722', '10796'),
  ('722', '10797'),
  ('722', '10798'),
  ('722', '10799'),
  ('722', '10800'),
  ('722', '10801'),
  ('722', '10802'),
  ('722', '10803'),
  ('722', '10804'),
  ('722', '10821'),

  ('731', '10087'),
  ('731', '10088'),
  ('731', '10094'),
  ('731', '10101'),
  ('731', '10107'),
  ('731', '10119'),
  ('731', '10122'),
  ('731', '10262'),
  ('731', '10264'),
  ('731', '10265'),
  ('731', '10266'),
  ('731', '10269'),
  ('731', '10363'),
  ('731', '10365'),
  ('731', '10485'),
  ('731', '10559'),
  ('731', '10562'),

  ('732', '10087'),
  ('732', '10088'),
  ('732', '10094'),
  ('732', '10101'),
  ('732', '10107'),
  ('732', '10119'),
  ('732', '10122'),
  ('732', '10262'),
  ('732', '10263'),
  ('732', '10264'),
  ('732', '10265'),
  ('732', '10266'),
  ('732', '10267'),
  ('732', '10268'),
  ('732', '10269'),
  ('732', '10363'),
  ('732', '10365'),
  ('732', '10485'),
  ('732', '10559'),
  ('732', '10562'),

  ('7411', '10363'),
  ('7411', '10365'),
  ('7411', '10366'),

  ('7412', '10363'),
  ('7412', '10364'),
  ('7412', '10365'),
  ('7412', '10366'),
  ('7412', '10367'),
  ('7412', '10368'),
  ('7412', '10369'),
  ('7412', '10370'),

  ('7421', '10363'),
  ('7421', '10485'),
  ('7421', '10487'),
  ('7421', '10491'),

  ('7422', '10363'),
  ('7422', '10485'),
  ('7422', '10486'),
  ('7422', '10487'),
  ('7422', '10488'),
  ('7422', '10489'),
  ('7422', '10490'),
  ('7422', '10491'),
  ('7422', '10492'),

  ('7511', '10363'),
  ('7511', '10365'),
  ('7511', '10376'),
  ('7511', '10379'),
  ('7511', '10382'),

  ('7512', '10363'),
  ('7512', '10365'),
  ('7512', '10376'),
  ('7512', '10377'),
  ('7512', '10378'),
  ('7512', '10379'),
  ('7512', '10380'),
  ('7512', '10381'),
  ('7512', '10382'),
  ('7512', '10383'),
  ('7512', '10384'),
  ('7512', '10386'),
  ('7512', '10390'),

  ('7521', '10161'),
  ('7521', '10164'),
  ('7521', '10165'),
  ('7521', '10363'),
  ('7521', '10365'),
  ('7521', '10376'),
  ('7521', '10379'),
  ('7521', '10382'),
  ('7521', '10423'),
  ('7521', '10559'),
  ('7521', '10562'),

  ('7522', '10161'),
  ('7522', '10162'),
  ('7522', '10163'),
  ('7522', '10164'),
  ('7522', '10165'),
  ('7522', '10166'),
  ('7522', '10167'),
  ('7522', '10168'),
  ('7522', '10169'),
  ('7522', '10363'),
  ('7522', '10365'),
  ('7522', '10376'),
  ('7522', '10379'),
  ('7522', '10381'),
  ('7522', '10382'),
  ('7522', '10384'),
  ('7522', '10385'),
  ('7522', '10386'),
  ('7522', '10387'),
  ('7522', '10388'),
  ('7522', '10389'),
  ('7522', '10423'),
  ('7522', '10559'),
  ('7522', '10562'),

  ('7611', '10363'),
  ('7611', '10485'),
  ('7611', '10601'),
  ('7611', '10605'),
  ('7611', '10607'),
  ('7611', '10610'),
  ('7611', '10614'),

  ('7612', '10363'),
  ('7612', '10485'),
  ('7612', '10601'),
  ('7612', '10605'),
  ('7612', '10606'),
  ('7612', '10607'),
  ('7612', '10608'),
  ('7612', '10609'),
  ('7612', '10610'),
  ('7612', '10611'),
  ('7612', '10612'),
  ('7612', '10614'),
  ('7612', '10615'),
  ('7612', '10619'),

  ('7621', '10423'),
  ('7621', '10602'),
  ('7621', '10604'),
  ('7621', '10605'),
  ('7621', '10614'),

  ('7622', '10169'),
  ('7622', '10423'),
  ('7622', '10602'),
  ('7622', '10604'),
  ('7622', '10605'),
  ('7622', '10612'),
  ('7622', '10613'),
  ('7622', '10614'),
  ('7622', '10616'),
  ('7622', '10617'),
  ('7622', '10618'),

  ('771', '10363'),
  ('771', '10365'),
  ('771', '10423'),
  ('771', '10485'),
  ('771', '10559'),
  ('771', '10562'),
  ('771', '10834'),
  ('771', '10838'),
  ('771', '10841'),
  ('771', '10842'),

  ('772', '10363'),
  ('772', '10365'),
  ('772', '10423'),
  ('772', '10485'),
  ('772', '10559'),
  ('772', '10562'),
  ('772', '10834'),
  ('772', '10835'),
  ('772', '10836'),
  ('772', '10837'),
  ('772', '10838'),
  ('772', '10839'),
  ('772', '10840'),
  ('772', '10841'),
  ('772', '10842'),
  ('772', '10843'),
  ('772', '10844'),

  ('911', '10203'),
  ('911', '10205'),
  ('911', '10207'),
  ('911', '10212'),
  ('911', '10214'),
  ('911', '10219'),
  ('911', '10220'),
  ('911', '10240'),
  ('911', '10242'),
  ('911', '10248'),
  ('911', '10251'),
  ('911', '10252'),
  ('911', '10276'),
  ('911', '10278'),
  ('911', '10418'),
  ('911', '10441'),
  ('911', '10563'),
  ('911', '10574'),

  ('912', '10178'),
  ('912', '10198'),
  ('912', '10199'),
  ('912', '10200'),
  ('912', '10201'),
  ('912', '10202'),
  ('912', '10203'),
  ('912', '10204'),
  ('912', '10205'),
  ('912', '10206'),
  ('912', '10207'),
  ('912', '10212'),
  ('912', '10213'),
  ('912', '10214'),
  ('912', '10215'),
  ('912', '10216'),
  ('912', '10217'),
  ('912', '10218'),
  ('912', '10219'),
  ('912', '10220'),
  ('912', '10221'),
  ('912', '10222'),
  ('912', '10240'),
  ('912', '10242'),
  ('912', '10248'),
  ('912', '10249'),
  ('912', '10250'),
  ('912', '10251'),
  ('912', '10252'),
  ('912', '10253'),
  ('912', '10276'),
  ('912', '10277'),
  ('912', '10278'),
  ('912', '10279'),
  ('912', '10280'),
  ('912', '10281'),
  ('912', '10418'),
  ('912', '10441'),
  ('912', '10442'),
  ('912', '10443'),
  ('912', '10444'),
  ('912', '10445'),
  ('912', '10563'),
  ('912', '10574'),
  ('912', '10575'),
  ('912', '10576'),

  ('92', '10502'),
  ('92', '10503'),
  ('92', '10504'),
  ('92', '10505'),
  ('92', '10506'),
  ('92', '10507'),
  ('92', '10508'),
  ('92', '10562'),
  ('92', '10585'),
  ('92', '10586'),
  ('92', '10587'),
  ('92', '10588'),
  ('92', '10589'),
  ('92', '10590'),
  ('92', '10591'),
  ('92', '10592'),
  ('92', '10596'),
  ('92', '10597'),
  ('92', '10598'),

  ('931', '10423'),
  ('931', '10587'),
  ('931', '10877'),
  ('931', '10885'),
  ('931', '10926'),
  ('931', '10928'),
  ('931', '10930'),

  ('932', '10423'),
  ('932', '10587'),
  ('932', '10877'),
  ('932', '10885'),
  ('932', '10926'),
  ('932', '10927'),
  ('932', '10928'),
  ('932', '10929'),
  ('932', '10930'),
  ('932', '10931'),
  ('932', '10932'),

  ('94', '10552'),
  ('94', '10553'),
  ('94', '10554'),
  ('94', '10555'),
  ('94', '10556'),
  ('94', '10557'),
  ('94', '10558'),

  ('95', '10179'),
  ('95', '10180'),
  ('95', '10181'),
  ('95', '10182'),
  ('95', '10183'),

  ('96', '10264'),
  ('96', '10363'),
  ('96', '10365'),
  ('96', '10455'),
  ('96', '10485'),
  ('96', '10534'),
  ('96', '10535'),
  ('96', '10536'),
  ('96', '10537'),
  ('96', '10538'),
  ('96', '10539'),
  ('96', '10540'),
  ('96', '10541'),
  ('96', '10542'),
  ('96', '10543'),
  ('96', '10544'),
  ('96', '10545'),
  ('96', '10546'),
  ('96', '10547'),
  ('96', '10548'),
  ('96', '10549'),
  ('96', '10550'),
  ('96', '10551'),
  ('96', '10559'),
  ('96', '10562'),

  ('97', '10226'),
  ('97', '10227'),
  ('97', '10228'),
  ('97', '10229'),
  ('97', '10230'),
  ('97', '10231'),
  ('97', '10232'),
  ('97', '10233'),
  ('97', '10234'),
  ('97', '10235'),
  ('97', '10236'),
  ('97', '10237'),
  ('97', '10238'),
  ('97', '10535'),

  ('1011', '10240'),
  ('1011', '10418'),
  ('1011', '10423'),
  ('1011', '10425'),
  ('1011', '10426'),
  ('1011', '10427'),
  ('1011', '10428'),
  ('1011', '10429'),
  ('1011', '10430'),
  ('1011', '10432'),

  ('1012', '10240'),
  ('1012', '10418'),
  ('1012', '10423'),
  ('1012', '10424'),
  ('1012', '10425'),
  ('1012', '10426'),
  ('1012', '10427'),
  ('1012', '10428'),
  ('1012', '10429'),
  ('1012', '10430'),
  ('1012', '10431'),
  ('1012', '10432'),
  ('1012', '10433'),
  ('1012', '10434'),
  ('1012', '10435'),
  ('1012', '10436'),
  ('1012', '10437'),
  ('1012', '10438'),
  ('1012', '10439'),
  ('1012', '10440'),

  ('1021', '10240'),
  ('1021', '10242'),
  ('1021', '10245'),
  ('1021', '10300'),
  ('1021', '10301'),
  ('1021', '10303'),
  ('1021', '10418'),
  ('1021', '10631'),
  ('1021', '10633'),

  ('1022', '10239'),
  ('1022', '10240'),
  ('1022', '10241'),
  ('1022', '10242'),
  ('1022', '10243'),
  ('1022', '10244'),
  ('1022', '10245'),
  ('1022', '10246'),
  ('1022', '10247'),
  ('1022', '10300'),
  ('1022', '10301'),
  ('1022', '10302'),
  ('1022', '10303'),
  ('1022', '10304'),
  ('1022', '10418'),
  ('1022', '10419'),
  ('1022', '10631'),
  ('1022', '10632'),
  ('1022', '10633'),
  ('1022', '10634'),

  ('981', '10420'),

  ('982', '10420'),
  ('982', '10421'),
  ('982', '10422');
