DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'Create push message to terminal request', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10001', 'Create push message to single terminal request', '/v1/3rd/cloudmsg/single', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10002', 'Get arrive rate of message.', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10003', 'Submit Apk', '/v1/3rd/developer/apk/upload', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10004', 'RKI服务器回调方法', '/v1/3rd/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10005', 'Find apk parameter list', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10006', 'Create Apk Parameter', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10007', 'Get apk parameter details', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10008', 'Update Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10009', 'Delete Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10010', 'Search Application', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10011', 'Search entity attributes', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10012', 'Create an Entity Attribute', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10013', 'Get entity attribute by id', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'Update Entity Attribute by Id', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10015', 'Delete entity attribute by id', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'Update entity attribute label', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'Verify estate', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'Search app business data from GoInsight', '/v1/3rdsys/goInsight/data/app-biz', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'Get merchant category list', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'Create a single merchant category', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'Batch create merchant categories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'Update merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'Delete merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'Get merchant list by search criterias', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10025', 'Create a merchant', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'Get merchant by id', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'Update merchant', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'Delete a merchant', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'Activate merchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10030', 'Disable a merchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'Replace merchant email', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'Find parameter push history by page', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'Find resellers', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'Create a reseller', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'Get reseller', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'Update a reseller', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'Delete a reseller', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'Activate a reseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'Disable a reseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'Replace reseller email', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10041', 'Find reseller RKI KEY template', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10042', 'Search push app list by terminal', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10043', 'Push app (with parameter if parameter app) to terminal', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10044', 'Suspend terminal push app', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10045', 'Uninstall terminal app', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10046', 'Get Terminal Apk', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10047', 'Search push firmware list by terminal', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10048', 'Push firmware to terminal', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10049', 'Cancel push firmware task', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10050', 'Get push firmware history by id', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10051', 'Search terminal group push application list', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10052', 'Push app (with parameter if parameter app) to terminal group', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10053', 'Get terminal group push apk', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10054', 'Delete terminal group push task', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10055', 'Suspend terminal group push task by id', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10056', 'Search push RKI Key task list by group', '/v1/3rdsys/terminalGroupRki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10057', 'Push RKI Key to group', '/v1/3rdsys/terminalGroupRki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10058', 'Get Terminal Group RKI Key push task detail', '/v1/3rdsys/terminalGroupRki/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10059', 'Suspend group push rki task', '/v1/3rdsys/terminalGroupRki/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10060', 'Get terminal group list', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10061', 'Create terminal group', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10062', 'Search terminals', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10063', 'Get terminal group by id', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10064', 'Update terminal group', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10065', 'Delete terminal group by id', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10066', 'Activate terminal group by id', '/v1/3rdsys/terminalGroups/{groupId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10067', 'Disable terminal group', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10068', 'Search terminals in specified group', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10069', 'Add terminal to group', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10070', 'Remove group terminals', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10071', 'Search push RKI Key task list by terminal', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10072', 'Push RKI Key to terminal', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10073', 'suspend terminal push Rki Key', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10074', 'Get Terminal RKI Key push task detail', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10075', 'Get terminal variable list', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10076', 'Create terminal parameter variables in batch', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10077', 'Batch deletion of terminal variables', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10078', 'Update terminal variable by variable id', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10079', 'Delete terminal variable by variable id', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10080', 'Find terminal list by page', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10081', 'Create terminal', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10082', 'Activate a terminal by query parameter', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10083', 'Batch add terminal to group', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10084', 'Get terminal by id', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10085', 'Update terminal', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10086', 'Delete a terminal', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10087', 'Activate a terminal', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10088', 'Get terminal configuration', '/v1/3rdsys/terminals/{terminalId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10089', 'Update terminal configuration', '/v1/3rdsys/terminals/{terminalId}/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10090', 'Disable a terminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10091', 'Move a terminal', '/v1/3rdsys/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10092', 'Push command to terminal', '/v1/3rdsys/terminals/{terminalId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10093', 'Get terminal PED information by terminal id', '/v1/3rdsys/terminals/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10094', 'Route request to Uptrillion', '/v1/3rdsys/upt/route-request', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10095', 'Set UpTrillion integration API key and secret', '/v1/3rdsys/upt/security', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10096', '获取当前用户信息', '/v1/account/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10097', '获取市场信息，登录和未登录有区别', '/v1/account/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10098', '开发者设置收款帐户', '/v1/account/receivable/developer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10099', '设置应用市场收款帐户', '/v1/account/receivable/market', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10100', '注销删除用户', '/v1/account/user', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10101', '更改密码', '/v1/account/user/change-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10102', '发送删除用户验证码邮件', '/v1/account/user/delete', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10103', 'List all configuration (notification)', '/v1/account/user/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10104', 'Update a configuration (notification)', '/v1/account/user/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10105', 'Publish global notification', '/v1/account/user/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10106', 'List messages (notification)', '/v1/account/user/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10107', 'Update a set of messages as read (notification)', '/v1/account/user/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10108', 'Delete a set of messages (notification)', '/v1/account/user/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10109', 'Update all messages as read (notification)', '/v1/account/user/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10110', 'Delete a message (notification)', '/v1/account/user/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10111', 'Download message attachment (notification)', '/v1/account/user/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10112', 'View message details (notification)', '/v1/account/user/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10113', 'List all subscribed topics (notification)', '/v1/account/user/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10114', 'Unsubscribe all topics (notification)', '/v1/account/user/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10115', 'Get subscription info if subscribed (notification)', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10116', 'Subscribe a topic (notification)', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10117', 'Unsubscribe a topic (notification)', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10118', '获取当前用户OTP', '/v1/account/user/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10119', '启用当前用户OTP', '/v1/account/user/otp/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10120', '关闭当前用户OTP', '/v1/account/user/otp/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10121', '获取当前用户OTP二维码图片', '/v1/account/user/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10122', '重置用户OTP的backupCode', '/v1/account/user/otp/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10123', '获取当前用户信息', '/v1/account/user/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10124', '更新用户信息', '/v1/account/user/profile', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10125', '发送用户更改邮箱邮件', '/v1/account/user/reset-email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10126', '配置是否允许发送使用数据开关', '/v1/account/user/send-usage', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10127', '查询活动列表', '/v1/activities', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10128', '批量删除活动', '/v1/activities/batch/deletion', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10129', '获取活动信息', '/v1/activities/{activityId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10130', '删除活动', '/v1/activities/{activityId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10131', '查询应用分成比例默认设置', '/v1/admin/app/download/fee/defaultSetting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10132', '更新应用分成比例认设置', '/v1/admin/app/download/fee/defaultSetting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10133', '查询子应用市场应用分成比例设置', '/v1/admin/app/download/fee/market/profit/rate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10134', '更新子应用市场应用分成比例设置', '/v1/admin/app/download/fee/market/profit/rate', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10135', '管理员查询应用列表', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10136', '创建管理员Apk下载任务', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10137', '创建管理员下载apk参数模版下载任务', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10138', '管理员删除apk参数模版', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10139', '对于签名失败的apk重新签名', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10140', '管理员获取AppFeatured图片', '/v1/admin/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10141', '管理员更新AppFeatured图片排序', '/v1/admin/apps/featured/{featuredAppId}/sort', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10142', '获取白名单', '/v1/admin/apps/whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10143', '创建白名单', '/v1/admin/apps/whiteList', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10144', '删除白名单', '/v1/admin/apps/whiteList', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10145', '管理员获取开发者应用详细信息', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10146', '删除应用APP', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10147', '应用上线', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10148', '管理员更新APP卸载权限', '/v1/admin/apps/{appId}/allowUninstall/{allowUninstall}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10149', '管理员获取APK列表', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10150', '管理员获取APK详情', '/v1/admin/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10151', '删除应用APK', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10152', '通过应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10153', '管理员下载APK', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10154', '查询应用APK定向发布的应用市场', '/v1/admin/apps/{appId}/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10155', '应用Apk下线', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10156', '应用Apk上线', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10157', '管理员添加参数模板', '/v1/admin/apps/{appId}/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10158', '拒绝应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10159', '管理员更新App ReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/releaseNote', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10160', '查询应用APK定向发布的代理商', '/v1/admin/apps/{appId}/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10161', 'Apk specific reseller or global APK publish to the normal market', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10162', '删除应用APK定向发布', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10163', '管理员更新APK机型', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10164', '管理员更新APP自动更新配置', '/v1/admin/apps/{appId}/autoUpdate/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10165', '更新应用的开发者', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10166', '应用下线', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10167', '更新app下载权限', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10168', '管理员添加AppFeatured图片', '/v1/admin/apps/{appId}/featured', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10169', '管理员删除AppFeatured图片', '/v1/admin/apps/{appId}/featured', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10170', '管理员更新FeaturedApp图片', '/v1/admin/apps/{appId}/featured/{featuredAppId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', 'Admin search insight sandbox app biz data', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', '创建已安装终端列表下载任务', '/v1/admin/apps/{appId}/installedTerminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', '查询应用定向发布的应用市场', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', 'Get app specific merchant categories', '/v1/admin/apps/{appId}/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', 'Update app specific merchant categories', '/v1/admin/apps/{appId}/merchant/categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', '查询应用定向发布的代理商', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', '应用恢复', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', 'App specific reseller or global APP publish to the normal market', '/v1/admin/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10179', '删除应用定向发布', '/v1/admin/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', '更新app可是范围', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', 'Refresh AccessToken via CloudServiceGateway', '/v1/admin/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', 'ping', '/v1/admin/cloudservice/refresh/ping', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', 'Get access url and token via CloudServiceGateway', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10184', '获取应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', '保存应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', '获取Dashboard里终端信息统计部件(W03)的数据', '/v1/admin/dashboard/widgets/W03', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', '获取Dashboard里终端数量部件(W09)的数据', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', '获取Dashboard里代理商商户终端汇总部件(W10)的数据', '/v1/admin/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', '获取Dashboard里代理商级别的操作日志(W11)的数据', '/v1/admin/dashboard/widgets/W11', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', '获取Dashboard里代理商终端汇总部件(W12)的数据', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', '获取Dashboard里 固件版本-终端数量-组织 (W13)的数据', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', '创建Dashboard里 FM-Terminal_Org(W13) 数据下载任务', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', '获取Dashboard里 Client-终端数量-组织 (W14)的数据', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', '创建Dashboard里 Client-终端数量-组织(W14) 数据下载任务', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', '获取Dashboard里 机型-终端数量 (W15)的数据', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', '创建Dashboard里 MODEL-Terminal_Org(W15) 数据下载任务', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', '获取Dashboard 长时间 offline (W16)的数据', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', '创建Dashboard里 offline-terminal(W16) 数据下载任务', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', '获取Dashboard里 FM-Terminal(W18) 数据', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', '创建Dashboard里 FM-Terminal(W18) 数据下载任务', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', '获取Dashboard里 Client-Terminal(W19) 数据', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', '创建Dashboard里 Client-Terminal(W19) 数据下载任务', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', '创建Dashboard里 数字卡片(W20) 数据下载任务', '/v1/admin/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', '获取Dashboard里 数字卡片(W20) 数量', '/v1/admin/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', '获取Dashboard里 数字卡片(W20) 设置', '/v1/admin/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10206', '更新Dashboard里 数字卡片(W20) 显示数据', '/v1/admin/dashboard/widgets/W20/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10207', '获取Dashboard里 PUK数量(W22) 显示数据', '/v1/admin/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10208', '创建Dashboard里 PUK数量(W22) 数据下载任务', '/v1/admin/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', '管理员查询开发者列表', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', '管理员获取开发者详细信息', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', '更新开发者账户', '/v1/admin/developers/{developerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', '删除开发者', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10213', '允许开发者访问第三方开发者API', '/v1/admin/developers/{developerId}/3rdsys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', '禁止开发者访问第三方开发者API', '/v1/admin/developers/{developerId}/3rdsys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10215', '通过开发者审核', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10216', '通过开发者线下付款审核', '/v1/admin/developers/{developerId}/pay/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10217', '拒绝开发者线下付款审核', '/v1/admin/developers/{developerId}/pay/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', '拒绝开发者审核', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10219', '定向发布开发者到代理商', '/v1/admin/developers/{developerId}/reseller', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10220', '更改定向发布开发者的代理商', '/v1/admin/developers/{developerId}/reseller/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10221', '关闭/删除开发者定向发布到代理商', '/v1/admin/developers/{developerId}/reseller/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10222', '恢复开发者帐号', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10223', '管理员获取开发者终端列表', '/v1/admin/developers/{developerId}/sandbox/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10224', '管理员删除开发者终端', '/v1/admin/developers/{developerId}/sandbox/terminal/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10225', '管理员更换企业开发者超级管理员', '/v1/admin/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10226', '停用开发者帐号', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10227', '激活开发者用户', '/v1/admin/developers/{developerId}/user/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10228', '管理员获取企业开发者列表信息', '/v1/admin/developers/{developerId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10229', '获取Global应用市场应用列表', '/v1/admin/global/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10230', '全局应用订阅', '/v1/admin/global/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10231', '全局应用取消订阅', '/v1/admin/global/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10232', '获取Global应用市场固件列表', '/v1/admin/global/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10233', '获取Global固件发布的应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10234', '发布全球应用市场固件到其他应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10235', '订阅Global应用市场的固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10236', '取消订阅Global应用市场固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10237', '获取地图当前边界内的标记', '/v1/admin/map/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10238', '获取当前环境所有终端标记', '/v1/admin/map/markers/god/perspective', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10239', 'Global获取市场列表', '/v1/admin/map/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10240', 'Global获取市场信息', '/v1/admin/map/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10241', '获取代理商定位开启状态', '/v1/admin/map/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10242', '获取市场或子代理商可见的代理商列表', '/v1/admin/map/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10243', '获取市场或子代理商可见的代理商信息', '/v1/admin/map/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10244', '获取某地点内的POS机', '/v1/admin/map/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10245', '允许应用市场第三方系统API访问', '/v1/admin/market/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10246', '查询应用市场第三方系统API访问配置', '/v1/admin/market/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10247', '禁止应用市场第三方系统API访问', '/v1/admin/market/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10248', '添加外部系统绑定的ip地址', '/v1/admin/market/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10249', '更新外部系统绑定的ip地址', '/v1/admin/market/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10250', '删除外部系统绑定的ip地址', '/v1/admin/market/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', '获取应用市场第三方系统API访问密钥', '/v1/admin/market/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', '重置应用市场第三方系统API访问密钥', '/v1/admin/market/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', '激活应用市场', '/v1/admin/market/activate', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10254', '更新协议', '/v1/admin/market/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', '查询协议', '/v1/admin/market/agreement/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10256', '获取应用市场协议开关配置', '/v1/admin/market/agreement/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', '更新应用市场协议开关配置', '/v1/admin/market/agreement/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', '删除协议', '/v1/admin/market/agreement/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', 'create announcement notification', '/v1/admin/market/announcement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', 'get announcement notification', '/v1/admin/market/announcement/detail/{announcementId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', 'List all announcement notification', '/v1/admin/market/announcement/message', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', 'publish announcement notification', '/v1/admin/market/announcement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10263', 'update announcement notification', '/v1/admin/market/announcement/{announcementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10264', 'delete announcement notification', '/v1/admin/market/announcement/{announcementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10265', '查询登陆日志', '/v1/admin/market/audit/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10266', '导出登陆日志', '/v1/admin/market/audit/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10267', '查询操作日志参数详情', '/v1/admin/market/audit/operation/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10268', '查询操作日志', '/v1/admin/market/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10269', '导出操作日志', '/v1/admin/market/audit/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10270', '获取存在的操作日志类型', '/v1/admin/market/audit/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', '获取应用市场收费应用下载情况统计', '/v1/admin/market/billing/app/download/fee/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', '下载市场应用下载的情况', '/v1/admin/market/billing/app/download/fee/app/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', '获取Global应用市场收费应用收入子应用市场下载列表', '/v1/admin/market/billing/app/download/fee/app/{appId}/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', '下载应用市场购买应用的详情', '/v1/admin/market/billing/app/download/fee/app/{appId}/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', '获取应用市场收费应用下载情况统计', '/v1/admin/market/billing/app/download/fee/app/{appId}/terminal/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', '下载终端购买应用的详情', '/v1/admin/market/billing/app/download/fee/app/{appId}/terminal/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10277', '获取应用市场收费应用的实时下载情况', '/v1/admin/market/billing/app/download/fee/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10278', '获取应用市场过去6个月收费应用的收入情况', '/v1/admin/market/billing/app/download/fee/dashboard/last6', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10279', '获取应用市场收费应用下载最多的前10名', '/v1/admin/market/billing/app/download/fee/dashboard/top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10280', '获取应用市场收费应用开发者统计', '/v1/admin/market/billing/app/download/fee/developer/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10281', '下载应用开发者统计列表', '/v1/admin/market/billing/app/download/fee/developer/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10282', '获取应用市场收费应用开发者应用统计', '/v1/admin/market/billing/app/download/fee/developer/{developerId}/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10283', '获取应用市场收费应用历史收入账单', '/v1/admin/market/billing/app/download/fee/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10284', '下载应用下载历史列表', '/v1/admin/market/billing/app/download/fee/history/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10285', '获取Global应用市场收费应用子应用市场下载统计', '/v1/admin/market/billing/app/download/fee/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10286', '下载Global应用子应用市场下载统计列表', '/v1/admin/market/billing/app/download/fee/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10287', '获取Global应用市场收费应用子应用市场下载应用统计', '/v1/admin/market/billing/app/download/fee/market/{marketId}/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10288', '下载应用市场购买详情', '/v1/admin/market/billing/app/download/fee/{marketId}/purchase/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10289', '修改月账单服务价格', '/v1/admin/market/billing/change/price/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10290', '获取应用市场当前账单', '/v1/admin/market/billing/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10291', '更新账单默认设置', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10292', '查询账单默认设置', '/v1/admin/market/billing/defaultSettings/{billType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10293', '创建应用市场账单接收邮箱', '/v1/admin/market/billing/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10294', '获取应用市场接受账单邮箱', '/v1/admin/market/billing/email/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10295', '下载应用市场时间段账单', '/v1/admin/market/billing/global/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10296', '获取Global应用市场某月收款账单详情', '/v1/admin/market/billing/global/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10297', '获取Global应用市场时间段账单', '/v1/admin/market/billing/global/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10298', '获取Global应用市场时间段汇总', '/v1/admin/market/billing/global/total/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10299', '获取当前市场账单未支付的总额', '/v1/admin/market/billing/global/unreceived/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10300', '获取Global应用市场未收款账单', '/v1/admin/market/billing/global/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10301', '下载应用分润详情', '/v1/admin/market/billing/income/app/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10302', '获取单月应用分润', '/v1/admin/market/billing/income/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10303', '获取单月应用分润总金额', '/v1/admin/market/billing/income/single/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10304', '获取应用市场时间段应用分润列表', '/v1/admin/market/billing/income/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10305', '获取应用市场应用分润总额', '/v1/admin/market/billing/income/time/period/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10306', '获取应用市场虚拟账户金额', '/v1/admin/market/billing/income/virtual/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10307', '下载应用购买详情', '/v1/admin/market/billing/income/{appId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10308', '下载应用市场付款历史账单', '/v1/admin/market/billing/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10309', '获取应用市场账单信息', '/v1/admin/market/billing/invoice/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10310', '修改应用市场账单信息', '/v1/admin/market/billing/invoice/info', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10311', '手动调用定时任务', '/v1/admin/market/billing/job', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10312', '获取账单相关操作日志', '/v1/admin/market/billing/log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10313', '获取账单服务详情操作日志', '/v1/admin/market/billing/log/detail/{batchId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10314', '下载应用市场付款历史账单', '/v1/admin/market/billing/market/{marketId}/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10315', '根据时间搜索子市场单月账单', '/v1/admin/market/billing/market/{marketId}/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10316', '获取应用市场时间段账单', '/v1/admin/market/billing/market/{marketId}/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10317', '获取账单支付记录列表', '/v1/admin/market/billing/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10318', '查询支付订单', '/v1/admin/market/billing/payment/check', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10319', 'GLOBAL管理员确认已支付账单', '/v1/admin/market/billing/payment/confirm', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10320', '创建支付记录数据下载任务', '/v1/admin/market/billing/payment/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10321', '初始化支付订单', '/v1/admin/market/billing/payment/init', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10322', '获取应用分成历史列表', '/v1/admin/market/billing/revenue/share', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', '提交购买结算的转帐请求', '/v1/admin/market/billing/revenue/share/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', '获取应用分成详细开发者列表', '/v1/admin/market/billing/revenue/share/developer/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', '下载应用分润统计列表', '/v1/admin/market/billing/revenue/share/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', '获取应用分成详细应用市场列表', '/v1/admin/market/billing/revenue/share/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', '下载应用分润统计应用市场列表', '/v1/admin/market/billing/revenue/share/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', '发送账单邮件并更改月账单状态', '/v1/admin/market/billing/send/invoice/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', '根据时间搜索单月账单', '/v1/admin/market/billing/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', '更新账单相关状态为可修改状态', '/v1/admin/market/billing/status/audit/{billingSummaryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', '获取应用市场时间段账单', '/v1/admin/market/billing/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', '获取当前市场账单未支付的总额', '/v1/admin/market/billing/unPaid/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', '获取应用市场历史账单', '/v1/admin/market/billing/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', '修改应用市场账单接收邮箱', '/v1/admin/market/billing/{emailId}/email', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', '删除应用市场账单接收邮箱', '/v1/admin/market/billing/{emailId}/email', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', '获取应用市场收款是否设置', '/v1/admin/market/receivable/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', '删除应用市场收款帐户', '/v1/admin/market/receivable/account', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', '查询应用市场敏感词配置', '/v1/admin/market/sensitiveWord', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', '创建应用市场敏感词', '/v1/admin/market/sensitiveWord', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', '下载应用市场敏感词文件', '/v1/admin/market/sensitiveWord/file/{sensitiveWordId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', '删除应用市场敏感词', '/v1/admin/market/sensitiveWord/{sensitiveWordId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', '查询所有应用市场配置', '/v1/admin/market/settings', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10343', '更新应用市场配置', '/v1/admin/market/settings', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10344', '查询页脚列表', '/v1/admin/market/settings/footer', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10345', '创建页脚', '/v1/admin/market/settings/footer', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10346', '更新页脚', '/v1/admin/market/settings/footer/{footerId}', 'PUT', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10347', '删除页脚', '/v1/admin/market/settings/footer/{footerId}', 'DELETE', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10348', '更改应用市场页脚顺序', '/v1/admin/market/settings/footer/{footerId}/sort', 'PUT', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10349', '查询SSO配置内容', '/v1/admin/market/sso/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', '更新SSO配置', '/v1/admin/market/sso/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', '查询TID生成策略配置', '/v1/admin/market/tid/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', '更新TID生成策略配置', '/v1/admin/market/tid/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', '查询代理商应用市场配置', '/v1/admin/market/ui/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', '更新代理商应用市场配置', '/v1/admin/market/ui/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', '获取应用市场变量列表', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', '创建应用市场变量', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10357', '批量删除应用市场变量', '/v1/admin/market/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10358', '导入应用市场变量', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10359', '创建终应用市场变量导入模板下载任务', '/v1/admin/market/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10360', '查找应用市场变量支持的应用列表', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10361', '查找应用市场变量已使用的的应用列表', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10362', '更新应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10363', '删除应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', '根据serviceType获取已定阅的应用市场', '/v1/admin/market/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', '提交重新生成购买结算记录', '/v1/admin/purchase/clr', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', '(重新)提交购买结算的转帐请求', '/v1/admin/purchase/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', '管理员查询版本发行通知列表', '/v1/admin/releaseNotes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', '创建版本发行通知', '/v1/admin/releaseNotes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', '创建邮件模板下载任务', '/v1/admin/releaseNotes/mail/template/{noteInfoId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', '获取版本发行通知信息', '/v1/admin/releaseNotes/{releaseNoteInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', '更新版本发行通知', '/v1/admin/releaseNotes/{releaseNoteInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10372', '下载邮件列表', '/v1/admin/releaseNotes/{releaseNoteInfoId}/downloadEmails', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10373', '发送邮件', '/v1/admin/releaseNotes/{releaseNoteInfoId}/sendMail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10374', '测试邮件', '/v1/admin/releaseNotes/{releaseNoteInfoId}/sendTestMail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10375', 'Admin Search Report List', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10376', 'Get Report Metadata', '/v1/admin/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10377', 'resetAllReportMetadataAndTemplate', '/v1/admin/report/metadata', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10378', 'Get report by id', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10379', 'Update Report', '/v1/admin/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', 'Delete Report', '/v1/admin/report/{reportId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', 'Activate Report', '/v1/admin/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', 'Download Report Band File', '/v1/admin/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', 'Disable Report', '/v1/admin/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', 'Download Report Template File', '/v1/admin/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', 'List All Fields of Report', '/v1/admin/reportField', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', 'Update Report Field', '/v1/admin/reportField/{reportFieldId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', 'Get All Parameters of Report', '/v1/admin/reportParam', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10388', 'Update Report Parameter', '/v1/admin/reportParam/{reportParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10389', '允许代理商第三方系统API访问', '/v1/admin/reseller/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10390', '查询代理商第三方系统API访问配置', '/v1/admin/reseller/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10391', '禁止代理商第三方系统API访问', '/v1/admin/reseller/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10392', '添加外部系统绑定的ip地址', '/v1/admin/reseller/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10393', '更新外部系统绑定的ip地址', '/v1/admin/reseller/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10394', '删除外部系统绑定的ip地址', '/v1/admin/reseller/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10395', '获取代理商第三方系统API访问密钥', '/v1/admin/reseller/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10396', '重置代理商第三方系统API访问密钥', '/v1/admin/reseller/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10397', '保存代理商签名配置', '/v1/admin/reseller/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10398', '查询TID生成策略代理商配置', '/v1/admin/reseller/tid/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10399', '更新TID生成策略代理商配置', '/v1/admin/reseller/tid/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10400', '更新代理商UI设置', '/v1/admin/reseller/ui/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', '创建应用市场RKI配置', '/v1/admin/rki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', '获取RKI服务列表', '/v1/admin/rki/servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10403', '删除RKI密钥模板KEY', '/v1/admin/rki/template/key', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10404', '获取RKI密钥模板KEY', '/v1/admin/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10405', '测试未保存应用市场RKI服务', '/v1/admin/rki/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10406', '测试已保存应用市场RKI服务', '/v1/admin/rki/test/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10407', '查询应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10408', '更新应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10409', '删除RKI服务配置', '/v1/admin/rki/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10410', '查询应用市场签名配置', '/v1/admin/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10411', '保存应用市场签名配置', '/v1/admin/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10412', '清除签名数据', '/v1/admin/signature/clearData', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10413', '查询支持签名配置的厂商', '/v1/admin/signature/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10414', '测试应用市场签名服务', '/v1/admin/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10415', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10416', 'disableVas', '/v1/admin/vas', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10417', 'getThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10418', 'createThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10419', 'updateThirdpartyAppSys', '/v1/admin/vas/3rdsys/app', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10420', 'Find Vas Agreement Page', '/v1/admin/vas/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10421', 'Create Vas Agreement', '/v1/admin/vas/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10422', 'Create Vas Agreement', '/v1/admin/vas/agreement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10423', '下载服务协议记录', '/v1/admin/vas/agreement/{agreementId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10424', 'Update Vas Agreement', '/v1/admin/vas/agreement/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10425', 'delete vas agreement', '/v1/admin/vas/agreement/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10426', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10427', 'listPosviewerFileTransferInfo', '/v1/admin/vas/posviewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10428', 'listPosviewerOperationInfo', '/v1/admin/vas/posviewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10429', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10430', '查询告警列表', '/v1/alarm', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10431', '查看告警配置', '/v1/alarm/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10432', '保存告警配置信息', '/v1/alarm/setting', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10433', '查找接收者信息', '/v1/alarm/setting/receiver', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10434', '查看告警卡片', '/v1/alarm/widgets/digital', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10435', '创建告警中的数字卡片 数据下载任务', '/v1/alarm/widgets/export/{type}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10436', '获取应用参数列表', '/v1/apk/parameters', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10437', '创建应用参数', '/v1/apk/parameters', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10438', '查询参数APK列表', '/v1/apk/parameters/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10439', '查询参数应用列表', '/v1/apk/parameters/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10440', '批量删除应用参数', '/v1/apk/parameters/batch', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10441', '获取APK参数值对比结果', '/v1/apk/parameters/comparison', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10442', '获取参数模板应用列表', '/v1/apk/parameters/parameter/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10443', '获取应用参数详情', '/v1/apk/parameters/{apkParameterId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10444', '更新应用参数formData', '/v1/apk/parameters/{apkParameterId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10445', '更新应用参数', '/v1/apk/parameters/{apkParameterId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10446', '删除应用参数', '/v1/apk/parameters/{apkParameterId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10447', '应用参数数据文件下载', '/v1/apk/parameters/{apkParameterId}/data/file/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10448', '获取应用参数Schema', '/v1/apk/parameters/{apkParameterId}/schema', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10449', '获取appscan dashBoard数据', '/v1/app_scan/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10450', 'global获取引擎黑名单', '/v1/app_scan/engine/blacklist', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10451', 'global更新引擎黑名单', '/v1/app_scan/engine/blacklist', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10452', '获取appscan 历史用量数据', '/v1/app_scan/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10453', '获取扫描可操作的选项', '/v1/app_scan/operation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10454', '重新扫描', '/v1/app_scan/rescan', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10455', '获取扫描成功后生成的zip包', '/v1/app_scan/resultZip', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10456', '获取扫描结果清单', '/v1/app_scan/results', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10457', '查询apk是否可以继续创建扫描任务', '/v1/app_scan/scanned', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10458', '获取扫描配置信息', '/v1/app_scan/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10459', '更新扫描配置', '/v1/app_scan/setting', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10460', '创建扫描任务', '/v1/app_scan/task', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10461', '获取appscan扫描用量', '/v1/app_scan/usage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10462', '查询应用列表', '/v1/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10463', '查询Entity属性', '/v1/attributes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10464', '创建Entity属性', '/v1/attributes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10465', '获取Entity属性信息', '/v1/attributes/{attributeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10466', '更新Entity属性', '/v1/attributes/{attributeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10467', '删除Entity属性', '/v1/attributes/{attributeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10468', '更新Entity属性标签', '/v1/attributes/{attributeId}/label', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10469', '验证激活验证码', '/v1/auth/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10470', '激活用户', '/v1/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10471', '验证激活验证码', '/v1/auth/activation/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10472', 'currentTokenLogin', '/v1/auth/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10473', 'destroySsoToken', '/v1/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10474', '验证重置邮箱验证码', '/v1/auth/email/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10475', '用户更改邮箱', '/v1/auth/email/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10476', '验证重置邮箱验证码', '/v1/auth/email/reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10477', '验证下载链接', '/v1/auth/extraction', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10478', '验证提取码', '/v1/auth/extraction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10479', '获取应用市场的相关信息', '/v1/auth/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10480', '获取应用市场DC的相关信息', '/v1/auth/market/dc', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10481', '通过backup code关闭用户OTP', '/v1/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10482', '验证关闭用户OTP Code是否有效', '/v1/auth/otp/disableCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10483', '发送关闭用户OTP邮件', '/v1/auth/otp/resetMail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10484', '忘记密码发送邮件(未激活的重发激活邮件)', '/v1/auth/password/forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10485', '验证重置密码验证码', '/v1/auth/password/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10486', '重置密码（用于忘记密码）', '/v1/auth/password/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10487', '验证重置密码验证码', '/v1/auth/password/reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10488', '获取当前的密码规则', '/v1/auth/password/rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10489', 'checkTokenExpire', '/v1/auth/ping', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10490', '注册用户', '/v1/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10491', '创建前端埋点', '/v1/buriedPoints', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10492', '管理员查看POS Client App列表', '/v1/client-apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10493', '查看POS Client Apk列表for approval', '/v1/client-apps/client-apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10494', '查看POS Client Apk', '/v1/client-apps/client-apks/{clientApkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10495', '获取POS Client应用市场列表', '/v1/client-apps/client-apks/{clientApkId}/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10496', '获取POS Client应用市场详情', '/v1/client-apps/client-apks/{clientApkId}/markets/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10497', '设置Global应用市场POS Client发布数量', '/v1/client-apps/client-apks/{clientApkId}/publish-amount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10498', '发布Global应用市场POS Client应用到指定应用市场', '/v1/client-apps/client-apks/{clientApkId}/publish-markets/{marketId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10499', '删除Global应用市场POS Client应用到指定应用市场', '/v1/client-apps/client-apks/{clientApkId}/publish-markets/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10500', '切换POS Client Apk发布范围', '/v1/client-apps/client-apks/{clientApkId}/publish-range', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10501', '查看POS Client App的factory列表', '/v1/client-apps/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10502', '创建最新PAXSTORE客户端下载任务', '/v1/client-apps/latest/client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10503', '获取POS Client App详细信息', '/v1/client-apps/{clientAppId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10504', '删除POS Client App', '/v1/client-apps/{clientAppId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10505', '查看某个POS Client App的Apk列表', '/v1/client-apps/{clientAppId}/client-apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10506', '上传新版本POS Client Apk', '/v1/client-apps/{clientAppId}/client-apks/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10507', '更新ClientApk(更新日志，强制更新)，ClientApp name', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10508', '删除POS Client Apk', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10509', '通过POS Client Apk审核', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/approval', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10510', '创建PAXSTORE客户端下载任务', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10511', '重新上传POS Client Apk', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10512', '下线POS Client Apk', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10513', '上线POS Client Apk', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/online', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10514', '拒绝POS Client Apk审核', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/rejection', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10515', '提交POS Client Apk审核', '/v1/client-apps/{clientAppId}/client-apks/{clientApkId}/submit', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10516', '首次上传POS Client Apk', '/v1/client-apps/{factoryId}/client-apks/file/first', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10517', '获取字典列表', '/v1/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10518', '获取语言列表', '/v1/codes/lang', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10519', 'super管理员获取Code配置列表', '/v1/codes/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10520', 'super管理员查询CodeType类型列表', '/v1/codes/setting/codeTypes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10521', 'super管理员保存Code配置', '/v1/codes/setting/save', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10522', 'super管理员获取Code配置', '/v1/codes/setting/{type}/{value}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10523', 'super管理员删除Code配置', '/v1/codes/setting/{type}/{value}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10524', '根据类型获取字典列表', '/v1/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10525', 'generateCaptcha', '/v1/common/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10526', '获取文档中心配置信息', '/v1/common/doc-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10527', '查询页脚列表', '/v1/common/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10528', '获取页脚', '/v1/common/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10529', '获取语言列表', '/v1/common/languages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10530', '根据不同模块调用返回需要的证书信息', '/v1/common/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10531', 'Get message statistics (notification)', '/v1/common/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10532', 'Read specific messages in top reminder (notification)', '/v1/common/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10533', 'Read a message (notification)', '/v1/common/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10534', 'View message details (notification)', '/v1/common/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10535', '获取系统配置', '/v1/common/system-config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10536', '查询用户协议', '/v1/common/user-agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10537', '用户同意协议', '/v1/common/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10538', '查询data center', '/v1/dcmgt', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10539', '新增一个 Data Center', '/v1/dcmgt', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10540', '根据 Id 删除对应的DC', '/v1/dcmgt/{id}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10541', '修改对应的key-secret', '/v1/dcmgt/{id}/ks', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10542', '申请成为开发者', '/v1/developer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10543', '允许开发者访问第三方系统API', '/v1/developer/3rd-sys/active', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10544', '查询开发者第三方API访问配置', '/v1/developer/3rd-sys/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10545', '禁止开发者访问第三方系统API', '/v1/developer/3rd-sys/inactive', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10546', '获取开发者场第三方系统API访问密钥', '/v1/developer/3rd-sys/secret', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10547', '重置开发者第三方系统API访问密钥', '/v1/developer/3rd-sys/secret', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10548', '开发者同意开发者协议', '/v1/developer/agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10549', '更新APK信息、参数模板,图标与截图等信息或者提交应用', '/v1/developer/apks/{apkId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10550', '删除开发者应用版本', '/v1/developer/apks/{apkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10551', '获取APK编辑的信息', '/v1/developer/apks/{apkId}/apk-edit', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10552', '更新APK文件', '/v1/developer/apks/{apkId}/apk-file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10553', '创建开发者原始Apk下载任务', '/v1/developer/apks/{apkId}/file/download-tasks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10554', '开发者下线应用Apk', '/v1/developer/apks/{apkId}/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10555', 'apk编辑页获取自定义参数模板列表', '/v1/developer/apks/{apkId}/param-templates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10556', '上传自定义参数模板并进行解析Xml2Json', '/v1/developer/apks/{apkId}/param-templates/analysis', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10557', 'apk参数文件中下载相关数据文件下载任务', '/v1/developer/apks/{apkId}/param-templates/data-file/download-tasks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10558', 'apk详情下载apk参数模版', '/v1/developer/apks/{apkId}/param-templates/download-tasks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10559', '获取开发者应用apk参数Schema', '/v1/developer/apks/{apkId}/param-templates/schema', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10560', '查询开发者应用列表', '/v1/developer/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10561', '创建用户应用', '/v1/developer/apps', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10562', '查询开发者概况', '/v1/developer/apps/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10563', '获取开发者应用详细信息', '/v1/developer/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10564', '删除开发者应用', '/v1/developer/apps/{appId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10565', '添加apk', '/v1/developer/apps/{appId}/apk-file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10566', '获取APK列表', '/v1/developer/apps/{appId}/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10567', '获取APK', '/v1/developer/apps/{appId}/apks/{apkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10568', '覆盖更新APP Key, APP Secret', '/v1/developer/apps/{appId}/appKey', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10569', 'developer search insight sandbox app biz data', '/v1/developer/apps/{appId}/sandbox/insight-data', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10570', '获取当前用户开发者信息', '/v1/developer/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10571', '获取当前用户信息', '/v1/developer/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10572', '检查注册用户邮箱', '/v1/developer/email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10573', '获取当前开发者可见厂商名称列表', '/v1/developer/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10574', '获取当前开发者可见厂商以及机型列表', '/v1/developer/factory/models', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10575', '获取市场信息，登录和未登录有区别', '/v1/developer/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10576', '查询企业开发者列表', '/v1/developer/members', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10577', '添加企业开发者', '/v1/developer/members', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10578', '将开发者设为管理员', '/v1/developer/members/{userId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10579', '刪除企业开发者用户', '/v1/developer/members/{userId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10580', '企业开发者超级管理员更换', '/v1/developer/members/{userId}/super/admin', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10581', '获取自定义参数模板列表', '/v1/developer/param-templates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10582', '创建参数模板', '/v1/developer/param-templates', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10583', '获取开发者应用列表', '/v1/developer/param-templates/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10584', '获取自定义参数模板列表', '/v1/developer/param-templates/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10585', '开发者获取自定义模板编辑内容', '/v1/developer/param-templates/{templateId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10586', '更新参数模板', '/v1/developer/param-templates/{templateId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10587', '删除自定义参数模板', '/v1/developer/param-templates/{templateId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10588', '克隆参数模板', '/v1/developer/param-templates/{templateId}/clone', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10589', '自定义参数模板相关的下载', '/v1/developer/param-templates/{templateId}/download-tasks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10590', '更新参数模板名称和描述', '/v1/developer/param-templates/{templateId}/name', 'PATCH', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10591', '获取开发者自定义应用参数Schema-预览', '/v1/developer/param-templates/{templateId}/schema', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10592', '上传自定义参数模板', '/v1/developer/param-templates/{templateId}/upload', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10593', '获得开发者支付注册费初始化参数', '/v1/developer/payment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10594', '提交开发者注册费支付请求', '/v1/developer/payment', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10595', '线下支付账单', '/v1/developer/payment/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10596', '开发者应用结算查询', '/v1/developer/revenue/clearences', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10597', '查询开发者的应用销售列表', '/v1/developer/revenue/purchased-apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10598', '查询开发者的应用所销售的市场列表', '/v1/developer/revenue/purchased-markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10599', '获取开发者的收款帐户信息', '/v1/developer/revenue/receivable-account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10600', '删除开发者的收款帐户', '/v1/developer/revenue/receivable-account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10601', '获取开发者终端列表', '/v1/developer/sandbox-terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10602', '创建开发者终端', '/v1/developer/sandbox-terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10603', '获取制造商列表', '/v1/developer/sandbox-terminals/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10604', '获取开发者终端', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10605', '更新开发者终端', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10606', '删除开发者终端', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10607', '获取应用沙箱测试列表', '/v1/developer/sandbox/terminal-apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10608', '创建沙箱终端推送应用', '/v1/developer/sandbox/terminal-apks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10609', '获取APK', '/v1/developer/sandbox/terminal-apks/apps/{appId}/apks/{apkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10610', '获取沙箱终端推送APK', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10611', '删除沙箱终端推送参数任务', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10612', '激活沙箱终端推送参数任务', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/active', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10613', '沙箱终端推送应用参数数据文件下载', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/download-tasks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10614', '更新沙箱终端推送参数', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/param-template-name', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10615', '获取沙箱终端推送APK参数', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10616', '更新沙箱终端推送参数formData', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10617', '重置沙箱终端推送参数任务', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10618', '查询开发者协议', '/v1/developer/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10619', '开发者获取服务协议列表', '/v1/developer/vas/agreements', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10620', '获取appMsg', '/v1/developer/vas/msg', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10621', '添加AppMsg', '/v1/developer/vas/msg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10622', '获取已安装该app的终端数量', '/v1/developer/vas/msg/app/{appId}/installed-terminal-count', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10623', '查询appmsg标签列表', '/v1/developer/vas/msg/tag/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10624', '创建app标签', '/v1/developer/vas/msg/tag/apps/{appId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10625', '删除appmsg标签', '/v1/developer/vas/msg/tag/apps/{appId}/tags/{tagId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10626', '上传msgTemplateFile', '/v1/developer/vas/msg/template/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10627', '核验网络图片url的大小', '/v1/developer/vas/msg/template/validation-img-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10628', '根据id 获取msg', '/v1/developer/vas/msg/{id}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10629', '根据id 获取定时msg状态', '/v1/developer/vas/msg/{id}/status', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10630', '更新AppMsg状态', '/v1/developer/vas/msg/{msgId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10631', '获取appMsg统计数据', '/v1/developer/vas/msg/{msgId}/statistics', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10632', '开发者获取服务协议', '/v1/developer/vas/{serviceType}/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10633', '开发者同意服务协议', '/v1/developer/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10634', 'get stackly active user active viscosity', '/v1/developers/stacklytics/active_users/active_viscosity', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10635', 'get stackly active user summary', '/v1/developers/stacklytics/active_users/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10636', 'get stackly active user trend', '/v1/developers/stacklytics/active_users/trend', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10637', 'get stackly alert strategy', '/v1/developers/stacklytics/alert/strategy', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10638', 'create stackly alert strategy', '/v1/developers/stacklytics/alert/strategy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10639', 'update stackly alert strategy', '/v1/developers/stacklytics/alert/strategy', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10640', 'delete stackly alert strategy', '/v1/developers/stacklytics/alert/strategy/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10641', 'create stackly app', '/v1/developers/stacklytics/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10642', 'get stackly app version list', '/v1/developers/stacklytics/app_version_list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10643', 'create stackly comment', '/v1/developers/stacklytics/comment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10644', 'update stackly comment', '/v1/developers/stacklytics/comment', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10645', 'delete stackly comment', '/v1/developers/stacklytics/comment', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10646', 'get stackly comments', '/v1/developers/stacklytics/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10647', 'get stackly crash report summary info', '/v1/developers/stacklytics/crash_report_info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10648', 'get stackly crash report summary list', '/v1/developers/stacklytics/crash_report_list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10649', 'get stackly custom event', '/v1/developers/stacklytics/custom_event', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10650', 'get stackly custom event', '/v1/developers/stacklytics/custom_event', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10651', 'update stackly custom event', '/v1/developers/stacklytics/custom_event', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10652', 'export stackly event log', '/v1/developers/stacklytics/custom_event/report/info/{customEventId}/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10653', 'get stackly event keys', '/v1/developers/stacklytics/custom_event/report/info/{customEventId}/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10654', 'delete stackly custom event', '/v1/developers/stacklytics/custom_event/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', 'get stackly exception dist data', '/v1/developers/stacklytics/dist/new/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', 'get stackly exception dist distribution', '/v1/developers/stacklytics/dist/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', 'get stackly stacktrace file download info', '/v1/developers/stacklytics/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', 'get stackly operation start number statistics', '/v1/developers/stacklytics/engagement/start_up_time', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', 'get stackly operation usage duration distribution', '/v1/developers/stacklytics/engagement/usage_duration_dist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', 'get stackly operation usage frequency', '/v1/developers/stacklytics/engagement/usage_frequency', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', 'get stackly operation equipment statistical', '/v1/developers/stacklytics/equipment_stat', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', 'get stackly operation equipment statistical detail', '/v1/developers/stacklytics/equipment_stat_detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', 'get stackly operation equipment statistical detail download', '/v1/developers/stacklytics/equipment_stat_detail/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10664', 'get stackly hash stack exception statistics', '/v1/developers/stacklytics/exception_statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', 'export exception list', '/v1/developers/stacklytics/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', 'get stackly hash stack files', '/v1/developers/stacklytics/files', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', 'get stacly hash stack filter infos', '/v1/developers/stacklytics/filter_infos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', 'get stackly hash stack summaries', '/v1/developers/stacklytics/hash_stack', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', 'get hash stack handle history', '/v1/developers/stacklytics/histories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', 'create stackly hash stack handle history', '/v1/developers/stacklytics/history', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10671', 'get stackly hash stack info', '/v1/developers/stacklytics/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10672', 'getDataQueryFromInsight', '/v1/developers/stacklytics/insight/worksheet', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', 'add stackly member', '/v1/developers/stacklytics/member', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', 'update stackly member', '/v1/developers/stacklytics/member', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', 'update stackly member', '/v1/developers/stacklytics/member', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', 'get current stackly user info', '/v1/developers/stacklytics/member/authority', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', 'handover stackly authority', '/v1/developers/stacklytics/member/handover', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', 'get stackly members', '/v1/developers/stacklytics/members', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', 'get stackly All members', '/v1/developers/stacklytics/members/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', 'get stackly new user percentage', '/v1/developers/stacklytics/new_users/percentage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10681', 'get stackly new user retention rate', '/v1/developers/stacklytics/new_users/retention_rate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', 'get stackly new user statistics', '/v1/developers/stacklytics/new_users/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', 'get stackly new user trend', '/v1/developers/stacklytics/new_users/trend', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', 'get stackly operation list', '/v1/developers/stacklytics/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', 'get stackly operation detail', '/v1/developers/stacklytics/report/info/{customEventId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', 'get stackly active user active viscosity', '/v1/developers/stacklytics/retention_rate/{retentionType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', 'retrace stacktrace', '/v1/developers/stacklytics/retrace', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10688', 'get stackly hash stack stack trace', '/v1/developers/stacklytics/stack_trace', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10689', 'get stackly exception status dist distribution', '/v1/developers/stacklytics/status/dist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10690', 'add stackly tag to hash stack', '/v1/developers/stacklytics/tag', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10691', 'delete stackly tag from hash stack', '/v1/developers/stacklytics/tag', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', 'get stackly app tags', '/v1/developers/stacklytics/tags', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10693', 'create stackly app tags', '/v1/developers/stacklytics/tags', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', 'update stackly app tags', '/v1/developers/stacklytics/tags', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10695', 'delete stackly app tags', '/v1/developers/stacklytics/tags/{tagId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10696', 'get terminal list', '/v1/developers/stacklytics/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', '下载Fastdfs服务器上的文件', '/v1/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10698', '下载Fastdfs服务器上的文件', '/v1/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10699', '根据下载号获得下载地址', '/v1/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10700', '获取制造商列表', '/v1/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', '创建制造商', '/v1/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', '获取制造商机型树', '/v1/factories/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', '获取应用市场签名提供商列表', '/v1/factories/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', '获取制造商', '/v1/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10705', '更新制造商', '/v1/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10706', '删除制造商', '/v1/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10707', '获取制造商定向发布到的市场列表', '/v1/factories/{factoryId}/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10708', '定向发布制造商到应用市场', '/v1/factories/{factoryId}/market', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', '删除制造商定向发布到市场', '/v1/factories/{factoryId}/market', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10710', '获取Fastdfs服务器上的文件', '/v1/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10711', '获取固件列表', '/v1/firmwares', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10712', '查看固件的factory列表', '/v1/firmwares/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10713', '上传固件', '/v1/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', '删除固件差分包', '/v1/firmwares/firmware/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', '创建固件文件下载任务', '/v1/firmwares/firmware/files/{fmFileId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', '获取固件', '/v1/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', '更新固件', '/v1/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', '删除固件', '/v1/firmwares/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', '固件审核通过', '/v1/firmwares/{firmwareId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', '上传固件差分包', '/v1/firmwares/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', '下线固件', '/v1/firmwares/{firmwareId}/offline', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', '上线固件', '/v1/firmwares/{firmwareId}/online', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', '固件审核拒绝', '/v1/firmwares/{firmwareId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', '提交固件更新', '/v1/firmwares/{firmwareId}/submit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', '获取终端分组变量列表', '/v1/groupVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10726', '创建终端分组变量', '/v1/groupVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10727', '批量删除终端分组变量', '/v1/groupVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10728', '导入终端分组变量', '/v1/groupVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10729', '创建终端分组变量导入模板下载任务', '/v1/groupVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10730', '查找终端分组变量支持的应用列表', '/v1/groupVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10731', '查找终端分组变量已使用的应用列表', '/v1/groupVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10732', '更新终端分组变量', '/v1/groupVariables/{groupVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10733', '删除终端分组变量', '/v1/groupVariables/{groupVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10734', '苏研账单中心同步账单数据', '/v1/internal/3rdsys/billing', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10735', '苏研账单中心废弃账单同步', '/v1/internal/3rdsys/billing/abandon', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10736', '苏研账单中心取消核销账单同步', '/v1/internal/3rdsys/billing/cancel/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10737', '获取所有激活的市场', '/v1/internal/3rdsys/billing/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10738', '苏研账单中心部分核销账单同步', '/v1/internal/3rdsys/billing/partial/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10739', '苏研账单中心核销账单同步', '/v1/internal/3rdsys/billing/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10740', '苏研账单中心同步数据回调', '/v1/internal/3rdsys/billing/{billingSummaryId}/callback', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10741', '修改月账单价格同步', '/v1/internal/3rdsys/billing/{billingSummaryId}/price/sync', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10742', 'Clear Cache', '/v1/internal/3rdsys/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10743', 'Clear Deleted Terminals', '/v1/internal/3rdsys/clearDeletedTerminals', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10744', 'Clear Expired Pending Terminal Actions', '/v1/internal/3rdsys/clearExpiredPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10745', 'Clear Group Pending Terminal Actions', '/v1/internal/3rdsys/clearGroupPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10746', 'Clear Terminal Pending Terminal Actions', '/v1/internal/3rdsys/clearTerminalPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10747', 'Delete Lock', '/v1/internal/3rdsys/delLock', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10748', 'Get Cache', '/v1/internal/3rdsys/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10749', 'Get Lock', '/v1/internal/3rdsys/getLock', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10750', 'Sync Dictionary Data to GoInsight', '/v1/internal/3rdsys/insight/sync/dictionary-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10751', 'Sync Market Data to GoInsight', '/v1/internal/3rdsys/insight/sync/market-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10752', 'Sync Terminal Realtime Data to GoInsight', '/v1/internal/3rdsys/insight/sync/terminal-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10753', 'Market api black list', '/v1/internal/3rdsys/marketBlackApi', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10754', 'Delete market black api', '/v1/internal/3rdsys/marketBlackApi/{marketBlackApiId}/market/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10755', 'Create market black api', '/v1/internal/3rdsys/marketBlackApi/{marketId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10756', 'Refresh Group Action Count', '/v1/internal/3rdsys/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10757', 'Refresh Reseller Installed Apks', '/v1/internal/3rdsys/refreshResellerInstalledApks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10758', 'Refresh Terminal Last Access Time', '/v1/internal/3rdsys/refreshTerminalLastAccessTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10759', 'Refresh Terminal Online Status', '/v1/internal/3rdsys/refreshTerminalOnlineStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10760', 'Refresh Terminal Stock', '/v1/internal/3rdsys/refreshTerminalStock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10761', 'Repair Activity Status', '/v1/internal/3rdsys/repairActivityStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10762', 'Get system property log', '/v1/internal/3rdsys/system/property/log', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10763', 'Get system properties', '/v1/internal/3rdsys/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10764', 'Save System Property', '/v1/internal/3rdsys/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10765', 'Get Terminal', '/v1/internal/3rdsys/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10766', 'Send terminal command', '/v1/internal/3rdsys/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10767', 'Send terminal message', '/v1/internal/3rdsys/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10768', 'Administrator view push history list', '/v1/internal/3rdsys/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10769', 'Reset terminal data', '/v1/internal/3rdsys/terminal/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10770', 'Get Filter Terminals', '/v1/internal/3rdsys/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10771', 'Post Filter Terminals', '/v1/internal/3rdsys/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10772', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10773', 'getLatestOnlineApkList', '/v1/internal/appUpdate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10774', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10775', 'getApps', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10776', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10777', '获取Launcher模板列表', '/v1/launcher/templates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10778', '创建Launcher模板', '/v1/launcher/templates', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10779', '获取Launcher模板详情', '/v1/launcher/templates/{launcherTemplateId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10780', '更新Launcher模板', '/v1/launcher/templates/{launcherTemplateId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10781', '删除Launcher模板', '/v1/launcher/templates/{launcherTemplateId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10782', '获取许可证信息', '/v1/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10783', '更新许可证', '/v1/license', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10784', '获取AirViewer当前用量', '/v1/marketAdmin/vas/airViewer/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10785', '获取AirViewer当前用量dashBoard', '/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10786', '获取AirViewer历史用量', '/v1/marketAdmin/vas/airViewer/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10787', '导出airviewer用量', '/v1/marketAdmin/vas/airviewer/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10788', '导出appscan用量', '/v1/marketAdmin/vas/appscan/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10789', '获取应用市场增值服务的账单设置', '/v1/marketAdmin/vas/billingSetting/{billingType}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10790', '获取CloudMsg当前用量', '/v1/marketAdmin/vas/cloudMsg/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10791', '获取cloudMsg当前用量dashBoard', '/v1/marketAdmin/vas/cloudMsg/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10792', '导出CloudMsg用量', '/v1/marketAdmin/vas/cloudMsg/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10793', '导出CloudMsg历史用量', '/v1/marketAdmin/vas/cloudMsg/historical/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10794', '获取CloudMsg历史用量', '/v1/marketAdmin/vas/cloudMsg/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10795', '导出detail压缩包', '/v1/marketAdmin/vas/export/detail/zip', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10796', '导出summary报表', '/v1/marketAdmin/vas/export/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10797', '获取GoInsight当前用量', '/v1/marketAdmin/vas/goInsight/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10798', '获取GoInsight当前用量dashBoard', '/v1/marketAdmin/vas/goInsight/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10799', '获取launcherUp当前用量', '/v1/marketAdmin/vas/launcherUp/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10800', '获取launcherUp当前用量dashBoard', '/v1/marketAdmin/vas/launcherUp/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10801', '导出launcherUp用量', '/v1/marketAdmin/vas/launcherUp/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10802', '导出launcherUp历史用量', '/v1/marketAdmin/vas/launcherUp/historical/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10803', '获取launcherUp历史用量', '/v1/marketAdmin/vas/launcherUp/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10804', '获取市场可见服务列表', '/v1/marketAdmin/vas/services', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10805', '是否显示当月用量', '/v1/marketAdmin/vas/services/show', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10806', '更新市场服务的激活状态', '/v1/marketAdmin/vas/services/{marketId}/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10807', '市场取消订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10808', '市场订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10809', '查询服务定向发布的代理商', '/v1/marketAdmin/vas/services/{serviceType}/reseller/specific', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10810', '修改服务定向发布', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10811', '删除服务定向发布', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10812', '获取skyhook当前用量', '/v1/marketAdmin/vas/skyhook/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10813', '获取skyhook当前用量dashBoard', '/v1/marketAdmin/vas/skyhook/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10814', '导出skyhook用量', '/v1/marketAdmin/vas/skyhook/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10815', '导出skyhook历史用量', '/v1/marketAdmin/vas/skyhook/historical/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10816', '获取skyhook历史用量', '/v1/marketAdmin/vas/skyhook/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10817', '获取应用市场指定年月的接入终端快照', '/v1/marketAdmin/vas/snapshot/file/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10818', '查询服务订阅历史记录', '/v1/marketAdmin/vas/subscriptionHistory/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10819', '获取当前应用市场的终端接入量详情', '/v1/marketAdmin/vas/terminal/enroll/bill', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10820', '下载指定市场当前月的接入终端详情', '/v1/marketAdmin/vas/terminal/enroll/current/{marketId}/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10821', '获取应用市场当前的终端接入量统计', '/v1/marketAdmin/vas/terminal/enroll/dashboard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10822', '获取当前应用市场的终端接入量统计', '/v1/marketAdmin/vas/terminal/enroll/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10823', '获取当前应用市场的终端接入量统计', '/v1/marketAdmin/vas/terminal/enroll/history/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10824', '市场获取服务协议', '/v1/marketAdmin/vas/{serviceType}/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10825', '市场同意服务协议', '/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10826', '查询应用市场列表', '/v1/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10827', '创建应用市场', '/v1/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10828', '获取app统计的详细信息', '/v1/markets/appNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10829', '获取应用市场下app数量', '/v1/markets/applicationNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10830', '统计开发者数量', '/v1/markets/developerNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10831', '获取开发者统计信息详情', '/v1/markets/developerNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10832', '下载市场列表', '/v1/markets/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10833', '获取应用市场数/快过期的数量', '/v1/markets/marketNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10834', '获取应用市场详细统计信息', '/v1/markets/marketNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10835', '创建统计列表数据下载任务', '/v1/markets/statistics/export/{type}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10836', '终端数量', '/v1/markets/terminalNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10837', '生成应用市场终端报表', '/v1/markets/terminalReport', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10838', '获取应用市场信息', '/v1/markets/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10839', '更新应用市场', '/v1/markets/{marketId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10840', '删除应用市场', '/v1/markets/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10841', '更新应用市场账单设置', '/v1/markets/{marketId}/billing', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10842', '更新应用市场服务设置', '/v1/markets/{marketId}/billing/service', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10843', '更新应用市场', '/v1/markets/{marketId}/overdue', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10844', '替换应用市场管理员（邮箱）', '/v1/markets/{marketId}/replaceEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10845', '重新发送应用市场激活邮件', '/v1/markets/{marketId}/resend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10846', '解挂应用市场', '/v1/markets/{marketId}/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10847', '挂起应用市场', '/v1/markets/{marketId}/suspend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10848', '获取当前用户信息', '/v1/merchant/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10849', '获取商户portal 购买应用列表', '/v1/merchant/dashboard/purchased-apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10850', '获取Dashboard里 机型-终端数量 (W15)的数据', '/v1/merchant/dashboard/widgets/W15', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10851', '获取Dashboard 长时间 offline (W16)的数据', '/v1/merchant/dashboard/widgets/W16', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10852', '获取商户portal dashboard(W21)的数据', '/v1/merchant/dashboard/widgets/W21', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10853', '获取市场信息，登录和未登录有区别', '/v1/merchant/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10854', '获取商户分类列表', '/v1/merchantCategories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10855', '创建商户分类', '/v1/merchantCategories', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10856', '批量删除商户分类', '/v1/merchantCategories/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10857', '导入商户类型', '/v1/merchantCategories/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10858', '创建商户类型导入模板下载任务', '/v1/merchantCategories/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10859', '更新商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10860', '删除商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10861', '获取商户变量列表', '/v1/merchantVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10862', '创建商户变量', '/v1/merchantVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10863', '批量删除商户变量', '/v1/merchantVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10864', '导入商户变量', '/v1/merchantVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10865', '创建商户变量导入模板下载任务', '/v1/merchantVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10866', '查找商户变量支持的应用列表', '/v1/merchantVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10867', '查找商户变量已使用的应用列表', '/v1/merchantVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10868', '更新商户变量', '/v1/merchantVariables/{merchantVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10869', '删除商户变量', '/v1/merchantVariables/{merchantVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10870', '获取商户列表', '/v1/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', '创建商户', '/v1/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10872', '创建商户导出下载任务', '/v1/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', '导入商户', '/v1/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', '创建商户导入模板下载任务', '/v1/merchants/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10875', '获取代理商下一层级商户', '/v1/merchants/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10876', '获取商户', '/v1/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10877', '更新商户', '/v1/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10878', '删除商户', '/v1/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10879', '激活商户', '/v1/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10880', '停用商户', '/v1/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10881', '获取商户配置文件', '/v1/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10882', '创建商户配置文件', '/v1/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10883', '替换商户管理员（邮箱）', '/v1/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', '重发商户激活邮件', '/v1/merchants/{merchantId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10885', '查询Migrations', '/v1/migrations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10886', '创建Migration', '/v1/migrations', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10887', '获取Migration', '/v1/migrations/{migrationId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10888', '删除Migration', '/v1/migrations/{migrationId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10889', '查询Migration Apk Template', '/v1/migrations/{migrationId}/apkTemplates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10890', '执行Migration', '/v1/migrations/{migrationId}/execute', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10891', '导出Migration结果', '/v1/migrations/{migrationId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10892', '查询Migration Merchant', '/v1/migrations/{migrationId}/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10893', '通知Migration新创建的用户', '/v1/migrations/{migrationId}/notify', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10894', '回滚Migration', '/v1/migrations/{migrationId}/rollback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10895', '查询Migration Terminal Apk', '/v1/migrations/{migrationId}/terminalApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10896', '查询Migration Terminal Group', '/v1/migrations/{migrationId}/terminalGroups', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10897', '查询Migration Terminal', '/v1/migrations/{migrationId}/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10898', '查询Migration User', '/v1/migrations/{migrationId}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10899', '验证Migration', '/v1/migrations/{migrationId}/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10900', '获取机型列表', '/v1/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10901', '创建机型', '/v1/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10902', '制造商发布当前市场可见机型', '/v1/models/factory/{factoryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10903', '获取市场无人值守的机型', '/v1/models/posviewer/unattended', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10904', '更新市场无人值守的机型', '/v1/models/posviewer/unattended', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10905', '获取机型', '/v1/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10906', '更新机型', '/v1/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10907', '删除机型', '/v1/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10908', '查询机型可用的应用列表', '/v1/models/{modelId}/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10909', 'listAll', '/v1/oauthClients', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10910', 'add', '/v1/oauthClients', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10911', 'get', '/v1/oauthClients/{id}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10912', 'update', '/v1/oauthClients/{id}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10913', 'delete', '/v1/oauthClients/{id}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10914', '获取所有受保护的操作列表', '/v1/operations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10915', '获取所有受保护的操作', '/v1/operations/{key}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10916', '关闭操作权限', '/v1/operations/{key}/close', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10917', '打开操作权限', '/v1/operations/{key}/open', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10918', '查询操作用户', '/v1/operations/{key}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10919', '添加操作用户', '/v1/operations/{key}/users/{userId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10920', '删除操作用户', '/v1/operations/{key}/users/{userId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10921', '获得支付初始化参数(支付账单)', '/v1/payment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10922', '提交支付请求', '/v1/payment', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10923', '查询应用列表', '/v1/portal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10924', '获取广告位信息', '/v1/portal/apps/advertisement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10925', '查询portal可见应用的行业分类', '/v1/portal/apps/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10926', '查询应用开发者所属其他应用', '/v1/portal/apps/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10927', '查找精品应用列表', '/v1/portal/apps/featured', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10928', '终端通过包名获取应用详情', '/v1/portal/apps/packageName', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10929', '查询应用列表排行', '/v1/portal/apps/rank', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10930', '查询应用分类相关其他应用', '/v1/portal/apps/related', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10931', '根据类型获取字典列表---应用类型分类', '/v1/portal/apps/types', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10932', '获取应用详情', '/v1/portal/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10933', '获取当前用户信息', '/v1/portal/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10934', '获取市场信息，登录和未登录有区别', '/v1/portal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10935', '查询权限列表', '/v1/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10936', '创建权限', '/v1/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10937', '获取权限信息', '/v1/privileges/{privilegeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10938', '更新权限', '/v1/privileges/{privilegeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10939', '删除权限', '/v1/privileges/{privilegeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10940', '获取权限资源列表', '/v1/privileges/{privilegeId}/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10941', '添加权限资源', '/v1/privileges/{privilegeId}/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10942', '删除权限资源', '/v1/privileges/{privilegeId}/resources', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10943', 'getEnvCode', '/v1/pub/vas/envCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10944', 'User Search Report', '/v1/report', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10945', 'Get Report Categories', '/v1/report/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10946', '获取安装过应用列表', '/v1/report/installed/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10947', 'GET merchants by resellerIds', '/v1/report/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10948', 'Refresh Report Parameter Sources', '/v1/report/parameter/{parameterId}/source/refresh', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10949', '获取已安装的puk列表', '/v1/report/puk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10950', '获取推送过的最新APK列表', '/v1/report/pushed/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10951', 'Delete Report Execution Tasks', '/v1/report/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10952', 'Search Report Job History', '/v1/report/reportJobHistory', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10953', 'Create Report Download Task', '/v1/report/reportTask/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10954', 'Update Report Tasks Status', '/v1/report/reportTask/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10955', 'Search Report Task', '/v1/report/reportTask/{reportId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10956', 'Update Report Task Status', '/v1/report/reportTask/{reportTaskId}/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10957', 'GET resellers by marketId', '/v1/report/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10958', 'Delete Report Execution Task', '/v1/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10959', 'User View Report', '/v1/report/{reportId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10960', 'Export Report', '/v1/report/{reportId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10961', 'find fields', '/v1/report/{reportId}/fields', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10962', 'Create Report Execution Task', '/v1/report/{reportId}/reportExecutionContext', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10963', 'Get Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10964', 'Update Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10965', '查询代理商迁移列表', '/v1/reseller-migrations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10966', '创建代理商迁移', '/v1/reseller-migrations', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10967', '获取代理商迁移信息', '/v1/reseller-migrations/{resellerMigrationId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10968', '获取代理商列表', '/v1/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10969', '创建代理商', '/v1/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10970', '代理商查询应用列表', '/v1/resellers/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10971', '代理商根据包名获取应用名字和图标', '/v1/resellers/reseller/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10972', '创建导入TMK模板下载任务', '/v1/resellers/rki/tmk/import/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10973', '获取当前用户代理商树', '/v1/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10974', '获取下一层级子代理商', '/v1/resellers/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10975', '获取代理商', '/v1/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10976', '更新代理商', '/v1/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10977', '删除代理商', '/v1/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10978', '激活代理商', '/v1/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10979', '停用代理商', '/v1/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10980', '获取当前代理商终端已安装应用列表', '/v1/resellers/{resellerId}/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10981', '移动代理商', '/v1/resellers/{resellerId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10982', '获取代理商配置文件', '/v1/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10983', '创建代理商配置文件', '/v1/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10984', '替换代理商管理员（邮箱）', '/v1/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10985', '重发激活邮件', '/v1/resellers/{resellerId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10986', '同步代理商的RKI密钥列表', '/v1/resellers/{resellerId}/rki/keys/collect', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10987', 'Push RKI进行预扣款', '/v1/resellers/{resellerId}/rki/pre-deduction', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10988', '获取RKI密钥模板KEY', '/v1/resellers/{resellerId}/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10989', '获取导入tmk记录', '/v1/resellers/{resellerId}/rki/tmk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10990', '导入终端主密钥', '/v1/resellers/{resellerId}/rki/tmk/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10991', '保存代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10992', '删除代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10993', '查询资源列表', '/v1/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10994', '创建资源', '/v1/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10995', '获取资源信息', '/v1/resources/{resourceId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10996', '更新资源', '/v1/resources/{resourceId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10997', '删除资源', '/v1/resources/{resourceId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10998', '获取资源权限列表', '/v1/resources/{resourceId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10999', '添加资源权限', '/v1/resources/{resourceId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11000', '删除资源权限', '/v1/resources/{resourceId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11001', '查询角色列表', '/v1/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11002', '创建角色', '/v1/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11003', '查询角色列表包含开发者和商户', '/v1/roles/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11004', '获取角色的用户', '/v1/roles/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11005', '查询所有角色的用户列表', '/v1/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11006', '获取角色信息', '/v1/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11007', '更新角色', '/v1/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11008', '删除角色', '/v1/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11009', '获取角色权限列表', '/v1/roles/{roleId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11010', '添加角色权限', '/v1/roles/{roleId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11011', '删除角色权限', '/v1/roles/{roleId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11012', '获取角色用户列表', '/v1/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11013', '添加角色用户', '/v1/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11014', '删除角色用户', '/v1/roles/{roleId}/users', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11015', '查询邮件服务配置', '/v1/system/mail-config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11016', '保存邮件服务配置', '/v1/system/mail-config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11017', '测试邮件服务配置生效', '/v1/system/mail-config-test', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11018', '查询系统属性', '/v1/system/properties', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11019', '创建系统属性', '/v1/system/properties', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11020', '获取系统属性信息', '/v1/system/properties/{systemPropertyId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11021', '更新系统属性', '/v1/system/properties/{systemPropertyId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11022', '删除系统属性', '/v1/system/properties/{systemPropertyId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11023', '查询推送检测结果', '/v1/system/push/diagnosis', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11024', '发送推送检测消息', '/v1/system/push/diagnosis', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11025', '查询预定义角色列表', '/v1/system/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11026', '查询登录相关配置', '/v1/system/security/loginCfg', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11027', '保存登录相关配置', '/v1/system/security/loginCfg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11028', '查询密码策略', '/v1/system/security/password/policy', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11029', '保存密码策略', '/v1/system/security/password/policy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11030', '查询终端迁移列表', '/v1/terminal-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11031', '创建终端迁移', '/v1/terminal-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11032', '获取终端迁移信息', '/v1/terminal-migrations/{terminalMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11033', '获取终端推送应用列表', '/v1/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11034', '创建终端推送应用', '/v1/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11035', '获取推送APK参数值对比结果', '/v1/terminalApks/history/param/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11036', '获取终端推送APK', '/v1/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11037', '删除终端推送应用', '/v1/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11038', '激活终端推送应用', '/v1/terminalApks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11039', '终端推送应用参数数据文件下载', '/v1/terminalApks/{terminalApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11040', '获取终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11041', '更新终端推送APK参数formData', '/v1/terminalApks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11042', '更新终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11043', '获取终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11044', '保存终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11045', '重置终端推送应用', '/v1/terminalApks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11046', '挂起终端推送应用', '/v1/terminalApks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11047', '获取终端推送固件列表', '/v1/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11048', '创建终端推送固件', '/v1/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11049', '获取终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11050', '删除终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11051', '激活终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11052', '重置终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11053', '挂起终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11054', '获取终端分组推送应用列表', '/v1/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11055', '创建终端分组推送应用', '/v1/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11056', '更新终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11057', '删除终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11058', '获取终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11059', '删除终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11060', '重新推送终端分组应用', '/v1/terminalGroupApks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11061', '激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11062', '分组推送应用参数数据文件下载', '/v1/terminalGroupApks/{groupApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11063', '创建终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11064', '更新终端分组推送应用的推送限制', '/v1/terminalGroupApks/{groupApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11065', '获取终端分组推送应用参数', '/v1/terminalGroupApks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11066', '更新终端分组推送应用参数参数formData', '/v1/terminalGroupApks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11067', '更新终端分组推送应用参数参数', '/v1/terminalGroupApks/{groupApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11068', '重新推送终端分组应用参数', '/v1/terminalGroupApks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11069', '获取终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11070', '修改终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11071', '获取终端分组推送应用参数终端列表', '/v1/terminalGroupApks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11072', '获取终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11073', '保存终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11074', '重新激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11075', '挂起终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11076', '获取终端分组推送应用终端列表', '/v1/terminalGroupApks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11077', '创建分组应用推送终端列表下载任务', '/v1/terminalGroupApks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11078', '获取分组推送固件列表', '/v1/terminalGroupFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11079', '创建分组推送固件', '/v1/terminalGroupFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11080', '更新终端分组推送固件的筛选条件', '/v1/terminalGroupFirmwares/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11081', '删除终端分组推送固件的筛选条件', '/v1/terminalGroupFirmwares/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11082', '获取分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11083', '删除分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11084', '重新推送终端分组固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11085', '激活分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11086', '创建终端分组推送固件的筛选条件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11087', '更新终端分组推送固件的推送限制', '/v1/terminalGroupFirmwares/{groupFirmwareId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11088', '重置分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11089', '挂起分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11090', '获取分组推送固件终端列表', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11091', '创建分组固件推送终端列表下载任务', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11092', '获取终端分组推送Launcher列表', '/v1/terminalGroupLaunchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11093', '创建终端分组推送Launcher', '/v1/terminalGroupLaunchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11094', '更新终端分组推送Launcher的筛选条件', '/v1/terminalGroupLaunchers/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11095', '删除终端分组推送Launcher的筛选条件', '/v1/terminalGroupLaunchers/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11096', '获取终端分组推送Launcher', '/v1/terminalGroupLaunchers/{groupLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11097', '删除终端分组推送Launcher', '/v1/terminalGroupLaunchers/{groupLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11098', '重新推送终端分组应用', '/v1/terminalGroupLaunchers/{groupLauncherId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11099', '激活终端分组推送Launcher', '/v1/terminalGroupLaunchers/{groupLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11100', '创建终端分组推送Launcher的筛选条件', '/v1/terminalGroupLaunchers/{groupLauncherId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11101', '更新终端分组推送Launcher的推送限制', '/v1/terminalGroupLaunchers/{groupLauncherId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11102', '获取终端分组推Launcher用参数', '/v1/terminalGroupLaunchers/{groupLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11103', '重新推送终端分组Launcher参数', '/v1/terminalGroupLaunchers/{groupLauncherId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11104', '获取终端分组推送Launcher参数终端列表', '/v1/terminalGroupLaunchers/{groupLauncherId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11105', '重新激活终端分组推送Launcher', '/v1/terminalGroupLaunchers/{groupLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11106', '挂起终端分组推送Launcher', '/v1/terminalGroupLaunchers/{groupLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11107', '获取终端分组推送Launcher终端列表', '/v1/terminalGroupLaunchers/{groupLauncherId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11108', '创建分组应用推送终端列表下载任务', '/v1/terminalGroupLaunchers/{groupLauncherId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11109', '获取分组推送Operation列表', '/v1/terminalGroupOpts', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11110', '创建分组推送Operation', '/v1/terminalGroupOpts', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11111', '获取分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11112', '删除分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11113', '重新推送终端分组Operation', '/v1/terminalGroupOpts/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11114', '激活分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11115', '更新终端分组Operation的推送限制', '/v1/terminalGroupOpts/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11116', '重置分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11117', '挂起分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11118', '获取分组推送Operation终端列表', '/v1/terminalGroupOpts/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11119', '创建分组Operation推送终端列表下载任务', '/v1/terminalGroupOpts/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11120', '创建分组推送PUK', '/v1/terminalGroupPuks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11121', '删除分组推送PUK', '/v1/terminalGroupPuks/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11122', '重新推送终端分组PUK', '/v1/terminalGroupPuks/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11123', '激活分组推送PUK', '/v1/terminalGroupPuks/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11124', '更新终端分组PUK的推送限制', '/v1/terminalGroupPuks/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11125', '重置分组推送PUK', '/v1/terminalGroupPuks/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11126', '挂起分组推送PUK', '/v1/terminalGroupPuks/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11127', '获取分组推送RKI列表', '/v1/terminalGroupRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11128', '创建分组推送RKI', '/v1/terminalGroupRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11129', '获取分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11130', '删除分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11131', '重新推送终端分组RKI', '/v1/terminalGroupRkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11132', '激活分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11133', '更新终端分组推送RKI的推送限制', '/v1/terminalGroupRkis/{groupRkiId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11134', '重置分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11135', '挂起分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11136', '获取分组推送RKI终端列表', '/v1/terminalGroupRkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11137', '创建分组RKI推送终端列表下载任务', '/v1/terminalGroupRkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11138', '获取分组推送卸载应用列表', '/v1/terminalGroupUninstallApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11139', '创建分组推送卸载应用', '/v1/terminalGroupUninstallApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11140', '获取分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11141', '删除分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11142', '重新推送终端分组卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11143', '激活分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11144', '更新终端分组卸载应用的推送限制', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11145', '重置分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11146', '挂起分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11147', '获取分组推送卸载应用终端列表', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11148', '创建分组卸载应用推送终端列表下载任务', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11149', '获取终端分组列表', '/v1/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11150', '创建终端分组', '/v1/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11151', '导入终端到终端分组', '/v1/terminalGroups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11152', '创建分组导入终端模板下载任务', '/v1/terminalGroups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11153', '获取终端分组', '/v1/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11154', '更新终端分组', '/v1/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11155', '删除终端分组', '/v1/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11156', '激活终端分组', '/v1/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11157', '停用终端分组', '/v1/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11158', '根据groupId查询签名公钥', '/v1/terminalGroups/{groupId}/signaturePuk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11159', '获取分组终端列表', '/v1/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11160', '添加分组终端', '/v1/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11161', '删除分组终端', '/v1/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11162', '获取终端推送RKI列表', '/v1/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11163', '创建终端推送RKI', '/v1/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11164', '获取终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11165', '删除终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11166', '激活终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11167', '重置终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11168', '挂起终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11169', '获取终端设备库存列表', '/v1/terminalStocks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11170', '批量创建终端设备库存', '/v1/terminalStocks/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11171', '批量分配库存终端', '/v1/terminalStocks/batch/assign', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11172', '批量删除库存终端', '/v1/terminalStocks/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11173', '创建库存终端导出下载任务', '/v1/terminalStocks/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11174', '导入库存终端', '/v1/terminalStocks/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11175', '创建库存终端导入模板下载任务', '/v1/terminalStocks/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11176', '根据序列号获取库存终端', '/v1/terminalStocks/serialNo/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11177', '获取库存终端', '/v1/terminalStocks/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11178', '更新库存终端', '/v1/terminalStocks/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11179', '删除库存终端', '/v1/terminalStocks/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11180', '获取终端变量列表', '/v1/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11181', '创建终端变量', '/v1/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11182', '批量删除终端变量', '/v1/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11183', '导入终端变量', '/v1/terminalVariables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11184', '创建终端变量导入模板下载任务', '/v1/terminalVariables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11185', '查找终端变量支持的应用列表', '/v1/terminalVariables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11186', '查找终端变量已使用的应用列表', '/v1/terminalVariables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11187', '更新终端变量', '/v1/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11188', '删除终端变量', '/v1/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11189', '获取终端列表', '/v1/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11190', '创建终端', '/v1/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11191', '批量创建终端', '/v1/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11192', '批量激活终端', '/v1/terminals/batch/activation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11193', '批量删除终端', '/v1/terminals/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11194', '批量更新终端代理商', '/v1/terminals/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11195', '批量停用终端', '/v1/terminals/batch/suspension', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11196', '清除地理围栏中心点', '/v1/terminals/clear/safe/range/{terminalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11197', '复制终端', '/v1/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11198', '获取SIM运营商信息', '/v1/terminals/detail/sim/operator', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11199', '创建终端导出下载任务', '/v1/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11200', '批量添加终端到分组', '/v1/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11201', '导入终端', '/v1/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11202', '导入批量处理的终端-删除', '/v1/terminals/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11203', '导入批量处理的终端-移动', '/v1/terminals/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11204', '创建终端导入批量处理模板', '/v1/terminals/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11205', '导入批量处理的终端-停用', '/v1/terminals/import/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11206', '创建终端导入模板下载任务', '/v1/terminals/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11207', '创建终端日志下载任务', '/v1/terminals/logs/{terminalLogId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11208', '快捷搜索终端', '/v1/terminals/quick/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11209', '更改终端级别地理围栏半径', '/v1/terminals/radius', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11210', '添加终端安全位置', '/v1/terminals/safeRange', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11211', '获取终端外接详情信息', '/v1/terminals/{accessoryId}/accessory/details', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11212', '获取终端', '/v1/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11213', '更新终端', '/v1/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11214', '删除终端', '/v1/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11215', '获取终端外接详情信息', '/v1/terminals/{terminalId}/accessory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11216', '激活终端', '/v1/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11217', '向终端推送AirViewer安装命令', '/v1/terminals/{terminalId}/airviewer/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11218', '向终端推送AirViewer启动命令', '/v1/terminals/{terminalId}/airviewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11219', '获取终端的操作日志', '/v1/terminals/{terminalId}/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11220', '获取终端的操作日志', '/v1/terminals/{terminalId}/audit/operations/{auditId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11221', '创建终端商业数据下载任务', '/v1/terminals/{terminalId}/bizData/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11222', '获取终端商业数据', '/v1/terminals/{terminalId}/bizDatas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11223', '收集终端logcat', '/v1/terminals/{terminalId}/collect/logcat/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11224', 'web查询终端配置', '/v1/terminals/{terminalId}/configurations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11225', '获取终端详情信息', '/v1/terminals/{terminalId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11226', '停用终端', '/v1/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11227', '获取终端硬件状态列表', '/v1/terminals/{terminalId}/hardware', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11228', '获取终端历史在线时长', '/v1/terminals/{terminalId}/history/online', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11229', '查询终端推送PUK历史记录', '/v1/terminals/{terminalId}/history/puk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11230', '获取终端已安装应用列表', '/v1/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11231', '获取终端已安装应用列表统计', '/v1/terminals/{terminalId}/installedApks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11232', '创建终端已安装应用参数数据文件下载任务', '/v1/terminals/{terminalId}/installedApks/{apkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11233', '查看终端已安装应用参数', '/v1/terminals/{terminalId}/installedApks/{apkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11234', '获取终端已安装应用详情', '/v1/terminals/{terminalId}/installedApks/{installedApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11235', '卸载终端已安装应用', '/v1/terminals/{terminalId}/installedApks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11236', '获取终端已安装固件', '/v1/terminals/{terminalId}/installedFirmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11237', '获取终端位置信息', '/v1/terminals/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11238', '获取终端日志列表', '/v1/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11239', '检查CheckUp应用版本', '/v1/terminals/{terminalId}/lowerCheckupVersion', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11240', '获取终端手动安装应用列表', '/v1/terminals/{terminalId}/manual/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11241', '获取终端监控信息', '/v1/terminals/{terminalId}/monitors', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11242', '更新终端代理商', '/v1/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11243', '推送终端命令', '/v1/terminals/{terminalId}/operation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11244', 'get terminal ped key injection status', '/v1/terminals/{terminalId}/ped/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11245', '查询签名公钥', '/v1/terminals/{terminalId}/puk/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11246', '向终端推送CheckUp应用', '/v1/terminals/{terminalId}/push/checkup', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11247', '获取后端给终端推送命令记录列表', '/v1/terminals/{terminalId}/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11248', '刷新终端地图定位', '/v1/terminals/{terminalId}/refresh/map', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11249', '收集终端详情(硬件等)', '/v1/terminals/{terminalId}/refresh/terminalDetail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11250', '更新终端是否允许远程换机配置', '/v1/terminals/{terminalId}/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11251', '获取终端的换机日志', '/v1/terminals/{terminalId}/replace/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11252', '还原终端数据', '/v1/terminals/{terminalId}/restore', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11253', '获取终端安全位置信息', '/v1/terminals/{terminalId}/safeRange', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11254', '获取终端流量', '/v1/terminals/{terminalId}/traffic', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11255', '查询所有用户列表', '/v1/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11256', '验证当前用户验证码', '/v1/users/captcha/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11257', '查询用户有开发者权限的应用市场', '/v1/users/developer/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11258', '获取当前用户开发者信息', '/v1/users/developers/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11259', '创建用户导出下载任务', '/v1/users/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11260', '查询用户绑定的设备所属的应用市场列表', '/v1/users/merchant/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11261', '验证当前用户OTP', '/v1/users/otp/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11262', '查询用户有代理商权限的应用市场', '/v1/users/reseller/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11263', '获取用户信息', '/v1/users/{userId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11264', '注销删除用户', '/v1/users/{userId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11265', '全局市场激活用户', '/v1/users/{userId}/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11266', '更改邮箱', '/v1/users/{userId}/changeEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11267', '全局市场停用用户', '/v1/users/{userId}/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11268', '用户更改密码', '/v1/users/{userId}/resetPassword', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11269', '移除用户所有角色', '/v1/users/{userId}/roles', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11270', '移除用户角色', '/v1/users/{userId}/roles/{roleId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11271', 'appscan get apk downloadUrl from pax', '/v1/vas/appscan/download-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11272', 'updateLocalMpushDeletagorSecret', '/v1/vas/cloudmsg/push-delegator', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11273', 'Update push server list', '/v1/vas/cloudmsg/pushServer', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11274', 'Batch Notify terminal online/offline', '/v1/vas/cloudmsg/terminalOnlineStatus/batch', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11275', 'getDcCodeByMarketId', '/v1/vas/dc-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11276', 'getDcList', '/v1/vas/dcs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11277', 'Search app list by user market admin role', '/v1/vas/insight/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11278', 'Search app list by user developer role', '/v1/vas/insight/apps/develop', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11279', 'allow app sync bizData', '/v1/vas/insight/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11280', 'control app sync bizData', '/v1/vas/insight/apps/{appId}/bizdata/control', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11281', 'Search merchant page by user', '/v1/vas/insight/developers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11282', 'Search merchant page by user', '/v1/vas/insight/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11283', 'Search reseller page by user', '/v1/vas/insight/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11284', 'Get market reseller tree', '/v1/vas/insight/resellers/tree/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11285', 'Search developer', '/v1/vas/insight/sync/developers', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11286', 'Search factory', '/v1/vas/insight/sync/factories', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11287', 'Search market', '/v1/vas/insight/sync/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11288', 'Search merchant', '/v1/vas/insight/sync/merchants', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11289', 'Search model', '/v1/vas/insight/sync/models', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11290', 'Search reseller', '/v1/vas/insight/sync/resellers', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11291', 'Search terminal', '/v1/vas/insight/sync/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11292', 'Fetch terminal count by status', '/v1/vas/insight/sync/{marketId}/terminal-count', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11293', 'Re sync terminal info by ids', '/v1/vas/insight/sync/{marketId}/terminal-re-sync', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11294', 'Search terminal page by user', '/v1/vas/insight/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11295', 'Add insight user detail', '/v1/vas/insight/users', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11296', 'Get one organization role user list', '/v1/vas/insight/users/role', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11297', 'Get paxstore role list by request', '/v1/vas/insight/users/role/all', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11298', 'Get paxstore role list by request', '/v1/vas/insight/users/role/scope', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11299', 'Verify user list', '/v1/vas/insight/users/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11300', 'fetchMarketPage', '/v1/vas/market-svc-setting/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11301', 'getMarketServiceSetting', '/v1/vas/market-svc-setting/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11302', 'Get one reseller role list', '/v1/vas/market/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11303', 'Get one organization role user list', '/v1/vas/market/roles/{roleId}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11304', 'Push New Engine Info To Pax', '/v1/vas/platform/engine', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11305', 'Get engines from pax', '/v1/vas/platform/engines', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11306', 'getMarketsByServiceType', '/v1/vas/platform/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11307', 'queryEnabledVasServices', '/v1/vas/platform/service', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11308', 'Enable service callback', '/v1/vas/platform/service', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11309', 'syncServiceDetail', '/v1/vas/platform/service', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11310', 'Disable service callback', '/v1/vas/platform/service', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11311', 'Query Service Usage By Year and Month', '/v1/vas/platform/service-usage/{serviceType}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11312', 'enableVasGlobally', '/v1/vas/platform/service/global', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11313', 'getUserDetail', '/v1/vas/user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11314', 'getNotificationCount', '/v1/vas/user-notifications', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11315', 'publishNotification', '/v1/vas/user-notifications', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11316', 'getVersion', '/v1/vas/version', 'GET', null, '0', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '10127'),
  ('1', '10128'),
  ('1', '10129'),
  ('1', '10130'),
  ('1', '10699'),
  ('1', '10782'),
  ('1', '11256'),
  ('1', '11257'),
  ('1', '11258'),
  ('1', '11259'),
  ('1', '11260'),
  ('1', '11261'),
  ('1', '11262'),
  ('1', '11264'),
  ('1', '11268'),

  ('2', '10543'),
  ('2', '10544'),
  ('2', '10545'),
  ('2', '10546'),
  ('2', '10547'),
  ('2', '10548'),
  ('2', '10549'),
  ('2', '10550'),
  ('2', '10551'),
  ('2', '10552'),
  ('2', '10553'),
  ('2', '10554'),
  ('2', '10555'),
  ('2', '10556'),
  ('2', '10557'),
  ('2', '10558'),
  ('2', '10559'),
  ('2', '10560'),
  ('2', '10561'),
  ('2', '10562'),
  ('2', '10563'),
  ('2', '10564'),
  ('2', '10565'),
  ('2', '10566'),
  ('2', '10567'),
  ('2', '10568'),
  ('2', '10569'),
  ('2', '10573'),
  ('2', '10574'),
  ('2', '10576'),
  ('2', '10577'),
  ('2', '10578'),
  ('2', '10579'),
  ('2', '10580'),
  ('2', '10581'),
  ('2', '10582'),
  ('2', '10583'),
  ('2', '10584'),
  ('2', '10585'),
  ('2', '10586'),
  ('2', '10587'),
  ('2', '10588'),
  ('2', '10589'),
  ('2', '10590'),
  ('2', '10591'),
  ('2', '10592'),
  ('2', '10593'),
  ('2', '10594'),
  ('2', '10595'),
  ('2', '10596'),
  ('2', '10597'),
  ('2', '10598'),
  ('2', '10599'),
  ('2', '10600'),
  ('2', '10601'),
  ('2', '10602'),
  ('2', '10603'),
  ('2', '10604'),
  ('2', '10605'),
  ('2', '10606'),
  ('2', '10607'),
  ('2', '10608'),
  ('2', '10609'),
  ('2', '10610'),
  ('2', '10611'),
  ('2', '10612'),
  ('2', '10613'),
  ('2', '10614'),
  ('2', '10615'),
  ('2', '10616'),
  ('2', '10617'),
  ('2', '10618'),
  ('2', '10619'),
  ('2', '10620'),
  ('2', '10621'),
  ('2', '10622'),
  ('2', '10623'),
  ('2', '10624'),
  ('2', '10625'),
  ('2', '10626'),
  ('2', '10627'),
  ('2', '10628'),
  ('2', '10629'),
  ('2', '10630'),
  ('2', '10631'),
  ('2', '10632'),
  ('2', '10633'),
  ('2', '10634'),
  ('2', '10635'),
  ('2', '10636'),
  ('2', '10637'),
  ('2', '10638'),
  ('2', '10639'),
  ('2', '10640'),
  ('2', '10641'),
  ('2', '10642'),
  ('2', '10643'),
  ('2', '10644'),
  ('2', '10645'),
  ('2', '10646'),
  ('2', '10647'),
  ('2', '10648'),
  ('2', '10649'),
  ('2', '10650'),
  ('2', '10651'),
  ('2', '10652'),
  ('2', '10653'),
  ('2', '10654'),
  ('2', '10655'),
  ('2', '10656'),
  ('2', '10657'),
  ('2', '10658'),
  ('2', '10659'),
  ('2', '10660'),
  ('2', '10661'),
  ('2', '10662'),
  ('2', '10663'),
  ('2', '10664'),
  ('2', '10665'),
  ('2', '10666'),
  ('2', '10667'),
  ('2', '10668'),
  ('2', '10669'),
  ('2', '10670'),
  ('2', '10671'),
  ('2', '10672'),
  ('2', '10673'),
  ('2', '10674'),
  ('2', '10675'),
  ('2', '10676'),
  ('2', '10677'),
  ('2', '10678'),
  ('2', '10679'),
  ('2', '10680'),
  ('2', '10681'),
  ('2', '10682'),
  ('2', '10683'),
  ('2', '10684'),
  ('2', '10685'),
  ('2', '10686'),
  ('2', '10687'),
  ('2', '10688'),
  ('2', '10689'),
  ('2', '10690'),
  ('2', '10691'),
  ('2', '10692'),
  ('2', '10693'),
  ('2', '10694'),
  ('2', '10695'),
  ('2', '10696'),
  ('2', '11263'),

  ('3', '10191'),
  ('3', '10192'),
  ('3', '10193'),
  ('3', '10194'),
  ('3', '10195'),
  ('3', '10196'),
  ('3', '10197'),
  ('3', '10198'),
  ('3', '10199'),
  ('3', '10200'),
  ('3', '10201'),
  ('3', '10202'),
  ('3', '10935'),
  ('3', '10936'),
  ('3', '10937'),
  ('3', '10938'),
  ('3', '10939'),
  ('3', '10940'),
  ('3', '10941'),
  ('3', '10942'),
  ('3', '10993'),
  ('3', '10994'),
  ('3', '10995'),
  ('3', '10996'),
  ('3', '10997'),
  ('3', '10998'),
  ('3', '10999'),
  ('3', '11000'),
  ('3', '11009'),
  ('3', '11010'),
  ('3', '11011'),
  ('3', '11018'),
  ('3', '11019'),
  ('3', '11020'),
  ('3', '11021'),
  ('3', '11022'),
  ('3', '11023'),
  ('3', '11024'),
  ('3', '11025'),

  ('20', '10849'),
  ('20', '10850'),
  ('20', '10851'),
  ('20', '10852'),

  ('30', '10885'),
  ('30', '10886'),
  ('30', '10887'),
  ('30', '10888'),
  ('30', '10889'),
  ('30', '10890'),
  ('30', '10891'),
  ('30', '10892'),
  ('30', '10893'),
  ('30', '10894'),
  ('30', '10895'),
  ('30', '10896'),
  ('30', '10897'),
  ('30', '10898'),
  ('30', '10899'),

  ('51', '10135'),
  ('51', '10186'),
  ('51', '10187'),
  ('51', '10188'),
  ('51', '10189'),
  ('51', '10190'),
  ('51', '10191'),
  ('51', '10192'),
  ('51', '10193'),
  ('51', '10194'),
  ('51', '10195'),
  ('51', '10196'),
  ('51', '10197'),
  ('51', '10198'),
  ('51', '10199'),
  ('51', '10200'),
  ('51', '10201'),
  ('51', '10202'),
  ('51', '10203'),
  ('51', '10204'),
  ('51', '10205'),
  ('51', '10206'),
  ('51', '10207'),
  ('51', '10208'),
  ('51', '10237'),
  ('51', '10241'),
  ('51', '10244'),

  ('53', '10237'),
  ('53', '10239'),
  ('53', '10240'),
  ('53', '10241'),
  ('53', '10242'),
  ('53', '10243'),
  ('53', '10244'),
  ('53', '11189'),
  ('53', '11212'),
  ('53', '11225'),
  ('53', '11230'),
  ('53', '11237'),

  ('1041', '10430'),
  ('1041', '10431'),
  ('1041', '10433'),
  ('1041', '10434'),
  ('1041', '10435'),
  ('1041', '10702'),
  ('1041', '10968'),
  ('1041', '10973'),
  ('1041', '11001'),
  ('1041', '11012'),

  ('1042', '10430'),
  ('1042', '10431'),
  ('1042', '10432'),
  ('1042', '10433'),
  ('1042', '10434'),
  ('1042', '10435'),
  ('1042', '10702'),
  ('1042', '10968'),
  ('1042', '10973'),
  ('1042', '11001'),
  ('1042', '11012'),

  ('601', '10136'),
  ('601', '10137'),
  ('601', '10145'),
  ('601', '10149'),
  ('601', '10150'),
  ('601', '10154'),
  ('601', '10160'),
  ('601', '10172'),
  ('601', '10173'),
  ('601', '10174'),
  ('601', '10176'),
  ('601', '10229'),
  ('601', '10232'),
  ('601', '10233'),
  ('601', '10700'),
  ('601', '10702'),
  ('601', '10715'),
  ('601', '10716'),
  ('601', '10854'),
  ('601', '10968'),
  ('601', '10973'),

  ('602', '10136'),
  ('602', '10137'),
  ('602', '10139'),
  ('602', '10145'),
  ('602', '10149'),
  ('602', '10150'),
  ('602', '10154'),
  ('602', '10160'),
  ('602', '10161'),
  ('602', '10162'),
  ('602', '10172'),
  ('602', '10173'),
  ('602', '10174'),
  ('602', '10175'),
  ('602', '10176'),
  ('602', '10178'),
  ('602', '10179'),
  ('602', '10229'),
  ('602', '10230'),
  ('602', '10231'),
  ('602', '10232'),
  ('602', '10233'),
  ('602', '10234'),
  ('602', '10235'),
  ('602', '10236'),
  ('602', '10700'),
  ('602', '10702'),
  ('602', '10715'),
  ('602', '10716'),
  ('602', '10854'),
  ('602', '10968'),
  ('602', '10973'),

  ('611', '10135'),
  ('611', '10136'),
  ('611', '10137'),
  ('611', '10140'),
  ('611', '10145'),
  ('611', '10149'),
  ('611', '10150'),
  ('611', '10154'),
  ('611', '10160'),
  ('611', '10171'),
  ('611', '10172'),
  ('611', '10173'),
  ('611', '10174'),
  ('611', '10176'),
  ('611', '10702'),
  ('611', '10826'),
  ('611', '10854'),
  ('611', '10900'),
  ('611', '10968'),
  ('611', '10973'),

  ('612', '10135'),
  ('612', '10136'),
  ('612', '10137'),
  ('612', '10138'),
  ('612', '10139'),
  ('612', '10140'),
  ('612', '10141'),
  ('612', '10145'),
  ('612', '10146'),
  ('612', '10147'),
  ('612', '10148'),
  ('612', '10149'),
  ('612', '10150'),
  ('612', '10151'),
  ('612', '10152'),
  ('612', '10153'),
  ('612', '10154'),
  ('612', '10155'),
  ('612', '10156'),
  ('612', '10157'),
  ('612', '10158'),
  ('612', '10159'),
  ('612', '10160'),
  ('612', '10161'),
  ('612', '10162'),
  ('612', '10163'),
  ('612', '10164'),
  ('612', '10165'),
  ('612', '10166'),
  ('612', '10167'),
  ('612', '10168'),
  ('612', '10169'),
  ('612', '10170'),
  ('612', '10171'),
  ('612', '10172'),
  ('612', '10173'),
  ('612', '10174'),
  ('612', '10175'),
  ('612', '10176'),
  ('612', '10177'),
  ('612', '10178'),
  ('612', '10179'),
  ('612', '10180'),
  ('612', '10702'),
  ('612', '10826'),
  ('612', '10854'),
  ('612', '10900'),
  ('612', '10968'),
  ('612', '10973'),

  ('661', '10233'),
  ('661', '10702'),
  ('661', '10711'),
  ('661', '10712'),
  ('661', '10715'),
  ('661', '10716'),
  ('661', '10826'),
  ('661', '10968'),
  ('661', '10973'),

  ('662', '10233'),
  ('662', '10234'),
  ('662', '10702'),
  ('662', '10711'),
  ('662', '10712'),
  ('662', '10713'),
  ('662', '10714'),
  ('662', '10715'),
  ('662', '10716'),
  ('662', '10717'),
  ('662', '10718'),
  ('662', '10719'),
  ('662', '10720'),
  ('662', '10721'),
  ('662', '10722'),
  ('662', '10723'),
  ('662', '10724'),
  ('662', '10826'),
  ('662', '10968'),
  ('662', '10973'),

  ('621', '10135'),
  ('621', '10145'),
  ('621', '10149'),
  ('621', '10150'),
  ('621', '10154'),
  ('621', '10160'),
  ('621', '10171'),
  ('621', '10173'),
  ('621', '10174'),
  ('621', '10176'),
  ('621', '10209'),
  ('621', '10210'),
  ('621', '10223'),
  ('621', '10228'),
  ('621', '10702'),
  ('621', '10968'),
  ('621', '10973'),

  ('622', '10135'),
  ('622', '10145'),
  ('622', '10146'),
  ('622', '10147'),
  ('622', '10148'),
  ('622', '10149'),
  ('622', '10150'),
  ('622', '10151'),
  ('622', '10152'),
  ('622', '10153'),
  ('622', '10154'),
  ('622', '10155'),
  ('622', '10156'),
  ('622', '10157'),
  ('622', '10158'),
  ('622', '10159'),
  ('622', '10160'),
  ('622', '10161'),
  ('622', '10162'),
  ('622', '10163'),
  ('622', '10164'),
  ('622', '10165'),
  ('622', '10166'),
  ('622', '10167'),
  ('622', '10168'),
  ('622', '10169'),
  ('622', '10170'),
  ('622', '10171'),
  ('622', '10172'),
  ('622', '10173'),
  ('622', '10174'),
  ('622', '10175'),
  ('622', '10176'),
  ('622', '10177'),
  ('622', '10178'),
  ('622', '10179'),
  ('622', '10180'),
  ('622', '10209'),
  ('622', '10210'),
  ('622', '10211'),
  ('622', '10212'),
  ('622', '10213'),
  ('622', '10214'),
  ('622', '10215'),
  ('622', '10216'),
  ('622', '10217'),
  ('622', '10218'),
  ('622', '10219'),
  ('622', '10220'),
  ('622', '10221'),
  ('622', '10222'),
  ('622', '10223'),
  ('622', '10224'),
  ('622', '10225'),
  ('622', '10226'),
  ('622', '10227'),
  ('622', '10228'),
  ('622', '10702'),
  ('622', '10968'),
  ('622', '10973'),

  ('651', '10135'),
  ('651', '10136'),
  ('651', '10137'),
  ('651', '10140'),
  ('651', '10145'),
  ('651', '10149'),
  ('651', '10150'),
  ('651', '10154'),
  ('651', '10160'),
  ('651', '10171'),
  ('651', '10172'),
  ('651', '10173'),
  ('651', '10174'),
  ('651', '10176'),
  ('651', '10702'),
  ('651', '10826'),
  ('651', '10968'),
  ('651', '10970'),
  ('651', '10973'),

  ('652', '10135'),
  ('652', '10136'),
  ('652', '10137'),
  ('652', '10139'),
  ('652', '10140'),
  ('652', '10145'),
  ('652', '10149'),
  ('652', '10150'),
  ('652', '10154'),
  ('652', '10160'),
  ('652', '10161'),
  ('652', '10162'),
  ('652', '10171'),
  ('652', '10172'),
  ('652', '10173'),
  ('652', '10174'),
  ('652', '10176'),
  ('652', '10178'),
  ('652', '10179'),
  ('652', '10702'),
  ('652', '10826'),
  ('652', '10968'),
  ('652', '10970'),
  ('652', '10973'),

  ('711', '10145'),
  ('711', '10150'),
  ('711', '10154'),
  ('711', '10160'),
  ('711', '10173'),
  ('711', '10176'),
  ('711', '10187'),
  ('711', '10188'),
  ('711', '10190'),
  ('711', '10427'),
  ('711', '10428'),
  ('711', '10436'),
  ('711', '10438'),
  ('711', '10439'),
  ('711', '10441'),
  ('711', '10442'),
  ('711', '10443'),
  ('711', '10447'),
  ('711', '10448'),
  ('711', '10463'),
  ('711', '10700'),
  ('711', '10702'),
  ('711', '10711'),
  ('711', '10715'),
  ('711', '10716'),
  ('711', '10854'),
  ('711', '10861'),
  ('711', '10866'),
  ('711', '10867'),
  ('711', '10870'),
  ('711', '10872'),
  ('711', '10875'),
  ('711', '10876'),
  ('711', '10881'),
  ('711', '10900'),
  ('711', '10908'),
  ('711', '10968'),
  ('711', '10970'),
  ('711', '10971'),
  ('711', '10972'),
  ('711', '10973'),
  ('711', '10974'),
  ('711', '10975'),
  ('711', '10980'),
  ('711', '10982'),
  ('711', '10986'),
  ('711', '10988'),
  ('711', '10989'),
  ('711', '11033'),
  ('711', '11035'),
  ('711', '11036'),
  ('711', '11039'),
  ('711', '11040'),
  ('711', '11043'),
  ('711', '11047'),
  ('711', '11049'),
  ('711', '11058'),
  ('711', '11062'),
  ('711', '11065'),
  ('711', '11077'),
  ('711', '11082'),
  ('711', '11091'),
  ('711', '11129'),
  ('711', '11137'),
  ('711', '11149'),
  ('711', '11162'),
  ('711', '11164'),
  ('711', '11176'),
  ('711', '11180'),
  ('711', '11185'),
  ('711', '11186'),
  ('711', '11189'),
  ('711', '11198'),
  ('711', '11199'),
  ('711', '11207'),
  ('711', '11208'),
  ('711', '11211'),
  ('711', '11212'),
  ('711', '11215'),
  ('711', '11217'),
  ('711', '11218'),
  ('711', '11219'),
  ('711', '11220'),
  ('711', '11222'),
  ('711', '11224'),
  ('711', '11225'),
  ('711', '11227'),
  ('711', '11228'),
  ('711', '11229'),
  ('711', '11230'),
  ('711', '11231'),
  ('711', '11232'),
  ('711', '11233'),
  ('711', '11234'),
  ('711', '11236'),
  ('711', '11237'),
  ('711', '11238'),
  ('711', '11239'),
  ('711', '11240'),
  ('711', '11241'),
  ('711', '11244'),
  ('711', '11245'),
  ('711', '11246'),
  ('711', '11247'),
  ('711', '11248'),
  ('711', '11249'),
  ('711', '11251'),
  ('711', '11253'),
  ('711', '11254'),

  ('712', '10145'),
  ('712', '10150'),
  ('712', '10154'),
  ('712', '10160'),
  ('712', '10173'),
  ('712', '10176'),
  ('712', '10187'),
  ('712', '10188'),
  ('712', '10190'),
  ('712', '10427'),
  ('712', '10428'),
  ('712', '10436'),
  ('712', '10438'),
  ('712', '10439'),
  ('712', '10441'),
  ('712', '10442'),
  ('712', '10443'),
  ('712', '10447'),
  ('712', '10448'),
  ('712', '10463'),
  ('712', '10700'),
  ('712', '10702'),
  ('712', '10711'),
  ('712', '10715'),
  ('712', '10716'),
  ('712', '10854'),
  ('712', '10861'),
  ('712', '10866'),
  ('712', '10867'),
  ('712', '10870'),
  ('712', '10872'),
  ('712', '10875'),
  ('712', '10876'),
  ('712', '10881'),
  ('712', '10900'),
  ('712', '10908'),
  ('712', '10968'),
  ('712', '10970'),
  ('712', '10971'),
  ('712', '10972'),
  ('712', '10973'),
  ('712', '10974'),
  ('712', '10975'),
  ('712', '10980'),
  ('712', '10982'),
  ('712', '10986'),
  ('712', '10988'),
  ('712', '10989'),
  ('712', '11033'),
  ('712', '11035'),
  ('712', '11036'),
  ('712', '11039'),
  ('712', '11040'),
  ('712', '11043'),
  ('712', '11047'),
  ('712', '11049'),
  ('712', '11058'),
  ('712', '11062'),
  ('712', '11065'),
  ('712', '11077'),
  ('712', '11082'),
  ('712', '11091'),
  ('712', '11129'),
  ('712', '11137'),
  ('712', '11149'),
  ('712', '11162'),
  ('712', '11164'),
  ('712', '11176'),
  ('712', '11180'),
  ('712', '11185'),
  ('712', '11186'),
  ('712', '11189'),
  ('712', '11198'),
  ('712', '11199'),
  ('712', '11207'),
  ('712', '11208'),
  ('712', '11211'),
  ('712', '11212'),
  ('712', '11215'),
  ('712', '11217'),
  ('712', '11218'),
  ('712', '11219'),
  ('712', '11220'),
  ('712', '11222'),
  ('712', '11224'),
  ('712', '11225'),
  ('712', '11227'),
  ('712', '11228'),
  ('712', '11229'),
  ('712', '11230'),
  ('712', '11231'),
  ('712', '11232'),
  ('712', '11233'),
  ('712', '11234'),
  ('712', '11236'),
  ('712', '11237'),
  ('712', '11238'),
  ('712', '11239'),
  ('712', '11240'),
  ('712', '11241'),
  ('712', '11244'),
  ('712', '11245'),
  ('712', '11246'),
  ('712', '11247'),
  ('712', '11248'),
  ('712', '11249'),
  ('712', '11251'),
  ('712', '11253'),
  ('712', '11254'),

  ('7124', '11047'),
  ('7124', '11048'),
  ('7124', '11049'),
  ('7124', '11050'),
  ('7124', '11051'),
  ('7124', '11052'),
  ('7124', '11053'),

  ('71211', '10870'),
  ('71211', '10871'),
  ('71211', '10872'),
  ('71211', '10873'),
  ('71211', '10874'),
  ('71211', '10875'),
  ('71211', '10876'),
  ('71211', '10877'),
  ('71211', '10878'),
  ('71211', '10879'),
  ('71211', '10880'),
  ('71211', '10881'),
  ('71211', '10883'),
  ('71211', '10884'),
  ('71211', '10968'),
  ('71211', '10969'),
  ('71211', '10970'),
  ('71211', '10971'),
  ('71211', '10972'),
  ('71211', '10973'),
  ('71211', '10974'),
  ('71211', '10975'),
  ('71211', '10976'),
  ('71211', '10977'),
  ('71211', '10978'),
  ('71211', '10979'),
  ('71211', '10980'),
  ('71211', '10981'),
  ('71211', '10982'),
  ('71211', '10984'),
  ('71211', '10985'),
  ('71211', '10986'),
  ('71211', '10987'),
  ('71211', '10988'),
  ('71211', '10989'),
  ('71211', '10990'),
  ('71211', '10991'),
  ('71211', '10992'),

  ('71212', '10882'),
  ('71212', '10983'),

  ('71221', '11189'),
  ('71221', '11190'),
  ('71221', '11191'),
  ('71221', '11192'),
  ('71221', '11193'),
  ('71221', '11194'),
  ('71221', '11195'),
  ('71221', '11197'),
  ('71221', '11198'),
  ('71221', '11199'),
  ('71221', '11200'),
  ('71221', '11201'),
  ('71221', '11202'),
  ('71221', '11203'),
  ('71221', '11204'),
  ('71221', '11205'),
  ('71221', '11206'),
  ('71221', '11207'),
  ('71221', '11208'),
  ('71221', '11211'),
  ('71221', '11212'),
  ('71221', '11213'),
  ('71221', '11214'),
  ('71221', '11215'),
  ('71221', '11216'),
  ('71221', '11217'),
  ('71221', '11218'),
  ('71221', '11219'),
  ('71221', '11220'),
  ('71221', '11221'),
  ('71221', '11222'),
  ('71221', '11224'),
  ('71221', '11225'),
  ('71221', '11226'),
  ('71221', '11227'),
  ('71221', '11228'),
  ('71221', '11229'),
  ('71221', '11230'),
  ('71221', '11231'),
  ('71221', '11232'),
  ('71221', '11233'),
  ('71221', '11234'),
  ('71221', '11235'),
  ('71221', '11236'),
  ('71221', '11237'),
  ('71221', '11238'),
  ('71221', '11239'),
  ('71221', '11240'),
  ('71221', '11241'),
  ('71221', '11242'),
  ('71221', '11244'),
  ('71221', '11245'),
  ('71221', '11246'),
  ('71221', '11247'),
  ('71221', '11249'),
  ('71221', '11251'),
  ('71221', '11252'),
  ('71221', '11253'),
  ('71221', '11254'),

  ('71222', '11196'),
  ('71222', '11209'),
  ('71222', '11210'),
  ('71222', '11223'),
  ('71222', '11243'),
  ('71222', '11248'),
  ('71222', '11250'),

  ('71231', '11033'),
  ('71231', '11034'),
  ('71231', '11035'),
  ('71231', '11036'),
  ('71231', '11037'),
  ('71231', '11038'),
  ('71231', '11039'),
  ('71231', '11040'),
  ('71231', '11043'),
  ('71231', '11045'),
  ('71231', '11046'),

  ('71232', '10861'),
  ('71232', '10862'),
  ('71232', '10863'),
  ('71232', '10864'),
  ('71232', '10865'),
  ('71232', '10866'),
  ('71232', '10867'),
  ('71232', '10868'),
  ('71232', '10869'),
  ('71232', '11033'),
  ('71232', '11034'),
  ('71232', '11035'),
  ('71232', '11036'),
  ('71232', '11037'),
  ('71232', '11038'),
  ('71232', '11039'),
  ('71232', '11040'),
  ('71232', '11041'),
  ('71232', '11042'),
  ('71232', '11043'),
  ('71232', '11044'),
  ('71232', '11045'),
  ('71232', '11046'),
  ('71232', '11180'),
  ('71232', '11181'),
  ('71232', '11182'),
  ('71232', '11183'),
  ('71232', '11184'),
  ('71232', '11185'),
  ('71232', '11186'),
  ('71232', '11187'),
  ('71232', '11188'),

  ('71233', '11235'),

  ('71251', '11162'),
  ('71251', '11163'),
  ('71251', '11164'),
  ('71251', '11165'),
  ('71251', '11166'),
  ('71251', '11167'),
  ('71251', '11168'),

  ('71252', '10986'),
  ('71252', '10987'),
  ('71252', '10988'),
  ('71252', '10989'),
  ('71252', '10990'),
  ('71252', '10991'),
  ('71252', '10992'),

  ('721', '10136'),
  ('721', '10137'),
  ('721', '10150'),
  ('721', '10404'),
  ('721', '10436'),
  ('721', '10438'),
  ('721', '10439'),
  ('721', '10441'),
  ('721', '10442'),
  ('721', '10443'),
  ('721', '10447'),
  ('721', '10448'),
  ('721', '10700'),
  ('721', '10702'),
  ('721', '10711'),
  ('721', '10715'),
  ('721', '10716'),
  ('721', '10777'),
  ('721', '10779'),
  ('721', '10854'),
  ('721', '10870'),
  ('721', '10900'),
  ('721', '10908'),
  ('721', '10968'),
  ('721', '10973'),
  ('721', '11054'),
  ('721', '11058'),
  ('721', '11062'),
  ('721', '11065'),
  ('721', '11069'),
  ('721', '11071'),
  ('721', '11072'),
  ('721', '11076'),
  ('721', '11077'),
  ('721', '11078'),
  ('721', '11082'),
  ('721', '11090'),
  ('721', '11091'),
  ('721', '11092'),
  ('721', '11096'),
  ('721', '11102'),
  ('721', '11104'),
  ('721', '11107'),
  ('721', '11108'),
  ('721', '11109'),
  ('721', '11111'),
  ('721', '11118'),
  ('721', '11119'),
  ('721', '11127'),
  ('721', '11129'),
  ('721', '11136'),
  ('721', '11137'),
  ('721', '11138'),
  ('721', '11140'),
  ('721', '11147'),
  ('721', '11148'),
  ('721', '11149'),
  ('721', '11153'),
  ('721', '11158'),
  ('721', '11159'),
  ('721', '11189'),
  ('721', '11198'),

  ('722', '10136'),
  ('722', '10137'),
  ('722', '10150'),
  ('722', '10404'),
  ('722', '10436'),
  ('722', '10438'),
  ('722', '10439'),
  ('722', '10441'),
  ('722', '10442'),
  ('722', '10443'),
  ('722', '10447'),
  ('722', '10448'),
  ('722', '10700'),
  ('722', '10702'),
  ('722', '10711'),
  ('722', '10715'),
  ('722', '10716'),
  ('722', '10777'),
  ('722', '10779'),
  ('722', '10854'),
  ('722', '10870'),
  ('722', '10900'),
  ('722', '10908'),
  ('722', '10968'),
  ('722', '10973'),
  ('722', '11054'),
  ('722', '11058'),
  ('722', '11062'),
  ('722', '11065'),
  ('722', '11069'),
  ('722', '11071'),
  ('722', '11072'),
  ('722', '11076'),
  ('722', '11077'),
  ('722', '11078'),
  ('722', '11082'),
  ('722', '11090'),
  ('722', '11091'),
  ('722', '11092'),
  ('722', '11096'),
  ('722', '11102'),
  ('722', '11104'),
  ('722', '11107'),
  ('722', '11108'),
  ('722', '11109'),
  ('722', '11111'),
  ('722', '11118'),
  ('722', '11119'),
  ('722', '11127'),
  ('722', '11129'),
  ('722', '11136'),
  ('722', '11137'),
  ('722', '11138'),
  ('722', '11140'),
  ('722', '11147'),
  ('722', '11148'),
  ('722', '11149'),
  ('722', '11153'),
  ('722', '11158'),
  ('722', '11159'),
  ('722', '11189'),
  ('722', '11198'),

  ('7221', '11149'),
  ('7221', '11150'),
  ('7221', '11151'),
  ('7221', '11152'),
  ('7221', '11153'),
  ('7221', '11154'),
  ('7221', '11155'),
  ('7221', '11156'),
  ('7221', '11157'),
  ('7221', '11158'),
  ('7221', '11159'),
  ('7221', '11160'),
  ('7221', '11161'),

  ('7223', '11078'),
  ('7223', '11079'),
  ('7223', '11080'),
  ('7223', '11081'),
  ('7223', '11082'),
  ('7223', '11083'),
  ('7223', '11084'),
  ('7223', '11085'),
  ('7223', '11086'),
  ('7223', '11087'),
  ('7223', '11088'),
  ('7223', '11089'),
  ('7223', '11090'),
  ('7223', '11091'),

  ('7224', '10987'),
  ('7224', '10988'),
  ('7224', '11127'),
  ('7224', '11128'),
  ('7224', '11129'),
  ('7224', '11130'),
  ('7224', '11131'),
  ('7224', '11132'),
  ('7224', '11133'),
  ('7224', '11134'),
  ('7224', '11135'),
  ('7224', '11136'),
  ('7224', '11137'),

  ('7225', '11109'),
  ('7225', '11110'),
  ('7225', '11111'),
  ('7225', '11112'),
  ('7225', '11113'),
  ('7225', '11114'),
  ('7225', '11115'),
  ('7225', '11116'),
  ('7225', '11117'),
  ('7225', '11118'),
  ('7225', '11119'),

  ('7226', '11120'),
  ('7226', '11121'),
  ('7226', '11122'),
  ('7226', '11123'),
  ('7226', '11124'),
  ('7226', '11125'),
  ('7226', '11126'),

  ('7227', '11092'),
  ('7227', '11093'),
  ('7227', '11094'),
  ('7227', '11095'),
  ('7227', '11096'),
  ('7227', '11097'),
  ('7227', '11098'),
  ('7227', '11099'),
  ('7227', '11100'),
  ('7227', '11101'),
  ('7227', '11102'),
  ('7227', '11103'),
  ('7227', '11104'),
  ('7227', '11105'),
  ('7227', '11106'),
  ('7227', '11107'),
  ('7227', '11108'),

  ('72221', '11054'),
  ('72221', '11055'),
  ('72221', '11056'),
  ('72221', '11057'),
  ('72221', '11058'),
  ('72221', '11059'),
  ('72221', '11060'),
  ('72221', '11061'),
  ('72221', '11062'),
  ('72221', '11063'),
  ('72221', '11064'),
  ('72221', '11065'),
  ('72221', '11069'),
  ('72221', '11071'),
  ('72221', '11072'),
  ('72221', '11074'),
  ('72221', '11075'),
  ('72221', '11076'),
  ('72221', '11077'),

  ('72222', '11054'),
  ('72222', '11055'),
  ('72222', '11056'),
  ('72222', '11057'),
  ('72222', '11058'),
  ('72222', '11059'),
  ('72222', '11060'),
  ('72222', '11061'),
  ('72222', '11062'),
  ('72222', '11063'),
  ('72222', '11064'),
  ('72222', '11065'),
  ('72222', '11066'),
  ('72222', '11067'),
  ('72222', '11068'),
  ('72222', '11069'),
  ('72222', '11070'),
  ('72222', '11071'),
  ('72222', '11072'),
  ('72222', '11073'),
  ('72222', '11074'),
  ('72222', '11075'),
  ('72222', '11076'),
  ('72222', '11077'),

  ('72223', '11138'),
  ('72223', '11139'),
  ('72223', '11140'),
  ('72223', '11141'),
  ('72223', '11142'),
  ('72223', '11143'),
  ('72223', '11144'),
  ('72223', '11145'),
  ('72223', '11146'),
  ('72223', '11147'),
  ('72223', '11148'),

  ('7311', '10136'),
  ('7311', '10137'),
  ('7311', '10145'),
  ('7311', '10150'),
  ('7311', '10154'),
  ('7311', '10160'),
  ('7311', '10173'),
  ('7311', '10176'),
  ('7311', '10436'),
  ('7311', '10438'),
  ('7311', '10439'),
  ('7311', '10441'),
  ('7311', '10442'),
  ('7311', '10443'),
  ('7311', '10447'),
  ('7311', '10448'),
  ('7311', '10700'),
  ('7311', '10702'),
  ('7311', '10900'),
  ('7311', '10968'),
  ('7311', '10973'),

  ('7312', '10136'),
  ('7312', '10137'),
  ('7312', '10145'),
  ('7312', '10150'),
  ('7312', '10154'),
  ('7312', '10160'),
  ('7312', '10173'),
  ('7312', '10176'),
  ('7312', '10436'),
  ('7312', '10437'),
  ('7312', '10438'),
  ('7312', '10439'),
  ('7312', '10440'),
  ('7312', '10441'),
  ('7312', '10442'),
  ('7312', '10443'),
  ('7312', '10444'),
  ('7312', '10445'),
  ('7312', '10446'),
  ('7312', '10447'),
  ('7312', '10448'),
  ('7312', '10700'),
  ('7312', '10702'),
  ('7312', '10900'),
  ('7312', '10968'),
  ('7312', '10973'),

  ('7321', '10777'),
  ('7321', '10779'),
  ('7321', '10970'),
  ('7321', '10971'),

  ('7322', '10777'),
  ('7322', '10778'),
  ('7322', '10779'),
  ('7322', '10780'),
  ('7322', '10781'),
  ('7322', '10970'),
  ('7322', '10971'),

  ('741', '10700'),
  ('741', '10702'),
  ('741', '10703'),
  ('741', '10704'),
  ('741', '10707'),
  ('741', '10826'),
  ('741', '10900'),
  ('741', '10903'),
  ('741', '10905'),
  ('741', '10908'),

  ('742', '10700'),
  ('742', '10701'),
  ('742', '10702'),
  ('742', '10703'),
  ('742', '10704'),
  ('742', '10705'),
  ('742', '10706'),
  ('742', '10707'),
  ('742', '10708'),
  ('742', '10709'),
  ('742', '10826'),
  ('742', '10900'),
  ('742', '10901'),
  ('742', '10902'),
  ('742', '10903'),
  ('742', '10904'),
  ('742', '10905'),
  ('742', '10906'),
  ('742', '10907'),
  ('742', '10908'),

  ('7511', '10700'),
  ('7511', '10702'),
  ('7511', '10711'),
  ('7511', '10712'),
  ('7511', '10715'),
  ('7511', '10716'),

  ('7512', '10700'),
  ('7512', '10702'),
  ('7512', '10711'),
  ('7512', '10712'),
  ('7512', '10713'),
  ('7512', '10714'),
  ('7512', '10715'),
  ('7512', '10716'),
  ('7512', '10717'),
  ('7512', '10718'),
  ('7512', '10720'),
  ('7512', '10724'),

  ('7521', '10229'),
  ('7521', '10232'),
  ('7521', '10233'),
  ('7521', '10700'),
  ('7521', '10702'),
  ('7521', '10711'),
  ('7521', '10712'),
  ('7521', '10715'),
  ('7521', '10716'),
  ('7521', '10826'),
  ('7521', '10968'),
  ('7521', '10973'),

  ('7522', '10229'),
  ('7522', '10230'),
  ('7522', '10231'),
  ('7522', '10232'),
  ('7522', '10233'),
  ('7522', '10234'),
  ('7522', '10235'),
  ('7522', '10236'),
  ('7522', '10700'),
  ('7522', '10702'),
  ('7522', '10711'),
  ('7522', '10712'),
  ('7522', '10714'),
  ('7522', '10715'),
  ('7522', '10716'),
  ('7522', '10718'),
  ('7522', '10719'),
  ('7522', '10720'),
  ('7522', '10721'),
  ('7522', '10722'),
  ('7522', '10723'),
  ('7522', '10826'),
  ('7522', '10968'),
  ('7522', '10973'),

  ('7611', '10492'),
  ('7611', '10494'),
  ('7611', '10495'),
  ('7611', '10496'),
  ('7611', '10503'),
  ('7611', '10505'),
  ('7611', '10510'),
  ('7611', '10700'),
  ('7611', '10900'),

  ('7612', '10492'),
  ('7612', '10494'),
  ('7612', '10495'),
  ('7612', '10496'),
  ('7612', '10503'),
  ('7612', '10504'),
  ('7612', '10505'),
  ('7612', '10506'),
  ('7612', '10507'),
  ('7612', '10508'),
  ('7612', '10510'),
  ('7612', '10511'),
  ('7612', '10515'),
  ('7612', '10516'),
  ('7612', '10700'),
  ('7612', '10900'),

  ('7621', '10493'),
  ('7621', '10494'),
  ('7621', '10495'),
  ('7621', '10496'),
  ('7621', '10501'),
  ('7621', '10510'),

  ('7622', '10493'),
  ('7622', '10494'),
  ('7622', '10495'),
  ('7622', '10496'),
  ('7622', '10497'),
  ('7622', '10498'),
  ('7622', '10499'),
  ('7622', '10500'),
  ('7622', '10501'),
  ('7622', '10508'),
  ('7622', '10509'),
  ('7622', '10510'),
  ('7622', '10512'),
  ('7622', '10513'),
  ('7622', '10514'),

  ('771', '10700'),
  ('771', '10702'),
  ('771', '10826'),
  ('771', '10900'),
  ('771', '10968'),
  ('771', '10973'),
  ('771', '11169'),
  ('771', '11173'),
  ('771', '11176'),
  ('771', '11177'),

  ('772', '10700'),
  ('772', '10702'),
  ('772', '10826'),
  ('772', '10900'),
  ('772', '10968'),
  ('772', '10973'),
  ('772', '11169'),
  ('772', '11170'),
  ('772', '11171'),
  ('772', '11172'),
  ('772', '11173'),
  ('772', '11174'),
  ('772', '11175'),
  ('772', '11176'),
  ('772', '11177'),
  ('772', '11178'),
  ('772', '11179'),

  ('911', '10140'),
  ('911', '10142'),
  ('911', '10246'),
  ('911', '10251'),
  ('911', '10255'),
  ('911', '10256'),
  ('911', '10260'),
  ('911', '10261'),
  ('911', '10271'),
  ('911', '10272'),
  ('911', '10273'),
  ('911', '10274'),
  ('911', '10275'),
  ('911', '10276'),
  ('911', '10277'),
  ('911', '10278'),
  ('911', '10279'),
  ('911', '10280'),
  ('911', '10281'),
  ('911', '10282'),
  ('911', '10283'),
  ('911', '10284'),
  ('911', '10285'),
  ('911', '10286'),
  ('911', '10287'),
  ('911', '10288'),
  ('911', '10290'),
  ('911', '10292'),
  ('911', '10294'),
  ('911', '10295'),
  ('911', '10296'),
  ('911', '10297'),
  ('911', '10298'),
  ('911', '10299'),
  ('911', '10300'),
  ('911', '10301'),
  ('911', '10302'),
  ('911', '10303'),
  ('911', '10304'),
  ('911', '10305'),
  ('911', '10306'),
  ('911', '10307'),
  ('911', '10308'),
  ('911', '10309'),
  ('911', '10312'),
  ('911', '10313'),
  ('911', '10314'),
  ('911', '10315'),
  ('911', '10316'),
  ('911', '10317'),
  ('911', '10322'),
  ('911', '10324'),
  ('911', '10325'),
  ('911', '10326'),
  ('911', '10327'),
  ('911', '10329'),
  ('911', '10331'),
  ('911', '10332'),
  ('911', '10333'),
  ('911', '10336'),
  ('911', '10338'),
  ('911', '10340'),
  ('911', '10342'),
  ('911', '10344'),
  ('911', '10349'),
  ('911', '10351'),
  ('911', '10353'),
  ('911', '10355'),
  ('911', '10360'),
  ('911', '10361'),
  ('911', '10364'),
  ('911', '10402'),
  ('911', '10404'),
  ('911', '10410'),
  ('911', '10413'),
  ('911', '10463'),
  ('911', '10465'),
  ('911', '10700'),
  ('911', '10854'),
  ('911', '10900'),
  ('911', '10975'),
  ('911', '10986'),
  ('911', '10988'),
  ('911', '10989'),

  ('912', '10140'),
  ('912', '10141'),
  ('912', '10142'),
  ('912', '10143'),
  ('912', '10144'),
  ('912', '10168'),
  ('912', '10169'),
  ('912', '10170'),
  ('912', '10245'),
  ('912', '10246'),
  ('912', '10247'),
  ('912', '10248'),
  ('912', '10249'),
  ('912', '10250'),
  ('912', '10251'),
  ('912', '10252'),
  ('912', '10253'),
  ('912', '10254'),
  ('912', '10255'),
  ('912', '10256'),
  ('912', '10257'),
  ('912', '10258'),
  ('912', '10259'),
  ('912', '10260'),
  ('912', '10261'),
  ('912', '10262'),
  ('912', '10263'),
  ('912', '10264'),
  ('912', '10271'),
  ('912', '10272'),
  ('912', '10273'),
  ('912', '10274'),
  ('912', '10275'),
  ('912', '10276'),
  ('912', '10277'),
  ('912', '10278'),
  ('912', '10279'),
  ('912', '10280'),
  ('912', '10281'),
  ('912', '10282'),
  ('912', '10283'),
  ('912', '10284'),
  ('912', '10285'),
  ('912', '10286'),
  ('912', '10287'),
  ('912', '10288'),
  ('912', '10289'),
  ('912', '10290'),
  ('912', '10291'),
  ('912', '10292'),
  ('912', '10293'),
  ('912', '10294'),
  ('912', '10295'),
  ('912', '10296'),
  ('912', '10297'),
  ('912', '10298'),
  ('912', '10299'),
  ('912', '10300'),
  ('912', '10301'),
  ('912', '10302'),
  ('912', '10303'),
  ('912', '10304'),
  ('912', '10305'),
  ('912', '10306'),
  ('912', '10307'),
  ('912', '10308'),
  ('912', '10309'),
  ('912', '10310'),
  ('912', '10311'),
  ('912', '10312'),
  ('912', '10313'),
  ('912', '10314'),
  ('912', '10315'),
  ('912', '10316'),
  ('912', '10317'),
  ('912', '10318'),
  ('912', '10319'),
  ('912', '10320'),
  ('912', '10321'),
  ('912', '10322'),
  ('912', '10323'),
  ('912', '10324'),
  ('912', '10325'),
  ('912', '10326'),
  ('912', '10327'),
  ('912', '10328'),
  ('912', '10329'),
  ('912', '10330'),
  ('912', '10331'),
  ('912', '10332'),
  ('912', '10333'),
  ('912', '10334'),
  ('912', '10335'),
  ('912', '10336'),
  ('912', '10337'),
  ('912', '10338'),
  ('912', '10339'),
  ('912', '10340'),
  ('912', '10341'),
  ('912', '10342'),
  ('912', '10343'),
  ('912', '10344'),
  ('912', '10345'),
  ('912', '10346'),
  ('912', '10347'),
  ('912', '10348'),
  ('912', '10349'),
  ('912', '10350'),
  ('912', '10351'),
  ('912', '10352'),
  ('912', '10353'),
  ('912', '10354'),
  ('912', '10355'),
  ('912', '10356'),
  ('912', '10357'),
  ('912', '10358'),
  ('912', '10359'),
  ('912', '10360'),
  ('912', '10361'),
  ('912', '10362'),
  ('912', '10363'),
  ('912', '10364'),
  ('912', '10402'),
  ('912', '10404'),
  ('912', '10410'),
  ('912', '10411'),
  ('912', '10412'),
  ('912', '10413'),
  ('912', '10414'),
  ('912', '10463'),
  ('912', '10464'),
  ('912', '10465'),
  ('912', '10466'),
  ('912', '10467'),
  ('912', '10468'),
  ('912', '10700'),
  ('912', '10854'),
  ('912', '10855'),
  ('912', '10856'),
  ('912', '10857'),
  ('912', '10858'),
  ('912', '10859'),
  ('912', '10860'),
  ('912', '10900'),
  ('912', '10902'),
  ('912', '10975'),
  ('912', '10986'),
  ('912', '10987'),
  ('912', '10988'),
  ('912', '10989'),
  ('912', '10990'),
  ('912', '10991'),
  ('912', '10992'),

  ('92', '10914'),
  ('92', '10915'),
  ('92', '10916'),
  ('92', '10917'),
  ('92', '10918'),
  ('92', '10919'),
  ('92', '10920'),
  ('92', '10973'),
  ('92', '11001'),
  ('92', '11002'),
  ('92', '11003'),
  ('92', '11004'),
  ('92', '11005'),
  ('92', '11006'),
  ('92', '11007'),
  ('92', '11008'),
  ('92', '11012'),
  ('92', '11013'),
  ('92', '11014'),

  ('931', '10826'),
  ('931', '11003'),
  ('931', '11255'),
  ('931', '11259'),
  ('931', '11263'),
  ('931', '11266'),
  ('931', '11268'),

  ('932', '10826'),
  ('932', '11003'),
  ('932', '11255'),
  ('932', '11259'),
  ('932', '11263'),
  ('932', '11265'),
  ('932', '11266'),
  ('932', '11267'),
  ('932', '11268'),
  ('932', '11269'),
  ('932', '11270'),

  ('95', '10189'),
  ('95', '10265'),
  ('95', '10266'),
  ('95', '10267'),
  ('95', '10268'),
  ('95', '10269'),
  ('95', '10270'),

  ('96', '10375'),
  ('96', '10376'),
  ('96', '10377'),
  ('96', '10378'),
  ('96', '10379'),
  ('96', '10380'),
  ('96', '10381'),
  ('96', '10382'),
  ('96', '10383'),
  ('96', '10384'),
  ('96', '10385'),
  ('96', '10386'),
  ('96', '10387'),
  ('96', '10388'),
  ('96', '10438'),
  ('96', '10439'),
  ('96', '10700'),
  ('96', '10702'),
  ('96', '10826'),
  ('96', '10870'),
  ('96', '10900'),
  ('96', '10944'),
  ('96', '10945'),
  ('96', '10946'),
  ('96', '10947'),
  ('96', '10948'),
  ('96', '10949'),
  ('96', '10950'),
  ('96', '10951'),
  ('96', '10952'),
  ('96', '10953'),
  ('96', '10954'),
  ('96', '10955'),
  ('96', '10956'),
  ('96', '10957'),
  ('96', '10958'),
  ('96', '10959'),
  ('96', '10960'),
  ('96', '10961'),
  ('96', '10962'),
  ('96', '10963'),
  ('96', '10964'),
  ('96', '10968'),
  ('96', '10973'),

  ('1011', '10292'),
  ('1011', '10402'),
  ('1011', '10538'),
  ('1011', '10700'),
  ('1011', '10826'),
  ('1011', '10828'),
  ('1011', '10829'),
  ('1011', '10830'),
  ('1011', '10831'),
  ('1011', '10832'),
  ('1011', '10833'),
  ('1011', '10834'),
  ('1011', '10836'),
  ('1011', '10838'),

  ('1012', '10291'),
  ('1012', '10292'),
  ('1012', '10402'),
  ('1012', '10538'),
  ('1012', '10700'),
  ('1012', '10826'),
  ('1012', '10827'),
  ('1012', '10828'),
  ('1012', '10829'),
  ('1012', '10830'),
  ('1012', '10831'),
  ('1012', '10832'),
  ('1012', '10833'),
  ('1012', '10834'),
  ('1012', '10835'),
  ('1012', '10836'),
  ('1012', '10837'),
  ('1012', '10838'),
  ('1012', '10839'),
  ('1012', '10840'),
  ('1012', '10841'),
  ('1012', '10842'),
  ('1012', '10843'),
  ('1012', '10844'),
  ('1012', '10845'),
  ('1012', '10846'),
  ('1012', '10847'),

  ('1021', '10292'),
  ('1021', '10364'),
  ('1021', '10402'),
  ('1021', '10404'),
  ('1021', '10407'),
  ('1021', '10519'),
  ('1021', '10520'),
  ('1021', '10522'),
  ('1021', '10782'),
  ('1021', '10909'),
  ('1021', '10911'),
  ('1021', '11026'),
  ('1021', '11028'),

  ('1022', '10291'),
  ('1022', '10292'),
  ('1022', '10364'),
  ('1022', '10401'),
  ('1022', '10402'),
  ('1022', '10403'),
  ('1022', '10404'),
  ('1022', '10405'),
  ('1022', '10406'),
  ('1022', '10407'),
  ('1022', '10408'),
  ('1022', '10409'),
  ('1022', '10519'),
  ('1022', '10520'),
  ('1022', '10521'),
  ('1022', '10522'),
  ('1022', '10523'),
  ('1022', '10782'),
  ('1022', '10783'),
  ('1022', '10909'),
  ('1022', '10910'),
  ('1022', '10911'),
  ('1022', '10912'),
  ('1022', '10913'),
  ('1022', '11026'),
  ('1022', '11027'),
  ('1022', '11028'),
  ('1022', '11029'),

  ('10511', '10826'),
  ('10511', '10965'),
  ('10511', '10967'),

  ('10512', '10826'),
  ('10512', '10965'),
  ('10512', '10966'),
  ('10512', '10967'),

  ('10521', '10826'),
  ('10521', '11030'),
  ('10521', '11032'),

  ('10522', '10826'),
  ('10522', '11030'),
  ('10522', '11031'),
  ('10522', '11032'),

  ('103', '10538'),
  ('103', '10539'),
  ('103', '10540'),
  ('103', '10541'),

  ('981', '10271'),
  ('981', '10272'),
  ('981', '10273'),
  ('981', '10274'),
  ('981', '10275'),
  ('981', '10276'),
  ('981', '10277'),
  ('981', '10278'),
  ('981', '10279'),
  ('981', '10280'),
  ('981', '10281'),
  ('981', '10282'),
  ('981', '10283'),
  ('981', '10284'),
  ('981', '10285'),
  ('981', '10286'),
  ('981', '10287'),
  ('981', '10288'),
  ('981', '10289'),
  ('981', '10290'),
  ('981', '10291'),
  ('981', '10292'),
  ('981', '10293'),
  ('981', '10294'),
  ('981', '10295'),
  ('981', '10296'),
  ('981', '10297'),
  ('981', '10298'),
  ('981', '10299'),
  ('981', '10300'),
  ('981', '10301'),
  ('981', '10302'),
  ('981', '10303'),
  ('981', '10304'),
  ('981', '10305'),
  ('981', '10306'),
  ('981', '10307'),
  ('981', '10308'),
  ('981', '10309'),
  ('981', '10310'),
  ('981', '10311'),
  ('981', '10312'),
  ('981', '10313'),
  ('981', '10314'),
  ('981', '10315'),
  ('981', '10316'),
  ('981', '10317'),
  ('981', '10318'),
  ('981', '10319'),
  ('981', '10320'),
  ('981', '10321'),
  ('981', '10322'),
  ('981', '10323'),
  ('981', '10324'),
  ('981', '10325'),
  ('981', '10326'),
  ('981', '10327'),
  ('981', '10328'),
  ('981', '10329'),
  ('981', '10330'),
  ('981', '10331'),
  ('981', '10332'),
  ('981', '10333'),
  ('981', '10334'),
  ('981', '10335'),
  ('981', '10449'),
  ('981', '10452'),
  ('981', '10461'),
  ('981', '10784'),
  ('981', '10785'),
  ('981', '10786'),
  ('981', '10787'),
  ('981', '10788'),
  ('981', '10790'),
  ('981', '10791'),
  ('981', '10792'),
  ('981', '10793'),
  ('981', '10794'),
  ('981', '10795'),
  ('981', '10796'),
  ('981', '10797'),
  ('981', '10798'),
  ('981', '10799'),
  ('981', '10800'),
  ('981', '10801'),
  ('981', '10802'),
  ('981', '10803'),
  ('981', '10804'),
  ('981', '10809'),
  ('981', '10812'),
  ('981', '10813'),
  ('981', '10814'),
  ('981', '10815'),
  ('981', '10816'),
  ('981', '10817'),
  ('981', '10818'),
  ('981', '10819'),
  ('981', '10820'),
  ('981', '10821'),
  ('981', '10822'),
  ('981', '10823'),
  ('981', '10824'),
  ('981', '10826'),
  ('981', '10832'),
  ('981', '10838'),
  ('981', '10903'),
  ('981', '10968'),
  ('981', '10973'),

  ('982', '10271'),
  ('982', '10272'),
  ('982', '10273'),
  ('982', '10274'),
  ('982', '10275'),
  ('982', '10276'),
  ('982', '10277'),
  ('982', '10278'),
  ('982', '10279'),
  ('982', '10280'),
  ('982', '10281'),
  ('982', '10282'),
  ('982', '10283'),
  ('982', '10284'),
  ('982', '10285'),
  ('982', '10286'),
  ('982', '10287'),
  ('982', '10288'),
  ('982', '10289'),
  ('982', '10290'),
  ('982', '10291'),
  ('982', '10292'),
  ('982', '10293'),
  ('982', '10294'),
  ('982', '10295'),
  ('982', '10296'),
  ('982', '10297'),
  ('982', '10298'),
  ('982', '10299'),
  ('982', '10300'),
  ('982', '10301'),
  ('982', '10302'),
  ('982', '10303'),
  ('982', '10304'),
  ('982', '10305'),
  ('982', '10306'),
  ('982', '10307'),
  ('982', '10308'),
  ('982', '10309'),
  ('982', '10310'),
  ('982', '10311'),
  ('982', '10312'),
  ('982', '10313'),
  ('982', '10314'),
  ('982', '10315'),
  ('982', '10316'),
  ('982', '10317'),
  ('982', '10318'),
  ('982', '10319'),
  ('982', '10320'),
  ('982', '10321'),
  ('982', '10322'),
  ('982', '10323'),
  ('982', '10324'),
  ('982', '10325'),
  ('982', '10326'),
  ('982', '10327'),
  ('982', '10328'),
  ('982', '10329'),
  ('982', '10330'),
  ('982', '10331'),
  ('982', '10332'),
  ('982', '10333'),
  ('982', '10334'),
  ('982', '10335'),
  ('982', '10449'),
  ('982', '10452'),
  ('982', '10461'),
  ('982', '10784'),
  ('982', '10785'),
  ('982', '10786'),
  ('982', '10787'),
  ('982', '10788'),
  ('982', '10790'),
  ('982', '10791'),
  ('982', '10792'),
  ('982', '10793'),
  ('982', '10794'),
  ('982', '10795'),
  ('982', '10796'),
  ('982', '10797'),
  ('982', '10798'),
  ('982', '10799'),
  ('982', '10800'),
  ('982', '10801'),
  ('982', '10802'),
  ('982', '10803'),
  ('982', '10804'),
  ('982', '10806'),
  ('982', '10807'),
  ('982', '10808'),
  ('982', '10809'),
  ('982', '10810'),
  ('982', '10811'),
  ('982', '10812'),
  ('982', '10813'),
  ('982', '10814'),
  ('982', '10815'),
  ('982', '10816'),
  ('982', '10817'),
  ('982', '10818'),
  ('982', '10819'),
  ('982', '10820'),
  ('982', '10821'),
  ('982', '10822'),
  ('982', '10823'),
  ('982', '10824'),
  ('982', '10825'),
  ('982', '10826'),
  ('982', '10832'),
  ('982', '10838'),
  ('982', '10841'),
  ('982', '10842'),
  ('982', '10903'),
  ('982', '10904'),
  ('982', '10968'),
  ('982', '10973');
