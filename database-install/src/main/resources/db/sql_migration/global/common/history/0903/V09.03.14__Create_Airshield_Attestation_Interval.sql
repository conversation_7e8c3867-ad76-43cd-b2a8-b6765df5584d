DROP TABLE IF EXISTS `pax_airshield_attestation_interval`;

CREATE TABLE `pax_airshield_attestation_interval` (
                                                      `id` int NOT NULL AUTO_INCREMENT,
                                                      `market_id` int NOT NULL COMMENT '市场id',
                                                      `interval` int NOT NULL COMMENT '时间间隔',
                                                      `created_date` datetime NOT NULL COMMENT '创建时间',
                                                      `created_by` int NOT NULL COMMENT '创建人',
                                                      `updated_date` datetime NOT NULL COMMENT '更新时间',
                                                      `updated_by` int NOT NULL COMMENT '更新人',
                                                      `del_flag` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志',
                                                      PRIMARY KEY (`id`)
) COMMENT='AirShield检测时间间隔表';