DROP TABLE IF EXISTS `PAX_PROFILE_SETTING`;

CREATE TABLE PAX_PROFILE_SETTING
(
    `id`            INT(11)      NOT NULL AUTO_INCREMENT COMMENT '编号',
    `key`           VARCHAR(128) NOT NULL COMMENT 'profile setting key',
    `value`         VARCHAR(128) NOT NULL COMMENT 'profile setting value',
    `default_value` VARCHAR(255) DEFAULT NULL COMMENT 'profile default value',
    `ui_type`       VARCHAR(128) NOT NULL COMMENT 'profile ui tab type',
    `product_type`  VARCHAR(128) NOT NULL COMMENT 'product type',
    `created_by`    INT          NOT NULL COMMENT '创建者',
    `created_date`  DATETIME     NOT NULL COMMENT '创建时间',
    `updated_by`    INT          NOT NULL COMMENT '更新者',
    `updated_date`  DATETIME     NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX UK_PRODUCT_TYPE_KEY (`product_type`,`key`)
) COMMENT '平台profile管理设置';

INSERT INTO `PAX_PROFILE_SETTING`(`key`, `value`, `default_value`, `ui_type`, `product_type`, `created_by`, `created_date`,
                                  `updated_by`, `updated_date`)
VALUES ('TERMINAL_REPLACEMENT_ENABLE', 'true', 'false', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_REPLACEMENT_TWO_FACTOR_ENABLE', 'true', 'true', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('GET_INFORMATION_FROM_PAXSTORE_ENABLE', 'true', 'false', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('NOTIFICATION_BAR_ENABLE', 'true', 'true', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('PAYMENT_CONTROL_ENABLE', 'true', 'false', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('PAYMENT_WHITELIST', 'true', null, 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('DEX_FILE', 'true', null, 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('PRINTER_WHITELIST', 'true', null, 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_ENABLE', 'true', 'false', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_DISTANCE', 'true', '2000', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_GEOGRAPHICAL_FENCE_ENABLE', 'true', 'false', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TERMINAL_AUTO_SET_CENTER_POINT_ENABLE', 'true', 'false', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TERMINAL_SAFE_RADIUS', 'true', '2000', 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CUSTOM_COORDINATE_POINT', 'true', null, 'tc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('GPS_SWITCH', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BLUETOOTH_SWITCH', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_APN', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_WIFI_CONFIG', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('WIFI_BLACKLIST', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BRIGHTNESS_SET', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SCREENOFF_TIME', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('VOLUME_SET', 'true', null, 'tsc','smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('LANGUAGE_SET', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('POWER_MODE_SET', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_TIMEZONE_ENABLE', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_TIMEZONE', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_DATETIME_ENABLE', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SETTING_PWD_ENABLE', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SETTING_PWD_SET', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_IP_CONFIG', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_DOMAIN_CONFIG', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SCHEDULED_RESTART_TERMINAL_CONFIG_ENABLE', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('SCHEDULED_RESTART_TERMINAL_CONFIG', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('APP_INSTALL_WHITELIST', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('DELETE_APP_OUT_OF_WHITELIST', 'true', null, 'tsc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('TML_SYNC_INTERVAL', 'true', '60', 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_POLLING_INTERVAL', 'true', '360', 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_CELLULAR_ALLOW_TYPE', 'true', '0', 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG_ENABLE', 'true', null, 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG', 'true', null, 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('AUTO_INSTALL_FIRMWARE_AFTER_REBOOT', 'true', null, 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TML_SEND_ERROR_REPORT', 'true', 'true', 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_ENABLE', 'true', null, 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_SET', 'true', null, 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_PROXY_CONFIG', 'true', null, 'scc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('ENABLE_RESELLER_SIGNATURE', 'true', 'false', 'rs', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GO_INSIGHT_SYNC_INTERVAL', 'true', '24', 'gc', 'smart', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),


       ('TERMINAL_REPLACEMENT_ENABLE', 'true', 'false', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_REPLACEMENT_TWO_FACTOR_ENABLE', 'true', 'true', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GET_INFORMATION_FROM_PAXSTORE_ENABLE', 'true', 'false', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('NOTIFICATION_BAR_ENABLE', 'true', 'true', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_ENABLE', 'true', 'false', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_DISTANCE', 'true', '2000', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_GEOGRAPHICAL_FENCE_ENABLE', 'true', 'false', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_AUTO_SET_CENTER_POINT_ENABLE', 'true', 'false', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_SAFE_RADIUS', 'true', '2000', 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CUSTOM_COORDINATE_POINT', 'true', null, 'tc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('ADD_UPDATE_WIFI_CONFIG', 'false', null, 'tsc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('VOLUME_SET', 'true', null, 'tsc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BRIGHTNESS_SET', 'true', null, 'tsc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_TIMEZONE_ENABLE', 'true', null, 'tsc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_TIMEZONE', 'true', null, 'tsc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_DATETIME_ENABLE', 'true', null, 'tsc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('TML_SYNC_INTERVAL', 'true', '60', 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_POLLING_INTERVAL', 'true', '360', 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_CELLULAR_ALLOW_TYPE', 'true', '0', 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG_ENABLE', 'true', null, 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG', 'true', null, 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('AUTO_INSTALL_FIRMWARE_AFTER_REBOOT', 'true', null, 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TML_SEND_ERROR_REPORT', 'true', 'true', 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_ENABLE', 'true', null, 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_SET', 'true', null, 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_PROXY_CONFIG', 'true', null, 'scc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GO_INSIGHT_SYNC_INTERVAL', 'false', '24', 'gc', 'android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),



       ('TERMINAL_REPLACEMENT_ENABLE', 'true', 'false', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_REPLACEMENT_TWO_FACTOR_ENABLE', 'true', 'true', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GET_INFORMATION_FROM_PAXSTORE_ENABLE', 'true', 'false', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('NOTIFICATION_BAR_ENABLE', 'true', 'true', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_ENABLE', 'true', 'false', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_DISTANCE', 'true', '2000', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_GEOGRAPHICAL_FENCE_ENABLE', 'true', 'false', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_AUTO_SET_CENTER_POINT_ENABLE', 'true', 'false', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_SAFE_RADIUS', 'true', '2000', 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CUSTOM_COORDINATE_POINT', 'true', null, 'tc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('ADD_UPDATE_WIFI_CONFIG', 'true', null, 'tsc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('VOLUME_SET', 'true', null, 'tsc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BRIGHTNESS_SET', 'true', null, 'tsc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_TIMEZONE_ENABLE', 'true', null, 'tsc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_TIMEZONE', 'true', null, 'tsc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_DATETIME_ENABLE', 'false', null, 'tsc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('TML_SYNC_INTERVAL', 'true', '60', 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_POLLING_INTERVAL', 'true', '360', 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_CELLULAR_ALLOW_TYPE', 'true', '0', 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_SEND_ERROR_REPORT', 'true', 'true', 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_ENABLE', 'true', null, 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_SET', 'true', null, 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_PROXY_CONFIG', 'true', null, 'scc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GO_INSIGHT_SYNC_INTERVAL', 'false', '24', 'gc', 'standard_android', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),


       ('TERMINAL_REPLACEMENT_ENABLE', 'true', 'false', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_REPLACEMENT_TWO_FACTOR_ENABLE', 'true', 'true', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GET_INFORMATION_FROM_PAXSTORE_ENABLE', 'true', 'false', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('NOTIFICATION_BAR_ENABLE', 'true', 'true', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_ENABLE', 'true', 'false', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_DISTANCE', 'true', '2000', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_GEOGRAPHICAL_FENCE_ENABLE', 'true', 'false', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_AUTO_SET_CENTER_POINT_ENABLE', 'true', 'false', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_SAFE_RADIUS', 'true', '2000', 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CUSTOM_COORDINATE_POINT', 'true', null, 'tc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('VOLUME_SET', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BRIGHTNESS_SET', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('LANGUAGE_SET', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_TIMEZONE_ENABLE', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_TIMEZONE', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_DATETIME_ENABLE', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SCREENOFF_TIME', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GPS_SWITCH', 'true', null, 'tsc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('TML_SYNC_INTERVAL', 'true', '60', 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_POLLING_INTERVAL', 'true', '360', 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_CELLULAR_ALLOW_TYPE', 'true', '0', 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG_ENABLE', 'true', null, 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG', 'true', null, 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTO_INSTALL_FIRMWARE_AFTER_REBOOT', 'true', null, 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_SEND_ERROR_REPORT', 'true', 'true', 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_ENABLE', 'true', null, 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_SET', 'true', null, 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_PROXY_CONFIG', 'true', null, 'scc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GO_INSIGHT_SYNC_INTERVAL', 'false', '24', 'gc', 'desktop', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),


       ('TERMINAL_REPLACEMENT_ENABLE', 'true', 'false', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_REPLACEMENT_TWO_FACTOR_ENABLE', 'true', 'true', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GET_INFORMATION_FROM_PAXSTORE_ENABLE', 'true', 'false', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('NOTIFICATION_BAR_ENABLE', 'true', 'true', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_ENABLE', 'true', 'false', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_DISTANCE', 'true', '2000', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_GEOGRAPHICAL_FENCE_ENABLE', 'true', 'false', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_AUTO_SET_CENTER_POINT_ENABLE', 'true', 'false', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_SAFE_RADIUS', 'true', '2000', 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CUSTOM_COORDINATE_POINT', 'true', null, 'tc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('VOLUME_SET', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BRIGHTNESS_SET', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('LANGUAGE_SET', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_TIMEZONE_ENABLE', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_TIMEZONE', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_DATETIME_ENABLE', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SCREENOFF_TIME', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GPS_SWITCH', 'true', null, 'tsc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('TML_SYNC_INTERVAL', 'true', '60', 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_POLLING_INTERVAL', 'true', '360', 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_CELLULAR_ALLOW_TYPE', 'true', '0', 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG_ENABLE', 'true', null, 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG', 'true', null, 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTO_INSTALL_FIRMWARE_AFTER_REBOOT', 'true', null, 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_SEND_ERROR_REPORT', 'true', 'true', 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_ENABLE', 'true', null, 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_SET', 'true', null, 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_PROXY_CONFIG', 'true', null, 'scc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('GO_INSIGHT_SYNC_INTERVAL', 'false', '24', 'gc', 'kds', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),


       ('TERMINAL_REPLACEMENT_ENABLE', 'true', 'false', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_REPLACEMENT_TWO_FACTOR_ENABLE', 'true', 'true', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('GET_INFORMATION_FROM_PAXSTORE_ENABLE', 'true', 'false', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('NOTIFICATION_BAR_ENABLE', 'true', 'true', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('PAYMENT_CONTROL_ENABLE', 'true', 'false', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('PAYMENT_WHITELIST', 'true', null, 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('DEX_FILE', 'true', null, 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('PRINTER_WHITELIST', 'true', null, 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_ENABLE', 'true', 'false', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_LOCATION_DISTANCE', 'true', '2000', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_GEOGRAPHICAL_FENCE_ENABLE', 'true', 'false', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TERMINAL_AUTO_SET_CENTER_POINT_ENABLE', 'true', 'false', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TERMINAL_SAFE_RADIUS', 'true', '2000', 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CUSTOM_COORDINATE_POINT', 'true', null, 'tc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('GPS_SWITCH', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BLUETOOTH_SWITCH', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_APN', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_WIFI_CONFIG', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('WIFI_BLACKLIST', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('BRIGHTNESS_SET', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SCREENOFF_TIME', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('VOLUME_SET', 'true', null, 'tsc','mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('LANGUAGE_SET', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('POWER_MODE_SET', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_TIMEZONE_ENABLE', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_TIMEZONE', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('AUTOMATIC_DATETIME_ENABLE', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SETTING_PWD_ENABLE', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SETTING_PWD_SET', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_IP_CONFIG', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ADD_UPDATE_DOMAIN_CONFIG', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('SCHEDULED_RESTART_TERMINAL_CONFIG_ENABLE', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('SCHEDULED_RESTART_TERMINAL_CONFIG', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('APP_INSTALL_WHITELIST', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('DELETE_APP_OUT_OF_WHITELIST', 'true', null, 'tsc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('TML_SYNC_INTERVAL', 'true', '60', 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_POLLING_INTERVAL', 'true', '360', 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TML_CELLULAR_ALLOW_TYPE', 'true', '0', 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG_ENABLE', 'true', null, 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('FIRMWARE_INSTALL_TIME_CONFIG', 'true', null, 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('AUTO_INSTALL_FIRMWARE_AFTER_REBOOT', 'true', null, 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1,CURRENT_TIMESTAMP),
       ('TML_SEND_ERROR_REPORT', 'true', 'true', 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_ENABLE', 'true', null, 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('CLIENT_PWD_SET', 'true', null, 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('TERMINAL_PROXY_CONFIG', 'true', null, 'scc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('GO_INSIGHT_SYNC_INTERVAL', 'false', '24', 'gc', 'mobile', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),



       ('ASY_WIFI', 'true', null, 'asy_sc', 'printer', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_BLUETOOTH_NAME', 'true', null, 'asy_sc', 'printer', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('ASY_FIRMWARE_CONFIG', 'true', null, 'asy_fc', 'printer', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),


       ('ASY_VOLUME', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_BRIGHTNESS', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_SLEEP_TIME', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_SCAN_PREVIEW', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_OPERATION_MODE', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_SWEEP_SYSTEM_CONFIG', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('ASY_FIRMWARE_CONFIG', 'true', null, 'asy_fc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),

       ('ASY_APP_CONFIG', 'true', null, 'asy_ac', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP),
       ('ASY_SCAN_MODE_CONFIG', 'true', null, 'asy_sc', 'scanner', '-1', CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP);
