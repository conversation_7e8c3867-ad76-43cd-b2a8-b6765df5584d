INSERT INTO pax_privilege (`id`, `parent_id`, `parent_ids`, `name`, `code`, `type`, `is_show`, `is_assignable`, `sort`, `style`, `active_style`, `href`, `title`, `market_required`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`)
 VALUES
     (71214, 7121, ',7,71,712,7121', '终端组织商户Profile权限', 'FUNC_TERMINAL_ORG_MERCHANT_PROFILE', 'F', 1, 1, 4, NULL, NULL, NULL, 'label_privilege_merchant_profile', 1, NULL, 1, CURRENT_TIMESTAMP, 1, CURRENT_TIMESTAMP);

UPDATE pax_privilege SET `code` = 'FUNC_TERMINAL_ORG_RESELLER_PROFILE', `name` = '终端组织代理商Profile权限', `title` = 'label_privilege_reseller_profile' WHERE id = 71212;

INSERT INTO `pax_role_privilege`(`role_id`, `privilege_id`) SELECT role_id, 71214 AS privilege_id FROM pax_role_privilege WHERE privilege_id = 71212;

