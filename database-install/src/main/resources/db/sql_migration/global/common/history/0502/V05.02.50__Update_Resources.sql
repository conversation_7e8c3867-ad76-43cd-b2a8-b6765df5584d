DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('1', 'Swagger UI', '/swagger-ui/**', 'GET', null, '0', '0', '1', NOW(), '1', NOW()), 
  ('2', 'Swagger UI API Docs', '/api-docs/**', 'GET', null, '0', '0', '1', NOW(), '1', NOW()), 
  ('3', 'Error Pages', '/error/**', 'GET', null, '0', '0', '1', NOW(), '1', NOW()), 
  ('4', 'H2 Page', '/h2/**', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10000', '获取应用参数列表', '/v1/apk/parameters', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10001', '创建应用参数', '/v1/apk/parameters', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10002', '查询参数应用列表', '/v1/apk/parameters/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10003', '删除应用参数', '/v1/apk/parameters/{apkParameterId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10004', '获取应用参数详情', '/v1/apk/parameters/{apkParameterId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10005', '更新应用参数', '/v1/apk/parameters/{apkParameterId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10006', '获取应用参数Schema', '/v1/apk/parameters/{apkParameterId}/schema', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('20000', '查询Entity属性', '/v1/attributes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('20001', '创建Entity属性', '/v1/attributes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('20002', '删除Entity属性', '/v1/attributes/{attributeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('20003', '获取Entity属性信息', '/v1/attributes/{attributeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('20004', '更新Entity属性', '/v1/attributes/{attributeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('20005', '更新Entity属性标签', '/v1/attributes/{attributeId}/label', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30000', '管理员查看POS Client App列表', '/v1/selfApps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30001', '查看POS Client App的factory列表', '/v1/selfApps/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30002', '创建最新PAXSTORE客户端下载任务', '/v1/selfApps/latest/client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('30003', '查看POS Client Apk列表for approval', '/v1/selfApps/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30004', '查看POS Client Apk', '/v1/selfApps/selfApks/{selfApkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30005', '首次上传POS Client Apk', '/v1/selfApps/{factoryId}/selfApk/file/first', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30006', '删除POS Client App', '/v1/selfApps/{selfAppId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30007', '获取POS Client App详细信息', '/v1/selfApps/{selfAppId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30008', '上传新版本POS Client Apk', '/v1/selfApps/{selfAppId}/selfApk/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30009', '查看某个POS Client App的Apk列表', '/v1/selfApps/{selfAppId}/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30010', '删除POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30011', '更新SelfApk(更新日志，强制更新)，SelfApp name', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30012', '通过POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/approval', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30013', '创建PAXSTORE客户端下载任务', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30014', '重新上传POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30015', '下线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30016', '上线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/online', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30017', '拒绝POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/rejection', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('30018', '提交POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/submit', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('40000', '获取代理商列表', '/v1/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40001', '创建代理商', '/v1/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40002', '代理商查询应用列表', '/v1/resellers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40003', '获取当前用户代理商树', '/v1/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40004', '删除代理商', '/v1/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40005', '获取代理商', '/v1/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40006', '更新代理商', '/v1/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40007', '激活代理商', '/v1/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40008', '停用代理商', '/v1/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40009', '替换代理商管理员（邮箱）', '/v1/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('40010', '重发激活邮件', '/v1/resellers/{resellerId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('50000', '获取应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('50001', '保存应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('50002', '获取Dashboard里终端信息统计部件(W03)的数据', '/v1/admin/dashboard/widgets/W03', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60000', '获取制造商列表', '/v1/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60001', '创建制造商', '/v1/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60002', '获取制造商机型树', '/v1/factories/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60003', '删除制造商', '/v1/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60004', '获取制造商', '/v1/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60005', '更新制造商', '/v1/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60006', '激活制造商', '/v1/factories/{factoryId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('60007', '停用制造商', '/v1/factories/{factoryId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('70000', '查询反馈列表', '/v1/feedbacks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('70001', '创建反馈', '/v1/feedbacks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('70002', '删除反馈', '/v1/feedbacks/{feedbackId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('70003', '获取反馈信息', '/v1/feedbacks/{feedbackId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('70004', '更新反馈', '/v1/feedbacks/{feedbackId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('80000', '获取商户列表', '/v1/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80001', '创建商户', '/v1/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80002', '导入商户', '/v1/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80003', '创建商户导入模板下载任务', '/v1/merchants/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80004', '删除商户', '/v1/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80005', '获取商户', '/v1/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80006', '更新商户', '/v1/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80007', '激活商户', '/v1/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80008', '停用商户', '/v1/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80009', '替换商户管理员（邮箱）', '/v1/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('80010', '重发商户激活邮件', '/v1/merchants/{merchantId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90000', '获取固件列表', '/v1/firmwares', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('90001', '查看固件的factory列表', '/v1/firmwares/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90002', '上传固件', '/v1/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90003', '删除固件差分包', '/v1/firmwares/firmware/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90004', '删除固件', '/v1/firmwares/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90005', '获取固件', '/v1/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90006', '更新固件', '/v1/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90007', '固件审核通过', '/v1/firmwares/{firmwareId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90008', '上传固件差分包', '/v1/firmwares/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90009', '下线固件', '/v1/firmwares/{firmwareId}/offline', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90010', '上线固件', '/v1/firmwares/{firmwareId}/online', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90011', '固件审核拒绝', '/v1/firmwares/{firmwareId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('90012', '提交固件更新', '/v1/firmwares/{firmwareId}/submit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('100000', '获取字典列表', '/v1/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100001', '获取语言列表', '/v1/codes/lang', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100002', 'super管理员获取Code配置列表', '/v1/codes/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100003', 'super管理员查询CodeType类型列表', '/v1/codes/setting/codeTypes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100004', 'super管理员保存Code配置', '/v1/codes/setting/save', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100005', 'super管理员删除Code配置', '/v1/codes/setting/{type}/{value}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100006', 'super管理员获取Code配置', '/v1/codes/setting/{type}/{value}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('100007', '根据类型获取字典列表', '/v1/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('110000', '获取应用市场变量列表', '/v1/admin/market/variables', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110001', '创建应用市场变量', '/v1/admin/market/variables', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110002', '导入应用市场变量', '/v1/admin/market/variables/import', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110003', '创建终应用市场变量导入模板下载任务', '/v1/admin/market/variables/import/template', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110004', '查找应用市场变量支持的应用列表', '/v1/admin/market/variables/supported/apps', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110005', '查找应用市场变量已使用的的应用列表', '/v1/admin/market/variables/used/apps', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110006', '删除应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '2', '0', '1', NOW(), '1', NOW()),
  ('110007', '更新应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '2', '0', '1', NOW(), '1', NOW()),
  ('120000', '查询应用市场列表', '/v1/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120001', '创建应用市场', '/v1/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120002', '删除应用市场', '/v1/markets/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120003', '获取应用市场信息', '/v1/markets/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120004', '更新应用市场', '/v1/markets/{marketId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120005', '手动激活应用市场（根据输入邮箱创建管理员和开发者并激活）', '/v1/markets/{marketId}/activateManuallyMarket', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120006', '替换应用市场管理员（邮箱）', '/v1/markets/{marketId}/replaceEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120007', '重新发送应用市场激活邮件', '/v1/markets/{marketId}/resend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120008', '重置应用市场code', '/v1/markets/{marketId}/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120009', '解挂应用市场', '/v1/markets/{marketId}/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('120010', '挂起应用市场', '/v1/markets/{marketId}/suspend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('130000', '查询应用市场签名配置', '/v1/admin/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('130001', '保存应用市场签名配置', '/v1/admin/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('130002', '获取应用市场签名提供商列表', '/v1/admin/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('130003', '测试应用市场签名服务', '/v1/admin/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140000', '激活应用市场', '/v1/admin/market/activate', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('140001', '查询登录日志', '/v1/admin/market/audit/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140002', '查询操作日志参数详情', '/v1/admin/market/audit/operation/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140003', '查询操作日志', '/v1/admin/market/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140004', '更新应用市场操作系统配置', '/v1/admin/market/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140005', '查询邮件模板', '/v1/admin/market/mailTemplates/{templateName}', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('140006', '更改终端PED设置', '/v1/admin/market/ped', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140007', '保存营销配置', '/v1/admin/market/sales', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('140008', '查询所有应用市场配置', '/v1/admin/market/settings', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('140009', '更新应用市场配置', '/v1/admin/market/settings', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('140010', '查询应用市场配置', '/v1/admin/market/settings/{key}', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('150000', '查询应用列表', '/v1/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('150001', '获取应用详情', '/v1/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('150002', '获取应用APK列表', '/v1/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('150003', '获取应用APK详情', '/v1/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('150004', '获取应用评论列表', '/v1/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160000', '管理员查询应用列表', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160001', '创建管理员Apk下载任务', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160002', '创建管理员下载apk参数模版下载任务', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160003', '对于签名失败的apk重新签名', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160004', '获取白名单', '/v1/admin/apps/whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160005', '创建白名单', '/v1/admin/apps/whiteList', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160006', '删除应用APP', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160007', '管理员获取开发者应用详细信息', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160008', '应用上线', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160009', '管理员获取APK列表', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160010', '删除应用APK', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160011', '通过应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160012', '管理员下载APK', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160013', '应用Apk下线', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160014', '应用Apk上线', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160015', '拒绝应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160016', '管理员更新APK机型', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160017', '应用下线', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160018', '获取已安装该应用的终端列表', '/v1/admin/apps/{appId}/installed/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('160019', 'App specific reseller or global APP publish to the normal market', '/v1/admin/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('170000', '查询当前用户的应用购买列表', '/v1/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170001', '为当前用户创建应用购买记录', '/v1/purchases', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170002', '查询当前用户指定应用的购买记录详情', '/v1/purchases/app/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170003', '查询当前用户指定应用的待付款购买记录详情', '/v1/purchases/app/{appId}/status/pending', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170004', '查询当前用户可购买该应用的终端列表(注:已成功购买过的终端除外)', '/v1/purchases/app/{appId}/terminals/purchasable', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170005', '提交支付请求', '/v1/purchases/payment', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170006', '获取支付服务的client配置信息', '/v1/purchases/payment/clientConfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170007', '查询可用的支付方式类型', '/v1/purchases/payments', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170008', '删除当前用户或终端的应用购买记录', '/v1/purchases/{purchaseId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170009', '查询当前用户指定的购买记录详情', '/v1/purchases/{purchaseId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170010', '更新当前用户或终端的应用购买记录', '/v1/purchases/{purchaseId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170011', '绑定购买应用至终端', '/v1/purchases/{purchaseId}/bind', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170012', '查询当前用户指定的购买记录列表', '/v1/purchases/{purchaseId}/items', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170013', '获得请求的支付类型的初始化参数(用户购买)', '/v1/purchases/{purchaseId}/payment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170014', '获取指定购买的终端推送状态', '/v1/purchases/{purchaseId}/push', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('170015', '推送购买应用至终端', '/v1/purchases/{purchaseId}/push', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('180000', '管理员查询开发者列表', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('180001', '管理员获取开发者详细信息', '/v1/admin/developers/{userId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('180002', '通过开发者审核', '/v1/admin/developers/{userId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('180003', '拒绝开发者审核', '/v1/admin/developers/{userId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('180004', '恢复开发者帐号', '/v1/admin/developers/{userId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('180005', '停用开发者帐号', '/v1/admin/developers/{userId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190000', '创建开发者原始Apk下载任务', '/v1/developers/apks/{apkId}/originFile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190001', '创建开发者下载apk参数模版下载任务', '/v1/developers/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190002', '查询开发者应用列表', '/v1/developers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190003', '创建用户应用', '/v1/developers/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190004', '删除开发者应用', '/v1/developers/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190005', '获取开发者应用详细信息', '/v1/developers/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190006', '获取APK列表', '/v1/developers/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190007', '添加apk', '/v1/developers/apps/{appId}/apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190008', '删除开发者应用版本', '/v1/developers/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190009', '获取APK', '/v1/developers/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190010', '覆盖更新APK信息、参数模板,图标与截图', '/v1/developers/apps/{appId}/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190011', '更新APK文件', '/v1/developers/apps/{appId}/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190012', '提交APK', '/v1/developers/apps/{appId}/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190013', '覆盖更新APP Key, APP Secret', '/v1/developers/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190014', '查询开发者的应用销售列表', '/v1/developers/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190015', '查询开发者的应用所销售的市场列表', '/v1/developers/purchases/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190016', '开发者应用结算查询', '/v1/developers/report/clearence', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190017', '开发者获取代理商列表', '/v1/developers/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190018', '开发者获取代理商树', '/v1/developers/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('190019', '查询开发者概况', '/v1/developers/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('200000', '查询开发者应用结算相关数据', '/v1/reports/clearence/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('200001', '查询应用市场应用结算相关数据', '/v1/reports/clearence/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('200002', '查询平台应用结算相关数据', '/v1/reports/clearence/platform', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('200003', '查询报表所需的应用市场列表', '/v1/reports/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('200004', '查询应用购买明细列表', '/v1/reports/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('200005', '按应用查询销售汇总', '/v1/reports/purchases/app/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('200006', '按开发者查询销售汇总', '/v1/reports/purchases/developer/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('210000', '下载Fastdfs服务器上的文件', '/v1/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('210001', '根据下载号获得下载地址', '/v1/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('220000', '读取指导文件信息', '/v1/guides/{guideId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('220001', '读取文档', '/v1/guides/{guideId}/docs/{docId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230000', '获取机型列表', '/v1/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230001', '创建机型', '/v1/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230002', '删除机型', '/v1/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230003', '获取机型', '/v1/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230004', '更新机型', '/v1/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230005', '激活机型', '/v1/models/{modelId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230006', '查询机型可用的应用列表', '/v1/models/{modelId}/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('230007', '停用机型', '/v1/models/{modelId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('240000', '查询权限列表', '/v1/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240001', '创建权限', '/v1/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240002', '删除权限', '/v1/privileges/{privilegeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240003', '获取权限信息', '/v1/privileges/{privilegeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240004', '更新权限', '/v1/privileges/{privilegeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240005', '获取权限资源列表', '/v1/privileges/{privilegeId}/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240006', '添加权限资源', '/v1/privileges/{privilegeId}/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('240007', '删除权限资源', '/v1/privileges/{privilegeId}/resources', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250000', '查询所有用户列表', '/v1/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250001', '更新用户信息', '/v1/users', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250002', '查询用户有开发者权限的应用市场', '/v1/users/developer/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250003', '删除开发者的收款帐户', '/v1/users/developer/receivable/account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250004', '获取开发者的收款帐户是否设置', '/v1/users/developer/receivable/account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250005', '设置开发者收款帐户', '/v1/users/developer/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250006', '申请成为开发者', '/v1/users/developers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('250007', '获取当前用户开发者信息', '/v1/users/developers/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('250008', '创建用户导出下载任务', '/v1/users/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250009', '删除应用市场收款帐户', '/v1/users/market/receivable/account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250010', '获取应用市场收款是否设置', '/v1/users/market/receivable/account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250011', '设置应用市场收款帐户', '/v1/users/market/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250012', '更改密码', '/v1/users/password/change', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250013', '获取当前用户信息', '/v1/users/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250014', '查询用户有代理商权限的应用市场', '/v1/users/reseller/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250015', '查询用户绑定的设备厂商列表', '/v1/users/terminal/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250016', '查询用户绑定的设备所属的应用市场列表', '/v1/users/terminal/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250017', '查询用户绑定的设备商户列表', '/v1/users/terminal/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250018', '查询用户绑定的设备机型列表', '/v1/users/terminal/models', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250019', '查询用户设备列表', '/v1/users/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250020', '获取用户设备信息', '/v1/users/terminals/{terminalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250021', '获取用户终端已安装应用详细信息', '/v1/users/terminals/{terminalId}/apks/{apkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250022', '获取用户终端已安装应用列表', '/v1/users/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250023', '获取用户信息', '/v1/users/{userId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250024', '全局市场激活用户', '/v1/users/{userId}/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250025', '全局市场停用用户', '/v1/users/{userId}/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('250026', '移除用户角色', '/v1/users/{userId}/roles/{roleId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('260000', '批量更新参数下载的操作状态', '/v1/3rdApps/actions', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('260001', '更新参数下载的操作状态', '/v1/3rdApps/actions/{actionId}/status', 'PUT', null, '1', '1', '1', NOW(), '1', NOW()),
  ('260002', '第三方应用同步信息', '/v1/3rdApps/info', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('260003', '获取终端参数下载信息', '/v1/3rdApps/param', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
  ('270000', '获取系统配置', '/v1/system/config', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('270001', '查询推送检测结果', '/v1/system/push/diagnosis', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270002', '发送推送检测消息', '/v1/system/push/diagnosis', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270003', '查询预定义角色列表', '/v1/system/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270004', '查询登录相关配置', '/v1/system/security/loginCfg', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270005', '保存登录相关配置', '/v1/system/security/loginCfg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270006', '查询密码策略', '/v1/system/security/password/policy', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270007', '保存密码策略', '/v1/system/security/password/policy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('270008', '根据登录名查找用户信息', '/v1/system/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('280000', '获取终端推送应用列表', '/v1/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280001', '创建终端推送应用', '/v1/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280002', '删除终端推送应用', '/v1/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280003', '获取终端推送APK', '/v1/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280004', '激活终端推送应用', '/v1/terminalApks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280005', '获取终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280006', '更新终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280007', '获取终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280008', '保存终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280009', '重置终端推送应用', '/v1/terminalApks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('280010', '挂起终端推送应用', '/v1/terminalApks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290000', '获取终端推送固件列表', '/v1/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290001', '创建终端推送固件', '/v1/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290002', '删除终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290003', '获取终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290004', '激活终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290005', '重置终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('290006', '挂起终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300000', '查询终端推送记录', '/v1/terminal/actions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300001', '批量更新终端操作状态', '/v1/terminal/actions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300002', '更新终端操作状态', '/v1/terminal/actions/{actionId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300003', 'POS端获取终端广告配置', '/v1/terminal/advertisements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300004', '获取终端应用下载列表for push', '/v1/terminal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300005', '终端查询应用列表', '/v1/terminal/apps/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300006', '终端获取应用APK列表', '/v1/terminal/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300007', '终端获取应用APK详情', '/v1/terminal/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300008', '下载应用APK', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300009', '<s>下载应用APK</s> - deprecated', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300010', '获取APK下载信息', '/v1/terminal/apps/{appId}/apks/{apkId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300011', '验证应用认证信息', '/v1/terminal/apps/{appId}/auth/verify', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300012', '终端删除应用评论', '/v1/terminal/apps/{appId}/comments', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300013', '终端获取应用评论', '/v1/terminal/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300014', '终端保存应用评论', '/v1/terminal/apps/{appId}/comments', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300015', '终端获取应用详情', '/v1/terminal/apps/{appId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300016', '<s>下载应用最新的APK</s> - deprecated', '/v1/terminal/apps/{appId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300017', '获取终端命令列表', '/v1/terminal/commands', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300018', '终端上送详情信息', '/v1/terminal/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300019', '终端下载固件', '/v1/terminal/firmware/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300020', '获取终端固件下载列表', '/v1/terminal/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300021', 'POS端获取终端硬件状态列表', '/v1/terminal/hardware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300022', '上传终端位置', '/v1/terminal/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300023', '终端登录', '/v1/terminal/login', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('300024', '终端上送日志', '/v1/terminal/logs', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300025', '获取应用市场APP最新的更新信息', '/v1/terminal/market/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300026', '下载应用市场APP', '/v1/terminal/market/update', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300027', '终端上送监控信息', '/v1/terminal/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300028', '获取终端应用下载列表', '/v1/terminal/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300029', '获取需要更新参数的应用Apk列表', '/v1/terminal/param/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300030', '获取需要更新参数的应用列表', '/v1/terminal/param/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300031', '查询ped设置', '/v1/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300032', '查询当前终端的应用购买列表', '/v1/terminal/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300033', '查询当前终端指定应用的购买记录详情', '/v1/terminal/purchases/app/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300034', '获得终端购买应用请求的支付类型的初始化参数', '/v1/terminal/purchases/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300035', '提交终端购买应用支付请求', '/v1/terminal/purchases/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300036', '远程初始化终端', '/v1/terminal/remote/init', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('300037', '根据SN获取终端信息', '/v1/terminal/remote/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('300038', '根据TID获取终端信息', '/v1/terminal/remote/tid/{tid}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('300039', '同步已安装应用列表', '/v1/terminal/sync/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300040', '同步已安装固件', '/v1/terminal/sync/firmware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300041', '同步硬件状态信息', '/v1/terminal/sync/hardware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300042', '终端推送测试', '/v1/terminal/test/push', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('300043', '获取需要卸载的应用Apk列表', '/v1/terminal/uninstall/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310000', '获取终端分组推送应用列表', '/v1/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310001', '创建终端分组推送应用', '/v1/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310002', '删除终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310003', '获取终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310004', '重新推送终端分组应用', '/v1/terminalGroupApks/{groupApkId}/actions/{terminalActionId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310005', '激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310006', '获取终端分组推送应用参数', '/v1/terminalGroupApks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310007', '更新终端分组推送应用参数参数', '/v1/terminalGroupApks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310008', '重新推送终端分组应用参数', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310009', '获取终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310010', '修改终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310011', '获取终端分组推送应用参数终端列表', '/v1/terminalGroupApks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310012', '重新激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310013', '挂起终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('310014', '获取终端分组推送应用终端列表', '/v1/terminalGroupApks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320000', '获取分组推送固件列表', '/v1/terminalGroupFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320001', '创建分组推送固件', '/v1/terminalGroupFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320002', '删除分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320003', '获取分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320004', '重新推送终端分组固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/actions/{terminalActionId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320005', '激活分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320006', '重置分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320007', '挂起分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('320008', '获取分组推送固件终端列表', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330000', '获取终端分组列表', '/v1/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330001', '创建终端分组', '/v1/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330002', '通过终端ID获取分组', '/v1/terminalGroups/terminalGroup/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330003', '删除终端分组', '/v1/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330004', '获取终端分组', '/v1/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330005', '更新终端分组', '/v1/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330006', '激活终端分组', '/v1/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330007', '停用终端分组', '/v1/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330008', '获取分组终端列表', '/v1/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330009', '添加分组终端', '/v1/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('330010', '删除分组终端', '/v1/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340000', '获取终端变量列表', '/v1/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340001', '创建终端变量', '/v1/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340002', '导入终端变量', '/v1/terminalVariables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340003', '创建终端变量导入模板下载任务', '/v1/terminalVariables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340004', '查找终端变量支持的应用列表', '/v1/terminalVariables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340005', '查找终端变量已使用的应用列表', '/v1/terminalVariables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340006', '删除终端变量', '/v1/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('340007', '更新终端变量', '/v1/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('350000', '获取地图标记', '/v1/admin/map/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('350001', '获取某地点内的POS机', '/v1/admin/map/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('360000', '创建终端导出下载任务', '/v1/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('360001', '导入终端', '/v1/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('360002', '创建终端导入模板下载任务', '/v1/terminals/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370000', '获取终端列表', '/v1/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370001', '创建终端', '/v1/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370002', '批量创建终端', '/v1/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370003', '批量激活终端', '/v1/terminals/batch/activation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370004', '批量删除终端', '/v1/terminals/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370005', '批量停用终端', '/v1/terminals/batch/suspension', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370006', '根据序列号获取终端', '/v1/terminals/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('370007', '删除终端', '/v1/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370008', '获取终端', '/v1/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370009', '更新终端', '/v1/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370010', '激活终端', '/v1/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370011', '获取终端的操作日志', '/v1/terminals/{terminalId}/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370012', '收集终端logcat', '/v1/terminals/{terminalId}/collect/logcat', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370013', '获取终端详情信息', '/v1/terminals/{terminalId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('370014', '停用终端', '/v1/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370015', '获取终端外接详情信息', '/v1/terminals/{terminalId}/extra/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370016', '获取终端硬件状态列表', '/v1/terminals/{terminalId}/hardware', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('370017', '获取终端已安装应用列表', '/v1/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('370018', '卸载终端已安装应用', '/v1/terminals/{terminalId}/installedApks/{apkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370019', '卸载终端已安装应用2', '/v1/terminals/{terminalId}/installedApks/{packageName}/{versionCode}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370020', '获取终端已安装固件', '/v1/terminals/{terminalId}/installedFirmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370021', '获取终端位置信息', '/v1/terminals/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370022', '获取终端日志列表', '/v1/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370023', '获取终端监控信息', '/v1/terminals/{terminalId}/monitors', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('370024', '推送终端命令', '/v1/terminals/{terminalId}/operation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('370025', '收集终端详情(硬件等)', '/v1/terminals/{terminalId}/refresh/terminalDetail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('370026', '获取终端流量', '/v1/terminals/{terminalId}/traffic', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('380000', '查询角色列表', '/v1/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('380001', '创建角色', '/v1/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380002', '查询所有角色的用户列表', '/v1/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380003', '删除角色', '/v1/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380004', '获取角色信息', '/v1/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380005', '更新角色', '/v1/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380006', '获取角色权限列表', '/v1/roles/{roleId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('380007', '添加角色权限', '/v1/roles/{roleId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('380008', '删除角色权限', '/v1/roles/{roleId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('380009', '获取角色用户列表', '/v1/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380010', '添加角色用户', '/v1/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('380011', '删除角色用户', '/v1/roles/{roleId}/users', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390000', '获取Global应用市场应用列表', '/v1/admin/global/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390001', '全局应用订阅', '/v1/admin/global/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390002', '全局应用取消订阅', '/v1/admin/global/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390003', '获取Global应用市场固件列表', '/v1/admin/global/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390004', '获取Global固件发布的应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390005', '发布全球应用市场固件到其他应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390006', '取消订阅Global应用市场固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390007', '订阅Global应用市场的固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390008', '获取Global应用市场POS Client应用列表', '/v1/admin/global/selfApps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390009', '发布Global应用市场POS Client应用到指定应用市场', '/v1/admin/global/selfApps/{selfAppId}/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390010', '订阅Global应用市场POS Client应用', '/v1/admin/global/selfApps/{selfAppId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('390011', '取消订阅Global应用市场POS Client应用', '/v1/admin/global/selfApps/{selfAppId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('400000', '验证激活验证码', '/v1/auth/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400001', '激活用户', '/v1/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400002', 'destroySsoToken', '/v1/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400003', 'currentTokenLogin', '/v1/auth/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400004', '获取应用市场的相关信息', '/v1/auth/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400005', '忘记密码发送邮件(未激活的重发激活邮件)', '/v1/auth/password/forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400006', '验证重置密码验证码', '/v1/auth/password/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400007', '重置密码（用于忘记密码）', '/v1/auth/password/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('400008', '注册用户', '/v1/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('410000', '获取许可证信息', '/v1/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('410001', '更新许可证', '/v1/license', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('420000', '提交重新生成购买结算记录', '/v1/admin/purchase/clr', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('420001', '(重新)提交购买结算的转帐请求', '/v1/admin/purchase/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('430000', '查询资源列表', '/v1/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430001', '创建资源', '/v1/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430002', '删除资源', '/v1/resources/{resourceId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430003', '获取资源信息', '/v1/resources/{resourceId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430004', '更新资源', '/v1/resources/{resourceId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430005', '获取资源权限列表', '/v1/resources/{resourceId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430006', '添加资源权限', '/v1/resources/{resourceId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('430007', '删除资源权限', '/v1/resources/{resourceId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '140010'),
  ('1', '250001'),
  ('1', '250002'),
  ('1', '250003'),
  ('1', '250004'),
  ('1', '250005'),
  ('1', '250006'),
  ('1', '250007'),
  ('1', '250008'),
  ('1', '250009'),
  ('1', '250010'),
  ('1', '250011'),
  ('1', '250012'),
  ('1', '250013'),
  ('1', '250014'),
  ('1', '250015'),
  ('1', '250016'),
  ('1', '250017'),
  ('1', '250018'),
  ('1', '250019'),
  ('1', '250020'),
  ('1', '250021'),
  ('1', '250022'),
  ('1', '250026'),
  ('1', '270008'),
  ('1', '370006'),

  ('2', '60000'),
  ('2', '140010'),
  ('2', '190000'),
  ('2', '190001'),
  ('2', '190002'),
  ('2', '190003'),
  ('2', '190004'),
  ('2', '190005'),
  ('2', '190006'),
  ('2', '190007'),
  ('2', '190008'),
  ('2', '190009'),
  ('2', '190010'),
  ('2', '190011'),
  ('2', '190012'),
  ('2', '190013'),
  ('2', '190014'),
  ('2', '190015'),
  ('2', '190016'),
  ('2', '190017'),
  ('2', '190018'),
  ('2', '190019'),
  ('2', '230000'),

  ('3', '240000'),
  ('3', '240001'),
  ('3', '240002'),
  ('3', '240003'),
  ('3', '240004'),
  ('3', '240005'),
  ('3', '240006'),
  ('3', '240007'),
  ('3', '270001'),
  ('3', '270002'),
  ('3', '270003'),
  ('3', '380006'),
  ('3', '380007'),
  ('3', '380008'),
  ('3', '430000'),
  ('3', '430001'),
  ('3', '430002'),
  ('3', '430003'),
  ('3', '430004'),
  ('3', '430005'),
  ('3', '430006'),
  ('3', '430007'),

  ('4', '260000'),
  ('4', '260001'),
  ('4', '260002'),
  ('4', '260003'),
  ('4', '300000'),
  ('4', '300001'),
  ('4', '300002'),
  ('4', '300003'),
  ('4', '300004'),
  ('4', '300005'),
  ('4', '300006'),
  ('4', '300007'),
  ('4', '300008'),
  ('4', '300009'),
  ('4', '300010'),
  ('4', '300011'),
  ('4', '300012'),
  ('4', '300013'),
  ('4', '300014'),
  ('4', '300015'),
  ('4', '300016'),
  ('4', '300017'),
  ('4', '300018'),
  ('4', '300019'),
  ('4', '300020'),
  ('4', '300021'),
  ('4', '300022'),
  ('4', '300024'),
  ('4', '300025'),
  ('4', '300026'),
  ('4', '300027'),
  ('4', '300028'),
  ('4', '300029'),
  ('4', '300030'),
  ('4', '300031'),
  ('4', '300032'),
  ('4', '300033'),
  ('4', '300034'),
  ('4', '300035'),
  ('4', '300039'),
  ('4', '300040'),
  ('4', '300041'),
  ('4', '300042'),
  ('4', '300043'),

  ('53', '300022'),
  ('53', '350000'),
  ('53', '350001'),
  ('53', '370000'),
  ('53', '370008'),
  ('53', '370013'),
  ('53', '370017'),
  ('53', '370021'),

  ('601', '40003'),
  ('601', '60000'),
  ('601', '60002'),
  ('601', '90005'),
  ('601', '160001'),
  ('601', '160002'),
  ('601', '160007'),
  ('601', '160009'),
  ('601', '160018'),
  ('601', '390000'),
  ('601', '390003'),
  ('601', '390008'),

  ('602', '40003'),
  ('602', '60000'),
  ('602', '60002'),
  ('602', '90005'),
  ('602', '160001'),
  ('602', '160002'),
  ('602', '160003'),
  ('602', '160007'),
  ('602', '160009'),
  ('602', '160018'),
  ('602', '160019'),
  ('602', '390000'),
  ('602', '390001'),
  ('602', '390002'),
  ('602', '390003'),
  ('602', '390006'),
  ('602', '390007'),
  ('602', '390008'),
  ('602', '390009'),
  ('602', '390010'),
  ('602', '390011'),

  ('611', '40003'),
  ('611', '120000'),
  ('611', '160000'),
  ('611', '160004'),
  ('611', '160007'),
  ('611', '160009'),
  ('611', '160018'),

  ('612', '40003'),
  ('612', '120000'),
  ('612', '160000'),
  ('612', '160001'),
  ('612', '160002'),
  ('612', '160003'),
  ('612', '160004'),
  ('612', '160005'),
  ('612', '160006'),
  ('612', '160007'),
  ('612', '160008'),
  ('612', '160009'),
  ('612', '160010'),
  ('612', '160011'),
  ('612', '160012'),
  ('612', '160013'),
  ('612', '160014'),
  ('612', '160015'),
  ('612', '160016'),
  ('612', '160017'),
  ('612', '160018'),
  ('612', '160019'),

  ('621', '180000'),
  ('621', '180001'),

  ('622', '180000'),
  ('622', '180001'),
  ('622', '180002'),
  ('622', '180003'),
  ('622', '180004'),
  ('622', '180005'),

  ('651', '40002'),
  ('651', '120000'),
  ('651', '160004'),
  ('651', '160007'),
  ('651', '160009'),
  ('651', '160018'),

  ('652', '40002'),
  ('652', '120000'),
  ('652', '160001'),
  ('652', '160002'),
  ('652', '160003'),
  ('652', '160004'),
  ('652', '160005'),
  ('652', '160006'),
  ('652', '160007'),
  ('652', '160008'),
  ('652', '160009'),
  ('652', '160010'),
  ('652', '160011'),
  ('652', '160012'),
  ('652', '160013'),
  ('652', '160014'),
  ('652', '160015'),
  ('652', '160016'),
  ('652', '160017'),
  ('652', '160018'),
  ('652', '160019'),

  ('711', '10000'),
  ('711', '20000'),
  ('711', '40000'),
  ('711', '40002'),
  ('711', '40003'),
  ('711', '40005'),
  ('711', '60000'),
  ('711', '60002'),
  ('711', '80000'),
  ('711', '80005'),
  ('711', '90000'),
  ('711', '90005'),
  ('711', '160001'),
  ('711', '160002'),
  ('711', '160007'),
  ('711', '230000'),
  ('711', '230006'),
  ('711', '270008'),
  ('711', '280000'),
  ('711', '280003'),
  ('711', '280005'),
  ('711', '280007'),
  ('711', '290000'),
  ('711', '290003'),
  ('711', '310003'),
  ('711', '310006'),
  ('711', '310009'),
  ('711', '310011'),
  ('711', '310014'),
  ('711', '320003'),
  ('711', '320008'),
  ('711', '330000'),
  ('711', '330002'),
  ('711', '330004'),
  ('711', '330008'),
  ('711', '340000'),
  ('711', '340004'),
  ('711', '340005'),
  ('711', '360000'),
  ('711', '370000'),
  ('711', '370006'),
  ('711', '370008'),
  ('711', '370011'),
  ('711', '370012'),
  ('711', '370013'),
  ('711', '370015'),
  ('711', '370016'),
  ('711', '370017'),
  ('711', '370020'),
  ('711', '370021'),
  ('711', '370022'),
  ('711', '370023'),
  ('711', '370025'),
  ('711', '370026'),

  ('712', '10000'),
  ('712', '20000'),
  ('712', '40000'),
  ('712', '40001'),
  ('712', '40002'),
  ('712', '40003'),
  ('712', '40004'),
  ('712', '40005'),
  ('712', '40006'),
  ('712', '40007'),
  ('712', '40008'),
  ('712', '40009'),
  ('712', '40010'),
  ('712', '60000'),
  ('712', '60002'),
  ('712', '80000'),
  ('712', '80001'),
  ('712', '80002'),
  ('712', '80003'),
  ('712', '80004'),
  ('712', '80005'),
  ('712', '80006'),
  ('712', '80007'),
  ('712', '80008'),
  ('712', '80009'),
  ('712', '80010'),
  ('712', '90000'),
  ('712', '90005'),
  ('712', '160001'),
  ('712', '160002'),
  ('712', '160007'),
  ('712', '230000'),
  ('712', '230006'),
  ('712', '270008'),
  ('712', '280000'),
  ('712', '280001'),
  ('712', '280002'),
  ('712', '280003'),
  ('712', '280004'),
  ('712', '280005'),
  ('712', '280006'),
  ('712', '280007'),
  ('712', '280008'),
  ('712', '280009'),
  ('712', '280010'),
  ('712', '290000'),
  ('712', '290001'),
  ('712', '290002'),
  ('712', '290003'),
  ('712', '290004'),
  ('712', '290005'),
  ('712', '290006'),
  ('712', '310003'),
  ('712', '310006'),
  ('712', '310009'),
  ('712', '310011'),
  ('712', '310014'),
  ('712', '320003'),
  ('712', '320008'),
  ('712', '330000'),
  ('712', '330001'),
  ('712', '330002'),
  ('712', '330003'),
  ('712', '330004'),
  ('712', '330005'),
  ('712', '330006'),
  ('712', '330007'),
  ('712', '330008'),
  ('712', '330009'),
  ('712', '330010'),
  ('712', '340000'),
  ('712', '340001'),
  ('712', '340002'),
  ('712', '340003'),
  ('712', '340004'),
  ('712', '340005'),
  ('712', '340006'),
  ('712', '340007'),
  ('712', '360000'),
  ('712', '360001'),
  ('712', '360002'),
  ('712', '370000'),
  ('712', '370001'),
  ('712', '370002'),
  ('712', '370003'),
  ('712', '370004'),
  ('712', '370005'),
  ('712', '370006'),
  ('712', '370007'),
  ('712', '370008'),
  ('712', '370009'),
  ('712', '370010'),
  ('712', '370011'),
  ('712', '370012'),
  ('712', '370013'),
  ('712', '370014'),
  ('712', '370015'),
  ('712', '370016'),
  ('712', '370017'),
  ('712', '370018'),
  ('712', '370019'),
  ('712', '370020'),
  ('712', '370021'),
  ('712', '370022'),
  ('712', '370023'),
  ('712', '370024'),
  ('712', '370025'),
  ('712', '370026'),

  ('721', '10000'),
  ('721', '40000'),
  ('721', '40003'),
  ('721', '60000'),
  ('721', '80000'),
  ('721', '90000'),
  ('721', '90005'),
  ('721', '230000'),
  ('721', '230006'),
  ('721', '310000'),
  ('721', '310003'),
  ('721', '310006'),
  ('721', '310009'),
  ('721', '310011'),
  ('721', '310014'),
  ('721', '320000'),
  ('721', '320003'),
  ('721', '320008'),
  ('721', '330000'),
  ('721', '330002'),
  ('721', '330004'),
  ('721', '330008'),
  ('721', '370000'),

  ('722', '10000'),
  ('722', '40000'),
  ('722', '40003'),
  ('722', '60000'),
  ('722', '80000'),
  ('722', '90000'),
  ('722', '90005'),
  ('722', '230000'),
  ('722', '230006'),
  ('722', '310000'),
  ('722', '310001'),
  ('722', '310002'),
  ('722', '310003'),
  ('722', '310004'),
  ('722', '310005'),
  ('722', '310006'),
  ('722', '310007'),
  ('722', '310008'),
  ('722', '310009'),
  ('722', '310010'),
  ('722', '310011'),
  ('722', '310012'),
  ('722', '310013'),
  ('722', '310014'),
  ('722', '320000'),
  ('722', '320001'),
  ('722', '320002'),
  ('722', '320003'),
  ('722', '320004'),
  ('722', '320005'),
  ('722', '320006'),
  ('722', '320007'),
  ('722', '320008'),
  ('722', '330000'),
  ('722', '330001'),
  ('722', '330002'),
  ('722', '330003'),
  ('722', '330004'),
  ('722', '330005'),
  ('722', '330006'),
  ('722', '330007'),
  ('722', '330008'),
  ('722', '330009'),
  ('722', '330010'),
  ('722', '370000'),

  ('731', '10000'),
  ('731', '10002'),
  ('731', '10004'),
  ('731', '10006'),
  ('731', '40000'),
  ('731', '40003'),
  ('731', '60000'),
  ('731', '60002'),
  ('731', '160001'),
  ('731', '160002'),
  ('731', '160007'),
  ('731', '230000'),

  ('732', '10000'),
  ('732', '10001'),
  ('732', '10002'),
  ('732', '10003'),
  ('732', '10004'),
  ('732', '10005'),
  ('732', '10006'),
  ('732', '40000'),
  ('732', '40003'),
  ('732', '60000'),
  ('732', '60002'),
  ('732', '160001'),
  ('732', '160002'),
  ('732', '160007'),
  ('732', '230000'),

  ('7411', '60000'),
  ('7411', '60002'),
  ('7411', '60004'),

  ('7412', '60000'),
  ('7412', '60001'),
  ('7412', '60002'),
  ('7412', '60003'),
  ('7412', '60004'),
  ('7412', '60005'),
  ('7412', '60006'),
  ('7412', '60007'),

  ('7421', '60000'),
  ('7421', '230000'),
  ('7421', '230003'),
  ('7421', '230006'),

  ('7422', '60000'),
  ('7422', '230000'),
  ('7422', '230001'),
  ('7422', '230002'),
  ('7422', '230003'),
  ('7422', '230004'),
  ('7422', '230005'),
  ('7422', '230006'),
  ('7422', '230007'),

  ('7511', '60000'),
  ('7511', '60002'),
  ('7511', '90000'),
  ('7511', '90001'),
  ('7511', '90005'),

  ('7512', '60000'),
  ('7512', '60002'),
  ('7512', '90000'),
  ('7512', '90001'),
  ('7512', '90002'),
  ('7512', '90003'),
  ('7512', '90004'),
  ('7512', '90005'),
  ('7512', '90006'),
  ('7512', '90008'),
  ('7512', '90012'),

  ('7521', '40003'),
  ('7521', '60000'),
  ('7521', '60002'),
  ('7521', '90000'),
  ('7521', '90001'),
  ('7521', '90005'),
  ('7521', '120000'),
  ('7521', '390004'),

  ('7522', '40003'),
  ('7522', '60000'),
  ('7522', '60002'),
  ('7522', '90000'),
  ('7522', '90001'),
  ('7522', '90004'),
  ('7522', '90005'),
  ('7522', '90007'),
  ('7522', '90009'),
  ('7522', '90010'),
  ('7522', '90011'),
  ('7522', '120000'),
  ('7522', '390004'),
  ('7522', '390005'),

  ('7611', '30000'),
  ('7611', '30004'),
  ('7611', '30007'),
  ('7611', '30009'),
  ('7611', '30013'),
  ('7611', '60000'),
  ('7611', '230000'),

  ('7612', '30000'),
  ('7612', '30004'),
  ('7612', '30005'),
  ('7612', '30006'),
  ('7612', '30007'),
  ('7612', '30008'),
  ('7612', '30009'),
  ('7612', '30010'),
  ('7612', '30011'),
  ('7612', '30013'),
  ('7612', '30014'),
  ('7612', '30018'),
  ('7612', '60000'),
  ('7612', '230000'),

  ('7621', '30001'),
  ('7621', '30003'),
  ('7621', '30004'),
  ('7621', '30013'),
  ('7621', '120000'),

  ('7622', '30001'),
  ('7622', '30003'),
  ('7622', '30004'),
  ('7622', '30010'),
  ('7622', '30012'),
  ('7622', '30013'),
  ('7622', '30015'),
  ('7622', '30016'),
  ('7622', '30017'),
  ('7622', '120000'),
  ('7622', '390009'),

  ('911', '20000'),
  ('911', '20003'),
  ('911', '110000'),
  ('911', '110004'),
  ('911', '110005'),
  ('911', '130000'),
  ('911', '130002'),
  ('911', '140005'),
  ('911', '140008'),
  ('911', '140010'),

  ('912', '20000'),
  ('912', '20001'),
  ('912', '20002'),
  ('912', '20003'),
  ('912', '20004'),
  ('912', '20005'),
  ('912', '110000'),
  ('912', '110001'),
  ('912', '110002'),
  ('912', '110003'),
  ('912', '110004'),
  ('912', '110005'),
  ('912', '110006'),
  ('912', '110007'),
  ('912', '130000'),
  ('912', '130001'),
  ('912', '130002'),
  ('912', '130003'),
  ('912', '140000'),
  ('912', '140004'),
  ('912', '140005'),
  ('912', '140006'),
  ('912', '140007'),
  ('912', '140008'),
  ('912', '140009'),
  ('912', '140010'),

  ('92', '40003'),
  ('92', '380000'),
  ('92', '380001'),
  ('92', '380002'),
  ('92', '380003'),
  ('92', '380004'),
  ('92', '380005'),
  ('92', '380009'),
  ('92', '380010'),
  ('92', '380011'),

  ('931', '250000'),
  ('931', '250008'),
  ('931', '250023'),

  ('932', '250000'),
  ('932', '250008'),
  ('932', '250023'),
  ('932', '250024'),
  ('932', '250025'),
  ('932', '250026'),

  ('94', '200000'),
  ('94', '200001'),
  ('94', '200002'),
  ('94', '200003'),
  ('94', '200004'),
  ('94', '200005'),
  ('94', '200006'),

  ('95', '140001'),
  ('95', '140002'),
  ('95', '140003'),

  ('1011', '120000'),
  ('1011', '120003'),
  ('1011', '410000'),

  ('1012', '120000'),
  ('1012', '120001'),
  ('1012', '120002'),
  ('1012', '120003'),
  ('1012', '120004'),
  ('1012', '120005'),
  ('1012', '120006'),
  ('1012', '120007'),
  ('1012', '120008'),
  ('1012', '120009'),
  ('1012', '120010'),
  ('1012', '410000'),

  ('1021', '100002'),
  ('1021', '100003'),
  ('1021', '100006'),
  ('1021', '270004'),
  ('1021', '270006'),
  ('1021', '410000'),

  ('1022', '100002'),
  ('1022', '100003'),
  ('1022', '100004'),
  ('1022', '100005'),
  ('1022', '100006'),
  ('1022', '270004'),
  ('1022', '270005'),
  ('1022', '270006'),
  ('1022', '270007'),
  ('1022', '410000'),
  ('1022', '410001');
