DROP TABLE IF EXISTS `pax_ad_group`;

CREATE TABLE `pax_ad_group`
(
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `name` varchar(64) NOT NULL COMMENT '名称',
    `start_time` datetime NOT NULL COMMENT '播放时间区间(开始)',
    `end_time` datetime NOT NULL COMMENT '播放时间区间(结束)',
    `ad_slot_type` char(2) NOT NULL COMMENT '广告位类型',
    `adaption` char(1) NOT NULL COMMENT '展示规则(F:自适应;S:拉伸;C:裁剪;N:不支持)',
    `apply_time` datetime COMMENT '播放时间区间(结束)',
    `status` char(2) NOT NULL COMMENT '状态(A:激活;D:禁用;E:过期;DR:草稿)',
    `created_by` int NOT NULL COMMENT '创建者',
    `created_date` datetime NOT NULL COMMENT '创建时间',
    `updated_by` int NOT NULL COMMENT '更新者',
    `updated_date` datetime NOT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志',
    PRIMARY KEY (`id`)
) COMMENT = '广告组表';