ALTER TABLE pax_market_billing_payment MODIFY COLUMN fee decimal(14, 2) null;
ALTER TABLE pax_market_billing_payment MODIFY COLUMN payment_by int(11) null;
ALTER TABLE pax_market_billing_payment ADD COLUMN `order_no` varchar(32) null comment '订单号' after summary_ids;
ALTER TABLE pax_market_billing_payment MODIFY COLUMN status varchar(12) null comment '支付状态';

update pax_market_billing_payment set `status` = 'paid' where `status` = 'S';

INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'zh_CN', 'paid', '已支付', 'paxpay_payment_status', '已支付', 0, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'zh_CN', 'unpaid', '未支付', 'paxpay_payment_status', '未支付', 1, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'zh_CN', 'fail', '支付失败', 'paxpay_payment_status', '等待审核', 2, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'zh_CN', 'refund', '已退款', 'paxpay_payment_status', '审核未通过', 3, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'en', 'paid', 'Paid', 'paxpay_payment_status', 'Paid', 0, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'en', 'unpaid', 'Unpaid', 'paxpay_payment_status', 'Unpaid', 1, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'en', 'fail', 'Fail', 'paxpay_payment_status', 'Pending', 2, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);
INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(-1, NULL, 'en', 'refund', 'Refund', 'paxpay_payment_status', 'Reject', 3, NULL, -1, CURRENT_TIMESTAMP, -1, CURRENT_TIMESTAMP, '0', 1, -1);