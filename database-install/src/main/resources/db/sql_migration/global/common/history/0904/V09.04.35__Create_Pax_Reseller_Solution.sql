DROP TABLE IF EXISTS `PAX_RESELLER_SOLUTION_APPLY`;
CREATE TABLE PAX_RESELLER_SOLUTION_APPLY
(
    id           BIGINT AUTO_INCREMENT COMMENT 'ID',
    reseller_id  INT NOT NULL COMMENT '代理商编号',
    market_id    INT(11) NOT NULL COMMENT '市场编号',
    app_id        BIGINT           NOT NULL COMMENT 'App ID',
    status       CHAR DEFAULT 'P' NOT NULL COMMENT 'P:pending,D:distributed,R:rejected',
    apply_date   DATETIME         NOT NULL COMMENT '申请时间',
    updated_date  DATETIME         NOT NULL COMMENT '更新时间',
    updated_by int NOT NULL COMMENT '更新者',
    PRIMARY KEY (`id`)
) COMMENT 'Reseller申请industry solution 记录表';