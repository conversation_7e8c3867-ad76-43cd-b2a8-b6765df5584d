-- 增加表字段说明
ALTER TABLE pax_data_source_info MODIFY COLUMN name varchar(255)  NULL COMMENT '数据库server名称';
ALTER TABLE pax_data_source_info MODIFY COLUMN url varchar(512)  NULL COMMENT '数据库Server地址';
ALTER TABLE pax_data_source_info MODIFY COLUMN username varchar(512)  NULL COMMENT '链接用户名';
ALTER TABLE pax_data_source_info MODIFY COLUMN password varchar(512)  NULL COMMENT '链接密码';
ALTER TABLE pax_data_source_info MODIFY COLUMN driver_class_name varchar(200)  NULL COMMENT '数据源驱动类';

-- 数据源、数据库实例、市场关系配置
ALTER TABLE pax_data_source_market MODIFY COLUMN market_name varchar(100)  NULL COMMENT '市场名称(冗余)';
ALTER TABLE pax_data_source_market MODIFY COLUMN market_id int NOT NULL COMMENT '市场ID';
ALTER TABLE pax_data_source_market MODIFY COLUMN datasource_id int NOT NULL COMMENT '数据源id';
-- 新增
ALTER TABLE pax_data_source_market ADD db_instance_name varchar(100)  DEFAULT '' null  COMMENT '数据库实例名称' after `datasource_id`;
ALTER TABLE pax_data_source_market ADD CONSTRAINT uniq_key_ds_market_bind UNIQUE KEY (market_id,datasource_id,db_instance_name);
