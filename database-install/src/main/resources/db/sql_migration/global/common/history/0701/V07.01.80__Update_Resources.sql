DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'Create push message to terminal request', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10001', 'Get arrive rate of message', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10002', '批量更新参数下载的操作状态', '/v1/3rdApps/actions', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10003', '更新参数下载的操作状态', '/v1/3rdApps/actions/{actionId}/status', 'PUT', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10004', '同步终端商业数据', '/v1/3rdApps/bizData', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10005', '第三方应用同步信息', '/v1/3rdApps/info', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10006', '获取终端参数下载信息', '/v1/3rdApps/param', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10007', '获取应用是否存在版本更新', '/v1/3rdApps/upgrade', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
  ('10008', 'Find apk parameter list', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10009', 'Create Apk Parameter', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10010', 'Get apk parameter details', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10011', 'Update Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10012', 'Delete Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10013', 'Search App in Marketplace', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'Find entity attribute', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10015', 'Create entity attribute', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'Get entity attribute by id', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'Update entity attribute', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'Delete entity attribute', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'Update entity attribute label', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'Verify estate', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'Get merchant category list', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'Create a single merchant category', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'Batch create merchant categories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'Update merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10025', 'Delete merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'Get merchant list by search criterias', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'Create a merchant', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'Get merchant by id', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'Update a merchant', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10030', 'Delete a merchant', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'Activate a merchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'Disable a merchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'Replace merchant email', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'Find parameter push history by page', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'Find resellers', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'Create a reseller', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'Get reseller', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'Update a reseller', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'Delete a reseller', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'Activate a reseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10041', 'Disable a reseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10042', 'Replace reseller email', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10043', 'Find reseller RKI KEY template', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10044', 'Search push app list by terminal', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10045', 'Push app (with parameter if parameter app) to terminal', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10046', 'Suspend terminal push app', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10047', 'Uninstall terminal app', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10048', 'Get Terminal Apk', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10049', 'Search push firmware list by terminal', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10050', 'Push firmware to terminal', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10051', 'Cancel push firmware task', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10052', 'Get push firmware history by id', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10053', 'Get terminal group push application list', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10054', 'Push app (with parameter if parameter app) to terminal group ', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10055', 'Get terminal group push apk', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10056', 'Delete terminal group push app', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10057', 'Suspend terminal group push app', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10058', 'Get terminal group list', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10059', 'Create terminal groups', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10060', 'Get terminal list', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10061', 'Get terminal group', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10062', 'Update terminal groups', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10063', 'Delete terminal group', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10064', 'Activate terminal group', '/v1/3rdsys/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10065', 'Disable terminal group', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10066', 'get terminal list in group', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10067', 'Create group terminals', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10068', 'Remove group terminals', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10069', 'Search push RKI Key task list by terminal', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10070', 'Push RKI Key to terminal', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10071', 'suspend terminal push Rki Key', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10072', 'Get Terminal RKI Key push task detail', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10073', 'Get terminal variable list', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10074', 'Create terminal parameter variables in batch', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10075', 'Batch deletion of terminal variables', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10076', 'Update terminal variable by variable id', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10077', 'Delete terminal variable by variable id', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10078', 'Find terminal list by page', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10079', 'Create terminal', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10080', 'Activate a terminal by query parameter', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10081', 'Batch add terminal to group', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10082', 'Get terminal by id', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10083', 'Update terminal', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10084', 'Delete a terminal', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10085', 'Activate a terminal by path parameter', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10086', 'Disable a terminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10087', '查询活动列表', '/v1/activities', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10088', '批量删除活动', '/v1/activities/batch/deletion', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10089', '获取活动信息', '/v1/activities/{activityId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10090', '删除活动', '/v1/activities/{activityId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10091', '管理员查询应用列表', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10092', '创建管理员Apk下载任务', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10093', '创建管理员下载apk参数模版下载任务', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10094', '管理员删除apk参数模版', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10095', '对于签名失败的apk重新签名', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10096', '获取白名单', '/v1/admin/apps/whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10097', '创建白名单', '/v1/admin/apps/whiteList', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10098', '删除白名单', '/v1/admin/apps/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10099', '管理员获取开发者应用详细信息', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10100', '删除应用APP', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10101', '应用上线', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10102', '管理员获取APK列表', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10103', '删除应用APK', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10104', '通过应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10105', '管理员下载APK', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10106', '查询应用APK定向发布的应用市场', '/v1/admin/apps/{appId}/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10107', '应用Apk下线', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10108', '应用Apk上线', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10109', '管理员添加参数模板', '/v1/admin/apps/{appId}/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10110', '拒绝应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10111', '管理员更新App ReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/releaseNote', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10112', '查询应用APK定向发布的代理商', '/v1/admin/apps/{appId}/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10113', 'Apk specific reseller or global APK publish to the normal market', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10114', '删除应用APK定向发布', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10115', '管理员更新APK机型', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10116', '管理员更新APP自动更新配置', '/v1/admin/apps/{appId}/autoUpdate/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10117', '更新应用的开发者', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10118', '应用下线', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10119', '更新app下载权限', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10120', 'Admin search insight sandbox app biz data', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10121', '获取已安装该应用的APK列表', '/v1/admin/apps/{appId}/installed/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10122', '获取已安装该应用的终端列表', '/v1/admin/apps/{appId}/installed/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10123', '创建已安装终端列表下载任务', '/v1/admin/apps/{appId}/installedTerminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10124', '查询应用定向发布的应用市场', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10125', 'App specific merchant categories', '/v1/admin/apps/{appId}/merchant/categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10126', '更新应用的消息服务调用权限', '/v1/admin/apps/{appId}/msgServiceEnabled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10127', '查询应用定向发布的代理商', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10128', '应用恢复', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10129', 'App specific reseller or global APP publish to the normal market', '/v1/admin/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10130', '删除应用定向发布', '/v1/admin/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10131', '更新app可是范围', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10132', 'Refresh AccessToken via CloudServiceGateway', '/v1/admin/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10133', 'Get access url and token via CloudServiceGateway', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10134', '获取应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10135', '保存应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10136', '获取Dashboard里终端信息统计部件(W03)的数据', '/v1/admin/dashboard/widgets/W03', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10137', '获取Dashboard里终端数量部件(W09)的数据', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10138', '获取Dashboard里代理商商户终端汇总部件(W10)的数据', '/v1/admin/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10139', '获取Dashboard里代理商级别的操作日志(W11)的数据', '/v1/admin/dashboard/widgets/W11', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10140', '获取Dashboard里代理商终端汇总部件(W12)的数据', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10141', '获取Dashboard里 固件版本-终端数量-组织 (W13)的数据', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10142', '创建Dashboard里 FM-Terminal_Org(W13) 数据下载任务', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10143', '获取Dashboard里 Client-终端数量-组织 (W14)的数据', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10144', '创建Dashboard里 Client-终端数量-组织(W14) 数据下载任务', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10145', '获取Dashboard里 机型-终端数量 (W15)的数据', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10146', '创建Dashboard里 MODEL-Terminal_Org(W15) 数据下载任务', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10147', '获取Dashboard 长时间 offline (W16)的数据', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10148', '创建Dashboard里 offline-terminal(W16) 数据下载任务', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10149', '获取Dashboard里 hardware error(W17) 数据', '/v1/admin/dashboard/widgets/W17', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10150', '创建Dashboard里 hardware error(W17) 数据下载任务', '/v1/admin/dashboard/widgets/W17/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10151', '获取Dashboard里 FM-Terminal(W18) 数据', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10152', '创建Dashboard里 FM-Terminal(W18) 数据下载任务', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10153', '获取Dashboard里 Client-Terminal(W19) 数据', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10154', '创建Dashboard里 Client-Terminal(W19) 数据下载任务', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10155', '管理员查询开发者列表', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10156', '管理员获取开发者详细信息', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10157', '删除开发者', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10158', '通过开发者审核', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10159', '拒绝开发者审核', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10160', '定向发布开发者到代理商', '/v1/admin/developers/{developerId}/reseller', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10161', '更改定向发布开发者的代理商', '/v1/admin/developers/{developerId}/reseller/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10162', '关闭/删除开发者定向发布到代理商', '/v1/admin/developers/{developerId}/reseller/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10163', '恢复开发者帐号', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10164', '停用开发者帐号', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10165', '更新开发者账户', '/v1/admin/developers/{developerId}/user', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10166', '获取Global应用市场应用列表', '/v1/admin/global/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10167', '全局应用订阅', '/v1/admin/global/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10168', '全局应用取消订阅', '/v1/admin/global/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10169', '获取Global应用市场固件列表', '/v1/admin/global/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10170', '获取Global固件发布的应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', '发布全球应用市场固件到其他应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', '订阅Global应用市场的固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', '取消订阅Global应用市场固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', '发布Global应用市场POS Client应用到指定应用市场', '/v1/admin/global/selfApks/{selfApkId}/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', '获取地图当前边界内的标记', '/v1/admin/map/markers/bound', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', '获取当前环境所有终端标记', '/v1/admin/map/markers/god/perspective', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', '获取某地点内的POS机', '/v1/admin/map/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', '查询应用市场第三方系统API访问配置', '/v1/admin/market/3rdsys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10179', '允许应用市场第三方系统API访问', '/v1/admin/market/3rdsys/config/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', '禁止应用市场第三方系统API访问', '/v1/admin/market/3rdsys/config/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', '获取应用市场第三方系统API访问密钥', '/v1/admin/market/3rdsys/config/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', '重置应用市场第三方系统API访问密钥', '/v1/admin/market/3rdsys/config/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', '激活应用市场', '/v1/admin/market/activate', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10184', '查询登陆日志', '/v1/admin/market/audit/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', '导出登陆日志', '/v1/admin/market/audit/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', '查询操作日志参数详情', '/v1/admin/market/audit/operation/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', '查询操作日志', '/v1/admin/market/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', '导出操作日志', '/v1/admin/market/audit/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', '获取应用市场年度账单列表', '/v1/admin/market/billing', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', '获取AirViewer当前用量', '/v1/admin/market/billing/airViewer/currentUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', '获取AirViewer当前用量dashBoard', '/v1/admin/market/billing/airViewer/currentUsage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', '获取AirViewer历史用量', '/v1/admin/market/billing/airViewer/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', '当前周期的账单详情', '/v1/admin/market/billing/current/billItem', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', '查询账单默认设置', '/v1/admin/market/billing/defaultSettings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', '更新账单默认设置', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', '获取应用市场指定年月的接入终端快照', '/v1/admin/market/billing/snapshot/file/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', '获取账单概要', '/v1/admin/market/billing/summary/overview', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', '下载指定应用市场账单', '/v1/admin/market/billing/summary/{billingSummaryId}/file/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', '获取当前应用市场的终端接入量详情', '/v1/admin/market/billing/terminal/enroll/bill', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', '下载指定市场当前月的接入终端详情', '/v1/admin/market/billing/terminal/enroll/current/{marketId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', '获取应用市场当前的终端接入量统计', '/v1/admin/market/billing/terminal/enroll/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', '获取当前应用市场的终端接入量统计', '/v1/admin/market/billing/terminal/enroll/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', '获取当前应用市场的终端接入量统计', '/v1/admin/market/billing/terminal/enroll/history/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', '下载指定应用市场账单数据文件', '/v1/admin/market/billing/{billingId}/datafile/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', '根据账单概要获取详细信息', '/v1/admin/market/billing/{summaryBillId}/billItems', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10206', '更新应用市场操作系统配置', '/v1/admin/market/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10207', '获取文档中心配置信息', '/v1/admin/market/doc/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10208', '创建页脚', '/v1/admin/market/footer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', '更新页脚', '/v1/admin/market/footer/{footerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', '删除页脚', '/v1/admin/market/footer/{footerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', '更改应用市场页脚顺序', '/v1/admin/market/footer/{footerId}/sort', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', '查询邮件模板', '/v1/admin/market/mailTemplates/{templateName}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10213', '保存营销配置', '/v1/admin/market/sales', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', '查询所有应用市场配置', '/v1/admin/market/settings', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10215', '更新应用市场配置', '/v1/admin/market/settings', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10216', '查询应用市场配置', '/v1/admin/market/settings/{key}', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
  ('10217', '查询TID生成策略配置', '/v1/admin/market/tid/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', '更新TID生成策略配置', '/v1/admin/market/tid/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10219', '查询代理商应用市场配置', '/v1/admin/market/ui/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10220', '更新代理商应用市场配置', '/v1/admin/market/ui/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10221', '查询用户协议设置', '/v1/admin/market/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10222', '更新用户协议', '/v1/admin/market/user/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10223', '获取应用市场变量列表', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10224', '创建应用市场变量', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10225', '批量删除应用市场变量', '/v1/admin/market/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10226', '导入应用市场变量', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10227', '创建终应用市场变量导入模板下载任务', '/v1/admin/market/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10228', '查找应用市场变量支持的应用列表', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10229', '查找应用市场变量已使用的的应用列表', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10230', '更新应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10231', '删除应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10232', '根据serviceType获取已定阅的应用市场', '/v1/admin/market/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10233', '提交重新生成购买结算记录', '/v1/admin/purchase/clr', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10234', '(重新)提交购买结算的转帐请求', '/v1/admin/purchase/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10235', '管理员查询版本发行通知列表', '/v1/admin/releaseNotes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10236', '创建版本发行通知', '/v1/admin/releaseNotes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10237', '获取版本发行通知信息', '/v1/admin/releaseNotes/{releaseNoeInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10238', '更新版本发行通知', '/v1/admin/releaseNotes/{releaseNoeInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10239', '发送邮件', '/v1/admin/releaseNotes/{releaseNoeInfoId}/sendMail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10240', '测试邮件', '/v1/admin/releaseNotes/{releaseNoeInfoId}/sendTestMail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10241', 'Admin Search Report List', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10242', 'Get Report Metadata', '/v1/admin/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10243', 'Get report by id', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10244', 'Update Report', '/v1/admin/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10245', 'Delete Report', '/v1/admin/report/{reportId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10246', 'Activate Report', '/v1/admin/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10247', 'Download Report Band File', '/v1/admin/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10248', 'Disable Report', '/v1/admin/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10249', 'Download Report Template File', '/v1/admin/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10250', 'List All Fields of Report', '/v1/admin/reportField', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', 'Update Report Field', '/v1/admin/reportField/{reportFieldId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', 'Get All Parameters of Report', '/v1/admin/reportParam', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', 'Update Report Parameter', '/v1/admin/reportParam/{reportParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10254', '创建应用市场RKI配置', '/v1/admin/rki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', '获取RKI服务列表', '/v1/admin/rki/servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10256', '删除RKI密钥模板KEY', '/v1/admin/rki/template/key', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', '获取RKI密钥模板KEY', '/v1/admin/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', '测试未保存应用市场RKI服务', '/v1/admin/rki/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', '测试已保存应用市场RKI服务', '/v1/admin/rki/test/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', '查询应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', '更新应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', '删除RKI服务配置', '/v1/admin/rki/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10263', '查询应用市场签名配置', '/v1/admin/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10264', '保存应用市场签名配置', '/v1/admin/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10265', '清除签名数据', '/v1/admin/signature/clearData', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10266', '获取应用市场签名提供商列表', '/v1/admin/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10267', '查询签名公钥', '/v1/admin/signature/signaturePuk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10268', '测试应用市场签名服务', '/v1/admin/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10269', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10270', 'disableVas', '/v1/admin/vas', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', 'listPosviewerFileTransferInfo', '/v1/admin/vas/posviewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', 'listPosvieweOperationInfo', '/v1/admin/vas/posviewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', 'listService', '/v1/admin/vas/service', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', 'enableService', '/v1/admin/vas/service/{serviceType}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10277', '获取应用参数列表', '/v1/apk/parameters', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10278', '创建应用参数', '/v1/apk/parameters', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10279', '查询参数APK列表', '/v1/apk/parameters/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10280', '查询参数应用列表', '/v1/apk/parameters/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10281', '获取应用参数详情', '/v1/apk/parameters/{apkParameterId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10282', '更新应用参数', '/v1/apk/parameters/{apkParameterId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10283', '删除应用参数', '/v1/apk/parameters/{apkParameterId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10284', '获取应用参数Schema', '/v1/apk/parameters/{apkParameterId}/schema', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10285', '查询应用列表', '/v1/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10286', '获取app统计的详细信息', '/v1/apps/appNumDetail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10287', '获取应用详情', '/v1/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10288', '获取应用APK列表', '/v1/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10289', '获取应用APK详情', '/v1/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10290', '获取应用评论列表', '/v1/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10291', '查询Entity属性', '/v1/attributes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10292', '创建Entity属性', '/v1/attributes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10293', '获取Entity属性信息', '/v1/attributes/{attributeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10294', '更新Entity属性', '/v1/attributes/{attributeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10295', '删除Entity属性', '/v1/attributes/{attributeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10296', '更新Entity属性标签', '/v1/attributes/{attributeId}/label', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10297', '验证激活验证码', '/v1/auth/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10298', '激活用户', '/v1/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10299', 'generateCaptcha', '/v1/auth/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10300', 'currentTokenLogin', '/v1/auth/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10301', 'destroySsoToken', '/v1/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10302', 'docCurrent', '/v1/auth/doc/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10303', '验证重置邮箱验证码', '/v1/auth/email/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10304', '用户更改邮箱', '/v1/auth/email/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10305', '获取应用市场的相关信息', '/v1/auth/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10306', '通过backup code关闭用户OTP', '/v1/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10307', '验证关闭用户OTP Code是否有效', '/v1/auth/otp/disableCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10308', '发送关闭用户OTP邮件', '/v1/auth/otp/resetMail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10309', '忘记密码发送邮件(未激活的重发激活邮件)', '/v1/auth/password/forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10310', '验证重置密码验证码', '/v1/auth/password/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10311', '重置密码（用于忘记密码）', '/v1/auth/password/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10312', '获取当前的密码规则', '/v1/auth/password/rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10313', '注册用户', '/v1/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10314', '获取字典列表', '/v1/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10315', '获取语言列表', '/v1/codes/lang', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10316', 'super管理员获取Code配置列表', '/v1/codes/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10317', 'super管理员查询CodeType类型列表', '/v1/codes/setting/codeTypes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10318', 'super管理员保存Code配置', '/v1/codes/setting/save', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10319', 'super管理员获取Code配置', '/v1/codes/setting/{type}/{value}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10320', 'super管理员删除Code配置', '/v1/codes/setting/{type}/{value}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10321', '根据类型获取字典列表', '/v1/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10322', '创建开发者原始Apk下载任务', '/v1/developers/apks/{apkId}/originFile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', '创建开发者下载apk参数模版下载任务', '/v1/developers/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', '获取开发者应用参数Schema', '/v1/developers/apks/{apkId}/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', '下载自定义参数模板最终生成的.p文件-内容为默认值', '/v1/developers/app/paramFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', '获取开发者在线自定义应用参数Schema-预览', '/v1/developers/app/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', '查询开发者应用列表', '/v1/developers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', '创建用户应用', '/v1/developers/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', '获取开发者应用详细信息', '/v1/developers/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', '删除开发者应用', '/v1/developers/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', '获取APK列表', '/v1/developers/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', '添加apk', '/v1/developers/apps/{appId}/apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', '获取APK', '/v1/developers/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', '覆盖更新APK信息、参数模板,图标与截图', '/v1/developers/apps/{appId}/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', '删除开发者应用版本', '/v1/developers/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', '更新APK文件', '/v1/developers/apps/{appId}/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', '提交APK', '/v1/developers/apps/{appId}/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', '覆盖更新APP Key, APP Secret', '/v1/developers/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', 'developer search insight sandbox app biz data', '/v1/developers/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', '查询开发者证书', '/v1/developers/cert', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', '开发者证书下载', '/v1/developers/cert', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', '开发者获取上次自定义模板编辑内容', '/v1/developers/custom/param/template/{paramId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10343', '添加企业开发者', '/v1/developers/enterprise', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10344', '查询企业开发者列表', '/v1/developers/enterprise/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10345', '刪除企业开发者用户', '/v1/developers/enterprise/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10346', '将开发者设为管理员', '/v1/developers/enterprise/{userId}/admin', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10347', '查询开发者的应用销售列表', '/v1/developers/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10348', '查询开发者的应用所销售的市场列表', '/v1/developers/purchases/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10349', '开发者应用结算查询', '/v1/developers/report/clearence', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', '开发者获取代理商列表', '/v1/developers/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', '开发者获取代理商树', '/v1/developers/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', '获取应用沙箱测试列表', '/v1/developers/sandbox/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', '创建沙箱终端推送应用', '/v1/developers/sandbox/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', '获取沙箱终端推送APK', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', '删除沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', '激活沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10357', '获取沙箱终端推送APK参数', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10358', '更新沙箱终端推送参数', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10359', '重置沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10360', '获取开发者终端列表', '/v1/developers/sandbox/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10361', '创建开发者终端', '/v1/developers/sandbox/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10362', '获取开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10363', '更新开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', '删除开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', '查询开发者概况', '/v1/developers/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', '上传自定义参数模板并进行解析Xml2Json', '/v1/developers/{devId}/app/{appId}/paramAnalysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', '创建参数模板', '/v1/developers/{devId}/app/{appId}/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', '删除自定义参数模板', '/v1/developers/{devId}/app/{appId}/paramTemplateSchema', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', '获取自定义参数模板列表', '/v1/developers/{devId}/custom/param/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', '检查参数模板名称', '/v1/developers/{devId}/paramTemplateName', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', '更新参数模板', '/v1/developers/{paramId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10372', '文档中心-文档列表', '/v1/doc-center', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10373', '文档中心-文档类别', '/v1/doc-center/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10374', '文档中心-读取指导文件信息', '/v1/doc-center/{guideId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10375', '文档中心-读取文档-docId', '/v1/doc-center/{guideId}/docs/{docId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10376', '文档中心-读取文档-fileName', '/v1/doc-center/{guideId}/file/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10377', '下载Fastdfs服务器上的文件', '/v1/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10378', '根据下载号获得下载地址', '/v1/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10379', '获取制造商列表', '/v1/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', '创建制造商', '/v1/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', '获取制造商机型树', '/v1/factories/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', '获取制造商', '/v1/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', '更新制造商', '/v1/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', '删除制造商', '/v1/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', '激活制造商', '/v1/factories/{factoryId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', '停用制造商', '/v1/factories/{factoryId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', '查询反馈列表', '/v1/feedbacks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10388', '创建反馈', '/v1/feedbacks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10389', '获取反馈信息', '/v1/feedbacks/{feedbackId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10390', '更新反馈', '/v1/feedbacks/{feedbackId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10391', '删除反馈', '/v1/feedbacks/{feedbackId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10392', '获取固件列表', '/v1/firmwares', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10393', '创建资源包客户类型', '/v1/firmwares/customerType', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10394', '删除资源包客户类型', '/v1/firmwares/customerType/code', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10395', '查看固件的factory列表', '/v1/firmwares/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10396', '上传固件', '/v1/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10397', '删除固件差分包', '/v1/firmwares/firmware/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10398', '获取固件', '/v1/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10399', '更新固件', '/v1/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10400', '删除固件', '/v1/firmwares/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', '固件审核通过', '/v1/firmwares/{firmwareId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', '上传固件差分包', '/v1/firmwares/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10403', '下线固件', '/v1/firmwares/{firmwareId}/offline', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10404', '上线固件', '/v1/firmwares/{firmwareId}/online', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10405', '固件审核拒绝', '/v1/firmwares/{firmwareId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10406', '提交固件更新', '/v1/firmwares/{firmwareId}/submit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10407', '获取终端分组变量列表', '/v1/groupVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10408', '创建终端分组变量', '/v1/groupVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10409', '批量删除终端分组变量', '/v1/groupVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10410', '导入终端分组变量', '/v1/groupVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10411', '创建终端分组变量导入模板下载任务', '/v1/groupVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10412', '查找终端分组变量支持的应用列表', '/v1/groupVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10413', '查找终端分组变量已使用的应用列表', '/v1/groupVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10414', '更新终端分组变量', '/v1/groupVariables/{groupVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10415', '删除终端分组变量', '/v1/groupVariables/{groupVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10416', '读取指导文件信息', '/v1/guides/{guideId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10417', '读取文档', '/v1/guides/{guideId}/docs/{docId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10418', 'Clear Cache', '/v1/internal/3rdsys/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10419', 'Get Cache', '/v1/internal/3rdsys/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10420', 'Refresh Group Action Count', '/v1/internal/3rdsys/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10421', 'Get system properties', '/v1/internal/3rdsys/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10422', 'Save System Property', '/v1/internal/3rdsys/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10423', 'Get Terminal', '/v1/internal/3rdsys/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10424', 'Send terminal command', '/v1/internal/3rdsys/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10425', 'Send terminal message', '/v1/internal/3rdsys/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10426', 'Administrator view push history list', '/v1/internal/3rdsys/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10427', 'Expire terminal token', '/v1/internal/3rdsys/terminal/token/expire', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10428', 'Get Filter Terminals', '/v1/internal/3rdsys/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10429', 'Post Filter Terminals', '/v1/internal/3rdsys/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10430', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10431', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10432', 'getApps', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10433', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10434', '获取许可证信息', '/v1/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10435', '更新许可证', '/v1/license', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10436', '获取市场可见服务列表', '/v1/marketAdmin/vas/services', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10437', '是否显示当月用量', '/v1/marketAdmin/vas/services/show', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10438', '市场取消订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10439', '市场订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10440', '查询应用市场列表', '/v1/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10441', '创建应用市场', '/v1/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10442', '获取应用市场下app数量', '/v1/markets/applicationNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10443', '统计开发者数量', '/v1/markets/developerNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10444', '获取开发者统计信息详情', '/v1/markets/developerNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10445', '获取应用市场数/快过期的数量', '/v1/markets/marketNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10446', '获取应用市场详细统计信息', '/v1/markets/marketNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10447', '终端数量', '/v1/markets/terminalNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10448', '生成应用市场终端报表', '/v1/markets/terminalReport', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10449', '获取应用市场信息', '/v1/markets/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10450', '更新应用市场', '/v1/markets/{marketId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10451', '删除应用市场', '/v1/markets/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10452', '手动激活应用市场（根据输入邮箱创建管理员和开发者并激活）', '/v1/markets/{marketId}/activateManuallyMarket', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10453', '替换应用市场管理员（邮箱）', '/v1/markets/{marketId}/replaceEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10454', '重新发送应用市场激活邮件', '/v1/markets/{marketId}/resend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10455', '重置应用市场code', '/v1/markets/{marketId}/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10456', '解挂应用市场', '/v1/markets/{marketId}/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10457', '挂起应用市场', '/v1/markets/{marketId}/suspend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10458', '获取商户分类列表', '/v1/merchantCategories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10459', '创建商户分类', '/v1/merchantCategories', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10460', '批量删除商户分类', '/v1/merchantCategories/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10461', '更新商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10462', '删除商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10463', '获取商户变量列表', '/v1/merchantVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10464', '创建商户变量', '/v1/merchantVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10465', '批量删除商户变量', '/v1/merchantVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10466', '导入商户变量', '/v1/merchantVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10467', '创建商户变量导入模板下载任务', '/v1/merchantVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10468', '查找商户变量支持的应用列表', '/v1/merchantVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10469', '查找商户变量已使用的应用列表', '/v1/merchantVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10470', '更新商户变量', '/v1/merchantVariables/{merchantVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10471', '删除商户变量', '/v1/merchantVariables/{merchantVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10472', '获取商户列表', '/v1/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10473', '创建商户', '/v1/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10474', '创建商户导出下载任务', '/v1/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10475', '导入商户', '/v1/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10476', '创建商户导入模板下载任务', '/v1/merchants/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10477', '获取商户', '/v1/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10478', '更新商户', '/v1/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10479', '删除商户', '/v1/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10480', '激活商户', '/v1/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10481', '停用商户', '/v1/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10482', '商户发送消息到终端', '/v1/merchants/{merchantId}/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10483', '获取商户配置文件', '/v1/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10484', '创建商户配置文件', '/v1/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10485', '替换商户管理员（邮箱）', '/v1/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10486', '重发商户激活邮件', '/v1/merchants/{merchantId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10487', '查询Migrations', '/v1/migrations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10488', '创建Migration', '/v1/migrations', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10489', '获取Migration', '/v1/migrations/{migrationId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10490', '删除Migration', '/v1/migrations/{migrationId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10491', '查询Migration Apk Template', '/v1/migrations/{migrationId}/apkTemplates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10492', '执行Migration', '/v1/migrations/{migrationId}/execute', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10493', '导出Migration结果', '/v1/migrations/{migrationId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10494', '查询Migration Merchant', '/v1/migrations/{migrationId}/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10495', '通知Migration新创建的用户', '/v1/migrations/{migrationId}/notify', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10496', '回滚Migration', '/v1/migrations/{migrationId}/rollback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10497', '查询Migration Terminal Apk', '/v1/migrations/{migrationId}/terminalApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10498', '查询Migration Terminal Group', '/v1/migrations/{migrationId}/terminalGroups', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10499', '查询Migration Terminal', '/v1/migrations/{migrationId}/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10500', '查询Migration User', '/v1/migrations/{migrationId}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10501', '验证Migration', '/v1/migrations/{migrationId}/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10502', '获取机型列表', '/v1/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10503', '创建机型', '/v1/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10504', '获取机型', '/v1/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10505', '更新机型', '/v1/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10506', '删除机型', '/v1/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10507', '激活机型', '/v1/models/{modelId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10508', '查询机型可用的应用列表', '/v1/models/{modelId}/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10509', '停用机型', '/v1/models/{modelId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10510', '添加AppMsg', '/v1/msg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10511', '删除AppMsg', '/v1/msg', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10512', '检查当前应用市场或自应用市场是否有终端安装该app', '/v1/msg/app/installed/check/{packageName}/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10513', '获取appMsg', '/v1/msg/appMsgList', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10514', '获取appMsg统计数据', '/v1/msg/report/{msgId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10515', '上传msgTemplateFile', '/v1/msg/template/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10516', '获取app的vasSupport', '/v1/msg/vas/support/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10517', '更新AppMsg状态', '/v1/msg/{appMsgId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10518', '根据id 获取msg', '/v1/msg/{id}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10519', '获取所有受保护的操作列表', '/v1/operations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10520', '获取所有受保护的操作', '/v1/operations/{key}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10521', '关闭操作权限', '/v1/operations/{key}/close', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10522', '打开操作权限', '/v1/operations/{key}/open', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10523', '查询操作用户', '/v1/operations/{key}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10524', '添加操作用户', '/v1/operations/{key}/users/{userId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10525', '删除操作用户', '/v1/operations/{key}/users/{userId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10526', '查询权限列表', '/v1/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10527', '创建权限', '/v1/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10528', '获取权限信息', '/v1/privileges/{privilegeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10529', '更新权限', '/v1/privileges/{privilegeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10530', '删除权限', '/v1/privileges/{privilegeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10531', '获取权限资源列表', '/v1/privileges/{privilegeId}/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10532', '添加权限资源', '/v1/privileges/{privilegeId}/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10533', '删除权限资源', '/v1/privileges/{privilegeId}/resources', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10534', 'getEnvCode', '/v1/pub/vas/envCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10535', '查询当前用户的应用购买列表', '/v1/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10536', '为当前用户创建应用购买记录', '/v1/purchases', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10537', '查询当前用户指定应用的购买记录详情', '/v1/purchases/app/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10538', '查询当前用户指定应用的待付款购买记录详情', '/v1/purchases/app/{appId}/status/pending', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10539', '查询当前用户可购买该应用的终端列表(注:已成功购买过的终端除外)', '/v1/purchases/app/{appId}/terminals/purchasable', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10540', '提交支付请求', '/v1/purchases/payment', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10541', '获取支付服务的client配置信息', '/v1/purchases/payment/clientConfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10542', '查询可用的支付方式类型', '/v1/purchases/payments', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10543', '查询当前用户指定的购买记录详情', '/v1/purchases/{purchaseId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10544', '更新当前用户或终端的应用购买记录', '/v1/purchases/{purchaseId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10545', '删除当前用户或终端的应用购买记录', '/v1/purchases/{purchaseId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10546', '绑定购买应用至终端', '/v1/purchases/{purchaseId}/bind', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10547', '查询当前用户指定的购买记录列表', '/v1/purchases/{purchaseId}/items', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10548', '获得请求的支付类型的初始化参数(用户购买)', '/v1/purchases/{purchaseId}/payment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10549', '获取指定购买的终端推送状态', '/v1/purchases/{purchaseId}/push', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10550', '推送购买应用至终端', '/v1/purchases/{purchaseId}/push', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10551', 'User Search Report', '/v1/report', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10552', 'Get Report Categories', '/v1/report/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10553', 'GET merchants by resellerIds', '/v1/report/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10554', 'Refresh Report Parameter Sources', '/v1/report/parameter/{parameterId}/source/refresh', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10555', 'Delete Report Execution Tasks', '/v1/report/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10556', 'Search Report Job History', '/v1/report/reportJobHistory', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10557', 'Search Report Task', '/v1/report/reportTask', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10558', 'Update Report Tasks Status', '/v1/report/reportTask/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10559', 'Update Report Task Status', '/v1/report/reportTask/{reportTaskId}/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10560', 'GET resellers by marketId', '/v1/report/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10561', 'Delete Report Execution Task', '/v1/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10562', 'User View Report', '/v1/report/{reportId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10563', 'View Report Online By Chart', '/v1/report/{reportId}/chart', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10564', 'Export Report', '/v1/report/{reportId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10565', 'Create Report Execution Task', '/v1/report/{reportId}/reportExecutionContext', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10566', 'Get Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10567', 'Update Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10568', 'View Report Online', '/v1/report/{reportId}/view', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10569', '查询开发者应用结算相关数据', '/v1/reports/clearence/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10570', '查询应用市场应用结算相关数据', '/v1/reports/clearence/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10571', '查询平台应用结算相关数据', '/v1/reports/clearence/platform', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10572', '查询报表所需的应用市场列表', '/v1/reports/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10573', '查询应用购买明细列表', '/v1/reports/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10574', '按应用查询销售汇总', '/v1/reports/purchases/app/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10575', '按开发者查询销售汇总', '/v1/reports/purchases/developer/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10576', '获取代理商列表', '/v1/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10577', '创建代理商', '/v1/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10578', '代理商查询应用列表', '/v1/resellers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10579', '获取当前用户代理商树', '/v1/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10580', '获取代理商', '/v1/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10581', '更新代理商', '/v1/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10582', '删除代理商', '/v1/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10583', '激活代理商', '/v1/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10584', '停用代理商', '/v1/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10585', '获取当前代理商终端已安装应用列表', '/v1/resellers/{resellerId}/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10586', '代理商发送消息到终端', '/v1/resellers/{resellerId}/message', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10587', '获取代理商配置文件', '/v1/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10588', '创建代理商配置文件', '/v1/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10589', '替换代理商管理员（邮箱）', '/v1/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10590', '重发激活邮件', '/v1/resellers/{resellerId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10591', '同步代理商的RKI密钥列表', '/v1/resellers/{resellerId}/rki/keys/collect', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10592', '保存代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10593', '删除代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10594', '查询资源列表', '/v1/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10595', '创建资源', '/v1/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10596', '获取资源信息', '/v1/resources/{resourceId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10597', '更新资源', '/v1/resources/{resourceId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10598', '删除资源', '/v1/resources/{resourceId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10599', '获取资源权限列表', '/v1/resources/{resourceId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10600', '添加资源权限', '/v1/resources/{resourceId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10601', '删除资源权限', '/v1/resources/{resourceId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10602', '查询角色列表', '/v1/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10603', '创建角色', '/v1/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10604', '查询角色列表包含开发者和商户', '/v1/roles/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10605', '获取角色的用户', '/v1/roles/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10606', '查询所有角色的用户列表', '/v1/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10607', '获取角色信息', '/v1/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10608', '更新角色', '/v1/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10609', '删除角色', '/v1/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10610', '获取角色权限列表', '/v1/roles/{roleId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10611', '添加角色权限', '/v1/roles/{roleId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10612', '删除角色权限', '/v1/roles/{roleId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10613', '获取角色用户列表', '/v1/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10614', '添加角色用户', '/v1/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10615', '删除角色用户', '/v1/roles/{roleId}/users', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10616', '获取所有的定时任务信息', '/v1/schedule', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10617', '手动调用定时任务', '/v1/schedule/active', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10618', '管理员查看POS Client App列表', '/v1/selfApps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10619', '查看POS Client App的factory列表', '/v1/selfApps/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10620', '创建最新PAXSTORE客户端下载任务', '/v1/selfApps/latest/client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10621', '查看POS Client Apk列表for approval', '/v1/selfApps/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10622', '查看POS Client Apk', '/v1/selfApps/selfApks/{selfApkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10623', '首次上传POS Client Apk', '/v1/selfApps/{factoryId}/selfApk/file/first', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10624', '获取POS Client App详细信息', '/v1/selfApps/{selfAppId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10625', '删除POS Client App', '/v1/selfApps/{selfAppId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10626', '上传新版本POS Client Apk', '/v1/selfApps/{selfAppId}/selfApk/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10627', '查看某个POS Client App的Apk列表', '/v1/selfApps/{selfAppId}/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10628', '更新SelfApk(更新日志，强制更新)，SelfApp name', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10629', '删除POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10630', '通过POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/approval', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10631', '创建PAXSTORE客户端下载任务', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10632', '重新上传POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10633', '下线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10634', '上线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/online', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10635', '拒绝POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/rejection', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10636', '提交POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/submit', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10637', '获取系统配置', '/v1/system/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10638', '查询页脚列表', '/v1/system/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10639', '获取页脚', '/v1/system/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10640', '查询系统属性', '/v1/system/properties', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10641', '创建系统属性', '/v1/system/properties', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10642', '获取系统属性信息', '/v1/system/properties/{systemPropertyId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10643', '更新系统属性', '/v1/system/properties/{systemPropertyId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10644', '删除系统属性', '/v1/system/properties/{systemPropertyId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10645', '查询推送检测结果', '/v1/system/push/diagnosis', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10646', '发送推送检测消息', '/v1/system/push/diagnosis', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10647', '查询预定义角色列表', '/v1/system/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10648', '查询登录相关配置', '/v1/system/security/loginCfg', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10649', '保存登录相关配置', '/v1/system/security/loginCfg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10650', '查询密码策略', '/v1/system/security/password/policy', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10651', '保存密码策略', '/v1/system/security/password/policy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10652', '查询用户协议', '/v1/system/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10653', '根据登录名查找用户信息', '/v1/system/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10654', '查询终端推送记录', '/v1/terminal/actions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', '批量更新终端操作状态', '/v1/terminal/actions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', '更新终端操作状态', '/v1/terminal/actions/{actionId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', 'POS端获取终端广告配置', '/v1/terminal/advertisements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', '获取终端应用参数下载列表', '/v1/terminal/app/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', '获取终端应用参数下载地址', '/v1/terminal/app/params/{actionId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', '获取终端应用下载列表', '/v1/terminal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', '根据服务器的信息获取终端应用自动更新列表', '/v1/terminal/apps/autoUpdate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', '根据终端上送的信息获取终端应用自动更新列表', '/v1/terminal/apps/autoUpdate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', '终端查询应用列表', '/v1/terminal/apps/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10664', '终端获取应用APK列表', '/v1/terminal/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', '终端获取应用APK详情', '/v1/terminal/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', '下载应用APK', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', '<s>下载应用APK</s> - deprecated', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', '获取APK下载信息', '/v1/terminal/apps/{appId}/apks/{apkId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', '验证应用认证信息', '/v1/terminal/apps/{appId}/auth/verify', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', '终端获取应用评论', '/v1/terminal/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10671', '终端保存应用评论', '/v1/terminal/apps/{appId}/comments', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10672', '终端删除应用评论', '/v1/terminal/apps/{appId}/comments', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', '终端获取应用详情', '/v1/terminal/apps/{appId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', '<s>下载应用最新的APK</s> - deprecated', '/v1/terminal/apps/{appId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', 'Get push channel in terminal table', '/v1/terminal/cloud/msg/pushchannel', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', 'Update push channel in terminal table', '/v1/terminal/cloud/msg/pushchannel', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', 'Refresh AccessToken via CloudServiceGateway for terminal', '/v1/terminal/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', 'Get access url and token via CloudServiceGateway for terminal', '/v1/terminal/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', '获取终端命令列表', '/v1/terminal/commands', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', '获取终端激活命令', '/v1/terminal/commands/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10681', '终端上送终端配置-Deprecated', '/v1/terminal/configurations', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', '终端上送详情信息-Deprecated', '/v1/terminal/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', '终端下载固件', '/v1/terminal/firmware/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', '获取终端固件下载列表', '/v1/terminal/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', 'POS端获取终端硬件状态列表', '/v1/terminal/hardware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', '终端上传终端位置-Deprecated', '/v1/terminal/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', '终端登录', '/v1/terminal/login', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10688', '终端登录验证', '/v1/terminal/login/validate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10689', '终端上送日志', '/v1/terminal/logs', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10690', '获取应用市场APP最新的更新信息', '/v1/terminal/market/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10691', '下载应用市场APP', '/v1/terminal/market/update', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', '终端商户登录', '/v1/terminal/merchant/login', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10693', '获取终端商户用户信息', '/v1/terminal/merchant/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', '终端上送监控信息-Deprecated', '/v1/terminal/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10695', '获取终端操作任务列表', '/v1/terminal/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10696', '获取需要更新参数的应用Apk列表', '/v1/terminal/param/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', '获取需要更新参数的应用列表', '/v1/terminal/param/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10698', '查询ped设置', '/v1/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10699', '获取终端配置文件', '/v1/terminal/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10700', '查询当前终端的应用购买列表', '/v1/terminal/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', '查询当前终端指定应用的购买记录详情', '/v1/terminal/purchases/app/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', '获得终端购买应用请求的支付类型的初始化参数', '/v1/terminal/purchases/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', '提交终端购买应用支付请求', '/v1/terminal/purchases/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', '远程初始化终端', '/v1/terminal/remote/init', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10705', '根据SN获取终端信息', '/v1/terminal/remote/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10706', '根据序列号检查库存终端是否合法', '/v1/terminal/remote/stock/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10707', '根据TID获取终端信息', '/v1/terminal/remote/tid/{tid}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10708', 'POS端获取终端代理商证书', '/v1/terminal/reseller/certificate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', 'RKI服务器回调方法', '/v1/terminal/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10710', '获取RKI服务器配置信息', '/v1/terminal/rki/server/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10711', '传统终端libpaxstore.so获取RKI任务接口', '/v1/terminal/rki/task', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10712', '获取终端RKI任务列表', '/v1/terminal/rki/task/action', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10713', '获取当前终端状态', '/v1/terminal/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', '同步已安装应用列表', '/v1/terminal/sync/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', '同步已安装(非PAXSTORE)应用列表ICON信息', '/v1/terminal/sync/apps/icons', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', '终端上送手动安装的应用', '/v1/terminal/sync/apps/manual', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', '同步已安装应用列表,返回需同步ICON的APP', '/v1/terminal/sync/apps/new', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', '终端上送终端配置', '/v1/terminal/sync/configurations', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', '终端上送单个终端配置', '/v1/terminal/sync/configurations/{key}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', '终端上送详情信息', '/v1/terminal/sync/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', '同步已安装固件', '/v1/terminal/sync/firmware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', '同步硬件状态信息', '/v1/terminal/sync/hardware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', '同步应用信息', '/v1/terminal/sync/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', '终端上传终端位置', '/v1/terminal/sync/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', '终端上送监控信息', '/v1/terminal/sync/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10726', '同步终端协议', '/v1/terminal/sync/protocol', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10727', '终端推送测试', '/v1/terminal/test/push', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10728', '获取需要卸载的应用Apk列表', '/v1/terminal/uninstall/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10729', '下载DEX', '/v1/terminal/{terminalId}/dex/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10730', '获取终端推送应用列表', '/v1/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10731', '创建终端推送应用', '/v1/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10732', '获取终端推送APK', '/v1/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10733', '删除终端推送应用', '/v1/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10734', '激活终端推送应用', '/v1/terminalApks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10735', '获取终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10736', '更新终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10737', '获取终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10738', '保存终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10739', '重置终端推送应用', '/v1/terminalApks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10740', '挂起终端推送应用', '/v1/terminalApks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10741', '获取终端推送固件列表', '/v1/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10742', '创建终端推送固件', '/v1/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10743', '获取终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10744', '删除终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10745', '激活终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10746', '重置终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10747', '挂起终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10748', '获取终端分组推送应用列表', '/v1/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10749', '创建终端分组推送应用', '/v1/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10750', '更新终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10751', '删除终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10752', '获取终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10753', '删除终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10754', '重新推送终端分组应用', '/v1/terminalGroupApks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10755', '激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10756', '创建终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10757', '获取终端分组推送应用参数', '/v1/terminalGroupApks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10758', '更新终端分组推送应用参数参数', '/v1/terminalGroupApks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10759', '重新推送终端分组应用参数', '/v1/terminalGroupApks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10760', '获取终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10761', '修改终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10762', '获取终端分组推送应用参数终端列表', '/v1/terminalGroupApks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10763', '获取终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10764', '保存终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10765', '重新激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10766', '挂起终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10767', '获取终端分组推送应用终端列表', '/v1/terminalGroupApks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10768', '创建分组应用推送终端列表下载任务', '/v1/terminalGroupApks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10769', '获取分组推送固件列表', '/v1/terminalGroupFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10770', '创建分组推送固件', '/v1/terminalGroupFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10771', '获取分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10772', '删除分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10773', '重新推送终端分组固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10774', '激活分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10775', '重置分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10776', '挂起分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10777', '获取分组推送固件终端列表', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10778', '创建分组固件推送终端列表下载任务', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10779', '获取分组推送Operation列表', '/v1/terminalGroupOpts', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10780', '创建分组推送Operation', '/v1/terminalGroupOpts', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10781', '获取分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10782', '删除分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10783', '重新推送终端分组Operation', '/v1/terminalGroupOpts/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10784', '激活分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10785', '重置分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10786', '挂起分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10787', '获取分组推送Operation终端列表', '/v1/terminalGroupOpts/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10788', '创建分组Operation推送终端列表下载任务', '/v1/terminalGroupOpts/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10789', '获取分组推送RKI列表', '/v1/terminalGroupRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10790', '创建分组推送RKI', '/v1/terminalGroupRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10791', '获取分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10792', '删除分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10793', '重新推送终端分组RKI', '/v1/terminalGroupRkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10794', '激活分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10795', '重置分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10796', '挂起分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10797', '获取分组推送RKI终端列表', '/v1/terminalGroupRkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10798', '创建分组RKI推送终端列表下载任务', '/v1/terminalGroupRkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10799', '获取分组推送卸载应用列表', '/v1/terminalGroupUninstallApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10800', '创建分组推送卸载应用', '/v1/terminalGroupUninstallApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10801', '获取分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10802', '删除分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10803', '重新推送终端分组卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10804', '激活分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10805', '重置分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10806', '挂起分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10807', '获取分组推送卸载应用终端列表', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10808', '创建分组卸载应用推送终端列表下载任务', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10809', '获取终端分组列表', '/v1/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10810', '创建终端分组', '/v1/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10811', '导入终端到终端分组', '/v1/terminalGroups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10812', '创建分组导入终端模板下载任务', '/v1/terminalGroups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10813', '获取终端分组', '/v1/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10814', '更新终端分组', '/v1/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10815', '删除终端分组', '/v1/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10816', '激活终端分组', '/v1/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10817', '停用终端分组', '/v1/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10818', '获取分组终端列表', '/v1/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10819', '添加分组终端', '/v1/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10820', '删除分组终端', '/v1/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10821', '获取分组终端数量', '/v1/terminalGroups/{groupId}/terminals/count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10822', '获取终端推送RKI列表', '/v1/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10823', '创建终端推送RKI', '/v1/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10824', '获取终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10825', '删除终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10826', '激活终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10827', '重置终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10828', '挂起终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10829', '获取终端变量列表', '/v1/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10830', '创建终端变量', '/v1/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10831', '批量删除终端变量', '/v1/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10832', '导入终端变量', '/v1/terminalVariables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10833', '创建终端变量导入模板下载任务', '/v1/terminalVariables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10834', '查找终端变量支持的应用列表', '/v1/terminalVariables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10835', '查找终端变量已使用的应用列表', '/v1/terminalVariables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10836', '更新终端变量', '/v1/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10837', '删除终端变量', '/v1/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10838', '获取终端列表', '/v1/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10839', '创建终端', '/v1/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10840', '批量创建终端', '/v1/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10841', '批量激活终端', '/v1/terminals/batch/activation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10842', '批量删除终端', '/v1/terminals/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10843', '批量停用终端', '/v1/terminals/batch/suspension', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10844', '复制终端', '/v1/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10845', '创建终端导出下载任务', '/v1/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10846', '批量添加终端到分组', '/v1/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10847', '导入终端', '/v1/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10848', '创建终端导入模板下载任务', '/v1/terminals/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10849', '获取后端给终端推送命令记录列表', '/v1/terminals/push/terminal/history/api', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10850', '根据序列号获取终端', '/v1/terminals/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10851', '获取终端设备库存列表', '/v1/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10852', '批量创建终端设备库存', '/v1/terminals/stock/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10853', '批量分配库存终端', '/v1/terminals/stock/batch/assign', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10854', '批量删除库存终端', '/v1/terminals/stock/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10855', '创建库存终端导出下载任务', '/v1/terminals/stock/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10856', '导入库存终端', '/v1/terminals/stock/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10857', '创建库存终端导入模板下载任务', '/v1/terminals/stock/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10858', '根据序列号获取库存终端', '/v1/terminals/stock/serialNo/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10859', '获取库存终端', '/v1/terminals/stock/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10860', '更新库存终端', '/v1/terminals/stock/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10861', '删除库存终端', '/v1/terminals/stock/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10862', '获取终端', '/v1/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10863', '更新终端', '/v1/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10864', '删除终端', '/v1/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10865', '激活终端', '/v1/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10866', '获取终端的操作日志', '/v1/terminals/{terminalId}/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10867', '创建终端商业数据下载任务', '/v1/terminals/{terminalId}/bizData/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10868', '获取终端商业数据', '/v1/terminals/{terminalId}/bizDatas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10869', '收集终端logcat', '/v1/terminals/{terminalId}/collect/logcat', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10870', 'web查询终端配置', '/v1/terminals/{terminalId}/configurations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', '获取终端详情信息', '/v1/terminals/{terminalId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10872', '停用终端', '/v1/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', '获取终端外接详情信息', '/v1/terminals/{terminalId}/extra/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', '获取终端硬件状态列表', '/v1/terminals/{terminalId}/hardware', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10875', '获取终端历史在线时长', '/v1/terminals/{terminalId}/history/online', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10876', '获取终端已安装应用列表', '/v1/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10877', '获取终端已安装应用列表统计', '/v1/terminals/{terminalId}/installedApks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10878', '查看终端已安装应用参数', '/v1/terminals/{terminalId}/installedApks/{apkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10879', '卸载终端已安装应用', '/v1/terminals/{terminalId}/installedApks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10880', '获取终端已安装固件', '/v1/terminals/{terminalId}/installedFirmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10881', '获取终端位置信息', '/v1/terminals/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10882', '获取终端日志列表', '/v1/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10883', '获取终端手动安装应用列表', '/v1/terminals/{terminalId}/manual/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', '获取终端监控信息', '/v1/terminals/{terminalId}/monitors', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10885', '推送终端命令', '/v1/terminals/{terminalId}/operation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10886', '获取终端操作状态', '/v1/terminals/{terminalId}/optStatus', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10887', '向终端推送CheckUp应用', '/v1/terminals/{terminalId}/push/checkup', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10888', '收集终端详情(硬件等)', '/v1/terminals/{terminalId}/refresh/terminalDetail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10889', '更新终端是否允许远程换机配置', '/v1/terminals/{terminalId}/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10890', '获取终端的换机日志', '/v1/terminals/{terminalId}/replace/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10891', '更新终端代理商', '/v1/terminals/{terminalId}/reseller', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10892', '获取终端流量', '/v1/terminals/{terminalId}/traffic', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10893', '向终端推送POSVIEWER或者启动POSVIEWER', '/v1/terminals/{terminalId}/{installedPosviewer}/remote/assistance', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10894', '查询所有用户列表', '/v1/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10895', '更新用户信息', '/v1/users', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10896', '查询用户有开发者权限的应用市场', '/v1/users/developer/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10897', '获取开发者的收款帐户是否设置', '/v1/users/developer/receivable/account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10898', '设置开发者收款帐户', '/v1/users/developer/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10899', '删除开发者的收款帐户', '/v1/users/developer/receivable/account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10900', '申请成为开发者', '/v1/users/developers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10901', '获取当前用户开发者信息', '/v1/users/developers/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10902', '创建用户导出下载任务', '/v1/users/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10903', '获取应用市场收款是否设置', '/v1/users/market/receivable/account', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10904', '设置应用市场收款帐户', '/v1/users/market/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10905', '删除应用市场收款帐户', '/v1/users/market/receivable/account', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10906', 'List all configuration (notification)', '/v1/users/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10907', 'Update a configuration (notification)', '/v1/users/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10908', 'Publish global notification', '/v1/users/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10909', 'List messages (notification)', '/v1/users/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10910', 'Update a set of messages as read (notification)', '/v1/users/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10911', 'Delete a set of messages (notification)', '/v1/users/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10912', 'Update all messages as read (notification)', '/v1/users/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10913', 'Get message statistics (notification)', '/v1/users/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10914', 'Read specific messages in top reminder (notification)', '/v1/users/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10915', 'Read a message (notification)', '/v1/users/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10916', 'Delete a message (notification)', '/v1/users/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10917', 'Download message attachment (notification)', '/v1/users/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10918', 'View message details (notification)', '/v1/users/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10919', 'List all subscribed topics (notification)', '/v1/users/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10920', 'Unsubscribe all topics (notification)', '/v1/users/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10921', 'Get subscription info if subscribed (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10922', 'Subscribe a topic (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10923', 'Unsubscribe a topic (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10924', '获取当前用户OTP', '/v1/users/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10925', '关闭当前用户OTP', '/v1/users/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10926', '启用当前用户OTP', '/v1/users/otp/activation', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10927', '重置用户OTP的backupCode', '/v1/users/otp/backupCode', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10928', '获取当前用户OTP二维码图片', '/v1/users/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10929', '更改密码', '/v1/users/password/change', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10930', '验证当前用户密码', '/v1/users/password/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10931', '获取当前用户信息', '/v1/users/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10932', '查询用户有代理商权限的应用市场', '/v1/users/reseller/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10933', '发送用户更改邮箱邮件', '/v1/users/resetEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10934', '查询用户绑定的设备厂商列表', '/v1/users/terminal/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10935', '查询用户绑定的设备所属的应用市场列表', '/v1/users/terminal/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10936', '查询用户绑定的设备商户列表', '/v1/users/terminal/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10937', '查询用户绑定的设备机型列表', '/v1/users/terminal/models', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10938', '查询用户设备列表', '/v1/users/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10939', '获取用户设备信息', '/v1/users/terminals/{terminalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10940', '获取用户终端已安装应用详细信息', '/v1/users/terminals/{terminalId}/apks/{apkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10941', '获取用户终端已安装应用列表', '/v1/users/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10942', '用户同意协议', '/v1/users/user/agreement', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10943', '获取用户信息', '/v1/users/{userId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10944', '全局市场激活用户', '/v1/users/{userId}/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10945', '更改邮箱', '/v1/users/{userId}/changeEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10946', '全局市场停用用户', '/v1/users/{userId}/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10947', '用户更改密码', '/v1/users/{userId}/resetPassword', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10948', '移除用户所有角色', '/v1/users/{userId}/roles', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10949', '移除用户角色', '/v1/users/{userId}/roles/{roleId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10950', 'Update push server list', '/v1/vas/cloudmsg/pushServer', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10951', 'Notify terminal online/offline', '/v1/vas/cloudmsg/terminalOnlineStatus', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10952', 'Batch Notify terminal online/offline', '/v1/vas/cloudmsg/terminalOnlineStatus/batch', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10953', 'Search app list by user market admin role', '/v1/vas/insight/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10954', 'Search app list by user developer role', '/v1/vas/insight/apps/develop', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10955', 'allow app sync bizData', '/v1/vas/insight/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10956', 'control app sync bizData', '/v1/vas/insight/apps/{appId}/bizdata/control', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10957', 'Search market page by app', '/v1/vas/insight/apps/{appId}/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10958', 'Search market page by user', '/v1/vas/insight/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10959', 'Search merchant page by user', '/v1/vas/insight/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10960', 'Search reseller page by user', '/v1/vas/insight/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10961', 'Search market', '/v1/vas/insight/sync/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10962', 'Search merchant', '/v1/vas/insight/sync/merchants', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10963', 'Search reseller', '/v1/vas/insight/sync/resellers', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10964', 'Search terminal', '/v1/vas/insight/sync/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10965', 'Search terminal page by user', '/v1/vas/insight/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10966', 'Get app(self&subscribe) market user list', '/v1/vas/insight/users/dataset/app/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10967', 'Get user detail info', '/v1/vas/insight/users/detail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10968', 'Get one organization all role user list', '/v1/vas/insight/users/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10969', 'Get market admin user list', '/v1/vas/insight/users/{marketId}/admin', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10970', 'Enable service callback', '/v1/vas/platform/service', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10971', 'Disable service callback', '/v1/vas/platform/service', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10972', 'enableVasGlobally', '/v1/vas/platform/service/global', 'PUT', null, '0', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '10087'),
  ('1', '10088'),
  ('1', '10089'),
  ('1', '10090'),
  ('1', '10216'),
  ('1', '10653'),
  ('1', '10850'),
  ('1', '10876'),
  ('1', '10895'),
  ('1', '10896'),
  ('1', '10897'),
  ('1', '10898'),
  ('1', '10899'),
  ('1', '10900'),
  ('1', '10901'),
  ('1', '10902'),
  ('1', '10903'),
  ('1', '10904'),
  ('1', '10905'),
  ('1', '10906'),
  ('1', '10907'),
  ('1', '10908'),
  ('1', '10909'),
  ('1', '10910'),
  ('1', '10911'),
  ('1', '10912'),
  ('1', '10913'),
  ('1', '10914'),
  ('1', '10915'),
  ('1', '10916'),
  ('1', '10917'),
  ('1', '10918'),
  ('1', '10919'),
  ('1', '10920'),
  ('1', '10921'),
  ('1', '10922'),
  ('1', '10923'),
  ('1', '10924'),
  ('1', '10925'),
  ('1', '10926'),
  ('1', '10927'),
  ('1', '10928'),
  ('1', '10929'),
  ('1', '10930'),
  ('1', '10931'),
  ('1', '10932'),
  ('1', '10933'),
  ('1', '10934'),
  ('1', '10935'),
  ('1', '10936'),
  ('1', '10937'),
  ('1', '10938'),
  ('1', '10939'),
  ('1', '10940'),
  ('1', '10941'),
  ('1', '10942'),
  ('1', '10947'),

  ('2', '10216'),
  ('2', '10322'),
  ('2', '10323'),
  ('2', '10324'),
  ('2', '10325'),
  ('2', '10326'),
  ('2', '10327'),
  ('2', '10328'),
  ('2', '10329'),
  ('2', '10330'),
  ('2', '10331'),
  ('2', '10332'),
  ('2', '10333'),
  ('2', '10334'),
  ('2', '10335'),
  ('2', '10336'),
  ('2', '10337'),
  ('2', '10338'),
  ('2', '10339'),
  ('2', '10340'),
  ('2', '10341'),
  ('2', '10342'),
  ('2', '10343'),
  ('2', '10344'),
  ('2', '10345'),
  ('2', '10346'),
  ('2', '10347'),
  ('2', '10348'),
  ('2', '10349'),
  ('2', '10350'),
  ('2', '10351'),
  ('2', '10352'),
  ('2', '10353'),
  ('2', '10354'),
  ('2', '10355'),
  ('2', '10356'),
  ('2', '10357'),
  ('2', '10358'),
  ('2', '10359'),
  ('2', '10360'),
  ('2', '10361'),
  ('2', '10362'),
  ('2', '10363'),
  ('2', '10364'),
  ('2', '10365'),
  ('2', '10366'),
  ('2', '10367'),
  ('2', '10368'),
  ('2', '10369'),
  ('2', '10370'),
  ('2', '10371'),
  ('2', '10379'),
  ('2', '10502'),
  ('2', '10943'),

  ('3', '10176'),
  ('3', '10487'),
  ('3', '10488'),
  ('3', '10489'),
  ('3', '10490'),
  ('3', '10491'),
  ('3', '10492'),
  ('3', '10493'),
  ('3', '10494'),
  ('3', '10495'),
  ('3', '10496'),
  ('3', '10497'),
  ('3', '10498'),
  ('3', '10499'),
  ('3', '10500'),
  ('3', '10501'),
  ('3', '10526'),
  ('3', '10527'),
  ('3', '10528'),
  ('3', '10529'),
  ('3', '10530'),
  ('3', '10531'),
  ('3', '10532'),
  ('3', '10533'),
  ('3', '10594'),
  ('3', '10595'),
  ('3', '10596'),
  ('3', '10597'),
  ('3', '10598'),
  ('3', '10599'),
  ('3', '10600'),
  ('3', '10601'),
  ('3', '10610'),
  ('3', '10611'),
  ('3', '10612'),
  ('3', '10640'),
  ('3', '10641'),
  ('3', '10642'),
  ('3', '10643'),
  ('3', '10644'),
  ('3', '10645'),
  ('3', '10646'),
  ('3', '10647'),

  ('4', '10002'),
  ('4', '10003'),
  ('4', '10004'),
  ('4', '10005'),
  ('4', '10006'),
  ('4', '10007'),
  ('4', '10654'),
  ('4', '10655'),
  ('4', '10656'),
  ('4', '10657'),
  ('4', '10658'),
  ('4', '10659'),
  ('4', '10660'),
  ('4', '10661'),
  ('4', '10662'),
  ('4', '10663'),
  ('4', '10664'),
  ('4', '10665'),
  ('4', '10666'),
  ('4', '10667'),
  ('4', '10668'),
  ('4', '10669'),
  ('4', '10670'),
  ('4', '10671'),
  ('4', '10672'),
  ('4', '10673'),
  ('4', '10674'),
  ('4', '10675'),
  ('4', '10676'),
  ('4', '10677'),
  ('4', '10678'),
  ('4', '10679'),
  ('4', '10680'),
  ('4', '10681'),
  ('4', '10682'),
  ('4', '10683'),
  ('4', '10684'),
  ('4', '10685'),
  ('4', '10686'),
  ('4', '10688'),
  ('4', '10689'),
  ('4', '10690'),
  ('4', '10691'),
  ('4', '10692'),
  ('4', '10693'),
  ('4', '10694'),
  ('4', '10695'),
  ('4', '10696'),
  ('4', '10697'),
  ('4', '10698'),
  ('4', '10699'),
  ('4', '10700'),
  ('4', '10701'),
  ('4', '10702'),
  ('4', '10703'),
  ('4', '10708'),
  ('4', '10709'),
  ('4', '10710'),
  ('4', '10711'),
  ('4', '10712'),
  ('4', '10713'),
  ('4', '10714'),
  ('4', '10715'),
  ('4', '10716'),
  ('4', '10717'),
  ('4', '10718'),
  ('4', '10719'),
  ('4', '10720'),
  ('4', '10721'),
  ('4', '10722'),
  ('4', '10723'),
  ('4', '10724'),
  ('4', '10725'),
  ('4', '10726'),
  ('4', '10727'),
  ('4', '10728'),
  ('4', '10729'),

  ('30', '10487'),
  ('30', '10488'),
  ('30', '10489'),
  ('30', '10490'),
  ('30', '10491'),
  ('30', '10492'),
  ('30', '10493'),
  ('30', '10494'),
  ('30', '10495'),
  ('30', '10496'),
  ('30', '10497'),
  ('30', '10498'),
  ('30', '10499'),
  ('30', '10500'),
  ('30', '10501'),

  ('53', '10175'),
  ('53', '10177'),
  ('53', '10686'),
  ('53', '10838'),
  ('53', '10862'),
  ('53', '10871'),
  ('53', '10876'),
  ('53', '10881'),

  ('601', '10092'),
  ('601', '10093'),
  ('601', '10099'),
  ('601', '10102'),
  ('601', '10106'),
  ('601', '10112'),
  ('601', '10122'),
  ('601', '10123'),
  ('601', '10124'),
  ('601', '10127'),
  ('601', '10166'),
  ('601', '10169'),
  ('601', '10170'),
  ('601', '10379'),
  ('601', '10381'),
  ('601', '10398'),
  ('601', '10458'),
  ('601', '10576'),
  ('601', '10579'),

  ('602', '10092'),
  ('602', '10093'),
  ('602', '10095'),
  ('602', '10099'),
  ('602', '10102'),
  ('602', '10106'),
  ('602', '10112'),
  ('602', '10113'),
  ('602', '10114'),
  ('602', '10122'),
  ('602', '10123'),
  ('602', '10124'),
  ('602', '10125'),
  ('602', '10127'),
  ('602', '10129'),
  ('602', '10130'),
  ('602', '10166'),
  ('602', '10167'),
  ('602', '10168'),
  ('602', '10169'),
  ('602', '10170'),
  ('602', '10171'),
  ('602', '10172'),
  ('602', '10173'),
  ('602', '10174'),
  ('602', '10379'),
  ('602', '10381'),
  ('602', '10398'),
  ('602', '10458'),
  ('602', '10576'),
  ('602', '10579'),

  ('611', '10091'),
  ('611', '10092'),
  ('611', '10093'),
  ('611', '10096'),
  ('611', '10099'),
  ('611', '10102'),
  ('611', '10106'),
  ('611', '10112'),
  ('611', '10120'),
  ('611', '10121'),
  ('611', '10122'),
  ('611', '10123'),
  ('611', '10124'),
  ('611', '10127'),
  ('611', '10381'),
  ('611', '10440'),
  ('611', '10458'),
  ('611', '10502'),
  ('611', '10576'),
  ('611', '10579'),

  ('612', '10091'),
  ('612', '10092'),
  ('612', '10093'),
  ('612', '10094'),
  ('612', '10095'),
  ('612', '10096'),
  ('612', '10097'),
  ('612', '10098'),
  ('612', '10099'),
  ('612', '10100'),
  ('612', '10101'),
  ('612', '10102'),
  ('612', '10103'),
  ('612', '10104'),
  ('612', '10105'),
  ('612', '10106'),
  ('612', '10107'),
  ('612', '10108'),
  ('612', '10109'),
  ('612', '10110'),
  ('612', '10111'),
  ('612', '10112'),
  ('612', '10113'),
  ('612', '10114'),
  ('612', '10115'),
  ('612', '10116'),
  ('612', '10117'),
  ('612', '10118'),
  ('612', '10119'),
  ('612', '10120'),
  ('612', '10121'),
  ('612', '10122'),
  ('612', '10123'),
  ('612', '10124'),
  ('612', '10125'),
  ('612', '10126'),
  ('612', '10127'),
  ('612', '10128'),
  ('612', '10129'),
  ('612', '10130'),
  ('612', '10131'),
  ('612', '10381'),
  ('612', '10440'),
  ('612', '10458'),
  ('612', '10502'),
  ('612', '10576'),
  ('612', '10579'),

  ('661', '10170'),
  ('661', '10381'),
  ('661', '10392'),
  ('661', '10395'),
  ('661', '10398'),
  ('661', '10440'),
  ('661', '10576'),
  ('661', '10579'),

  ('662', '10170'),
  ('662', '10171'),
  ('662', '10381'),
  ('662', '10392'),
  ('662', '10393'),
  ('662', '10394'),
  ('662', '10395'),
  ('662', '10396'),
  ('662', '10397'),
  ('662', '10398'),
  ('662', '10399'),
  ('662', '10400'),
  ('662', '10401'),
  ('662', '10402'),
  ('662', '10403'),
  ('662', '10404'),
  ('662', '10405'),
  ('662', '10406'),
  ('662', '10440'),
  ('662', '10576'),
  ('662', '10579'),

  ('621', '10091'),
  ('621', '10155'),
  ('621', '10156'),

  ('622', '10091'),
  ('622', '10155'),
  ('622', '10156'),
  ('622', '10157'),
  ('622', '10158'),
  ('622', '10159'),
  ('622', '10160'),
  ('622', '10161'),
  ('622', '10162'),
  ('622', '10163'),
  ('622', '10164'),
  ('622', '10165'),

  ('651', '10091'),
  ('651', '10092'),
  ('651', '10093'),
  ('651', '10096'),
  ('651', '10099'),
  ('651', '10102'),
  ('651', '10106'),
  ('651', '10112'),
  ('651', '10113'),
  ('651', '10114'),
  ('651', '10120'),
  ('651', '10121'),
  ('651', '10122'),
  ('651', '10123'),
  ('651', '10124'),
  ('651', '10127'),
  ('651', '10129'),
  ('651', '10130'),
  ('651', '10381'),
  ('651', '10440'),
  ('651', '10576'),
  ('651', '10578'),
  ('651', '10579'),

  ('652', '10091'),
  ('652', '10092'),
  ('652', '10093'),
  ('652', '10096'),
  ('652', '10099'),
  ('652', '10102'),
  ('652', '10106'),
  ('652', '10112'),
  ('652', '10113'),
  ('652', '10114'),
  ('652', '10120'),
  ('652', '10121'),
  ('652', '10122'),
  ('652', '10123'),
  ('652', '10124'),
  ('652', '10127'),
  ('652', '10129'),
  ('652', '10130'),
  ('652', '10381'),
  ('652', '10440'),
  ('652', '10576'),
  ('652', '10578'),
  ('652', '10579'),

  ('711', '10092'),
  ('711', '10093'),
  ('711', '10099'),
  ('711', '10106'),
  ('711', '10112'),
  ('711', '10124'),
  ('711', '10127'),
  ('711', '10186'),
  ('711', '10267'),
  ('711', '10277'),
  ('711', '10279'),
  ('711', '10280'),
  ('711', '10281'),
  ('711', '10284'),
  ('711', '10291'),
  ('711', '10379'),
  ('711', '10381'),
  ('711', '10392'),
  ('711', '10398'),
  ('711', '10458'),
  ('711', '10463'),
  ('711', '10468'),
  ('711', '10469'),
  ('711', '10472'),
  ('711', '10474'),
  ('711', '10477'),
  ('711', '10483'),
  ('711', '10502'),
  ('711', '10508'),
  ('711', '10576'),
  ('711', '10578'),
  ('711', '10579'),
  ('711', '10580'),
  ('711', '10585'),
  ('711', '10587'),
  ('711', '10591'),
  ('711', '10653'),
  ('711', '10730'),
  ('711', '10732'),
  ('711', '10735'),
  ('711', '10737'),
  ('711', '10741'),
  ('711', '10743'),
  ('711', '10752'),
  ('711', '10757'),
  ('711', '10760'),
  ('711', '10762'),
  ('711', '10763'),
  ('711', '10767'),
  ('711', '10768'),
  ('711', '10771'),
  ('711', '10777'),
  ('711', '10778'),
  ('711', '10788'),
  ('711', '10791'),
  ('711', '10797'),
  ('711', '10798'),
  ('711', '10808'),
  ('711', '10809'),
  ('711', '10813'),
  ('711', '10818'),
  ('711', '10821'),
  ('711', '10822'),
  ('711', '10824'),
  ('711', '10829'),
  ('711', '10834'),
  ('711', '10835'),
  ('711', '10838'),
  ('711', '10845'),
  ('711', '10849'),
  ('711', '10850'),
  ('711', '10858'),
  ('711', '10862'),
  ('711', '10866'),
  ('711', '10868'),
  ('711', '10869'),
  ('711', '10870'),
  ('711', '10871'),
  ('711', '10873'),
  ('711', '10874'),
  ('711', '10875'),
  ('711', '10876'),
  ('711', '10877'),
  ('711', '10878'),
  ('711', '10880'),
  ('711', '10881'),
  ('711', '10882'),
  ('711', '10883'),
  ('711', '10884'),
  ('711', '10886'),
  ('711', '10888'),
  ('711', '10890'),
  ('711', '10892'),

  ('712', '10092'),
  ('712', '10093'),
  ('712', '10099'),
  ('712', '10106'),
  ('712', '10112'),
  ('712', '10124'),
  ('712', '10127'),
  ('712', '10186'),
  ('712', '10267'),
  ('712', '10277'),
  ('712', '10278'),
  ('712', '10279'),
  ('712', '10280'),
  ('712', '10281'),
  ('712', '10282'),
  ('712', '10283'),
  ('712', '10284'),
  ('712', '10291'),
  ('712', '10379'),
  ('712', '10381'),
  ('712', '10392'),
  ('712', '10398'),
  ('712', '10458'),
  ('712', '10463'),
  ('712', '10464'),
  ('712', '10465'),
  ('712', '10466'),
  ('712', '10467'),
  ('712', '10468'),
  ('712', '10469'),
  ('712', '10470'),
  ('712', '10471'),
  ('712', '10472'),
  ('712', '10473'),
  ('712', '10474'),
  ('712', '10475'),
  ('712', '10476'),
  ('712', '10477'),
  ('712', '10478'),
  ('712', '10479'),
  ('712', '10480'),
  ('712', '10481'),
  ('712', '10482'),
  ('712', '10483'),
  ('712', '10484'),
  ('712', '10485'),
  ('712', '10486'),
  ('712', '10502'),
  ('712', '10508'),
  ('712', '10576'),
  ('712', '10577'),
  ('712', '10578'),
  ('712', '10579'),
  ('712', '10580'),
  ('712', '10581'),
  ('712', '10582'),
  ('712', '10583'),
  ('712', '10584'),
  ('712', '10585'),
  ('712', '10586'),
  ('712', '10587'),
  ('712', '10588'),
  ('712', '10589'),
  ('712', '10590'),
  ('712', '10591'),
  ('712', '10592'),
  ('712', '10593'),
  ('712', '10653'),
  ('712', '10730'),
  ('712', '10731'),
  ('712', '10732'),
  ('712', '10733'),
  ('712', '10734'),
  ('712', '10735'),
  ('712', '10736'),
  ('712', '10737'),
  ('712', '10738'),
  ('712', '10739'),
  ('712', '10740'),
  ('712', '10741'),
  ('712', '10742'),
  ('712', '10743'),
  ('712', '10744'),
  ('712', '10745'),
  ('712', '10746'),
  ('712', '10747'),
  ('712', '10752'),
  ('712', '10757'),
  ('712', '10760'),
  ('712', '10762'),
  ('712', '10763'),
  ('712', '10767'),
  ('712', '10768'),
  ('712', '10771'),
  ('712', '10777'),
  ('712', '10778'),
  ('712', '10788'),
  ('712', '10791'),
  ('712', '10797'),
  ('712', '10798'),
  ('712', '10808'),
  ('712', '10809'),
  ('712', '10810'),
  ('712', '10811'),
  ('712', '10812'),
  ('712', '10813'),
  ('712', '10814'),
  ('712', '10815'),
  ('712', '10816'),
  ('712', '10817'),
  ('712', '10818'),
  ('712', '10819'),
  ('712', '10820'),
  ('712', '10821'),
  ('712', '10822'),
  ('712', '10823'),
  ('712', '10824'),
  ('712', '10825'),
  ('712', '10826'),
  ('712', '10827'),
  ('712', '10828'),
  ('712', '10829'),
  ('712', '10830'),
  ('712', '10831'),
  ('712', '10832'),
  ('712', '10833'),
  ('712', '10834'),
  ('712', '10835'),
  ('712', '10836'),
  ('712', '10837'),
  ('712', '10838'),
  ('712', '10839'),
  ('712', '10840'),
  ('712', '10841'),
  ('712', '10842'),
  ('712', '10843'),
  ('712', '10844'),
  ('712', '10845'),
  ('712', '10846'),
  ('712', '10847'),
  ('712', '10848'),
  ('712', '10849'),
  ('712', '10850'),
  ('712', '10858'),
  ('712', '10862'),
  ('712', '10863'),
  ('712', '10864'),
  ('712', '10865'),
  ('712', '10866'),
  ('712', '10867'),
  ('712', '10868'),
  ('712', '10869'),
  ('712', '10870'),
  ('712', '10871'),
  ('712', '10872'),
  ('712', '10873'),
  ('712', '10874'),
  ('712', '10875'),
  ('712', '10876'),
  ('712', '10877'),
  ('712', '10878'),
  ('712', '10879'),
  ('712', '10880'),
  ('712', '10881'),
  ('712', '10882'),
  ('712', '10883'),
  ('712', '10884'),
  ('712', '10885'),
  ('712', '10886'),
  ('712', '10887'),
  ('712', '10888'),
  ('712', '10889'),
  ('712', '10890'),
  ('712', '10891'),
  ('712', '10892'),
  ('712', '10893'),

  ('721', '10092'),
  ('721', '10093'),
  ('721', '10257'),
  ('721', '10267'),
  ('721', '10277'),
  ('721', '10279'),
  ('721', '10280'),
  ('721', '10281'),
  ('721', '10284'),
  ('721', '10379'),
  ('721', '10381'),
  ('721', '10392'),
  ('721', '10398'),
  ('721', '10472'),
  ('721', '10502'),
  ('721', '10508'),
  ('721', '10576'),
  ('721', '10579'),
  ('721', '10748'),
  ('721', '10752'),
  ('721', '10757'),
  ('721', '10760'),
  ('721', '10762'),
  ('721', '10763'),
  ('721', '10767'),
  ('721', '10768'),
  ('721', '10769'),
  ('721', '10771'),
  ('721', '10777'),
  ('721', '10778'),
  ('721', '10779'),
  ('721', '10781'),
  ('721', '10787'),
  ('721', '10788'),
  ('721', '10789'),
  ('721', '10791'),
  ('721', '10797'),
  ('721', '10798'),
  ('721', '10799'),
  ('721', '10801'),
  ('721', '10807'),
  ('721', '10808'),
  ('721', '10809'),
  ('721', '10813'),
  ('721', '10818'),
  ('721', '10821'),
  ('721', '10838'),

  ('722', '10092'),
  ('722', '10093'),
  ('722', '10257'),
  ('722', '10267'),
  ('722', '10277'),
  ('722', '10278'),
  ('722', '10279'),
  ('722', '10280'),
  ('722', '10281'),
  ('722', '10282'),
  ('722', '10283'),
  ('722', '10284'),
  ('722', '10379'),
  ('722', '10381'),
  ('722', '10392'),
  ('722', '10398'),
  ('722', '10472'),
  ('722', '10502'),
  ('722', '10508'),
  ('722', '10576'),
  ('722', '10579'),
  ('722', '10748'),
  ('722', '10749'),
  ('722', '10750'),
  ('722', '10751'),
  ('722', '10752'),
  ('722', '10753'),
  ('722', '10754'),
  ('722', '10755'),
  ('722', '10756'),
  ('722', '10757'),
  ('722', '10758'),
  ('722', '10759'),
  ('722', '10760'),
  ('722', '10761'),
  ('722', '10762'),
  ('722', '10763'),
  ('722', '10764'),
  ('722', '10765'),
  ('722', '10766'),
  ('722', '10767'),
  ('722', '10768'),
  ('722', '10769'),
  ('722', '10770'),
  ('722', '10771'),
  ('722', '10772'),
  ('722', '10773'),
  ('722', '10774'),
  ('722', '10775'),
  ('722', '10776'),
  ('722', '10777'),
  ('722', '10778'),
  ('722', '10779'),
  ('722', '10780'),
  ('722', '10781'),
  ('722', '10782'),
  ('722', '10783'),
  ('722', '10784'),
  ('722', '10785'),
  ('722', '10786'),
  ('722', '10787'),
  ('722', '10788'),
  ('722', '10789'),
  ('722', '10790'),
  ('722', '10791'),
  ('722', '10792'),
  ('722', '10793'),
  ('722', '10794'),
  ('722', '10795'),
  ('722', '10796'),
  ('722', '10797'),
  ('722', '10798'),
  ('722', '10799'),
  ('722', '10800'),
  ('722', '10801'),
  ('722', '10802'),
  ('722', '10803'),
  ('722', '10804'),
  ('722', '10805'),
  ('722', '10806'),
  ('722', '10807'),
  ('722', '10808'),
  ('722', '10809'),
  ('722', '10810'),
  ('722', '10811'),
  ('722', '10812'),
  ('722', '10813'),
  ('722', '10814'),
  ('722', '10815'),
  ('722', '10816'),
  ('722', '10817'),
  ('722', '10818'),
  ('722', '10819'),
  ('722', '10820'),
  ('722', '10821'),
  ('722', '10838'),

  ('731', '10092'),
  ('731', '10093'),
  ('731', '10099'),
  ('731', '10106'),
  ('731', '10112'),
  ('731', '10124'),
  ('731', '10127'),
  ('731', '10277'),
  ('731', '10279'),
  ('731', '10280'),
  ('731', '10281'),
  ('731', '10284'),
  ('731', '10379'),
  ('731', '10381'),
  ('731', '10502'),
  ('731', '10576'),
  ('731', '10579'),

  ('732', '10092'),
  ('732', '10093'),
  ('732', '10099'),
  ('732', '10106'),
  ('732', '10112'),
  ('732', '10124'),
  ('732', '10127'),
  ('732', '10277'),
  ('732', '10278'),
  ('732', '10279'),
  ('732', '10280'),
  ('732', '10281'),
  ('732', '10282'),
  ('732', '10283'),
  ('732', '10284'),
  ('732', '10379'),
  ('732', '10381'),
  ('732', '10502'),
  ('732', '10576'),
  ('732', '10579'),

  ('7411', '10379'),
  ('7411', '10381'),
  ('7411', '10382'),

  ('7412', '10379'),
  ('7412', '10380'),
  ('7412', '10381'),
  ('7412', '10382'),
  ('7412', '10383'),
  ('7412', '10384'),
  ('7412', '10385'),
  ('7412', '10386'),

  ('7421', '10379'),
  ('7421', '10502'),
  ('7421', '10504'),
  ('7421', '10508'),

  ('7422', '10379'),
  ('7422', '10502'),
  ('7422', '10503'),
  ('7422', '10504'),
  ('7422', '10505'),
  ('7422', '10506'),
  ('7422', '10507'),
  ('7422', '10508'),
  ('7422', '10509'),

  ('7511', '10379'),
  ('7511', '10381'),
  ('7511', '10392'),
  ('7511', '10395'),
  ('7511', '10398'),

  ('7512', '10379'),
  ('7512', '10381'),
  ('7512', '10392'),
  ('7512', '10393'),
  ('7512', '10394'),
  ('7512', '10395'),
  ('7512', '10396'),
  ('7512', '10397'),
  ('7512', '10398'),
  ('7512', '10399'),
  ('7512', '10400'),
  ('7512', '10402'),
  ('7512', '10406'),

  ('7521', '10166'),
  ('7521', '10169'),
  ('7521', '10170'),
  ('7521', '10379'),
  ('7521', '10381'),
  ('7521', '10392'),
  ('7521', '10395'),
  ('7521', '10398'),
  ('7521', '10440'),
  ('7521', '10576'),
  ('7521', '10579'),

  ('7522', '10166'),
  ('7522', '10167'),
  ('7522', '10168'),
  ('7522', '10169'),
  ('7522', '10170'),
  ('7522', '10171'),
  ('7522', '10172'),
  ('7522', '10173'),
  ('7522', '10174'),
  ('7522', '10379'),
  ('7522', '10381'),
  ('7522', '10392'),
  ('7522', '10395'),
  ('7522', '10397'),
  ('7522', '10398'),
  ('7522', '10400'),
  ('7522', '10401'),
  ('7522', '10402'),
  ('7522', '10403'),
  ('7522', '10404'),
  ('7522', '10405'),
  ('7522', '10440'),
  ('7522', '10576'),
  ('7522', '10579'),

  ('7611', '10379'),
  ('7611', '10502'),
  ('7611', '10618'),
  ('7611', '10622'),
  ('7611', '10624'),
  ('7611', '10627'),
  ('7611', '10631'),

  ('7612', '10379'),
  ('7612', '10502'),
  ('7612', '10618'),
  ('7612', '10622'),
  ('7612', '10623'),
  ('7612', '10624'),
  ('7612', '10625'),
  ('7612', '10626'),
  ('7612', '10627'),
  ('7612', '10628'),
  ('7612', '10629'),
  ('7612', '10631'),
  ('7612', '10632'),
  ('7612', '10636'),

  ('7621', '10440'),
  ('7621', '10619'),
  ('7621', '10621'),
  ('7621', '10622'),
  ('7621', '10631'),

  ('7622', '10174'),
  ('7622', '10440'),
  ('7622', '10619'),
  ('7622', '10621'),
  ('7622', '10622'),
  ('7622', '10629'),
  ('7622', '10630'),
  ('7622', '10631'),
  ('7622', '10633'),
  ('7622', '10634'),
  ('7622', '10635'),

  ('771', '10379'),
  ('771', '10381'),
  ('771', '10440'),
  ('771', '10502'),
  ('771', '10576'),
  ('771', '10579'),
  ('771', '10851'),
  ('771', '10855'),
  ('771', '10858'),
  ('771', '10859'),

  ('772', '10379'),
  ('772', '10381'),
  ('772', '10440'),
  ('772', '10502'),
  ('772', '10576'),
  ('772', '10579'),
  ('772', '10851'),
  ('772', '10852'),
  ('772', '10853'),
  ('772', '10854'),
  ('772', '10855'),
  ('772', '10856'),
  ('772', '10857'),
  ('772', '10858'),
  ('772', '10859'),
  ('772', '10860'),
  ('772', '10861'),

  ('911', '10212'),
  ('911', '10214'),
  ('911', '10216'),
  ('911', '10221'),
  ('911', '10223'),
  ('911', '10228'),
  ('911', '10229'),
  ('911', '10255'),
  ('911', '10257'),
  ('911', '10263'),
  ('911', '10266'),
  ('911', '10267'),
  ('911', '10291'),
  ('911', '10293'),
  ('911', '10434'),
  ('911', '10458'),
  ('911', '10580'),
  ('911', '10591'),

  ('912', '10183'),
  ('912', '10206'),
  ('912', '10208'),
  ('912', '10209'),
  ('912', '10210'),
  ('912', '10211'),
  ('912', '10212'),
  ('912', '10213'),
  ('912', '10214'),
  ('912', '10215'),
  ('912', '10216'),
  ('912', '10221'),
  ('912', '10222'),
  ('912', '10223'),
  ('912', '10224'),
  ('912', '10225'),
  ('912', '10226'),
  ('912', '10227'),
  ('912', '10228'),
  ('912', '10229'),
  ('912', '10230'),
  ('912', '10231'),
  ('912', '10255'),
  ('912', '10257'),
  ('912', '10263'),
  ('912', '10264'),
  ('912', '10265'),
  ('912', '10266'),
  ('912', '10267'),
  ('912', '10268'),
  ('912', '10291'),
  ('912', '10292'),
  ('912', '10293'),
  ('912', '10294'),
  ('912', '10295'),
  ('912', '10296'),
  ('912', '10434'),
  ('912', '10458'),
  ('912', '10459'),
  ('912', '10460'),
  ('912', '10461'),
  ('912', '10462'),
  ('912', '10580'),
  ('912', '10591'),
  ('912', '10592'),
  ('912', '10593'),

  ('92', '10519'),
  ('92', '10520'),
  ('92', '10521'),
  ('92', '10522'),
  ('92', '10523'),
  ('92', '10524'),
  ('92', '10525'),
  ('92', '10579'),
  ('92', '10602'),
  ('92', '10603'),
  ('92', '10604'),
  ('92', '10605'),
  ('92', '10606'),
  ('92', '10607'),
  ('92', '10608'),
  ('92', '10609'),
  ('92', '10613'),
  ('92', '10614'),
  ('92', '10615'),

  ('931', '10440'),
  ('931', '10604'),
  ('931', '10894'),
  ('931', '10902'),
  ('931', '10943'),
  ('931', '10945'),
  ('931', '10947'),

  ('932', '10440'),
  ('932', '10604'),
  ('932', '10894'),
  ('932', '10902'),
  ('932', '10943'),
  ('932', '10944'),
  ('932', '10945'),
  ('932', '10946'),
  ('932', '10947'),
  ('932', '10948'),
  ('932', '10949'),

  ('94', '10569'),
  ('94', '10570'),
  ('94', '10571'),
  ('94', '10572'),
  ('94', '10573'),
  ('94', '10574'),
  ('94', '10575'),

  ('95', '10184'),
  ('95', '10185'),
  ('95', '10186'),
  ('95', '10187'),
  ('95', '10188'),

  ('96', '10279'),
  ('96', '10379'),
  ('96', '10381'),
  ('96', '10472'),
  ('96', '10502'),
  ('96', '10551'),
  ('96', '10552'),
  ('96', '10553'),
  ('96', '10554'),
  ('96', '10555'),
  ('96', '10556'),
  ('96', '10557'),
  ('96', '10558'),
  ('96', '10559'),
  ('96', '10560'),
  ('96', '10561'),
  ('96', '10562'),
  ('96', '10563'),
  ('96', '10564'),
  ('96', '10565'),
  ('96', '10566'),
  ('96', '10567'),
  ('96', '10568'),
  ('96', '10576'),
  ('96', '10579'),

  ('97', '10241'),
  ('97', '10242'),
  ('97', '10243'),
  ('97', '10244'),
  ('97', '10245'),
  ('97', '10246'),
  ('97', '10247'),
  ('97', '10248'),
  ('97', '10249'),
  ('97', '10250'),
  ('97', '10251'),
  ('97', '10252'),
  ('97', '10253'),
  ('97', '10552'),

  ('1011', '10255'),
  ('1011', '10434'),
  ('1011', '10440'),
  ('1011', '10442'),
  ('1011', '10443'),
  ('1011', '10444'),
  ('1011', '10445'),
  ('1011', '10446'),
  ('1011', '10447'),
  ('1011', '10449'),

  ('1012', '10255'),
  ('1012', '10434'),
  ('1012', '10440'),
  ('1012', '10441'),
  ('1012', '10442'),
  ('1012', '10443'),
  ('1012', '10444'),
  ('1012', '10445'),
  ('1012', '10446'),
  ('1012', '10447'),
  ('1012', '10448'),
  ('1012', '10449'),
  ('1012', '10450'),
  ('1012', '10451'),
  ('1012', '10452'),
  ('1012', '10453'),
  ('1012', '10454'),
  ('1012', '10455'),
  ('1012', '10456'),
  ('1012', '10457'),

  ('1021', '10255'),
  ('1021', '10257'),
  ('1021', '10260'),
  ('1021', '10316'),
  ('1021', '10317'),
  ('1021', '10319'),
  ('1021', '10434'),
  ('1021', '10648'),
  ('1021', '10650'),

  ('1022', '10254'),
  ('1022', '10255'),
  ('1022', '10256'),
  ('1022', '10257'),
  ('1022', '10258'),
  ('1022', '10259'),
  ('1022', '10260'),
  ('1022', '10261'),
  ('1022', '10262'),
  ('1022', '10316'),
  ('1022', '10317'),
  ('1022', '10318'),
  ('1022', '10319'),
  ('1022', '10320'),
  ('1022', '10434'),
  ('1022', '10435'),
  ('1022', '10648'),
  ('1022', '10649'),
  ('1022', '10650'),
  ('1022', '10651'),

  ('981', '10436'),

  ('982', '10436'),
  ('982', '10438'),
  ('982', '10439');
