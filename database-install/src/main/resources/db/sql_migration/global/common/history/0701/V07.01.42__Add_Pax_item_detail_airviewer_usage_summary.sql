DROP TABLE IF EXISTS pax_item_detail_airviewer_usage_summary;
DROP TABLE IF EXISTS pax_market_billing_item_detail_airviewer_usage_summary;
CREATE TABLE `pax_market_billing_item_detail_airviewer_usage_summary` (
  `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
  `market_id`  int(11)  NOT NULL,
  `reseller_id`  int(11)  NOT NULL,
  `year` int(11) not null ,
  `month` int(11) not null ,
  `usage_amount` bigint(32) not null,
  `created_date` datetime NOT NULL default current_timestamp ,
  `created_by` int(11),
  `updated_date` datetime NOT NULL default current_timestamp ,
  `updated_by` int(11),
  PRIMARY KEY ( `id` )
) ;
