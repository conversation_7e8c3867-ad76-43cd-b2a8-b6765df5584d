DROP TABLE IF EXISTS pax_terminal_last_access_time;
CREATE TABLE `pax_terminal_last_access_time`
(
    `terminal_id`        bigint(20)  NOT NULL,
    `last_access_time`       datetime NOT NULL,
    PRIMARY KEY (`terminal_id`)
) COMMENT ='终端最后访问时间表';

INSERT INTO pax_terminal_last_access_time
(terminal_id, last_access_time)
SELECT id, last_access_time
FROM pax_terminal WHERE last_access_time IS NOT NULL AND del_flag = 0;

DROP TABLE IF EXISTS pax_terminal_protocol;
CREATE TABLE `pax_terminal_protocol`
(
    `terminal_id`        bigint(20)  NOT NULL,
    `protocol`       tinyint(1) NOT NULL DEFAULT '0',
    PRIMARY KEY (`terminal_id`)
) COMMENT ='终端协议表';

INSERT INTO pax_terminal_protocol
(terminal_id, protocol)
SELECT id, protocol
FROM pax_terminal WHERE protocol != 0 AND del_flag = 0;