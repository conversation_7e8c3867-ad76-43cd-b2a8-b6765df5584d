DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'updateMarketBillingDefaultPrice_1', '/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10001', 'getMarketBillingDefaultPrice_1', '/defaultSettings/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10002', 'error', '/error', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10003', 'error_3', '/error', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10004', 'error_2', '/error', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10005', 'error_5', '/error', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10006', 'error_6', '/error', 'OPTIONS', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10007', 'error_1', '/error', 'HEAD', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10008', 'error_4', '/error', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10009', 'sendMessageToTerminal', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10010', 'sendMessageByTag', '/v1/3rd/cloudmsg/bytag', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10011', 'sendMessageToSingleTerminal', '/v1/3rd/cloudmsg/single', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10012', 'getMessageArrivalRate', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10013', 'submitApkInfo', '/v1/3rd/developer/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'rkiCallback', '/v1/3rd/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10015', 'findApkParameters', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'createApkTemplate', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'getApkParameterById', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'updateApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'deleteApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'searchApps_3', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'searchEntityAttributes', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'createEntityAttribute_1', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'getEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'updateEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10025', 'deleteEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'updateEntityAttributeLabel_1', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'verifyEstateBySerialNo', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'findDataFromInsight', '/v1/3rdsys/goInsight/data/app-biz', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'findMerchantVariablePage_1', '/v1/3rdsys/merchant/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10030', 'createMerchantVariable_1', '/v1/3rdsys/merchant/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'batchDeleteMerchantVariables_1', '/v1/3rdsys/merchant/variables/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'updateMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'deleteMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'getMerchantCategories', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'createMerchantCategory_1', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'batchCreateMerchantCategories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'updateMerchantCategory_1', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'deleteCategory', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'searchMerchant', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'createMerchant_1', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10041', 'getMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10042', 'updateMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10043', 'deleteMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10044', 'activeMerchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10045', 'disableMerchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10046', 'replaceMerchantEmail_1', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10047', 'findParameterPushHistory', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10048', 'searchReseller_1', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10049', 'createReseller_1', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10050', 'getReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10051', 'updateReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10052', 'deleteReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10053', 'activeReseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10054', 'disableReseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10055', 'replaceResellerEmail_1', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10056', 'searchResellerRkiKey', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10057', 'searchTerminalApkPage', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10058', 'createTerminalApk', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10059', 'suspendTerminalApk_1', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10060', 'uninstallTerminalApk', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10061', 'getTerminalApk_1', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10062', 'searchTerminalFmPage', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10063', 'createTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10064', 'suspendTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10065', 'getTerminalFm', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10066', 'searchTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10067', 'createTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10068', 'getTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10069', 'deleteTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10070', 'suspendTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10071', 'searchTerminalGroupRkiPage', '/v1/3rdsys/terminalGroupRki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10072', 'createGroupRki_1', '/v1/3rdsys/terminalGroupRki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10073', 'getGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10074', 'suspendGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10075', 'searchGroups_2', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10076', 'createGroup_1', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10077', 'searchTerminal_2', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10078', 'getGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10079', 'updateGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10080', 'deleteGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10081', 'activeGroup', '/v1/3rdsys/terminalGroups/{groupId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10082', 'disableGroup', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10083', 'searchGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10084', 'removeGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10085', 'createGroupTerminals_2', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10086', 'searchTerminalRkiPage', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10087', 'createTerminalRki_1', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10088', 'suspendTerminalRki_1', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10089', 'getTerminalRki_1', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10090', 'findTerminalVariableList', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10091', 'createTerminalVariable_1', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10092', 'batchDeleteTerminalVariables_1', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10093', 'updateTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10094', 'deleteTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10095', 'findTerminals', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10096', 'createTerminal_1', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10097', 'activateTerminal2', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10098', 'copyTerminal_1', '/v1/3rdsys/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10099', 'createTerminalsGroup', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10100', 'getTerminalNetwork', '/v1/3rdsys/terminals/network', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10101', 'getTerminal_2', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10102', 'updateTerminal_2', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10103', 'delTerminal', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10104', 'activateTerminal', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10105', 'getTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10106', 'updateTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10107', 'disableTerminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10108', 'moveTerminal_1', '/v1/3rdsys/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10109', 'pushTerminalAction', '/v1/3rdsys/terminals/{terminalId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10110', 'getTerminalPed', '/v1/3rdsys/terminals/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10111', 'transfRequest', '/v1/3rdsys/upt/route-request', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10112', 'updateUptrillionSecurityInfo', '/v1/3rdsys/upt/security', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10113', 'getUser_6', '/v1/account/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10114', 'getMarket_6', '/v1/account/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10115', 'resetUserPassword_2', '/v1/account/public/{userId}/reset-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10116', 'setDeveloperReceivableAccount', '/v1/account/receivable/developer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10117', 'setMarketReceivableAccount', '/v1/account/receivable/market', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10118', 'deleteAccount', '/v1/account/user', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10119', 'changeUserPwd', '/v1/account/user/change-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10120', 'sendDeleteAccountCode', '/v1/account/user/delete', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10121', 'listUserConfig', '/v1/account/user/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10122', 'updateUserConfig', '/v1/account/user/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10123', 'publishGlobalNotification', '/v1/account/user/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10124', 'listMessages', '/v1/account/user/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10125', 'readMessages', '/v1/account/user/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10126', 'deleteMessages', '/v1/account/user/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10127', 'readAllMessage', '/v1/account/user/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10128', 'deleteMessage', '/v1/account/user/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10129', 'downloadMessageAttachment', '/v1/account/user/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10130', 'viewMessageDetails_1', '/v1/account/user/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10131', 'listTopics', '/v1/account/user/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10132', 'unsubscribeAllTopics', '/v1/account/user/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10133', 'getTopicSubscription_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10134', 'subscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10135', 'unsubscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10136', 'getOTP', '/v1/account/user/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10137', 'activateOTP', '/v1/account/user/otp/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10138', 'disableOTP', '/v1/account/user/otp/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10139', 'bindOTP', '/v1/account/user/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10140', 'resetOtpBackupCode', '/v1/account/user/otp/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10141', 'getUserProfile', '/v1/account/user/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10142', 'updateUser', '/v1/account/user/profile', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10143', 'resetUserEmail', '/v1/account/user/reset-email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10144', 'updateAllowSendUsageData', '/v1/account/user/send-usage', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10145', 'findActivityPage', '/v1/admin/activities', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10146', 'batchDeleteActivities', '/v1/admin/activities/batch', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10147', 'getActivity', '/v1/admin/activities/{activityId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10148', 'deleteActivity', '/v1/admin/activities/{activityId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10149', 'searchAlarm', '/v1/admin/alarm', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10150', 'getAlarmSetting', '/v1/admin/alarm/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10151', 'saveAlarmSetting', '/v1/admin/alarm/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10152', 'searchRoles_2', '/v1/admin/alarm/setting/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10153', 'searchRoleUsers_1', '/v1/admin/alarm/setting/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10154', 'findAlarmTypeList', '/v1/admin/alarm/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10155', 'getAlarmWidgets', '/v1/admin/alarm/widgets/digital', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10156', 'createExportAlarmDownloadTasks', '/v1/admin/alarm/widgets/{type}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10157', 'searchApps_2', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10158', 'getApkInfo_1', '/v1/admin/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10159', 'createApkDownloadTask', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10160', 'findSpecificApkMarketPage', '/v1/admin/apps/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10161', 'specificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10162', 'deleteSpecificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10163', 'findSpecificApkMarketAllListPage', '/v1/admin/apps/apks/{apkId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10164', 'createApkParamTemplateDownloadTask_1', '/v1/admin/apps/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10165', 'deleteApkParamTemplate', '/v1/admin/apps/apks/{apkId}/param-template', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10166', 'findSpecificApkResellerPage_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10167', 'specificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10168', 'deleteSpecificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10169', 'findSpecificApkResellerAllListPage_2', '/v1/admin/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10170', 'reSignApk_3', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', 'findApkSignatureList_3', '/v1/admin/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', 'getPendingApprovalAppCount', '/v1/admin/apps/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', 'getTopicSubscription_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', 'subscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', 'unsubscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', 'getAppInfo_1', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', 'deleteApp_1', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', 'onlineApp', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10179', 'searchApk_1', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', 'deleteApk_1', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', 'approveApp', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', 'downloadApk', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', 'offlineApk_1', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10184', 'onlineApk', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', 'addApkParamTemplate', '/v1/admin/apps/{appId}/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', 'rejectApp', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', 'updateApkReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/release-note', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', 'updateApkModel', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', 'updateAppAutoUpdate', '/v1/admin/apps/{appId}/auto-update/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', 'updateAppDeveloper', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', 'offlineApp', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', 'updateAppDownloadAuthentication', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', 'getBizDataFromGoInsight_3', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', 'findSpecificAppMarketPage', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', 'specificMarketApp', '/v1/admin/apps/{appId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', 'deleteSpecificAppMarket', '/v1/admin/apps/{appId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', 'findSpecificAppMarketAllListPage', '/v1/admin/apps/{appId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', 'findSpecificAppMerchantCategoryPage', '/v1/admin/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', 'specificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', 'deleteSpecificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', 'findSpecificAppResellerPage_2', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', 'specificAppReseller_2', '/v1/admin/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', 'deleteSpecificResellerApp_1', '/v1/admin/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', 'findSpecificAppResellerAllListPage_2', '/v1/admin/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', 'resumeApp', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10206', 'getAppSettingVo_2', '/v1/admin/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10207', 'getAppVasSettingVo_3', '/v1/admin/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10208', 'updateAppVisualScope', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', 'searchAuthLog', '/v1/admin/audit-log/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', 'exportAuthLog', '/v1/admin/audit-log/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', 'searchOperationLog', '/v1/admin/audit-log/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', 'exportAuditLog', '/v1/admin/audit-log/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10213', 'getAuditLogParamDetail', '/v1/admin/audit-log/operations/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', 'getExistAuditTypes', '/v1/admin/audit-log/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10215', 'findClientApp', '/v1/admin/client-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10216', 'findClientApk', '/v1/admin/client-apps-approval/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10217', 'findClientAppFactory', '/v1/admin/client-apps-approval/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', 'findFirmwares', '/v1/admin/client-apps-approval/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10219', 'approveClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10220', 'offlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10221', 'onlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10222', 'rejectClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10223', 'getClientApk', '/v1/admin/client-apps-common/client-apks/{clientApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10224', 'createClientApkDownloadTask', '/v1/admin/client-apps-common/client-apks/{clientApkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10225', 'deleteClientApk', '/v1/admin/client-apps-common/{clientAppId}/client-apks/{clientApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10226', 'findClientApkFirmwarePage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10227', 'createClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10228', 'updateClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10229', 'removeClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10230', 'findClientMarketPage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10231', 'getClientMarketSummary', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10232', 'saveClientPublishAmount', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-amount', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10233', 'addGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10234', 'removeGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10235', 'updateClientApkPublishRange', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-range', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10236', 'getClientApp', '/v1/admin/client-apps/{clientAppId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10237', 'findClientApkByAppId', '/v1/admin/client-apps/{clientAppId}/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10238', 'addNewClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10239', 'updateClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10240', 'updateClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10241', 'submitClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10242', 'addFistClientApkFile', '/v1/admin/client-apps/{factoryId}/client-apks/file/first', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10243', 'getIOTPlatformAccessUrl', '/v1/admin/cloudservice/iot/access/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10244', 'ping', '/v1/admin/cloudservice/refresh/ping', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10245', 'getUrl', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10246', 'findApkParameterPage_1', '/v1/admin/common/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10247', 'createApkParameterDataFileDownloadTask_1', '/v1/admin/common/app/apk/parameters/{apkParameterId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10248', 'getApkParameterSchemaInfo', '/v1/admin/common/app/apk/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10249', 'getApkDetailVo_4', '/v1/admin/common/app/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10250', 'findOnlineAppPage_1', '/v1/admin/common/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', 'findEntityAttributePage_1', '/v1/admin/common/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', 'findFactoryPage_1', '/v1/admin/common/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', 'findFactoryModelTree', '/v1/admin/common/factory/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10254', 'findFirmwarePage', '/v1/admin/common/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', 'createTerminalFirmwareFileDownloadTask', '/v1/admin/common/firmwares/file/{fmFileId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10256', 'getFirmwareDetailVo_2', '/v1/admin/common/firmwares/{firmwareId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', 'searchMarkets_3', '/v1/admin/common/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', 'findMerchantCategoryPage_1', '/v1/admin/common/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', 'findMerchantPage', '/v1/admin/common/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', 'findModelPage_2', '/v1/admin/common/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', 'findRkiTemplateKeyPage', '/v1/admin/common/reseller/{resellerId}/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', 'findResellerPage', '/v1/admin/common/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10263', 'findResellerTreePage', '/v1/admin/common/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10264', 'getMarket_5', '/v1/admin/current-market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10265', 'getUser_5', '/v1/admin/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10266', 'searchPendingApps', '/v1/admin/dashboard/apps-pending', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10267', 'searchAppsTop10', '/v1/admin/dashboard/apps-top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10268', 'getDashboardLayout', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10269', 'saveDashboard', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10270', 'searchMarkers_1', '/v1/admin/dashboard/map-markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', 'searchTerminalsByPlace_1', '/v1/admin/dashboard/map-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', 'getResellerProfile_2', '/v1/admin/dashboard/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', 'getTerminalNumberStatisticData_1', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', 'getTerminalNumberOfResellerData_1', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', 'getFmTerminalForWidget', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', 'exportFmTerminalOrgWidget', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10277', 'getClientTerminalWidget', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10278', 'exportClientTerminalWidget', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10279', 'loadWidgetModelTerminal_1', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10280', 'exportModelTerminalWidget', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10281', 'loadWidgetTerminalOffline_1', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10282', 'exportTerminalOfflineWidget', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10283', 'getFmTerminalWidget', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10284', 'exportFmTerminalWidget', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10285', 'loadClientTerminalWidget', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10286', 'downloadClientTerminalWidget', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10287', 'createExportTerminalsDownloadTask_1', '/v1/admin/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10288', 'getWidgetCardNumberActive', '/v1/admin/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10289', 'getWidgetDigitalDisplaySetting', '/v1/admin/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10290', 'updateWidgetDigitalDisplay', '/v1/admin/dashboard/widgets/W20/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10291', 'getPukTerminalWidget', '/v1/admin/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10292', 'exportPUKTerminalWidget', '/v1/admin/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10293', 'findPageList_1', '/v1/admin/datasource/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10294', 'createDataSourceInfo', '/v1/admin/datasource/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10295', 'changeStatus', '/v1/admin/datasource/info/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10296', 'testConnection', '/v1/admin/datasource/info/testConnection', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10297', 'findById_1', '/v1/admin/datasource/info/{dataSourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10298', 'updateModel_2', '/v1/admin/datasource/info/{dataSourceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10299', 'deleteById_1', '/v1/admin/datasource/info/{dataSourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10300', 'findPageList', '/v1/admin/datasource/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10301', 'createDataSourceMarket', '/v1/admin/datasource/market', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10302', 'findById', '/v1/admin/datasource/market/{configId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10303', 'updateModel_1', '/v1/admin/datasource/market/{configId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10304', 'deleteById', '/v1/admin/datasource/market/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10305', 'searchDevelopers', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10306', 'exportDevelopers', '/v1/admin/developers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10307', 'getDeveloper_1', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10308', 'updateDeveloper', '/v1/admin/developers/{developerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10309', 'deleteDeveloper', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10310', 'activeDeveloper3rdSysAccess_1', '/v1/admin/developers/{developerId}/3rd-system/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10311', 'disableDeveloper3rdSysAccess', '/v1/admin/developers/{developerId}/3rd-system/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10312', 'approveDeveloper', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10313', 'updateAllowIndustrySolution', '/v1/admin/developers/{developerId}/industry-solution', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10314', 'approveGlobalDeveloperPayment', '/v1/admin/developers/{developerId}/pay/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10315', 'rejectDeveloperPayment', '/v1/admin/developers/{developerId}/pay/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10316', 'rejectDeveloper', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10317', 'specificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10318', 'changeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10319', 'closeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10320', 'resumeDeveloper', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10321', 'findDeveloperSandboxTerminalPage', '/v1/admin/developers/{developerId}/sandbox/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10322', 'deleteSandboxTerminal', '/v1/admin/developers/{developerId}/sandbox/terminal/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', 'findDeveloperServices', '/v1/admin/developers/{developerId}/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', 'updateDeveloperSuperAdmin_1', '/v1/admin/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', 'suspendDeveloper', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', 'activeDeveloperUser', '/v1/admin/developers/{developerId}/user/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', 'findEnterpriseDeveloperUserPage', '/v1/admin/developers/{developerId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', 'findFactoryPage', '/v1/admin/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', 'createFactory', '/v1/admin/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', 'getSignatureProviderList', '/v1/admin/factories/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', 'getFactoryDetail', '/v1/admin/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', 'updateFactory', '/v1/admin/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', 'deleteFactory', '/v1/admin/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', 'findSpecificFactoryMarketPage', '/v1/admin/factories/{factoryId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', 'specificFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', 'deleteFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', 'findSpecificFactoryMarketAllList', '/v1/admin/factories/{factoryId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', 'searchFirmware_2', '/v1/admin/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', 'searchFirmware_3', '/v1/admin/firmwares-approval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', 'listFirmwareFactory', '/v1/admin/firmwares-approval/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', 'getFirmware_1', '/v1/admin/firmwares-approval/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', 'approveFirmware', '/v1/admin/firmwares-approval/{firmwareId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10343', 'specificMarketFirmware', '/v1/admin/firmwares-approval/{firmwareId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10344', 'findSpecificFirmwareMarketAllPage', '/v1/admin/firmwares-approval/{firmwareId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10345', 'offlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10346', 'onlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10347', 'rejectFirmware', '/v1/admin/firmwares-approval/{firmwareId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10348', 'specificResellerFirmware_2', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10349', 'findFirmwareResellerSpecificAllPage', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', 'deleteFirmwareDiff', '/v1/admin/firmwares-common/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', 'deleteFirmware', '/v1/admin/firmwares-common/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', 'uploadFirmwareDiffFile', '/v1/admin/firmwares-common/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', 'uploadFirmwareFile', '/v1/admin/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', 'getFirmware', '/v1/admin/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', 'updateFirmware', '/v1/admin/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', 'getFirmwareForEditPage', '/v1/admin/firmwares/{firmwareId}/edit-page', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10357', 'submitFirmware', '/v1/admin/firmwares/{firmwareId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10358', 'searchMarkers', '/v1/admin/geo-location/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10359', 'searchMarkersGodPerspective', '/v1/admin/geo-location/markers/god-perspective', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10360', 'searchMarket', '/v1/admin/geo-location/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10361', 'getMarket_4', '/v1/admin/geo-location/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10362', 'getResellerProfile_1', '/v1/admin/geo-location/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10363', 'searchReseller', '/v1/admin/geo-location/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', 'getReseller', '/v1/admin/geo-location/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', 'searchTerminalsByPlace', '/v1/admin/geo-location/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', 'getTerminalDetail', '/v1/admin/geo-location/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', 'searchMarkets_2', '/v1/admin/global/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', 'createMarket', '/v1/admin/global/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', 'findRkiServerList', '/v1/admin/global/markets/rki-servers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', 'getMarketInfoSummary', '/v1/admin/global/markets/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', 'findAppNumDetail', '/v1/admin/global/markets/statistics/app-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10372', 'findDeveloperNumDetail', '/v1/admin/global/markets/statistics/developer-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10373', 'createExportStatisticsDownloadTask', '/v1/admin/global/markets/statistics/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10374', 'findMarketNumDetail', '/v1/admin/global/markets/statistics/market-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10375', 'sendMarketTerminalReport', '/v1/admin/global/markets/terminal-report', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10376', 'getMarket_3', '/v1/admin/global/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10377', 'updateMarket', '/v1/admin/global/markets/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10378', 'deleteMarket', '/v1/admin/global/markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10379', 'updateMarketBillingSetting', '/v1/admin/global/markets/{marketId}/billing', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', 'getMarketIconStatus', '/v1/admin/global/markets/{marketId}/icon-status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', 'updateOverdueMarket', '/v1/admin/global/markets/{marketId}/overdue', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', 'getMarketPermissions', '/v1/admin/global/markets/{marketId}/permissions/{functionType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', 'updateMarketBillingPriceSetting', '/v1/admin/global/markets/{marketId}/price-setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', 'findMarketBillingPriceSettings', '/v1/admin/global/markets/{marketId}/price-settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', 'replaceMarketResellerEmail', '/v1/admin/global/markets/{marketId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', 'sendMarketActivateEmail', '/v1/admin/global/markets/{marketId}/resend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', 'resumeMarket', '/v1/admin/global/markets/{marketId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10388', 'getMarketServiceSetting', '/v1/admin/global/markets/{marketId}/service-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10389', 'getMarketSummary', '/v1/admin/global/markets/{marketId}/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10390', 'suspendMarket', '/v1/admin/global/markets/{marketId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10391', 'getReportMetadataForCreateAndUpdate', '/v1/admin/global/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10392', 'getReport_1', '/v1/admin/global/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10393', 'updateReport', '/v1/admin/global/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10394', 'activateReport', '/v1/admin/global/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10395', 'createDownloadBandFileTask', '/v1/admin/global/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10396', 'disableReport', '/v1/admin/global/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10397', 'createDownloadTaskForTemplateFile', '/v1/admin/global/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10398', 'findLauncherTemplatePage', '/v1/admin/launcher/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10399', 'createLauncherTemplate', '/v1/admin/launcher/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10400', 'findResellerOnlineAppPage', '/v1/admin/launcher/templates/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', 'getResellerOnlineApkNameAndIcon_1', '/v1/admin/launcher/templates/reseller/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', 'getLauncherTemplate_1', '/v1/admin/launcher/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10403', 'updateLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10404', 'deleteLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10405', 'activeMarket3rdSysAccess', '/v1/admin/market/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10406', 'getMarket3rdSysConfig', '/v1/admin/market/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10407', 'deActiveMarket3rdSysAccess', '/v1/admin/market/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10408', 'add3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10409', 'update3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10410', 'delete3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10411', 'getMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10412', 'resetMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10413', 'find3rdSysWebHookPage_1', '/v1/admin/market/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10414', 'create3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10415', 'getWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10416', 'get3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10417', 'update3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10418', 'delete3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10419', 'findWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10420', 'test3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10421', 'findAnnouncementPage', '/v1/admin/market/advance/announcement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10422', 'createAnnouncementNotification', '/v1/admin/market/advance/announcement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10423', 'publishAnnouncementNotification', '/v1/admin/market/advance/announcement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10424', 'getAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10425', 'updateAnnouncementNotification', '/v1/admin/market/advance/announcement/{announcementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10426', 'deleteAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10427', 'findAppWhiteListPage_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10428', 'createAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10429', 'deleteAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10430', 'findPage', '/v1/admin/market/advance/ip-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10431', 'createAccessIp', '/v1/admin/market/advance/ip-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10432', 'getAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10433', 'closeAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10434', 'openAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/open', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10435', 'updateAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10436', 'deleteAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10437', 'getMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10438', 'createMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10439', 'deleteSensitiveWordId', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10440', 'createSensitiveWordFileDownloadTask', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10441', 'findAppBlackListPage', '/v1/admin/market/app-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10442', 'updateAppBlackList', '/v1/admin/market/app-blacklist', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10443', 'createAppBlackList', '/v1/admin/market/app-blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10444', 'deleteAppBlackList', '/v1/admin/market/app-blacklist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10445', 'findAppWhiteListPage', '/v1/admin/market/app-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10446', 'createAppWhiteList', '/v1/admin/market/app-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10447', 'deleteAppWhiteList', '/v1/admin/market/app-whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10448', 'findEntityAttributePage', '/v1/admin/market/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10449', 'createEntityAttribute', '/v1/admin/market/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10450', 'getEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10451', 'updateEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10452', 'deleteEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10453', 'updateEntityAttributeLabel', '/v1/admin/market/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10454', 'loadCurrentPeriodAppDownloadAppList', '/v1/admin/market/billing/app/download/fee/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10455', 'downloadAppDownloadList', '/v1/admin/market/billing/app/download/fee/app/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10456', 'loadCurrentPeriodGlobalAppDownloadMarketList', '/v1/admin/market/billing/app/download/fee/app/{appId}/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10457', 'downloadAppPurchaseMarketList', '/v1/admin/market/billing/app/download/fee/app/{appId}/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10458', 'loadCurrentPeriodTerminalDownloadAppList', '/v1/admin/market/billing/app/download/fee/app/{appId}/terminal/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10459', 'downloadAppPurchaseTerminalList_1', '/v1/admin/market/billing/app/download/fee/app/{appId}/terminal/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10460', 'loadCurrentPeriodAppDownloadInfo', '/v1/admin/market/billing/app/download/fee/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10461', 'loadCurrentPeriodAppDownloadLast6', '/v1/admin/market/billing/app/download/fee/dashboard/last6', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10462', 'loadCurrentPeriodAppDownloadTop10', '/v1/admin/market/billing/app/download/fee/dashboard/top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10463', 'loadAppDownloadDeveloperList', '/v1/admin/market/billing/app/download/fee/developer/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10464', 'downloadAppPurchaseDeveloperList', '/v1/admin/market/billing/app/download/fee/developer/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10465', 'loadAppDownloadDeveloperAppList', '/v1/admin/market/billing/app/download/fee/developer/{developerId}/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10466', 'loadAppDownloadFeeHistory', '/v1/admin/market/billing/app/download/fee/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10467', 'downloadAppPurchaseHistoryList', '/v1/admin/market/billing/app/download/fee/history/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10468', 'loadGlobalAppDownloadMarketList_1', '/v1/admin/market/billing/app/download/fee/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10469', 'downloadGlobalAppPurchaseMarketList', '/v1/admin/market/billing/app/download/fee/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10470', 'loadGlobalAppDownloadMarketList', '/v1/admin/market/billing/app/download/fee/market/{marketId}/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10471', 'downloadMarketAppPurchaseList', '/v1/admin/market/billing/app/download/fee/{marketId}/purchase/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10472', 'updateBillingServicePrice_1', '/v1/admin/market/billing/change/price/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10473', 'getCurrentBilling', '/v1/admin/market/billing/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10474', 'updateMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10475', 'getMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10476', 'downloadGlobalTimePeriodBilling', '/v1/admin/market/billing/global/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10477', 'loadGlobalSingleMonthBill', '/v1/admin/market/billing/global/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10478', 'loadTimePeriodBilling', '/v1/admin/market/billing/global/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10479', 'loadTotalBillItemTimePeriod', '/v1/admin/market/billing/global/total/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10480', 'getUnreceivedAmount', '/v1/admin/market/billing/global/unreceived/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10481', 'findGlobalUnresolvedInvoices', '/v1/admin/market/billing/global/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10482', 'downloadPaidAppStatistics', '/v1/admin/market/billing/income/app/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10483', 'getSingleMonthIncome', '/v1/admin/market/billing/income/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10484', 'getSingleMonthTotalIncome_1', '/v1/admin/market/billing/income/single/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10485', 'searchMarketPaidAppIncome', '/v1/admin/market/billing/income/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10486', 'getSingleMonthTotalIncome', '/v1/admin/market/billing/income/time/period/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10487', 'downloadAppPurchaseTerminalList', '/v1/admin/market/billing/income/{appId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10488', 'reRunBillingJob', '/v1/admin/market/billing/job', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10489', 'findOperationLog', '/v1/admin/market/billing/log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10490', 'findBillingServiceDetailLog', '/v1/admin/market/billing/log/detail/{batchId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10491', 'downloadPaymentBillHistory', '/v1/admin/market/billing/market/{marketId}/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10492', 'getMarketSingleMonthBilling', '/v1/admin/market/billing/market/{marketId}/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10493', 'getMarketTimePeriodBilling', '/v1/admin/market/billing/market/{marketId}/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10494', 'searchPayment', '/v1/admin/market/billing/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10495', 'checkPaymentStatus', '/v1/admin/market/billing/payment/check', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10496', 'confirmPayBilling', '/v1/admin/market/billing/payment/confirm', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10497', 'initPayment', '/v1/admin/market/billing/payment/init', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10498', 'loadAppDownloadFeeRevenueShareList', '/v1/admin/market/billing/revenue/share', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10499', 'startTransferPurchaseClearence_1', '/v1/admin/market/billing/revenue/share/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10500', 'loadRevenueShareMarketDeveloperList', '/v1/admin/market/billing/revenue/share/developer/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10501', 'downloadRevenueShareHistoryList', '/v1/admin/market/billing/revenue/share/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10502', 'loadAppDownloadFeeRevenueShareMarketList', '/v1/admin/market/billing/revenue/share/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10503', 'downloadRevenueShareMarketList', '/v1/admin/market/billing/revenue/share/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10504', 'sendBillingInvoice', '/v1/admin/market/billing/send/invoice/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10505', 'getMarketInvoice', '/v1/admin/market/billing/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10506', 'updateMarketInvoice', '/v1/admin/market/billing/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10507', 'getSubMarketAppDownloadFeeProfitRate', '/v1/admin/market/billing/settings/app/profit/rate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10508', 'updateSubMarketAppDownloadFeeProfitRate', '/v1/admin/market/billing/settings/app/profit/rate', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10509', 'loadGlobalAppDownloadFeeDefaultProfitRate', '/v1/admin/market/billing/settings/app/profit/rate/default', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10510', 'updateGlobalAppDownloadFeeDefaultProfitRate', '/v1/admin/market/billing/settings/app/profit/rate/default', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10511', 'findBillingReceiveEmailList', '/v1/admin/market/billing/settings/email', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10512', 'createReceiveEmail', '/v1/admin/market/billing/settings/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10513', 'updateReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10514', 'deleteReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10515', 'getMarketReceivableAccount', '/v1/admin/market/billing/settings/receivable/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10516', 'removeMarketReceivableAccount', '/v1/admin/market/billing/settings/receivable/account', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10517', 'getSingleMonthBilling', '/v1/admin/market/billing/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10518', 'updateBillingStatusToAudit', '/v1/admin/market/billing/status/audit/{billingSummaryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10519', 'loadSummaryBillItemTimePeriod', '/v1/admin/market/billing/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10520', 'getUnPaidAmountPayable', '/v1/admin/market/billing/unPaid/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10521', 'findHistoryUnresolvedInvoices', '/v1/admin/market/billing/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10522', 'findMerchantCategoryPage', '/v1/admin/market/merchant-categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10523', 'createMerchantCategory', '/v1/admin/market/merchant-categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10524', 'batchDeleteMerchantCategories', '/v1/admin/market/merchant-categories/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10525', 'importMerchantCategory', '/v1/admin/market/merchant-categories/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10526', 'createMerchantCategoryImportTemplateDownloadTask', '/v1/admin/market/merchant-categories/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10527', 'updateMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10528', 'deleteMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10529', 'specificModel2Market', '/v1/admin/market/model/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10530', 'findFactoryIncludeModelPage', '/v1/admin/market/model/settings/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10531', 'findModelPage_1', '/v1/admin/market/model/settings/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10532', 'getMarketSetting', '/v1/admin/market/settings', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10533', 'saveMarketSettings', '/v1/admin/market/settings', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10534', 'activateMarket', '/v1/admin/market/settings/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10535', 'findAgreementPage', '/v1/admin/market/settings/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10536', 'createAgreement', '/v1/admin/market/settings/agreement', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10537', 'findAgreementSettingPage', '/v1/admin/market/settings/agreement/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10538', 'saveAgreementSettings', '/v1/admin/market/settings/agreement/config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10539', 'publishAgreement', '/v1/admin/market/settings/agreement/publish', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10540', 'updateAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10541', 'deleteAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10542', 'exportAgreementAgreedRecords', '/v1/admin/market/settings/agreement/{agreementId}/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10543', 'findFooter_1', '/v1/admin/market/settings/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10544', 'createMarketFooter', '/v1/admin/market/settings/footer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10545', 'updateMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10546', 'deleteMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10547', 'sortMarketFooter', '/v1/admin/market/settings/footer/{footerId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10548', 'getMarketLimitConfig', '/v1/admin/market/settings/limitconfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10549', 'findRkiServerPage', '/v1/admin/market/settings/rki-servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10550', 'getTIDSettings', '/v1/admin/market/settings/tid', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10551', 'updateTIDSetting', '/v1/admin/market/settings/tid', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10552', 'getMarketUiSettings_1', '/v1/admin/market/settings/ui', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10553', 'saveMarketUiSettings', '/v1/admin/market/settings/ui', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10554', 'findOnlineAppPageForFeatured', '/v1/admin/market/settings/ui/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10555', 'findFeaturedApp_1', '/v1/admin/market/settings/ui/apps/featured', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10556', 'updateFeaturedAppSort', '/v1/admin/market/settings/ui/apps/featured/{featuredAppId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10557', 'addFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10558', 'deleteFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10559', 'updateFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured/{featuredAppId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10560', 'getSignatureSetting', '/v1/admin/market/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10561', 'saveSignatureSetting', '/v1/admin/market/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10562', 'clearSignatureData', '/v1/admin/market/signature/data/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10563', 'findSignatureFactoryPage', '/v1/admin/market/signature/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10564', 'testSignatureConfigServer_1', '/v1/admin/market/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10565', 'getSsoSetting', '/v1/admin/market/sso/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10566', 'updateSsoSetting', '/v1/admin/market/sso/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10567', 'searchRoles_1', '/v1/admin/market/sso/settings/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10568', 'findMarketVariablePage', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10569', 'createMarketVariable', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10570', 'batchDeleteTerminalVariables_2', '/v1/admin/market/variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10571', 'importMarketVariable', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10572', 'createMarketVariableImportTemplateDownloadTask', '/v1/admin/market/variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10573', 'findMarketVariableRelatedAppPage', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10574', 'findMarketVariableUsedAppPage', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10575', 'updateMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10576', 'deleteMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10577', 'searchUsers_2', '/v1/admin/merchant/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10578', 'createExportUserDownloadTask_1', '/v1/admin/merchant/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10579', 'activeUser_1', '/v1/admin/merchant/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10580', 'sendActivateUserEmail_1', '/v1/admin/merchant/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10581', 'resetUserPassword_1', '/v1/admin/merchant/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10582', 'findModelPage', '/v1/admin/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10583', 'createModel', '/v1/admin/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10584', 'getModelDetail', '/v1/admin/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10585', 'updateModel', '/v1/admin/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10586', 'deleteModel', '/v1/admin/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10587', 'findProtectedOperations', '/v1/admin/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10588', 'getUser_4', '/v1/admin/operations/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10589', 'getProtectedOperation', '/v1/admin/operations/{key}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10590', 'closeOperation', '/v1/admin/operations/{key}/close', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10591', 'openOperation', '/v1/admin/operations/{key}/open', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10592', 'getOperationUsers', '/v1/admin/operations/{key}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10593', 'addOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10594', 'removeOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10595', 'getCodeLangConfigs', '/v1/admin/platform/configuration/codes/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10596', 'saveCodeLangConfig', '/v1/admin/platform/configuration/codes/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10597', 'getCodeTypes', '/v1/admin/platform/configuration/codes/setting/codeTypes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10598', 'getCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10599', 'deleteCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10600', 'getLicense_1', '/v1/admin/platform/configuration/license', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10601', 'updateLicense', '/v1/admin/platform/configuration/license', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10602', 'loadLoginSettings', '/v1/admin/platform/configuration/login-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10603', 'savePwdPolicy_1', '/v1/admin/platform/configuration/login-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10604', 'loadMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10605', 'saveMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10606', 'testMailServiceConfig', '/v1/admin/platform/configuration/mail-config/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10607', 'listAll', '/v1/admin/platform/configuration/oauth-client', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10608', 'add', '/v1/admin/platform/configuration/oauth-client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10609', 'get', '/v1/admin/platform/configuration/oauth-client/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10610', 'update', '/v1/admin/platform/configuration/oauth-client/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10611', 'delete', '/v1/admin/platform/configuration/oauth-client/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10612', 'loadPwdPolicy', '/v1/admin/platform/configuration/password-policy', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10613', 'savePwdPolicy', '/v1/admin/platform/configuration/password-policy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10614', 'searchReleaseNoteInfos', '/v1/admin/platform/configuration/release-note', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10615', 'createReleaseNoteInfo', '/v1/admin/platform/configuration/release-note', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10616', 'createMailTemplateDownloadTask', '/v1/admin/platform/configuration/release-note/mail/template/{releaseNoteInfoId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10617', 'getReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10618', 'updateReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10619', 'downloadEmails', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/download-emails', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10620', 'sendMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/send-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10621', 'testMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/test-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10622', 'finsRkiServerList', '/v1/admin/platform/configuration/rki-server', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10623', 'createRkiServerSetting', '/v1/admin/platform/configuration/rki-server', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10624', 'testSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10625', 'getRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10626', 'updateRkiServerSetting', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10627', 'deleteRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10628', 'testExistSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/{rkiId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10629', 'searchDiscountTerminals', '/v1/admin/platform/internal/discount/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10630', 'importMerchant_1', '/v1/admin/platform/internal/discount/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10631', 'searchPredefinedRoles', '/v1/admin/platform/internal/predefined-roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10632', 'searchPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10633', 'removePredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10634', 'createPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10635', 'searchPrivileges', '/v1/admin/platform/internal/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10636', 'createPrivilege', '/v1/admin/platform/internal/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10637', 'getPrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10638', 'updatePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10639', 'deletePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10640', 'searchPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10641', 'removePrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10642', 'createPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10643', 'searchSystemProperties', '/v1/admin/platform/internal/properties', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10644', 'createSystemProperty_1', '/v1/admin/platform/internal/properties', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10645', 'getSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10646', 'updateSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10647', 'deleteSystemProperty_1', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10648', 'getPushDiagnosisResult', '/v1/admin/platform/internal/push-diagnosis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10649', 'sendPushDiagnosisTest', '/v1/admin/platform/internal/push-diagnosis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10650', 'searchResources', '/v1/admin/platform/internal/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10651', 'createResource', '/v1/admin/platform/internal/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10652', 'getResource', '/v1/admin/platform/internal/resources/{resourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10653', 'updateResource', '/v1/admin/platform/internal/resources/{resourceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10654', 'deleteResource', '/v1/admin/platform/internal/resources/{resourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', 'searchPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', 'removePrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', 'createPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', 'triggerScheduleJob', '/v1/admin/platform/internal/schedule-job', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', 'searchResellerMigrations', '/v1/admin/platform/migration/reseller-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', 'createResellerMigration', '/v1/admin/platform/migration/reseller-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', 'getResellerMigration', '/v1/admin/platform/migration/reseller-migrations/{resellerMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', 'searchTerminalMigrations', '/v1/admin/platform/migration/terminal-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', 'createTerminalMigration', '/v1/admin/platform/migration/terminal-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10664', 'getTerminalMigration', '/v1/admin/platform/migration/terminal-migrations/{terminalMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', 'regenerateTransferPurchaseClearence', '/v1/admin/purchase/clr', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', 'startTransferPurchaseClearence', '/v1/admin/purchase/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', 'findParamAppPage', '/v1/admin/push/template/param-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', 'findParamSolutionAppPage', '/v1/admin/push/template/param-solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', 'findApkParametersPage', '/v1/admin/push/template/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', 'createApkParameter', '/v1/admin/push/template/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10671', 'findApkParameterAppPage', '/v1/admin/push/template/parameters/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10672', 'batchDeleteApkParameter', '/v1/admin/push/template/parameters/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', 'findApkParameterComparePage', '/v1/admin/push/template/parameters/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', 'getApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', 'updateApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', 'deleteApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', 'updateApkParameterFormData', '/v1/admin/push/template/parameters/{apkParameterId}/data', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', 'createApkParameterDataFileDownloadTask', '/v1/admin/push/template/parameters/{apkParameterId}/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', 'getApkParameterSchema', '/v1/admin/push/template/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', 'searchReport', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10681', 'getMerchantByResellerIds', '/v1/admin/report/data-source/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', 'searchInstalledPUKList', '/v1/admin/report/data-source/puk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', 'getResellersByMarketId', '/v1/admin/report/data-source/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', 'searchReportApkList', '/v1/admin/report/data-source/{reportId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', 'refreshParameterSourceItems', '/v1/admin/report/parameter/{parameterId}/source/refresh', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', 'deleteReportExecution_1', '/v1/admin/report/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', 'getReportJobHistoryPage', '/v1/admin/report/reportJobHistory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10688', 'createDownloadTaskForReport', '/v1/admin/report/reportTask/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10689', 'updateReportTasksStatus', '/v1/admin/report/reportTask/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10690', 'updateReportTaskStatus', '/v1/admin/report/reportTask/{reportExecutionContextId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10691', 'getReportTaskByPage', '/v1/admin/report/reportTask/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', 'deleteReportExecution', '/v1/admin/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10693', 'getReport', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', 'findReportDynamicFields', '/v1/admin/report/{reportId}/dynamic-fields', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10695', 'createImmediateReportExecution', '/v1/admin/report/{reportId}/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10696', 'createScheduledReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', 'getReportExecutionContext', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10698', 'updateReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10699', 'activeReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10700', 'getReseller3rdSysConfig', '/v1/admin/reseller/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', 'deActiveReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', 'addReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', 'updateReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', 'deleteReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10705', 'getReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10706', 'resetReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10707', 'find3rdSysWebHookPage', '/v1/admin/reseller/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10708', 'create3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', 'getWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10710', 'get3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10711', 'update3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10712', 'delete3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10713', 'findWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', 'test3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', 'findResellerOnlineApps', '/v1/admin/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', 'getApkInfo', '/v1/admin/reseller/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', 'reSignApk_2', '/v1/admin/reseller/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', 'findApkSignatureList_2', '/v1/admin/reseller/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', 'findSpecificApkResellerPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', 'specificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', 'deleteSpecificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', 'findSpecificApkResellerAllListPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', 'getTopicSubscription_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', 'subscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', 'unsubscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10726', 'getAppInfo', '/v1/admin/reseller/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10727', 'searchApk', '/v1/admin/reseller/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10728', 'getBizDataFromGoInsight_2', '/v1/admin/reseller/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10729', 'getAppMerchantCategory', '/v1/admin/reseller/apps/{appId}/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10730', 'getAppSettingVo_1', '/v1/admin/reseller/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10731', 'findSpecificAppResellerPage_1', '/v1/admin/reseller/apps/{appId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10732', 'specificAppReseller_1', '/v1/admin/reseller/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10733', 'deleteSpecificResellerApp', '/v1/admin/reseller/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10734', 'findSpecificAppResellerAllListPage_1', '/v1/admin/reseller/apps/{appId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10735', 'getAppVasSettingVo_2', '/v1/admin/reseller/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10736', 'searchFirmware_1', '/v1/admin/reseller/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10737', 'getTopicSubscription_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10738', 'subscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10739', 'unsubscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10740', 'getFirmwareDetailVo_1', '/v1/admin/reseller/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10741', 'specificResellerFirmware_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10742', 'findSpecificFirmwareResellerAllListPage_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10743', 'getResellerRki_1', '/v1/admin/reseller/rki/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10744', 'refreshResellerRkiKeys_1', '/v1/admin/reseller/rki/settings/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10745', 'saveResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10746', 'deleteResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10747', 'getResellerTIDSettings', '/v1/admin/reseller/settings/tid', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10748', 'updateResellerTIDSetting', '/v1/admin/reseller/settings/tid', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10749', 'getMarketUiSettings', '/v1/admin/reseller/settings/ui', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10750', 'saveResellerUISettings', '/v1/admin/reseller/settings/ui', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10751', 'saveResellerSignatureSetting', '/v1/admin/reseller/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10752', 'searchRoles', '/v1/admin/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10753', 'createRole', '/v1/admin/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10754', 'searchUsers_1', '/v1/admin/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10755', 'getRole', '/v1/admin/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10756', 'updateRole', '/v1/admin/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10757', 'deleteRole', '/v1/admin/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10758', 'searchRoleUsers', '/v1/admin/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10759', 'addRoleUsers', '/v1/admin/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10760', 'removeRoleUsers', '/v1/admin/roles/{roleId}/users', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10761', 'findSolutionAppPage', '/v1/admin/service/industry-solution/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10762', 'reSignApk_1', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10763', 'findApkSignatureList_1', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10764', 'findSolutionAppIntroductionPage', '/v1/admin/service/industry-solution/apps/introduction', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10765', 'getSolutionAppDetail', '/v1/admin/service/industry-solution/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10766', 'applyIndustrySolutionAppForSpecific', '/v1/admin/service/industry-solution/apps/{appId}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10767', 'findVasAppCurrentUsage', '/v1/admin/service/industry-solution/apps/{appId}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10768', 'exportVasAppCurrentUsage', '/v1/admin/service/industry-solution/apps/{appId}/export/current/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10769', 'exportVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10770', 'findVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10771', 'findVasAppMarkets', '/v1/admin/service/industry-solution/apps/{appId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10772', 'findSpecificSolutionResellerPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10773', 'specificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10774', 'deleteSpecificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10775', 'findSpecificSolutionResellerAllListPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10776', 'findVasAppServiceHistory', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10777', 'updateAppServicePrice', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/price', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10778', 'updateAppServiceStatus', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10779', 'getVasAppUsageDashBoard', '/v1/admin/service/industry-solution/apps/{appId}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10780', 'getAppVasSettingVo_1', '/v1/admin/service/industry-solution/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10781', 'findGlobalPublishAppPage', '/v1/admin/subscription/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10782', 'getApkDetailVo_3', '/v1/admin/subscription/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10783', 'findSpecificApkResellerPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10784', 'specificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10785', 'deleteSpecificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10786', 'findSpecificApkResellerAllListPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10787', 'reSignApk', '/v1/admin/subscription/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10788', 'findApkSignatureList', '/v1/admin/subscription/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10789', 'exportGlobalPublishApp', '/v1/admin/subscription/apps/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10790', 'getAppDetailVo', '/v1/admin/subscription/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10791', 'subscriptionApp', '/v1/admin/subscription/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10792', 'findApkPage', '/v1/admin/subscription/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10793', 'unSubscriptionApp', '/v1/admin/subscription/apps/{appId}/cancel', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10794', 'getBizDataFromGoInsight_1', '/v1/admin/subscription/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10795', 'findAppMerchantCategoryPage', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10796', 'specificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10797', 'deleteSpecificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10798', 'findSpecificAppResellerPage', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10799', 'specificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10800', 'deleteSpecificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10801', 'findSpecificAppResellerAllListPage', '/v1/admin/subscription/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10802', 'getAppSettingVo', '/v1/admin/subscription/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10803', 'getAppVasSettingVo', '/v1/admin/subscription/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10804', 'findGlobalPublishFirmware', '/v1/admin/subscription/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10805', 'exportGlobalPublishFirmware', '/v1/admin/subscription/firmwares/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10806', 'getFirmwareDetailVo', '/v1/admin/subscription/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10807', 'subscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10808', 'unsubscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/cancel', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10809', 'specificResellerFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10810', 'findSpecificFirmwareResellerAllListPage', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10811', 'getTopicSubscription', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10812', 'subscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10813', 'unsubscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10814', 'searchGroups_1', '/v1/admin/terminal-groups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10815', 'createGroup', '/v1/admin/terminal-groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10816', 'searchTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10817', 'createTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10818', 'updateGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10819', 'deleteGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10820', 'getTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10821', 'deleteTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10822', 'resumeGroupTerminalApk', '/v1/admin/terminal-groups/apks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10823', 'activateTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10824', 'getApkDetailVo_2', '/v1/admin/terminal-groups/apks/{groupApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10825', 'createGroupApkDataFileDownloadTask', '/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10826', 'createGroupApkFilter', '/v1/admin/terminal-groups/apks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10827', 'updateGroupApkPushLimit', '/v1/admin/terminal-groups/apks/{groupApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10828', 'getTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10829', 'updateTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10830', 'updateTerminalGroupApkParam_1', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10831', 'resumeGroupTerminalApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10832', 'getGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10833', 'saveGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10834', 'searchGroupApkParamTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10835', 'getGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10836', 'saveGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10837', 'resetTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10838', 'suspendTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10839', 'searchGroupApkTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10840', 'createGroupApkTerminalsExportTasks', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10841', 'searchGroupFirmwares', '/v1/admin/terminal-groups/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10842', 'createGroupFirmware', '/v1/admin/terminal-groups/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10843', 'searchFirmware', '/v1/admin/terminal-groups/firmwares/filter', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10844', 'updateGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10845', 'createGroupFirmwareFilter_1', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10846', 'getGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10847', 'deleteGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10848', 'resumeGroupTerminalFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10849', 'activateGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10850', 'createGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10851', 'updateGroupFirmwarePushLimit', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10852', 'resetGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10853', 'suspendGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10854', 'searchGroupFirmwareTerminals', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10855', 'createGroupFirmwareTerminalsExportTasks', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10856', 'importGroupTerminal', '/v1/admin/terminal-groups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10857', 'createGroupTerminalImportTemplateDownloadTask', '/v1/admin/terminal-groups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10858', 'searchTerminalGroupLaunchers', '/v1/admin/terminal-groups/launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10859', 'createTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10860', 'getResellerOnlineApkNameAndIcon', '/v1/admin/terminal-groups/launchers/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10861', 'searchLauncherTemplates', '/v1/admin/terminal-groups/launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10862', 'getLauncherTemplate', '/v1/admin/terminal-groups/launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10863', 'updateGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10864', 'deleteGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10865', 'getTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10866', 'deleteTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10867', 'resumeGroupTerminalLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10868', 'activateTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10869', 'getApkDetailVo_1', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10870', 'createGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', 'updateGroupLauncherPushLimit', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10872', 'getTerminalGroupLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', 'resumeGroupTerminalLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', 'searchGroupLauncherParamTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10875', 'resetTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10876', 'suspendTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10877', 'searchGroupLauncherTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10878', 'createGroupLauncherTerminalsExportTasks', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10879', 'searchGroupOperation', '/v1/admin/terminal-groups/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10880', 'createGroupOperation', '/v1/admin/terminal-groups/operations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10881', 'getGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10882', 'deleteGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10883', 'resumeGroupTerminalOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', 'activateGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10885', 'updateGroupOperationPushLimit', '/v1/admin/terminal-groups/operations/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10886', 'resetGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10887', 'suspendGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10888', 'searchGroupOperationTerminals', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10889', 'createGroupOperationTerminalsExportTasks', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10890', 'searchGroupPuks', '/v1/admin/terminal-groups/puks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10891', 'createGroupPuk', '/v1/admin/terminal-groups/puks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10892', 'getSignaturePuk_1', '/v1/admin/terminal-groups/puks/{groupId}/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10893', 'getGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10894', 'deleteGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10895', 'resumeGroupTerminalPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10896', 'activateGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10897', 'updateGroupPukPushLimit', '/v1/admin/terminal-groups/puks/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10898', 'resetGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10899', 'suspendGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10900', 'searchGroupPukTerminals', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10901', 'createGroupPukTerminalsExportTasks', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10902', 'searchGroupRkis', '/v1/admin/terminal-groups/rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10903', 'createGroupRki', '/v1/admin/terminal-groups/rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10904', 'getGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10905', 'deleteGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10906', 'resumeGroupTerminalRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10907', 'activateGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10908', 'updateGroupRkiPushLimit', '/v1/admin/terminal-groups/rkis/{groupRkiId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10909', 'resetGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10910', 'suspendGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10911', 'searchGroupRkiTerminals', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10912', 'createGroupRkiTerminalsExportTasks', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10913', 'searchTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10914', 'createTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10915', 'findApkParameterPage', '/v1/admin/terminal-groups/solutions/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10916', 'findOnlineAppPage', '/v1/admin/terminal-groups/solutions/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10917', 'updateGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10918', 'deleteGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10919', 'getTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10920', 'deleteTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10921', 'resumeGroupTerminalSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10922', 'activateTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10923', 'getSolutionApkDetailVo', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10924', 'createGroupSolutionDataFileDownloadTask', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10925', 'createGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10926', 'updateGroupSolutionPushLimit', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10927', 'getTerminalGroupSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10928', 'updateTerminalGroupSolutionApkParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10929', 'updateTerminalGroupSolutionApkParamFormData', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10930', 'resumeGroupTerminalSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10931', 'getGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10932', 'saveGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10933', 'searchGroupSolutionParamTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10934', 'getGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10935', 'saveGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10936', 'resetTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10937', 'suspendTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10938', 'searchGroupSolutionTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10939', 'createGroupSolutionTerminalsExportTasks', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10940', 'findSimOperator', '/v1/admin/terminal-groups/terminal/sim/operator', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10941', 'searchTerminal_1', '/v1/admin/terminal-groups/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10942', 'searchGroupUninstallApks', '/v1/admin/terminal-groups/uninstall-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10943', 'createGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10944', 'getGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10945', 'deleteGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10946', 'resumeGroupTerminalUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10947', 'activateGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10948', 'updateGroupUninstallApkPushLimit', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10949', 'resetGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10950', 'suspendGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10951', 'searchGroupUninstallApkTerminals', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10952', 'createGroupUninstallApkTerminalsExportTasks', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10953', 'findTerminalGroupVariableList', '/v1/admin/terminal-groups/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10954', 'createTerminalGroupVariable', '/v1/admin/terminal-groups/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10955', 'batchDeleteTerminalGroupVariables', '/v1/admin/terminal-groups/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10956', 'importTerminalGroupVariable', '/v1/admin/terminal-groups/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10957', 'createTerminalGroupVariableImportTemplateDownloadTask', '/v1/admin/terminal-groups/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10958', 'findTerminalGroupVariableSupportedAppList', '/v1/admin/terminal-groups/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10959', 'findTerminalGroupVariableUsedAppList', '/v1/admin/terminal-groups/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10960', 'updateTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10961', 'deleteTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10962', 'getGroup', '/v1/admin/terminal-groups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10963', 'updateGroup', '/v1/admin/terminal-groups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10964', 'deleteGroup', '/v1/admin/terminal-groups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10965', 'activeGroup_1', '/v1/admin/terminal-groups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10966', 'disableGroup_1', '/v1/admin/terminal-groups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10967', 'removeGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10968', 'createGroupTerminals_1', '/v1/admin/terminal-groups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10969', 'searchGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10970', 'getTerminalNumberStatisticData', '/v1/admin/terminal-management/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10971', 'getTerminalNumberOfMerchantData', '/v1/admin/terminal-management/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10972', 'getTerminalNumberOfResellerData', '/v1/admin/terminal-management/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10973', 'findMerchantVariablePage', '/v1/admin/terminal-management/merchant-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10974', 'createMerchantVariable', '/v1/admin/terminal-management/merchant-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10975', 'batchDeleteMerchantVariables', '/v1/admin/terminal-management/merchant-variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10976', 'importMerchantVariable', '/v1/admin/terminal-management/merchant-variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10977', 'createMerchantVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/merchant-variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10978', 'findMerchantVariableSupportedAppPage', '/v1/admin/terminal-management/merchant-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10979', 'findMerchantVariableUsedAppPage', '/v1/admin/terminal-management/merchant-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10980', 'updateMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10981', 'deleteMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10982', 'createMerchant', '/v1/admin/terminal-management/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10983', 'createExportMerchantsDownloadTask', '/v1/admin/terminal-management/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10984', 'importMerchant', '/v1/admin/terminal-management/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10985', 'createMerchantImportTemplateDownloadTask', '/v1/admin/terminal-management/merchants/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10986', 'findSubMerchantPageForOrganization', '/v1/admin/terminal-management/merchants/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10987', 'findSubMerchantPage', '/v1/admin/terminal-management/merchants/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10988', 'getMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10989', 'updateMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10990', 'deleteMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10991', 'getMerchantAccessoryProfile', '/v1/admin/terminal-management/merchants/{merchantId}/accessory/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10992', 'createMerchantAccessoryProfile', '/v1/admin/terminal-management/merchants/{merchantId}/accessory/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10993', 'activeMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10994', 'disableMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10995', 'moveMerchant', '/v1/admin/terminal-management/merchants/{merchantId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10996', 'getMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10997', 'createMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10998', 'replaceMerchantEmail', '/v1/admin/terminal-management/merchants/{merchantId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10999', 'activeMerchantResendEmail', '/v1/admin/terminal-management/merchants/{merchantId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11000', 'createReseller', '/v1/admin/terminal-management/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11001', 'createExportResellersDownloadTask', '/v1/admin/terminal-management/resellers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11002', 'findResellerPageForOrganization', '/v1/admin/terminal-management/resellers/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11003', 'createTmkImportTemplateDownloadTask', '/v1/admin/terminal-management/resellers/rki/tmk/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11004', 'findSubResellerPage', '/v1/admin/terminal-management/resellers/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11005', 'getResellerDetailVo', '/v1/admin/terminal-management/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11006', 'updateReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11007', 'deleteReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11008', 'getResellerAccessoryProfile', '/v1/admin/terminal-management/resellers/{resellerId}/accessory/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11009', 'createResellerAccessoryProfile', '/v1/admin/terminal-management/resellers/{resellerId}/accessory/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11010', 'activeReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11011', 'disableReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11012', 'moveReseller', '/v1/admin/terminal-management/resellers/{resellerId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11013', 'getResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11014', 'createResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11015', 'replaceResellerEmail', '/v1/admin/terminal-management/resellers/{resellerId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11016', 'activeResellerResendEmail', '/v1/admin/terminal-management/resellers/{resellerId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11017', 'getResellerRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11018', 'refreshResellerRkiKeys', '/v1/admin/terminal-management/resellers/{resellerId}/rki/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11019', 'verifyPushRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki/pre-deduction', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11020', 'findTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11021', 'importTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11022', 'saveResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11023', 'deleteResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11024', 'findTerminalApkPage', '/v1/admin/terminal-management/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11025', 'createTerminalApks', '/v1/admin/terminal-management/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11026', 'findTerminalApkParamComparePage', '/v1/admin/terminal-management/terminal-apks/history/param/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11027', 'getTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11028', 'deleteTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11029', 'activateTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11030', 'getApkDetailVo', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11031', 'createTerminalApkDataFileDownloadTask', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11032', 'getTerminalApkParam', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11033', 'updateTerminalApkParam_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11034', 'updateTerminalApkParamFormData', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11035', 'findTerminalApkParamVariablePage', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11036', 'saveTerminalApkParamVariables', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11037', 'resetTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11038', 'suspendTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11039', 'findTerminalDetailPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11040', 'findTerminalAuditLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11041', 'getTerminalAuditLogDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log/{auditId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11042', 'findTerminalDownloadApkLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/download-apk-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11043', 'getTerminalInstalledApkStatistics', '/v1/admin/terminal-management/terminal-detail/{terminalId}/installed-apks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11044', 'getTerminalDashboardLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11045', 'refreshTerminalLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11046', 'getTerminalDashboardMonitor', '/v1/admin/terminal-management/terminal-detail/{terminalId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11047', 'getTerminalPedStatus', '/v1/admin/terminal-management/terminal-detail/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11048', 'findTerminalPushHistoryPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/push-history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11049', 'refreshTerminalDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11050', 'findTerminalReplacementLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/replace-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11051', 'getTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11052', 'createTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11053', 'clearTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11054', 'updateTerminalSafeRadius', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/radius', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11055', 'getTerminalTraffic', '/v1/admin/terminal-management/terminal-detail/{terminalId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11056', 'findTerminalFirmwarePage', '/v1/admin/terminal-management/terminal-firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11057', 'createTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11058', 'getTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11059', 'deleteTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11060', 'activateTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11061', 'resetTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11062', 'suspendTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11063', 'findTerminalRkiPage', '/v1/admin/terminal-management/terminal-rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11064', 'createTerminalRki', '/v1/admin/terminal-management/terminal-rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11065', 'getTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11066', 'deleteTerminalRKI', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11067', 'activateTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11068', 'resetTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11069', 'suspendTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11070', 'findTerminalVariablePage', '/v1/admin/terminal-management/terminal-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11071', 'createTerminalVariable', '/v1/admin/terminal-management/terminal-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11072', 'batchDeleteTerminalVariables', '/v1/admin/terminal-management/terminal-variables/batch/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11073', 'findTerminalVariableSupportedAppPage', '/v1/admin/terminal-management/terminal-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11074', 'findTerminalVariableUsedAppPage', '/v1/admin/terminal-management/terminal-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11075', 'updateTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11076', 'deleteTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11077', 'createTerminal', '/v1/admin/terminal-management/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11078', 'searchAccessory', '/v1/admin/terminal-management/terminals/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11079', 'createExportTerminalAccessoryDownloadTask', '/v1/admin/terminal-management/terminals/accessories/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11080', 'cancelTerminalAccessoryOperation', '/v1/admin/terminal-management/terminals/accessories/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11081', 'findTerminalAccessTypeList', '/v1/admin/terminal-management/terminals/accessories/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11082', 'findAccessoryQtyByType', '/v1/admin/terminal-management/terminals/accessories/widgets/W23', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11083', 'findAccessoryQtyByModel', '/v1/admin/terminal-management/terminals/accessories/widgets/W24', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11084', 'findTerminalAccessoryDetail', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11085', 'findTerminalAccessoryDetailPage', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/details', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11086', 'findTerminalAccessoryEvents', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/events', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11087', 'findTerminalAccessoryOperations', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11088', 'pushTerminalActions_1', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11089', 'refreshTerminalAccessory', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11090', 'createTerminals_1', '/v1/admin/terminal-management/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11091', 'batchActiveTerminals', '/v1/admin/terminal-management/terminals/batch/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11092', 'createGroupTerminals', '/v1/admin/terminal-management/terminals/batch/add-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11093', 'batchDeleteTerminals_1', '/v1/admin/terminal-management/terminals/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11094', 'batchSuspendTerminals', '/v1/admin/terminal-management/terminals/batch/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11095', 'batchMoveTerminals', '/v1/admin/terminal-management/terminals/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11096', 'copyTerminal', '/v1/admin/terminal-management/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11097', 'createExportTerminalsDownloadTask', '/v1/admin/terminal-management/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11098', 'createExportTerminalStaticIpConfigDownloadTask', '/v1/admin/terminal-management/terminals/export/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11099', 'createExportTerminalVariableDownloadTask', '/v1/admin/terminal-management/terminals/export/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11100', 'searchGroups', '/v1/admin/terminal-management/terminals/group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11101', 'importTerminal', '/v1/admin/terminal-management/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11102', 'importTerminalBatchDelete', '/v1/admin/terminal-management/terminals/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11103', 'importTerminalBatchSuspend', '/v1/admin/terminal-management/terminals/import/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11104', 'importTerminalBatchMove', '/v1/admin/terminal-management/terminals/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11105', 'createTerminalImportBatchOperationTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11106', 'importTerminalStaticIpConfig', '/v1/admin/terminal-management/terminals/import/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11107', 'createTerminalStaticIpConfigTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/static-ip/config/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11108', 'createTerminalImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11109', 'importTerminalVariable', '/v1/admin/terminal-management/terminals/import/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11110', 'createTerminalVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/variable/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11111', 'searchTerminal', '/v1/admin/terminal-management/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11112', 'getTerminalQuickBySn', '/v1/admin/terminal-management/terminals/quick/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11113', 'getTerminalStockBySerialNo', '/v1/admin/terminal-management/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11114', 'getTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11115', 'updateTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11116', 'deleteTerminal_2', '/v1/admin/terminal-management/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11117', 'findTerminalAccessoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11118', 'activeTerminal', '/v1/admin/terminal-management/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11119', 'getTerminalAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11120', 'checkCheckUpVersion', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/check/checkup/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11121', 'pushInstallCheckup', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/checkup/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11122', 'pushInstallAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11123', 'startAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11124', 'disableTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11125', 'findTerminalInstalledApkPage', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11126', 'getTerminalInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11127', 'createParameterDataFileDownloadTask_1', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11128', 'getInstalledApkParam', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11129', 'uninstallInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11130', 'getTerminalInstalledFirmware', '/v1/admin/terminal-management/terminals/{terminalId}/installed-firmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11131', 'moveTerminal', '/v1/admin/terminal-management/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11132', 'getPukPushStatus', '/v1/admin/terminal-management/terminals/{terminalId}/puk/push/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11133', 'collectTerminalLogcat', '/v1/admin/terminal-management/terminals/{terminalId}/setting/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11134', 'findTerminalSystemConfigPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/configs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11135', 'disablePushPukTerminalAction', '/v1/admin/terminal-management/terminals/{terminalId}/setting/disable/puk/push', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11136', 'getTerminalLocationEnable', '/v1/admin/terminal-management/terminals/{terminalId}/setting/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11137', 'findTerminalLogPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11138', 'createTerminalLogDownloadTask', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs/{terminalLogId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11139', 'findTerminalNetworkConfigurationList', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11140', 'findPukPushHistoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11141', 'getSignaturePuk', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11142', 'pushTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/push/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11143', 'updateTerminalRemoteConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11144', 'getWifLanStaticIpProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/static-ip/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11145', 'saveTerminalStaticIpConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11146', 'disableTerminalStaticIpConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/static-ip/config/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11147', 'findTerminalStockPage', '/v1/admin/terminal-stocks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11148', 'createTerminals', '/v1/admin/terminal-stocks/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11149', 'assignTerminals', '/v1/admin/terminal-stocks/batch/assign', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11150', 'batchDeleteTerminals', '/v1/admin/terminal-stocks/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11151', 'createExportStockTerminalsDownloadTask', '/v1/admin/terminal-stocks/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11152', 'importStockTerminal', '/v1/admin/terminal-stocks/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11153', 'createStockTerminalImportTemplateDownloadTask', '/v1/admin/terminal-stocks/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11154', 'getTerminalStock', '/v1/admin/terminal-stocks/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11155', 'updateTerminal', '/v1/admin/terminal-stocks/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11156', 'deleteTerminal_1', '/v1/admin/terminal-stocks/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11157', 'searchUsers', '/v1/admin/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11158', 'createExportUserDownloadTask', '/v1/admin/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11159', 'searchRoleList', '/v1/admin/users/role-all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11160', 'getUser_3', '/v1/admin/users/{userId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11161', 'deleteUser', '/v1/admin/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11162', 'activeUser', '/v1/admin/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11163', 'sendActivateUserEmail', '/v1/admin/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11164', 'changeEmail', '/v1/admin/users/{userId}/change-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11165', 'disableUser', '/v1/admin/users/{userId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11166', 'findUserRoles', '/v1/admin/users/{userId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11167', 'resetUserPassword', '/v1/admin/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11168', 'deleteUserRoles', '/v1/admin/users/{userId}/roles', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11169', 'deleteUserRole', '/v1/admin/users/{userId}/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11170', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11171', 'getThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11172', 'updateThirdpartyAppSys', '/v1/admin/vas/3rdsys/app', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11173', 'createThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11174', 'listPosviewerFileTransferInfo', '/v1/admin/vas/air-viewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11175', 'getModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11176', 'updateModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11177', 'listPosviewerOperationInfo', '/v1/admin/vas/air-viewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11178', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11179', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11180', 'getDashBoard', '/v1/app_scan/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11181', 'getEngineBlacklist', '/v1/app_scan/engine/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11182', 'updateEngineBlacklist', '/v1/app_scan/engine/blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11183', 'getHistoricalUsage', '/v1/app_scan/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11184', 'getAvailableScanEngineList', '/v1/app_scan/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11185', 'rescan', '/v1/app_scan/rescan', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11186', 'getResultFile', '/v1/app_scan/resultZip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11187', 'getScanResult', '/v1/app_scan/results', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11188', 'isCreateTaskPermitted', '/v1/app_scan/scanned', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11189', 'getSetting', '/v1/app_scan/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11190', 'updateSetting', '/v1/app_scan/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11191', 'createScanTask', '/v1/app_scan/task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11192', 'deleteScanTask', '/v1/app_scan/task/{scanTaskId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11193', 'getUsage', '/v1/app_scan/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11194', 'createBuriedPoints', '/v1/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11195', 'activateUser', '/v1/common/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11196', 'validateActivate1', '/v1/common/auth/activation/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11197', 'destroySsoToken', '/v1/common/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11198', 'resetEmail', '/v1/common/auth/email-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11199', 'validateResetEmail1', '/v1/common/auth/email-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11200', 'validateExtraction', '/v1/common/auth/extraction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11201', 'validateDownloadLink', '/v1/common/auth/extraction/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11202', 'getMarketDc', '/v1/common/auth/market/dc', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11203', 'disableOtpByBackupCode', '/v1/common/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11204', 'validateDisableCode', '/v1/common/auth/otp/disable-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11205', 'sendDisableOtpMail', '/v1/common/auth/otp/reset-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11206', 'forgetPwd', '/v1/common/auth/password-forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11207', 'resetPwd', '/v1/common/auth/password-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11208', 'validateResetPwd1', '/v1/common/auth/password-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11209', 'checkTokenExpire', '/v1/common/auth/ping', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11210', 'registerUser', '/v1/common/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11211', 'findCodes', '/v1/common/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11212', 'getCodes', '/v1/common/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11213', 'getDocSetting', '/v1/common/doc-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11214', 'download2', '/v1/common/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11215', 'createLatestOnlineClientApkDownloadTask', '/v1/common/download/client-app/latest/client', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11216', 'download1', '/v1/common/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11217', 'getDownloadUrl', '/v1/common/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11218', 'getCurrentEnv', '/v1/common/env', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11219', 'getFile', '/v1/common/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11220', 'findFooter', '/v1/common/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11221', 'getFooter', '/v1/common/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11222', 'getLangList', '/v1/common/languages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11223', 'getLicense', '/v1/common/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11224', 'initMessageStats', '/v1/common/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11225', 'readTopXMessages', '/v1/common/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11226', 'readMessage', '/v1/common/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11227', 'viewMessageDetails', '/v1/common/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11228', 'getAllPasswordValidatorPolicyFailureDetail', '/v1/common/password-rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11229', 'getSystemConfig', '/v1/common/system-config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11230', 'getUserAgreement_1', '/v1/common/user-agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11231', 'agreeUserAgreement_1', '/v1/common/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11232', 'generateCaptcha', '/v1/common/users/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11233', 'verifyCaptcha', '/v1/common/users/captcha/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11234', 'getCurrentUserRouterSwitchList', '/v1/common/users/routers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11235', 'createDeveloper', '/v1/developer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11236', 'activeDeveloper3rdSysAccess', '/v1/developer/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11237', 'getDeveloper3rdSysConfig', '/v1/developer/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11238', 'deActiveDeveloper3rdSysAccess', '/v1/developer/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11239', 'getDeveloper3rdSysAccessSecret', '/v1/developer/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11240', 'resetDeveloper3rdSysAccess', '/v1/developer/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11241', 'getDeveloperAccountVo', '/v1/developer/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11242', 'agreeUserAgreement', '/v1/developer/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11243', 'updateApk', '/v1/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11244', 'deleteApk', '/v1/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11245', 'getApkEditDetail', '/v1/developer/apks/{apkId}/apk-edit', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11246', 'updateApkFile', '/v1/developer/apks/{apkId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11247', 'createOriginalApkDownloadTask', '/v1/developer/apks/{apkId}/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11248', 'offlineApk', '/v1/developer/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11249', 'findCustomParamTemplate', '/v1/developer/apks/{apkId}/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11250', 'analysisDevParamTemplate', '/v1/developer/apks/{apkId}/param-templates/analysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11251', 'createParameterDataFileDownloadTask', '/v1/developer/apks/{apkId}/param-templates/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11252', 'createApkParamTemplateDownloadTask', '/v1/developer/apks/{apkId}/param-templates/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11253', 'getApkParamTemplateSchema', '/v1/developer/apks/{apkId}/param-templates/schema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11254', 'submitApk', '/v1/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11255', 'searchApps_1', '/v1/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11256', 'createApp', '/v1/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11257', 'getDeveloperAppSummary', '/v1/developer/apps/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11258', 'getAppDetail_1', '/v1/developer/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11259', 'deleteApp', '/v1/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11260', 'addApkFile', '/v1/developer/apps/{appId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11261', 'searchApks', '/v1/developer/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11262', 'getApkDetail_1', '/v1/developer/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11263', 'updateAppKey', '/v1/developer/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11264', 'getBizDataFromGoInsight', '/v1/developer/apps/{appId}/sandbox/insight-data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11265', 'getDeveloperBalance', '/v1/developer/balance', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11266', 'findDeveloperTransactionList', '/v1/developer/balance/transactions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11267', 'getDeveloper', '/v1/developer/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11268', 'getUser_2', '/v1/developer/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11269', 'validateUserEmail', '/v1/developer/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11270', 'findFactoryNameList', '/v1/developer/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11271', 'findFactoryModelList', '/v1/developer/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11272', 'applyIndustrySolution', '/v1/developer/industry-solution/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11273', 'getMarket_2', '/v1/developer/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11274', 'findEnterpriseDevelopers', '/v1/developer/members', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11275', 'addEnterpriseDeveloper', '/v1/developer/members', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11276', 'deleteEnterpriseDeveloper', '/v1/developer/members/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11277', 'updateAdminDeveloper', '/v1/developer/members/{userId}/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11278', 'updateDeveloperSuperAdmin', '/v1/developer/members/{userId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11279', 'updateUserDeveloper', '/v1/developer/members/{userId}/user', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11280', 'searchCustomParamTemplate', '/v1/developer/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11281', 'createParamTemplate', '/v1/developer/param-templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11282', 'searchAppName', '/v1/developer/param-templates/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11283', 'searchCustomParamTemplate_1', '/v1/developer/param-templates/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11284', 'getDevParamTemplate', '/v1/developer/param-templates/{templateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11285', 'updateParameterSchema', '/v1/developer/param-templates/{templateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11286', 'deleteCustomParamTemplate', '/v1/developer/param-templates/{templateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11287', 'cloneParamTemplate', '/v1/developer/param-templates/{templateId}/clone', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11288', 'createApkParamTemplateDownloadPoFilesTask', '/v1/developer/param-templates/{templateId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11289', 'updateParamTemplateName', '/v1/developer/param-templates/{templateId}/name', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11290', 'getParamTemplateSchema', '/v1/developer/param-templates/{templateId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11291', 'uploadDevParamTemplate', '/v1/developer/param-templates/{templateId}/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11292', 'initDeveloperPayment', '/v1/developer/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11293', 'checkout_1', '/v1/developer/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11294', 'payDeveloperOffline', '/v1/developer/payment/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11295', 'getPurchaseClearenceReportData', '/v1/developer/revenue/clearences', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11296', 'searchPurchasedApp', '/v1/developer/revenue/purchased-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11297', 'searchPurchasedMarket', '/v1/developer/revenue/purchased-markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11298', 'getReceivableAccount', '/v1/developer/revenue/receivable-account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11299', 'removeDeveloperReceivableAccount', '/v1/developer/revenue/receivable-account', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11300', 'findAppPageForSandBox', '/v1/developer/sandbox-data/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11301', 'searchSandboxTerminal', '/v1/developer/sandbox-terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11302', 'createSandboxTerminal', '/v1/developer/sandbox-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11303', 'searchFactory', '/v1/developer/sandbox-terminals/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11304', 'getSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11305', 'updateSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11306', 'deleteTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11307', 'isSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11308', 'updateSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11309', 'searchSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11310', 'createSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11311', 'getApkDetail', '/v1/developer/sandbox/terminal-apks/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11312', 'getSandboxTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11313', 'deleteTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11314', 'activateTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11315', 'createTerminalApkDataDownloadTask', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11316', 'updateTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/param-template-name', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11317', 'getSandboxTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11318', 'updateTerminalApkParam_2', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11319', 'resetTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11320', 'getUserAgreement', '/v1/developer/user/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11321', 'getValueAddServiceSummaryVo', '/v1/developer/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11322', 'findVasAgreedAgreements', '/v1/developer/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11323', 'findDeveloperAppForVas', '/v1/developer/vas/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11324', 'clearAppCloudMessagesData', '/v1/developer/vas/clear-data', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11325', 'confirmConnectDialog', '/v1/developer/vas/confirm/connect/dialog', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11326', 'listAppMsg', '/v1/developer/vas/msg', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11327', 'addAppMsg', '/v1/developer/vas/msg', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11328', 'getTerminalInstalledAppCount', '/v1/developer/vas/msg/app/{appId}/installed-terminal-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11329', 'findAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11330', 'createAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11331', 'deleteAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}/tags/{tagId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11332', 'uploadMsgTemplate', '/v1/developer/vas/msg/template/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11333', 'validateUrl', '/v1/developer/vas/msg/template/validation-img-url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11334', 'getMsgById', '/v1/developer/vas/msg/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11335', 'getMsgStatusById', '/v1/developer/vas/msg/{id}/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11336', 'logicDeleteMessage', '/v1/developer/vas/msg/{msgId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11337', 'disableMessage', '/v1/developer/vas/msg/{msgId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11338', 'getMsgReport', '/v1/developer/vas/msg/{msgId}/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11339', 'showConnectDialog', '/v1/developer/vas/show/connect/dialog', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11340', 'findStatisticsTypes', '/v1/developer/vas/statistics/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11341', 'getVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11342', 'agreeVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11343', 'serviceApply', '/v1/developer/vas/{serviceType}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11344', 'findHistoryUsageByServiceType', '/v1/developer/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11345', 'getUsageDashboardByServiceType', '/v1/developer/vas/{serviceType}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11346', 'createTables', '/v1/internal/3rdsys/audit-log/create-tables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11347', 'migrationAuditLog', '/v1/internal/3rdsys/audit-log/migration/audit-log', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11348', 'migrationAuditTrail', '/v1/internal/3rdsys/audit-log/migration/audit-trail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11349', 'refreshNodes', '/v1/internal/3rdsys/audit-log/refresh-nodes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11350', 'syncBillingListToZolonBillingCenter', '/v1/internal/3rdsys/billing', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11351', 'abandonBillings', '/v1/internal/3rdsys/billing/abandon', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11352', 'cancelWriteOffMarketBillingSummary', '/v1/internal/3rdsys/billing/cancel/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11353', 'syncSolutionDeveloperList', '/v1/internal/3rdsys/billing/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11354', 'searchMarkets_1', '/v1/internal/3rdsys/billing/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11355', 'partialWriteOffMarketBilling', '/v1/internal/3rdsys/billing/partial/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11356', 'writeOffMarketBilling', '/v1/internal/3rdsys/billing/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11357', 'updateBillingServiceSyncedStatus', '/v1/internal/3rdsys/billing/{billingSummaryId}/callback', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11358', 'updateBillingServicePrice', '/v1/internal/3rdsys/billing/{billingSummaryId}/price/sync', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11359', 'lockSet', '/v1/internal/3rdsys/cache/set', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11360', 'clearCache', '/v1/internal/3rdsys/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11361', 'clearDeletedTerminals', '/v1/internal/3rdsys/clearDeletedTerminals', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11362', 'clearExpiredPendingTerminalActions', '/v1/internal/3rdsys/clearExpiredPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11363', 'clearGroupPendingTerminalActions', '/v1/internal/3rdsys/clearGroupPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11364', 'clearTerminalPendingTerminalActions', '/v1/internal/3rdsys/clearTerminalPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11365', 'delLock', '/v1/internal/3rdsys/delLock', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11366', 'getAllDisabledRequests', '/v1/internal/3rdsys/disabled-request', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11367', 'addDisabledRequest', '/v1/internal/3rdsys/disabled-request', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11368', 'delDisabledRequest', '/v1/internal/3rdsys/disabled-request', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11369', 'clearDisabledRequest', '/v1/internal/3rdsys/disabled-request/all', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11370', 'generateApkPatchMd5', '/v1/internal/3rdsys/generateApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11371', 'generateClientApkPatchMd5', '/v1/internal/3rdsys/generateClientApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11372', 'getCache', '/v1/internal/3rdsys/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11373', 'getLock', '/v1/internal/3rdsys/getLock', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11374', 'sendDictionaryDataToGoInsight', '/v1/internal/3rdsys/insight/sync/dictionary-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11375', 'sendMarketDataToGoInsight', '/v1/internal/3rdsys/insight/sync/market-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11376', 'sendTerminalRealTimeDataToGoInsight', '/v1/internal/3rdsys/insight/sync/terminal-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11377', 'loadSystemProperty', '/v1/internal/3rdsys/load/system/property/cache', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11378', 'lock', '/v1/internal/3rdsys/lock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11379', 'findMarketApiBlackList', '/v1/internal/3rdsys/marketBlackApi', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11380', 'delMarketBlackApi', '/v1/internal/3rdsys/marketBlackApi/{marketBlackApiId}/market/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11381', 'createMarketBlackApi', '/v1/internal/3rdsys/marketBlackApi/{marketId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11382', 'getAuthCode', '/v1/internal/3rdsys/maxsearch/auth-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11383', 'checkDataSyncProcess', '/v1/internal/3rdsys/maxsearch/data-sync', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11384', 'doDataSync', '/v1/internal/3rdsys/maxsearch/data-sync', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11385', 'migrationGroupFilteredAction', '/v1/internal/3rdsys/migrationGroupFilteredAction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11386', 'refreshGroupActionCount', '/v1/internal/3rdsys/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11387', 'refreshPushTaskDownloadTime', '/v1/internal/3rdsys/refreshPushTaskDownloadTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11388', 'refreshResellerInstalledApks', '/v1/internal/3rdsys/refreshResellerInstalledApks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11389', 'refreshResellerPushedParamApk', '/v1/internal/3rdsys/refreshResellerPushedParamApk', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11390', 'refreshTerminalLastAccessTime', '/v1/internal/3rdsys/refreshTerminalLastAccessTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11391', 'refreshTerminalLastApkParam', '/v1/internal/3rdsys/refreshTerminalLastApkParam', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11392', 'refreshTerminalOnlineStatus', '/v1/internal/3rdsys/refreshTerminalOnlineStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11393', 'refreshTerminalStock', '/v1/internal/3rdsys/refreshTerminalStock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11394', 'repairActivityStatus', '/v1/internal/3rdsys/repairActivityStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11395', 'repairGroupPushTask', '/v1/internal/3rdsys/repairGroupPushTask', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11396', 'repairMerchantMigrationStatus', '/v1/internal/3rdsys/repairMerchantMigrationStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11397', 'repairReportExecutionContextStatus', '/v1/internal/3rdsys/repairReportExecutionContextStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11398', 'getSystemPropertyLog', '/v1/internal/3rdsys/system/property/log', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11399', 'findSystemProperties', '/v1/internal/3rdsys/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11400', 'createSystemProperty', '/v1/internal/3rdsys/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11401', 'deleteSystemProperty', '/v1/internal/3rdsys/systemProperty', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11402', 'resetSystemProperty', '/v1/internal/3rdsys/systemProperty/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11403', 'getTerminal', '/v1/internal/3rdsys/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11404', 'sendTerminalCommand', '/v1/internal/3rdsys/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11405', 'sendTerminalMessage', '/v1/internal/3rdsys/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11406', 'getTerminalPushHistory', '/v1/internal/3rdsys/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11407', 'resetTerminal', '/v1/internal/3rdsys/terminal/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11408', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11409', 'getLatestOnlineApkList', '/v1/internal/appUpdate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11410', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11411', 'getApps', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11412', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11413', 'createVasAgreement', '/v1/marketAdmin/vas/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11414', 'publishVasAgreement', '/v1/marketAdmin/vas/agreement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11415', 'updateVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11416', 'deleteVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11417', 'downloadVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11418', 'findVasAgreements', '/v1/marketAdmin/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11419', 'getAirViewerCurrentUsage', '/v1/marketAdmin/vas/airViewer/currentUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11420', 'getAirViewerCurrentUsageDashBoard', '/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11421', 'getAirViewerHistoricalUsage', '/v1/marketAdmin/vas/airViewer/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11422', 'exportAirviewerUsage', '/v1/marketAdmin/vas/airviewer/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11423', 'exportAppScanUsage', '/v1/marketAdmin/vas/appscan/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11424', 'getMarketServiceBillingSetting', '/v1/marketAdmin/vas/billingSetting/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11425', 'findCyberLabTerminalBlacklistPage', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11426', 'deleteCyberLabTerminalBlacklist', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11427', 'exportDetailZipByServiceType', '/v1/marketAdmin/vas/export/detail/zip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11428', 'exportSummaryByServiceType', '/v1/marketAdmin/vas/export/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11429', 'getInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11430', 'changeInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11431', 'searchMarkets', '/v1/marketAdmin/vas/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11432', 'exportMarkets', '/v1/marketAdmin/vas/markets/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11433', 'getCloudMessageTrialCount', '/v1/marketAdmin/vas/markets/{marketId}/trial/msg/count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11434', 'findVasServices', '/v1/marketAdmin/vas/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11435', 'showCurrentMonthUsage', '/v1/marketAdmin/vas/services/show', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11436', 'updateActiveStatus', '/v1/marketAdmin/vas/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11437', 'unsubscribeService', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11438', 'subscribeService', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11439', 'findServiceResellerSpecificPage', '/v1/marketAdmin/vas/services/{serviceType}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11440', 'specificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11441', 'deleteSpecificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11442', 'findSubscribeHistoryPage', '/v1/marketAdmin/vas/subscriptionHistory/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11443', 'loadCurrentEnrollTerminalBill', '/v1/marketAdmin/vas/terminal/enroll/bill', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11444', 'loadCurrentEnrollTerminalDashBoard', '/v1/marketAdmin/vas/terminal/enroll/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11445', 'loadEnrollTerminalHistory', '/v1/marketAdmin/vas/terminal/enroll/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11446', 'loadEnrollTerminalHistoryDetail', '/v1/marketAdmin/vas/terminal/enroll/history/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11447', 'downloadCurrentEnrollTerminal', '/v1/marketAdmin/vas/terminal/enroll/{marketId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11448', 'updateMarketDevServiceStatus', '/v1/marketAdmin/vas/{developerId}/service/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11449', 'getVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11450', 'agreeVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11451', 'findCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11452', 'searchMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11453', 'exportCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/export/current/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11454', 'exportMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/export/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11455', 'exportHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11456', 'findHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11457', 'getUsageDashBoard', '/v1/marketAdmin/vas/{serviceType}/usage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11458', 'getUser_1', '/v1/merchant/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11459', 'findPurchaseAppList', '/v1/merchant/dashboard/purchased-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11460', 'loadWidgetModelTerminal', '/v1/merchant/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11461', 'loadWidgetTerminalOffline', '/v1/merchant/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11462', 'loadMerchantPortalWidget', '/v1/merchant/dashboard/widgets/W21', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11463', 'getMarket_1', '/v1/merchant/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11464', 'createNavigoBuriedPoints', '/v1/navigo/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11465', 'initBillingPayment', '/v1/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11466', 'checkout', '/v1/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11467', 'searchApps', '/v1/portal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11468', 'getAdvertisement', '/v1/portal/apps/advertisement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11469', 'searchOnlineAppCategories', '/v1/portal/apps/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11470', 'findDeveloperApp', '/v1/portal/apps/developer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11471', 'findFeaturedApp', '/v1/portal/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11472', 'getAppDetailByPackageName', '/v1/portal/apps/packageName', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11473', 'searchAppsRank', '/v1/portal/apps/rank', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11474', 'findRelatedApp', '/v1/portal/apps/related', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11475', 'getAppTypes', '/v1/portal/apps/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11476', 'getAppDetail', '/v1/portal/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11477', 'getUser', '/v1/portal/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11478', 'getMarket', '/v1/portal/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11479', 'sysVersion', '/v1/public/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
('1', '11229'),
('1', '11233'),
('1', '11234'),

('2', '11186'),
('2', '11187'),
('2', '11215'),
('2', '11236'),
('2', '11237'),
('2', '11238'),
('2', '11239'),
('2', '11240'),
('2', '11241'),
('2', '11242'),
('2', '11243'),
('2', '11244'),
('2', '11245'),
('2', '11246'),
('2', '11247'),
('2', '11248'),
('2', '11249'),
('2', '11250'),
('2', '11251'),
('2', '11252'),
('2', '11253'),
('2', '11254'),
('2', '11255'),
('2', '11256'),
('2', '11257'),
('2', '11258'),
('2', '11259'),
('2', '11260'),
('2', '11261'),
('2', '11262'),
('2', '11263'),
('2', '11264'),
('2', '11265'),
('2', '11266'),
('2', '11270'),
('2', '11271'),
('2', '11272'),
('2', '11274'),
('2', '11275'),
('2', '11276'),
('2', '11277'),
('2', '11278'),
('2', '11279'),
('2', '11280'),
('2', '11281'),
('2', '11282'),
('2', '11283'),
('2', '11284'),
('2', '11285'),
('2', '11286'),
('2', '11287'),
('2', '11288'),
('2', '11289'),
('2', '11290'),
('2', '11291'),
('2', '11292'),
('2', '11293'),
('2', '11294'),
('2', '11295'),
('2', '11296'),
('2', '11297'),
('2', '11298'),
('2', '11299'),
('2', '11300'),
('2', '11301'),
('2', '11302'),
('2', '11303'),
('2', '11304'),
('2', '11305'),
('2', '11306'),
('2', '11307'),
('2', '11308'),
('2', '11309'),
('2', '11310'),
('2', '11311'),
('2', '11312'),
('2', '11313'),
('2', '11314'),
('2', '11315'),
('2', '11316'),
('2', '11317'),
('2', '11318'),
('2', '11319'),
('2', '11320'),
('2', '11321'),
('2', '11322'),
('2', '11323'),
('2', '11324'),
('2', '11325'),
('2', '11326'),
('2', '11327'),
('2', '11328'),
('2', '11329'),
('2', '11330'),
('2', '11331'),
('2', '11332'),
('2', '11333'),
('2', '11334'),
('2', '11335'),
('2', '11336'),
('2', '11337'),
('2', '11338'),
('2', '11339'),
('2', '11340'),
('2', '11341'),
('2', '11342'),
('2', '11343'),
('2', '11344'),
('2', '11345'),

('3', '10629'),
('3', '10630'),
('3', '10631'),
('3', '10632'),
('3', '10633'),
('3', '10634'),
('3', '10635'),
('3', '10636'),
('3', '10637'),
('3', '10638'),
('3', '10639'),
('3', '10640'),
('3', '10641'),
('3', '10642'),
('3', '10643'),
('3', '10644'),
('3', '10645'),
('3', '10646'),
('3', '10647'),
('3', '10648'),
('3', '10649'),
('3', '10650'),
('3', '10651'),
('3', '10652'),
('3', '10653'),
('3', '10654'),
('3', '10655'),
('3', '10656'),
('3', '10657'),
('3', '10658'),

('20', '11459'),
('20', '11460'),
('20', '11461'),
('20', '11462'),

('51', '10263'),
('51', '10266'),
('51', '10267'),
('51', '10268'),
('51', '10269'),
('51', '10270'),
('51', '10271'),
('51', '10272'),
('51', '10273'),
('51', '10274'),
('51', '10275'),
('51', '10276'),
('51', '10277'),
('51', '10278'),
('51', '10279'),
('51', '10280'),
('51', '10281'),
('51', '10282'),
('51', '10283'),
('51', '10284'),
('51', '10285'),
('51', '10286'),
('51', '10287'),
('51', '10288'),
('51', '10289'),
('51', '10290'),
('51', '10291'),
('51', '10292'),
('51', '11215'),

('53', '10358'),
('53', '10360'),
('53', '10361'),
('53', '10362'),
('53', '10363'),
('53', '10364'),
('53', '10365'),
('53', '10366'),

('1041', '10149'),
('1041', '10150'),
('1041', '10152'),
('1041', '10153'),
('1041', '10154'),
('1041', '10155'),
('1041', '10156'),
('1041', '10253'),
('1041', '10262'),
('1041', '10263'),

('1042', '10149'),
('1042', '10150'),
('1042', '10151'),
('1042', '10152'),
('1042', '10153'),
('1042', '10154'),
('1042', '10155'),
('1042', '10156'),
('1042', '10253'),
('1042', '10262'),
('1042', '10263'),

('601', '10253'),
('601', '10255'),
('601', '10257'),
('601', '10258'),
('601', '10262'),
('601', '10263'),
('601', '10781'),
('601', '10782'),
('601', '10783'),
('601', '10786'),
('601', '10788'),
('601', '10789'),
('601', '10790'),
('601', '10792'),
('601', '10794'),
('601', '10795'),
('601', '10798'),
('601', '10801'),
('601', '10802'),
('601', '10803'),
('601', '10804'),
('601', '10805'),
('601', '10806'),
('601', '10810'),
('601', '10811'),
('601', '10812'),
('601', '10813'),
('601', '11184'),
('601', '11186'),
('601', '11187'),
('601', '11188'),

('602', '10253'),
('602', '10257'),
('602', '10258'),
('602', '10262'),
('602', '10263'),
('602', '10781'),
('602', '10782'),
('602', '10783'),
('602', '10784'),
('602', '10785'),
('602', '10786'),
('602', '10787'),
('602', '10788'),
('602', '10789'),
('602', '10790'),
('602', '10791'),
('602', '10792'),
('602', '10793'),
('602', '10794'),
('602', '10795'),
('602', '10796'),
('602', '10797'),
('602', '10798'),
('602', '10799'),
('602', '10800'),
('602', '10801'),
('602', '10802'),
('602', '10803'),
('602', '10804'),
('602', '10805'),
('602', '10806'),
('602', '10807'),
('602', '10808'),
('602', '10809'),
('602', '10810'),
('602', '10811'),
('602', '10812'),
('602', '10813'),
('602', '11184'),
('602', '11185'),
('602', '11186'),
('602', '11187'),
('602', '11188'),
('602', '11191'),
('602', '11192'),

('611', '10157'),
('611', '10158'),
('611', '10159'),
('611', '10160'),
('611', '10163'),
('611', '10164'),
('611', '10166'),
('611', '10169'),
('611', '10171'),
('611', '10172'),
('611', '10173'),
('611', '10174'),
('611', '10175'),
('611', '10176'),
('611', '10179'),
('611', '10193'),
('611', '10194'),
('611', '10197'),
('611', '10198'),
('611', '10201'),
('611', '10204'),
('611', '10206'),
('611', '10207'),
('611', '10253'),
('611', '10257'),
('611', '10258'),
('611', '10262'),
('611', '10263'),
('611', '11184'),
('611', '11186'),
('611', '11187'),
('611', '11188'),

('612', '10157'),
('612', '10158'),
('612', '10159'),
('612', '10160'),
('612', '10161'),
('612', '10162'),
('612', '10163'),
('612', '10164'),
('612', '10165'),
('612', '10166'),
('612', '10167'),
('612', '10168'),
('612', '10169'),
('612', '10170'),
('612', '10171'),
('612', '10172'),
('612', '10173'),
('612', '10174'),
('612', '10175'),
('612', '10176'),
('612', '10177'),
('612', '10178'),
('612', '10179'),
('612', '10180'),
('612', '10181'),
('612', '10182'),
('612', '10183'),
('612', '10184'),
('612', '10185'),
('612', '10186'),
('612', '10187'),
('612', '10188'),
('612', '10189'),
('612', '10190'),
('612', '10191'),
('612', '10192'),
('612', '10193'),
('612', '10194'),
('612', '10195'),
('612', '10196'),
('612', '10197'),
('612', '10198'),
('612', '10199'),
('612', '10200'),
('612', '10201'),
('612', '10202'),
('612', '10203'),
('612', '10204'),
('612', '10205'),
('612', '10206'),
('612', '10207'),
('612', '10208'),
('612', '10253'),
('612', '10257'),
('612', '10258'),
('612', '10262'),
('612', '10263'),
('612', '11184'),
('612', '11185'),
('612', '11186'),
('612', '11187'),
('612', '11188'),
('612', '11191'),
('612', '11192'),

('661', '10253'),
('661', '10255'),
('661', '10257'),
('661', '10262'),
('661', '10263'),
('661', '10736'),
('661', '10737'),
('661', '10738'),
('661', '10739'),
('661', '10740'),
('661', '10742'),

('662', '10253'),
('662', '10255'),
('662', '10257'),
('662', '10262'),
('662', '10263'),
('662', '10736'),
('662', '10737'),
('662', '10738'),
('662', '10739'),
('662', '10740'),
('662', '10741'),
('662', '10742'),

('621', '10157'),
('621', '10158'),
('621', '10159'),
('621', '10160'),
('621', '10163'),
('621', '10164'),
('621', '10166'),
('621', '10169'),
('621', '10171'),
('621', '10172'),
('621', '10173'),
('621', '10174'),
('621', '10175'),
('621', '10176'),
('621', '10179'),
('621', '10193'),
('621', '10194'),
('621', '10197'),
('621', '10198'),
('621', '10201'),
('621', '10204'),
('621', '10206'),
('621', '10207'),
('621', '10253'),
('621', '10257'),
('621', '10262'),
('621', '10263'),
('621', '10305'),
('621', '10306'),
('621', '10307'),
('621', '10321'),
('621', '10323'),
('621', '10327'),
('621', '11184'),
('621', '11186'),
('621', '11187'),
('621', '11188'),

('622', '10157'),
('622', '10158'),
('622', '10159'),
('622', '10160'),
('622', '10163'),
('622', '10164'),
('622', '10166'),
('622', '10169'),
('622', '10171'),
('622', '10172'),
('622', '10173'),
('622', '10174'),
('622', '10175'),
('622', '10176'),
('622', '10179'),
('622', '10193'),
('622', '10194'),
('622', '10197'),
('622', '10198'),
('622', '10201'),
('622', '10204'),
('622', '10206'),
('622', '10207'),
('622', '10253'),
('622', '10257'),
('622', '10262'),
('622', '10263'),
('622', '10305'),
('622', '10306'),
('622', '10307'),
('622', '10308'),
('622', '10309'),
('622', '10310'),
('622', '10311'),
('622', '10312'),
('622', '10313'),
('622', '10314'),
('622', '10315'),
('622', '10316'),
('622', '10317'),
('622', '10318'),
('622', '10319'),
('622', '10320'),
('622', '10321'),
('622', '10322'),
('622', '10323'),
('622', '10324'),
('622', '10325'),
('622', '10326'),
('622', '10327'),
('622', '11184'),
('622', '11186'),
('622', '11187'),
('622', '11188'),

('651', '10253'),
('651', '10258'),
('651', '10262'),
('651', '10263'),
('651', '10715'),
('651', '10716'),
('651', '10718'),
('651', '10719'),
('651', '10722'),
('651', '10723'),
('651', '10724'),
('651', '10725'),
('651', '10726'),
('651', '10727'),
('651', '10728'),
('651', '10729'),
('651', '10730'),
('651', '10731'),
('651', '10734'),
('651', '10735'),
('651', '11187'),
('651', '11188'),

('652', '10253'),
('652', '10258'),
('652', '10262'),
('652', '10263'),
('652', '10715'),
('652', '10716'),
('652', '10717'),
('652', '10718'),
('652', '10719'),
('652', '10720'),
('652', '10721'),
('652', '10722'),
('652', '10723'),
('652', '10724'),
('652', '10725'),
('652', '10726'),
('652', '10727'),
('652', '10728'),
('652', '10729'),
('652', '10730'),
('652', '10731'),
('652', '10732'),
('652', '10733'),
('652', '10734'),
('652', '10735'),
('652', '11187'),
('652', '11188'),

('711', '10246'),
('711', '10248'),
('711', '10249'),
('711', '10250'),
('711', '10251'),
('711', '10252'),
('711', '10253'),
('711', '10254'),
('711', '10255'),
('711', '10258'),
('711', '10260'),
('711', '10262'),
('711', '10263'),
('711', '10824'),
('711', '10825'),
('711', '10828'),
('711', '10970'),
('711', '10971'),
('711', '10972'),
('711', '10973'),
('711', '10978'),
('711', '10979'),
('711', '10983'),
('711', '10986'),
('711', '10987'),
('711', '10988'),
('711', '10991'),
('711', '10996'),
('711', '11001'),
('711', '11002'),
('711', '11004'),
('711', '11005'),
('711', '11008'),
('711', '11013'),
('711', '11017'),
('711', '11020'),
('711', '11024'),
('711', '11026'),
('711', '11027'),
('711', '11030'),
('711', '11031'),
('711', '11032'),
('711', '11035'),
('711', '11039'),
('711', '11040'),
('711', '11041'),
('711', '11042'),
('711', '11043'),
('711', '11044'),
('711', '11045'),
('711', '11046'),
('711', '11047'),
('711', '11048'),
('711', '11049'),
('711', '11050'),
('711', '11051'),
('711', '11055'),
('711', '11056'),
('711', '11058'),
('711', '11063'),
('711', '11065'),
('711', '11070'),
('711', '11073'),
('711', '11074'),
('711', '11078'),
('711', '11079'),
('711', '11081'),
('711', '11082'),
('711', '11083'),
('711', '11084'),
('711', '11085'),
('711', '11086'),
('711', '11087'),
('711', '11089'),
('711', '11097'),
('711', '11098'),
('711', '11099'),
('711', '11100'),
('711', '11111'),
('711', '11112'),
('711', '11113'),
('711', '11114'),
('711', '11117'),
('711', '11119'),
('711', '11120'),
('711', '11121'),
('711', '11122'),
('711', '11123'),
('711', '11125'),
('711', '11126'),
('711', '11127'),
('711', '11128'),
('711', '11130'),
('711', '11132'),
('711', '11134'),
('711', '11136'),
('711', '11137'),
('711', '11138'),
('711', '11139'),
('711', '11140'),
('711', '11141'),
('711', '11144'),
('711', '11174'),
('711', '11177'),

('712', '10246'),
('712', '10248'),
('712', '10249'),
('712', '10250'),
('712', '10251'),
('712', '10252'),
('712', '10253'),
('712', '10254'),
('712', '10255'),
('712', '10258'),
('712', '10260'),
('712', '10262'),
('712', '10263'),
('712', '10824'),
('712', '10825'),
('712', '10828'),
('712', '10970'),
('712', '10971'),
('712', '10972'),
('712', '10973'),
('712', '10978'),
('712', '10979'),
('712', '10983'),
('712', '10986'),
('712', '10987'),
('712', '10988'),
('712', '10991'),
('712', '10996'),
('712', '11001'),
('712', '11002'),
('712', '11004'),
('712', '11005'),
('712', '11008'),
('712', '11013'),
('712', '11017'),
('712', '11020'),
('712', '11024'),
('712', '11026'),
('712', '11027'),
('712', '11030'),
('712', '11031'),
('712', '11032'),
('712', '11035'),
('712', '11039'),
('712', '11040'),
('712', '11041'),
('712', '11042'),
('712', '11043'),
('712', '11044'),
('712', '11045'),
('712', '11046'),
('712', '11047'),
('712', '11048'),
('712', '11049'),
('712', '11050'),
('712', '11051'),
('712', '11055'),
('712', '11056'),
('712', '11058'),
('712', '11063'),
('712', '11065'),
('712', '11070'),
('712', '11073'),
('712', '11074'),
('712', '11078'),
('712', '11079'),
('712', '11081'),
('712', '11082'),
('712', '11083'),
('712', '11084'),
('712', '11085'),
('712', '11086'),
('712', '11087'),
('712', '11089'),
('712', '11097'),
('712', '11098'),
('712', '11099'),
('712', '11100'),
('712', '11111'),
('712', '11112'),
('712', '11113'),
('712', '11114'),
('712', '11117'),
('712', '11119'),
('712', '11120'),
('712', '11121'),
('712', '11122'),
('712', '11123'),
('712', '11125'),
('712', '11126'),
('712', '11127'),
('712', '11128'),
('712', '11130'),
('712', '11132'),
('712', '11134'),
('712', '11136'),
('712', '11137'),
('712', '11138'),
('712', '11139'),
('712', '11140'),
('712', '11141'),
('712', '11144'),
('712', '11174'),
('712', '11177'),

('7124', '11056'),
('7124', '11057'),
('7124', '11058'),
('7124', '11059'),
('7124', '11060'),
('7124', '11061'),
('7124', '11062'),

('71211', '11000'),
('71211', '11001'),
('71211', '11002'),
('71211', '11003'),
('71211', '11004'),
('71211', '11005'),
('71211', '11006'),
('71211', '11007'),
('71211', '11008'),
('71211', '11010'),
('71211', '11011'),
('71211', '11012'),
('71211', '11013'),
('71211', '11015'),
('71211', '11016'),
('71211', '11017'),
('71211', '11018'),
('71211', '11019'),
('71211', '11020'),
('71211', '11021'),
('71211', '11022'),
('71211', '11023'),

('71213', '10982'),
('71213', '10983'),
('71213', '10984'),
('71213', '10985'),
('71213', '10986'),
('71213', '10987'),
('71213', '10988'),
('71213', '10989'),
('71213', '10990'),
('71213', '10991'),
('71213', '10993'),
('71213', '10994'),
('71213', '10995'),
('71213', '10996'),
('71213', '10998'),
('71213', '10999'),

('71212', '10992'),
('71212', '10997'),
('71212', '11009'),
('71212', '11014'),

('71221', '11077'),
('71221', '11078'),
('71221', '11079'),
('71221', '11080'),
('71221', '11081'),
('71221', '11082'),
('71221', '11083'),
('71221', '11084'),
('71221', '11085'),
('71221', '11086'),
('71221', '11087'),
('71221', '11088'),
('71221', '11089'),
('71221', '11090'),
('71221', '11091'),
('71221', '11092'),
('71221', '11093'),
('71221', '11094'),
('71221', '11095'),
('71221', '11096'),
('71221', '11097'),
('71221', '11098'),
('71221', '11099'),
('71221', '11100'),
('71221', '11101'),
('71221', '11102'),
('71221', '11103'),
('71221', '11104'),
('71221', '11105'),
('71221', '11106'),
('71221', '11107'),
('71221', '11108'),
('71221', '11109'),
('71221', '11110'),
('71221', '11111'),
('71221', '11112'),
('71221', '11113'),
('71221', '11114'),
('71221', '11115'),
('71221', '11116'),
('71221', '11117'),
('71221', '11118'),
('71221', '11119'),
('71221', '11120'),
('71221', '11121'),
('71221', '11122'),
('71221', '11123'),
('71221', '11124'),
('71221', '11125'),
('71221', '11126'),
('71221', '11127'),
('71221', '11128'),
('71221', '11129'),
('71221', '11130'),
('71221', '11131'),
('71221', '11132'),
('71221', '11133'),
('71221', '11134'),
('71221', '11135'),
('71221', '11136'),
('71221', '11137'),
('71221', '11138'),
('71221', '11139'),
('71221', '11140'),
('71221', '11141'),
('71221', '11142'),
('71221', '11143'),
('71221', '11144'),
('71221', '11145'),
('71221', '11146'),

('71222', '11039'),
('71222', '11040'),
('71222', '11041'),
('71222', '11042'),
('71222', '11043'),
('71222', '11044'),
('71222', '11045'),
('71222', '11046'),
('71222', '11047'),
('71222', '11048'),
('71222', '11049'),
('71222', '11050'),
('71222', '11051'),
('71222', '11052'),
('71222', '11053'),
('71222', '11054'),
('71222', '11055'),
('71222', '11133'),
('71222', '11134'),
('71222', '11135'),
('71222', '11136'),
('71222', '11137'),
('71222', '11138'),
('71222', '11139'),
('71222', '11140'),
('71222', '11141'),
('71222', '11142'),
('71222', '11143'),
('71222', '11144'),
('71222', '11145'),
('71222', '11146'),

('71231', '11024'),
('71231', '11025'),
('71231', '11026'),
('71231', '11027'),
('71231', '11028'),
('71231', '11029'),
('71231', '11030'),
('71231', '11031'),
('71231', '11032'),
('71231', '11035'),
('71231', '11037'),
('71231', '11038'),

('71232', '10973'),
('71232', '10974'),
('71232', '10975'),
('71232', '10976'),
('71232', '10977'),
('71232', '10978'),
('71232', '10979'),
('71232', '10980'),
('71232', '10981'),
('71232', '11024'),
('71232', '11025'),
('71232', '11026'),
('71232', '11027'),
('71232', '11028'),
('71232', '11029'),
('71232', '11030'),
('71232', '11031'),
('71232', '11032'),
('71232', '11033'),
('71232', '11034'),
('71232', '11035'),
('71232', '11036'),
('71232', '11037'),
('71232', '11038'),
('71232', '11070'),
('71232', '11071'),
('71232', '11072'),
('71232', '11073'),
('71232', '11074'),
('71232', '11075'),
('71232', '11076'),

('71233', '11129'),

('71251', '10261'),
('71251', '11063'),
('71251', '11064'),
('71251', '11065'),
('71251', '11066'),
('71251', '11067'),
('71251', '11068'),
('71251', '11069'),

('71252', '10261'),
('71252', '11017'),
('71252', '11018'),
('71252', '11019'),
('71252', '11020'),
('71252', '11021'),
('71252', '11022'),
('71252', '11023'),

('721', '10246'),
('721', '10248'),
('721', '10249'),
('721', '10250'),
('721', '10252'),
('721', '10253'),
('721', '10254'),
('721', '10255'),
('721', '10258'),
('721', '10260'),
('721', '10262'),
('721', '10263'),
('721', '10814'),
('721', '10816'),
('721', '10820'),
('721', '10824'),
('721', '10825'),
('721', '10828'),
('721', '10832'),
('721', '10834'),
('721', '10835'),
('721', '10839'),
('721', '10840'),
('721', '10841'),
('721', '10843'),
('721', '10846'),
('721', '10854'),
('721', '10855'),
('721', '10858'),
('721', '10860'),
('721', '10861'),
('721', '10862'),
('721', '10865'),
('721', '10869'),
('721', '10872'),
('721', '10874'),
('721', '10877'),
('721', '10878'),
('721', '10879'),
('721', '10881'),
('721', '10888'),
('721', '10889'),
('721', '10890'),
('721', '10892'),
('721', '10893'),
('721', '10900'),
('721', '10901'),
('721', '10902'),
('721', '10904'),
('721', '10911'),
('721', '10912'),
('721', '10913'),
('721', '10915'),
('721', '10916'),
('721', '10919'),
('721', '10923'),
('721', '10924'),
('721', '10927'),
('721', '10931'),
('721', '10933'),
('721', '10934'),
('721', '10938'),
('721', '10939'),
('721', '10940'),
('721', '10942'),
('721', '10944'),
('721', '10951'),
('721', '10952'),
('721', '10953'),
('721', '10958'),
('721', '10959'),
('721', '10962'),
('721', '10969'),

('722', '10246'),
('722', '10248'),
('722', '10249'),
('722', '10250'),
('722', '10252'),
('722', '10253'),
('722', '10254'),
('722', '10255'),
('722', '10258'),
('722', '10260'),
('722', '10262'),
('722', '10263'),
('722', '10814'),
('722', '10816'),
('722', '10820'),
('722', '10824'),
('722', '10825'),
('722', '10828'),
('722', '10832'),
('722', '10834'),
('722', '10835'),
('722', '10839'),
('722', '10840'),
('722', '10841'),
('722', '10843'),
('722', '10846'),
('722', '10854'),
('722', '10855'),
('722', '10858'),
('722', '10860'),
('722', '10861'),
('722', '10862'),
('722', '10865'),
('722', '10869'),
('722', '10872'),
('722', '10874'),
('722', '10877'),
('722', '10878'),
('722', '10879'),
('722', '10881'),
('722', '10888'),
('722', '10889'),
('722', '10890'),
('722', '10892'),
('722', '10893'),
('722', '10900'),
('722', '10901'),
('722', '10902'),
('722', '10904'),
('722', '10911'),
('722', '10912'),
('722', '10913'),
('722', '10915'),
('722', '10916'),
('722', '10919'),
('722', '10923'),
('722', '10924'),
('722', '10927'),
('722', '10931'),
('722', '10933'),
('722', '10934'),
('722', '10938'),
('722', '10939'),
('722', '10940'),
('722', '10942'),
('722', '10944'),
('722', '10951'),
('722', '10952'),
('722', '10953'),
('722', '10958'),
('722', '10959'),
('722', '10962'),
('722', '10969'),

('7221', '10814'),
('7221', '10815'),
('7221', '10816'),
('7221', '10817'),
('7221', '10818'),
('7221', '10819'),
('7221', '10820'),
('7221', '10821'),
('7221', '10822'),
('7221', '10823'),
('7221', '10824'),
('7221', '10825'),
('7221', '10826'),
('7221', '10827'),
('7221', '10828'),
('7221', '10829'),
('7221', '10830'),
('7221', '10831'),
('7221', '10832'),
('7221', '10833'),
('7221', '10834'),
('7221', '10835'),
('7221', '10836'),
('7221', '10837'),
('7221', '10838'),
('7221', '10839'),
('7221', '10840'),
('7221', '10841'),
('7221', '10842'),
('7221', '10843'),
('7221', '10844'),
('7221', '10845'),
('7221', '10846'),
('7221', '10847'),
('7221', '10848'),
('7221', '10849'),
('7221', '10850'),
('7221', '10851'),
('7221', '10852'),
('7221', '10853'),
('7221', '10854'),
('7221', '10855'),
('7221', '10856'),
('7221', '10857'),
('7221', '10858'),
('7221', '10859'),
('7221', '10860'),
('7221', '10861'),
('7221', '10862'),
('7221', '10863'),
('7221', '10864'),
('7221', '10865'),
('7221', '10866'),
('7221', '10867'),
('7221', '10868'),
('7221', '10869'),
('7221', '10870'),
('7221', '10871'),
('7221', '10872'),
('7221', '10873'),
('7221', '10874'),
('7221', '10875'),
('7221', '10876'),
('7221', '10877'),
('7221', '10878'),
('7221', '10879'),
('7221', '10880'),
('7221', '10881'),
('7221', '10882'),
('7221', '10883'),
('7221', '10884'),
('7221', '10885'),
('7221', '10886'),
('7221', '10887'),
('7221', '10888'),
('7221', '10889'),
('7221', '10890'),
('7221', '10891'),
('7221', '10892'),
('7221', '10893'),
('7221', '10894'),
('7221', '10895'),
('7221', '10896'),
('7221', '10897'),
('7221', '10898'),
('7221', '10899'),
('7221', '10900'),
('7221', '10901'),
('7221', '10902'),
('7221', '10903'),
('7221', '10904'),
('7221', '10905'),
('7221', '10906'),
('7221', '10907'),
('7221', '10908'),
('7221', '10909'),
('7221', '10910'),
('7221', '10911'),
('7221', '10912'),
('7221', '10913'),
('7221', '10914'),
('7221', '10915'),
('7221', '10916'),
('7221', '10917'),
('7221', '10918'),
('7221', '10919'),
('7221', '10920'),
('7221', '10921'),
('7221', '10922'),
('7221', '10923'),
('7221', '10924'),
('7221', '10925'),
('7221', '10926'),
('7221', '10927'),
('7221', '10928'),
('7221', '10929'),
('7221', '10930'),
('7221', '10931'),
('7221', '10932'),
('7221', '10933'),
('7221', '10934'),
('7221', '10935'),
('7221', '10936'),
('7221', '10937'),
('7221', '10938'),
('7221', '10939'),
('7221', '10940'),
('7221', '10941'),
('7221', '10942'),
('7221', '10943'),
('7221', '10944'),
('7221', '10945'),
('7221', '10946'),
('7221', '10947'),
('7221', '10948'),
('7221', '10949'),
('7221', '10950'),
('7221', '10951'),
('7221', '10952'),
('7221', '10953'),
('7221', '10954'),
('7221', '10955'),
('7221', '10956'),
('7221', '10957'),
('7221', '10958'),
('7221', '10959'),
('7221', '10960'),
('7221', '10961'),
('7221', '10962'),
('7221', '10963'),
('7221', '10964'),
('7221', '10965'),
('7221', '10966'),
('7221', '10967'),
('7221', '10968'),
('7221', '10969'),

('7223', '10841'),
('7223', '10842'),
('7223', '10843'),
('7223', '10844'),
('7223', '10845'),
('7223', '10846'),
('7223', '10847'),
('7223', '10848'),
('7223', '10849'),
('7223', '10850'),
('7223', '10851'),
('7223', '10852'),
('7223', '10853'),
('7223', '10854'),
('7223', '10855'),

('7224', '10261'),
('7224', '10902'),
('7224', '10903'),
('7224', '10904'),
('7224', '10905'),
('7224', '10906'),
('7224', '10907'),
('7224', '10908'),
('7224', '10909'),
('7224', '10910'),
('7224', '10911'),
('7224', '10912'),

('7225', '10879'),
('7225', '10880'),
('7225', '10881'),
('7225', '10882'),
('7225', '10883'),
('7225', '10884'),
('7225', '10885'),
('7225', '10886'),
('7225', '10887'),
('7225', '10888'),
('7225', '10889'),

('7226', '10890'),
('7226', '10891'),
('7226', '10892'),
('7226', '10893'),
('7226', '10894'),
('7226', '10895'),
('7226', '10896'),
('7226', '10897'),
('7226', '10898'),
('7226', '10899'),
('7226', '10900'),
('7226', '10901'),

('7227', '10858'),
('7227', '10859'),
('7227', '10860'),
('7227', '10861'),
('7227', '10862'),
('7227', '10863'),
('7227', '10864'),
('7227', '10865'),
('7227', '10866'),
('7227', '10867'),
('7227', '10868'),
('7227', '10869'),
('7227', '10870'),
('7227', '10871'),
('7227', '10872'),
('7227', '10873'),
('7227', '10874'),
('7227', '10875'),
('7227', '10876'),
('7227', '10877'),
('7227', '10878'),

('7228', '10913'),
('7228', '10914'),
('7228', '10915'),
('7228', '10916'),
('7228', '10917'),
('7228', '10918'),
('7228', '10919'),
('7228', '10920'),
('7228', '10921'),
('7228', '10922'),
('7228', '10923'),
('7228', '10924'),
('7228', '10925'),
('7228', '10926'),
('7228', '10927'),
('7228', '10928'),
('7228', '10929'),
('7228', '10930'),
('7228', '10931'),
('7228', '10932'),
('7228', '10933'),
('7228', '10934'),
('7228', '10935'),
('7228', '10936'),
('7228', '10937'),
('7228', '10938'),
('7228', '10939'),

('72221', '10816'),
('72221', '10817'),
('72221', '10818'),
('72221', '10819'),
('72221', '10820'),
('72221', '10821'),
('72221', '10822'),
('72221', '10823'),
('72221', '10824'),
('72221', '10825'),
('72221', '10826'),
('72221', '10827'),
('72221', '10828'),
('72221', '10832'),
('72221', '10834'),
('72221', '10835'),
('72221', '10837'),
('72221', '10838'),
('72221', '10839'),
('72221', '10840'),

('72222', '10816'),
('72222', '10817'),
('72222', '10818'),
('72222', '10819'),
('72222', '10820'),
('72222', '10821'),
('72222', '10822'),
('72222', '10823'),
('72222', '10824'),
('72222', '10825'),
('72222', '10826'),
('72222', '10827'),
('72222', '10828'),
('72222', '10829'),
('72222', '10830'),
('72222', '10831'),
('72222', '10832'),
('72222', '10833'),
('72222', '10834'),
('72222', '10835'),
('72222', '10836'),
('72222', '10837'),
('72222', '10838'),
('72222', '10839'),
('72222', '10840'),
('72222', '10953'),
('72222', '10954'),
('72222', '10955'),
('72222', '10956'),
('72222', '10957'),
('72222', '10958'),
('72222', '10959'),
('72222', '10960'),
('72222', '10961'),

('72223', '10942'),
('72223', '10943'),
('72223', '10944'),
('72223', '10945'),
('72223', '10946'),
('72223', '10947'),
('72223', '10948'),
('72223', '10949'),
('72223', '10950'),
('72223', '10951'),
('72223', '10952'),

('7311', '10249'),
('7311', '10667'),
('7311', '10668'),
('7311', '10669'),
('7311', '10671'),
('7311', '10673'),
('7311', '10674'),
('7311', '10678'),
('7311', '10679'),

('7312', '10249'),
('7312', '10667'),
('7312', '10668'),
('7312', '10669'),
('7312', '10670'),
('7312', '10671'),
('7312', '10672'),
('7312', '10673'),
('7312', '10674'),
('7312', '10675'),
('7312', '10676'),
('7312', '10677'),
('7312', '10678'),
('7312', '10679'),

('7321', '10398'),
('7321', '10400'),
('7321', '10401'),
('7321', '10402'),

('7322', '10398'),
('7322', '10399'),
('7322', '10400'),
('7322', '10401'),
('7322', '10402'),
('7322', '10403'),
('7322', '10404'),

('741', '10257'),
('741', '10328'),
('741', '10330'),
('741', '10331'),
('741', '10334'),
('741', '10337'),
('741', '10582'),
('741', '10584'),

('742', '10257'),
('742', '10328'),
('742', '10329'),
('742', '10330'),
('742', '10331'),
('742', '10332'),
('742', '10333'),
('742', '10334'),
('742', '10335'),
('742', '10336'),
('742', '10337'),
('742', '10582'),
('742', '10583'),
('742', '10584'),
('742', '10585'),
('742', '10586'),

('79', '10293'),
('79', '10294'),
('79', '10296'),
('79', '10297'),
('79', '10298'),
('79', '10299'),
('79', '10300'),
('79', '10301'),
('79', '10302'),
('79', '10303'),
('79', '10304'),

('7511', '10252'),
('7511', '10253'),
('7511', '10255'),
('7511', '10338'),
('7511', '10339'),
('7511', '10340'),
('7511', '10341'),
('7511', '10344'),
('7511', '10349'),
('7511', '10354'),
('7511', '10356'),

('7512', '10252'),
('7512', '10253'),
('7512', '10338'),
('7512', '10339'),
('7512', '10340'),
('7512', '10341'),
('7512', '10342'),
('7512', '10343'),
('7512', '10344'),
('7512', '10345'),
('7512', '10346'),
('7512', '10347'),
('7512', '10348'),
('7512', '10349'),
('7512', '10350'),
('7512', '10351'),
('7512', '10352'),
('7512', '10353'),
('7512', '10354'),
('7512', '10355'),
('7512', '10356'),
('7512', '10357'),

('7521', '10252'),
('7521', '10253'),
('7521', '10255'),
('7521', '10257'),
('7521', '10262'),
('7521', '10263'),
('7521', '10339'),
('7521', '10340'),
('7521', '10341'),
('7521', '10344'),
('7521', '10349'),

('7522', '10252'),
('7522', '10253'),
('7522', '10255'),
('7522', '10257'),
('7522', '10262'),
('7522', '10263'),
('7522', '10339'),
('7522', '10340'),
('7522', '10341'),
('7522', '10342'),
('7522', '10343'),
('7522', '10344'),
('7522', '10345'),
('7522', '10346'),
('7522', '10347'),
('7522', '10348'),
('7522', '10349'),
('7522', '10350'),
('7522', '10351'),
('7522', '10352'),

('7611', '10215'),
('7611', '10216'),
('7611', '10217'),
('7611', '10218'),
('7611', '10223'),
('7611', '10224'),
('7611', '10226'),
('7611', '10230'),
('7611', '10231'),
('7611', '10236'),
('7611', '10237'),
('7611', '10252'),
('7611', '10253'),
('7611', '10260'),

('7612', '10215'),
('7612', '10216'),
('7612', '10217'),
('7612', '10218'),
('7612', '10219'),
('7612', '10220'),
('7612', '10221'),
('7612', '10222'),
('7612', '10223'),
('7612', '10224'),
('7612', '10225'),
('7612', '10226'),
('7612', '10227'),
('7612', '10228'),
('7612', '10229'),
('7612', '10230'),
('7612', '10231'),
('7612', '10232'),
('7612', '10233'),
('7612', '10234'),
('7612', '10235'),
('7612', '10236'),
('7612', '10237'),
('7612', '10238'),
('7612', '10239'),
('7612', '10240'),
('7612', '10241'),
('7612', '10242'),
('7612', '10252'),
('7612', '10253'),
('7612', '10260'),
('7612', '11215'),

('7621', '10216'),
('7621', '10217'),
('7621', '10218'),
('7621', '10223'),
('7621', '10226'),
('7621', '10230'),
('7621', '10231'),
('7621', '10252'),

('7622', '10216'),
('7622', '10217'),
('7622', '10218'),
('7622', '10219'),
('7622', '10220'),
('7622', '10221'),
('7622', '10222'),
('7622', '10223'),
('7622', '10224'),
('7622', '10225'),
('7622', '10226'),
('7622', '10227'),
('7622', '10228'),
('7622', '10229'),
('7622', '10230'),
('7622', '10231'),
('7622', '10232'),
('7622', '10233'),
('7622', '10234'),
('7622', '10235'),
('7622', '10252'),
('7622', '11215'),

('771', '10252'),
('771', '10253'),
('771', '10257'),
('771', '10260'),
('771', '11147'),
('771', '11151'),
('771', '11154'),

('772', '10252'),
('772', '10253'),
('772', '10257'),
('772', '10260'),
('772', '11147'),
('772', '11148'),
('772', '11149'),
('772', '11150'),
('772', '11151'),
('772', '11152'),
('772', '11153'),
('772', '11154'),
('772', '11155'),
('772', '11156'),

('911', '10252'),
('911', '10257'),
('911', '10260'),
('911', '10261'),
('911', '10406'),
('911', '10411'),
('911', '10413'),
('911', '10415'),
('911', '10416'),
('911', '10419'),
('911', '10421'),
('911', '10424'),
('911', '10427'),
('911', '10430'),
('911', '10432'),
('911', '10437'),
('911', '10440'),
('911', '10441'),
('911', '10445'),
('911', '10448'),
('911', '10450'),
('911', '10505'),
('911', '10507'),
('911', '10509'),
('911', '10511'),
('911', '10515'),
('911', '10522'),
('911', '10530'),
('911', '10531'),
('911', '10532'),
('911', '10535'),
('911', '10537'),
('911', '10542'),
('911', '10543'),
('911', '10548'),
('911', '10549'),
('911', '10550'),
('911', '10552'),
('911', '10554'),
('911', '10555'),
('911', '10560'),
('911', '10563'),
('911', '10565'),
('911', '10567'),
('911', '10568'),
('911', '10573'),
('911', '10574'),
('911', '10700'),
('911', '10705'),
('911', '10707'),
('911', '10709'),
('911', '10710'),
('911', '10713'),
('911', '10743'),
('911', '10747'),
('911', '10749'),

('912', '10252'),
('912', '10257'),
('912', '10260'),
('912', '10261'),
('912', '10405'),
('912', '10406'),
('912', '10407'),
('912', '10408'),
('912', '10409'),
('912', '10410'),
('912', '10411'),
('912', '10412'),
('912', '10413'),
('912', '10414'),
('912', '10415'),
('912', '10416'),
('912', '10417'),
('912', '10418'),
('912', '10419'),
('912', '10420'),
('912', '10421'),
('912', '10422'),
('912', '10423'),
('912', '10424'),
('912', '10425'),
('912', '10426'),
('912', '10427'),
('912', '10428'),
('912', '10429'),
('912', '10430'),
('912', '10431'),
('912', '10432'),
('912', '10433'),
('912', '10434'),
('912', '10435'),
('912', '10436'),
('912', '10437'),
('912', '10438'),
('912', '10439'),
('912', '10440'),
('912', '10441'),
('912', '10442'),
('912', '10443'),
('912', '10444'),
('912', '10445'),
('912', '10446'),
('912', '10447'),
('912', '10448'),
('912', '10449'),
('912', '10450'),
('912', '10451'),
('912', '10452'),
('912', '10453'),
('912', '10505'),
('912', '10506'),
('912', '10507'),
('912', '10508'),
('912', '10509'),
('912', '10510'),
('912', '10511'),
('912', '10512'),
('912', '10513'),
('912', '10514'),
('912', '10515'),
('912', '10516'),
('912', '10522'),
('912', '10523'),
('912', '10524'),
('912', '10525'),
('912', '10526'),
('912', '10527'),
('912', '10528'),
('912', '10529'),
('912', '10530'),
('912', '10531'),
('912', '10532'),
('912', '10533'),
('912', '10534'),
('912', '10535'),
('912', '10536'),
('912', '10537'),
('912', '10538'),
('912', '10539'),
('912', '10540'),
('912', '10541'),
('912', '10542'),
('912', '10543'),
('912', '10544'),
('912', '10545'),
('912', '10546'),
('912', '10547'),
('912', '10548'),
('912', '10549'),
('912', '10550'),
('912', '10551'),
('912', '10552'),
('912', '10553'),
('912', '10554'),
('912', '10555'),
('912', '10556'),
('912', '10557'),
('912', '10558'),
('912', '10559'),
('912', '10560'),
('912', '10561'),
('912', '10562'),
('912', '10563'),
('912', '10564'),
('912', '10565'),
('912', '10566'),
('912', '10567'),
('912', '10568'),
('912', '10569'),
('912', '10570'),
('912', '10571'),
('912', '10572'),
('912', '10573'),
('912', '10574'),
('912', '10575'),
('912', '10576'),
('912', '10699'),
('912', '10700'),
('912', '10701'),
('912', '10702'),
('912', '10703'),
('912', '10704'),
('912', '10705'),
('912', '10706'),
('912', '10707'),
('912', '10708'),
('912', '10709'),
('912', '10710'),
('912', '10711'),
('912', '10712'),
('912', '10713'),
('912', '10714'),
('912', '10743'),
('912', '10744'),
('912', '10745'),
('912', '10746'),
('912', '10747'),
('912', '10748'),
('912', '10749'),
('912', '10750'),
('912', '10751'),

('92', '10587'),
('92', '10588'),
('92', '10589'),
('92', '10590'),
('92', '10591'),
('92', '10592'),
('92', '10593'),
('92', '10594'),
('92', '10752'),
('92', '10753'),
('92', '10754'),
('92', '10755'),
('92', '10756'),
('92', '10757'),
('92', '10758'),
('92', '10759'),
('92', '10760'),

('931', '10577'),
('931', '10578'),
('931', '11157'),
('931', '11158'),
('931', '11159'),
('931', '11160'),
('931', '11166'),

('932', '10577'),
('932', '10578'),
('932', '10579'),
('932', '10580'),
('932', '10581'),
('932', '11157'),
('932', '11158'),
('932', '11159'),
('932', '11160'),
('932', '11161'),
('932', '11162'),
('932', '11163'),
('932', '11164'),
('932', '11165'),
('932', '11166'),
('932', '11167'),
('932', '11168'),
('932', '11169'),

('95', '10209'),
('95', '10210'),
('95', '10211'),
('95', '10212'),
('95', '10213'),
('95', '10214'),

('96', '10253'),
('96', '10257'),
('96', '10262'),
('96', '10263'),
('96', '10391'),
('96', '10392'),
('96', '10393'),
('96', '10394'),
('96', '10395'),
('96', '10396'),
('96', '10397'),
('96', '10680'),
('96', '10681'),
('96', '10682'),
('96', '10683'),
('96', '10684'),
('96', '10685'),
('96', '10686'),
('96', '10687'),
('96', '10688'),
('96', '10689'),
('96', '10690'),
('96', '10691'),
('96', '10692'),
('96', '10693'),
('96', '10694'),
('96', '10695'),
('96', '10696'),
('96', '10697'),
('96', '10698'),

('1011', '10252'),
('1011', '10367'),
('1011', '10369'),
('1011', '10370'),
('1011', '10371'),
('1011', '10372'),
('1011', '10373'),
('1011', '10374'),
('1011', '10376'),
('1011', '10380'),
('1011', '10382'),
('1011', '10384'),
('1011', '10388'),
('1011', '10389'),
('1011', '10475'),
('1011', '11433'),

('1012', '10252'),
('1012', '10367'),
('1012', '10368'),
('1012', '10369'),
('1012', '10370'),
('1012', '10371'),
('1012', '10372'),
('1012', '10373'),
('1012', '10374'),
('1012', '10375'),
('1012', '10376'),
('1012', '10377'),
('1012', '10378'),
('1012', '10379'),
('1012', '10380'),
('1012', '10381'),
('1012', '10382'),
('1012', '10383'),
('1012', '10384'),
('1012', '10385'),
('1012', '10386'),
('1012', '10387'),
('1012', '10388'),
('1012', '10389'),
('1012', '10390'),
('1012', '10474'),
('1012', '10475'),
('1012', '11433'),

('1021', '10475'),
('1021', '10595'),
('1021', '10597'),
('1021', '10598'),
('1021', '10600'),
('1021', '10602'),
('1021', '10604'),
('1021', '10607'),
('1021', '10609'),
('1021', '10612'),
('1021', '10614'),
('1021', '10617'),
('1021', '10619'),
('1021', '10622'),
('1021', '10625'),

('1022', '10474'),
('1022', '10475'),
('1022', '10595'),
('1022', '10596'),
('1022', '10597'),
('1022', '10598'),
('1022', '10599'),
('1022', '10600'),
('1022', '10601'),
('1022', '10602'),
('1022', '10603'),
('1022', '10604'),
('1022', '10605'),
('1022', '10606'),
('1022', '10607'),
('1022', '10608'),
('1022', '10609'),
('1022', '10610'),
('1022', '10611'),
('1022', '10612'),
('1022', '10613'),
('1022', '10614'),
('1022', '10615'),
('1022', '10616'),
('1022', '10617'),
('1022', '10618'),
('1022', '10619'),
('1022', '10620'),
('1022', '10621'),
('1022', '10622'),
('1022', '10623'),
('1022', '10624'),
('1022', '10625'),
('1022', '10626'),
('1022', '10627'),
('1022', '10628'),

('10511', '10257'),
('10511', '10659'),
('10511', '10661'),

('10512', '10257'),
('10512', '10659'),
('10512', '10660'),
('10512', '10661'),

('10521', '10257'),
('10521', '10662'),
('10521', '10664'),

('10522', '10257'),
('10522', '10662'),
('10522', '10663'),
('10522', '10664'),


('801', '10257'),
('801', '10262'),
('801', '10263'),
('801', '10384'),
('801', '10388'),
('801', '10454'),
('801', '10455'),
('801', '10456'),
('801', '10457'),
('801', '10458'),
('801', '10459'),
('801', '10460'),
('801', '10461'),
('801', '10462'),
('801', '10463'),
('801', '10464'),
('801', '10465'),
('801', '10466'),
('801', '10467'),
('801', '10468'),
('801', '10469'),
('801', '10470'),
('801', '10471'),
('801', '10472'),
('801', '10473'),
('801', '10475'),
('801', '10476'),
('801', '10477'),
('801', '10478'),
('801', '10479'),
('801', '10480'),
('801', '10481'),
('801', '10482'),
('801', '10483'),
('801', '10484'),
('801', '10485'),
('801', '10486'),
('801', '10487'),
('801', '10488'),
('801', '10489'),
('801', '10490'),
('801', '10491'),
('801', '10492'),
('801', '10493'),
('801', '10494'),
('801', '10495'),
('801', '10496'),
('801', '10497'),
('801', '10498'),
('801', '10499'),
('801', '10500'),
('801', '10501'),
('801', '10502'),
('801', '10503'),
('801', '10504'),
('801', '10505'),
('801', '10506'),
('801', '10507'),
('801', '10508'),
('801', '10509'),
('801', '10510'),
('801', '10511'),
('801', '10512'),
('801', '10513'),
('801', '10514'),
('801', '10515'),
('801', '10516'),
('801', '10517'),
('801', '10518'),
('801', '10519'),
('801', '10520'),
('801', '10521'),
('801', '11170'),
('801', '11175'),
('801', '11180'),
('801', '11181'),
('801', '11183'),
('801', '11189'),
('801', '11193'),
('801', '11417'),
('801', '11418'),
('801', '11419'),
('801', '11420'),
('801', '11421'),
('801', '11422'),
('801', '11423'),
('801', '11424'),
('801', '11425'),
('801', '11427'),
('801', '11428'),
('801', '11429'),
('801', '11431'),
('801', '11432'),
('801', '11433'),
('801', '11434'),
('801', '11435'),
('801', '11439'),
('801', '11442'),
('801', '11443'),
('801', '11444'),
('801', '11445'),
('801', '11446'),
('801', '11447'),
('801', '11449'),
('801', '11451'),
('801', '11452'),
('801', '11453'),
('801', '11454'),
('801', '11455'),
('801', '11456'),
('801', '11457'),

('802', '10257'),
('802', '10262'),
('802', '10263'),
('802', '10383'),
('802', '10384'),
('802', '10388'),
('802', '10454'),
('802', '10455'),
('802', '10456'),
('802', '10457'),
('802', '10458'),
('802', '10459'),
('802', '10460'),
('802', '10461'),
('802', '10462'),
('802', '10463'),
('802', '10464'),
('802', '10465'),
('802', '10466'),
('802', '10467'),
('802', '10468'),
('802', '10469'),
('802', '10470'),
('802', '10471'),
('802', '10472'),
('802', '10473'),
('802', '10474'),
('802', '10475'),
('802', '10476'),
('802', '10477'),
('802', '10478'),
('802', '10479'),
('802', '10480'),
('802', '10481'),
('802', '10482'),
('802', '10483'),
('802', '10484'),
('802', '10485'),
('802', '10486'),
('802', '10487'),
('802', '10488'),
('802', '10489'),
('802', '10490'),
('802', '10491'),
('802', '10492'),
('802', '10493'),
('802', '10494'),
('802', '10495'),
('802', '10496'),
('802', '10497'),
('802', '10498'),
('802', '10499'),
('802', '10500'),
('802', '10501'),
('802', '10502'),
('802', '10503'),
('802', '10504'),
('802', '10505'),
('802', '10506'),
('802', '10507'),
('802', '10508'),
('802', '10509'),
('802', '10510'),
('802', '10511'),
('802', '10512'),
('802', '10513'),
('802', '10514'),
('802', '10515'),
('802', '10516'),
('802', '10517'),
('802', '10518'),
('802', '10519'),
('802', '10520'),
('802', '10521'),
('802', '11170'),
('802', '11175'),
('802', '11176'),
('802', '11179'),
('802', '11180'),
('802', '11181'),
('802', '11182'),
('802', '11183'),
('802', '11189'),
('802', '11190'),
('802', '11193'),
('802', '11413'),
('802', '11414'),
('802', '11415'),
('802', '11416'),
('802', '11417'),
('802', '11418'),
('802', '11419'),
('802', '11420'),
('802', '11421'),
('802', '11422'),
('802', '11423'),
('802', '11424'),
('802', '11425'),
('802', '11426'),
('802', '11427'),
('802', '11428'),
('802', '11429'),
('802', '11430'),
('802', '11431'),
('802', '11432'),
('802', '11433'),
('802', '11434'),
('802', '11435'),
('802', '11436'),
('802', '11437'),
('802', '11438'),
('802', '11439'),
('802', '11440'),
('802', '11441'),
('802', '11442'),
('802', '11443'),
('802', '11444'),
('802', '11445'),
('802', '11446'),
('802', '11447'),
('802', '11448'),
('802', '11449'),
('802', '11450'),
('802', '11451'),
('802', '11452'),
('802', '11453'),
('802', '11454'),
('802', '11455'),
('802', '11456'),
('802', '11457'),

('811', '10253'),
('811', '10262'),
('811', '10263'),
('811', '10761'),
('811', '10763'),
('811', '10764'),
('811', '10765'),
('811', '10767'),
('811', '10768'),
('811', '10769'),
('811', '10770'),
('811', '10771'),
('811', '10772'),
('811', '10775'),
('811', '10776'),
('811', '10779'),
('811', '10780'),

('812', '10253'),
('812', '10262'),
('812', '10263'),
('812', '10761'),
('812', '10762'),
('812', '10763'),
('812', '10764'),
('812', '10765'),
('812', '10766'),
('812', '10767'),
('812', '10768'),
('812', '10769'),
('812', '10770'),
('812', '10771'),
('812', '10772'),
('812', '10773'),
('812', '10774'),
('812', '10775'),
('812', '10776'),
('812', '10777'),
('812', '10778'),
('812', '10779'),
('812', '10780');
