DROP TABLE IF EXISTS pax_terminal_uninstall_opt;
CREATE TABLE `pax_terminal_uninstall_opt`
(
    `id`             int(11) NOT NULL COMMENT '编号',
    `app_name`       varchar(128) DEFAULT NULL COMMENT '应用名',
    `icon_md5`       varchar(32)  DEFAULT NULL,
    `package_name`   varchar(128) DEFAULT NULL COMMENT '包名',
    `version_name`   varchar(255) DEFAULT NULL,
    `created_by`     int(11)      DEFAULT NULL COMMENT '创建者',
    `created_date`   datetime     DEFAULT NULL COMMENT '时间',
    PRIMARY KEY (`id`)
) COMMENT ='终端流量表';

DELETE FROM PAX_TERMINAL_ACTION WHERE action_type = 5;
DELETE FROM PAX_TERMINAL_ACTION_HISTORY WHERE action_type = 5;