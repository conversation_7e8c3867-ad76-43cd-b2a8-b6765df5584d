DROP TABLE IF EXISTS `pax_airlink_order`;
CREATE TABLE `pax_airlink_order` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `market_id` INT(11) NOT NULL COMMENT '市场id',
  `order_number` VARCHAR(32) NOT NULL COMMENT '订单编号',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
  `remain_balance` DECIMAL(10,2) DEFAULT NULL COMMENT '当前余额',
  `order_type` CHAR(1) NOT NULL COMMENT '订单类型 R:Recharge D:Deduction',
  `deduct_type` CHAR(1) DEFAULT NULL COMMENT '扣款类型 R:Recharge I:Import T:Traffic_over M:Monthly',
  `payment_method` CHAR(1) NOT NULL COMMENT '支付方式 O:Offline B:Balance',
  `payment_time` DATETIME NOT NULL COMMENT '支付时间',
  `period` VARCHAR(10) NOT NULL COMMENT '订单周期',
  `charge_code` VARCHAR(64) DEFAULT NULL COMMENT '汇款码',
  `status` CHAR(1) DEFAULT NULL COMMENT '状态',
  `remarks` TEXT DEFAULT NULL COMMENT '备注',
  `created_date` DATETIME NOT NULL COMMENT '活动创建者',
  `created_by` INT(11) NOT NULL COMMENT '活动创建时间',
  `updated_date` DATETIME NOT NULL COMMENT '活动更新时间',
  `updated_by` INT(11) NOT NULL COMMENT '活动更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  INDEX `IDX_MARKET_PERIOD` (`market_id`, `period`)
) COMMENT='AirLink订单表';

DROP TABLE IF EXISTS `pax_airlink_order_item`;
CREATE TABLE `pax_airlink_order_item` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `order_id` INT(11) NOT NULL COMMENT '订单id',
  `type` CHAR(1) NOT NULL COMMENT '类型 M:终端包月 O:超额流量 D:码号下载',
  `quantity` BIGINT UNSIGNED DEFAULT NULL COMMENT '终端数量/超额流量取整数量',
  `traffic_overage` double DEFAULT NULL COMMENT '超额流量',
  `price` DECIMAL(10,2) NOT NULL COMMENT '包月价格/超额流量价格',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '付款金额',
  PRIMARY KEY (`id`)
) COMMENT='AirLink订单条目表';

DROP TABLE IF EXISTS `pax_airlink_order_payment_voucher`;
CREATE TABLE `pax_airlink_order_payment_voucher` (
 `id` INT(11) NOT NULL AUTO_INCREMENT,
 `order_id` INT(11) NOT NULL COMMENT '订单id',
 `file_id` VARCHAR(255) NOT NULL COMMENT '附件下载地址',
 PRIMARY KEY (`id`)
) COMMENT='AirLink付款凭证表';

