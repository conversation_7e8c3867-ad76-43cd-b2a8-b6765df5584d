UPDATE `pax_code` SET label = '固件分布-饼图' WHERE lang = 'zh_CN' AND `value` = 'W_FT';
UPDATE `pax_code` SET label = 'Firmware Distribution-Pie Chart' WHERE lang = 'en' AND `value` = 'W_FT';
UPDATE `pax_code` SET label = '导出PAXSTORE客户端分布-饼图' WHERE lang = 'zh_CN' AND `value` = 'W_CT';
UPDATE `pax_code` SET label = 'Export PAXSTORE Client Distribution-Pie Chart' WHERE lang = 'en' AND `value` = 'W_CT';

INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`)
VALUES (-1, 0, 'zh_CN', 'ET_EN', '导出接入终端', 'activity_type', NULL, 29, NULL, -1, '2020-02-26 11:25:34', -1, '2020-02-26 11:25:46', '0', 1, -1);

INSERT INTO `pax_code`(`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`)
VALUES (-1, 0, 'en', 'ET_EN', 'Export Enrolled Terminals', 'activity_type', NULL, 29, NULL, -1, '2020-02-26 11:25:34', -1, '2020-02-26 11:25:46', '0', 1, -1);

