ALTER TABLE pax_market_billing_setting_terminal_enroll CHANGE `unit_price` `pax_android_price` DECIMAL ( 10, 2 ) NOT NULL COMMENT 'PAX智能终端价格';
ALTER TABLE pax_market_billing_setting_terminal_enroll ADD COLUMN `pax_traditional_price` DECIMAL ( 10, 2 ) DEFAULT NULL COMMENT 'PAX传统终端价格' AFTER pax_android_price;
ALTER TABLE pax_market_billing_setting_terminal_enroll ADD COLUMN `other_android_price` DECIMAL ( 10, 2 ) DEFAULT NULL COMMENT '其他厂商智能终端价格' AFTER pax_traditional_price;
ALTER TABLE pax_market_billing_setting_terminal_enroll ADD COLUMN `other_traditional_price` DECIMAL ( 10, 2 ) DEFAULT NULL COMMENT '其他厂商传统终端价格' AFTER other_android_price;

DELETE FROM pax_market_billing_setting_terminal_enroll_ref_price WHERE bill_type=1;

INSERT INTO `pax_market_billing_setting_terminal_enroll_ref_price` (`bill_type`, `start_range`, `end_range`, `pax_android_price`,`pax_traditional_price`,`other_android_price`,`other_traditional_price`, `updated_date`) VALUES (1, 0, 1000, 0.64,0.16,0.96,0.24, CURRENT_TIMESTAMP);
INSERT INTO `pax_market_billing_setting_terminal_enroll_ref_price` (`bill_type`, `start_range`, `end_range`, `pax_android_price`,`pax_traditional_price`,`other_android_price`,`other_traditional_price`, `updated_date`) VALUES (1, 1001, 5000, 0.56,0.14,0.84,0.21, CURRENT_TIMESTAMP);
INSERT INTO `pax_market_billing_setting_terminal_enroll_ref_price` (`bill_type`, `start_range`, `end_range`, `pax_android_price`,`pax_traditional_price`,`other_android_price`,`other_traditional_price`, `updated_date`) VALUES (1, 5001, 20000, 0.48,0.12,0.72,0.18, CURRENT_TIMESTAMP);
INSERT INTO `pax_market_billing_setting_terminal_enroll_ref_price` (`bill_type`, `start_range`, `end_range`, `pax_android_price`,`pax_traditional_price`,`other_android_price`,`other_traditional_price`, `updated_date`) VALUES (1, 20001, 50000, 0.40,0.10,0.60,0.15, CURRENT_TIMESTAMP);
INSERT INTO `pax_market_billing_setting_terminal_enroll_ref_price` (`bill_type`, `start_range`, `end_range`, `pax_android_price`,`pax_traditional_price`,`other_android_price`,`other_traditional_price`, `updated_date`) VALUES (1, 50001, 100000, 0.32,0.08,0.48,0.12, CURRENT_TIMESTAMP);
INSERT INTO `pax_market_billing_setting_terminal_enroll_ref_price` (`bill_type`, `start_range`, `end_range`, `pax_android_price`,`pax_traditional_price`,`other_android_price`,`other_traditional_price`, `updated_date`) VALUES (1, 100001, -1, 0.24,0.06,0.36,0.09, CURRENT_TIMESTAMP);

UPDATE pax_market_billing_setting_terminal_enroll_ref_price set pax_android_price = 0.1 WHERE bill_type=3;

ALTER TABLE pax_market_billing_setting_terminal_enroll RENAME TO pax_market_billing_setting_price;