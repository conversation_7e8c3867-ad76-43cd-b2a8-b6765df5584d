DROP TABLE IF EXISTS `PAX_MARKET_3RD_SYS_IP`;
CREATE TABLE `PAX_MARKET_3RD_SYS_IP`
(
    id          INT NOT NULL AUTO_INCREMENT COMMENT '编号',
    market_id   INT NOT NULL COMMENT '市场id',
    reseller_id INT NOT NULL COMMENT '代理商id',
    ip          INT UNSIGNED NOT NULL COMMENT '第三方系统绑定的ip',
    description VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX  `idx_market_reseller`(`market_id`,`reseller_id`) USING BTREE
) COMMENT='应用市场第三方业务系统绑定IP表' AUTO_INCREMENT=1000000000;
