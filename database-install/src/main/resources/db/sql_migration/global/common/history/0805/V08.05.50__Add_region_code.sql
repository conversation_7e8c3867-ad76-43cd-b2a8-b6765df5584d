DELETE FROM PAX_CODE WHERE `type` = 'region';
INSERT INTO PAX_CODE (`market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
('-1', null, 'en', 'EMEA', 'EMEA', 'region', 'EMEA', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
('-1', null, 'en', 'LACIS', 'LACIS', 'region', 'LACIS', '1', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
('-1', null, 'en', 'APAC', 'APAC', 'region', 'APAC', '2', null, '-1', NOW(), '1', NOW(), '0', '1', '-1');

ALTER TABLE PAX_MARKET ADD COLUMN region varchar(64) DEFAULT NULL COMMENT '大区' AFTER in_production;
ALTER TABLE PAX_MARKET ADD COLUMN country varchar(64) DEFAULT NULL COMMENT '国家' AFTER region;
ALTER TABLE PAX_MARKET ADD COLUMN billing_owner varchar(64) DEFAULT NULL COMMENT '账单所有者' AFTER country;


