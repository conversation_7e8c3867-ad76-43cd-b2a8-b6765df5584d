DROP TABLE IF EXISTS `pax_app_setting`;
CREATE TABLE `pax_app_setting` (
    `app_id` int(11) NOT NULL COMMENT '应用编号',
    `sync_biz_data_status` varchar(1) NULL DEFAULT NULL COMMENT '是否允许上送业务数据的状态',
    `msg_service_enabled` INT(1) NULL DEFAULT NULL COMMENT '应用是否开启第三方系统消息服务',
    `created_by` int(11) DEFAULT NULL,
    `created_date` datetime DEFAULT NULL,
    `updated_by` int(11) DEFAULT NULL,
    `updated_date` datetime DEFAULT NULL,
    PRIMARY KEY (`app_id`),
    CONSTRAINT `FK_APP_ST_APP_ID` FOREIGN KEY (`app_id`) REFERENCES `pax_app` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) COMMENT='应用配置表';