DROP TABLE IF EXISTS `PAX_TERMINAL_MASTER_KEY`;
CREATE TABLE `PAX_TERMINAL_MASTER_KEY` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `terminal_id` int(11) NOT NULL COMMENT '终端编号',
  `serial_no` varchar(32) NOT NULL COMMENT '终端SN',
  `key_slot` int(3) NOT NULL COMMENT '密钥槽',
  `kcv` varchar(16) NOT NULL COMMENT '密钥校验值',
  `task_id` varchar(64) NOT NULL COMMENT '任务标识',
  `opt_reseller` int(11) NOT NULL COMMENT '代理商编号',
  `created_by` int(11) NOT NULL COMMENT '创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) COMMENT='终端主密钥导入记录表';