DROP TABLE IF EXISTS `pax_ad_group_item`;

CREATE TABLE `pax_ad_group_item`
(
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `market_id` bigint(20) NOT NULL COMMENT '市场id',
    `ad_group_id` int NOT NULL COMMENT '广告组id',
    `status` char(2) NOT NULL COMMENT '广告组状态',
    `model_id` int NOT NULL COMMENT '机型编号',
    `reseller_id` int NOT NULL COMMENT '代理商编号',
    `ad_visual_ids` varchar(255) COMMENT '广告素材id链(多个，逗号隔开)',
    PRIMARY KEY (`id`),
    KEY `IDX_MARKET_MODEL_RESELLER` (`market_id`, `model_id`, `reseller_id`)
) COMMENT = '广告组明细表';