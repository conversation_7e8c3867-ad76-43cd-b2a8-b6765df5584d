ALTER TABLE `pax_rki_batch_bind_record` ADD COLUMN `token` VARCHAR(16) NULL DEFAULT NULL COMMENT 'RKI User Token' AFTER `key_id`;

ALTER TABLE `pax_rki_server` CHANGE COLUMN `url` `batch_bind_url` varchar(64) NOT NULL COMMENT 'Batch Bind API服务器地址';
ALTER TABLE `pax_rki_server` ADD COLUMN `pull_key_url` varchar(64) NULL DEFAULT NULL COMMENT 'Pull Key API服务器地址' AFTER `batch_bind_url`;

DROP TABLE IF EXISTS `pax_reseller_rki`;
CREATE TABLE `pax_reseller_rki` (
  `reseller_id` int(11) NOT NULL COMMENT 'reseller编号',
  `rki_user_token` varchar(64) NOT NULL COMMENT 'RKI用户token',
  `allow_child_use` INT(1) NOT NULL DEFAULT 0 COMMENT '是否允许子代理商使用',
  PRIMARY KEY (`reseller_id`,`rki_user_token`),
  CONSTRAINT `fk_reseller_rki_reseller_id` FOREIGN KEY (`reseller_id`) REFERENCES `pax_reseller` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) COMMENT='代理商RKI配置表';

DROP TABLE IF EXISTS `pax_rki_pull_keys_record`;
CREATE TABLE `pax_rki_pull_keys_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
	`market_id` int(11) NOT NULL COMMENT '应用市场编号',
	`reseller_id` int(11) NOT NULL COMMENT '代理商编号',
  `rki_id` int(11) NOT NULL COMMENT 'RKI服务器编号',
  `task_id` varchar(64) NOT NULL COMMENT '请求唯一标识',
  `rki_user_token` varchar(64) NOT NULL COMMENT 'RKI用户token',
  `req_date` datetime DEFAULT NULL COMMENT '请求时间',
  `res_result` varchar(64) DEFAULT NULL COMMENT 'Response结果',
  `res_msg` varchar(128) DEFAULT NULL COMMENT 'Response消息',
  `created_by` int(11) NOT NULL COMMENT '创建者',
  `created_date` datetime NOT NULL COMMENT '创建时间',
  `updated_by` int(11) NOT NULL COMMENT '更新者',
  `updated_date` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) COMMENT='应用市场RKI_API_PULL_KEY记录表' AUTO_INCREMENT=1000000000;

DROP TABLE IF EXISTS `pax_rki_key`;
CREATE TABLE `pax_rki_key` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `market_id` int(11) NOT NULL COMMENT '应用市场编号',
  `reseller_id` int(11) NOT NULL COMMENT '代理商编号',
  `key_id` varchar(64) NOT NULL COMMENT '密钥模板标识',
  `key_name` varchar(64) NOT NULL COMMENT '密钥模板名称',
  `rki_customer` varchar(64) NOT NULL COMMENT 'RKI客户',
  `note` varchar(64) NOT NULL COMMENT '密钥模板备注',
  `sync_date` datetime NOT NULL COMMENT '同步时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_pax_rki_key_id` (`market_id`,`reseller_id`,`key_id`)
) COMMENT='代理商RKI服务密钥模板Key表' AUTO_INCREMENT=1000000000;

ALTER TABLE `pax_model` ADD COLUMN `factory_model_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '工程名' AFTER `name`;