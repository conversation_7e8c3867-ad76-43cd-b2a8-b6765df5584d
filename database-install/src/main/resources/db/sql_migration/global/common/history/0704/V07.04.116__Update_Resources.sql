DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
('10000', 'Create push message to terminal request', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10001', 'Get arrive rate of message.', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10002', 'Submit Apk', '/v1/3rd/developer/apk/upload', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10003', '批量更新参数下载的操作状态', '/v1/3rdApps/actions', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
('10004', '更新参数下载的操作状态', '/v1/3rdApps/actions/{actionId}/status', 'PUT', null, '1', '1', '1', NOW(), '1', NOW()),
('10005', '同步终端商业数据', '/v1/3rdApps/bizData', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
('10006', '终端应用查询数据', '/v1/3rdApps/goInsight/data/querying/{queryCode}', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
('10007', '终端应用上送数据', '/v1/3rdApps/goInsight/data/send', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
('10008', '第三方应用同步信息', '/v1/3rdApps/info', 'POST', null, '1', '1', '1', NOW(), '1', NOW()),
('10009', 'TID activation terminal', '/v1/3rdApps/init', 'PUT', null, '1', '1', '1', NOW(), '1', NOW()),
('10010', '获取终端参数下载信息', '/v1/3rdApps/param', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
('10011', '获取应用是否存在版本更新', '/v1/3rdApps/upgrade', 'GET', null, '1', '1', '1', NOW(), '1', NOW()),
('10012', 'Find apk parameter list', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10013', 'Create Apk Parameter', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10014', 'Get apk parameter details', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10015', 'Update Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10016', 'Delete Apk Parameter', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10017', 'Search Application', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10018', 'Search entity attributes', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10019', 'Create an Entity Attribute', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10020', 'Get entity attribute by id', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10021', 'Update Entity Attribute by Id', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10022', 'Delete entity attribute by id', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10023', 'Update entity attribute label', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10024', 'Verify estate', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10025', 'Search app business data from GoInsight', '/v1/3rdsys/goInsight/data/app-biz', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10026', 'Get merchant category list', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10027', 'Create a single merchant category', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10028', 'Batch create merchant categories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10029', 'Update merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10030', 'Delete merchant category', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10031', 'Get merchant list by search criterias', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10032', 'Create a merchant', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10033', 'Get merchant by id', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10034', 'Update merchant', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10035', 'Delete a merchant', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10036', 'Activate merchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10037', 'Disable a merchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10038', 'Replace merchant email', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10039', 'Find parameter push history by page', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10040', 'Find resellers', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10041', 'Create a reseller', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10042', 'Get reseller', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10043', 'Update a reseller', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10044', 'Delete a reseller', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10045', 'Activate a reseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10046', 'Disable a reseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10047', 'Replace reseller email', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10048', 'Find reseller RKI KEY template', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10049', 'Search push app list by terminal', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10050', 'Push app (with parameter if parameter app) to terminal', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10051', 'Suspend terminal push app', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10052', 'Uninstall terminal app', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10053', 'Get Terminal Apk', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10054', 'Search push firmware list by terminal', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10055', 'Push firmware to terminal', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10056', 'Cancel push firmware task', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10057', 'Get push firmware history by id', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10058', 'Search terminal group push application list', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10059', 'Push app (with parameter if parameter app) to terminal group', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10060', 'Get terminal group push apk', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10061', 'Delete terminal group push task', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10062', 'Suspend terminal group push task by id', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10063', 'Get terminal group list', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10064', 'Create terminal group', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10065', 'Search terminals', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10066', 'Get terminal group by id', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10067', 'Update terminal group', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10068', 'Delete terminal group by id', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10069', 'Activate terminal group by id', '/v1/3rdsys/terminalGroups/{groupId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10070', 'Disable terminal group', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10071', 'Search terminals in specified group', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10072', 'Add terminal to group', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10073', 'Remove group terminals', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10074', 'Search push RKI Key task list by terminal', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10075', 'Push RKI Key to terminal', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10076', 'suspend terminal push Rki Key', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10077', 'Get Terminal RKI Key push task detail', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10078', 'Get terminal variable list', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10079', 'Create terminal parameter variables in batch', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10080', 'Batch deletion of terminal variables', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10081', 'Update terminal variable by variable id', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10082', 'Delete terminal variable by variable id', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10083', 'Find terminal list by page', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10084', 'Create terminal', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10085', 'Activate a terminal by query parameter', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10086', 'Batch add terminal to group', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10087', 'Get terminal by id', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10088', 'Update terminal', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10089', 'Delete a terminal', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10090', 'Activate a terminal', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10091', 'Get terminal configuration', '/v1/3rdsys/terminals/{terminalId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10092', 'Update terminal configuration', '/v1/3rdsys/terminals/{terminalId}/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10093', 'Disable a terminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10094', 'Move a terminal', '/v1/3rdsys/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10095', 'Push command to terminal', '/v1/3rdsys/terminals/{terminalId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10096', 'Get terminal PED information by terminal id', '/v1/3rdsys/terminals/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10097', 'Route request to Uptrillion', '/v1/3rdsys/upt/route-request', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10098', 'Set UpTrillion integration API key and secret', '/v1/3rdsys/upt/security', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10099', '查询活动列表', '/v1/activities', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10100', '批量删除活动', '/v1/activities/batch/deletion', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10101', '获取活动信息', '/v1/activities/{activityId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10102', '删除活动', '/v1/activities/{activityId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10103', '查询应用分成比例默认设置', '/v1/admin/app/download/fee/defaultSetting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10104', '更新应用分成比例认设置', '/v1/admin/app/download/fee/defaultSetting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10105', '查询子应用市场应用分成比例设置', '/v1/admin/app/download/fee/market/profit/rate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10106', '更新子应用市场应用分成比例设置', '/v1/admin/app/download/fee/market/profit/rate', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10107', '管理员查询应用列表', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10108', '创建管理员Apk下载任务', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10109', '创建管理员下载apk参数模版下载任务', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10110', '管理员删除apk参数模版', '/v1/admin/apps/apks/{apkId}/paramTemplate', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10111', '对于签名失败的apk重新签名', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10112', '管理员获取AppFeatured图片', '/v1/admin/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10113', '管理员更新AppFeatured图片排序', '/v1/admin/apps/featured/{featuredAppId}/sort', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10114', '获取白名单', '/v1/admin/apps/whiteList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10115', '创建白名单', '/v1/admin/apps/whiteList', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10116', '删除白名单', '/v1/admin/apps/whiteList', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10117', '管理员获取开发者应用详细信息', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10118', '删除应用APP', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10119', '应用上线', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10120', '管理员更新APP卸载权限', '/v1/admin/apps/{appId}/allowUninstall/{allowUninstall}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10121', '管理员获取APK列表', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10122', '管理员获取APK详情', '/v1/admin/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10123', '删除应用APK', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10124', '通过应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10125', '管理员下载APK', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10126', '查询应用APK定向发布的应用市场', '/v1/admin/apps/{appId}/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10127', '应用Apk下线', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10128', '应用Apk上线', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10129', '管理员添加参数模板', '/v1/admin/apps/{appId}/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10130', '拒绝应用审核', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10131', '管理员更新App ReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/releaseNote', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10132', '查询应用APK定向发布的代理商', '/v1/admin/apps/{appId}/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10133', 'Apk specific reseller or global APK publish to the normal market', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10134', '删除应用APK定向发布', '/v1/admin/apps/{appId}/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10135', '管理员更新APK机型', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10136', '管理员更新APP自动更新配置', '/v1/admin/apps/{appId}/autoUpdate/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10137', '更新应用的开发者', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10138', '应用下线', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10139', '更新app下载权限', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10140', '管理员添加AppFeatured图片', '/v1/admin/apps/{appId}/featured', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10141', '管理员删除AppFeatured图片', '/v1/admin/apps/{appId}/featured', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10142', '管理员更新FeaturedApp图片', '/v1/admin/apps/{appId}/featured/{featuredAppId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10143', 'Admin search insight sandbox app biz data', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10144', '创建已安装终端列表下载任务', '/v1/admin/apps/{appId}/installedTerminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10145', '查询应用定向发布的应用市场', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10146', 'Get app specific merchant categories', '/v1/admin/apps/{appId}/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10147', 'Update app specific merchant categories', '/v1/admin/apps/{appId}/merchant/categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10148', '更新应用的消息服务调用权限', '/v1/admin/apps/{appId}/msgServiceEnabled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10149', '查询应用定向发布的代理商', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10150', '应用恢复', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10151', 'App specific reseller or global APP publish to the normal market', '/v1/admin/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10152', '删除应用定向发布', '/v1/admin/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10153', '更新app可是范围', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10154', 'Refresh AccessToken via CloudServiceGateway', '/v1/admin/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10155', 'ping', '/v1/admin/cloudservice/refresh/ping', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10156', 'Get access url and token via CloudServiceGateway', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10157', '获取应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10158', '保存应用市场管理员Dashboard布局', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10159', '获取Dashboard里终端信息统计部件(W03)的数据', '/v1/admin/dashboard/widgets/W03', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10160', '获取Dashboard里终端数量部件(W09)的数据', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10161', '获取Dashboard里代理商商户终端汇总部件(W10)的数据', '/v1/admin/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10162', '获取Dashboard里代理商级别的操作日志(W11)的数据', '/v1/admin/dashboard/widgets/W11', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10163', '获取Dashboard里代理商终端汇总部件(W12)的数据', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10164', '获取Dashboard里 固件版本-终端数量-组织 (W13)的数据', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10165', '创建Dashboard里 FM-Terminal_Org(W13) 数据下载任务', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10166', '获取Dashboard里 Client-终端数量-组织 (W14)的数据', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10167', '创建Dashboard里 Client-终端数量-组织(W14) 数据下载任务', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10168', '获取Dashboard里 机型-终端数量 (W15)的数据', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10169', '创建Dashboard里 MODEL-Terminal_Org(W15) 数据下载任务', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10170', '获取Dashboard 长时间 offline (W16)的数据', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10171', '创建Dashboard里 offline-terminal(W16) 数据下载任务', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10172', '获取Dashboard里 FM-Terminal(W18) 数据', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10173', '创建Dashboard里 FM-Terminal(W18) 数据下载任务', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10174', '获取Dashboard里 Client-Terminal(W19) 数据', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10175', '创建Dashboard里 Client-Terminal(W19) 数据下载任务', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10176', '创建Dashboard里 数字卡片(W20) 数据下载任务', '/v1/admin/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10177', '获取Dashboard里 数字卡片(W20) 数量', '/v1/admin/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10178', '获取Dashboard里 数字卡片(W20) 设置', '/v1/admin/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10179', '更新Dashboard里 数字卡片(W20) 显示数据', '/v1/admin/dashboard/widgets/W20/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10180', '管理员查询开发者列表', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10181', '管理员获取开发者详细信息', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10182', '更新开发者账户', '/v1/admin/developers/{developerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10183', '删除开发者', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10184', '允许开发者访问第三方开发者API', '/v1/admin/developers/{developerId}/3rdsys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10185', '禁止开发者访问第三方开发者API', '/v1/admin/developers/{developerId}/3rdsys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10186', '通过开发者审核', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10187', '通过开发者线下付款审核', '/v1/admin/developers/{developerId}/pay/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10188', '拒绝开发者线下付款审核', '/v1/admin/developers/{developerId}/pay/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10189', '拒绝开发者审核', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10190', '定向发布开发者到代理商', '/v1/admin/developers/{developerId}/reseller', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10191', '更改定向发布开发者的代理商', '/v1/admin/developers/{developerId}/reseller/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10192', '关闭/删除开发者定向发布到代理商', '/v1/admin/developers/{developerId}/reseller/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10193', '恢复开发者帐号', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10194', '管理员获取开发者终端列表', '/v1/admin/developers/{developerId}/sandbox/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10195', '停用开发者帐号', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10196', '管理员获取企业开发者列表信息', '/v1/admin/developers/{developerId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10197', '获取Global应用市场应用列表', '/v1/admin/global/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10198', '全局应用订阅', '/v1/admin/global/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10199', '全局应用取消订阅', '/v1/admin/global/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10200', '获取Global应用市场固件列表', '/v1/admin/global/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10201', '获取Global固件发布的应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10202', '发布全球应用市场固件到其他应用市场', '/v1/admin/global/firmwares/{firmwareId}/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10203', '订阅Global应用市场的固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10204', '取消订阅Global应用市场固件', '/v1/admin/global/firmwares/{firmwareId}/subscribe', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10205', '发布Global应用市场POS Client应用到指定应用市场', '/v1/admin/global/selfApks/{selfApkId}/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10206', '获取地图当前边界内的标记', '/v1/admin/map/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10207', '获取当前环境所有终端标记', '/v1/admin/map/markers/god/perspective', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10208', '获取某地点内的POS机', '/v1/admin/map/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10209', '查询应用市场第三方系统API访问配置', '/v1/admin/market/3rdsys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10210', '允许应用市场第三方系统API访问', '/v1/admin/market/3rdsys/config/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10211', '禁止应用市场第三方系统API访问', '/v1/admin/market/3rdsys/config/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10212', '获取应用市场第三方系统API访问密钥', '/v1/admin/market/3rdsys/config/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10213', '重置应用市场第三方系统API访问密钥', '/v1/admin/market/3rdsys/config/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10214', '激活应用市场', '/v1/admin/market/activate', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
('10215', 'List all announcement notification', '/v1/admin/market/announcement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10216', 'create announcement notification', '/v1/admin/market/announcement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10217', 'get announcement notification', '/v1/admin/market/announcement/detail/{announcementId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10218', 'publish announcement notification', '/v1/admin/market/announcement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10219', 'update announcement notification', '/v1/admin/market/announcement/{announcementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10220', 'delete announcement notification', '/v1/admin/market/announcement/{announcementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10221', '查询登陆日志', '/v1/admin/market/audit/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10222', '导出登陆日志', '/v1/admin/market/audit/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10223', '查询操作日志参数详情', '/v1/admin/market/audit/operation/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10224', '查询操作日志', '/v1/admin/market/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10225', '导出操作日志', '/v1/admin/market/audit/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10226', '获取应用市场年度账单列表', '/v1/admin/market/billing', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10227', '获取应用市场收费应用下载情况统计', '/v1/admin/market/billing/app/download/fee/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10228', '下载市场应用下载的情况', '/v1/admin/market/billing/app/download/fee/app/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10229', '获取Global应用市场收费应用收入子应用市场下载列表', '/v1/admin/market/billing/app/download/fee/app/{appId}/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10230', '下载应用市场购买应用的详情', '/v1/admin/market/billing/app/download/fee/app/{appId}/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10231', '获取应用市场收费应用下载情况统计', '/v1/admin/market/billing/app/download/fee/app/{appId}/terminal/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10232', '下载终端购买应用的详情', '/v1/admin/market/billing/app/download/fee/app/{appId}/terminal/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10233', '获取应用市场收费应用的实时下载情况', '/v1/admin/market/billing/app/download/fee/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10234', '获取应用市场过去6个月收费应用的收入情况', '/v1/admin/market/billing/app/download/fee/dashboard/last6', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10235', '获取应用市场收费应用下载最多的前10名', '/v1/admin/market/billing/app/download/fee/dashboard/top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10236', '获取应用市场收费应用开发者统计', '/v1/admin/market/billing/app/download/fee/developer/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10237', '下载应用开发者统计列表', '/v1/admin/market/billing/app/download/fee/developer/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10238', '获取应用市场收费应用开发者应用统计', '/v1/admin/market/billing/app/download/fee/developer/{developerId}/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10239', '获取应用市场收费应用历史收入账单', '/v1/admin/market/billing/app/download/fee/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10240', '下载应用下载历史列表', '/v1/admin/market/billing/app/download/fee/history/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10241', '获取Global应用市场收费应用子应用市场下载统计', '/v1/admin/market/billing/app/download/fee/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10242', '下载Global应用子应用市场下载统计列表', '/v1/admin/market/billing/app/download/fee/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10243', '获取Global应用市场收费应用子应用市场下载应用统计', '/v1/admin/market/billing/app/download/fee/market/{marketId}/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10244', '下载应用市场购买详情', '/v1/admin/market/billing/app/download/fee/{marketId}/purchase/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10245', '获取Global应用市场当前收款账单详情', '/v1/admin/market/billing/current/receivable/detail/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10246', '获取Global应用市场当前收款账单', '/v1/admin/market/billing/current/receivable/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10247', '获取应用市场当前账单', '/v1/admin/market/billing/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10248', '获取Global应用市场当前收款账单', '/v1/admin/market/billing/dashboard/global', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10249', '获取应用市场过去六个月的账单', '/v1/admin/market/billing/dashboard/last6', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10250', '获取Global应用市场过去六个月的收款账单', '/v1/admin/market/billing/dashboard/receivable/last6', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10251', '查询账单默认设置', '/v1/admin/market/billing/defaultSettings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10252', '更新账单默认设置', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10253', '创建应用市场账单接收邮箱', '/v1/admin/market/billing/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10254', '获取应用市场接受账单邮箱', '/v1/admin/market/billing/email/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10255', '获取应用市场历史账单', '/v1/admin/market/billing/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10256', '获取应用市场历史账单详情', '/v1/admin/market/billing/history/detail/{billingSummaryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10257', '下载应用市场付款账单历史列表', '/v1/admin/market/billing/history/payment/bill/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10258', '获取Global应用市场某月收款账单详情', '/v1/admin/market/billing/history/receivable/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10259', '下载应用市场账单列表', '/v1/admin/market/billing/history/receivable/detail/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10260', '获取Global应用市场历史收款账单', '/v1/admin/market/billing/history/receivable/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10261', '下载收款账单历史列表', '/v1/admin/market/billing/history/receivable/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10262', '获取应用市场账单信息', '/v1/admin/market/billing/invoice/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10263', '修改应用市场账单信息', '/v1/admin/market/billing/invoice/info', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10264', '手动调用定时任务', '/v1/admin/market/billing/job', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10265', '线下支付账单', '/v1/admin/market/billing/pay/offline/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10266', '获取应用分成历史列表', '/v1/admin/market/billing/revenue/share', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10267', '提交购买结算的转帐请求', '/v1/admin/market/billing/revenue/share/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10268', '获取应用分成详细开发者列表', '/v1/admin/market/billing/revenue/share/developer/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10269', '下载应用分润统计列表', '/v1/admin/market/billing/revenue/share/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10270', '获取应用分成详细应用市场列表', '/v1/admin/market/billing/revenue/share/market/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10271', '下载应用分润统计应用市场列表', '/v1/admin/market/billing/revenue/share/market/list/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10272', '下载指定应用市场账单', '/v1/admin/market/billing/summary/{billingSummaryId}/file/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10273', '下载指定应用市场账单数据文件', '/v1/admin/market/billing/{billingId}/datafile/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10274', 'GLOBAL管理员确认已支付账单', '/v1/admin/market/billing/{billingSummaryId}/confirm', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10275', 'GLOBAL管理员拒绝已支付账单', '/v1/admin/market/billing/{billingSummaryId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10276', '修改应用市场账单接收邮箱', '/v1/admin/market/billing/{emailId}/email', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10277', '删除应用市场账单接收邮箱', '/v1/admin/market/billing/{emailId}/email', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10278', '创建页脚', '/v1/admin/market/footer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10279', '更新页脚', '/v1/admin/market/footer/{footerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10280', '删除页脚', '/v1/admin/market/footer/{footerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10281', '更改应用市场页脚顺序', '/v1/admin/market/footer/{footerId}/sort', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10282', '获取应用市场收款是否设置', '/v1/admin/market/receivable/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10283', '删除应用市场收款帐户', '/v1/admin/market/receivable/account', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10284', '查询应用市场敏感词配置', '/v1/admin/market/sensitiveWord', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10285', '创建应用市场敏感词', '/v1/admin/market/sensitiveWord', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10286', '下载应用市场敏感词文件', '/v1/admin/market/sensitiveWord/file/{sensitiveWordId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10287', '删除应用市场敏感词', '/v1/admin/market/sensitiveWord/{sensitiveWordId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10288', '查询所有应用市场配置', '/v1/admin/market/settings', 'GET', null, '2', '0', '1', NOW(), '1', NOW()),
('10289', '更新应用市场配置', '/v1/admin/market/settings', 'POST', null, '2', '0', '1', NOW(), '1', NOW()),
('10290', '查询SSO配置内容', '/v1/admin/market/sso/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10291', '更新SSO配置', '/v1/admin/market/sso/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10292', '查询TID生成策略配置', '/v1/admin/market/tid/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10293', '更新TID生成策略配置', '/v1/admin/market/tid/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10294', '查询代理商应用市场配置', '/v1/admin/market/ui/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10295', '更新代理商应用市场配置', '/v1/admin/market/ui/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10296', '查询用户协议设置', '/v1/admin/market/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10297', '更新用户协议', '/v1/admin/market/user/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10298', '获取应用市场变量列表', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10299', '创建应用市场变量', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10300', '批量删除应用市场变量', '/v1/admin/market/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10301', '导入应用市场变量', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10302', '创建终应用市场变量导入模板下载任务', '/v1/admin/market/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10303', '查找应用市场变量支持的应用列表', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10304', '查找应用市场变量已使用的的应用列表', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10305', '更新应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10306', '删除应用市场变量', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10307', '根据serviceType获取已定阅的应用市场', '/v1/admin/market/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10308', '提交重新生成购买结算记录', '/v1/admin/purchase/clr', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10309', '(重新)提交购买结算的转帐请求', '/v1/admin/purchase/clr/transfer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10310', '管理员查询版本发行通知列表', '/v1/admin/releaseNotes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10311', '创建版本发行通知', '/v1/admin/releaseNotes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10312', '创建邮件模板下载任务', '/v1/admin/releaseNotes/mail/template/{noteInfoId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10313', '获取版本发行通知信息', '/v1/admin/releaseNotes/{releaseNoteInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10314', '更新版本发行通知', '/v1/admin/releaseNotes/{releaseNoteInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10315', '下载邮件列表', '/v1/admin/releaseNotes/{releaseNoteInfoId}/downloadEmails', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10316', '发送邮件', '/v1/admin/releaseNotes/{releaseNoteInfoId}/sendMail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10317', '测试邮件', '/v1/admin/releaseNotes/{releaseNoteInfoId}/sendTestMail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10318', 'Admin Search Report List', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10319', 'Get Report Metadata', '/v1/admin/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10320', 'resetAllReportMetadataAndTemplate', '/v1/admin/report/metadata', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10321', 'Get report by id', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10322', 'Update Report', '/v1/admin/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10323', 'Delete Report', '/v1/admin/report/{reportId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10324', 'Activate Report', '/v1/admin/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10325', 'Download Report Band File', '/v1/admin/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10326', 'Disable Report', '/v1/admin/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10327', 'Download Report Template File', '/v1/admin/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10328', 'List All Fields of Report', '/v1/admin/reportField', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10329', 'Update Report Field', '/v1/admin/reportField/{reportFieldId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10330', 'Get All Parameters of Report', '/v1/admin/reportParam', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10331', 'Update Report Parameter', '/v1/admin/reportParam/{reportParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10332', '查询代理商第三方系统API访问配置', '/v1/admin/reseller/3rdsys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10333', '允许代理商第三方系统API访问', '/v1/admin/reseller/3rdsys/config/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10334', '禁止代理商第三方系统API访问', '/v1/admin/reseller/3rdsys/config/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10335', '获取代理商第三方系统API访问密钥', '/v1/admin/reseller/3rdsys/config/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10336', '重置代理商第三方系统API访问密钥', '/v1/admin/reseller/3rdsys/config/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10337', '查询TID生成策略代理商配置', '/v1/admin/reseller/tid/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10338', '更新TID生成策略代理商配置', '/v1/admin/reseller/tid/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10339', '更新代理商UI设置', '/v1/admin/reseller/ui/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10340', '创建应用市场RKI配置', '/v1/admin/rki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10341', '获取RKI服务列表', '/v1/admin/rki/servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10342', '删除RKI密钥模板KEY', '/v1/admin/rki/template/key', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10343', '获取RKI密钥模板KEY', '/v1/admin/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10344', '测试未保存应用市场RKI服务', '/v1/admin/rki/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10345', '测试已保存应用市场RKI服务', '/v1/admin/rki/test/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10346', '查询应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10347', '更新应用市场RKI配置', '/v1/admin/rki/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10348', '删除RKI服务配置', '/v1/admin/rki/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10349', '查询应用市场签名配置', '/v1/admin/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10350', '保存应用市场签名配置', '/v1/admin/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10351', '清除签名数据', '/v1/admin/signature/clearData', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10352', '查询制造商PUK', '/v1/admin/signature/factoryPuk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10353', '获取应用市场签名提供商列表', '/v1/admin/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10354', '查询签名公钥', '/v1/admin/signature/signaturePuk/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10355', '测试应用市场签名服务', '/v1/admin/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10356', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10357', 'disableVas', '/v1/admin/vas', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10358', 'getThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10359', 'createThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10360', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10361', 'listPosviewerFileTransferInfo', '/v1/admin/vas/posviewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10362', 'listPosviewerOperationInfo', '/v1/admin/vas/posviewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10363', 'listService', '/v1/admin/vas/service', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10364', 'enableService', '/v1/admin/vas/service/{serviceType}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10365', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10366', '获取应用参数列表', '/v1/apk/parameters', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10367', '创建应用参数', '/v1/apk/parameters', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10368', '查询参数APK列表', '/v1/apk/parameters/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10369', '查询参数应用列表', '/v1/apk/parameters/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10370', '获取参数模板应用列表', '/v1/apk/parameters/parameter/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10371', '获取应用参数详情', '/v1/apk/parameters/{apkParameterId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10372', '更新应用参数', '/v1/apk/parameters/{apkParameterId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10373', '删除应用参数', '/v1/apk/parameters/{apkParameterId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10374', '应用参数数据文件下载', '/v1/apk/parameters/{apkParameterId}/data/file/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10375', '获取应用参数Schema', '/v1/apk/parameters/{apkParameterId}/schema', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10376', '查询应用列表', '/v1/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10377', '查询portal可见应用的行业分类', '/v1/apps/category', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10378', '获取featured图片', '/v1/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10379', '获取应用详情', '/v1/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10380', '获取应用评论列表', '/v1/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10381', '获取appscan dashBoard数据', '/v1/appscan/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10382', '获取appscan 历史用量数据', '/v1/appscan/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10383', '获取扫描可操作的选项', '/v1/appscan/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10384', '重新扫描', '/v1/appscan/rescan', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10385', '获取扫描成功后生成的zip包', '/v1/appscan/resultZip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10386', '获取扫描结果清单', '/v1/appscan/results', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10387', '查询apk是否可以继续创建扫描任务', '/v1/appscan/scanned', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10388', '创建扫描任务', '/v1/appscan/task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10389', '获取appscan扫描用量', '/v1/appscan/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10390', '查询Entity属性', '/v1/attributes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10391', '创建Entity属性', '/v1/attributes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10392', '获取Entity属性信息', '/v1/attributes/{attributeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10393', '更新Entity属性', '/v1/attributes/{attributeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10394', '删除Entity属性', '/v1/attributes/{attributeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10395', '更新Entity属性标签', '/v1/attributes/{attributeId}/label', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10396', '验证激活验证码', '/v1/auth/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10397', '激活用户', '/v1/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10398', '验证激活验证码', '/v1/auth/activation/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10399', 'generateCaptcha', '/v1/auth/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10400', '检查注册用户邮箱', '/v1/auth/check/email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10401', 'currentTokenLogin', '/v1/auth/current', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10402', 'destroySsoToken', '/v1/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10403', '申请成为开发者', '/v1/auth/developers', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10404', '验证重置邮箱验证码', '/v1/auth/email/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10405', '用户更改邮箱', '/v1/auth/email/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10406', '验证重置邮箱验证码', '/v1/auth/email/reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10407', '获取应用市场的相关信息', '/v1/auth/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10408', '获取应用市场DC的相关信息', '/v1/auth/market/dc', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10409', '通过backup code关闭用户OTP', '/v1/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10410', '验证关闭用户OTP Code是否有效', '/v1/auth/otp/disableCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10411', '发送关闭用户OTP邮件', '/v1/auth/otp/resetMail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10412', '忘记密码发送邮件(未激活的重发激活邮件)', '/v1/auth/password/forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10413', '验证重置密码验证码', '/v1/auth/password/reset', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10414', '重置密码（用于忘记密码）', '/v1/auth/password/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10415', '验证重置密码验证码', '/v1/auth/password/reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10416', '获取当前的密码规则', '/v1/auth/password/rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10417', 'checkTokenExpire', '/v1/auth/ping', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10418', '注册用户', '/v1/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10419', '创建前端埋点', '/v1/buriedPoints', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10420', '获取字典列表', '/v1/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10421', '获取语言列表', '/v1/codes/lang', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10422', 'super管理员获取Code配置列表', '/v1/codes/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10423', 'super管理员查询CodeType类型列表', '/v1/codes/setting/codeTypes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10424', 'super管理员保存Code配置', '/v1/codes/setting/save', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10425', 'super管理员获取Code配置', '/v1/codes/setting/{type}/{value}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10426', 'super管理员删除Code配置', '/v1/codes/setting/{type}/{value}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10427', '根据类型获取字典列表', '/v1/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10428', '查询data center', '/v1/dcmgt', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10429', '新增一个 Data Center', '/v1/dcmgt', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10430', '根据 Id 删除对应的DC', '/v1/dcmgt/{id}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10431', '修改对应的key-secret', '/v1/dcmgt/{id}/ks', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10432', '查询开发者第三方API访问配置', '/v1/developers/3rdsys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10433', '允许开发者访问第三方系统API', '/v1/developers/3rdsys/config/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10434', '禁止开发者访问第三方系统API', '/v1/developers/3rdsys/config/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10435', '获取开发者场第三方系统API访问密钥', '/v1/developers/3rdsys/config/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10436', '重置开发者第三方系统API访问密钥', '/v1/developers/3rdsys/config/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10437', '创建开发者下载apk数据文件下载任务', '/v1/developers/apks/{apkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10438', '创建开发者原始Apk下载任务', '/v1/developers/apks/{apkId}/originFile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10439', '创建开发者下载apk参数模版下载任务', '/v1/developers/apks/{apkId}/paramTemplate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10440', '获取开发者应用参数Schema', '/v1/developers/apks/{apkId}/paramTemplateSchema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10441', '创建参数模板', '/v1/developers/app/{appId}/param/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10442', '删除自定义参数模板', '/v1/developers/app/{appId}/param/templates', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10443', '上传自定义参数模板并进行解析Xml2Json', '/v1/developers/app/{appId}/paramAnalysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10444', '查询开发者应用列表', '/v1/developers/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10445', '创建用户应用', '/v1/developers/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10446', '获取开发者应用详细信息', '/v1/developers/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10447', '删除开发者应用', '/v1/developers/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10448', '获取APK列表', '/v1/developers/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10449', '添加apk', '/v1/developers/apps/{appId}/apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10450', '获取APK', '/v1/developers/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10451', '覆盖更新APK信息、参数模板,图标与截图', '/v1/developers/apps/{appId}/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10452', '删除开发者应用版本', '/v1/developers/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10453', '更新APK文件', '/v1/developers/apps/{appId}/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10454', '提交APK', '/v1/developers/apps/{appId}/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10455', '覆盖更新APP Key, APP Secret', '/v1/developers/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10456', 'developer search insight sandbox app biz data', '/v1/developers/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10457', '查询开发者证书', '/v1/developers/cert', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10458', '开发者证书下载', '/v1/developers/cert', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10459', '开发者同意协议', '/v1/developers/developer/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10460', '企业开发者超级管理员更换', '/v1/developers/developer/{userId}/super/admin', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10461', '添加企业开发者', '/v1/developers/enterprise', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10462', '查询企业开发者列表', '/v1/developers/enterprise/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10463', '刪除企业开发者用户', '/v1/developers/enterprise/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10464', '将开发者设为管理员', '/v1/developers/enterprise/{userId}/admin', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10465', '下载自定义参数模板最终生成的.p文件-内容为默认值', '/v1/developers/param/template/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10466', '检查参数模板名称', '/v1/developers/param/template/name/validate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10467', '获取开发者在线自定义应用参数Schema-预览', '/v1/developers/param/template/schema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10468', '获取自定义参数模板列表', '/v1/developers/param/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10469', '开发者获取上次自定义模板编辑内容', '/v1/developers/param/templates/{paramId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10470', '更新参数模板', '/v1/developers/param/templates/{paramId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10471', '获得开发者支付注册费初始化参数', '/v1/developers/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10472', '提交开发者注册费支付请求', '/v1/developers/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10473', '线下支付账单', '/v1/developers/payment/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10474', '查询开发者的应用销售列表', '/v1/developers/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10475', '查询开发者的应用所销售的市场列表', '/v1/developers/purchases/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10476', '获取开发者的收款帐户是否设置', '/v1/developers/receivable/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10477', '删除开发者的收款帐户', '/v1/developers/receivable/account', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10478', '开发者应用结算查询', '/v1/developers/report/clearence', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10479', '获取应用沙箱测试列表', '/v1/developers/sandbox/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10480', '创建沙箱终端推送应用', '/v1/developers/sandbox/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10481', '获取沙箱终端推送APK', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10482', '删除沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10483', '激活沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10484', '沙箱终端推送应用参数数据文件下载', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10485', '获取沙箱终端推送APK参数', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10486', '更新沙箱终端推送参数', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10487', '重置沙箱终端推送参数任务', '/v1/developers/sandbox/terminalApks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10488', '获取开发者终端列表', '/v1/developers/sandbox/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10489', '创建开发者终端', '/v1/developers/sandbox/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10490', '获取开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10491', '更新开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10492', '删除开发者终端', '/v1/developers/sandbox/terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10493', 'get stackly alert strategy', '/v1/developers/stacklytics/alert/strategy', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10494', 'create stackly alert strategy', '/v1/developers/stacklytics/alert/strategy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10495', 'update stackly alert strategy', '/v1/developers/stacklytics/alert/strategy', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10496', 'delete stackly alert strategy', '/v1/developers/stacklytics/alert/strategy/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10497', 'get stackly app info', '/v1/developers/stacklytics/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10498', 'create stackly app', '/v1/developers/stacklytics/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10499', 'update stackly app info', '/v1/developers/stacklytics/app', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10500', 'create stackly comment', '/v1/developers/stacklytics/comment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10501', 'update stackly comment', '/v1/developers/stacklytics/comment', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10502', 'delete stackly comment', '/v1/developers/stacklytics/comment', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10503', 'get stackly comments', '/v1/developers/stacklytics/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10504', 'get stackly crash report summary info', '/v1/developers/stacklytics/crash_report_info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10505', 'get stackly crash report summary list', '/v1/developers/stacklytics/crash_report_list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10506', 'get stackly exception dist data', '/v1/developers/stacklytics/dist/new/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10507', 'get stackly exception dist distribution', '/v1/developers/stacklytics/dist/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10508', 'get stackly stacktrace file download info', '/v1/developers/stacklytics/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10509', 'get stackly hash stack exception statistics', '/v1/developers/stacklytics/exception_statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10510', 'export exception list', '/v1/developers/stacklytics/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10511', 'get stackly hash stack files', '/v1/developers/stacklytics/files', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10512', 'get stacly hash stack filter infos', '/v1/developers/stacklytics/filter_infos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10513', 'get stackly hash stack summaries', '/v1/developers/stacklytics/hash_stack', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10514', 'get hash stack handle history', '/v1/developers/stacklytics/histories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10515', 'create stackly hash stack handle history', '/v1/developers/stacklytics/history', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10516', 'get stackly hash stack info', '/v1/developers/stacklytics/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10517', 'getDataQueryFromInsight', '/v1/developers/stacklytics/insight/worksheet', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10518', 'add stackly member', '/v1/developers/stacklytics/member', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10519', 'update stackly member', '/v1/developers/stacklytics/member', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10520', 'update stackly member', '/v1/developers/stacklytics/member', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10521', 'get current stackly user info', '/v1/developers/stacklytics/member/authority', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10522', 'handover stackly authority', '/v1/developers/stacklytics/member/handover', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10523', 'get stackly members', '/v1/developers/stacklytics/members', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10524', 'get stackly All members', '/v1/developers/stacklytics/members/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10525', 'get stackly hash stack stack trace', '/v1/developers/stacklytics/stack_trace', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10526', 'get stackly exception status dist distribution', '/v1/developers/stacklytics/status/dist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10527', 'syncDeveloperList', '/v1/developers/stacklytics/sync/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10528', 'add stackly tag to hash stack', '/v1/developers/stacklytics/tag', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10529', 'delete stackly tag from hash stack', '/v1/developers/stacklytics/tag', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10530', 'get stackly app tags', '/v1/developers/stacklytics/tags', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10531', 'create stackly app tags', '/v1/developers/stacklytics/tags', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10532', 'update stackly app tags', '/v1/developers/stacklytics/tags', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10533', 'delete stackly app tags', '/v1/developers/stacklytics/tags/{tagId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10534', '查询开发者概况', '/v1/developers/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10535', '下载Fastdfs服务器上的文件', '/v1/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10536', '下载Fastdfs服务器上的文件', '/v1/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10537', '根据下载号获得下载地址', '/v1/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10538', '获取制造商列表', '/v1/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10539', '创建制造商', '/v1/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10540', '获取制造商机型树', '/v1/factories/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10541', '获取制造商', '/v1/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10542', '更新制造商', '/v1/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10543', '删除制造商', '/v1/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10544', '获取制造商定向发布到的市场列表', '/v1/factories/{factoryId}/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10545', '定向发布制造商到应用市场', '/v1/factories/{factoryId}/market', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10546', '删除制造商定向发布到市场', '/v1/factories/{factoryId}/market', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10547', '查询反馈列表', '/v1/feedbacks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10548', '创建反馈', '/v1/feedbacks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10549', '获取反馈信息', '/v1/feedbacks/{feedbackId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10550', '更新反馈', '/v1/feedbacks/{feedbackId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10551', '删除反馈', '/v1/feedbacks/{feedbackId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10552', '获取Fastdfs服务器上的文件', '/v1/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10553', '获取固件列表', '/v1/firmwares', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10554', '查看固件的factory列表', '/v1/firmwares/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10555', '上传固件', '/v1/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10556', '删除固件差分包', '/v1/firmwares/firmware/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10557', '创建固件文件下载任务', '/v1/firmwares/firmware/files/{fmFileId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10558', '获取固件', '/v1/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10559', '更新固件', '/v1/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10560', '删除固件', '/v1/firmwares/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10561', '固件审核通过', '/v1/firmwares/{firmwareId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10562', '上传固件差分包', '/v1/firmwares/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10563', '下线固件', '/v1/firmwares/{firmwareId}/offline', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10564', '上线固件', '/v1/firmwares/{firmwareId}/online', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10565', '固件审核拒绝', '/v1/firmwares/{firmwareId}/reject', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10566', '提交固件更新', '/v1/firmwares/{firmwareId}/submit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10567', '获取终端分组变量列表', '/v1/groupVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10568', '创建终端分组变量', '/v1/groupVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10569', '批量删除终端分组变量', '/v1/groupVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10570', '导入终端分组变量', '/v1/groupVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10571', '创建终端分组变量导入模板下载任务', '/v1/groupVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10572', '查找终端分组变量支持的应用列表', '/v1/groupVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10573', '查找终端分组变量已使用的应用列表', '/v1/groupVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10574', '更新终端分组变量', '/v1/groupVariables/{groupVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10575', '删除终端分组变量', '/v1/groupVariables/{groupVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10576', 'Clear Cache', '/v1/internal/3rdsys/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10577', 'Clear Group Pending Terminal Actions', '/v1/internal/3rdsys/clearGroupPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10578', 'Delete Lock', '/v1/internal/3rdsys/delLock', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10579', 'Get Cache', '/v1/internal/3rdsys/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10580', 'Get Lock', '/v1/internal/3rdsys/getLock', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10581', 'Refresh Group Action Count', '/v1/internal/3rdsys/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10582', 'Refresh Reseller Installed Apks', '/v1/internal/3rdsys/refreshResellerInstalledApks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10583', 'Refresh Terminal Last Access Time', '/v1/internal/3rdsys/refreshTerminalLastAccessTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10584', 'Refresh Terminal Online Status', '/v1/internal/3rdsys/refreshTerminalOnlineStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10585', 'Get system property log', '/v1/internal/3rdsys/system/property/log', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10586', 'Get system properties', '/v1/internal/3rdsys/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10587', 'Save System Property', '/v1/internal/3rdsys/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10588', 'Get Terminal', '/v1/internal/3rdsys/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10589', 'Send terminal command', '/v1/internal/3rdsys/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10590', 'Send terminal message', '/v1/internal/3rdsys/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10591', 'Administrator view push history list', '/v1/internal/3rdsys/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10592', 'Reset terminal data', '/v1/internal/3rdsys/terminal/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10593', 'Expire terminal token', '/v1/internal/3rdsys/terminal/token/expire', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10594', 'Get Filter Terminals', '/v1/internal/3rdsys/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10595', 'Post Filter Terminals', '/v1/internal/3rdsys/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10596', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10597', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10598', 'getApps', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10599', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10600', '获取许可证信息', '/v1/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10601', '更新许可证', '/v1/license', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10602', '获取AirViewer当前用量', '/v1/marketAdmin/vas/airViewer/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10603', '获取AirViewer当前用量dashBoard', '/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10604', '获取AirViewer历史用量', '/v1/marketAdmin/vas/airViewer/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10605', '导出airviewer用量', '/v1/marketAdmin/vas/airviewer/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10606', '导出appscan用量', '/v1/marketAdmin/vas/appscan/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10607', '获取应用市场增值服务的账单设置', '/v1/marketAdmin/vas/billingSetting/{billingType}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10608', '获取GoInsight当前用量', '/v1/marketAdmin/vas/goInsight/currentUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10609', '获取GoInsight当前用量dashBoard', '/v1/marketAdmin/vas/goInsight/currentUsage/dashBoard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10610', '获取GoInsight历史用量', '/v1/marketAdmin/vas/goInsight/historicalUsage', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10611', '获取市场可见服务列表', '/v1/marketAdmin/vas/services', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10612', '市场更新AirViewer服务连接时长', '/v1/marketAdmin/vas/services/airviewer/connectionTime/{operationId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10613', '是否显示当月用量', '/v1/marketAdmin/vas/services/show', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10614', '市场取消订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10615', '市场订阅某个服务', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10616', '查询服务定向发布的代理商', '/v1/marketAdmin/vas/services/{serviceType}/reseller/specific', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10617', '修改服务定向发布', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10618', '删除服务定向发布', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10619', '市场更新服务状态', '/v1/marketAdmin/vas/services/{serviceType}/{status}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10620', '获取应用市场指定年月的接入终端快照', '/v1/marketAdmin/vas/snapshot/file/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10621', '获取当前应用市场的终端接入量详情', '/v1/marketAdmin/vas/terminal/enroll/bill', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10622', '下载指定市场当前月的接入终端详情', '/v1/marketAdmin/vas/terminal/enroll/current/{marketId}/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10623', '获取应用市场当前的终端接入量统计', '/v1/marketAdmin/vas/terminal/enroll/dashboard', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10624', '获取当前应用市场的终端接入量统计', '/v1/marketAdmin/vas/terminal/enroll/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10625', '获取当前应用市场的终端接入量统计', '/v1/marketAdmin/vas/terminal/enroll/history/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10626', '查询应用市场列表', '/v1/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10627', '创建应用市场', '/v1/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10628', '获取app统计的详细信息', '/v1/markets/appNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10629', '获取应用市场下app数量', '/v1/markets/applicationNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10630', '统计开发者数量', '/v1/markets/developerNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10631', '获取开发者统计信息详情', '/v1/markets/developerNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10632', '获取应用市场数/快过期的数量', '/v1/markets/marketNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10633', '获取应用市场详细统计信息', '/v1/markets/marketNumDetail', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10634', '终端数量', '/v1/markets/terminalNum', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10635', '生成应用市场终端报表', '/v1/markets/terminalReport', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10636', '获取应用市场信息', '/v1/markets/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10637', '更新应用市场', '/v1/markets/{marketId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10638', '删除应用市场', '/v1/markets/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10639', '更新应用市场账单设置', '/v1/markets/{marketId}/billing', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10640', '更新应用市场服务设置', '/v1/markets/{marketId}/billing/service', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10641', '替换应用市场管理员（邮箱）', '/v1/markets/{marketId}/replaceEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10642', '重新发送应用市场激活邮件', '/v1/markets/{marketId}/resend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10643', '解挂应用市场', '/v1/markets/{marketId}/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10644', '挂起应用市场', '/v1/markets/{marketId}/suspend', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10645', '获取商户分类列表', '/v1/merchantCategories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10646', '创建商户分类', '/v1/merchantCategories', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10647', '批量删除商户分类', '/v1/merchantCategories/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10648', '更新商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10649', '删除商户分类', '/v1/merchantCategories/{merchantCategoryId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10650', '获取商户变量列表', '/v1/merchantVariables', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10651', '创建商户变量', '/v1/merchantVariables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10652', '批量删除商户变量', '/v1/merchantVariables/batch/deletion', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10653', '导入商户变量', '/v1/merchantVariables/import', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10654', '创建商户变量导入模板下载任务', '/v1/merchantVariables/import/template', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10655', '查找商户变量支持的应用列表', '/v1/merchantVariables/supported/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10656', '查找商户变量已使用的应用列表', '/v1/merchantVariables/used/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10657', '更新商户变量', '/v1/merchantVariables/{merchantVariableId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10658', '删除商户变量', '/v1/merchantVariables/{merchantVariableId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10659', '获取商户列表', '/v1/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10660', '创建商户', '/v1/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10661', '创建商户导出下载任务', '/v1/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10662', '导入商户', '/v1/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10663', '创建商户导入模板下载任务', '/v1/merchants/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10664', '获取代理商下一层级商户', '/v1/merchants/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10665', '获取商户', '/v1/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10666', '更新商户', '/v1/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10667', '删除商户', '/v1/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10668', '激活商户', '/v1/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10669', '停用商户', '/v1/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10670', '获取商户配置文件', '/v1/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10671', '创建商户配置文件', '/v1/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10672', '替换商户管理员（邮箱）', '/v1/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10673', '重发商户激活邮件', '/v1/merchants/{merchantId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10674', '查询Migrations', '/v1/migrations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10675', '创建Migration', '/v1/migrations', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10676', '获取Migration', '/v1/migrations/{migrationId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10677', '删除Migration', '/v1/migrations/{migrationId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10678', '查询Migration Apk Template', '/v1/migrations/{migrationId}/apkTemplates', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10679', '执行Migration', '/v1/migrations/{migrationId}/execute', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10680', '导出Migration结果', '/v1/migrations/{migrationId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10681', '查询Migration Merchant', '/v1/migrations/{migrationId}/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10682', '通知Migration新创建的用户', '/v1/migrations/{migrationId}/notify', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10683', '回滚Migration', '/v1/migrations/{migrationId}/rollback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10684', '查询Migration Terminal Apk', '/v1/migrations/{migrationId}/terminalApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10685', '查询Migration Terminal Group', '/v1/migrations/{migrationId}/terminalGroups', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10686', '查询Migration Terminal', '/v1/migrations/{migrationId}/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10687', '查询Migration User', '/v1/migrations/{migrationId}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10688', '验证Migration', '/v1/migrations/{migrationId}/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10689', '获取机型列表', '/v1/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10690', '创建机型', '/v1/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10691', '制造商发布当前市场可见机型', '/v1/models/factory/{factoryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10692', '获取机型', '/v1/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10693', '更新机型', '/v1/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10694', '删除机型', '/v1/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10695', '查询机型可用的应用列表', '/v1/models/{modelId}/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10696', '添加AppMsg', '/v1/msg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10697', '检查当前应用市场或自应用市场是否有终端安装该app', '/v1/msg/app/installed/check/{packageName}/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10698', '获取appMsg', '/v1/msg/appMsgList', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10699', '获取appMsg统计数据', '/v1/msg/report/{msgId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10700', '上传msgTemplateFile', '/v1/msg/template/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10701', '核验网络图片url的大小', '/v1/msg/template/validation-img-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10702', '获取app的vasSupport', '/v1/msg/vas/support/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10703', '更新AppMsg状态', '/v1/msg/{appMsgId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10704', '根据id 获取msg', '/v1/msg/{id}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10705', '根据id 获取定时msg状态', '/v1/msg/{id}/status', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10706', '获取所有受保护的操作列表', '/v1/operations', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10707', '获取所有受保护的操作', '/v1/operations/{key}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10708', '关闭操作权限', '/v1/operations/{key}/close', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10709', '打开操作权限', '/v1/operations/{key}/open', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10710', '查询操作用户', '/v1/operations/{key}/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10711', '添加操作用户', '/v1/operations/{key}/users/{userId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10712', '删除操作用户', '/v1/operations/{key}/users/{userId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10713', '获得支付初始化参数(支付账单)', '/v1/payment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10714', '提交支付请求', '/v1/payment', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10715', '查询权限列表', '/v1/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10716', '创建权限', '/v1/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10717', '获取权限信息', '/v1/privileges/{privilegeId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10718', '更新权限', '/v1/privileges/{privilegeId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10719', '删除权限', '/v1/privileges/{privilegeId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10720', '获取权限资源列表', '/v1/privileges/{privilegeId}/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10721', '添加权限资源', '/v1/privileges/{privilegeId}/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10722', '删除权限资源', '/v1/privileges/{privilegeId}/resources', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10723', 'getEnvCode', '/v1/pub/vas/envCode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10724', 'User Search Report', '/v1/report', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10725', 'Get Report Categories', '/v1/report/categories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10726', '获取安装过应用列表', '/v1/report/installed/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10727', 'GET merchants by resellerIds', '/v1/report/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10728', 'Refresh Report Parameter Sources', '/v1/report/parameter/{parameterId}/source/refresh', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10729', '获取推送过的最新APK列表', '/v1/report/pushed/apks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10730', 'Delete Report Execution Tasks', '/v1/report/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10731', 'Search Report Job History', '/v1/report/reportJobHistory', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10732', 'Create Report Download Task', '/v1/report/reportTask/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10733', 'Update Report Tasks Status', '/v1/report/reportTask/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10734', 'Search Report Task', '/v1/report/reportTask/{reportId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10735', 'Update Report Task Status', '/v1/report/reportTask/{reportTaskId}/status', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10736', 'GET resellers by marketId', '/v1/report/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10737', 'Delete Report Execution Task', '/v1/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10738', 'User View Report', '/v1/report/{reportId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10739', 'Export Report', '/v1/report/{reportId}/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10740', 'find fields', '/v1/report/{reportId}/fields', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10741', 'Create Report Execution Task', '/v1/report/{reportId}/reportExecutionContext', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10742', 'Get Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10743', 'Update Report Execution Task Info', '/v1/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10744', '查询开发者应用结算相关数据', '/v1/reports/clearence/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10745', '查询应用市场应用结算相关数据', '/v1/reports/clearence/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10746', '查询平台应用结算相关数据', '/v1/reports/clearence/platform', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10747', '查询报表所需的应用市场列表', '/v1/reports/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10748', '查询应用购买明细列表', '/v1/reports/purchases', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10749', '按应用查询销售汇总', '/v1/reports/purchases/app/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10750', '按开发者查询销售汇总', '/v1/reports/purchases/developer/summary', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10751', '获取代理商列表', '/v1/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10752', '创建代理商', '/v1/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10753', '代理商查询应用列表', '/v1/resellers/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10754', '创建导入TMK模板下载任务', '/v1/resellers/rki/tmk/import/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10755', '获取当前用户代理商树', '/v1/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10756', '获取下一层级子代理商', '/v1/resellers/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10757', '获取代理商', '/v1/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10758', '更新代理商', '/v1/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10759', '删除代理商', '/v1/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10760', '激活代理商', '/v1/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10761', '停用代理商', '/v1/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10762', '获取当前代理商终端已安装应用列表', '/v1/resellers/{resellerId}/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10763', '获取代理商配置文件', '/v1/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10764', '创建代理商配置文件', '/v1/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10765', '替换代理商管理员（邮箱）', '/v1/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10766', '重发激活邮件', '/v1/resellers/{resellerId}/resendEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10767', '同步代理商的RKI密钥列表', '/v1/resellers/{resellerId}/rki/keys/collect', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10768', 'Push RKI进行预扣款', '/v1/resellers/{resellerId}/rki/pre-deduction', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10769', '获取RKI密钥模板KEY', '/v1/resellers/{resellerId}/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10770', '获取导入tmk记录', '/v1/resellers/{resellerId}/rki/tmk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10771', '导入终端主密钥', '/v1/resellers/{resellerId}/rki/tmk/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10772', '保存代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10773', '删除代理商RKI用户Token', '/v1/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10774', '查询资源列表', '/v1/resources', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10775', '创建资源', '/v1/resources', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10776', '获取资源信息', '/v1/resources/{resourceId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10777', '更新资源', '/v1/resources/{resourceId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10778', '删除资源', '/v1/resources/{resourceId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10779', '获取资源权限列表', '/v1/resources/{resourceId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10780', '添加资源权限', '/v1/resources/{resourceId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10781', '删除资源权限', '/v1/resources/{resourceId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10782', '查询角色列表', '/v1/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10783', '创建角色', '/v1/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10784', '查询角色列表包含开发者和商户', '/v1/roles/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10785', '获取角色的用户', '/v1/roles/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10786', '查询所有角色的用户列表', '/v1/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10787', '获取角色信息', '/v1/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10788', '更新角色', '/v1/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10789', '删除角色', '/v1/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10790', '获取角色权限列表', '/v1/roles/{roleId}/privileges', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10791', '添加角色权限', '/v1/roles/{roleId}/privileges', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10792', '删除角色权限', '/v1/roles/{roleId}/privileges', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10793', '获取角色用户列表', '/v1/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10794', '添加角色用户', '/v1/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10795', '删除角色用户', '/v1/roles/{roleId}/users', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10796', '管理员查看POS Client App列表', '/v1/selfApps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10797', '查看POS Client App的factory列表', '/v1/selfApps/factories', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10798', '创建最新PAXSTORE客户端下载任务', '/v1/selfApps/latest/client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10799', '查看POS Client Apk列表for approval', '/v1/selfApps/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10800', '查看POS Client Apk', '/v1/selfApps/selfApks/{selfApkId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10801', '首次上传POS Client Apk', '/v1/selfApps/{factoryId}/selfApk/file/first', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10802', '获取POS Client App详细信息', '/v1/selfApps/{selfAppId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10803', '删除POS Client App', '/v1/selfApps/{selfAppId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10804', '上传新版本POS Client Apk', '/v1/selfApps/{selfAppId}/selfApk/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10805', '查看某个POS Client App的Apk列表', '/v1/selfApps/{selfAppId}/selfApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10806', '更新SelfApk(更新日志，强制更新)，SelfApp name', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10807', '删除POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10808', '通过POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/approval', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10809', '创建PAXSTORE客户端下载任务', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/download', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10810', '重新上传POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/file', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10811', '下线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/offline', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10812', '上线POS Client Apk', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/online', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10813', '拒绝POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/rejection', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10814', '提交POS Client Apk审核', '/v1/selfApps/{selfAppId}/selfApks/{selfApkId}/submit', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10815', '获取系统配置', '/v1/system/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10816', '获取文档中心配置信息', '/v1/system/doc/setting', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10817', '查询页脚列表', '/v1/system/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10818', '获取页脚', '/v1/system/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10819', '查询系统属性', '/v1/system/properties', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10820', '创建系统属性', '/v1/system/properties', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10821', '获取系统属性信息', '/v1/system/properties/{systemPropertyId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10822', '更新系统属性', '/v1/system/properties/{systemPropertyId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('10823', '删除系统属性', '/v1/system/properties/{systemPropertyId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('10824', '查询推送检测结果', '/v1/system/push/diagnosis', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10825', '发送推送检测消息', '/v1/system/push/diagnosis', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10826', '查询预定义角色列表', '/v1/system/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10827', '查询登录相关配置', '/v1/system/security/loginCfg', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10828', '保存登录相关配置', '/v1/system/security/loginCfg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10829', '查询密码策略', '/v1/system/security/password/policy', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10830', '保存密码策略', '/v1/system/security/password/policy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10831', '查询用户协议', '/v1/system/user/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10832', '根据登录名查找用户信息', '/v1/system/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10833', '查询终端推送记录', '/v1/terminal/actions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10834', '批量更新终端操作状态', '/v1/terminal/actions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10835', '更新终端操作状态', '/v1/terminal/actions/{actionId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10836', 'POS端获取终端广告配置', '/v1/terminal/advertisements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10837', '获取终端应用参数下载列表', '/v1/terminal/app/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10838', '获取终端应用参数下载地址', '/v1/terminal/app/params/{actionId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10839', '获取终端应用下载列表', '/v1/terminal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10840', '根据服务器的信息获取终端应用自动更新列表', '/v1/terminal/apps/autoUpdate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10841', '终端获取 featured app', '/v1/terminal/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10842', '终端查询应用列表', '/v1/terminal/apps/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10843', '终端获取应用APK列表', '/v1/terminal/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10844', '终端获取应用APK详情', '/v1/terminal/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10845', '<s>下载应用APK</s> - deprecated', '/v1/terminal/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10846', '获取APK下载信息', '/v1/terminal/apps/{appId}/apks/{apkId}/download_url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10847', '验证应用认证信息', '/v1/terminal/apps/{appId}/auth/verify', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10848', '终端获取应用评论', '/v1/terminal/apps/{appId}/comments', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10849', '终端保存应用评论', '/v1/terminal/apps/{appId}/comments', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10850', '终端删除应用评论', '/v1/terminal/apps/{appId}/comments', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10851', '终端获取应用详情', '/v1/terminal/apps/{appId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10852', '<s>下载应用最新的APK</s> - deprecated', '/v1/terminal/apps/{appId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10853', 'Get push channel in terminal table', '/v1/terminal/cloud/msg/pushchannel', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10854', 'Update push channel in terminal table', '/v1/terminal/cloud/msg/pushchannel', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10855', 'Refresh AccessToken via CloudServiceGateway for terminal', '/v1/terminal/cloudservice/access/refresh', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10856', 'Get access url and token via CloudServiceGateway for terminal', '/v1/terminal/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10857', '获取终端命令列表', '/v1/terminal/commands', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10858', '获取终端激活命令', '/v1/terminal/commands/activation', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10859', '获取终端命令列表和Profile', '/v1/terminal/commands/new', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10860', '终端上送终端配置-Deprecated', '/v1/terminal/configurations', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10861', '终端上送详情信息-Deprecated', '/v1/terminal/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10862', '终端下载文件', '/v1/terminal/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10863', '获取终端文件', '/v1/terminal/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10864', '终端下载固件', '/v1/terminal/firmware/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10865', '获取终端固件下载列表', '/v1/terminal/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10866', '终端查询页脚列表', '/v1/terminal/footer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10867', 'POS端获取终端硬件状态列表', '/v1/terminal/hardware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10868', '终端上传终端位置-Deprecated', '/v1/terminal/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10869', '终端登录', '/v1/terminal/login', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10870', '传统终端SN加密登录', '/v1/terminal/login/legacy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10871', '终端获取Token信息', '/v1/terminal/login/token', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10872', '终端SN加密登录', '/v1/terminal/login/token', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10873', '终端登录验证', '/v1/terminal/login/validate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10874', 'POS端获取logo配置', '/v1/terminal/logo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10875', '终端上送日志', '/v1/terminal/logs', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10876', '下载应用市场APP', '/v1/terminal/market/update', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10877', '终端商户登录', '/v1/terminal/merchant/login', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10878', '获取终端商户用户信息', '/v1/terminal/merchant/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10879', '终端上送监控信息-Deprecated', '/v1/terminal/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10880', '获取终端操作任务列表', '/v1/terminal/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10881', '获取需要更新参数的应用Apk列表', '/v1/terminal/param/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10882', '获取需要更新参数的应用列表', '/v1/terminal/param/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10883', '查询ped设置', '/v1/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10884', '获取终端配置文件', '/v1/terminal/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10885', '查询当前终端的应用购买列表', '/v1/terminal/purchases', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10886', '查询当前终端指定应用的购买记录详情', '/v1/terminal/purchases/app/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10887', '获得终端购买应用请求的支付类型的初始化参数', '/v1/terminal/purchases/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10888', '提交终端购买应用支付请求', '/v1/terminal/purchases/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10889', '远程初始化终端', '/v1/terminal/remote/init', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10890', '远程初始化传统终端', '/v1/terminal/remote/init/legacy', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10891', '换机操作，根据SN获取终端信息', '/v1/terminal/remote/serialNo/**', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10892', '根据TID获取终端信息', '/v1/terminal/remote/tid/**', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10893', 'POS端获取终端代理商证书', '/v1/terminal/reseller/certificate', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10894', 'RKI服务器回调方法', '/v1/terminal/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10895', '获取RKI服务器配置信息', '/v1/terminal/rki/server/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10896', '传统终端libpaxstore.so获取RKI任务接口', '/v1/terminal/rki/task', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10897', '获取终端RKI任务列表', '/v1/terminal/rki/task/action', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('10898', '传统终端libpaxstore.so上送RKI任务执行结果接口', '/v1/terminal/rki/task/result', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('10899', '获取当前终端状态', '/v1/terminal/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10900', '同步已安装应用列表', '/v1/terminal/sync/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10901', '同步已安装(非PAXSTORE)应用列表ICON信息', '/v1/terminal/sync/apps/icons', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10902', '终端上送手动安装的应用', '/v1/terminal/sync/apps/manual', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10903', '同步已安装应用列表,返回需同步ICON的APP', '/v1/terminal/sync/apps/new', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10904', '终端上送终端配置', '/v1/terminal/sync/configurations', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10905', '终端上送单个终端配置', '/v1/terminal/sync/configurations/{key}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10906', '终端上送详情信息', '/v1/terminal/sync/details', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10907', '同步已安装固件', '/v1/terminal/sync/firmware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10908', '同步硬件状态信息', '/v1/terminal/sync/hardware', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10909', '同步终端历史信息', '/v1/terminal/sync/historic', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10910', '同步应用信息', '/v1/terminal/sync/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10911', '终端上传终端位置', '/v1/terminal/sync/location', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10912', '终端上送监控信息', '/v1/terminal/sync/monitors', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10913', '同步终端协议', '/v1/terminal/sync/protocol', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10914', '终端推送测试', '/v1/terminal/test/push', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10915', '获取需要卸载的应用Apk列表', '/v1/terminal/uninstall/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10916', '获取终端推送应用列表', '/v1/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10917', '创建终端推送应用', '/v1/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10918', '获取终端推送APK', '/v1/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10919', '删除终端推送应用', '/v1/terminalApks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10920', '激活终端推送应用', '/v1/terminalApks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10921', '终端推送应用参数数据文件下载', '/v1/terminalApks/{terminalApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10922', '获取终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10923', '更新终端推送APK参数', '/v1/terminalApks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10924', '获取终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10925', '保存终端推送应用参数变量列表', '/v1/terminalApks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10926', '重置终端推送应用', '/v1/terminalApks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10927', '挂起终端推送应用', '/v1/terminalApks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10928', '获取终端推送固件列表', '/v1/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10929', '创建终端推送固件', '/v1/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10930', '获取终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10931', '删除终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10932', '激活终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10933', '重置终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10934', '挂起终端推送固件', '/v1/terminalFirmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10935', '获取终端分组推送应用列表', '/v1/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10936', '创建终端分组推送应用', '/v1/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10937', '更新终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('10938', '删除终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10939', '获取终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10940', '删除终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10941', '重新推送终端分组应用', '/v1/terminalGroupApks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10942', '激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10943', '分组推送应用参数数据文件下载', '/v1/terminalGroupApks/{groupApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10944', '创建终端分组推送应用的筛选条件', '/v1/terminalGroupApks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10945', '获取终端分组推送应用参数', '/v1/terminalGroupApks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10946', '更新终端分组推送应用参数参数', '/v1/terminalGroupApks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10947', '重新推送终端分组应用参数', '/v1/terminalGroupApks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10948', '获取终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10949', '修改终端分组推送应用参数终端变量列表', '/v1/terminalGroupApks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10950', '获取终端分组推送应用参数终端列表', '/v1/terminalGroupApks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10951', '获取终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10952', '保存终端分组推送应用参数变量列表', '/v1/terminalGroupApks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10953', '重新激活终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10954', '挂起终端分组推送应用', '/v1/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10955', '获取终端分组推送应用终端列表', '/v1/terminalGroupApks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10956', '创建分组应用推送终端列表下载任务', '/v1/terminalGroupApks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10957', '获取分组推送固件列表', '/v1/terminalGroupFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10958', '创建分组推送固件', '/v1/terminalGroupFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10959', '获取分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10960', '删除分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10961', '重新推送终端分组固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10962', '激活分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10963', '重置分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10964', '挂起分组推送固件', '/v1/terminalGroupFirmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10965', '获取分组推送固件终端列表', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10966', '创建分组固件推送终端列表下载任务', '/v1/terminalGroupFirmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10967', '获取分组推送Operation列表', '/v1/terminalGroupOpts', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10968', '创建分组推送Operation', '/v1/terminalGroupOpts', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10969', '获取分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10970', '删除分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10971', '重新推送终端分组Operation', '/v1/terminalGroupOpts/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10972', '激活分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10973', '重置分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10974', '挂起分组推送Operation', '/v1/terminalGroupOpts/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10975', '获取分组推送Operation终端列表', '/v1/terminalGroupOpts/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10976', '创建分组Operation推送终端列表下载任务', '/v1/terminalGroupOpts/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10977', '获取分组推送RKI列表', '/v1/terminalGroupRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10978', '创建分组推送RKI', '/v1/terminalGroupRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10979', '获取分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10980', '删除分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10981', '重新推送终端分组RKI', '/v1/terminalGroupRkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10982', '激活分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10983', '重置分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10984', '挂起分组推送RKI', '/v1/terminalGroupRkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10985', '获取分组推送RKI终端列表', '/v1/terminalGroupRkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10986', '创建分组RKI推送终端列表下载任务', '/v1/terminalGroupRkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10987', '获取分组推送卸载应用列表', '/v1/terminalGroupUninstallApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10988', '创建分组推送卸载应用', '/v1/terminalGroupUninstallApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10989', '获取分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10990', '删除分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('10991', '重新推送终端分组卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10992', '激活分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10993', '重置分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10994', '挂起分组推送卸载应用', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10995', '获取分组推送卸载应用终端列表', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10996', '创建分组卸载应用推送终端列表下载任务', '/v1/terminalGroupUninstallApks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10997', '获取终端分组列表', '/v1/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('10998', '创建终端分组', '/v1/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('10999', '导入终端到终端分组', '/v1/terminalGroups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11000', '创建分组导入终端模板下载任务', '/v1/terminalGroups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11001', '获取终端分组', '/v1/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11002', '更新终端分组', '/v1/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11003', '删除终端分组', '/v1/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11004', '激活终端分组', '/v1/terminalGroups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11005', '停用终端分组', '/v1/terminalGroups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11006', '获取分组终端列表', '/v1/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11007', '添加分组终端', '/v1/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11008', '删除分组终端', '/v1/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11009', '获取终端推送RKI列表', '/v1/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11010', '创建终端推送RKI', '/v1/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11011', '获取终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11012', '删除终端推送RKI', '/v1/terminalRkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11013', '激活终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11014', '重置终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11015', '挂起终端推送RKI', '/v1/terminalRkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11016', '获取终端变量列表', '/v1/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11017', '创建终端变量', '/v1/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11018', '批量删除终端变量', '/v1/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11019', '导入终端变量', '/v1/terminalVariables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11020', '创建终端变量导入模板下载任务', '/v1/terminalVariables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11021', '查找终端变量支持的应用列表', '/v1/terminalVariables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11022', '查找终端变量已使用的应用列表', '/v1/terminalVariables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11023', '更新终端变量', '/v1/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11024', '删除终端变量', '/v1/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11025', '获取终端列表', '/v1/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11026', '创建终端', '/v1/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11027', '批量创建终端', '/v1/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11028', '批量激活终端', '/v1/terminals/batch/activation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11029', '批量删除终端', '/v1/terminals/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11030', '批量更新终端代理商', '/v1/terminals/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11031', '批量停用终端', '/v1/terminals/batch/suspension', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11032', '清除地理围栏中心点', '/v1/terminals/clear/safe/range/{terminalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11033', '复制终端', '/v1/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11034', '创建终端导出下载任务', '/v1/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11035', '批量添加终端到分组', '/v1/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11036', '导入终端', '/v1/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11037', '创建终端导入模板下载任务', '/v1/terminals/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11038', '创建终端日志下载任务', '/v1/terminals/logs/{terminalLogId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11039', '更改终端级别地理围栏半径', '/v1/terminals/radius', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11040', '添加终端安全位置', '/v1/terminals/safeRange', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11041', '根据序列号获取终端', '/v1/terminals/serialNo/{serialNo}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11042', '获取终端设备库存列表', '/v1/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11043', '批量创建终端设备库存', '/v1/terminals/stock/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11044', '批量分配库存终端', '/v1/terminals/stock/batch/assign', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11045', '批量删除库存终端', '/v1/terminals/stock/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11046', '创建库存终端导出下载任务', '/v1/terminals/stock/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11047', '导入库存终端', '/v1/terminals/stock/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11048', '创建库存终端导入模板下载任务', '/v1/terminals/stock/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11049', '根据序列号获取库存终端', '/v1/terminals/stock/serialNo/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11050', '获取库存终端', '/v1/terminals/stock/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11051', '更新库存终端', '/v1/terminals/stock/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11052', '删除库存终端', '/v1/terminals/stock/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11053', '获取终端', '/v1/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11054', '更新终端', '/v1/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11055', '删除终端', '/v1/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
('11056', '激活终端', '/v1/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11057', '向终端推送AirViewer安装命令', '/v1/terminals/{terminalId}/airviewer/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11058', '向终端推送AirViewer启动命令', '/v1/terminals/{terminalId}/airviewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11059', '获取终端的操作日志', '/v1/terminals/{terminalId}/audit/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11060', '创建终端商业数据下载任务', '/v1/terminals/{terminalId}/bizData/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11061', '获取终端商业数据', '/v1/terminals/{terminalId}/bizDatas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11062', '收集终端logcat', '/v1/terminals/{terminalId}/collect/logcat', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11063', 'web查询终端配置', '/v1/terminals/{terminalId}/configurations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11064', '获取终端详情信息', '/v1/terminals/{terminalId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11065', '停用终端', '/v1/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11066', '获取终端外接详情信息', '/v1/terminals/{terminalId}/extra/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11067', '获取终端硬件状态列表', '/v1/terminals/{terminalId}/hardware', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11068', '获取终端历史在线时长', '/v1/terminals/{terminalId}/history/online', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11069', '获取终端已安装应用列表', '/v1/terminals/{terminalId}/installedApks', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11070', '获取终端已安装应用列表统计', '/v1/terminals/{terminalId}/installedApks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11071', '创建终端已安装应用参数数据文件下载任务', '/v1/terminals/{terminalId}/installedApks/{apkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11072', '查看终端已安装应用参数', '/v1/terminals/{terminalId}/installedApks/{apkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11073', '获取终端已安装应用详情', '/v1/terminals/{terminalId}/installedApks/{installedApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11074', '卸载终端已安装应用', '/v1/terminals/{terminalId}/installedApks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11075', '获取终端已安装固件', '/v1/terminals/{terminalId}/installedFirmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11076', '获取终端位置信息', '/v1/terminals/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11077', '获取终端日志列表', '/v1/terminals/{terminalId}/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11078', '检查CheckUp应用版本', '/v1/terminals/{terminalId}/lowerCheckupVersion', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11079', '获取终端手动安装应用列表', '/v1/terminals/{terminalId}/manual/installedApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11080', '获取终端监控信息', '/v1/terminals/{terminalId}/monitors', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11081', '更新终端代理商', '/v1/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
('11082', '推送终端命令', '/v1/terminals/{terminalId}/operation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11083', 'get terminal ped key injection status', '/v1/terminals/{terminalId}/ped/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11084', '向终端推送CheckUp应用', '/v1/terminals/{terminalId}/push/checkup', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11085', '获取后端给终端推送命令记录列表', '/v1/terminals/{terminalId}/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11086', '收集终端详情(硬件等)', '/v1/terminals/{terminalId}/refresh/terminalDetail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11087', '更新终端是否允许远程换机配置', '/v1/terminals/{terminalId}/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11088', '获取终端的换机日志', '/v1/terminals/{terminalId}/replace/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11089', '还原终端数据', '/v1/terminals/{terminalId}/restore', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
('11090', '获取终端安全位置信息', '/v1/terminals/{terminalId}/safeRange', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11091', '获取终端流量', '/v1/terminals/{terminalId}/traffic', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11092', '查询所有用户列表', '/v1/users', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11093', '更新用户信息', '/v1/users', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11094', '查询用户有开发者权限的应用市场', '/v1/users/developer/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11095', '设置开发者收款帐户', '/v1/users/developer/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11096', '获取当前用户开发者信息', '/v1/users/developers/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
('11097', '创建用户导出下载任务', '/v1/users/export', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11098', '设置应用市场收款帐户', '/v1/users/market/receivable/account', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11099', 'List all configuration (notification)', '/v1/users/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11100', 'Update a configuration (notification)', '/v1/users/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11101', 'Publish global notification', '/v1/users/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11102', 'List messages (notification)', '/v1/users/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11103', 'Update a set of messages as read (notification)', '/v1/users/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11104', 'Delete a set of messages (notification)', '/v1/users/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11105', 'Update all messages as read (notification)', '/v1/users/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11106', 'Get message statistics (notification)', '/v1/users/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11107', 'Read specific messages in top reminder (notification)', '/v1/users/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11108', 'Read a message (notification)', '/v1/users/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11109', 'Delete a message (notification)', '/v1/users/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11110', 'Download message attachment (notification)', '/v1/users/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11111', 'View message details (notification)', '/v1/users/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11112', 'List all subscribed topics (notification)', '/v1/users/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11113', 'Unsubscribe all topics (notification)', '/v1/users/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11114', 'Get subscription info if subscribed (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11115', 'Subscribe a topic (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11116', 'Unsubscribe a topic (notification)', '/v1/users/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11117', '获取当前用户OTP', '/v1/users/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11118', '关闭当前用户OTP', '/v1/users/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11119', '启用当前用户OTP', '/v1/users/otp/activation', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11120', '重置用户OTP的backupCode', '/v1/users/otp/backupCode', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11121', '获取当前用户OTP二维码图片', '/v1/users/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11122', '更改密码', '/v1/users/password/change', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11123', '验证当前用户密码', '/v1/users/password/validate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11124', '获取当前用户信息', '/v1/users/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11125', '查询用户有代理商权限的应用市场', '/v1/users/reseller/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11126', '发送用户更改邮箱邮件', '/v1/users/resetEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11127', '查询用户绑定的设备所属的应用市场列表', '/v1/users/terminal/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11128', '用户同意协议', '/v1/users/user/agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11129', '获取用户信息', '/v1/users/{userId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11130', '全局市场激活用户', '/v1/users/{userId}/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11131', '更改邮箱', '/v1/users/{userId}/changeEmail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11132', '全局市场停用用户', '/v1/users/{userId}/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11133', '用户更改密码', '/v1/users/{userId}/resetPassword', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11134', '移除用户所有角色', '/v1/users/{userId}/roles', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11135', '移除用户角色', '/v1/users/{userId}/roles/{roleId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11136', 'appscan get apk downloadUrl from pax', '/v1/vas/appscan/download-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11137', 'Update push server list', '/v1/vas/cloudmsg/pushServer', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11138', 'Batch Notify terminal online/offline', '/v1/vas/cloudmsg/terminalOnlineStatus/batch', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11139', 'getDcCodeByMarketId', '/v1/vas/dc-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11140', 'getDcList', '/v1/vas/dcs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11141', 'Search app list by user market admin role', '/v1/vas/insight/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11142', 'Search app list by user developer role', '/v1/vas/insight/apps/develop', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11143', 'allow app sync bizData', '/v1/vas/insight/apps/{appId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11144', 'control app sync bizData', '/v1/vas/insight/apps/{appId}/bizdata/control', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11145', 'Search merchant page by user', '/v1/vas/insight/merchants', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11146', 'Search reseller page by user', '/v1/vas/insight/resellers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11147', 'Search market', '/v1/vas/insight/sync/markets', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11148', 'Search merchant', '/v1/vas/insight/sync/merchants', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11149', 'Search reseller', '/v1/vas/insight/sync/resellers', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11150', 'Search terminal', '/v1/vas/insight/sync/terminals', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11151', 'Search terminal page by user', '/v1/vas/insight/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11152', 'Add insight user detail', '/v1/vas/insight/users', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11153', 'Get paxstore role list by request', '/v1/vas/insight/users/role/all', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11154', 'Get paxstore role list by request', '/v1/vas/insight/users/role/scope', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11155', 'Get one organization all role user list', '/v1/vas/insight/users/roles', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11156', 'getMarketServiceSetting', '/v1/vas/market-svc-setting/{marketId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11157', 'getMarketsByServiceType', '/v1/vas/platform/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11158', 'Enable service callback', '/v1/vas/platform/service', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11159', 'Disable service callback', '/v1/vas/platform/service', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
('11160', 'enableVasGlobally', '/v1/vas/platform/service/global', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
('11161', 'getUserDetail', '/v1/vas/user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11162', 'getNotificationCount', '/v1/vas/user-notifications', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
('11163', 'publishNotification', '/v1/vas/user-notifications', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
('11164', 'getVersion', '/v1/vas/version', 'GET', null, '0', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
('1', '10099'),
('1', '10100'),
('1', '10101'),
('1', '10102'),
('1', '10537'),
('1', '10600'),
('1', '11093'),
('1', '11094'),
('1', '11095'),
('1', '11096'),
('1', '11097'),
('1', '11098'),
('1', '11099'),
('1', '11100'),
('1', '11101'),
('1', '11102'),
('1', '11103'),
('1', '11104'),
('1', '11105'),
('1', '11106'),
('1', '11107'),
('1', '11108'),
('1', '11109'),
('1', '11110'),
('1', '11111'),
('1', '11112'),
('1', '11113'),
('1', '11114'),
('1', '11115'),
('1', '11116'),
('1', '11117'),
('1', '11118'),
('1', '11119'),
('1', '11120'),
('1', '11121'),
('1', '11122'),
('1', '11123'),
('1', '11124'),
('1', '11125'),
('1', '11126'),
('1', '11127'),
('1', '11128'),
('1', '11133'),

('2', '10432'),
('2', '10433'),
('2', '10434'),
('2', '10435'),
('2', '10436'),
('2', '10437'),
('2', '10438'),
('2', '10439'),
('2', '10440'),
('2', '10441'),
('2', '10442'),
('2', '10443'),
('2', '10444'),
('2', '10445'),
('2', '10446'),
('2', '10447'),
('2', '10448'),
('2', '10449'),
('2', '10450'),
('2', '10451'),
('2', '10452'),
('2', '10453'),
('2', '10454'),
('2', '10455'),
('2', '10456'),
('2', '10457'),
('2', '10458'),
('2', '10459'),
('2', '10460'),
('2', '10461'),
('2', '10462'),
('2', '10463'),
('2', '10464'),
('2', '10465'),
('2', '10466'),
('2', '10467'),
('2', '10468'),
('2', '10469'),
('2', '10470'),
('2', '10471'),
('2', '10472'),
('2', '10473'),
('2', '10474'),
('2', '10475'),
('2', '10476'),
('2', '10477'),
('2', '10478'),
('2', '10479'),
('2', '10480'),
('2', '10481'),
('2', '10482'),
('2', '10483'),
('2', '10484'),
('2', '10485'),
('2', '10486'),
('2', '10487'),
('2', '10488'),
('2', '10489'),
('2', '10490'),
('2', '10491'),
('2', '10492'),
('2', '10493'),
('2', '10494'),
('2', '10495'),
('2', '10496'),
('2', '10497'),
('2', '10498'),
('2', '10499'),
('2', '10500'),
('2', '10501'),
('2', '10502'),
('2', '10503'),
('2', '10504'),
('2', '10505'),
('2', '10506'),
('2', '10507'),
('2', '10508'),
('2', '10509'),
('2', '10510'),
('2', '10511'),
('2', '10512'),
('2', '10513'),
('2', '10514'),
('2', '10515'),
('2', '10516'),
('2', '10517'),
('2', '10518'),
('2', '10519'),
('2', '10520'),
('2', '10521'),
('2', '10522'),
('2', '10523'),
('2', '10524'),
('2', '10525'),
('2', '10526'),
('2', '10527'),
('2', '10528'),
('2', '10529'),
('2', '10530'),
('2', '10531'),
('2', '10532'),
('2', '10533'),
('2', '10534'),
('2', '10538'),
('2', '10689'),
('2', '11129'),

('3', '10164'),
('3', '10165'),
('3', '10166'),
('3', '10167'),
('3', '10168'),
('3', '10169'),
('3', '10170'),
('3', '10171'),
('3', '10172'),
('3', '10173'),
('3', '10174'),
('3', '10175'),
('3', '10715'),
('3', '10716'),
('3', '10717'),
('3', '10718'),
('3', '10719'),
('3', '10720'),
('3', '10721'),
('3', '10722'),
('3', '10774'),
('3', '10775'),
('3', '10776'),
('3', '10777'),
('3', '10778'),
('3', '10779'),
('3', '10780'),
('3', '10781'),
('3', '10790'),
('3', '10791'),
('3', '10792'),
('3', '10819'),
('3', '10820'),
('3', '10821'),
('3', '10822'),
('3', '10823'),
('3', '10824'),
('3', '10825'),
('3', '10826'),

('4', '10003'),
('4', '10004'),
('4', '10005'),
('4', '10006'),
('4', '10007'),
('4', '10008'),
('4', '10009'),
('4', '10010'),
('4', '10011'),
('4', '10833'),
('4', '10834'),
('4', '10835'),
('4', '10836'),
('4', '10837'),
('4', '10838'),
('4', '10839'),
('4', '10840'),
('4', '10841'),
('4', '10842'),
('4', '10843'),
('4', '10844'),
('4', '10845'),
('4', '10846'),
('4', '10847'),
('4', '10848'),
('4', '10849'),
('4', '10850'),
('4', '10851'),
('4', '10852'),
('4', '10853'),
('4', '10854'),
('4', '10855'),
('4', '10856'),
('4', '10857'),
('4', '10858'),
('4', '10859'),
('4', '10860'),
('4', '10861'),
('4', '10862'),
('4', '10863'),
('4', '10864'),
('4', '10865'),
('4', '10866'),
('4', '10867'),
('4', '10868'),
('4', '10870'),
('4', '10871'),
('4', '10872'),
('4', '10873'),
('4', '10874'),
('4', '10875'),
('4', '10876'),
('4', '10877'),
('4', '10878'),
('4', '10879'),
('4', '10880'),
('4', '10881'),
('4', '10882'),
('4', '10883'),
('4', '10884'),
('4', '10885'),
('4', '10886'),
('4', '10887'),
('4', '10888'),
('4', '10893'),
('4', '10894'),
('4', '10895'),
('4', '10896'),
('4', '10897'),
('4', '10898'),
('4', '10899'),
('4', '10900'),
('4', '10901'),
('4', '10902'),
('4', '10903'),
('4', '10904'),
('4', '10905'),
('4', '10906'),
('4', '10907'),
('4', '10908'),
('4', '10909'),
('4', '10910'),
('4', '10911'),
('4', '10912'),
('4', '10913'),
('4', '10914'),
('4', '10915'),

('30', '10674'),
('30', '10675'),
('30', '10676'),
('30', '10677'),
('30', '10678'),
('30', '10679'),
('30', '10680'),
('30', '10681'),
('30', '10682'),
('30', '10683'),
('30', '10684'),
('30', '10685'),
('30', '10686'),
('30', '10687'),
('30', '10688'),
('30', '10832'),

('53', '10206'),
('53', '10208'),
('53', '10868'),
('53', '11025'),
('53', '11053'),
('53', '11064'),
('53', '11069'),
('53', '11076'),

('601', '10108'),
('601', '10109'),
('601', '10117'),
('601', '10121'),
('601', '10122'),
('601', '10126'),
('601', '10132'),
('601', '10144'),
('601', '10145'),
('601', '10146'),
('601', '10149'),
('601', '10197'),
('601', '10200'),
('601', '10201'),
('601', '10538'),
('601', '10540'),
('601', '10558'),
('601', '10645'),
('601', '10751'),
('601', '10755'),

('602', '10108'),
('602', '10109'),
('602', '10111'),
('602', '10117'),
('602', '10121'),
('602', '10126'),
('602', '10132'),
('602', '10133'),
('602', '10134'),
('602', '10144'),
('602', '10145'),
('602', '10147'),
('602', '10149'),
('602', '10151'),
('602', '10152'),
('602', '10197'),
('602', '10198'),
('602', '10199'),
('602', '10200'),
('602', '10201'),
('602', '10202'),
('602', '10203'),
('602', '10204'),
('602', '10205'),
('602', '10538'),
('602', '10540'),
('602', '10558'),
('602', '10645'),
('602', '10751'),
('602', '10755'),

('611', '10107'),
('611', '10108'),
('611', '10109'),
('611', '10112'),
('611', '10117'),
('611', '10121'),
('611', '10122'),
('611', '10126'),
('611', '10132'),
('611', '10143'),
('611', '10144'),
('611', '10145'),
('611', '10146'),
('611', '10149'),
('611', '10540'),
('611', '10626'),
('611', '10645'),
('611', '10689'),
('611', '10751'),
('611', '10755'),

('612', '10107'),
('612', '10108'),
('612', '10109'),
('612', '10110'),
('612', '10111'),
('612', '10112'),
('612', '10113'),
('612', '10117'),
('612', '10118'),
('612', '10119'),
('612', '10120'),
('612', '10121'),
('612', '10122'),
('612', '10123'),
('612', '10124'),
('612', '10125'),
('612', '10126'),
('612', '10127'),
('612', '10128'),
('612', '10129'),
('612', '10130'),
('612', '10131'),
('612', '10132'),
('612', '10133'),
('612', '10134'),
('612', '10135'),
('612', '10136'),
('612', '10137'),
('612', '10138'),
('612', '10139'),
('612', '10140'),
('612', '10141'),
('612', '10142'),
('612', '10143'),
('612', '10144'),
('612', '10145'),
('612', '10146'),
('612', '10147'),
('612', '10148'),
('612', '10149'),
('612', '10150'),
('612', '10151'),
('612', '10152'),
('612', '10153'),
('612', '10540'),
('612', '10626'),
('612', '10645'),
('612', '10689'),
('612', '10751'),
('612', '10755'),

('661', '10201'),
('661', '10540'),
('661', '10553'),
('661', '10554'),
('661', '10558'),
('661', '10626'),
('661', '10751'),
('661', '10755'),

('662', '10201'),
('662', '10202'),
('662', '10540'),
('662', '10553'),
('662', '10554'),
('662', '10555'),
('662', '10556'),
('662', '10557'),
('662', '10558'),
('662', '10559'),
('662', '10560'),
('662', '10561'),
('662', '10562'),
('662', '10563'),
('662', '10564'),
('662', '10565'),
('662', '10566'),
('662', '10626'),
('662', '10751'),
('662', '10755'),

('621', '10107'),
('621', '10117'),
('621', '10121'),
('621', '10122'),
('621', '10126'),
('621', '10132'),
('621', '10143'),
('621', '10145'),
('621', '10146'),
('621', '10149'),
('621', '10180'),
('621', '10181'),
('621', '10194'),
('621', '10196'),
('621', '10540'),
('621', '10751'),
('621', '10755'),

('622', '10107'),
('622', '10117'),
('622', '10118'),
('622', '10119'),
('622', '10120'),
('622', '10121'),
('622', '10122'),
('622', '10123'),
('622', '10124'),
('622', '10125'),
('622', '10126'),
('622', '10127'),
('622', '10128'),
('622', '10129'),
('622', '10130'),
('622', '10131'),
('622', '10132'),
('622', '10133'),
('622', '10134'),
('622', '10135'),
('622', '10136'),
('622', '10137'),
('622', '10138'),
('622', '10139'),
('622', '10140'),
('622', '10141'),
('622', '10142'),
('622', '10143'),
('622', '10144'),
('622', '10145'),
('622', '10146'),
('622', '10147'),
('622', '10148'),
('622', '10149'),
('622', '10150'),
('622', '10151'),
('622', '10152'),
('622', '10153'),
('622', '10180'),
('622', '10181'),
('622', '10182'),
('622', '10183'),
('622', '10184'),
('622', '10185'),
('622', '10186'),
('622', '10187'),
('622', '10188'),
('622', '10189'),
('622', '10190'),
('622', '10191'),
('622', '10192'),
('622', '10193'),
('622', '10194'),
('622', '10195'),
('622', '10196'),
('622', '10540'),
('622', '10751'),
('622', '10755'),

('651', '10107'),
('651', '10108'),
('651', '10109'),
('651', '10112'),
('651', '10117'),
('651', '10121'),
('651', '10122'),
('651', '10126'),
('651', '10132'),
('651', '10133'),
('651', '10134'),
('651', '10143'),
('651', '10144'),
('651', '10145'),
('651', '10146'),
('651', '10149'),
('651', '10151'),
('651', '10152'),
('651', '10540'),
('651', '10626'),
('651', '10751'),
('651', '10753'),
('651', '10755'),

('652', '10107'),
('652', '10108'),
('652', '10109'),
('652', '10112'),
('652', '10117'),
('652', '10121'),
('652', '10122'),
('652', '10126'),
('652', '10132'),
('652', '10133'),
('652', '10134'),
('652', '10143'),
('652', '10144'),
('652', '10145'),
('652', '10146'),
('652', '10149'),
('652', '10151'),
('652', '10152'),
('652', '10540'),
('652', '10626'),
('652', '10751'),
('652', '10753'),
('652', '10755'),

('711', '10108'),
('711', '10109'),
('711', '10117'),
('711', '10126'),
('711', '10132'),
('711', '10145'),
('711', '10149'),
('711', '10159'),
('711', '10160'),
('711', '10161'),
('711', '10163'),
('711', '10164'),
('711', '10166'),
('711', '10168'),
('711', '10170'),
('711', '10172'),
('711', '10174'),
('711', '10177'),
('711', '10178'),
('711', '10223'),
('711', '10354'),
('711', '10366'),
('711', '10368'),
('711', '10369'),
('711', '10370'),
('711', '10371'),
('711', '10374'),
('711', '10375'),
('711', '10390'),
('711', '10538'),
('711', '10540'),
('711', '10553'),
('711', '10558'),
('711', '10645'),
('711', '10650'),
('711', '10655'),
('711', '10656'),
('711', '10659'),
('711', '10661'),
('711', '10664'),
('711', '10665'),
('711', '10670'),
('711', '10689'),
('711', '10695'),
('711', '10751'),
('711', '10753'),
('711', '10754'),
('711', '10755'),
('711', '10756'),
('711', '10757'),
('711', '10762'),
('711', '10763'),
('711', '10767'),
('711', '10769'),
('711', '10770'),
('711', '10832'),
('711', '10916'),
('711', '10918'),
('711', '10921'),
('711', '10922'),
('711', '10924'),
('711', '10928'),
('711', '10930'),
('711', '10939'),
('711', '10943'),
('711', '10945'),
('711', '10948'),
('711', '10950'),
('711', '10951'),
('711', '10955'),
('711', '10956'),
('711', '10959'),
('711', '10965'),
('711', '10966'),
('711', '10976'),
('711', '10979'),
('711', '10985'),
('711', '10986'),
('711', '10996'),
('711', '10997'),
('711', '11001'),
('711', '11006'),
('711', '11009'),
('711', '11011'),
('711', '11016'),
('711', '11021'),
('711', '11022'),
('711', '11025'),
('711', '11034'),
('711', '11038'),
('711', '11041'),
('711', '11049'),
('711', '11053'),
('711', '11059'),
('711', '11061'),
('711', '11062'),
('711', '11063'),
('711', '11064'),
('711', '11066'),
('711', '11067'),
('711', '11068'),
('711', '11069'),
('711', '11070'),
('711', '11071'),
('711', '11072'),
('711', '11073'),
('711', '11075'),
('711', '11076'),
('711', '11077'),
('711', '11078'),
('711', '11079'),
('711', '11080'),
('711', '11083'),
('711', '11085'),
('711', '11086'),
('711', '11088'),
('711', '11090'),
('711', '11091'),

('712', '10108'),
('712', '10109'),
('712', '10117'),
('712', '10126'),
('712', '10132'),
('712', '10145'),
('712', '10149'),
('712', '10159'),
('712', '10160'),
('712', '10161'),
('712', '10163'),
('712', '10164'),
('712', '10165'),
('712', '10166'),
('712', '10167'),
('712', '10168'),
('712', '10169'),
('712', '10170'),
('712', '10171'),
('712', '10172'),
('712', '10173'),
('712', '10174'),
('712', '10175'),
('712', '10176'),
('712', '10177'),
('712', '10178'),
('712', '10179'),
('712', '10223'),
('712', '10354'),
('712', '10366'),
('712', '10367'),
('712', '10368'),
('712', '10369'),
('712', '10370'),
('712', '10371'),
('712', '10372'),
('712', '10373'),
('712', '10374'),
('712', '10375'),
('712', '10390'),
('712', '10538'),
('712', '10540'),
('712', '10553'),
('712', '10558'),
('712', '10645'),
('712', '10650'),
('712', '10651'),
('712', '10652'),
('712', '10653'),
('712', '10654'),
('712', '10655'),
('712', '10656'),
('712', '10657'),
('712', '10658'),
('712', '10659'),
('712', '10660'),
('712', '10661'),
('712', '10662'),
('712', '10663'),
('712', '10664'),
('712', '10665'),
('712', '10666'),
('712', '10667'),
('712', '10668'),
('712', '10669'),
('712', '10670'),
('712', '10671'),
('712', '10672'),
('712', '10673'),
('712', '10689'),
('712', '10695'),
('712', '10751'),
('712', '10752'),
('712', '10753'),
('712', '10754'),
('712', '10755'),
('712', '10756'),
('712', '10757'),
('712', '10758'),
('712', '10759'),
('712', '10760'),
('712', '10761'),
('712', '10762'),
('712', '10763'),
('712', '10764'),
('712', '10765'),
('712', '10766'),
('712', '10767'),
('712', '10768'),
('712', '10769'),
('712', '10770'),
('712', '10771'),
('712', '10772'),
('712', '10773'),
('712', '10832'),
('712', '10916'),
('712', '10917'),
('712', '10918'),
('712', '10919'),
('712', '10920'),
('712', '10921'),
('712', '10922'),
('712', '10923'),
('712', '10924'),
('712', '10925'),
('712', '10926'),
('712', '10927'),
('712', '10928'),
('712', '10929'),
('712', '10930'),
('712', '10931'),
('712', '10932'),
('712', '10933'),
('712', '10934'),
('712', '10939'),
('712', '10943'),
('712', '10945'),
('712', '10948'),
('712', '10950'),
('712', '10951'),
('712', '10955'),
('712', '10956'),
('712', '10959'),
('712', '10965'),
('712', '10966'),
('712', '10976'),
('712', '10979'),
('712', '10985'),
('712', '10986'),
('712', '10996'),
('712', '10997'),
('712', '10998'),
('712', '10999'),
('712', '11000'),
('712', '11001'),
('712', '11002'),
('712', '11003'),
('712', '11004'),
('712', '11005'),
('712', '11006'),
('712', '11007'),
('712', '11008'),
('712', '11009'),
('712', '11010'),
('712', '11011'),
('712', '11012'),
('712', '11013'),
('712', '11014'),
('712', '11015'),
('712', '11016'),
('712', '11017'),
('712', '11018'),
('712', '11019'),
('712', '11020'),
('712', '11021'),
('712', '11022'),
('712', '11023'),
('712', '11024'),
('712', '11025'),
('712', '11026'),
('712', '11027'),
('712', '11028'),
('712', '11029'),
('712', '11030'),
('712', '11031'),
('712', '11032'),
('712', '11033'),
('712', '11034'),
('712', '11035'),
('712', '11036'),
('712', '11037'),
('712', '11038'),
('712', '11039'),
('712', '11040'),
('712', '11041'),
('712', '11049'),
('712', '11053'),
('712', '11054'),
('712', '11055'),
('712', '11056'),
('712', '11057'),
('712', '11058'),
('712', '11059'),
('712', '11060'),
('712', '11061'),
('712', '11062'),
('712', '11063'),
('712', '11064'),
('712', '11065'),
('712', '11066'),
('712', '11067'),
('712', '11068'),
('712', '11069'),
('712', '11070'),
('712', '11071'),
('712', '11072'),
('712', '11073'),
('712', '11074'),
('712', '11075'),
('712', '11076'),
('712', '11077'),
('712', '11078'),
('712', '11079'),
('712', '11080'),
('712', '11081'),
('712', '11082'),
('712', '11083'),
('712', '11084'),
('712', '11085'),
('712', '11086'),
('712', '11087'),
('712', '11088'),
('712', '11089'),
('712', '11090'),
('712', '11091'),

('721', '10108'),
('721', '10109'),
('721', '10343'),
('721', '10354'),
('721', '10366'),
('721', '10368'),
('721', '10369'),
('721', '10370'),
('721', '10371'),
('721', '10374'),
('721', '10375'),
('721', '10538'),
('721', '10540'),
('721', '10553'),
('721', '10558'),
('721', '10659'),
('721', '10689'),
('721', '10695'),
('721', '10751'),
('721', '10755'),
('721', '10935'),
('721', '10939'),
('721', '10943'),
('721', '10945'),
('721', '10948'),
('721', '10950'),
('721', '10951'),
('721', '10955'),
('721', '10956'),
('721', '10957'),
('721', '10959'),
('721', '10965'),
('721', '10966'),
('721', '10967'),
('721', '10969'),
('721', '10975'),
('721', '10976'),
('721', '10977'),
('721', '10979'),
('721', '10985'),
('721', '10986'),
('721', '10987'),
('721', '10989'),
('721', '10995'),
('721', '10996'),
('721', '10997'),
('721', '11001'),
('721', '11006'),
('721', '11025'),

('722', '10108'),
('722', '10109'),
('722', '10343'),
('722', '10354'),
('722', '10366'),
('722', '10367'),
('722', '10368'),
('722', '10369'),
('722', '10370'),
('722', '10371'),
('722', '10372'),
('722', '10373'),
('722', '10374'),
('722', '10375'),
('722', '10538'),
('722', '10540'),
('722', '10553'),
('722', '10558'),
('722', '10659'),
('722', '10689'),
('722', '10695'),
('722', '10751'),
('722', '10755'),
('722', '10768'),
('722', '10935'),
('722', '10936'),
('722', '10937'),
('722', '10938'),
('722', '10939'),
('722', '10940'),
('722', '10941'),
('722', '10942'),
('722', '10943'),
('722', '10944'),
('722', '10945'),
('722', '10946'),
('722', '10947'),
('722', '10948'),
('722', '10949'),
('722', '10950'),
('722', '10951'),
('722', '10952'),
('722', '10953'),
('722', '10954'),
('722', '10955'),
('722', '10956'),
('722', '10957'),
('722', '10958'),
('722', '10959'),
('722', '10960'),
('722', '10961'),
('722', '10962'),
('722', '10963'),
('722', '10964'),
('722', '10965'),
('722', '10966'),
('722', '10967'),
('722', '10968'),
('722', '10969'),
('722', '10970'),
('722', '10971'),
('722', '10972'),
('722', '10973'),
('722', '10974'),
('722', '10975'),
('722', '10976'),
('722', '10977'),
('722', '10978'),
('722', '10979'),
('722', '10980'),
('722', '10981'),
('722', '10982'),
('722', '10983'),
('722', '10984'),
('722', '10985'),
('722', '10986'),
('722', '10987'),
('722', '10988'),
('722', '10989'),
('722', '10990'),
('722', '10991'),
('722', '10992'),
('722', '10993'),
('722', '10994'),
('722', '10995'),
('722', '10996'),
('722', '10997'),
('722', '10998'),
('722', '10999'),
('722', '11000'),
('722', '11001'),
('722', '11002'),
('722', '11003'),
('722', '11004'),
('722', '11005'),
('722', '11006'),
('722', '11007'),
('722', '11008'),
('722', '11025'),

('731', '10108'),
('731', '10109'),
('731', '10117'),
('731', '10126'),
('731', '10132'),
('731', '10145'),
('731', '10149'),
('731', '10366'),
('731', '10368'),
('731', '10369'),
('731', '10370'),
('731', '10371'),
('731', '10374'),
('731', '10375'),
('731', '10538'),
('731', '10540'),
('731', '10689'),
('731', '10751'),
('731', '10755'),

('732', '10108'),
('732', '10109'),
('732', '10117'),
('732', '10126'),
('732', '10132'),
('732', '10145'),
('732', '10149'),
('732', '10366'),
('732', '10367'),
('732', '10368'),
('732', '10369'),
('732', '10370'),
('732', '10371'),
('732', '10372'),
('732', '10373'),
('732', '10374'),
('732', '10375'),
('732', '10538'),
('732', '10540'),
('732', '10689'),
('732', '10751'),
('732', '10755'),

('741', '10538'),
('741', '10540'),
('741', '10541'),
('741', '10544'),
('741', '10626'),
('741', '10689'),
('741', '10692'),
('741', '10695'),

('742', '10538'),
('742', '10539'),
('742', '10540'),
('742', '10541'),
('742', '10542'),
('742', '10543'),
('742', '10544'),
('742', '10545'),
('742', '10546'),
('742', '10626'),
('742', '10689'),
('742', '10690'),
('742', '10691'),
('742', '10692'),
('742', '10693'),
('742', '10694'),
('742', '10695'),

('7511', '10538'),
('7511', '10540'),
('7511', '10553'),
('7511', '10554'),
('7511', '10557'),
('7511', '10558'),

('7512', '10538'),
('7512', '10540'),
('7512', '10553'),
('7512', '10554'),
('7512', '10555'),
('7512', '10556'),
('7512', '10557'),
('7512', '10558'),
('7512', '10559'),
('7512', '10560'),
('7512', '10562'),
('7512', '10566'),

('7521', '10197'),
('7521', '10200'),
('7521', '10201'),
('7521', '10538'),
('7521', '10540'),
('7521', '10553'),
('7521', '10554'),
('7521', '10557'),
('7521', '10558'),
('7521', '10626'),
('7521', '10751'),
('7521', '10755'),

('7522', '10197'),
('7522', '10198'),
('7522', '10199'),
('7522', '10200'),
('7522', '10201'),
('7522', '10202'),
('7522', '10203'),
('7522', '10204'),
('7522', '10205'),
('7522', '10538'),
('7522', '10540'),
('7522', '10553'),
('7522', '10554'),
('7522', '10556'),
('7522', '10557'),
('7522', '10558'),
('7522', '10560'),
('7522', '10561'),
('7522', '10562'),
('7522', '10563'),
('7522', '10564'),
('7522', '10565'),
('7522', '10626'),
('7522', '10751'),
('7522', '10755'),

('7611', '10538'),
('7611', '10689'),
('7611', '10796'),
('7611', '10800'),
('7611', '10802'),
('7611', '10805'),
('7611', '10809'),

('7612', '10538'),
('7612', '10689'),
('7612', '10796'),
('7612', '10800'),
('7612', '10801'),
('7612', '10802'),
('7612', '10803'),
('7612', '10804'),
('7612', '10805'),
('7612', '10806'),
('7612', '10807'),
('7612', '10809'),
('7612', '10810'),
('7612', '10814'),

('7621', '10626'),
('7621', '10797'),
('7621', '10799'),
('7621', '10800'),
('7621', '10809'),

('7622', '10205'),
('7622', '10626'),
('7622', '10797'),
('7622', '10799'),
('7622', '10800'),
('7622', '10807'),
('7622', '10808'),
('7622', '10809'),
('7622', '10811'),
('7622', '10812'),
('7622', '10813'),

('771', '10538'),
('771', '10540'),
('771', '10626'),
('771', '10689'),
('771', '10751'),
('771', '10755'),
('771', '11042'),
('771', '11046'),
('771', '11049'),
('771', '11050'),

('772', '10538'),
('772', '10540'),
('772', '10626'),
('772', '10689'),
('772', '10751'),
('772', '10755'),
('772', '11042'),
('772', '11043'),
('772', '11044'),
('772', '11045'),
('772', '11046'),
('772', '11047'),
('772', '11048'),
('772', '11049'),
('772', '11050'),
('772', '11051'),
('772', '11052'),

('911', '10112'),
('911', '10114'),
('911', '10209'),
('911', '10212'),
('911', '10215'),
('911', '10217'),
('911', '10221'),
('911', '10222'),
('911', '10223'),
('911', '10224'),
('911', '10225'),
('911', '10226'),
('911', '10227'),
('911', '10228'),
('911', '10229'),
('911', '10230'),
('911', '10231'),
('911', '10232'),
('911', '10233'),
('911', '10234'),
('911', '10235'),
('911', '10236'),
('911', '10237'),
('911', '10238'),
('911', '10239'),
('911', '10240'),
('911', '10241'),
('911', '10242'),
('911', '10243'),
('911', '10244'),
('911', '10245'),
('911', '10246'),
('911', '10247'),
('911', '10248'),
('911', '10249'),
('911', '10250'),
('911', '10251'),
('911', '10254'),
('911', '10255'),
('911', '10256'),
('911', '10257'),
('911', '10258'),
('911', '10259'),
('911', '10260'),
('911', '10261'),
('911', '10262'),
('911', '10266'),
('911', '10268'),
('911', '10269'),
('911', '10270'),
('911', '10271'),
('911', '10272'),
('911', '10273'),
('911', '10282'),
('911', '10284'),
('911', '10286'),
('911', '10288'),
('911', '10290'),
('911', '10292'),
('911', '10294'),
('911', '10296'),
('911', '10298'),
('911', '10303'),
('911', '10304'),
('911', '10307'),
('911', '10341'),
('911', '10343'),
('911', '10349'),
('911', '10352'),
('911', '10353'),
('911', '10354'),
('911', '10390'),
('911', '10392'),
('911', '10538'),
('911', '10645'),
('911', '10689'),
('911', '10757'),
('911', '10767'),
('911', '10769'),
('911', '10770'),

('912', '10112'),
('912', '10113'),
('912', '10114'),
('912', '10115'),
('912', '10116'),
('912', '10140'),
('912', '10141'),
('912', '10142'),
('912', '10209'),
('912', '10210'),
('912', '10211'),
('912', '10212'),
('912', '10213'),
('912', '10214'),
('912', '10215'),
('912', '10216'),
('912', '10217'),
('912', '10218'),
('912', '10219'),
('912', '10220'),
('912', '10221'),
('912', '10222'),
('912', '10223'),
('912', '10224'),
('912', '10225'),
('912', '10226'),
('912', '10227'),
('912', '10228'),
('912', '10229'),
('912', '10230'),
('912', '10231'),
('912', '10232'),
('912', '10233'),
('912', '10234'),
('912', '10235'),
('912', '10236'),
('912', '10237'),
('912', '10238'),
('912', '10239'),
('912', '10240'),
('912', '10241'),
('912', '10242'),
('912', '10243'),
('912', '10244'),
('912', '10245'),
('912', '10246'),
('912', '10247'),
('912', '10248'),
('912', '10249'),
('912', '10250'),
('912', '10251'),
('912', '10252'),
('912', '10253'),
('912', '10254'),
('912', '10255'),
('912', '10256'),
('912', '10257'),
('912', '10258'),
('912', '10259'),
('912', '10260'),
('912', '10261'),
('912', '10262'),
('912', '10263'),
('912', '10264'),
('912', '10265'),
('912', '10266'),
('912', '10267'),
('912', '10268'),
('912', '10269'),
('912', '10270'),
('912', '10271'),
('912', '10272'),
('912', '10273'),
('912', '10274'),
('912', '10275'),
('912', '10276'),
('912', '10277'),
('912', '10278'),
('912', '10279'),
('912', '10280'),
('912', '10281'),
('912', '10282'),
('912', '10283'),
('912', '10284'),
('912', '10285'),
('912', '10286'),
('912', '10287'),
('912', '10288'),
('912', '10289'),
('912', '10290'),
('912', '10291'),
('912', '10292'),
('912', '10293'),
('912', '10294'),
('912', '10295'),
('912', '10296'),
('912', '10297'),
('912', '10298'),
('912', '10299'),
('912', '10300'),
('912', '10301'),
('912', '10302'),
('912', '10303'),
('912', '10304'),
('912', '10305'),
('912', '10306'),
('912', '10307'),
('912', '10341'),
('912', '10343'),
('912', '10349'),
('912', '10350'),
('912', '10351'),
('912', '10352'),
('912', '10353'),
('912', '10354'),
('912', '10355'),
('912', '10390'),
('912', '10391'),
('912', '10392'),
('912', '10393'),
('912', '10394'),
('912', '10395'),
('912', '10538'),
('912', '10645'),
('912', '10646'),
('912', '10647'),
('912', '10648'),
('912', '10649'),
('912', '10689'),
('912', '10691'),
('912', '10757'),
('912', '10767'),
('912', '10768'),
('912', '10769'),
('912', '10770'),
('912', '10771'),
('912', '10772'),
('912', '10773'),

('92', '10706'),
('92', '10707'),
('92', '10708'),
('92', '10709'),
('92', '10710'),
('92', '10711'),
('92', '10712'),
('92', '10755'),
('92', '10782'),
('92', '10783'),
('92', '10784'),
('92', '10785'),
('92', '10786'),
('92', '10787'),
('92', '10788'),
('92', '10789'),
('92', '10793'),
('92', '10794'),
('92', '10795'),

('931', '10626'),
('931', '10784'),
('931', '11092'),
('931', '11097'),
('931', '11129'),
('931', '11131'),
('931', '11133'),

('932', '10626'),
('932', '10784'),
('932', '11092'),
('932', '11097'),
('932', '11129'),
('932', '11130'),
('932', '11131'),
('932', '11132'),
('932', '11133'),
('932', '11134'),
('932', '11135'),

('94', '10744'),
('94', '10745'),
('94', '10746'),
('94', '10747'),
('94', '10748'),
('94', '10749'),
('94', '10750'),

('95', '10162'),
('95', '10221'),
('95', '10222'),
('95', '10223'),
('95', '10224'),
('95', '10225'),

('96', '10368'),
('96', '10369'),
('96', '10538'),
('96', '10540'),
('96', '10659'),
('96', '10689'),
('96', '10724'),
('96', '10725'),
('96', '10726'),
('96', '10727'),
('96', '10728'),
('96', '10729'),
('96', '10730'),
('96', '10731'),
('96', '10732'),
('96', '10733'),
('96', '10734'),
('96', '10735'),
('96', '10736'),
('96', '10737'),
('96', '10738'),
('96', '10739'),
('96', '10740'),
('96', '10741'),
('96', '10742'),
('96', '10743'),
('96', '10751'),
('96', '10755'),

('97', '10318'),
('97', '10319'),
('97', '10320'),
('97', '10321'),
('97', '10322'),
('97', '10323'),
('97', '10324'),
('97', '10325'),
('97', '10326'),
('97', '10327'),
('97', '10328'),
('97', '10329'),
('97', '10330'),
('97', '10331'),
('97', '10725'),

('1011', '10251'),
('1011', '10341'),
('1011', '10428'),
('1011', '10538'),
('1011', '10626'),
('1011', '10628'),
('1011', '10629'),
('1011', '10630'),
('1011', '10631'),
('1011', '10632'),
('1011', '10633'),
('1011', '10634'),
('1011', '10636'),

('1012', '10251'),
('1012', '10341'),
('1012', '10428'),
('1012', '10538'),
('1012', '10626'),
('1012', '10627'),
('1012', '10628'),
('1012', '10629'),
('1012', '10630'),
('1012', '10631'),
('1012', '10632'),
('1012', '10633'),
('1012', '10634'),
('1012', '10635'),
('1012', '10636'),
('1012', '10637'),
('1012', '10638'),
('1012', '10639'),
('1012', '10640'),
('1012', '10641'),
('1012', '10642'),
('1012', '10643'),
('1012', '10644'),

('1021', '10251'),
('1021', '10307'),
('1021', '10341'),
('1021', '10343'),
('1021', '10346'),
('1021', '10422'),
('1021', '10423'),
('1021', '10425'),
('1021', '10600'),
('1021', '10827'),
('1021', '10829'),

('1022', '10251'),
('1022', '10252'),
('1022', '10307'),
('1022', '10340'),
('1022', '10341'),
('1022', '10342'),
('1022', '10343'),
('1022', '10344'),
('1022', '10345'),
('1022', '10346'),
('1022', '10347'),
('1022', '10348'),
('1022', '10422'),
('1022', '10423'),
('1022', '10424'),
('1022', '10425'),
('1022', '10426'),
('1022', '10600'),
('1022', '10601'),
('1022', '10827'),
('1022', '10828'),
('1022', '10829'),
('1022', '10830'),

('103', '10428'),
('103', '10429'),
('103', '10430'),
('103', '10431'),

('981', '10226'),
('981', '10227'),
('981', '10228'),
('981', '10229'),
('981', '10230'),
('981', '10231'),
('981', '10232'),
('981', '10233'),
('981', '10234'),
('981', '10235'),
('981', '10236'),
('981', '10237'),
('981', '10238'),
('981', '10239'),
('981', '10240'),
('981', '10241'),
('981', '10242'),
('981', '10243'),
('981', '10244'),
('981', '10245'),
('981', '10246'),
('981', '10247'),
('981', '10248'),
('981', '10249'),
('981', '10250'),
('981', '10251'),
('981', '10252'),
('981', '10253'),
('981', '10254'),
('981', '10255'),
('981', '10256'),
('981', '10257'),
('981', '10258'),
('981', '10259'),
('981', '10260'),
('981', '10261'),
('981', '10262'),
('981', '10263'),
('981', '10264'),
('981', '10265'),
('981', '10266'),
('981', '10267'),
('981', '10268'),
('981', '10269'),
('981', '10270'),
('981', '10271'),
('981', '10272'),
('981', '10273'),
('981', '10274'),
('981', '10275'),
('981', '10276'),
('981', '10277'),
('981', '10608'),
('981', '10609'),
('981', '10610'),
('981', '10611'),
('981', '10616'),
('981', '10626'),
('981', '10751'),
('981', '10755'),

('982', '10226'),
('982', '10227'),
('982', '10228'),
('982', '10229'),
('982', '10230'),
('982', '10231'),
('982', '10232'),
('982', '10233'),
('982', '10234'),
('982', '10235'),
('982', '10236'),
('982', '10237'),
('982', '10238'),
('982', '10239'),
('982', '10240'),
('982', '10241'),
('982', '10242'),
('982', '10243'),
('982', '10244'),
('982', '10245'),
('982', '10246'),
('982', '10247'),
('982', '10248'),
('982', '10249'),
('982', '10250'),
('982', '10251'),
('982', '10252'),
('982', '10253'),
('982', '10254'),
('982', '10255'),
('982', '10256'),
('982', '10257'),
('982', '10258'),
('982', '10259'),
('982', '10260'),
('982', '10261'),
('982', '10262'),
('982', '10263'),
('982', '10264'),
('982', '10265'),
('982', '10266'),
('982', '10267'),
('982', '10268'),
('982', '10269'),
('982', '10270'),
('982', '10271'),
('982', '10272'),
('982', '10273'),
('982', '10274'),
('982', '10275'),
('982', '10276'),
('982', '10277'),
('982', '10608'),
('982', '10609'),
('982', '10610'),
('982', '10611'),
('982', '10614'),
('982', '10615'),
('982', '10616'),
('982', '10617'),
('982', '10618'),
('982', '10626'),
('982', '10751'),
('982', '10755');
