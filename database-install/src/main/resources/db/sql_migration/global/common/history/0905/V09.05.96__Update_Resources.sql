DELETE FROM `PAX_PRIVILEGE_RESOURCE`;
DELETE FROM `PAX_RESOURCE`;

INSERT INTO `PAX_RESOURCE` ( `id`, `name`, `url`, `method`, `remarks`, `market_required`, `application_required`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES
  ('10000', 'error', '/error', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10001', 'error_3', '/error', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10002', 'error_2', '/error', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10003', 'error_5', '/error', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10004', 'error_6', '/error', 'OPTIONS', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10005', 'error_1', '/error', 'HEAD', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10006', 'error_4', '/error', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10007', 'sendMessageToTerminal', '/v1/3rd/cloudmsg', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10008', 'sendMessageByTag', '/v1/3rd/cloudmsg/bytag', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10009', 'sendMessageToSingleTerminal', '/v1/3rd/cloudmsg/single', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10010', 'getMessageArrivalRate', '/v1/3rd/cloudmsg/{msgIdentifier}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10011', 'submitApkInfo_1', '/v1/3rd/coverapp/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10012', 'categoryList', '/v1/3rd/coverapp/app/categoryList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10013', 'searchApps_4', '/v1/3rd/coverapp/app/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10014', 'findFactoryModelList_1', '/v1/3rd/coverapp/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10015', 'validateAppKey', '/v1/3rd/coverapp/validate/appKey', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10016', 'submitApkInfo', '/v1/3rd/developer/apk/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10017', 'editApk', '/v1/3rd/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10018', 'deleteApk_2', '/v1/3rd/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10019', 'submitApk_1', '/v1/3rd/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10020', 'getApp', '/v1/3rd/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10021', 'createApp_1', '/v1/3rd/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10022', 'deleteApp_2', '/v1/3rd/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10023', 'createApk', '/v1/3rd/developer/apps/{appId}/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10024', 'rkiCallback', '/v1/3rd/rki/callback', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10025', 'findApkParameters', '/v1/3rdsys/apkParameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10026', 'createApkTemplate', '/v1/3rdsys/apkParameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10027', 'getApkParameterById', '/v1/3rdsys/apkParameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10028', 'updateApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10029', 'deleteApkParameter_1', '/v1/3rdsys/apkParameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10030', 'searchApps_3', '/v1/3rdsys/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10031', 'searchEntityAttributes', '/v1/3rdsys/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10032', 'createEntityAttribute_1', '/v1/3rdsys/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10033', 'getEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10034', 'updateEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10035', 'deleteEntityAttribute_1', '/v1/3rdsys/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10036', 'updateEntityAttributeLabel_1', '/v1/3rdsys/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10037', 'verifyEstateBySerialNo', '/v1/3rdsys/estates/verify/{serialNo}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10038', 'findDataFromInsight', '/v1/3rdsys/goInsight/data/app-biz', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10039', 'findDataFromGoInsight', '/v1/3rdsys/goInsight/data/app-biz/{queryCode}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10040', 'createTables', '/v1/3rdsys/internal/audit-log/create-tables', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10041', 'migrationAuditLog', '/v1/3rdsys/internal/audit-log/migration/audit-log', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10042', 'migrationAuditTrail', '/v1/3rdsys/internal/audit-log/migration/audit-trail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10043', 'refreshNodes', '/v1/3rdsys/internal/audit-log/refresh-nodes', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10044', 'syncBillingListToZolonBillingCenter', '/v1/3rdsys/internal/billing', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10045', 'abandonBillings', '/v1/3rdsys/internal/billing/abandon', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10046', 'cancelWriteOffMarketBillingSummary', '/v1/3rdsys/internal/billing/cancel/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10047', 'syncSolutionDeveloperList', '/v1/3rdsys/internal/billing/developer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10048', 'syncFineTerminalListToZolonBillingCenter', '/v1/3rdsys/internal/billing/fine/terminals', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10049', 'searchMarkets_3', '/v1/3rdsys/internal/billing/markets', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10050', 'writeOffMarketBilling', '/v1/3rdsys/internal/billing/writeOff', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10051', 'updateBillingServiceSyncedStatus', '/v1/3rdsys/internal/billing/{billingSummaryId}/callback', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10052', 'updateBillingServicePrice', '/v1/3rdsys/internal/billing/{billingSummaryId}/price/sync', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10053', 'lockSet', '/v1/3rdsys/internal/cache/set', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10054', 'clearCache', '/v1/3rdsys/internal/clearCache', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10055', 'clearDeletedTerminals', '/v1/3rdsys/internal/clearDeletedTerminals', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10056', 'clearExpiredPendingTerminalActions', '/v1/3rdsys/internal/clearExpiredPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10057', 'clearGroupPendingTerminalActions', '/v1/3rdsys/internal/clearGroupPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10058', 'clearTerminalPendingTerminalActions', '/v1/3rdsys/internal/clearTerminalPendingTerminalActions', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10059', 'delLock', '/v1/3rdsys/internal/delLock', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10060', 'getAllDisabledRequests', '/v1/3rdsys/internal/disabled-request', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10061', 'addDisabledRequest', '/v1/3rdsys/internal/disabled-request', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10062', 'delDisabledRequest', '/v1/3rdsys/internal/disabled-request', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10063', 'clearDisabledRequest', '/v1/3rdsys/internal/disabled-request/all', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10064', 'finAllDeviceList', '/v1/3rdsys/internal/emm/device/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10065', 'getDeviceInfo', '/v1/3rdsys/internal/emm/device/{enterpriseId}/{originalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10066', 'deleteDeviceByDeviceName', '/v1/3rdsys/internal/emm/device/{enterpriseId}/{originalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10067', 'pageQueryEnrollmentToken', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10068', 'getEnrollmentTokenByName', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/{tokenId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10069', 'deleteEnrollmentToken', '/v1/3rdsys/internal/emm/enrollment/token/{enterpriseId}/{tokenId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10070', 'getEnterpriseByName', '/v1/3rdsys/internal/emm/enterprise/{enterpriseId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10071', 'updateEnterprise_1', '/v1/3rdsys/internal/emm/enterprise/{enterpriseId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10072', 'pageQueryEnterprise', '/v1/3rdsys/internal/emm/enterprise/{projectId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10073', 'findAllPolicyList', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/list', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10074', 'getPolicy', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/{policyName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10075', 'deletePolicyByPolicyName', '/v1/3rdsys/internal/emm/policy/{enterpriseId}/{policyName}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10076', 'generateApkPatchMd5', '/v1/3rdsys/internal/generateApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10077', 'generateClientApkPatchMd5', '/v1/3rdsys/internal/generateClientApkPatchMd5', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10078', 'getCache', '/v1/3rdsys/internal/getCache', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10079', 'getLock', '/v1/3rdsys/internal/getLock', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10080', 'sendDictionaryDataToGoInsight', '/v1/3rdsys/internal/insight/sync/dictionary-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10081', 'sendMarketDataToGoInsight', '/v1/3rdsys/internal/insight/sync/market-data', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10082', 'loadCacheByCacheName', '/v1/3rdsys/internal/load/cache', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10083', 'lock', '/v1/3rdsys/internal/lock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10084', 'findMarketApiBlackList', '/v1/3rdsys/internal/marketBlackApi', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10085', 'delMarketBlackApi', '/v1/3rdsys/internal/marketBlackApi/{marketBlackApiId}/market/{marketId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10086', 'createMarketBlackApi', '/v1/3rdsys/internal/marketBlackApi/{marketId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10087', 'getAuthCode', '/v1/3rdsys/internal/maxsearch/auth-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10088', 'resumeBinlogConsumer', '/v1/3rdsys/internal/maxsearch/binlog-consumer/resume', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10089', 'checkFullSyncProcess', '/v1/3rdsys/internal/maxsearch/full-sync', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10090', 'doFullSync', '/v1/3rdsys/internal/maxsearch/full-sync', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10091', 'doFullSyncAll', '/v1/3rdsys/internal/maxsearch/full-sync/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10092', 'getMaxSearchStats', '/v1/3rdsys/internal/maxsearch/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10093', 'configSyncCache', '/v1/3rdsys/internal/maxsearch/sync-cache-config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10094', 'migrationGroupFilteredAction', '/v1/3rdsys/internal/migrationGroupFilteredAction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10095', 'refreshApkParamTemplate', '/v1/3rdsys/internal/refreshApkParamTemplate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10096', 'refreshGroupActionCount', '/v1/3rdsys/internal/refreshGroupActionCount', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10097', 'refreshPushTaskDownloadTime', '/v1/3rdsys/internal/refreshPushTaskDownloadTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10098', 'refreshResellerInstalledApks', '/v1/3rdsys/internal/refreshResellerInstalledApks', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10099', 'refreshResellerPushedParamApk', '/v1/3rdsys/internal/refreshResellerPushedParamApk', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10100', 'refreshTerminalLastAccessTime', '/v1/3rdsys/internal/refreshTerminalLastAccessTime', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10101', 'refreshTerminalLastApkParam', '/v1/3rdsys/internal/refreshTerminalLastApkParam', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10102', 'refreshTerminalOnlineStatus', '/v1/3rdsys/internal/refreshTerminalOnlineStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10103', 'refreshTerminalStock', '/v1/3rdsys/internal/refreshTerminalStock', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10104', 'refreshUserMarket', '/v1/3rdsys/internal/refreshUserMarket', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10105', 'repairActivityStatus', '/v1/3rdsys/internal/repairActivityStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10106', 'repairGroupPushTask', '/v1/3rdsys/internal/repairGroupPushTask', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10107', 'repairMerchantMigrationStatus', '/v1/3rdsys/internal/repairMerchantMigrationStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10108', 'repairReportExecutionContextStatus', '/v1/3rdsys/internal/repairReportExecutionContextStatus', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10109', 'getSystemPropertyLog', '/v1/3rdsys/internal/system/property/log', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10110', 'findSystemProperties', '/v1/3rdsys/internal/systemProperty', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10111', 'createSystemProperty_1', '/v1/3rdsys/internal/systemProperty', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10112', 'deleteSystemProperty_1', '/v1/3rdsys/internal/systemProperty', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10113', 'resetSystemProperty', '/v1/3rdsys/internal/systemProperty/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10114', 'getTerminal_2', '/v1/3rdsys/internal/terminal', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10115', 'sendTerminalCommand', '/v1/3rdsys/internal/terminal/command', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10116', 'sendTerminalMessage', '/v1/3rdsys/internal/terminal/message', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10117', 'getTerminalPushHistory', '/v1/3rdsys/internal/terminal/push/history', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10118', 'resetTerminal', '/v1/3rdsys/internal/terminal/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10119', 'findMerchantVariablePage_1', '/v1/3rdsys/merchant/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10120', 'createMerchantVariable_1', '/v1/3rdsys/merchant/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10121', 'batchDeleteMerchantVariables_1', '/v1/3rdsys/merchant/variables/batch/deletion', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10122', 'updateMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10123', 'deleteMerchantVariable_1', '/v1/3rdsys/merchant/variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10124', 'getMerchantCategories', '/v1/3rdsys/merchantCategories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10125', 'createMerchantCategory_1', '/v1/3rdsys/merchantCategories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10126', 'batchCreateMerchantCategories', '/v1/3rdsys/merchantCategories/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10127', 'updateMerchantCategory_1', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10128', 'deleteCategory', '/v1/3rdsys/merchantCategories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10129', 'searchMerchant', '/v1/3rdsys/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10130', 'createMerchant_1', '/v1/3rdsys/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10131', 'getMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10132', 'updateMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10133', 'deleteMerchant_1', '/v1/3rdsys/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10134', 'activeMerchant', '/v1/3rdsys/merchants/{merchantId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10135', 'disableMerchant', '/v1/3rdsys/merchants/{merchantId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10136', 'replaceMerchantEmail_1', '/v1/3rdsys/merchants/{merchantId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10137', 'findParameterPushHistory', '/v1/3rdsys/parameter/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10138', 'searchReseller_1', '/v1/3rdsys/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10139', 'createReseller_1', '/v1/3rdsys/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10140', 'getReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10141', 'updateReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10142', 'deleteReseller_1', '/v1/3rdsys/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10143', 'activeReseller', '/v1/3rdsys/resellers/{resellerId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10144', 'disableReseller', '/v1/3rdsys/resellers/{resellerId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10145', 'replaceResellerEmail_1', '/v1/3rdsys/resellers/{resellerId}/replaceEmail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10146', 'searchResellerRkiKey', '/v1/3rdsys/resellers/{resellerId}/rki/template', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10147', 'getTerminalBySN', '/v1/3rdsys/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10148', 'updateTerminalBySN', '/v1/3rdsys/terminal', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10149', 'deleteTerminalBySN', '/v1/3rdsys/terminal', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10150', 'activateTerminalBySN', '/v1/3rdsys/terminal/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10151', 'getTerminalConfigBySN', '/v1/3rdsys/terminal/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10152', 'updateTerminalConfigBySN', '/v1/3rdsys/terminal/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10153', 'copyTerminalBySN', '/v1/3rdsys/terminal/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10154', 'disableTerminalBySN', '/v1/3rdsys/terminal/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10155', 'findTerminalGeoFenceWhiteList', '/v1/3rdsys/terminal/geofence/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10156', 'createTerminalGeoFenceWhiteList_1', '/v1/3rdsys/terminal/geofence/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10157', 'deleteTerminalGeoFenceWhiteList_1', '/v1/3rdsys/terminal/geofence/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10158', 'createTerminalsGroupBySN', '/v1/3rdsys/terminal/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10159', 'moveTerminalBySN', '/v1/3rdsys/terminal/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10160', 'pushTerminalActionBySN', '/v1/3rdsys/terminal/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10161', 'getTerminalPedBySN', '/v1/3rdsys/terminal/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10162', 'searchTerminalApkPage', '/v1/3rdsys/terminalApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10163', 'createTerminalApk', '/v1/3rdsys/terminalApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10164', 'suspendTerminalApk_1', '/v1/3rdsys/terminalApks/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10165', 'uninstallTerminalApk', '/v1/3rdsys/terminalApks/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10166', 'getTerminalApk_1', '/v1/3rdsys/terminalApks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10167', 'searchTerminalFmPage', '/v1/3rdsys/terminalFirmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10168', 'createTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10169', 'suspendTerminalFirmware_1', '/v1/3rdsys/terminalFirmwares/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10170', 'getTerminalFm', '/v1/3rdsys/terminalFirmwares/{terminalFmId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10171', 'searchTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10172', 'createTerminalGroupApks_1', '/v1/3rdsys/terminalGroupApks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10173', 'getTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10174', 'deleteTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10175', 'suspendTerminalGroupApk_1', '/v1/3rdsys/terminalGroupApks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10176', 'searchTerminalGroupRkiPage', '/v1/3rdsys/terminalGroupRki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10177', 'createGroupRki_1', '/v1/3rdsys/terminalGroupRki', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10178', 'getGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10179', 'suspendGroupRki_1', '/v1/3rdsys/terminalGroupRki/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10180', 'searchGroups_2', '/v1/3rdsys/terminalGroups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10181', 'createGroup_1', '/v1/3rdsys/terminalGroups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10182', 'searchTerminal_2', '/v1/3rdsys/terminalGroups/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10183', 'getGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10184', 'updateGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10185', 'deleteGroup_1', '/v1/3rdsys/terminalGroups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10186', 'activeGroup', '/v1/3rdsys/terminalGroups/{groupId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10187', 'disableGroup', '/v1/3rdsys/terminalGroups/{groupId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10188', 'searchGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10189', 'removeGroupTerminals_1', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10190', 'createGroupTerminals_2', '/v1/3rdsys/terminalGroups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10191', 'searchTerminalRkiPage', '/v1/3rdsys/terminalRkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10192', 'createTerminalRki_1', '/v1/3rdsys/terminalRkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10193', 'suspendTerminalRki_1', '/v1/3rdsys/terminalRkis/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10194', 'getTerminalRki_1', '/v1/3rdsys/terminalRkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10195', 'findTerminalVariableList', '/v1/3rdsys/terminalVariables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10196', 'createTerminalVariable_1', '/v1/3rdsys/terminalVariables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10197', 'batchDeleteTerminalVariables_1', '/v1/3rdsys/terminalVariables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10198', 'updateTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10199', 'deleteTerminalVariable_1', '/v1/3rdsys/terminalVariables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10200', 'findTerminals', '/v1/3rdsys/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10201', 'createTerminal_1', '/v1/3rdsys/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10202', 'activateTerminalInParam', '/v1/3rdsys/terminals/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10203', 'copyTerminal_1', '/v1/3rdsys/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10204', 'createTerminalsGroup', '/v1/3rdsys/terminals/groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10205', 'getTerminalNetwork', '/v1/3rdsys/terminals/network', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10206', 'getTerminal_1', '/v1/3rdsys/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10207', 'updateTerminal_2', '/v1/3rdsys/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10208', 'deleteTerminal_3', '/v1/3rdsys/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10209', 'activateTerminalInPath', '/v1/3rdsys/terminals/{terminalId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10210', 'getTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10211', 'updateTerminalConfig', '/v1/3rdsys/terminals/{terminalId}/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10212', 'disableTerminal', '/v1/3rdsys/terminals/{terminalId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10213', 'moveTerminal_1', '/v1/3rdsys/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10214', 'pushTerminalAction', '/v1/3rdsys/terminals/{terminalId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10215', 'getTerminalPed', '/v1/3rdsys/terminals/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10216', 'transfRequest', '/v1/3rdsys/upt/route-request', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10217', 'updateUptrillionSecurityInfo', '/v1/3rdsys/upt/security', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10218', 'getServiceAgreement', '/v1/account/agreement/service', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10219', 'getSystemAgreement', '/v1/account/agreement/system', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10220', 'getUser_6', '/v1/account/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10221', 'getMarket_6', '/v1/account/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10222', 'resetUserPassword_2', '/v1/account/public/{userId}/reset-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10223', 'deleteAccount', '/v1/account/user', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10224', 'changeUserPwd', '/v1/account/user/change-password', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10225', 'sendDeleteAccountCode', '/v1/account/user/delete', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10226', 'listUserConfig', '/v1/account/user/notification/configs', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10227', 'updateUserConfig', '/v1/account/user/notification/configs/{configId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10228', 'publishGlobalNotification', '/v1/account/user/notification/global', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10229', 'listMessages', '/v1/account/user/notification/messages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10230', 'readMessages', '/v1/account/user/notification/messages', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10231', 'deleteMessages', '/v1/account/user/notification/messages', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10232', 'readAllMessage', '/v1/account/user/notification/messages/all', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10233', 'deleteMessage', '/v1/account/user/notification/messages/{messageId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10234', 'downloadMessageAttachment', '/v1/account/user/notification/messages/{messageId}/attachment', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10235', 'viewMessageDetails_1', '/v1/account/user/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10236', 'listTopics', '/v1/account/user/notification/subscription', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10237', 'unsubscribeAllTopics', '/v1/account/user/notification/subscription', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10238', 'getTopicSubscription_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10239', 'subscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10240', 'unsubscribeTopic_4', '/v1/account/user/notification/subscription/{topicCategory}/{topicExternalId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10241', 'getOTP', '/v1/account/user/otp', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10242', 'activateOTP', '/v1/account/user/otp/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10243', 'disableOTP', '/v1/account/user/otp/disable', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10244', 'bindOTP', '/v1/account/user/otp/qrcode', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10245', 'resetOtpBackupCode', '/v1/account/user/otp/reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10246', 'getUserProfile', '/v1/account/user/profile', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10247', 'updateUser', '/v1/account/user/profile', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10248', 'resetUserEmail', '/v1/account/user/reset-email', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10249', 'updateAllowSendUsageData', '/v1/account/user/send-usage', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10250', 'findActivityPage', '/v1/admin/activities', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10251', 'batchDeleteActivities', '/v1/admin/activities/batch', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10252', 'getActivity', '/v1/admin/activities/{activityId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10253', 'deleteActivity', '/v1/admin/activities/{activityId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10254', 'searchAlarm', '/v1/admin/alarm', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10255', 'getAlarmSetting', '/v1/admin/alarm/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10256', 'saveAlarmSetting', '/v1/admin/alarm/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10257', 'searchRoles_2', '/v1/admin/alarm/setting/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10258', 'searchRoleUsers_1', '/v1/admin/alarm/setting/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10259', 'findAlarmTypeList', '/v1/admin/alarm/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10260', 'getAlarmWidgets', '/v1/admin/alarm/widgets/digital', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10261', 'createExportAlarmDownloadTasks', '/v1/admin/alarm/widgets/{type}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10262', 'searchApps_2', '/v1/admin/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10263', 'getApkInfo_1', '/v1/admin/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10264', 'createApkDownloadTask', '/v1/admin/apps/apks/{apkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10265', 'findSpecificApkMarketPage', '/v1/admin/apps/apks/{apkId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10266', 'specificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10267', 'deleteSpecificApkMarket', '/v1/admin/apps/apks/{apkId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10268', 'findSpecificApkMarketAllListPage', '/v1/admin/apps/apks/{apkId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10269', 'createApkParamTemplateDownloadTask_1', '/v1/admin/apps/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10270', 'deleteApkParamTemplate_1', '/v1/admin/apps/apks/{apkId}/param-template', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10271', 'findSpecificApkResellerPage_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10272', 'specificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10273', 'deleteSpecificApkReseller_2', '/v1/admin/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10274', 'findSpecificApkResellerAllListPage_2', '/v1/admin/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10275', 'reSignApk_3', '/v1/admin/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10276', 'findApkSignatureList_3', '/v1/admin/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10277', 'getPendingApprovalAppCount', '/v1/admin/apps/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10278', 'getTopicSubscription_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10279', 'subscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10280', 'unsubscribeTopic_3', '/v1/admin/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10281', 'getAppInfo_1', '/v1/admin/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10282', 'deleteApp_1', '/v1/admin/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10283', 'onlineApp', '/v1/admin/apps/{appId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10284', 'searchApk_1', '/v1/admin/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10285', 'deleteApk_1', '/v1/admin/apps/{appId}/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10286', 'approveApp', '/v1/admin/apps/{appId}/apks/{apkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10287', 'downloadApk', '/v1/admin/apps/{appId}/apks/{apkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10288', 'offlineApk_1', '/v1/admin/apps/{appId}/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10289', 'onlineApk', '/v1/admin/apps/{appId}/apks/{apkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10290', 'addApkParamTemplate', '/v1/admin/apps/{appId}/apks/{apkId}/param-template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10291', 'rejectApp', '/v1/admin/apps/{appId}/apks/{apkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10292', 'updateApkReleaseNote', '/v1/admin/apps/{appId}/apks/{apkId}/release-note', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10293', 'updateApkModel', '/v1/admin/apps/{appId}/apks/{apkId}/update/model', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10294', 'updateAppAutoUpdate', '/v1/admin/apps/{appId}/auto-update/{autoUpdate}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10295', 'updateAppDeveloper', '/v1/admin/apps/{appId}/developer', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10296', 'offlineApp', '/v1/admin/apps/{appId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10297', 'updateAppDownloadAuthentication', '/v1/admin/apps/{appId}/download/authentication', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10298', 'saveAppEntityAttributeValueList', '/v1/admin/apps/{appId}/entity-attribute-value', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10299', 'getBizDataFromGoInsight_3', '/v1/admin/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10300', 'findSpecificAppMarketPage', '/v1/admin/apps/{appId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10301', 'specificMarketApp', '/v1/admin/apps/{appId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10302', 'deleteSpecificAppMarket', '/v1/admin/apps/{appId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10303', 'findSpecificAppMarketAllListPage', '/v1/admin/apps/{appId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10304', 'findSpecificAppMerchantCategoryPage', '/v1/admin/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10305', 'specificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10306', 'deleteSpecificAppMerchantCategory_1', '/v1/admin/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10307', 'findSpecificAppResellerPage_2', '/v1/admin/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10308', 'specificAppReseller_2', '/v1/admin/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10309', 'deleteSpecificResellerApp_1', '/v1/admin/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10310', 'findSpecificAppResellerAllListPage_2', '/v1/admin/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10311', 'resumeApp', '/v1/admin/apps/{appId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10312', 'getAppSettingVo_2', '/v1/admin/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10313', 'getAppVasSettingVo_3', '/v1/admin/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10314', 'updateAppVisualScope', '/v1/admin/apps/{appId}/visual', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10315', 'searchAuthLog', '/v1/admin/audit-log/auth', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10316', 'exportAuthLog', '/v1/admin/audit-log/auth/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10317', 'searchOperationLog', '/v1/admin/audit-log/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10318', 'exportAuditLog', '/v1/admin/audit-log/operations/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10319', 'getAuditLogParamDetail', '/v1/admin/audit-log/operations/{auditId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10320', 'getExistAuditTypes', '/v1/admin/audit-log/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10321', 'findClientApp', '/v1/admin/client-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10322', 'findClientApk', '/v1/admin/client-apps-approval/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10323', 'findClientAppFactory', '/v1/admin/client-apps-approval/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10324', 'findFirmwares', '/v1/admin/client-apps-approval/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10325', 'approveClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10326', 'updateClientApkModel', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/model/update', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10327', 'offlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10328', 'onlineClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10329', 'rejectClientApk', '/v1/admin/client-apps-approval/{clientAppId}/client-apks/{clientApkId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10330', 'getClientApk', '/v1/admin/client-apps-common/client-apks/{clientApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10331', 'createClientApkDownloadTask', '/v1/admin/client-apps-common/client-apks/{clientApkId}/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10332', 'deleteClientApk', '/v1/admin/client-apps-common/{clientAppId}/client-apks/{clientApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10333', 'findClientApkFirmwarePage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10334', 'createClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10335', 'updateClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10336', 'removeClientApkFirmware', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/firmwares/{clientApkFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10337', 'findClientMarketPage', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10338', 'getClientMarketSummary', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10339', 'saveClientPublishAmount', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-amount', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10340', 'addGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10341', 'removeGlobalApkPublish', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10342', 'updateClientApkPublishRange', '/v1/admin/client-apps-publish/client-apks/{clientApkId}/publish-range', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10343', 'getClientApp', '/v1/admin/client-apps/{clientAppId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10344', 'findClientApkByAppId', '/v1/admin/client-apps/{clientAppId}/client-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10345', 'addNewClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10346', 'updateClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10347', 'updateClientApkFile', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10348', 'submitClientApk', '/v1/admin/client-apps/{clientAppId}/client-apks/{clientApkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10349', 'addFistClientApkFile', '/v1/admin/client-apps/{factoryId}/client-apks/file/first', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10350', 'getIOTPlatformAccessUrl', '/v1/admin/cloudservice/iot/access/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10351', 'ping', '/v1/admin/cloudservice/refresh/ping', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10352', 'getUrl', '/v1/admin/cloudservice/{serviceType}/access/url', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10353', 'findApkParameterPage_1', '/v1/admin/common/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10354', 'createApkParameterDataFileDownloadTask_1', '/v1/admin/common/app/apk/parameters/{apkParameterId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10355', 'getApkParameterSchemaInfo', '/v1/admin/common/app/apk/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10356', 'getApkDetailVo_4', '/v1/admin/common/app/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10357', 'findOnlineAppPage_1', '/v1/admin/common/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10358', 'findEntityAttributePage_1', '/v1/admin/common/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10359', 'findFactoryPage_1', '/v1/admin/common/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10360', 'findFactoryModelTree', '/v1/admin/common/factory/model/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10361', 'checkFile', '/v1/admin/common/file/check-file', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10362', 'uploadPart', '/v1/admin/common/file/chunk-upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10363', 'findFirmwarePage', '/v1/admin/common/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10364', 'createTerminalFirmwareFileDownloadTask', '/v1/admin/common/firmwares/file/{fmFileId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10365', 'getFirmwareDetailVo_2', '/v1/admin/common/firmwares/{firmwareId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10366', 'searchMarkets_2', '/v1/admin/common/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10367', 'findMerchantCategoryPage_1', '/v1/admin/common/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10368', 'findMerchantPage', '/v1/admin/common/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10369', 'findModelPage_3', '/v1/admin/common/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10370', 'findRkiTemplateKeyPage', '/v1/admin/common/reseller/{resellerId}/rki/template/keys', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10371', 'findResellerPage', '/v1/admin/common/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10372', 'findResellerTreePage', '/v1/admin/common/resellers/tree', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10373', 'getMarket_5', '/v1/admin/current-market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10374', 'getUser_5', '/v1/admin/current-user', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10375', 'searchPendingApps', '/v1/admin/dashboard/apps-pending', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10376', 'searchAppsTop10', '/v1/admin/dashboard/apps-top10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10377', 'getDashboardLayout', '/v1/admin/dashboard/layout', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10378', 'saveDashboard', '/v1/admin/dashboard/layout', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10379', 'searchMarkers_1', '/v1/admin/dashboard/map-markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10380', 'searchTerminalsByPlace_1', '/v1/admin/dashboard/map-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10381', 'getResellerProfile_2', '/v1/admin/dashboard/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10382', 'getTerminalNumberStatisticData_1', '/v1/admin/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10383', 'getTerminalNumberOfResellerData_1', '/v1/admin/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10384', 'getFmTerminalForWidget', '/v1/admin/dashboard/widgets/W13', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10385', 'exportFmTerminalOrgWidget', '/v1/admin/dashboard/widgets/W13/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10386', 'getClientTerminalWidget', '/v1/admin/dashboard/widgets/W14', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10387', 'exportClientTerminalWidget', '/v1/admin/dashboard/widgets/W14/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10388', 'loadWidgetModelTerminal_1', '/v1/admin/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10389', 'exportModelTerminalWidget', '/v1/admin/dashboard/widgets/W15/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10390', 'loadWidgetTerminalOffline_1', '/v1/admin/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10391', 'exportTerminalOfflineWidget', '/v1/admin/dashboard/widgets/W16/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10392', 'getFmTerminalWidget', '/v1/admin/dashboard/widgets/W18', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10393', 'exportFmTerminalWidget', '/v1/admin/dashboard/widgets/W18/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10394', 'loadClientTerminalWidget', '/v1/admin/dashboard/widgets/W19', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10395', 'downloadClientTerminalWidget', '/v1/admin/dashboard/widgets/W19/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10396', 'createExportTerminalsDownloadTask_1', '/v1/admin/dashboard/widgets/W20/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10397', 'getWidgetCardNumberActive', '/v1/admin/dashboard/widgets/W20/number/{type}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10398', 'getWidgetDigitalDisplaySetting', '/v1/admin/dashboard/widgets/W20/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10399', 'updateWidgetDigitalDisplay', '/v1/admin/dashboard/widgets/W20/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10400', 'getPukTerminalWidget', '/v1/admin/dashboard/widgets/W22', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10401', 'exportPUKTerminalWidget', '/v1/admin/dashboard/widgets/W22/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10402', 'findPageList_1', '/v1/admin/datasource/info', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10403', 'createDataSourceInfo', '/v1/admin/datasource/info', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10404', 'testConnection', '/v1/admin/datasource/info/testConnection', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10405', 'findById_1', '/v1/admin/datasource/info/{dataSourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10406', 'deleteById_1', '/v1/admin/datasource/info/{dataSourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10407', 'findPageList', '/v1/admin/datasource/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10408', 'createDataSourceMarket', '/v1/admin/datasource/market', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10409', 'findById', '/v1/admin/datasource/market/{configId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10410', 'deleteById', '/v1/admin/datasource/market/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10411', 'searchDevelopers', '/v1/admin/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10412', 'exportDevelopers', '/v1/admin/developers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10413', 'getDeveloper_1', '/v1/admin/developers/{developerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10414', 'updateDeveloper', '/v1/admin/developers/{developerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10415', 'deleteDeveloper', '/v1/admin/developers/{developerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10416', 'activeDeveloper3rdSysAccess_1', '/v1/admin/developers/{developerId}/3rd-system/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10417', 'disableDeveloper3rdSysAccess', '/v1/admin/developers/{developerId}/3rd-system/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10418', 'approveDeveloper', '/v1/admin/developers/{developerId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10419', 'updateAllowIndustrySolution', '/v1/admin/developers/{developerId}/industry-solution', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10420', 'approveGlobalDeveloperPayment', '/v1/admin/developers/{developerId}/pay/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10421', 'rejectDeveloperPayment', '/v1/admin/developers/{developerId}/pay/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10422', 'rejectDeveloper', '/v1/admin/developers/{developerId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10423', 'specificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10424', 'changeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/change', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10425', 'closeSpecificDeveloperReseller', '/v1/admin/developers/{developerId}/reseller/specific/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10426', 'resumeDeveloper', '/v1/admin/developers/{developerId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10427', 'findDeveloperSandboxTerminalPage', '/v1/admin/developers/{developerId}/sandbox/terminal', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10428', 'deleteSandboxTerminal', '/v1/admin/developers/{developerId}/sandbox/terminal/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10429', 'findDeveloperServices', '/v1/admin/developers/{developerId}/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10430', 'updateDeveloperSuperAdmin_1', '/v1/admin/developers/{developerId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10431', 'suspendDeveloper', '/v1/admin/developers/{developerId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10432', 'activeDeveloperUser', '/v1/admin/developers/{developerId}/user/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10433', 'findEnterpriseDeveloperUserPage', '/v1/admin/developers/{developerId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10434', 'findEmmAppPage', '/v1/admin/emm/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10435', 'updateEmmAppConfig', '/v1/admin/emm/apps/config/{configId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10436', 'removeEmmAppConfig', '/v1/admin/emm/apps/config/{configId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10437', 'createEmmApp', '/v1/admin/emm/apps/create', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10438', 'findEmmAppSubscriptionPage', '/v1/admin/emm/apps/subscription', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10439', 'createWebToken', '/v1/admin/emm/apps/webToken', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10440', 'getEmmAppDetail', '/v1/admin/emm/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10441', 'removeEmmApp', '/v1/admin/emm/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10442', 'findEmmAppConfigPage', '/v1/admin/emm/apps/{appId}/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10443', 'createEmmAppConfig', '/v1/admin/emm/apps/{appId}/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10444', 'findEmmAppConfigList', '/v1/admin/emm/apps/{appId}/configList', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10445', 'retrieveEmmAppPerms', '/v1/admin/emm/apps/{appId}/permissions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10446', 'subscribeEmmApp', '/v1/admin/emm/apps/{appId}/subscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10447', 'unSubscribeEmmApp', '/v1/admin/emm/apps/{appId}/unsubscribe', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10448', 'getMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10449', 'createMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10450', 'syncMerchantEmmPolicy', '/v1/admin/emm/policy/merchant/{merchantId}/sync', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10451', 'getResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10452', 'createResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10453', 'syncResellerEmmPolicy', '/v1/admin/emm/policy/reseller/{resellerId}/sync', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10454', 'findFactoryPage', '/v1/admin/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10455', 'createFactory', '/v1/admin/factories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10456', 'getSignatureProviderList', '/v1/admin/factories/signature/providers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10457', 'getFactoryDetail', '/v1/admin/factories/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10458', 'updateFactory', '/v1/admin/factories/{factoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10459', 'deleteFactory', '/v1/admin/factories/{factoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10460', 'findSpecificFactoryMarketPage', '/v1/admin/factories/{factoryId}/market/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10461', 'specificFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10462', 'deleteFactoryMarket', '/v1/admin/factories/{factoryId}/market/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10463', 'findSpecificFactoryMarketAllList', '/v1/admin/factories/{factoryId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10464', 'searchFirmware_2', '/v1/admin/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10465', 'searchFirmware_3', '/v1/admin/firmwares-approval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10466', 'listFirmwareFactory', '/v1/admin/firmwares-approval/factory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10467', 'findPendingStatistic', '/v1/admin/firmwares-approval/pendingStatistic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10468', 'getFirmware_1', '/v1/admin/firmwares-approval/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10469', 'approveFirmware', '/v1/admin/firmwares-approval/{firmwareId}/approve', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10470', 'specificMarketFirmware', '/v1/admin/firmwares-approval/{firmwareId}/market/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10471', 'findSpecificFirmwareMarketAllPage', '/v1/admin/firmwares-approval/{firmwareId}/market/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10472', 'updateFirmwareModel', '/v1/admin/firmwares-approval/{firmwareId}/model/update', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10473', 'offlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10474', 'onlineFirmware', '/v1/admin/firmwares-approval/{firmwareId}/online', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10475', 'rejectFirmware', '/v1/admin/firmwares-approval/{firmwareId}/reject', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10476', 'specificResellerFirmware_2', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10477', 'findFirmwareResellerSpecificAllPage', '/v1/admin/firmwares-approval/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10478', 'deleteFirmwareDiff', '/v1/admin/firmwares-common/diffFiles/{fmFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10479', 'deleteFirmware', '/v1/admin/firmwares-common/{firmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10480', 'uploadFirmwareDiffFile', '/v1/admin/firmwares-common/{firmwareId}/diffFiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10481', 'uploadFirmwareFile', '/v1/admin/firmwares/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10482', 'getFirmware', '/v1/admin/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10483', 'updateFirmware', '/v1/admin/firmwares/{firmwareId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10484', 'getFirmwareForEditPage', '/v1/admin/firmwares/{firmwareId}/edit-page', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10485', 'submitFirmware', '/v1/admin/firmwares/{firmwareId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10486', 'searchMarkers', '/v1/admin/geo-location/markers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10487', 'searchMarket', '/v1/admin/geo-location/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10488', 'getMarket_4', '/v1/admin/geo-location/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10489', 'getResellerProfile_1', '/v1/admin/geo-location/profile/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10490', 'searchReseller', '/v1/admin/geo-location/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10491', 'getReseller', '/v1/admin/geo-location/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10492', 'searchTerminalsByPlace', '/v1/admin/geo-location/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10493', 'getTerminalDetail', '/v1/admin/geo-location/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10494', 'searchMarkets_1', '/v1/admin/global/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10495', 'createMarket', '/v1/admin/global/markets', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10496', 'findRkiServerList', '/v1/admin/global/markets/rki-servers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10497', 'getMarketInfoSummary', '/v1/admin/global/markets/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10498', 'findAppNumDetail', '/v1/admin/global/markets/statistics/app-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10499', 'findDeveloperNumDetail', '/v1/admin/global/markets/statistics/developer-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10500', 'createExportStatisticsDownloadTask', '/v1/admin/global/markets/statistics/export/{type}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10501', 'findMarketNumDetail', '/v1/admin/global/markets/statistics/market-num-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10502', 'sendMarketTerminalReport', '/v1/admin/global/markets/terminal-report', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10503', 'getMarket_3', '/v1/admin/global/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10504', 'updateMarket', '/v1/admin/global/markets/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10505', 'deleteMarket', '/v1/admin/global/markets/{marketId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10506', 'updateMarketBillingSetting', '/v1/admin/global/markets/{marketId}/billing', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10507', 'getMarketIconStatus', '/v1/admin/global/markets/{marketId}/icon-status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10508', 'updateOverdueMarket', '/v1/admin/global/markets/{marketId}/overdue', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10509', 'getMarketPermissions', '/v1/admin/global/markets/{marketId}/permissions/{functionType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10510', 'updateMarketBillingPriceSetting', '/v1/admin/global/markets/{marketId}/price-setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10511', 'findMarketBillingPriceSettings', '/v1/admin/global/markets/{marketId}/price-settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10512', 'replaceMarketResellerEmail', '/v1/admin/global/markets/{marketId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10513', 'sendMarketActivateEmail', '/v1/admin/global/markets/{marketId}/resend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10514', 'resumeMarket', '/v1/admin/global/markets/{marketId}/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10515', 'getMarketServiceSetting', '/v1/admin/global/markets/{marketId}/service-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10516', 'getMarketSummary', '/v1/admin/global/markets/{marketId}/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10517', 'suspendMarket', '/v1/admin/global/markets/{marketId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10518', 'getReportMetadataForCreateAndUpdate', '/v1/admin/global/report/metadata', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10519', 'getReport_1', '/v1/admin/global/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10520', 'updateReport', '/v1/admin/global/report/{reportId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10521', 'activateReport', '/v1/admin/global/report/{reportId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10522', 'createDownloadBandFileTask', '/v1/admin/global/report/{reportId}/bandfile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10523', 'disableReport', '/v1/admin/global/report/{reportId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10524', 'createDownloadTaskForTemplateFile', '/v1/admin/global/report/{reportId}/templatefile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10525', 'findLauncherTemplatePage', '/v1/admin/launcher/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10526', 'createLauncherTemplate', '/v1/admin/launcher/templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10527', 'findResellerOnlineAppPage', '/v1/admin/launcher/templates/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10528', 'getResellerOnlineApkNameAndIcon_1', '/v1/admin/launcher/templates/reseller/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10529', 'getLauncherTemplate_2', '/v1/admin/launcher/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10530', 'updateLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10531', 'deleteLauncherTemplate', '/v1/admin/launcher/templates/{launcherTemplateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10532', 'activeMarket3rdSysAccess', '/v1/admin/market/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10533', 'getMarket3rdSysConfig', '/v1/admin/market/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10534', 'find3rdSysConfigIpPage_1', '/v1/admin/market/3rd-sys/config/ip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10535', 'deActiveMarket3rdSysAccess', '/v1/admin/market/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10536', 'add3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10537', 'update3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10538', 'delete3rdSysIpAddress', '/v1/admin/market/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10539', 'getMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10540', 'resetMarket3rdSysAccessSecret', '/v1/admin/market/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10541', 'find3rdSysWebHookPage_1', '/v1/admin/market/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10542', 'create3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10543', 'getWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10544', 'get3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10545', 'update3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10546', 'delete3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10547', 'findWebHookMessageHistory_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10548', 'test3rdSysWebHook_1', '/v1/admin/market/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10549', 'findAnnouncementPage', '/v1/admin/market/advance/announcement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10550', 'createAnnouncementNotification', '/v1/admin/market/advance/announcement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10551', 'publishAnnouncementNotification', '/v1/admin/market/advance/announcement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10552', 'getAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10553', 'updateAnnouncementNotification', '/v1/admin/market/advance/announcement/{announcementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10554', 'deleteAnnouncement', '/v1/admin/market/advance/announcement/{announcementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10555', 'findAppWhiteListPage_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10556', 'createAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10557', 'deleteAppWhiteList_1', '/v1/admin/market/advance/app-uninstall/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10558', 'findPage', '/v1/admin/market/advance/ip-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10559', 'createAccessIp', '/v1/admin/market/advance/ip-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10560', 'getAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10561', 'closeAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/close', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10562', 'openAccessIpStatus', '/v1/admin/market/advance/ip-whitelist/status/open', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10563', 'updateAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10564', 'deleteAccessIp', '/v1/admin/market/advance/ip-whitelist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10565', 'getMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10566', 'createMarketSensitiveWord', '/v1/admin/market/advance/sensitiveWord', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10567', 'deleteSensitiveWordId', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10568', 'createSensitiveWordFileDownloadTask', '/v1/admin/market/advance/sensitiveWord/{sensitiveWordId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10569', 'findAppBlackListPage_1', '/v1/admin/market/app-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10570', 'updateAppBlackList', '/v1/admin/market/app-blacklist', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10571', 'createAppBlackList', '/v1/admin/market/app-blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10572', 'deleteAppBlackList', '/v1/admin/market/app-blacklist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10573', 'findAppWhiteListPage', '/v1/admin/market/app-whitelist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10574', 'createAppWhiteList', '/v1/admin/market/app-whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10575', 'deleteAppWhiteList', '/v1/admin/market/app-whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10576', 'findEntityAttributePage', '/v1/admin/market/attributes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10577', 'createEntityAttribute', '/v1/admin/market/attributes', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10578', 'getEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10579', 'updateEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10580', 'deleteEntityAttribute', '/v1/admin/market/attributes/{attributeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10581', 'updateEntityAttributeLabel', '/v1/admin/market/attributes/{attributeId}/label', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10582', 'updateBillingServicePrice_1', '/v1/admin/market/billing/change/price/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10583', 'getCurrentBilling', '/v1/admin/market/billing/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10584', 'updateMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10585', 'getMarketBillingDefaultPrice', '/v1/admin/market/billing/defaultSettings/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10586', 'downloadGlobalTimePeriodBilling', '/v1/admin/market/billing/global/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10587', 'loadGlobalSingleMonthBill', '/v1/admin/market/billing/global/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10588', 'loadTimePeriodBilling', '/v1/admin/market/billing/global/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10589', 'loadTotalBillItemTimePeriod', '/v1/admin/market/billing/global/total/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10590', 'getUnreceivedAmount', '/v1/admin/market/billing/global/unreceived/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10591', 'findGlobalUnresolvedInvoices', '/v1/admin/market/billing/global/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10592', 'reRunBillingJob', '/v1/admin/market/billing/job', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10593', 'findOperationLog', '/v1/admin/market/billing/log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10594', 'findBillingServiceDetailLog', '/v1/admin/market/billing/log/detail/{batchId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10595', 'downloadPaymentBillHistory', '/v1/admin/market/billing/market/{marketId}/invoice/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10596', 'getMarketSingleMonthBilling', '/v1/admin/market/billing/market/{marketId}/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10597', 'getMarketTimePeriodBilling', '/v1/admin/market/billing/market/{marketId}/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10598', 'searchPayment', '/v1/admin/market/billing/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10599', 'checkPaymentStatus', '/v1/admin/market/billing/payment/check', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10600', 'confirmPayBilling', '/v1/admin/market/billing/payment/confirm', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10601', 'initPayment', '/v1/admin/market/billing/payment/init', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10602', 'sendBillingInvoice', '/v1/admin/market/billing/send/invoice/{billingSummaryId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10603', 'getMarketInvoice', '/v1/admin/market/billing/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10604', 'updateMarketInvoice', '/v1/admin/market/billing/settings', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10605', 'findBillingReceiveEmailList', '/v1/admin/market/billing/settings/email', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10606', 'createReceiveEmail', '/v1/admin/market/billing/settings/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10607', 'updateReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10608', 'deleteReceiveEmail', '/v1/admin/market/billing/settings/email/{emailId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10609', 'getSingleMonthBilling', '/v1/admin/market/billing/single', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10610', 'updateBillingStatusToAudit', '/v1/admin/market/billing/status/audit/{billingSummaryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10611', 'loadSummaryBillItemTimePeriod', '/v1/admin/market/billing/time/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10612', 'getUnPaidAmountPayable', '/v1/admin/market/billing/unPaid/amount', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10613', 'findHistoryUnresolvedInvoices', '/v1/admin/market/billing/unresolved/invoices', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10614', 'findMerchantCategoryPage', '/v1/admin/market/merchant-categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10615', 'createMerchantCategory', '/v1/admin/market/merchant-categories', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10616', 'batchDeleteMerchantCategories', '/v1/admin/market/merchant-categories/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10617', 'importMerchantCategory', '/v1/admin/market/merchant-categories/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10618', 'createMerchantCategoryImportTemplateDownloadTask', '/v1/admin/market/merchant-categories/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10619', 'updateMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10620', 'deleteMerchantCategory', '/v1/admin/market/merchant-categories/{merchantCategoryId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10621', 'specificModel2Market', '/v1/admin/market/model/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10622', 'findFactoryIncludeModelPage', '/v1/admin/market/model/settings/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10623', 'findModelPage_2', '/v1/admin/market/model/settings/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10624', 'getMarketSetting', '/v1/admin/market/settings', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10625', 'saveMarketSettings', '/v1/admin/market/settings', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10626', 'activateMarket', '/v1/admin/market/settings/activate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10627', 'findAgreementPage', '/v1/admin/market/settings/agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10628', 'createAgreement', '/v1/admin/market/settings/agreement', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10629', 'findAgreementSettingPage', '/v1/admin/market/settings/agreement/config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10630', 'saveAgreementSettings', '/v1/admin/market/settings/agreement/config', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10631', 'publishAgreement', '/v1/admin/market/settings/agreement/publish', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10632', 'updateAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10633', 'deleteAgreement', '/v1/admin/market/settings/agreement/{agreementId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10634', 'exportAgreementAgreedRecords', '/v1/admin/market/settings/agreement/{agreementId}/export', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10635', 'findFooter_1', '/v1/admin/market/settings/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10636', 'createMarketFooter', '/v1/admin/market/settings/footer', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10637', 'updateMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10638', 'deleteMarketFooter', '/v1/admin/market/settings/footer/{footerId}', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10639', 'sortMarketFooter', '/v1/admin/market/settings/footer/{footerId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10640', 'getMarketLimitConfig', '/v1/admin/market/settings/limitconfig', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10641', 'findRkiServerPage', '/v1/admin/market/settings/rki-servers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10642', 'getTIDSettings', '/v1/admin/market/settings/tid', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10643', 'updateTIDSetting', '/v1/admin/market/settings/tid', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10644', 'getMarketUiSettings_1', '/v1/admin/market/settings/ui', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10645', 'saveMarketUiSettings', '/v1/admin/market/settings/ui', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10646', 'saveMarketUiAdvanceSettings', '/v1/admin/market/settings/ui/advance', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10647', 'deleteMarketUiAdvanceSettings', '/v1/admin/market/settings/ui/advance', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10648', 'findOnlineAppPageForFeatured', '/v1/admin/market/settings/ui/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10649', 'findFeaturedApp_1', '/v1/admin/market/settings/ui/apps/featured', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10650', 'updateFeaturedAppSort', '/v1/admin/market/settings/ui/apps/featured/{featuredAppId}/sort', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10651', 'addFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10652', 'deleteFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10653', 'updateFeaturedApp', '/v1/admin/market/settings/ui/apps/{appId}/featured/{featuredAppId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('10654', 'getSignatureSetting', '/v1/admin/market/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10655', 'saveSignatureSetting', '/v1/admin/market/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10656', 'clearSignatureData', '/v1/admin/market/signature/data/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10657', 'findSignatureFactoryPage', '/v1/admin/market/signature/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10658', 'testSignatureConfigServer_1', '/v1/admin/market/signature/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10659', 'getSsoSetting', '/v1/admin/market/sso/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10660', 'updateSsoSetting', '/v1/admin/market/sso/settings', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10661', 'searchRoles_1', '/v1/admin/market/sso/settings/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10662', 'getTerminalBlacklist', '/v1/admin/market/terminal-blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10663', 'importTerminalBlacklist', '/v1/admin/market/terminal-blacklist/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10664', 'createTerminalImportTemplateDownloadTask_1', '/v1/admin/market/terminal-blacklist/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10665', 'deleteTerminalBlacklist', '/v1/admin/market/terminal-blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10666', 'findMarketVariablePage', '/v1/admin/market/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10667', 'createMarketVariable', '/v1/admin/market/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10668', 'batchDeleteTerminalVariables_2', '/v1/admin/market/variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10669', 'importMarketVariable', '/v1/admin/market/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10670', 'createMarketVariableImportTemplateDownloadTask', '/v1/admin/market/variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10671', 'findMarketVariableRelatedAppPage', '/v1/admin/market/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10672', 'findMarketVariableUsedAppPage', '/v1/admin/market/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10673', 'updateMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10674', 'deleteMarketVariable', '/v1/admin/market/variables/{marketVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10675', 'searchUsers_2', '/v1/admin/merchant/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10676', 'createExportUserDownloadTask_1', '/v1/admin/merchant/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10677', 'activeUser_1', '/v1/admin/merchant/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10678', 'sendActivateUserEmail_1', '/v1/admin/merchant/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10679', 'getUserMerchants', '/v1/admin/merchant/users/{userId}/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10680', 'resetUserPassword_1', '/v1/admin/merchant/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10681', 'findModelPage_1', '/v1/admin/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10682', 'createModel', '/v1/admin/models', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10683', 'getModelDetail', '/v1/admin/models/{modelId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10684', 'updateModel', '/v1/admin/models/{modelId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10685', 'deleteModel', '/v1/admin/models/{modelId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10686', 'findProtectedOperations', '/v1/admin/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10687', 'getUser_4', '/v1/admin/operations/user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10688', 'getProtectedOperation', '/v1/admin/operations/{key}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10689', 'closeOperation', '/v1/admin/operations/{key}/close', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10690', 'openOperation', '/v1/admin/operations/{key}/open', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10691', 'getOperationUsers', '/v1/admin/operations/{key}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10692', 'addOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10693', 'removeOperationUser', '/v1/admin/operations/{key}/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10694', 'getCodeLangConfigs', '/v1/admin/platform/configuration/codes/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10695', 'saveCodeLangConfig', '/v1/admin/platform/configuration/codes/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10696', 'getCodeTypes', '/v1/admin/platform/configuration/codes/setting/codeTypes', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10697', 'getCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10698', 'deleteCodeLangConfig', '/v1/admin/platform/configuration/codes/setting/{type}/{value}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10699', 'getLicense_1', '/v1/admin/platform/configuration/license', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10700', 'updateLicense', '/v1/admin/platform/configuration/license', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10701', 'loadLoginSettings', '/v1/admin/platform/configuration/login-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10702', 'savePwdPolicy_1', '/v1/admin/platform/configuration/login-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10703', 'loadMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10704', 'saveMailServiceConfig', '/v1/admin/platform/configuration/mail-config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10705', 'testMailServiceConfig', '/v1/admin/platform/configuration/mail-config/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10706', 'listAll', '/v1/admin/platform/configuration/oauth-client', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10707', 'add', '/v1/admin/platform/configuration/oauth-client', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10708', 'get', '/v1/admin/platform/configuration/oauth-client/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10709', 'update', '/v1/admin/platform/configuration/oauth-client/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10710', 'delete', '/v1/admin/platform/configuration/oauth-client/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10711', 'loadPwdPolicy', '/v1/admin/platform/configuration/password-policy', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10712', 'savePwdPolicy', '/v1/admin/platform/configuration/password-policy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10713', 'searchReleaseNoteInfos', '/v1/admin/platform/configuration/release-note', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10714', 'createReleaseNoteInfo', '/v1/admin/platform/configuration/release-note', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10715', 'createMailTemplateDownloadTask', '/v1/admin/platform/configuration/release-note/mail/template/{releaseNoteInfoId}/download-task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10716', 'getReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10717', 'updateReleaseNoteInfo', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10718', 'downloadEmails', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/download-emails', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10719', 'sendMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/send-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10720', 'testMail', '/v1/admin/platform/configuration/release-note/{releaseNoteInfoId}/test-mail', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10721', 'finsRkiServerList', '/v1/admin/platform/configuration/rki-server', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10722', 'createRkiServerSetting', '/v1/admin/platform/configuration/rki-server', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10723', 'testSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10724', 'getRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10725', 'updateRkiServerSetting', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10726', 'deleteRkiServer', '/v1/admin/platform/configuration/rki-server/{rkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10727', 'testExistSignatureConfigServer', '/v1/admin/platform/configuration/rki-server/{rkiId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10728', 'searchDiscountTerminals', '/v1/admin/platform/internal/discount/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10729', 'importMerchant_1', '/v1/admin/platform/internal/discount/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10730', 'searchPredefinedRoles', '/v1/admin/platform/internal/predefined-roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10731', 'searchPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10732', 'removePredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10733', 'createPredefinedRolePrivileges', '/v1/admin/platform/internal/predefined-roles/{roleId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10734', 'searchPrivileges', '/v1/admin/platform/internal/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10735', 'createPrivilege', '/v1/admin/platform/internal/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10736', 'getPrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10737', 'updatePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10738', 'deletePrivilege', '/v1/admin/platform/internal/privileges/{privilegeId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10739', 'searchPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10740', 'removePrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10741', 'createPrivilegeResources_1', '/v1/admin/platform/internal/privileges/{privilegeId}/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10742', 'searchSystemProperties', '/v1/admin/platform/internal/properties', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10743', 'createSystemProperty', '/v1/admin/platform/internal/properties', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10744', 'getSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10745', 'updateSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10746', 'deleteSystemProperty', '/v1/admin/platform/internal/properties/{systemPropertyId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10747', 'getPushDiagnosisResult', '/v1/admin/platform/internal/push-diagnosis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10748', 'sendPushDiagnosisTest', '/v1/admin/platform/internal/push-diagnosis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10749', 'searchResources', '/v1/admin/platform/internal/resources', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10750', 'createResource', '/v1/admin/platform/internal/resources', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10751', 'getResource', '/v1/admin/platform/internal/resources/{resourceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10752', 'updateResource', '/v1/admin/platform/internal/resources/{resourceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10753', 'deleteResource', '/v1/admin/platform/internal/resources/{resourceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10754', 'searchPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10755', 'removePrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10756', 'createPrivilegeResources', '/v1/admin/platform/internal/resources/{resourceId}/privileges', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10757', 'triggerScheduleJob', '/v1/admin/platform/internal/schedule-job', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10758', 'searchResellerMigrations', '/v1/admin/platform/migration/reseller-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10759', 'createResellerMigration', '/v1/admin/platform/migration/reseller-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10760', 'getResellerMigration', '/v1/admin/platform/migration/reseller-migrations/{resellerMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10761', 'searchTerminalMigrations', '/v1/admin/platform/migration/terminal-migrations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10762', 'createTerminalMigration', '/v1/admin/platform/migration/terminal-migrations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10763', 'getTerminalMigration', '/v1/admin/platform/migration/terminal-migrations/{terminalMigrationId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10764', 'getProductList', '/v1/admin/products', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10765', 'findProfileSettingList', '/v1/admin/products/profiles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10766', 'saveProfileSettingList', '/v1/admin/products/profiles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10767', 'getProductDetail', '/v1/admin/products/{codeValue}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10768', 'getProductProfile', '/v1/admin/products/{codeValue}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10769', 'getProductService', '/v1/admin/products/{codeValue}/service', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10770', 'getProductSetting', '/v1/admin/products/{codeValue}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10771', 'exportParameters', '/v1/admin/push/template/export/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10772', 'createExportApkParameterCompareDownloadTask_1', '/v1/admin/push/template/export/parameters/comparison', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10773', 'findParamAppPage', '/v1/admin/push/template/param-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10774', 'findParamSolutionAppPage', '/v1/admin/push/template/param-solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10775', 'findApkParametersPage', '/v1/admin/push/template/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10776', 'createApkParameter', '/v1/admin/push/template/parameters', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10777', 'findApkParameterAppPage', '/v1/admin/push/template/parameters/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10778', 'batchDeleteApkParameter', '/v1/admin/push/template/parameters/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10779', 'findApkParameterComparePage', '/v1/admin/push/template/parameters/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10780', 'getApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10781', 'updateApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10782', 'deleteApkParameter', '/v1/admin/push/template/parameters/{apkParameterId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10783', 'updateApkParameterFormData', '/v1/admin/push/template/parameters/{apkParameterId}/data', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10784', 'createApkParameterDataFileDownloadTask', '/v1/admin/push/template/parameters/{apkParameterId}/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10785', 'getApkParameterSchema', '/v1/admin/push/template/parameters/{apkParameterId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10786', 'searchReport', '/v1/admin/report', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10787', 'getMerchantByResellerIds', '/v1/admin/report/data-source/merchants', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10788', 'searchInstalledPUKList', '/v1/admin/report/data-source/puk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10789', 'getResellersByMarketId', '/v1/admin/report/data-source/resellers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10790', 'searchReportApkList', '/v1/admin/report/data-source/{reportId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10791', 'refreshParameterSourceItems', '/v1/admin/report/parameter/{parameterId}/source/refresh', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10792', 'deleteReportExecution_1', '/v1/admin/report/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10793', 'getReportJobHistoryPage', '/v1/admin/report/reportJobHistory', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10794', 'createDownloadTaskForReport', '/v1/admin/report/reportTask/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10795', 'updateReportTasksStatus', '/v1/admin/report/reportTask/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10796', 'updateReportTaskStatus', '/v1/admin/report/reportTask/{reportExecutionContextId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10797', 'getReportTaskByPage', '/v1/admin/report/reportTask/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10798', 'deleteReportExecution', '/v1/admin/report/{reportExecutionContextId}/reportExecutionContext', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10799', 'getReport', '/v1/admin/report/{reportId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10800', 'findReportDynamicFields', '/v1/admin/report/{reportId}/dynamic-fields', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10801', 'createImmediateReportExecution', '/v1/admin/report/{reportId}/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10802', 'createScheduledReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10803', 'getReportExecutionContext', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10804', 'updateReportExecution', '/v1/admin/report/{reportId}/reportExecutionContext/{reportExecutionContextId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10805', 'activeReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10806', 'getReseller3rdSysConfig', '/v1/admin/reseller/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10807', 'find3rdSysConfigIpPage', '/v1/admin/reseller/3rd-sys/config/ip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10808', 'deActiveReseller3rdSysAccess', '/v1/admin/reseller/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10809', 'addReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10810', 'updateReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10811', 'deleteReseller3rdSysIpAddress', '/v1/admin/reseller/3rd-sys/ip/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10812', 'getReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10813', 'resetReseller3rdSysAccessSecret', '/v1/admin/reseller/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10814', 'find3rdSysWebHookPage', '/v1/admin/reseller/3rd-sys/web-hook', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10815', 'create3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10816', 'getWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/message/history/{webHookId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10817', 'get3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10818', 'update3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10819', 'delete3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10820', 'findWebHookMessageHistory', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/message/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10821', 'test3rdSysWebHook', '/v1/admin/reseller/3rd-sys/web-hook/{webHookId}/test', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10822', 'findResellerOnlineApps', '/v1/admin/reseller/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10823', 'getApkInfo', '/v1/admin/reseller/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10824', 'reSignApk_2', '/v1/admin/reseller/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10825', 'findApkSignatureList_2', '/v1/admin/reseller/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10826', 'findSpecificApkResellerPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10827', 'specificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10828', 'deleteSpecificApkReseller_1', '/v1/admin/reseller/apps/apks/{apkId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10829', 'findSpecificApkResellerAllListPage_1', '/v1/admin/reseller/apps/apks/{apkId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10830', 'getTopicSubscription_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10831', 'subscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10832', 'unsubscribeTopic_2', '/v1/admin/reseller/apps/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10833', 'getAppInfo', '/v1/admin/reseller/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10834', 'searchApk', '/v1/admin/reseller/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10835', 'getBizDataFromGoInsight_2', '/v1/admin/reseller/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10836', 'getAppMerchantCategory', '/v1/admin/reseller/apps/{appId}/merchant/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10837', 'getAppSettingVo_1', '/v1/admin/reseller/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10838', 'findSpecificAppResellerPage_1', '/v1/admin/reseller/apps/{appId}/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10839', 'specificAppReseller_1', '/v1/admin/reseller/apps/{appId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10840', 'deleteSpecificResellerApp', '/v1/admin/reseller/apps/{appId}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10841', 'findSpecificAppResellerAllListPage_1', '/v1/admin/reseller/apps/{appId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10842', 'getAppVasSettingVo_2', '/v1/admin/reseller/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10843', 'searchFirmware_1', '/v1/admin/reseller/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10844', 'getTopicSubscription_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10845', 'subscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10846', 'unsubscribeTopic_1', '/v1/admin/reseller/firmwares/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10847', 'getFirmwareDetailVo_1', '/v1/admin/reseller/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10848', 'specificResellerFirmware_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10849', 'findSpecificFirmwareResellerAllListPage_1', '/v1/admin/reseller/firmwares/{firmwareId}/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10850', 'getResellerRki_1', '/v1/admin/reseller/rki/settings', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10851', 'refreshResellerRkiKeys_1', '/v1/admin/reseller/rki/settings/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10852', 'saveResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10853', 'deleteResellerRkiToken_1', '/v1/admin/reseller/rki/settings/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10854', 'getResellerTIDSettings', '/v1/admin/reseller/settings/tid', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10855', 'updateResellerTIDSetting', '/v1/admin/reseller/settings/tid', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10856', 'getMarketUiSettings', '/v1/admin/reseller/settings/ui', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10857', 'saveResellerUISettings', '/v1/admin/reseller/settings/ui', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10858', 'saveResellerUIAdvanceSettings', '/v1/admin/reseller/settings/ui/advance', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10859', 'deleteResellerUIAdvanceSettings', '/v1/admin/reseller/settings/ui/advance', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10860', 'saveResellerSignatureSetting', '/v1/admin/reseller/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10861', 'searchRoles', '/v1/admin/roles', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10862', 'createRole', '/v1/admin/roles', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10863', 'searchUsers_1', '/v1/admin/roles/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10864', 'getRole', '/v1/admin/roles/{roleId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10865', 'updateRole', '/v1/admin/roles/{roleId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10866', 'deleteRole', '/v1/admin/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10867', 'searchRoleUsers', '/v1/admin/roles/{roleId}/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10868', 'addRoleUsers', '/v1/admin/roles/{roleId}/users', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10869', 'removeRoleUsers', '/v1/admin/roles/{roleId}/users', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10870', 'findSolutionAppPage', '/v1/admin/service/industry-solution/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10871', 'reSignApk_1', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10872', 'findApkSignatureList_1', '/v1/admin/service/industry-solution/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10873', 'findSolutionAppIntroductionPage', '/v1/admin/service/industry-solution/apps/introduction', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10874', 'getSolutionAppDetail', '/v1/admin/service/industry-solution/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10875', 'applyIndustrySolutionAppForSpecific', '/v1/admin/service/industry-solution/apps/{appId}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10876', 'findVasAppCurrentUsage', '/v1/admin/service/industry-solution/apps/{appId}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10877', 'exportVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10878', 'findVasAppHistoryUsage', '/v1/admin/service/industry-solution/apps/{appId}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10879', 'findVasAppMarkets', '/v1/admin/service/industry-solution/apps/{appId}/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10880', 'findSpecificSolutionResellerPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10881', 'specificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10882', 'deleteSpecificSolutionReseller', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10883', 'findSpecificSolutionResellerAllListPage', '/v1/admin/service/industry-solution/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10884', 'updateAppServiceSetting', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10885', 'findVasAppServiceHistory', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10886', 'updateAppServicePrice', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/price', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10887', 'updateAppServiceStatus', '/v1/admin/service/industry-solution/apps/{appId}/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10888', 'getSolutionTrialPage', '/v1/admin/service/industry-solution/apps/{appId}/trial/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10889', 'getVasAppUsageDashBoard', '/v1/admin/service/industry-solution/apps/{appId}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10890', 'getAppVasSettingVo_1', '/v1/admin/service/industry-solution/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10891', 'findDeveloperSolutionApplyPage', '/v1/admin/service/industry-solution/developer/apply', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10892', 'getDeveloperSolutionApplyCount', '/v1/admin/service/industry-solution/developer/apply/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10893', 'updateDeveloperAllowIndustrySolution', '/v1/admin/service/industry-solution/developer/{developerId}/approve', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10894', 'getResellerApplies', '/v1/admin/service/industry-solution/reseller/apply', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10895', 'getResellerSolutionApplyCount', '/v1/admin/service/industry-solution/reseller/apply/pending-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10896', 'updateResellerApply', '/v1/admin/service/industry-solution/reseller/apply/{applyId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10897', 'findGlobalPublishAppPage', '/v1/admin/subscription/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10898', 'getApkDetailVo_3', '/v1/admin/subscription/apps/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10899', 'findSpecificApkResellerPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10900', 'specificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10901', 'deleteSpecificApkReseller', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10902', 'findSpecificApkResellerAllListPage', '/v1/admin/subscription/apps/apks/{apkId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10903', 'reSignApk', '/v1/admin/subscription/apps/apks/{apkId}/signature', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10904', 'findApkSignatureList', '/v1/admin/subscription/apps/apks/{apkId}/signatures', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10905', 'exportGlobalPublishApp', '/v1/admin/subscription/apps/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10906', 'getAppDetailVo', '/v1/admin/subscription/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10907', 'subscriptionApp', '/v1/admin/subscription/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10908', 'findApkPage', '/v1/admin/subscription/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10909', 'unSubscriptionApp', '/v1/admin/subscription/apps/{appId}/cancel', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10910', 'getBizDataFromGoInsight_1', '/v1/admin/subscription/apps/{appId}/insight/sandbox/data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10911', 'findAppMerchantCategoryPage', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10912', 'specificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10913', 'deleteSpecificAppMerchantCategory', '/v1/admin/subscription/apps/{appId}/merchant/categories/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10914', 'findSpecificAppResellerPage', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10915', 'specificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10916', 'deleteSpecificAppReseller', '/v1/admin/subscription/apps/{appId}/reseller/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10917', 'findSpecificAppResellerAllListPage', '/v1/admin/subscription/apps/{appId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10918', 'getAppSettingVo', '/v1/admin/subscription/apps/{appId}/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10919', 'getAppVasSettingVo', '/v1/admin/subscription/apps/{appId}/vas-setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10920', 'findGlobalPublishFirmware', '/v1/admin/subscription/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10921', 'exportGlobalPublishFirmware', '/v1/admin/subscription/firmwares/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10922', 'getFirmwareDetailVo', '/v1/admin/subscription/firmwares/{firmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10923', 'subscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10924', 'unsubscribeFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/cancel', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10925', 'specificResellerFirmware', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10926', 'findSpecificFirmwareResellerAllListPage', '/v1/admin/subscription/firmwares/{firmwareId}/reseller/specific/all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10927', 'getTopicSubscription', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10928', 'subscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10929', 'unsubscribeTopic', '/v1/admin/subscription/topic/{topicCategory}/{topicExternalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10930', 'searchGroups_1', '/v1/admin/terminal-groups', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10931', 'createGroup', '/v1/admin/terminal-groups', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10932', 'searchTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10933', 'createTerminalGroupApks', '/v1/admin/terminal-groups/apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10934', 'updateGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10935', 'deleteGroupApkFilter', '/v1/admin/terminal-groups/apks/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10936', 'getTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10937', 'deleteTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10938', 'resumeGroupTerminalApk', '/v1/admin/terminal-groups/apks/{groupApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10939', 'activateTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10940', 'getApkDetailVo_2', '/v1/admin/terminal-groups/apks/{groupApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10941', 'createGroupApkDataFileDownloadTask', '/v1/admin/terminal-groups/apks/{groupApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10942', 'createGroupApkFilter', '/v1/admin/terminal-groups/apks/{groupApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10943', 'updateGroupApkPushLimit', '/v1/admin/terminal-groups/apks/{groupApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10944', 'getTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10945', 'updateTerminalGroupApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10946', 'updateTerminalGroupApkParam_1', '/v1/admin/terminal-groups/apks/{groupApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10947', 'resumeGroupTerminalApkParam', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10948', 'getGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10949', 'saveGroupTerminalApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10950', 'searchGroupApkParamTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10951', 'getGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10952', 'saveGroupApkParamVariables', '/v1/admin/terminal-groups/apks/{groupApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10953', 'resetTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10954', 'suspendTerminalGroupApk', '/v1/admin/terminal-groups/apks/{groupApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10955', 'searchGroupApkTerminals', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10956', 'createGroupApkTerminalsExportTasks', '/v1/admin/terminal-groups/apks/{groupApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10957', 'searchGroupFirmwares', '/v1/admin/terminal-groups/firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10958', 'createGroupFirmware', '/v1/admin/terminal-groups/firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10959', 'searchFirmware', '/v1/admin/terminal-groups/firmwares/filter', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10960', 'updateGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10961', 'createGroupFirmwareFilter_1', '/v1/admin/terminal-groups/firmwares/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10962', 'getGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10963', 'deleteGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10964', 'resumeGroupTerminalFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10965', 'activateGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10966', 'createGroupFirmwareFilter', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10967', 'updateGroupFirmwarePushLimit', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10968', 'resetGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10969', 'suspendGroupFirmware', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10970', 'searchGroupFirmwareTerminals', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10971', 'createGroupFirmwareTerminalsExportTasks', '/v1/admin/terminal-groups/firmwares/{groupFirmwareId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10972', 'importGroupTerminal', '/v1/admin/terminal-groups/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10973', 'createGroupTerminalImportTemplateDownloadTask', '/v1/admin/terminal-groups/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10974', 'searchTerminalGroupLaunchers', '/v1/admin/terminal-groups/launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10975', 'createTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10976', 'getResellerOnlineApkNameAndIcon', '/v1/admin/terminal-groups/launchers/apps/**', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10977', 'searchLauncherTemplates_1', '/v1/admin/terminal-groups/launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10978', 'getLauncherTemplate_1', '/v1/admin/terminal-groups/launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10979', 'updateGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10980', 'deleteGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10981', 'getTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10982', 'deleteTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10983', 'resumeGroupTerminalLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10984', 'activateTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10985', 'getApkDetailVo_1', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10986', 'createGroupLauncherFilter', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10987', 'updateGroupLauncherPushLimit', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10988', 'getTerminalGroupLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10989', 'resumeGroupTerminalLauncherParam', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10990', 'searchGroupLauncherParamTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10991', 'resetTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10992', 'suspendTerminalGroupLauncher', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10993', 'searchGroupLauncherTerminals', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10994', 'createGroupLauncherTerminalsExportTasks', '/v1/admin/terminal-groups/launchers/{groupLauncherId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10995', 'searchGroupOperation', '/v1/admin/terminal-groups/operations', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10996', 'createGroupOperation', '/v1/admin/terminal-groups/operations', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10997', 'getGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10998', 'deleteGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('10999', 'resumeGroupTerminalOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11000', 'activateGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11001', 'updateGroupOperationPushLimit', '/v1/admin/terminal-groups/operations/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11002', 'resetGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11003', 'suspendGroupOperation', '/v1/admin/terminal-groups/operations/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11004', 'searchGroupOperationTerminals', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11005', 'createGroupOperationTerminalsExportTasks', '/v1/admin/terminal-groups/operations/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11006', 'searchGroupPuks', '/v1/admin/terminal-groups/puks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11007', 'createGroupPuk', '/v1/admin/terminal-groups/puks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11008', 'getSignaturePuk_1', '/v1/admin/terminal-groups/puks/{groupId}/signature', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11009', 'getGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11010', 'deleteGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11011', 'resumeGroupTerminalPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11012', 'activateGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11013', 'updateGroupPukPushLimit', '/v1/admin/terminal-groups/puks/{groupOptId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11014', 'resetGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11015', 'suspendGroupPuk', '/v1/admin/terminal-groups/puks/{groupOptId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11016', 'searchGroupPukTerminals', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11017', 'createGroupPukTerminalsExportTasks', '/v1/admin/terminal-groups/puks/{groupOptId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11018', 'searchGroupRkis', '/v1/admin/terminal-groups/rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11019', 'createGroupRki', '/v1/admin/terminal-groups/rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11020', 'getGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11021', 'deleteGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11022', 'resumeGroupTerminalRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11023', 'activateGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11024', 'updateGroupRkiPushLimit', '/v1/admin/terminal-groups/rkis/{groupRkiId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11025', 'resetGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11026', 'suspendGroupRki', '/v1/admin/terminal-groups/rkis/{groupRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11027', 'searchGroupRkiTerminals', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11028', 'createGroupRkiTerminalsExportTasks', '/v1/admin/terminal-groups/rkis/{groupRkiId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11029', 'searchTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11030', 'createTerminalGroupSolutions', '/v1/admin/terminal-groups/solutions', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11031', 'findApkParameterPage', '/v1/admin/terminal-groups/solutions/app/apk/parameters', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11032', 'findOnlineAppPage', '/v1/admin/terminal-groups/solutions/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11033', 'updateGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11034', 'deleteGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{filterId}/filter', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11035', 'getTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11036', 'deleteTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11037', 'resumeGroupTerminalSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11038', 'activateTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11039', 'getSolutionApkDetailVo', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11040', 'createGroupSolutionDataFileDownloadTask', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/data/file/download', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11041', 'createGroupSolutionFilter', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/filter', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11042', 'updateGroupSolutionPushLimit', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11043', 'getTerminalGroupSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11044', 'updateTerminalGroupSolutionApkParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11045', 'updateTerminalGroupSolutionApkParamFormData', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11046', 'resumeGroupTerminalSolutionParam', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11047', 'getGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11048', 'saveGroupTerminalSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/actions/{terminalActionId}/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11049', 'searchGroupSolutionParamTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11050', 'getGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11051', 'saveGroupSolutionParamVariables', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11052', 'resetTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11053', 'suspendTerminalGroupSolution', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11054', 'searchGroupSolutionTerminals', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11055', 'createGroupSolutionTerminalsExportTasks', '/v1/admin/terminal-groups/solutions/{groupSolutionApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11056', 'findSimOperator', '/v1/admin/terminal-groups/terminal/sim/operator', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11057', 'searchTerminal_1', '/v1/admin/terminal-groups/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11058', 'searchGroupUninstallApks', '/v1/admin/terminal-groups/uninstall-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11059', 'createGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11060', 'getGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11061', 'deleteGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11062', 'resumeGroupTerminalUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/actions/resume', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11063', 'activateGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11064', 'updateGroupUninstallApkPushLimit', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/limit', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11065', 'resetGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11066', 'suspendGroupUninstallApk', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11067', 'searchGroupUninstallApkTerminals', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11068', 'createGroupUninstallApkTerminalsExportTasks', '/v1/admin/terminal-groups/uninstall-apks/{groupUninstallApkId}/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11069', 'findTerminalGroupVariableList', '/v1/admin/terminal-groups/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11070', 'createTerminalGroupVariable', '/v1/admin/terminal-groups/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11071', 'batchDeleteTerminalGroupVariables', '/v1/admin/terminal-groups/variables/batch/deletion', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11072', 'importTerminalGroupVariable', '/v1/admin/terminal-groups/variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11073', 'createTerminalGroupVariableImportTemplateDownloadTask', '/v1/admin/terminal-groups/variables/import/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11074', 'findTerminalGroupVariableSupportedAppList', '/v1/admin/terminal-groups/variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11075', 'findTerminalGroupVariableUsedAppList', '/v1/admin/terminal-groups/variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11076', 'updateTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11077', 'deleteTerminalGroupVariable', '/v1/admin/terminal-groups/variables/{groupVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11078', 'getGroup', '/v1/admin/terminal-groups/{groupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11079', 'updateGroup', '/v1/admin/terminal-groups/{groupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11080', 'deleteGroup', '/v1/admin/terminal-groups/{groupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11081', 'activeGroup_1', '/v1/admin/terminal-groups/{groupId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11082', 'disableGroup_1', '/v1/admin/terminal-groups/{groupId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11083', 'removeGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11084', 'createGroupTerminals_1', '/v1/admin/terminal-groups/{groupId}/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11085', 'searchGroupTerminals', '/v1/admin/terminal-groups/{groupId}/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11086', 'getTerminalNumberStatisticData', '/v1/admin/terminal-management/dashboard/widgets/W09', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11087', 'getTerminalNumberOfMerchantData', '/v1/admin/terminal-management/dashboard/widgets/W10', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11088', 'getTerminalNumberOfResellerData', '/v1/admin/terminal-management/dashboard/widgets/W12', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11089', 'findEmmDeviceDetailPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11090', 'findEmmDeviceAuditLogPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11091', 'findEmmDeviceInstalledAppPage', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/installed-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11092', 'getEmmDeviceDashboardMonitor', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11093', 'getEmmDeviceTraffic', '/v1/admin/terminal-management/emm-device-detail/{deviceId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11094', 'searchEmmDevice', '/v1/admin/terminal-management/emm-devices', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11095', 'batchDeleteEmmDevices', '/v1/admin/terminal-management/emm-devices/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11096', 'batchMoveEmmDevices', '/v1/admin/terminal-management/emm-devices/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11097', 'createEmmDevicesExportDownloadTask', '/v1/admin/terminal-management/emm-devices/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11098', 'findEmmDeviceModels', '/v1/admin/terminal-management/emm-devices/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11099', 'createRegisterQRCode', '/v1/admin/terminal-management/emm-devices/register-qrcode/create', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11100', 'getEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11101', 'updateEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11102', 'deleteEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11103', 'lockEmmDeviceScreen', '/v1/admin/terminal-management/emm-devices/{deviceId}/lockscreen', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11104', 'rebootEmmDevice', '/v1/admin/terminal-management/emm-devices/{deviceId}/reboot', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11105', 'resetEmmDevicePassword', '/v1/admin/terminal-management/emm-devices/{deviceId}/resetpw', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11106', 'startEmmDeviceLostMode', '/v1/admin/terminal-management/emm-devices/{deviceId}/startlost', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11107', 'stopEmmDeviceLostMode', '/v1/admin/terminal-management/emm-devices/{deviceId}/stoplost', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11108', 'findMerchantVariablePage', '/v1/admin/terminal-management/merchant-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11109', 'createMerchantVariable', '/v1/admin/terminal-management/merchant-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11110', 'batchDeleteMerchantVariables', '/v1/admin/terminal-management/merchant-variables/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11111', 'importMerchantVariable', '/v1/admin/terminal-management/merchant-variables/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11112', 'createMerchantVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/merchant-variables/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11113', 'findMerchantVariableSupportedAppPage', '/v1/admin/terminal-management/merchant-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11114', 'findMerchantVariableUsedAppPage', '/v1/admin/terminal-management/merchant-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11115', 'updateMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11116', 'deleteMerchantVariable', '/v1/admin/terminal-management/merchant-variables/{merchantVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11117', 'createMerchant', '/v1/admin/terminal-management/merchants', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11118', 'createExportMerchantsDownloadTask', '/v1/admin/terminal-management/merchants/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11119', 'importMerchant', '/v1/admin/terminal-management/merchants/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11120', 'createMerchantImportTemplateDownloadTask', '/v1/admin/terminal-management/merchants/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11121', 'findSubMerchantPageForOrganization', '/v1/admin/terminal-management/merchants/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11122', 'findSubMerchantPage', '/v1/admin/terminal-management/merchants/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11123', 'getMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11124', 'updateMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11125', 'deleteMerchant', '/v1/admin/terminal-management/merchants/{merchantId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11126', 'activeMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11127', 'disableMerchant_1', '/v1/admin/terminal-management/merchants/{merchantId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11128', 'moveMerchant', '/v1/admin/terminal-management/merchants/{merchantId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11129', 'getMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11130', 'createMerchantProfile', '/v1/admin/terminal-management/merchants/{merchantId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11131', 'replaceMerchantEmail', '/v1/admin/terminal-management/merchants/{merchantId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11132', 'activeMerchantResendEmail', '/v1/admin/terminal-management/merchants/{merchantId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11133', 'createReseller', '/v1/admin/terminal-management/resellers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11134', 'createExportResellersDownloadTask', '/v1/admin/terminal-management/resellers/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11135', 'findResellerPageForOrganization', '/v1/admin/terminal-management/resellers/organization/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11136', 'createTmkImportTemplateDownloadTask', '/v1/admin/terminal-management/resellers/rki/tmk/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11137', 'findSubResellerPage', '/v1/admin/terminal-management/resellers/tree/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11138', 'getResellerDetailVo', '/v1/admin/terminal-management/resellers/{resellerId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11139', 'updateReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11140', 'deleteReseller', '/v1/admin/terminal-management/resellers/{resellerId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11141', 'activeReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11142', 'disableReseller_1', '/v1/admin/terminal-management/resellers/{resellerId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11143', 'moveReseller', '/v1/admin/terminal-management/resellers/{resellerId}/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11144', 'getResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11145', 'createResellerProfile', '/v1/admin/terminal-management/resellers/{resellerId}/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11146', 'replaceResellerEmail', '/v1/admin/terminal-management/resellers/{resellerId}/replace-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11147', 'activeResellerResendEmail', '/v1/admin/terminal-management/resellers/{resellerId}/resend-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11148', 'getResellerRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11149', 'refreshResellerRkiKeys', '/v1/admin/terminal-management/resellers/{resellerId}/rki/keys/collect', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11150', 'verifyPushRki', '/v1/admin/terminal-management/resellers/{resellerId}/rki/pre-deduction', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11151', 'findTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11152', 'importTerminalMasterKey', '/v1/admin/terminal-management/resellers/{resellerId}/rki/tmk/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11153', 'saveResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11154', 'deleteResellerRkiToken', '/v1/admin/terminal-management/resellers/{resellerId}/rki/token', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11155', 'findTerminalApkPage', '/v1/admin/terminal-management/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11156', 'createTerminalApks', '/v1/admin/terminal-management/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11157', 'createExportApkParameterCompareDownloadTask', '/v1/admin/terminal-management/terminal-apks/export/history/param/comparison', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11158', 'findTerminalApkParamComparePage', '/v1/admin/terminal-management/terminal-apks/history/param/comparison', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11159', 'getTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11160', 'deleteTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11161', 'activateTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11162', 'getApkDetailVo', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/apk-detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11163', 'createTerminalApkDataFileDownloadTask', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11164', 'getTerminalApkParam', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11165', 'updateTerminalApkParam_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11166', 'updateTerminalApkParamFormData', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11167', 'findTerminalApkParamVariablePage', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11168', 'saveTerminalApkParamVariables', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/param/variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11169', 'resetTerminalApk_1', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11170', 'suspendTerminalApk', '/v1/admin/terminal-management/terminal-apks/{terminalApkId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11171', 'findTerminalDetailPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11172', 'findTerminalAuditLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11173', 'getTerminalAuditLogDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/audit-log/{auditId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11174', 'getCheckUpDetailByTerminalId', '/v1/admin/terminal-management/terminal-detail/{terminalId}/check-up', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11175', 'findTerminalDownloadApkLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/download-apk-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11176', 'createTerminalGeoFenceWhiteList', '/v1/admin/terminal-management/terminal-detail/{terminalId}/geofence/whitelist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11177', 'deleteTerminalGeoFenceWhiteList', '/v1/admin/terminal-management/terminal-detail/{terminalId}/geofence/whitelist', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11178', 'getTerminalInstalledApkStatistics', '/v1/admin/terminal-management/terminal-detail/{terminalId}/installed-apks/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11179', 'getTerminalDashboardLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11180', 'refreshTerminalLocation', '/v1/admin/terminal-management/terminal-detail/{terminalId}/location/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11181', 'getTerminalDashboardMonitor', '/v1/admin/terminal-management/terminal-detail/{terminalId}/monitor', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11182', 'getTerminalPedStatus', '/v1/admin/terminal-management/terminal-detail/{terminalId}/ped', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11183', 'findTerminalPushHistoryPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/push-history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11184', 'refreshTerminalDetail', '/v1/admin/terminal-management/terminal-detail/{terminalId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11185', 'findTerminalReplacementLogPage', '/v1/admin/terminal-management/terminal-detail/{terminalId}/replace-log', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11186', 'getTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11187', 'saveTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11188', 'updateTerminalSafeRangeAutoLock', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/auto-lock', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11189', 'clearTerminalSafeRange', '/v1/admin/terminal-management/terminal-detail/{terminalId}/safe-range/clear', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11190', 'getTerminalTraffic', '/v1/admin/terminal-management/terminal-detail/{terminalId}/traffic', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11191', 'findTerminalFirmwarePage', '/v1/admin/terminal-management/terminal-firmwares', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11192', 'createTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11193', 'getTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11194', 'deleteTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11195', 'activateTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11196', 'resetTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11197', 'suspendTerminalFirmware', '/v1/admin/terminal-management/terminal-firmwares/{terminalFirmwareId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11198', 'findTerminalLauncherPage', '/v1/admin/terminal-management/terminal-launchers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11199', 'createTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11200', 'searchLauncherTemplates', '/v1/admin/terminal-management/terminal-launchers/templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11201', 'getLauncherTemplate', '/v1/admin/terminal-management/terminal-launchers/templates/{launcherTemplateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11202', 'getTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11203', 'deleteTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11204', 'activateTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11205', 'getTerminalLauncherParam', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11206', 'resetTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11207', 'suspendTerminalLauncher', '/v1/admin/terminal-management/terminal-launchers/{terminalLauncherId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11208', 'findTerminalRkiPage', '/v1/admin/terminal-management/terminal-rkis', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11209', 'createTerminalRki', '/v1/admin/terminal-management/terminal-rkis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11210', 'getTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11211', 'deleteTerminalRKI', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11212', 'activateTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11213', 'resetTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11214', 'suspendTerminalRki', '/v1/admin/terminal-management/terminal-rkis/{terminalRkiId}/suspend', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11215', 'findTerminalVariablePage', '/v1/admin/terminal-management/terminal-variables', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11216', 'createTerminalVariable', '/v1/admin/terminal-management/terminal-variables', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11217', 'batchDeleteTerminalVariables', '/v1/admin/terminal-management/terminal-variables/batch/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11218', 'findTerminalVariableSupportedAppPage', '/v1/admin/terminal-management/terminal-variables/supported/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11219', 'findTerminalVariableUsedAppPage', '/v1/admin/terminal-management/terminal-variables/used/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11220', 'updateTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11221', 'deleteTerminalVariable', '/v1/admin/terminal-management/terminal-variables/{terminalVariableId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11222', 'createTerminal', '/v1/admin/terminal-management/terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11223', 'searchAccessory', '/v1/admin/terminal-management/terminals/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11224', 'createExportTerminalAccessoryDownloadTask', '/v1/admin/terminal-management/terminals/accessories/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11225', 'cancelTerminalAccessoryOperation', '/v1/admin/terminal-management/terminals/accessories/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11226', 'findAccessoryQtyByType', '/v1/admin/terminal-management/terminals/accessories/widgets/W23', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11227', 'findAccessoryQtyByModel', '/v1/admin/terminal-management/terminals/accessories/widgets/W24', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11228', 'findTerminalAccessoryDetail', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/detail', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11229', 'findTerminalAccessoryDetailPage', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/details', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11230', 'findTerminalAccessoryEvents', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/events', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11231', 'findTerminalAccessoryOperations', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11232', 'pushTerminalActions_1', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11233', 'refreshTerminalAccessory', '/v1/admin/terminal-management/terminals/accessories/{accessoryId}/refresh', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11234', 'removeAppointment', '/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11235', 'cancelAppointment', '/v1/admin/terminal-management/terminals/air-viewer/appointment/{appointmentId}/canceled', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11236', 'createTerminals_1', '/v1/admin/terminal-management/terminals/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11237', 'batchActiveTerminals', '/v1/admin/terminal-management/terminals/batch/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11238', 'createGroupTerminals', '/v1/admin/terminal-management/terminals/batch/add-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11239', 'batchDeleteTerminals_1', '/v1/admin/terminal-management/terminals/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11240', 'batchSuspendTerminals', '/v1/admin/terminal-management/terminals/batch/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11241', 'batchMoveTerminals', '/v1/admin/terminal-management/terminals/batch/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11242', 'copyTerminal', '/v1/admin/terminal-management/terminals/copy', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11243', 'createExportTerminalsDownloadTask', '/v1/admin/terminal-management/terminals/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11244', 'createExportTerminalStaticIpConfigDownloadTask', '/v1/admin/terminal-management/terminals/export/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11245', 'createExportTerminalVariableDownloadTask', '/v1/admin/terminal-management/terminals/export/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11246', 'searchGroups', '/v1/admin/terminal-management/terminals/group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11247', 'importTerminal', '/v1/admin/terminal-management/terminals/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11248', 'importTerminalBatchDelete', '/v1/admin/terminal-management/terminals/import/delete', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11249', 'importTerminalBatchSuspend', '/v1/admin/terminal-management/terminals/import/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11250', 'importTerminalBatchMove', '/v1/admin/terminal-management/terminals/import/move', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11251', 'createTerminalImportBatchOperationTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/operation/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11252', 'importTerminalStaticIpConfig', '/v1/admin/terminal-management/terminals/import/static-ip/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11253', 'createTerminalStaticIpConfigTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/static-ip/config/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11254', 'createTerminalImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11255', 'importTerminalVariable', '/v1/admin/terminal-management/terminals/import/variable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11256', 'createTerminalVariableImportTemplateDownloadTask', '/v1/admin/terminal-management/terminals/import/variable/template', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11257', 'searchTerminal', '/v1/admin/terminal-management/terminals/list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11258', 'findTerminalAccessTypeList', '/v1/admin/terminal-management/terminals/product-types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11259', 'getTerminalQuickBySn', '/v1/admin/terminal-management/terminals/quick/search', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11260', 'getTerminalStockBySerialNo', '/v1/admin/terminal-management/terminals/stock', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11261', 'findModelPage', '/v1/admin/terminal-management/terminals/support-models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11262', 'getTerminalBasicVoByTidOrSerialNo', '/v1/admin/terminal-management/terminals/tid-sn/{tidOrSN}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11263', 'getTerminal', '/v1/admin/terminal-management/terminals/{terminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11264', 'updateTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11265', 'deleteTerminal_2', '/v1/admin/terminal-management/terminals/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11266', 'findTerminalAccessoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/accessories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11267', 'activeTerminal', '/v1/admin/terminal-management/terminals/{terminalId}/active', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11268', 'getTerminalAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11269', 'getAppointmentPage', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11270', 'createAppointment', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/appointment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11271', 'checkCheckUpVersion', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/check/checkup/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11272', 'pushInstallCheckup', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/checkup/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11273', 'pushInstallAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/install', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11274', 'startAirViewer', '/v1/admin/terminal-management/terminals/{terminalId}/air-viewer/start', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11275', 'disableTerminal_1', '/v1/admin/terminal-management/terminals/{terminalId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11276', 'findTerminalInstalledApkPage', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11277', 'getTerminalInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11278', 'createParameterDataFileDownloadTask_1', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/data/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11279', 'getInstalledApkParam', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/param', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11280', 'uninstallInstalledApk', '/v1/admin/terminal-management/terminals/{terminalId}/installed-apks/{installedApkId}/uninstall', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11281', 'getTerminalInstalledFirmware', '/v1/admin/terminal-management/terminals/{terminalId}/installed-firmware', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11282', 'moveTerminal', '/v1/admin/terminal-management/terminals/{terminalId}/move', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11283', 'getPukPushStatus', '/v1/admin/terminal-management/terminals/{terminalId}/puk/push/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11284', 'cancelTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/cancel/operation/{operationId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11285', 'collectTerminalLogcat', '/v1/admin/terminal-management/terminals/{terminalId}/setting/collect/log', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11286', 'findTerminalSystemConfigPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/configs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11287', 'disablePushPukTerminalAction', '/v1/admin/terminal-management/terminals/{terminalId}/setting/disable/puk/push', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11288', 'getTerminalLocationEnable', '/v1/admin/terminal-management/terminals/{terminalId}/setting/location', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11289', 'findTerminalLogPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11290', 'createTerminalLogDownloadTask', '/v1/admin/terminal-management/terminals/{terminalId}/setting/logs/{terminalLogId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11291', 'findTerminalNetworkConfigurationList', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11292', 'deleteTerminalNetworkConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/network/config', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11293', 'getProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11294', 'saveTerminalProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11295', 'deleteTerminalProfile', '/v1/admin/terminal-management/terminals/{terminalId}/setting/profile', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11296', 'findPukPushHistoryPage', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/push/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11297', 'getSignaturePuk', '/v1/admin/terminal-management/terminals/{terminalId}/setting/puk/{factoryId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11298', 'pushTerminalActions', '/v1/admin/terminal-management/terminals/{terminalId}/setting/push/operation', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11299', 'updateTerminalRemoteConfig', '/v1/admin/terminal-management/terminals/{terminalId}/setting/remote/config', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11300', 'findTerminalStockPage', '/v1/admin/terminal-stocks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11301', 'createTerminals', '/v1/admin/terminal-stocks/batch', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11302', 'assignTerminals', '/v1/admin/terminal-stocks/batch/assign', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11303', 'batchDeleteTerminals', '/v1/admin/terminal-stocks/batch/delete', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11304', 'createExportStockTerminalsDownloadTask', '/v1/admin/terminal-stocks/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11305', 'importStockTerminal', '/v1/admin/terminal-stocks/import', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11306', 'createStockTerminalImportTemplateDownloadTask', '/v1/admin/terminal-stocks/import/template/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11307', 'getTerminalStock', '/v1/admin/terminal-stocks/{terminalStockId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11308', 'updateTerminal', '/v1/admin/terminal-stocks/{terminalStockId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11309', 'deleteTerminal_1', '/v1/admin/terminal-stocks/{terminalStockId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11310', 'searchUsers', '/v1/admin/users', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11311', 'createExportUserDownloadTask', '/v1/admin/users/export', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11312', 'searchRoleList', '/v1/admin/users/role-all', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11313', 'getUser_3', '/v1/admin/users/{userId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11314', 'deleteUser', '/v1/admin/users/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11315', 'activeUser', '/v1/admin/users/{userId}/activate', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11316', 'sendActivateUserEmail', '/v1/admin/users/{userId}/activate-user-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11317', 'changeEmail', '/v1/admin/users/{userId}/change-email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11318', 'disableUser', '/v1/admin/users/{userId}/disable', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11319', 'findUserRoles', '/v1/admin/users/{userId}/markets/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11320', 'resetUserPassword', '/v1/admin/users/{userId}/reset-password', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11321', 'deleteUserRoles', '/v1/admin/users/{userId}/roles', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11322', 'deleteUserRole', '/v1/admin/users/{userId}/roles/{roleId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11323', 'isVasEnable', '/v1/admin/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11324', 'getThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11325', 'updateThirdpartyAppSys', '/v1/admin/vas/3rdsys/app', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11326', 'createThirdpartyApp', '/v1/admin/vas/3rdsys/app', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11327', 'exportDetectionSummary', '/v1/admin/vas/air-shield/attestation/history/{terminalId}/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11328', 'clearHistory', '/v1/admin/vas/air-shield/attestation/{terminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11329', 'findDetectionHistoryPage', '/v1/admin/vas/air-shield/attestation/{terminalId}/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11330', 'listPosviewerFileTransferInfo', '/v1/admin/vas/air-viewer/fileTransferInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11331', 'getModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11332', 'updateModels4MarketUnattended', '/v1/admin/vas/air-viewer/models/unattended', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11333', 'listPosviewerOperationInfo', '/v1/admin/vas/air-viewer/operationInfos', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11334', 'getVasGlobalInfo', '/v1/admin/vas/globalInfo', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11335', 'disableService', '/v1/admin/vas/service/{serviceType}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11336', 'getDashBoard', '/v1/app_scan/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11337', 'getEngineBlacklist', '/v1/app_scan/engine/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11338', 'updateEngineBlacklist', '/v1/app_scan/engine/blacklist', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11339', 'getHistoricalUsage', '/v1/app_scan/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11340', 'getAvailableScanEngineList', '/v1/app_scan/operation', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11341', 'rescan', '/v1/app_scan/rescan', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11342', 'getResultFile', '/v1/app_scan/resultZip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11343', 'getScanResult', '/v1/app_scan/results', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11344', 'isCreateTaskPermitted', '/v1/app_scan/scanned', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11345', 'getSetting', '/v1/app_scan/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11346', 'updateSetting', '/v1/app_scan/setting', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11347', 'createScanTask', '/v1/app_scan/task', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11348', 'deleteScanTask', '/v1/app_scan/task/{scanTaskId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11349', 'getUsage', '/v1/app_scan/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11350', 'createBuriedPoints', '/v1/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11351', 'queryNavigoAssistant', '/v1/common/assistant', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11352', 'evaluateData', '/v1/common/assistant/qa/evaluate', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11353', 'activateUser', '/v1/common/auth/activation', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11354', 'validateActivate1', '/v1/common/auth/activation/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11355', 'destroySsoToken', '/v1/common/auth/current', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11356', 'resetEmail', '/v1/common/auth/email-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11357', 'validateResetEmail1', '/v1/common/auth/email-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11358', 'validateExtraction', '/v1/common/auth/extraction', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11359', 'validateDownloadLink', '/v1/common/auth/extraction/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11360', 'getMarketDc', '/v1/common/auth/market/dc', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11361', 'disableOtpByBackupCode', '/v1/common/auth/otp', 'DELETE', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11362', 'validateDisableCode', '/v1/common/auth/otp/disable-code', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11363', 'sendDisableOtpMail', '/v1/common/auth/otp/reset-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11364', 'forgetPwd', '/v1/common/auth/password-forget', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11365', 'resetPwd', '/v1/common/auth/password-reset', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11366', 'validateResetPwd1', '/v1/common/auth/password-reset/{token}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11367', 'checkTokenExpire', '/v1/common/auth/ping', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11368', 'reactivateUser', '/v1/common/auth/reactivate-user', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11369', 'sendReactivateUserMail', '/v1/common/auth/reactivate-user-mail', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11370', 'registerUser', '/v1/common/auth/register', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11371', 'findCodes', '/v1/common/codes', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11372', 'getCodes', '/v1/common/codes/types/{type}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11373', 'getDocSetting', '/v1/common/doc-url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11374', 'download2', '/v1/common/download', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11375', 'createLatestOnlineClientApkDownloadTask', '/v1/common/download/client-app/latest/client', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11376', 'download1', '/v1/common/download/data/{dataId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11377', 'getDownloadUrl', '/v1/common/download/url', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11378', 'getCurrentEnv', '/v1/common/env', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11379', 'getFile', '/v1/common/files/{fileName}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11380', 'findFooter', '/v1/common/footer', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11381', 'getFooter', '/v1/common/footer/{footerId}', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11382', 'getLangList', '/v1/common/languages', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11383', 'getLicense', '/v1/common/license', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11384', 'initMessageStats', '/v1/common/notification/messages/stats', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11385', 'readTopXMessages', '/v1/common/notification/messages/stats', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11386', 'readMessage', '/v1/common/notification/messages/{messageId}', 'PUT', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11387', 'viewMessageDetails', '/v1/common/notification/messages/{messageId}/details', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11388', 'uploadTerminalGroupApkParamFile', '/v1/common/param/data/upload', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11389', 'getAllPasswordValidatorPolicyFailureDetail', '/v1/common/password-rules', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11390', 'getSystemConfig', '/v1/common/system-config', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11391', 'getUserAgreement_1', '/v1/common/user-agreement', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11392', 'agreeUserAgreement_1', '/v1/common/user-agreement/{agreementId}', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11393', 'generateCaptcha', '/v1/common/users/captcha', 'POST', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11394', 'verifyCaptcha', '/v1/common/users/captcha/verify', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11395', 'getCurrentUserRouterSwitchList', '/v1/common/users/routers', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11396', 'createDeveloper', '/v1/developer', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11397', 'activeDeveloper3rdSysAccess', '/v1/developer/3rd-sys/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11398', 'getDeveloper3rdSysConfig', '/v1/developer/3rd-sys/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11399', 'deActiveDeveloper3rdSysAccess', '/v1/developer/3rd-sys/inactive', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11400', 'getDeveloper3rdSysAccessSecret', '/v1/developer/3rd-sys/secret', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11401', 'resetDeveloper3rdSysAccess', '/v1/developer/3rd-sys/secret', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11402', 'getDeveloperAccountVo', '/v1/developer/account', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11403', 'agreeUserAgreement', '/v1/developer/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11404', 'updateApk', '/v1/developer/apks/{apkId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11405', 'deleteApk', '/v1/developer/apks/{apkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11406', 'getApkEditDetail', '/v1/developer/apks/{apkId}/apk-edit', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11407', 'updateApkFile', '/v1/developer/apks/{apkId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11408', 'createOriginalApkDownloadTask', '/v1/developer/apks/{apkId}/file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11409', 'offlineApk', '/v1/developer/apks/{apkId}/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11410', 'uploadApkParamTemplate', '/v1/developer/apks/{apkId}/param', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11411', 'deleteApkParamTemplate', '/v1/developer/apks/{apkId}/param', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11412', 'findCustomParamTemplate', '/v1/developer/apks/{apkId}/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11413', 'analysisDevParamTemplate', '/v1/developer/apks/{apkId}/param-templates/analysis', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11414', 'createParameterDataFileDownloadTask', '/v1/developer/apks/{apkId}/param-templates/data-file/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11415', 'createApkParamTemplateDownloadTask', '/v1/developer/apks/{apkId}/param-templates/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11416', 'getApkParamTemplateSchema', '/v1/developer/apks/{apkId}/param-templates/schema', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11417', 'submitApk', '/v1/developer/apks/{apkId}/submit', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11418', 'searchApps_1', '/v1/developer/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11419', 'createApp', '/v1/developer/apps', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11420', 'getDeveloperAppSummary', '/v1/developer/apps/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11421', 'getAppDetail_1', '/v1/developer/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11422', 'deleteApp', '/v1/developer/apps/{appId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11423', 'addApkFile', '/v1/developer/apps/{appId}/apk-file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11424', 'searchApks', '/v1/developer/apps/{appId}/apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11425', 'getApkDetail_1', '/v1/developer/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11426', 'updateAppKey', '/v1/developer/apps/{appId}/appKey', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11427', 'findSolutionAppUsage', '/v1/developer/apps/{appId}/industry-solution/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11428', 'findSolutionAppUsagePeriod', '/v1/developer/apps/{appId}/industry-solution/usage/period', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11429', 'getBizDataFromGoInsight', '/v1/developer/apps/{appId}/sandbox/insight-data', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11430', 'getDeveloperBalance', '/v1/developer/balance', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11431', 'findDeveloperTransactionList', '/v1/developer/balance/transactions', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11432', 'getDeveloper', '/v1/developer/current', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11433', 'getUser_2', '/v1/developer/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11434', 'validateUserEmail', '/v1/developer/email', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11435', 'findFactoryNameList', '/v1/developer/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11436', 'findFactoryModelList', '/v1/developer/factory/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11437', 'applyIndustrySolution', '/v1/developer/industry-solution/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11438', 'getMarket_2', '/v1/developer/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11439', 'findEnterpriseDevelopers', '/v1/developer/members', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11440', 'addEnterpriseDeveloper', '/v1/developer/members', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11441', 'deleteEnterpriseDeveloper', '/v1/developer/members/{userId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11442', 'updateAdminDeveloper', '/v1/developer/members/{userId}/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11443', 'updateDeveloperSuperAdmin', '/v1/developer/members/{userId}/super/admin', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11444', 'updateUserDeveloper', '/v1/developer/members/{userId}/user', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11445', 'searchCustomParamTemplate', '/v1/developer/param-templates', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11446', 'createParamTemplate', '/v1/developer/param-templates', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11447', 'searchAppName', '/v1/developer/param-templates/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11448', 'searchCustomParamTemplate_1', '/v1/developer/param-templates/list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11449', 'getDevParamTemplate', '/v1/developer/param-templates/{templateId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11450', 'updateParameterSchema', '/v1/developer/param-templates/{templateId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11451', 'deleteCustomParamTemplate', '/v1/developer/param-templates/{templateId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11452', 'cloneParamTemplate', '/v1/developer/param-templates/{templateId}/clone', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11453', 'createApkParamTemplateDownloadPoFilesTask', '/v1/developer/param-templates/{templateId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11454', 'updateParamTemplateName', '/v1/developer/param-templates/{templateId}/name', 'PATCH', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11455', 'getParamTemplateSchema', '/v1/developer/param-templates/{templateId}/schema', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11456', 'uploadDevParamTemplate', '/v1/developer/param-templates/{templateId}/upload', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11457', 'initDeveloperPayment', '/v1/developer/payment', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11458', 'checkout', '/v1/developer/payment', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11459', 'payDeveloperOffline', '/v1/developer/payment/offline', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11460', 'findAppPageForSandBox', '/v1/developer/sandbox-data/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11461', 'searchSandboxTerminal', '/v1/developer/sandbox-terminals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11462', 'createSandboxTerminal', '/v1/developer/sandbox-terminals', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11463', 'searchFactory', '/v1/developer/sandbox-terminals/factories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11464', 'getSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11465', 'updateSandboxTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11466', 'deleteTerminal', '/v1/developer/sandbox-terminals/{sandboxTerminalId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11467', 'isSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11468', 'updateSolutionSandboxSubscribe', '/v1/developer/sandbox/industry-solution/{appId}/subscribe', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11469', 'searchSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11470', 'createSandboxTerminalApks', '/v1/developer/sandbox/terminal-apks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11471', 'getApkDetail', '/v1/developer/sandbox/terminal-apks/apps/{appId}/apks/{apkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11472', 'getSandboxTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11473', 'deleteTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11474', 'activateTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/active', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11475', 'createTerminalApkDataDownloadTask', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/download-tasks', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11476', 'updateTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/param-template-name', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11477', 'getSandboxTerminalApkParam', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11478', 'updateTerminalApkParam_2', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/params', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11479', 'resetTerminalApk', '/v1/developer/sandbox/terminal-apks/{sandboxTerminalApkId}/reset', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11480', 'getUserAgreement', '/v1/developer/user/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11481', 'getValueAddServiceSummaryVo', '/v1/developer/vas', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11482', 'findVasAgreedAgreements', '/v1/developer/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11483', 'findDeveloperAppForVas', '/v1/developer/vas/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11484', 'clearAppCloudMessagesData', '/v1/developer/vas/clear-data', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11485', 'confirmConnectDialog', '/v1/developer/vas/confirm/connect/dialog', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11486', 'listAppMsg', '/v1/developer/vas/msg', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11487', 'addAppMsg', '/v1/developer/vas/msg', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11488', 'getTerminalInstalledAppCount', '/v1/developer/vas/msg/app/{appId}/installed-terminal-count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11489', 'findAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11490', 'createAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11491', 'deleteAppMsgTag', '/v1/developer/vas/msg/tag/apps/{appId}/tags/{tagId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11492', 'uploadMsgTemplate', '/v1/developer/vas/msg/template/file', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11493', 'validateUrl', '/v1/developer/vas/msg/template/validation-img-url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11494', 'getMsgById', '/v1/developer/vas/msg/{id}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11495', 'getMsgStatusById', '/v1/developer/vas/msg/{id}/status', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11496', 'logicDeleteMessage', '/v1/developer/vas/msg/{msgId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11497', 'disableMessage', '/v1/developer/vas/msg/{msgId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11498', 'getMsgReport', '/v1/developer/vas/msg/{msgId}/statistics', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11499', 'showConnectDialog', '/v1/developer/vas/show/connect/dialog', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11500', 'findStatisticsTypes', '/v1/developer/vas/statistics/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11501', 'getVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11502', 'agreeVasAgreement_1', '/v1/developer/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11503', 'serviceApply', '/v1/developer/vas/{serviceType}/apply', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11504', 'findHistoryUsageByServiceType', '/v1/developer/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11505', 'getUsageDashboardByServiceType', '/v1/developer/vas/{serviceType}/usage/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11506', 'getApk', '/v1/internal/apk', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11507', 'getLatestOnlineApkList', '/v1/internal/appUpdate', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11508', 'getAppDownloadsInfo', '/v1/internal/appdownloads', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11509', 'getApps', '/v1/internal/apps', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11510', 'getMarkets', '/v1/internal/market', 'GET', null, '0', '0', '1', NOW(), '1', NOW()),
  ('11511', 'searchAdGroup', '/v1/marketAdmin/vas/adup/ad-group', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11512', 'createAdGroup', '/v1/marketAdmin/vas/adup/ad-group', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11513', 'findAdGroupSlotPage', '/v1/marketAdmin/vas/adup/ad-group/ad-slot', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11514', 'updateAdGroupVisual', '/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adGroupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11515', 'findAdGroupVisualPage', '/v1/marketAdmin/vas/adup/ad-group/ad-visual/{adSlotId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11516', 'getAdGroupDetail', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11517', 'updateAdGroup', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11518', 'removeAdGroup', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11519', 'updateAdGroupStatus', '/v1/marketAdmin/vas/adup/ad-group/{adGroupId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11520', 'createAdSlot', '/v1/marketAdmin/vas/adup/ad-slot', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11521', 'findAdGroupModels', '/v1/marketAdmin/vas/adup/ad-slot/models', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11522', 'findAdSlotSpecList', '/v1/marketAdmin/vas/adup/ad-slot/specs', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11523', 'deleteAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11524', 'activateAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/activate', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11525', 'disableAdSlot', '/v1/marketAdmin/vas/adup/ad-slot/{adSlotId}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11526', 'findAdSlots', '/v1/marketAdmin/vas/adup/ad-slots', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11527', 'createAdVisual', '/v1/marketAdmin/vas/adup/ad-visual', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11528', 'retrieveAdVisualMaxDuration', '/v1/marketAdmin/vas/adup/ad-visual/maxDuration', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11529', 'findAdVisualPage', '/v1/marketAdmin/vas/adup/ad-visuals', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11530', 'createVasAgreement', '/v1/marketAdmin/vas/agreement', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11531', 'publishVasAgreement', '/v1/marketAdmin/vas/agreement/publish', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11532', 'updateVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11533', 'deleteVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11534', 'downloadVasAgreement', '/v1/marketAdmin/vas/agreement/{agreementId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11535', 'findVasAgreements', '/v1/marketAdmin/vas/agreements', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11536', 'findAppBlackListPage', '/v1/marketAdmin/vas/airShield/app-black-list', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11537', 'addAppBlackList', '/v1/marketAdmin/vas/airShield/app-black-list', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11538', 'updateAppBlack', '/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11539', 'deleteBlackApp', '/v1/marketAdmin/vas/airShield/app-black-list/{blackAppId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11540', 'getAttestationInterval', '/v1/marketAdmin/vas/airShield/interval', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11541', 'changeInterval', '/v1/marketAdmin/vas/airShield/interval', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11542', 'findSysFileAccessPage', '/v1/marketAdmin/vas/airShield/sys-file-access', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11543', 'addRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11544', 'updateRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11545', 'deleteRiskFileInfo', '/v1/marketAdmin/vas/airShield/sys-file-access/{riskFileId}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11546', 'getAirViewerCurrentUsage', '/v1/marketAdmin/vas/airViewer/currentUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11547', 'getAirViewerCurrentUsageDashBoard', '/v1/marketAdmin/vas/airViewer/currentUsage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11548', 'getAirViewerHistoricalUsage', '/v1/marketAdmin/vas/airViewer/historicalUsage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11549', 'exportAirviewerUsage', '/v1/marketAdmin/vas/airviewer/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11550', 'exportAppScanUsage', '/v1/marketAdmin/vas/appscan/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11551', 'getMarketServiceBillingSetting', '/v1/marketAdmin/vas/billingSetting/{serviceType}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11552', 'findCyberLabTerminalBlacklistPage', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11553', 'deleteCyberLabTerminalBlacklist', '/v1/marketAdmin/vas/cyberLab/terminal/blacklist/{id}', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11554', 'getGoogleEmmConfig', '/v1/marketAdmin/vas/emm/config', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11555', 'updateEmmConfig', '/v1/marketAdmin/vas/emm/config', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11556', 'getEmmEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11557', 'updateEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11558', 'createEmmEnterprise', '/v1/marketAdmin/vas/emm/enterprise', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11559', 'getEmmSignupUrl', '/v1/marketAdmin/vas/emm/signup/url', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11560', 'exportDetailZipByServiceType', '/v1/marketAdmin/vas/export/detail/zip', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11561', 'exportSummaryByServiceType', '/v1/marketAdmin/vas/export/summary', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11562', 'getInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11563', 'changeInsight2MarketSetting', '/v1/marketAdmin/vas/insight/setting', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11564', 'searchMarkets', '/v1/marketAdmin/vas/markets', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11565', 'exportMarkets', '/v1/marketAdmin/vas/markets/export', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11566', 'getCloudMessageTrialCount', '/v1/marketAdmin/vas/markets/{marketId}/trial/msg/count', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11567', 'findVasServices', '/v1/marketAdmin/vas/services', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11568', 'showCurrentMonthUsage', '/v1/marketAdmin/vas/services/show', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11569', 'updateActiveStatus', '/v1/marketAdmin/vas/services/{marketId}/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11570', 'unsubscribeService', '/v1/marketAdmin/vas/services/{serviceType}/disable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11571', 'subscribeService', '/v1/marketAdmin/vas/services/{serviceType}/enable', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11572', 'findServiceResellerSpecificPage', '/v1/marketAdmin/vas/services/{serviceType}/reseller/specific', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11573', 'specificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11574', 'deleteSpecificService', '/v1/marketAdmin/vas/services/{serviceType}/specific', 'DELETE', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11575', 'findSubscribeHistoryPage', '/v1/marketAdmin/vas/subscriptionHistory/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11576', 'loadCurrentEnrollTerminalBill', '/v1/marketAdmin/vas/terminal/enroll/bill', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11577', 'loadCurrentEnrollTerminalDashBoard', '/v1/marketAdmin/vas/terminal/enroll/dashboard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11578', 'loadEnrollTerminalHistory', '/v1/marketAdmin/vas/terminal/enroll/history', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11579', 'loadEnrollTerminalHistoryDetail', '/v1/marketAdmin/vas/terminal/enroll/history/{marketId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11580', 'downloadCurrentEnrollTerminal', '/v1/marketAdmin/vas/terminal/enroll/{marketId}/download', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11581', 'updateMarketDevServiceStatus', '/v1/marketAdmin/vas/{developerId}/service/status', 'PUT', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11582', 'getVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11583', 'agreeVasAgreement', '/v1/marketAdmin/vas/{serviceType}/agreement/{agreementId}', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11584', 'findCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/current/month/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11585', 'searchMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11586', 'exportCurrentUsage', '/v1/marketAdmin/vas/{serviceType}/export/current/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11587', 'exportMarketDevelopers', '/v1/marketAdmin/vas/{serviceType}/export/developers', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11588', 'exportHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/export/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11589', 'findHistoryUsage', '/v1/marketAdmin/vas/{serviceType}/history/usage', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11590', 'getUsageDashBoard', '/v1/marketAdmin/vas/{serviceType}/usage/dashBoard', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11591', 'getUser_1', '/v1/merchant/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11592', 'findPurchaseAppList', '/v1/merchant/dashboard/purchased-apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11593', 'loadWidgetModelTerminal', '/v1/merchant/dashboard/widgets/W15', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11594', 'loadWidgetTerminalOffline', '/v1/merchant/dashboard/widgets/W16', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11595', 'loadMerchantPortalWidget', '/v1/merchant/dashboard/widgets/W21', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11596', 'getMarket_1', '/v1/merchant/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11597', 'createNavigoBuriedPoints', '/v1/navigo/buriedPoints', 'POST', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11598', 'searchApps', '/v1/portal/apps', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11599', 'getAdvertisement', '/v1/portal/apps/advertisement', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11600', 'searchOnlineAppCategories', '/v1/portal/apps/categories', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11601', 'findDeveloperApp', '/v1/portal/apps/developer', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11602', 'findFeaturedApp', '/v1/portal/apps/featured', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11603', 'getAppDetailByPackageName', '/v1/portal/apps/packageName', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11604', 'searchAppsRank', '/v1/portal/apps/rank', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11605', 'findRelatedApp', '/v1/portal/apps/related', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11606', 'getAppTypes', '/v1/portal/apps/types', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11607', 'getAppDetail', '/v1/portal/apps/{appId}', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11608', 'getUser', '/v1/portal/current-user', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11609', 'getMarket', '/v1/portal/market', 'GET', null, '1', '0', '1', NOW(), '1', NOW()),
  ('11610', 'sysVersion', '/v1/public/version', 'GET', null, '1', '0', '1', NOW(), '1', NOW());

INSERT INTO `PAX_PRIVILEGE_RESOURCE` ( `privilege_id`, `resource_id`) VALUES
  ('1', '11390'),
  ('1', '11394'),
  ('1', '11395'),

  ('2', '11342'),
  ('2', '11343'),
  ('2', '11375'),
  ('2', '11388'),
  ('2', '11397'),
  ('2', '11398'),
  ('2', '11399'),
  ('2', '11400'),
  ('2', '11401'),
  ('2', '11402'),
  ('2', '11403'),
  ('2', '11404'),
  ('2', '11405'),
  ('2', '11406'),
  ('2', '11407'),
  ('2', '11408'),
  ('2', '11409'),
  ('2', '11410'),
  ('2', '11411'),
  ('2', '11412'),
  ('2', '11413'),
  ('2', '11414'),
  ('2', '11415'),
  ('2', '11416'),
  ('2', '11417'),
  ('2', '11418'),
  ('2', '11419'),
  ('2', '11420'),
  ('2', '11421'),
  ('2', '11422'),
  ('2', '11423'),
  ('2', '11424'),
  ('2', '11425'),
  ('2', '11426'),
  ('2', '11427'),
  ('2', '11428'),
  ('2', '11429'),
  ('2', '11430'),
  ('2', '11431'),
  ('2', '11435'),
  ('2', '11436'),
  ('2', '11437'),
  ('2', '11439'),
  ('2', '11440'),
  ('2', '11441'),
  ('2', '11442'),
  ('2', '11443'),
  ('2', '11444'),
  ('2', '11445'),
  ('2', '11446'),
  ('2', '11447'),
  ('2', '11448'),
  ('2', '11449'),
  ('2', '11450'),
  ('2', '11451'),
  ('2', '11452'),
  ('2', '11453'),
  ('2', '11454'),
  ('2', '11455'),
  ('2', '11456'),
  ('2', '11457'),
  ('2', '11458'),
  ('2', '11459'),
  ('2', '11460'),
  ('2', '11461'),
  ('2', '11462'),
  ('2', '11463'),
  ('2', '11464'),
  ('2', '11465'),
  ('2', '11466'),
  ('2', '11467'),
  ('2', '11468'),
  ('2', '11469'),
  ('2', '11470'),
  ('2', '11471'),
  ('2', '11472'),
  ('2', '11473'),
  ('2', '11474'),
  ('2', '11475'),
  ('2', '11476'),
  ('2', '11477'),
  ('2', '11478'),
  ('2', '11479'),
  ('2', '11480'),
  ('2', '11481'),
  ('2', '11482'),
  ('2', '11483'),
  ('2', '11484'),
  ('2', '11485'),
  ('2', '11486'),
  ('2', '11487'),
  ('2', '11488'),
  ('2', '11489'),
  ('2', '11490'),
  ('2', '11491'),
  ('2', '11492'),
  ('2', '11493'),
  ('2', '11494'),
  ('2', '11495'),
  ('2', '11496'),
  ('2', '11497'),
  ('2', '11498'),
  ('2', '11499'),
  ('2', '11500'),
  ('2', '11501'),
  ('2', '11502'),
  ('2', '11503'),
  ('2', '11504'),
  ('2', '11505'),

  ('3', '10728'),
  ('3', '10729'),
  ('3', '10730'),
  ('3', '10731'),
  ('3', '10732'),
  ('3', '10733'),
  ('3', '10734'),
  ('3', '10735'),
  ('3', '10736'),
  ('3', '10737'),
  ('3', '10738'),
  ('3', '10739'),
  ('3', '10740'),
  ('3', '10741'),
  ('3', '10742'),
  ('3', '10743'),
  ('3', '10744'),
  ('3', '10745'),
  ('3', '10746'),
  ('3', '10747'),
  ('3', '10748'),
  ('3', '10749'),
  ('3', '10750'),
  ('3', '10751'),
  ('3', '10752'),
  ('3', '10753'),
  ('3', '10754'),
  ('3', '10755'),
  ('3', '10756'),
  ('3', '10757'),

  ('20', '11592'),
  ('20', '11593'),
  ('20', '11594'),
  ('20', '11595'),

  ('51', '10372'),
  ('51', '10375'),
  ('51', '10376'),
  ('51', '10377'),
  ('51', '10378'),
  ('51', '10379'),
  ('51', '10380'),
  ('51', '10381'),
  ('51', '10382'),
  ('51', '10383'),
  ('51', '10384'),
  ('51', '10385'),
  ('51', '10386'),
  ('51', '10387'),
  ('51', '10388'),
  ('51', '10389'),
  ('51', '10390'),
  ('51', '10391'),
  ('51', '10392'),
  ('51', '10393'),
  ('51', '10394'),
  ('51', '10395'),
  ('51', '10396'),
  ('51', '10397'),
  ('51', '10398'),
  ('51', '10399'),
  ('51', '10400'),
  ('51', '10401'),
  ('51', '11375'),

  ('53', '10486'),
  ('53', '10487'),
  ('53', '10488'),
  ('53', '10489'),
  ('53', '10490'),
  ('53', '10491'),
  ('53', '10492'),
  ('53', '10493'),

  ('1041', '10254'),
  ('1041', '10255'),
  ('1041', '10257'),
  ('1041', '10258'),
  ('1041', '10259'),
  ('1041', '10260'),
  ('1041', '10261'),
  ('1041', '10360'),
  ('1041', '10371'),
  ('1041', '10372'),

  ('1042', '10254'),
  ('1042', '10255'),
  ('1042', '10256'),
  ('1042', '10257'),
  ('1042', '10258'),
  ('1042', '10259'),
  ('1042', '10260'),
  ('1042', '10261'),
  ('1042', '10360'),
  ('1042', '10371'),
  ('1042', '10372'),

  ('601', '10360'),
  ('601', '10364'),
  ('601', '10366'),
  ('601', '10367'),
  ('601', '10371'),
  ('601', '10372'),
  ('601', '10897'),
  ('601', '10898'),
  ('601', '10899'),
  ('601', '10902'),
  ('601', '10904'),
  ('601', '10905'),
  ('601', '10906'),
  ('601', '10908'),
  ('601', '10910'),
  ('601', '10911'),
  ('601', '10914'),
  ('601', '10917'),
  ('601', '10918'),
  ('601', '10919'),
  ('601', '10920'),
  ('601', '10921'),
  ('601', '10922'),
  ('601', '10926'),
  ('601', '10927'),
  ('601', '10928'),
  ('601', '10929'),
  ('601', '11340'),
  ('601', '11342'),
  ('601', '11343'),
  ('601', '11344'),

  ('602', '10360'),
  ('602', '10364'),
  ('602', '10366'),
  ('602', '10367'),
  ('602', '10371'),
  ('602', '10372'),
  ('602', '10897'),
  ('602', '10898'),
  ('602', '10899'),
  ('602', '10900'),
  ('602', '10901'),
  ('602', '10902'),
  ('602', '10903'),
  ('602', '10904'),
  ('602', '10905'),
  ('602', '10906'),
  ('602', '10907'),
  ('602', '10908'),
  ('602', '10909'),
  ('602', '10910'),
  ('602', '10911'),
  ('602', '10912'),
  ('602', '10913'),
  ('602', '10914'),
  ('602', '10915'),
  ('602', '10916'),
  ('602', '10917'),
  ('602', '10918'),
  ('602', '10919'),
  ('602', '10920'),
  ('602', '10921'),
  ('602', '10922'),
  ('602', '10923'),
  ('602', '10924'),
  ('602', '10925'),
  ('602', '10926'),
  ('602', '10927'),
  ('602', '10928'),
  ('602', '10929'),
  ('602', '11340'),
  ('602', '11341'),
  ('602', '11342'),
  ('602', '11343'),
  ('602', '11344'),
  ('602', '11347'),
  ('602', '11348'),

  ('611', '10262'),
  ('611', '10263'),
  ('611', '10264'),
  ('611', '10265'),
  ('611', '10268'),
  ('611', '10269'),
  ('611', '10271'),
  ('611', '10274'),
  ('611', '10276'),
  ('611', '10277'),
  ('611', '10278'),
  ('611', '10279'),
  ('611', '10280'),
  ('611', '10281'),
  ('611', '10284'),
  ('611', '10299'),
  ('611', '10300'),
  ('611', '10303'),
  ('611', '10304'),
  ('611', '10307'),
  ('611', '10310'),
  ('611', '10312'),
  ('611', '10313'),
  ('611', '10358'),
  ('611', '10360'),
  ('611', '10366'),
  ('611', '10367'),
  ('611', '10371'),
  ('611', '10372'),
  ('611', '10434'),
  ('611', '10438'),
  ('611', '10440'),
  ('611', '10442'),
  ('611', '10444'),
  ('611', '11340'),
  ('611', '11342'),
  ('611', '11343'),
  ('611', '11344'),

  ('612', '10262'),
  ('612', '10263'),
  ('612', '10264'),
  ('612', '10265'),
  ('612', '10266'),
  ('612', '10267'),
  ('612', '10268'),
  ('612', '10269'),
  ('612', '10270'),
  ('612', '10271'),
  ('612', '10272'),
  ('612', '10273'),
  ('612', '10274'),
  ('612', '10275'),
  ('612', '10276'),
  ('612', '10277'),
  ('612', '10278'),
  ('612', '10279'),
  ('612', '10280'),
  ('612', '10281'),
  ('612', '10282'),
  ('612', '10283'),
  ('612', '10284'),
  ('612', '10285'),
  ('612', '10286'),
  ('612', '10287'),
  ('612', '10288'),
  ('612', '10289'),
  ('612', '10290'),
  ('612', '10291'),
  ('612', '10292'),
  ('612', '10293'),
  ('612', '10294'),
  ('612', '10295'),
  ('612', '10296'),
  ('612', '10297'),
  ('612', '10298'),
  ('612', '10299'),
  ('612', '10300'),
  ('612', '10301'),
  ('612', '10302'),
  ('612', '10303'),
  ('612', '10304'),
  ('612', '10305'),
  ('612', '10306'),
  ('612', '10307'),
  ('612', '10308'),
  ('612', '10309'),
  ('612', '10310'),
  ('612', '10311'),
  ('612', '10312'),
  ('612', '10313'),
  ('612', '10314'),
  ('612', '10358'),
  ('612', '10360'),
  ('612', '10366'),
  ('612', '10367'),
  ('612', '10371'),
  ('612', '10372'),
  ('612', '10434'),
  ('612', '10435'),
  ('612', '10436'),
  ('612', '10437'),
  ('612', '10438'),
  ('612', '10439'),
  ('612', '10440'),
  ('612', '10441'),
  ('612', '10442'),
  ('612', '10443'),
  ('612', '10444'),
  ('612', '10446'),
  ('612', '10447'),
  ('612', '11340'),
  ('612', '11341'),
  ('612', '11342'),
  ('612', '11343'),
  ('612', '11344'),
  ('612', '11347'),
  ('612', '11348'),

  ('661', '10360'),
  ('661', '10364'),
  ('661', '10366'),
  ('661', '10371'),
  ('661', '10372'),
  ('661', '10843'),
  ('661', '10844'),
  ('661', '10845'),
  ('661', '10846'),
  ('661', '10847'),
  ('661', '10849'),

  ('662', '10360'),
  ('662', '10364'),
  ('662', '10366'),
  ('662', '10371'),
  ('662', '10372'),
  ('662', '10843'),
  ('662', '10844'),
  ('662', '10845'),
  ('662', '10846'),
  ('662', '10847'),
  ('662', '10848'),
  ('662', '10849'),

  ('621', '10262'),
  ('621', '10263'),
  ('621', '10264'),
  ('621', '10265'),
  ('621', '10268'),
  ('621', '10269'),
  ('621', '10271'),
  ('621', '10274'),
  ('621', '10276'),
  ('621', '10277'),
  ('621', '10278'),
  ('621', '10279'),
  ('621', '10280'),
  ('621', '10281'),
  ('621', '10284'),
  ('621', '10299'),
  ('621', '10300'),
  ('621', '10303'),
  ('621', '10304'),
  ('621', '10307'),
  ('621', '10310'),
  ('621', '10312'),
  ('621', '10313'),
  ('621', '10360'),
  ('621', '10366'),
  ('621', '10371'),
  ('621', '10372'),
  ('621', '10411'),
  ('621', '10412'),
  ('621', '10413'),
  ('621', '10427'),
  ('621', '10429'),
  ('621', '10433'),
  ('621', '11340'),
  ('621', '11342'),
  ('621', '11343'),
  ('621', '11344'),

  ('622', '10262'),
  ('622', '10263'),
  ('622', '10264'),
  ('622', '10265'),
  ('622', '10268'),
  ('622', '10269'),
  ('622', '10271'),
  ('622', '10274'),
  ('622', '10276'),
  ('622', '10277'),
  ('622', '10278'),
  ('622', '10279'),
  ('622', '10280'),
  ('622', '10281'),
  ('622', '10284'),
  ('622', '10299'),
  ('622', '10300'),
  ('622', '10303'),
  ('622', '10304'),
  ('622', '10307'),
  ('622', '10310'),
  ('622', '10312'),
  ('622', '10313'),
  ('622', '10360'),
  ('622', '10366'),
  ('622', '10371'),
  ('622', '10372'),
  ('622', '10411'),
  ('622', '10412'),
  ('622', '10413'),
  ('622', '10414'),
  ('622', '10415'),
  ('622', '10416'),
  ('622', '10417'),
  ('622', '10418'),
  ('622', '10419'),
  ('622', '10420'),
  ('622', '10421'),
  ('622', '10422'),
  ('622', '10423'),
  ('622', '10424'),
  ('622', '10425'),
  ('622', '10426'),
  ('622', '10427'),
  ('622', '10428'),
  ('622', '10429'),
  ('622', '10430'),
  ('622', '10431'),
  ('622', '10432'),
  ('622', '10433'),
  ('622', '11340'),
  ('622', '11342'),
  ('622', '11343'),
  ('622', '11344'),

  ('651', '10360'),
  ('651', '10367'),
  ('651', '10371'),
  ('651', '10372'),
  ('651', '10434'),
  ('651', '10438'),
  ('651', '10440'),
  ('651', '10442'),
  ('651', '10444'),
  ('651', '10822'),
  ('651', '10823'),
  ('651', '10825'),
  ('651', '10826'),
  ('651', '10829'),
  ('651', '10830'),
  ('651', '10831'),
  ('651', '10832'),
  ('651', '10833'),
  ('651', '10834'),
  ('651', '10835'),
  ('651', '10836'),
  ('651', '10837'),
  ('651', '10838'),
  ('651', '10841'),
  ('651', '10842'),
  ('651', '11343'),
  ('651', '11344'),

  ('652', '10360'),
  ('652', '10367'),
  ('652', '10371'),
  ('652', '10372'),
  ('652', '10434'),
  ('652', '10435'),
  ('652', '10436'),
  ('652', '10437'),
  ('652', '10438'),
  ('652', '10439'),
  ('652', '10440'),
  ('652', '10441'),
  ('652', '10442'),
  ('652', '10443'),
  ('652', '10444'),
  ('652', '10446'),
  ('652', '10447'),
  ('652', '10822'),
  ('652', '10823'),
  ('652', '10824'),
  ('652', '10825'),
  ('652', '10826'),
  ('652', '10827'),
  ('652', '10828'),
  ('652', '10829'),
  ('652', '10830'),
  ('652', '10831'),
  ('652', '10832'),
  ('652', '10833'),
  ('652', '10834'),
  ('652', '10835'),
  ('652', '10836'),
  ('652', '10837'),
  ('652', '10838'),
  ('652', '10839'),
  ('652', '10840'),
  ('652', '10841'),
  ('652', '10842'),
  ('652', '11343'),
  ('652', '11344'),

  ('711', '10353'),
  ('711', '10355'),
  ('711', '10356'),
  ('711', '10357'),
  ('711', '10358'),
  ('711', '10359'),
  ('711', '10360'),
  ('711', '10363'),
  ('711', '10364'),
  ('711', '10367'),
  ('711', '10369'),
  ('711', '10371'),
  ('711', '10372'),
  ('711', '10434'),
  ('711', '10445'),
  ('711', '10448'),
  ('711', '10451'),
  ('711', '10940'),
  ('711', '10941'),
  ('711', '10944'),
  ('711', '10985'),
  ('711', '10988'),
  ('711', '11086'),
  ('711', '11087'),
  ('711', '11088'),
  ('711', '11089'),
  ('711', '11090'),
  ('711', '11091'),
  ('711', '11092'),
  ('711', '11093'),
  ('711', '11094'),
  ('711', '11097'),
  ('711', '11098'),
  ('711', '11100'),
  ('711', '11108'),
  ('711', '11113'),
  ('711', '11114'),
  ('711', '11118'),
  ('711', '11121'),
  ('711', '11122'),
  ('711', '11123'),
  ('711', '11129'),
  ('711', '11134'),
  ('711', '11135'),
  ('711', '11137'),
  ('711', '11138'),
  ('711', '11144'),
  ('711', '11148'),
  ('711', '11151'),
  ('711', '11155'),
  ('711', '11157'),
  ('711', '11158'),
  ('711', '11159'),
  ('711', '11162'),
  ('711', '11163'),
  ('711', '11164'),
  ('711', '11167'),
  ('711', '11171'),
  ('711', '11172'),
  ('711', '11173'),
  ('711', '11174'),
  ('711', '11175'),
  ('711', '11178'),
  ('711', '11179'),
  ('711', '11180'),
  ('711', '11181'),
  ('711', '11182'),
  ('711', '11183'),
  ('711', '11184'),
  ('711', '11185'),
  ('711', '11186'),
  ('711', '11190'),
  ('711', '11191'),
  ('711', '11193'),
  ('711', '11198'),
  ('711', '11200'),
  ('711', '11201'),
  ('711', '11202'),
  ('711', '11205'),
  ('711', '11208'),
  ('711', '11210'),
  ('711', '11215'),
  ('711', '11218'),
  ('711', '11219'),
  ('711', '11223'),
  ('711', '11224'),
  ('711', '11226'),
  ('711', '11227'),
  ('711', '11228'),
  ('711', '11229'),
  ('711', '11230'),
  ('711', '11231'),
  ('711', '11233'),
  ('711', '11234'),
  ('711', '11235'),
  ('711', '11243'),
  ('711', '11244'),
  ('711', '11245'),
  ('711', '11246'),
  ('711', '11257'),
  ('711', '11258'),
  ('711', '11259'),
  ('711', '11260'),
  ('711', '11261'),
  ('711', '11262'),
  ('711', '11263'),
  ('711', '11266'),
  ('711', '11268'),
  ('711', '11269'),
  ('711', '11270'),
  ('711', '11271'),
  ('711', '11272'),
  ('711', '11273'),
  ('711', '11274'),
  ('711', '11276'),
  ('711', '11277'),
  ('711', '11278'),
  ('711', '11279'),
  ('711', '11281'),
  ('711', '11283'),
  ('711', '11286'),
  ('711', '11288'),
  ('711', '11289'),
  ('711', '11290'),
  ('711', '11291'),
  ('711', '11293'),
  ('711', '11296'),
  ('711', '11297'),
  ('711', '11327'),
  ('711', '11329'),
  ('711', '11330'),
  ('711', '11333'),

  ('712', '10353'),
  ('712', '10355'),
  ('712', '10356'),
  ('712', '10357'),
  ('712', '10358'),
  ('712', '10359'),
  ('712', '10360'),
  ('712', '10363'),
  ('712', '10364'),
  ('712', '10367'),
  ('712', '10369'),
  ('712', '10371'),
  ('712', '10372'),
  ('712', '10434'),
  ('712', '10445'),
  ('712', '10448'),
  ('712', '10451'),
  ('712', '10940'),
  ('712', '10941'),
  ('712', '10944'),
  ('712', '10985'),
  ('712', '10988'),
  ('712', '11086'),
  ('712', '11087'),
  ('712', '11088'),
  ('712', '11089'),
  ('712', '11090'),
  ('712', '11091'),
  ('712', '11092'),
  ('712', '11093'),
  ('712', '11094'),
  ('712', '11097'),
  ('712', '11098'),
  ('712', '11100'),
  ('712', '11108'),
  ('712', '11113'),
  ('712', '11114'),
  ('712', '11118'),
  ('712', '11121'),
  ('712', '11122'),
  ('712', '11123'),
  ('712', '11129'),
  ('712', '11134'),
  ('712', '11135'),
  ('712', '11137'),
  ('712', '11138'),
  ('712', '11144'),
  ('712', '11148'),
  ('712', '11151'),
  ('712', '11155'),
  ('712', '11157'),
  ('712', '11158'),
  ('712', '11159'),
  ('712', '11162'),
  ('712', '11163'),
  ('712', '11164'),
  ('712', '11167'),
  ('712', '11171'),
  ('712', '11172'),
  ('712', '11173'),
  ('712', '11174'),
  ('712', '11175'),
  ('712', '11178'),
  ('712', '11179'),
  ('712', '11180'),
  ('712', '11181'),
  ('712', '11182'),
  ('712', '11183'),
  ('712', '11184'),
  ('712', '11185'),
  ('712', '11186'),
  ('712', '11190'),
  ('712', '11191'),
  ('712', '11193'),
  ('712', '11198'),
  ('712', '11200'),
  ('712', '11201'),
  ('712', '11202'),
  ('712', '11205'),
  ('712', '11208'),
  ('712', '11210'),
  ('712', '11215'),
  ('712', '11218'),
  ('712', '11219'),
  ('712', '11223'),
  ('712', '11224'),
  ('712', '11226'),
  ('712', '11227'),
  ('712', '11228'),
  ('712', '11229'),
  ('712', '11230'),
  ('712', '11231'),
  ('712', '11233'),
  ('712', '11234'),
  ('712', '11235'),
  ('712', '11243'),
  ('712', '11244'),
  ('712', '11245'),
  ('712', '11246'),
  ('712', '11257'),
  ('712', '11258'),
  ('712', '11259'),
  ('712', '11260'),
  ('712', '11261'),
  ('712', '11262'),
  ('712', '11263'),
  ('712', '11266'),
  ('712', '11268'),
  ('712', '11269'),
  ('712', '11270'),
  ('712', '11271'),
  ('712', '11272'),
  ('712', '11273'),
  ('712', '11274'),
  ('712', '11276'),
  ('712', '11277'),
  ('712', '11278'),
  ('712', '11279'),
  ('712', '11281'),
  ('712', '11283'),
  ('712', '11286'),
  ('712', '11288'),
  ('712', '11289'),
  ('712', '11290'),
  ('712', '11291'),
  ('712', '11293'),
  ('712', '11296'),
  ('712', '11297'),
  ('712', '11327'),
  ('712', '11328'),
  ('712', '11329'),
  ('712', '11330'),
  ('712', '11333'),

  ('7124', '11191'),
  ('7124', '11192'),
  ('7124', '11193'),
  ('7124', '11194'),
  ('7124', '11195'),
  ('7124', '11196'),
  ('7124', '11197'),

  ('7126', '11198'),
  ('7126', '11199'),
  ('7126', '11200'),
  ('7126', '11201'),
  ('7126', '11202'),
  ('7126', '11203'),
  ('7126', '11204'),
  ('7126', '11205'),
  ('7126', '11206'),
  ('7126', '11207'),

  ('71211', '11133'),
  ('71211', '11134'),
  ('71211', '11135'),
  ('71211', '11136'),
  ('71211', '11137'),
  ('71211', '11138'),
  ('71211', '11139'),
  ('71211', '11140'),
  ('71211', '11141'),
  ('71211', '11142'),
  ('71211', '11143'),
  ('71211', '11144'),
  ('71211', '11146'),
  ('71211', '11147'),
  ('71211', '11148'),
  ('71211', '11149'),
  ('71211', '11150'),
  ('71211', '11151'),
  ('71211', '11152'),
  ('71211', '11153'),
  ('71211', '11154'),

  ('71213', '11117'),
  ('71213', '11118'),
  ('71213', '11119'),
  ('71213', '11120'),
  ('71213', '11121'),
  ('71213', '11122'),
  ('71213', '11123'),
  ('71213', '11124'),
  ('71213', '11125'),
  ('71213', '11126'),
  ('71213', '11127'),
  ('71213', '11128'),
  ('71213', '11129'),
  ('71213', '11131'),
  ('71213', '11132'),

  ('71212', '10452'),
  ('71212', '10453'),
  ('71212', '11145'),

  ('71214', '10449'),
  ('71214', '10450'),
  ('71214', '11130'),

  ('71221', '11094'),
  ('71221', '11095'),
  ('71221', '11096'),
  ('71221', '11097'),
  ('71221', '11098'),
  ('71221', '11099'),
  ('71221', '11100'),
  ('71221', '11101'),
  ('71221', '11102'),
  ('71221', '11103'),
  ('71221', '11104'),
  ('71221', '11105'),
  ('71221', '11106'),
  ('71221', '11107'),
  ('71221', '11222'),
  ('71221', '11223'),
  ('71221', '11224'),
  ('71221', '11225'),
  ('71221', '11226'),
  ('71221', '11227'),
  ('71221', '11228'),
  ('71221', '11229'),
  ('71221', '11230'),
  ('71221', '11231'),
  ('71221', '11232'),
  ('71221', '11233'),
  ('71221', '11234'),
  ('71221', '11235'),
  ('71221', '11236'),
  ('71221', '11237'),
  ('71221', '11238'),
  ('71221', '11239'),
  ('71221', '11240'),
  ('71221', '11241'),
  ('71221', '11242'),
  ('71221', '11243'),
  ('71221', '11244'),
  ('71221', '11245'),
  ('71221', '11246'),
  ('71221', '11247'),
  ('71221', '11248'),
  ('71221', '11249'),
  ('71221', '11250'),
  ('71221', '11251'),
  ('71221', '11252'),
  ('71221', '11253'),
  ('71221', '11254'),
  ('71221', '11255'),
  ('71221', '11256'),
  ('71221', '11257'),
  ('71221', '11258'),
  ('71221', '11259'),
  ('71221', '11260'),
  ('71221', '11261'),
  ('71221', '11262'),
  ('71221', '11263'),
  ('71221', '11264'),
  ('71221', '11265'),
  ('71221', '11266'),
  ('71221', '11267'),
  ('71221', '11268'),
  ('71221', '11269'),
  ('71221', '11270'),
  ('71221', '11271'),
  ('71221', '11272'),
  ('71221', '11273'),
  ('71221', '11274'),
  ('71221', '11275'),
  ('71221', '11276'),
  ('71221', '11277'),
  ('71221', '11278'),
  ('71221', '11279'),
  ('71221', '11280'),
  ('71221', '11281'),
  ('71221', '11282'),
  ('71221', '11283'),
  ('71221', '11284'),
  ('71221', '11285'),
  ('71221', '11286'),
  ('71221', '11287'),
  ('71221', '11288'),
  ('71221', '11289'),
  ('71221', '11290'),
  ('71221', '11291'),
  ('71221', '11292'),
  ('71221', '11293'),
  ('71221', '11294'),
  ('71221', '11295'),
  ('71221', '11296'),
  ('71221', '11297'),
  ('71221', '11298'),
  ('71221', '11299'),
  ('71221', '11572'),

  ('71222', '11171'),
  ('71222', '11172'),
  ('71222', '11173'),
  ('71222', '11174'),
  ('71222', '11175'),
  ('71222', '11176'),
  ('71222', '11177'),
  ('71222', '11178'),
  ('71222', '11179'),
  ('71222', '11180'),
  ('71222', '11181'),
  ('71222', '11182'),
  ('71222', '11183'),
  ('71222', '11184'),
  ('71222', '11185'),
  ('71222', '11186'),
  ('71222', '11187'),
  ('71222', '11188'),
  ('71222', '11189'),
  ('71222', '11190'),
  ('71222', '11252'),
  ('71222', '11253'),
  ('71222', '11284'),
  ('71222', '11285'),
  ('71222', '11286'),
  ('71222', '11287'),
  ('71222', '11288'),
  ('71222', '11289'),
  ('71222', '11290'),
  ('71222', '11291'),
  ('71222', '11292'),
  ('71222', '11293'),
  ('71222', '11294'),
  ('71222', '11295'),
  ('71222', '11296'),
  ('71222', '11297'),
  ('71222', '11298'),
  ('71222', '11299'),

  ('71231', '11155'),
  ('71231', '11156'),
  ('71231', '11157'),
  ('71231', '11158'),
  ('71231', '11159'),
  ('71231', '11160'),
  ('71231', '11161'),
  ('71231', '11162'),
  ('71231', '11163'),
  ('71231', '11164'),
  ('71231', '11167'),
  ('71231', '11169'),
  ('71231', '11170'),

  ('71232', '11108'),
  ('71232', '11109'),
  ('71232', '11110'),
  ('71232', '11111'),
  ('71232', '11112'),
  ('71232', '11113'),
  ('71232', '11114'),
  ('71232', '11115'),
  ('71232', '11116'),
  ('71232', '11155'),
  ('71232', '11156'),
  ('71232', '11157'),
  ('71232', '11158'),
  ('71232', '11159'),
  ('71232', '11160'),
  ('71232', '11161'),
  ('71232', '11162'),
  ('71232', '11163'),
  ('71232', '11164'),
  ('71232', '11165'),
  ('71232', '11166'),
  ('71232', '11167'),
  ('71232', '11168'),
  ('71232', '11169'),
  ('71232', '11170'),
  ('71232', '11215'),
  ('71232', '11216'),
  ('71232', '11217'),
  ('71232', '11218'),
  ('71232', '11219'),
  ('71232', '11220'),
  ('71232', '11221'),
  ('71232', '11255'),
  ('71232', '11256'),
  ('71232', '11388'),

  ('71233', '11280'),

  ('71251', '10370'),
  ('71251', '11208'),
  ('71251', '11209'),
  ('71251', '11210'),
  ('71251', '11211'),
  ('71251', '11212'),
  ('71251', '11213'),
  ('71251', '11214'),

  ('71252', '10370'),
  ('71252', '11148'),
  ('71252', '11149'),
  ('71252', '11150'),
  ('71252', '11151'),
  ('71252', '11152'),
  ('71252', '11153'),
  ('71252', '11154'),

  ('721', '10353'),
  ('721', '10355'),
  ('721', '10356'),
  ('721', '10357'),
  ('721', '10359'),
  ('721', '10360'),
  ('721', '10363'),
  ('721', '10364'),
  ('721', '10367'),
  ('721', '10369'),
  ('721', '10371'),
  ('721', '10372'),
  ('721', '10930'),
  ('721', '10932'),
  ('721', '10936'),
  ('721', '10940'),
  ('721', '10941'),
  ('721', '10944'),
  ('721', '10948'),
  ('721', '10950'),
  ('721', '10951'),
  ('721', '10955'),
  ('721', '10956'),
  ('721', '10957'),
  ('721', '10959'),
  ('721', '10962'),
  ('721', '10970'),
  ('721', '10971'),
  ('721', '10974'),
  ('721', '10976'),
  ('721', '10977'),
  ('721', '10978'),
  ('721', '10981'),
  ('721', '10985'),
  ('721', '10988'),
  ('721', '10990'),
  ('721', '10993'),
  ('721', '10994'),
  ('721', '10995'),
  ('721', '10997'),
  ('721', '11004'),
  ('721', '11005'),
  ('721', '11006'),
  ('721', '11008'),
  ('721', '11009'),
  ('721', '11016'),
  ('721', '11017'),
  ('721', '11018'),
  ('721', '11020'),
  ('721', '11027'),
  ('721', '11028'),
  ('721', '11029'),
  ('721', '11031'),
  ('721', '11032'),
  ('721', '11035'),
  ('721', '11039'),
  ('721', '11040'),
  ('721', '11043'),
  ('721', '11047'),
  ('721', '11049'),
  ('721', '11050'),
  ('721', '11054'),
  ('721', '11055'),
  ('721', '11056'),
  ('721', '11058'),
  ('721', '11060'),
  ('721', '11067'),
  ('721', '11068'),
  ('721', '11069'),
  ('721', '11074'),
  ('721', '11075'),
  ('721', '11078'),
  ('721', '11085'),

  ('722', '10353'),
  ('722', '10355'),
  ('722', '10356'),
  ('722', '10357'),
  ('722', '10359'),
  ('722', '10360'),
  ('722', '10363'),
  ('722', '10364'),
  ('722', '10367'),
  ('722', '10369'),
  ('722', '10371'),
  ('722', '10372'),
  ('722', '10930'),
  ('722', '10932'),
  ('722', '10936'),
  ('722', '10940'),
  ('722', '10941'),
  ('722', '10944'),
  ('722', '10948'),
  ('722', '10950'),
  ('722', '10951'),
  ('722', '10955'),
  ('722', '10956'),
  ('722', '10957'),
  ('722', '10959'),
  ('722', '10962'),
  ('722', '10970'),
  ('722', '10971'),
  ('722', '10974'),
  ('722', '10976'),
  ('722', '10977'),
  ('722', '10978'),
  ('722', '10981'),
  ('722', '10985'),
  ('722', '10988'),
  ('722', '10990'),
  ('722', '10993'),
  ('722', '10994'),
  ('722', '10995'),
  ('722', '10997'),
  ('722', '11004'),
  ('722', '11005'),
  ('722', '11006'),
  ('722', '11008'),
  ('722', '11009'),
  ('722', '11016'),
  ('722', '11017'),
  ('722', '11018'),
  ('722', '11020'),
  ('722', '11027'),
  ('722', '11028'),
  ('722', '11029'),
  ('722', '11031'),
  ('722', '11032'),
  ('722', '11035'),
  ('722', '11039'),
  ('722', '11040'),
  ('722', '11043'),
  ('722', '11047'),
  ('722', '11049'),
  ('722', '11050'),
  ('722', '11054'),
  ('722', '11055'),
  ('722', '11056'),
  ('722', '11058'),
  ('722', '11060'),
  ('722', '11067'),
  ('722', '11068'),
  ('722', '11069'),
  ('722', '11074'),
  ('722', '11075'),
  ('722', '11078'),
  ('722', '11085'),

  ('7221', '10930'),
  ('7221', '10931'),
  ('7221', '10932'),
  ('7221', '10933'),
  ('7221', '10934'),
  ('7221', '10935'),
  ('7221', '10936'),
  ('7221', '10937'),
  ('7221', '10938'),
  ('7221', '10939'),
  ('7221', '10940'),
  ('7221', '10941'),
  ('7221', '10942'),
  ('7221', '10943'),
  ('7221', '10944'),
  ('7221', '10945'),
  ('7221', '10946'),
  ('7221', '10947'),
  ('7221', '10948'),
  ('7221', '10949'),
  ('7221', '10950'),
  ('7221', '10951'),
  ('7221', '10952'),
  ('7221', '10953'),
  ('7221', '10954'),
  ('7221', '10955'),
  ('7221', '10956'),
  ('7221', '10957'),
  ('7221', '10958'),
  ('7221', '10959'),
  ('7221', '10960'),
  ('7221', '10961'),
  ('7221', '10962'),
  ('7221', '10963'),
  ('7221', '10964'),
  ('7221', '10965'),
  ('7221', '10966'),
  ('7221', '10967'),
  ('7221', '10968'),
  ('7221', '10969'),
  ('7221', '10970'),
  ('7221', '10971'),
  ('7221', '10972'),
  ('7221', '10973'),
  ('7221', '10974'),
  ('7221', '10975'),
  ('7221', '10976'),
  ('7221', '10977'),
  ('7221', '10978'),
  ('7221', '10979'),
  ('7221', '10980'),
  ('7221', '10981'),
  ('7221', '10982'),
  ('7221', '10983'),
  ('7221', '10984'),
  ('7221', '10985'),
  ('7221', '10986'),
  ('7221', '10987'),
  ('7221', '10988'),
  ('7221', '10989'),
  ('7221', '10990'),
  ('7221', '10991'),
  ('7221', '10992'),
  ('7221', '10993'),
  ('7221', '10994'),
  ('7221', '10995'),
  ('7221', '10996'),
  ('7221', '10997'),
  ('7221', '10998'),
  ('7221', '10999'),
  ('7221', '11000'),
  ('7221', '11001'),
  ('7221', '11002'),
  ('7221', '11003'),
  ('7221', '11004'),
  ('7221', '11005'),
  ('7221', '11006'),
  ('7221', '11007'),
  ('7221', '11008'),
  ('7221', '11009'),
  ('7221', '11010'),
  ('7221', '11011'),
  ('7221', '11012'),
  ('7221', '11013'),
  ('7221', '11014'),
  ('7221', '11015'),
  ('7221', '11016'),
  ('7221', '11017'),
  ('7221', '11018'),
  ('7221', '11019'),
  ('7221', '11020'),
  ('7221', '11021'),
  ('7221', '11022'),
  ('7221', '11023'),
  ('7221', '11024'),
  ('7221', '11025'),
  ('7221', '11026'),
  ('7221', '11027'),
  ('7221', '11028'),
  ('7221', '11029'),
  ('7221', '11030'),
  ('7221', '11031'),
  ('7221', '11032'),
  ('7221', '11033'),
  ('7221', '11034'),
  ('7221', '11035'),
  ('7221', '11036'),
  ('7221', '11037'),
  ('7221', '11038'),
  ('7221', '11039'),
  ('7221', '11040'),
  ('7221', '11041'),
  ('7221', '11042'),
  ('7221', '11043'),
  ('7221', '11044'),
  ('7221', '11045'),
  ('7221', '11046'),
  ('7221', '11047'),
  ('7221', '11048'),
  ('7221', '11049'),
  ('7221', '11050'),
  ('7221', '11051'),
  ('7221', '11052'),
  ('7221', '11053'),
  ('7221', '11054'),
  ('7221', '11055'),
  ('7221', '11056'),
  ('7221', '11057'),
  ('7221', '11058'),
  ('7221', '11059'),
  ('7221', '11060'),
  ('7221', '11061'),
  ('7221', '11062'),
  ('7221', '11063'),
  ('7221', '11064'),
  ('7221', '11065'),
  ('7221', '11066'),
  ('7221', '11067'),
  ('7221', '11068'),
  ('7221', '11069'),
  ('7221', '11070'),
  ('7221', '11071'),
  ('7221', '11072'),
  ('7221', '11073'),
  ('7221', '11074'),
  ('7221', '11075'),
  ('7221', '11076'),
  ('7221', '11077'),
  ('7221', '11078'),
  ('7221', '11079'),
  ('7221', '11080'),
  ('7221', '11081'),
  ('7221', '11082'),
  ('7221', '11083'),
  ('7221', '11084'),
  ('7221', '11085'),

  ('7223', '10957'),
  ('7223', '10958'),
  ('7223', '10959'),
  ('7223', '10960'),
  ('7223', '10961'),
  ('7223', '10962'),
  ('7223', '10963'),
  ('7223', '10964'),
  ('7223', '10965'),
  ('7223', '10966'),
  ('7223', '10967'),
  ('7223', '10968'),
  ('7223', '10969'),
  ('7223', '10970'),
  ('7223', '10971'),

  ('7224', '10370'),
  ('7224', '11018'),
  ('7224', '11019'),
  ('7224', '11020'),
  ('7224', '11021'),
  ('7224', '11022'),
  ('7224', '11023'),
  ('7224', '11024'),
  ('7224', '11025'),
  ('7224', '11026'),
  ('7224', '11027'),
  ('7224', '11028'),

  ('7225', '10995'),
  ('7225', '10996'),
  ('7225', '10997'),
  ('7225', '10998'),
  ('7225', '10999'),
  ('7225', '11000'),
  ('7225', '11001'),
  ('7225', '11002'),
  ('7225', '11003'),
  ('7225', '11004'),
  ('7225', '11005'),

  ('7226', '11006'),
  ('7226', '11007'),
  ('7226', '11008'),
  ('7226', '11009'),
  ('7226', '11010'),
  ('7226', '11011'),
  ('7226', '11012'),
  ('7226', '11013'),
  ('7226', '11014'),
  ('7226', '11015'),
  ('7226', '11016'),
  ('7226', '11017'),

  ('7227', '10974'),
  ('7227', '10975'),
  ('7227', '10976'),
  ('7227', '10977'),
  ('7227', '10978'),
  ('7227', '10979'),
  ('7227', '10980'),
  ('7227', '10981'),
  ('7227', '10982'),
  ('7227', '10983'),
  ('7227', '10984'),
  ('7227', '10985'),
  ('7227', '10986'),
  ('7227', '10987'),
  ('7227', '10988'),
  ('7227', '10989'),
  ('7227', '10990'),
  ('7227', '10991'),
  ('7227', '10992'),
  ('7227', '10993'),
  ('7227', '10994'),

  ('7228', '11029'),
  ('7228', '11030'),
  ('7228', '11031'),
  ('7228', '11032'),
  ('7228', '11033'),
  ('7228', '11034'),
  ('7228', '11035'),
  ('7228', '11036'),
  ('7228', '11037'),
  ('7228', '11038'),
  ('7228', '11039'),
  ('7228', '11040'),
  ('7228', '11041'),
  ('7228', '11042'),
  ('7228', '11043'),
  ('7228', '11044'),
  ('7228', '11045'),
  ('7228', '11046'),
  ('7228', '11047'),
  ('7228', '11048'),
  ('7228', '11049'),
  ('7228', '11050'),
  ('7228', '11051'),
  ('7228', '11052'),
  ('7228', '11053'),
  ('7228', '11054'),
  ('7228', '11055'),
  ('7228', '11069'),
  ('7228', '11070'),
  ('7228', '11071'),
  ('7228', '11072'),
  ('7228', '11073'),
  ('7228', '11074'),
  ('7228', '11075'),
  ('7228', '11076'),
  ('7228', '11077'),
  ('7228', '11388'),

  ('72221', '10932'),
  ('72221', '10933'),
  ('72221', '10934'),
  ('72221', '10935'),
  ('72221', '10936'),
  ('72221', '10937'),
  ('72221', '10938'),
  ('72221', '10939'),
  ('72221', '10940'),
  ('72221', '10941'),
  ('72221', '10942'),
  ('72221', '10943'),
  ('72221', '10944'),
  ('72221', '10948'),
  ('72221', '10950'),
  ('72221', '10951'),
  ('72221', '10953'),
  ('72221', '10954'),
  ('72221', '10955'),
  ('72221', '10956'),

  ('72222', '10932'),
  ('72222', '10933'),
  ('72222', '10934'),
  ('72222', '10935'),
  ('72222', '10936'),
  ('72222', '10937'),
  ('72222', '10938'),
  ('72222', '10939'),
  ('72222', '10940'),
  ('72222', '10941'),
  ('72222', '10942'),
  ('72222', '10943'),
  ('72222', '10944'),
  ('72222', '10945'),
  ('72222', '10946'),
  ('72222', '10947'),
  ('72222', '10948'),
  ('72222', '10949'),
  ('72222', '10950'),
  ('72222', '10951'),
  ('72222', '10952'),
  ('72222', '10953'),
  ('72222', '10954'),
  ('72222', '10955'),
  ('72222', '10956'),
  ('72222', '11069'),
  ('72222', '11070'),
  ('72222', '11071'),
  ('72222', '11072'),
  ('72222', '11073'),
  ('72222', '11074'),
  ('72222', '11075'),
  ('72222', '11076'),
  ('72222', '11077'),
  ('72222', '11388'),

  ('72223', '11058'),
  ('72223', '11059'),
  ('72223', '11060'),
  ('72223', '11061'),
  ('72223', '11062'),
  ('72223', '11063'),
  ('72223', '11064'),
  ('72223', '11065'),
  ('72223', '11066'),
  ('72223', '11067'),
  ('72223', '11068'),

  ('7311', '10356'),
  ('7311', '10771'),
  ('7311', '10772'),
  ('7311', '10773'),
  ('7311', '10774'),
  ('7311', '10775'),
  ('7311', '10777'),
  ('7311', '10779'),
  ('7311', '10780'),
  ('7311', '10784'),
  ('7311', '10785'),
  ('7311', '11388'),

  ('7312', '10356'),
  ('7312', '10771'),
  ('7312', '10772'),
  ('7312', '10773'),
  ('7312', '10774'),
  ('7312', '10775'),
  ('7312', '10776'),
  ('7312', '10777'),
  ('7312', '10778'),
  ('7312', '10779'),
  ('7312', '10780'),
  ('7312', '10781'),
  ('7312', '10782'),
  ('7312', '10783'),
  ('7312', '10784'),
  ('7312', '10785'),
  ('7312', '11388'),

  ('7321', '10525'),
  ('7321', '10527'),
  ('7321', '10528'),
  ('7321', '10529'),

  ('7322', '10525'),
  ('7322', '10526'),
  ('7322', '10527'),
  ('7322', '10528'),
  ('7322', '10529'),
  ('7322', '10530'),
  ('7322', '10531'),

  ('741', '10366'),
  ('741', '10454'),
  ('741', '10456'),
  ('741', '10457'),
  ('741', '10460'),
  ('741', '10463'),
  ('741', '10681'),
  ('741', '10683'),

  ('742', '10366'),
  ('742', '10454'),
  ('742', '10455'),
  ('742', '10456'),
  ('742', '10457'),
  ('742', '10458'),
  ('742', '10459'),
  ('742', '10460'),
  ('742', '10461'),
  ('742', '10462'),
  ('742', '10463'),
  ('742', '10681'),
  ('742', '10682'),
  ('742', '10683'),
  ('742', '10684'),
  ('742', '10685'),

  ('781', '10764'),
  ('781', '10765'),
  ('781', '10767'),
  ('781', '10768'),
  ('781', '10769'),
  ('781', '10770'),

  ('782', '10764'),
  ('782', '10765'),
  ('782', '10766'),
  ('782', '10767'),
  ('782', '10768'),
  ('782', '10769'),
  ('782', '10770'),

  ('79', '10402'),
  ('79', '10403'),
  ('79', '10404'),
  ('79', '10405'),
  ('79', '10406'),
  ('79', '10407'),
  ('79', '10408'),
  ('79', '10409'),
  ('79', '10410'),

  ('7511', '10359'),
  ('7511', '10360'),
  ('7511', '10361'),
  ('7511', '10364'),
  ('7511', '10464'),
  ('7511', '10465'),
  ('7511', '10466'),
  ('7511', '10467'),
  ('7511', '10468'),
  ('7511', '10471'),
  ('7511', '10477'),
  ('7511', '10482'),
  ('7511', '10484'),

  ('7512', '10359'),
  ('7512', '10360'),
  ('7512', '10361'),
  ('7512', '10362'),
  ('7512', '10364'),
  ('7512', '10464'),
  ('7512', '10465'),
  ('7512', '10466'),
  ('7512', '10467'),
  ('7512', '10468'),
  ('7512', '10469'),
  ('7512', '10470'),
  ('7512', '10471'),
  ('7512', '10472'),
  ('7512', '10473'),
  ('7512', '10474'),
  ('7512', '10475'),
  ('7512', '10476'),
  ('7512', '10477'),
  ('7512', '10478'),
  ('7512', '10479'),
  ('7512', '10480'),
  ('7512', '10481'),
  ('7512', '10482'),
  ('7512', '10483'),
  ('7512', '10484'),
  ('7512', '10485'),

  ('7521', '10359'),
  ('7521', '10360'),
  ('7521', '10361'),
  ('7521', '10364'),
  ('7521', '10366'),
  ('7521', '10371'),
  ('7521', '10372'),
  ('7521', '10465'),
  ('7521', '10466'),
  ('7521', '10467'),
  ('7521', '10468'),
  ('7521', '10471'),
  ('7521', '10477'),

  ('7522', '10359'),
  ('7522', '10360'),
  ('7522', '10361'),
  ('7522', '10362'),
  ('7522', '10364'),
  ('7522', '10366'),
  ('7522', '10371'),
  ('7522', '10372'),
  ('7522', '10465'),
  ('7522', '10466'),
  ('7522', '10467'),
  ('7522', '10468'),
  ('7522', '10469'),
  ('7522', '10470'),
  ('7522', '10471'),
  ('7522', '10472'),
  ('7522', '10473'),
  ('7522', '10474'),
  ('7522', '10475'),
  ('7522', '10476'),
  ('7522', '10477'),
  ('7522', '10478'),
  ('7522', '10479'),
  ('7522', '10480'),

  ('7611', '10321'),
  ('7611', '10322'),
  ('7611', '10323'),
  ('7611', '10324'),
  ('7611', '10330'),
  ('7611', '10331'),
  ('7611', '10333'),
  ('7611', '10337'),
  ('7611', '10338'),
  ('7611', '10343'),
  ('7611', '10344'),
  ('7611', '10359'),
  ('7611', '10360'),
  ('7611', '10369'),

  ('7612', '10321'),
  ('7612', '10322'),
  ('7612', '10323'),
  ('7612', '10324'),
  ('7612', '10325'),
  ('7612', '10326'),
  ('7612', '10327'),
  ('7612', '10328'),
  ('7612', '10329'),
  ('7612', '10330'),
  ('7612', '10331'),
  ('7612', '10332'),
  ('7612', '10333'),
  ('7612', '10334'),
  ('7612', '10335'),
  ('7612', '10336'),
  ('7612', '10337'),
  ('7612', '10338'),
  ('7612', '10339'),
  ('7612', '10340'),
  ('7612', '10341'),
  ('7612', '10342'),
  ('7612', '10343'),
  ('7612', '10344'),
  ('7612', '10345'),
  ('7612', '10346'),
  ('7612', '10347'),
  ('7612', '10348'),
  ('7612', '10349'),
  ('7612', '10359'),
  ('7612', '10360'),
  ('7612', '10369'),
  ('7612', '11375'),

  ('7621', '10322'),
  ('7621', '10323'),
  ('7621', '10324'),
  ('7621', '10330'),
  ('7621', '10331'),
  ('7621', '10333'),
  ('7621', '10337'),
  ('7621', '10338'),
  ('7621', '10359'),
  ('7621', '10360'),

  ('7622', '10322'),
  ('7622', '10323'),
  ('7622', '10324'),
  ('7622', '10325'),
  ('7622', '10326'),
  ('7622', '10327'),
  ('7622', '10328'),
  ('7622', '10329'),
  ('7622', '10330'),
  ('7622', '10331'),
  ('7622', '10332'),
  ('7622', '10333'),
  ('7622', '10334'),
  ('7622', '10335'),
  ('7622', '10336'),
  ('7622', '10337'),
  ('7622', '10338'),
  ('7622', '10339'),
  ('7622', '10340'),
  ('7622', '10341'),
  ('7622', '10342'),
  ('7622', '10359'),
  ('7622', '10360'),
  ('7622', '11375'),

  ('771', '10359'),
  ('771', '10360'),
  ('771', '10366'),
  ('771', '10369'),
  ('771', '11300'),
  ('771', '11304'),
  ('771', '11307'),

  ('772', '10359'),
  ('772', '10360'),
  ('772', '10366'),
  ('772', '10369'),
  ('772', '11300'),
  ('772', '11301'),
  ('772', '11302'),
  ('772', '11303'),
  ('772', '11304'),
  ('772', '11305'),
  ('772', '11306'),
  ('772', '11307'),
  ('772', '11308'),
  ('772', '11309'),

  ('911', '10359'),
  ('911', '10366'),
  ('911', '10369'),
  ('911', '10370'),
  ('911', '10533'),
  ('911', '10534'),
  ('911', '10539'),
  ('911', '10541'),
  ('911', '10543'),
  ('911', '10544'),
  ('911', '10547'),
  ('911', '10549'),
  ('911', '10552'),
  ('911', '10555'),
  ('911', '10558'),
  ('911', '10560'),
  ('911', '10565'),
  ('911', '10568'),
  ('911', '10569'),
  ('911', '10573'),
  ('911', '10576'),
  ('911', '10578'),
  ('911', '10603'),
  ('911', '10605'),
  ('911', '10614'),
  ('911', '10622'),
  ('911', '10623'),
  ('911', '10624'),
  ('911', '10627'),
  ('911', '10629'),
  ('911', '10634'),
  ('911', '10635'),
  ('911', '10640'),
  ('911', '10641'),
  ('911', '10642'),
  ('911', '10644'),
  ('911', '10648'),
  ('911', '10649'),
  ('911', '10654'),
  ('911', '10657'),
  ('911', '10659'),
  ('911', '10661'),
  ('911', '10662'),
  ('911', '10664'),
  ('911', '10666'),
  ('911', '10671'),
  ('911', '10672'),
  ('911', '10806'),
  ('911', '10807'),
  ('911', '10812'),
  ('911', '10814'),
  ('911', '10816'),
  ('911', '10817'),
  ('911', '10820'),
  ('911', '10850'),
  ('911', '10854'),
  ('911', '10856'),

  ('912', '10359'),
  ('912', '10366'),
  ('912', '10369'),
  ('912', '10370'),
  ('912', '10532'),
  ('912', '10533'),
  ('912', '10534'),
  ('912', '10535'),
  ('912', '10536'),
  ('912', '10537'),
  ('912', '10538'),
  ('912', '10539'),
  ('912', '10540'),
  ('912', '10541'),
  ('912', '10542'),
  ('912', '10543'),
  ('912', '10544'),
  ('912', '10545'),
  ('912', '10546'),
  ('912', '10547'),
  ('912', '10548'),
  ('912', '10549'),
  ('912', '10550'),
  ('912', '10551'),
  ('912', '10552'),
  ('912', '10553'),
  ('912', '10554'),
  ('912', '10555'),
  ('912', '10556'),
  ('912', '10557'),
  ('912', '10558'),
  ('912', '10559'),
  ('912', '10560'),
  ('912', '10561'),
  ('912', '10562'),
  ('912', '10563'),
  ('912', '10564'),
  ('912', '10565'),
  ('912', '10566'),
  ('912', '10567'),
  ('912', '10568'),
  ('912', '10569'),
  ('912', '10570'),
  ('912', '10571'),
  ('912', '10572'),
  ('912', '10573'),
  ('912', '10574'),
  ('912', '10575'),
  ('912', '10576'),
  ('912', '10577'),
  ('912', '10578'),
  ('912', '10579'),
  ('912', '10580'),
  ('912', '10581'),
  ('912', '10603'),
  ('912', '10604'),
  ('912', '10605'),
  ('912', '10606'),
  ('912', '10607'),
  ('912', '10608'),
  ('912', '10614'),
  ('912', '10615'),
  ('912', '10616'),
  ('912', '10617'),
  ('912', '10618'),
  ('912', '10619'),
  ('912', '10620'),
  ('912', '10621'),
  ('912', '10622'),
  ('912', '10623'),
  ('912', '10624'),
  ('912', '10625'),
  ('912', '10626'),
  ('912', '10627'),
  ('912', '10628'),
  ('912', '10629'),
  ('912', '10630'),
  ('912', '10631'),
  ('912', '10632'),
  ('912', '10633'),
  ('912', '10634'),
  ('912', '10635'),
  ('912', '10636'),
  ('912', '10637'),
  ('912', '10638'),
  ('912', '10639'),
  ('912', '10640'),
  ('912', '10641'),
  ('912', '10642'),
  ('912', '10643'),
  ('912', '10644'),
  ('912', '10645'),
  ('912', '10646'),
  ('912', '10647'),
  ('912', '10648'),
  ('912', '10649'),
  ('912', '10650'),
  ('912', '10651'),
  ('912', '10652'),
  ('912', '10653'),
  ('912', '10654'),
  ('912', '10655'),
  ('912', '10656'),
  ('912', '10657'),
  ('912', '10658'),
  ('912', '10659'),
  ('912', '10660'),
  ('912', '10661'),
  ('912', '10662'),
  ('912', '10663'),
  ('912', '10664'),
  ('912', '10665'),
  ('912', '10666'),
  ('912', '10667'),
  ('912', '10668'),
  ('912', '10669'),
  ('912', '10670'),
  ('912', '10671'),
  ('912', '10672'),
  ('912', '10673'),
  ('912', '10674'),
  ('912', '10805'),
  ('912', '10806'),
  ('912', '10807'),
  ('912', '10808'),
  ('912', '10809'),
  ('912', '10810'),
  ('912', '10811'),
  ('912', '10812'),
  ('912', '10813'),
  ('912', '10814'),
  ('912', '10815'),
  ('912', '10816'),
  ('912', '10817'),
  ('912', '10818'),
  ('912', '10819'),
  ('912', '10820'),
  ('912', '10821'),
  ('912', '10850'),
  ('912', '10851'),
  ('912', '10852'),
  ('912', '10853'),
  ('912', '10854'),
  ('912', '10855'),
  ('912', '10856'),
  ('912', '10857'),
  ('912', '10858'),
  ('912', '10859'),
  ('912', '10860'),

  ('92', '10686'),
  ('92', '10687'),
  ('92', '10688'),
  ('92', '10689'),
  ('92', '10690'),
  ('92', '10691'),
  ('92', '10692'),
  ('92', '10693'),
  ('92', '10861'),
  ('92', '10862'),
  ('92', '10863'),
  ('92', '10864'),
  ('92', '10865'),
  ('92', '10866'),
  ('92', '10867'),
  ('92', '10868'),
  ('92', '10869'),

  ('931', '10675'),
  ('931', '10676'),
  ('931', '10679'),
  ('931', '11310'),
  ('931', '11311'),
  ('931', '11312'),
  ('931', '11313'),
  ('931', '11319'),

  ('932', '10675'),
  ('932', '10676'),
  ('932', '10677'),
  ('932', '10678'),
  ('932', '10679'),
  ('932', '10680'),
  ('932', '11310'),
  ('932', '11311'),
  ('932', '11312'),
  ('932', '11313'),
  ('932', '11314'),
  ('932', '11315'),
  ('932', '11316'),
  ('932', '11317'),
  ('932', '11318'),
  ('932', '11319'),
  ('932', '11320'),
  ('932', '11321'),
  ('932', '11322'),

  ('95', '10315'),
  ('95', '10316'),
  ('95', '10317'),
  ('95', '10318'),
  ('95', '10319'),
  ('95', '10320'),

  ('96', '10360'),
  ('96', '10366'),
  ('96', '10371'),
  ('96', '10372'),
  ('96', '10518'),
  ('96', '10519'),
  ('96', '10520'),
  ('96', '10521'),
  ('96', '10522'),
  ('96', '10523'),
  ('96', '10524'),
  ('96', '10786'),
  ('96', '10787'),
  ('96', '10788'),
  ('96', '10789'),
  ('96', '10790'),
  ('96', '10791'),
  ('96', '10792'),
  ('96', '10793'),
  ('96', '10794'),
  ('96', '10795'),
  ('96', '10796'),
  ('96', '10797'),
  ('96', '10798'),
  ('96', '10799'),
  ('96', '10800'),
  ('96', '10801'),
  ('96', '10802'),
  ('96', '10803'),
  ('96', '10804'),

  ('1011', '10359'),
  ('1011', '10494'),
  ('1011', '10496'),
  ('1011', '10497'),
  ('1011', '10498'),
  ('1011', '10499'),
  ('1011', '10500'),
  ('1011', '10501'),
  ('1011', '10503'),
  ('1011', '10507'),
  ('1011', '10509'),
  ('1011', '10511'),
  ('1011', '10515'),
  ('1011', '10516'),
  ('1011', '10585'),
  ('1011', '11566'),

  ('1012', '10359'),
  ('1012', '10494'),
  ('1012', '10495'),
  ('1012', '10496'),
  ('1012', '10497'),
  ('1012', '10498'),
  ('1012', '10499'),
  ('1012', '10500'),
  ('1012', '10501'),
  ('1012', '10502'),
  ('1012', '10503'),
  ('1012', '10504'),
  ('1012', '10505'),
  ('1012', '10506'),
  ('1012', '10507'),
  ('1012', '10508'),
  ('1012', '10509'),
  ('1012', '10510'),
  ('1012', '10511'),
  ('1012', '10512'),
  ('1012', '10513'),
  ('1012', '10514'),
  ('1012', '10515'),
  ('1012', '10516'),
  ('1012', '10517'),
  ('1012', '10584'),
  ('1012', '10585'),
  ('1012', '11566'),

  ('1021', '10585'),
  ('1021', '10694'),
  ('1021', '10696'),
  ('1021', '10697'),
  ('1021', '10699'),
  ('1021', '10701'),
  ('1021', '10703'),
  ('1021', '10706'),
  ('1021', '10708'),
  ('1021', '10711'),
  ('1021', '10713'),
  ('1021', '10716'),
  ('1021', '10718'),
  ('1021', '10721'),
  ('1021', '10724'),

  ('1022', '10584'),
  ('1022', '10585'),
  ('1022', '10694'),
  ('1022', '10695'),
  ('1022', '10696'),
  ('1022', '10697'),
  ('1022', '10698'),
  ('1022', '10699'),
  ('1022', '10700'),
  ('1022', '10701'),
  ('1022', '10702'),
  ('1022', '10703'),
  ('1022', '10704'),
  ('1022', '10705'),
  ('1022', '10706'),
  ('1022', '10707'),
  ('1022', '10708'),
  ('1022', '10709'),
  ('1022', '10710'),
  ('1022', '10711'),
  ('1022', '10712'),
  ('1022', '10713'),
  ('1022', '10714'),
  ('1022', '10715'),
  ('1022', '10716'),
  ('1022', '10717'),
  ('1022', '10718'),
  ('1022', '10719'),
  ('1022', '10720'),
  ('1022', '10721'),
  ('1022', '10722'),
  ('1022', '10723'),
  ('1022', '10724'),
  ('1022', '10725'),
  ('1022', '10726'),
  ('1022', '10727'),

  ('10511', '10366'),
  ('10511', '10758'),
  ('10511', '10760'),

  ('10512', '10366'),
  ('10512', '10758'),
  ('10512', '10759'),
  ('10512', '10760'),

  ('10521', '10366'),
  ('10521', '10761'),
  ('10521', '10763'),

  ('10522', '10366'),
  ('10522', '10761'),
  ('10522', '10762'),
  ('10522', '10763'),


  ('801', '10360'),
  ('801', '10366'),
  ('801', '10371'),
  ('801', '10372'),
  ('801', '10511'),
  ('801', '10515'),
  ('801', '10582'),
  ('801', '10583'),
  ('801', '10585'),
  ('801', '10586'),
  ('801', '10587'),
  ('801', '10588'),
  ('801', '10589'),
  ('801', '10590'),
  ('801', '10591'),
  ('801', '10592'),
  ('801', '10593'),
  ('801', '10594'),
  ('801', '10595'),
  ('801', '10596'),
  ('801', '10597'),
  ('801', '10598'),
  ('801', '10599'),
  ('801', '10600'),
  ('801', '10601'),
  ('801', '10602'),
  ('801', '10603'),
  ('801', '10604'),
  ('801', '10605'),
  ('801', '10606'),
  ('801', '10607'),
  ('801', '10608'),
  ('801', '10609'),
  ('801', '10610'),
  ('801', '10611'),
  ('801', '10612'),
  ('801', '10613'),
  ('801', '11323'),
  ('801', '11331'),
  ('801', '11336'),
  ('801', '11337'),
  ('801', '11339'),
  ('801', '11345'),
  ('801', '11349'),
  ('801', '11522'),
  ('801', '11526'),
  ('801', '11534'),
  ('801', '11535'),
  ('801', '11536'),
  ('801', '11540'),
  ('801', '11542'),
  ('801', '11546'),
  ('801', '11547'),
  ('801', '11548'),
  ('801', '11549'),
  ('801', '11550'),
  ('801', '11551'),
  ('801', '11552'),
  ('801', '11554'),
  ('801', '11556'),
  ('801', '11559'),
  ('801', '11560'),
  ('801', '11561'),
  ('801', '11562'),
  ('801', '11564'),
  ('801', '11565'),
  ('801', '11566'),
  ('801', '11567'),
  ('801', '11568'),
  ('801', '11572'),
  ('801', '11575'),
  ('801', '11576'),
  ('801', '11577'),
  ('801', '11578'),
  ('801', '11579'),
  ('801', '11580'),
  ('801', '11582'),
  ('801', '11584'),
  ('801', '11585'),
  ('801', '11586'),
  ('801', '11587'),
  ('801', '11588'),
  ('801', '11589'),
  ('801', '11590'),

  ('802', '10360'),
  ('802', '10366'),
  ('802', '10371'),
  ('802', '10372'),
  ('802', '10510'),
  ('802', '10511'),
  ('802', '10515'),
  ('802', '10582'),
  ('802', '10583'),
  ('802', '10584'),
  ('802', '10585'),
  ('802', '10586'),
  ('802', '10587'),
  ('802', '10588'),
  ('802', '10589'),
  ('802', '10590'),
  ('802', '10591'),
  ('802', '10592'),
  ('802', '10593'),
  ('802', '10594'),
  ('802', '10595'),
  ('802', '10596'),
  ('802', '10597'),
  ('802', '10598'),
  ('802', '10599'),
  ('802', '10600'),
  ('802', '10601'),
  ('802', '10602'),
  ('802', '10603'),
  ('802', '10604'),
  ('802', '10605'),
  ('802', '10606'),
  ('802', '10607'),
  ('802', '10608'),
  ('802', '10609'),
  ('802', '10610'),
  ('802', '10611'),
  ('802', '10612'),
  ('802', '10613'),
  ('802', '11323'),
  ('802', '11331'),
  ('802', '11332'),
  ('802', '11335'),
  ('802', '11336'),
  ('802', '11337'),
  ('802', '11338'),
  ('802', '11339'),
  ('802', '11345'),
  ('802', '11346'),
  ('802', '11349'),
  ('802', '11520'),
  ('802', '11522'),
  ('802', '11523'),
  ('802', '11524'),
  ('802', '11525'),
  ('802', '11526'),
  ('802', '11530'),
  ('802', '11531'),
  ('802', '11532'),
  ('802', '11533'),
  ('802', '11534'),
  ('802', '11535'),
  ('802', '11536'),
  ('802', '11537'),
  ('802', '11538'),
  ('802', '11539'),
  ('802', '11540'),
  ('802', '11541'),
  ('802', '11542'),
  ('802', '11543'),
  ('802', '11544'),
  ('802', '11545'),
  ('802', '11546'),
  ('802', '11547'),
  ('802', '11548'),
  ('802', '11549'),
  ('802', '11550'),
  ('802', '11551'),
  ('802', '11552'),
  ('802', '11553'),
  ('802', '11554'),
  ('802', '11555'),
  ('802', '11556'),
  ('802', '11557'),
  ('802', '11558'),
  ('802', '11559'),
  ('802', '11560'),
  ('802', '11561'),
  ('802', '11562'),
  ('802', '11563'),
  ('802', '11564'),
  ('802', '11565'),
  ('802', '11566'),
  ('802', '11567'),
  ('802', '11568'),
  ('802', '11569'),
  ('802', '11570'),
  ('802', '11571'),
  ('802', '11572'),
  ('802', '11573'),
  ('802', '11574'),
  ('802', '11575'),
  ('802', '11576'),
  ('802', '11577'),
  ('802', '11578'),
  ('802', '11579'),
  ('802', '11580'),
  ('802', '11581'),
  ('802', '11582'),
  ('802', '11583'),
  ('802', '11584'),
  ('802', '11585'),
  ('802', '11586'),
  ('802', '11587'),
  ('802', '11588'),
  ('802', '11589'),
  ('802', '11590'),

  ('811', '10360'),
  ('811', '10371'),
  ('811', '10372'),
  ('811', '10870'),
  ('811', '10872'),
  ('811', '10873'),
  ('811', '10874'),
  ('811', '10876'),
  ('811', '10877'),
  ('811', '10878'),
  ('811', '10879'),
  ('811', '10880'),
  ('811', '10883'),
  ('811', '10885'),
  ('811', '10888'),
  ('811', '10889'),
  ('811', '10890'),
  ('811', '10891'),
  ('811', '10892'),
  ('811', '10894'),
  ('811', '10895'),

  ('812', '10360'),
  ('812', '10371'),
  ('812', '10372'),
  ('812', '10870'),
  ('812', '10871'),
  ('812', '10872'),
  ('812', '10873'),
  ('812', '10874'),
  ('812', '10875'),
  ('812', '10876'),
  ('812', '10877'),
  ('812', '10878'),
  ('812', '10879'),
  ('812', '10880'),
  ('812', '10881'),
  ('812', '10882'),
  ('812', '10883'),
  ('812', '10884'),
  ('812', '10885'),
  ('812', '10886'),
  ('812', '10887'),
  ('812', '10888'),
  ('812', '10889'),
  ('812', '10890'),
  ('812', '10891'),
  ('812', '10892'),
  ('812', '10893'),
  ('812', '10894'),
  ('812', '10895'),
  ('812', '10896'),

  ('821', '11262'),
  ('821', '11268'),
  ('821', '11271'),
  ('821', '11330'),
  ('821', '11333'),
  ('821', '11567'),

  ('822', '11262'),
  ('822', '11268'),
  ('822', '11271'),
  ('822', '11272'),
  ('822', '11273'),
  ('822', '11274'),
  ('822', '11330'),
  ('822', '11333'),
  ('822', '11567'),

  ('831', '10525'),
  ('831', '10527'),
  ('831', '10528'),
  ('831', '10529'),
  ('831', '11567'),

  ('832', '10525'),
  ('832', '10526'),
  ('832', '10527'),
  ('832', '10528'),
  ('832', '10529'),
  ('832', '10530'),
  ('832', '10531'),
  ('832', '11567'),

  ('841', '10371'),
  ('841', '10372'),
  ('841', '11511'),
  ('841', '11513'),
  ('841', '11515'),
  ('841', '11516'),
  ('841', '11521'),
  ('841', '11522'),
  ('841', '11528'),
  ('841', '11529'),
  ('841', '11567'),

  ('842', '10371'),
  ('842', '10372'),
  ('842', '11511'),
  ('842', '11512'),
  ('842', '11513'),
  ('842', '11514'),
  ('842', '11515'),
  ('842', '11516'),
  ('842', '11517'),
  ('842', '11518'),
  ('842', '11519'),
  ('842', '11521'),
  ('842', '11522'),
  ('842', '11527'),
  ('842', '11528'),
  ('842', '11529'),
  ('842', '11567');
