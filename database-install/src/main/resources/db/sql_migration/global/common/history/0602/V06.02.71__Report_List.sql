DELETE FROM PAX_CODE WHERE id in(
31000,31001,31002,31003,31004,31005,31006,31007
);

DELETE FROM PAX_CODE_TYPE WHERE code_type='auth_log_action_type';
INSERT INTO PAX_CODE_TYPE VALUES ('auth_log_action_type', 'PRELOAD_ADMIN_INHERIT');

INSERT INTO PAX_CODE (`id`, `market_id`, `parent_id`, `lang`, `value`, `label`, `type`, `description`, `sort`, `remarks`, `created_by`, `created_date`, `updated_by`, `updated_date`, `del_flag`, `reversion`, `release_version`) VALUES
(31000, '-1', null, 'zh_CN', 'L', '登录', 'auth_log_action_type', '登录', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
(31001, '-1', null, 'zh_CN', 'O', '登出', 'auth_log_action_type', '登出', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
(31002, '-1', null, 'en', 'L', 'Login', 'auth_log_action_type', 'Login', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
(31003, '-1', null, 'en', 'O', 'Logout', 'auth_log_action_type', 'Logout', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),

(31004, '-1', null, 'zh_CN', '0', '失败', 'auth_log_action_result', '登录', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
(31005, '-1', null, 'zh_CN', '1', '成功', 'auth_log_action_result', '登出', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
(31006, '-1', null, 'en', '0', 'Fail', 'auth_log_action_result', 'Login', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1'),
(31007, '-1', null, 'en', '1', 'Success', 'auth_log_action_result', 'Logout', '0', null, '-1', NOW(), '1', NOW(), '0', '1', '-1');



DELETE FROM `pax_report` WHERE id in(1000000000, 1000000001, 1000000002, 1000000003, 1000000004, 1000000005, 1000000006);

INSERT INTO `pax_report` (`id`,`name`,`category_id`,`band_path`,`band_file_name`,`status`,`report_type`,`is_free`,`support_publish`,`support_send_by_mail`,`support_schedule`,`support_run_strategy`,`description`,`del_flag`,`created_by`,`created_date`,`updated_by`, `updated_date`)
VALUES 
(1000000000, 'Login Log Report', 2, '','band-login-log.xml', 'A', 'S', 1, 0, 1, 0, 0, 'This is a report for market admin to show user login and logout log', 0, NULL, NULL, NULL, NULL),
(1000000001, 'Terminal Replacement History Report', 1, '', 'band-terminal-replacement.xml','A', 'S', 1, 0, 1, 1, 0, 'This is a report for terminal replacement history', 0, NULL, NULL, NULL, NULL),
(1000000002, 'Terminal Firmware Statistics', 1, '', 'band-firmware.xml','A', 'S', 1, 0, 1, 1, 0, 'Terminal Firmware Statistics', 0, NULL, NULL, NULL, NULL),
(1000000003, 'PAXSTORE Version Report', 1, '', 'band-paxstore-version.xml','A', 'S', 1, 0, 1, 1, 0, 'PAXSTORE Version Report', 0, NULL, NULL, NULL, NULL),
(1000000004, 'Terminal Model Report', 1, '','band-terminal-model.xml', 'A', 'S', 1, 0, 1, 1, 0, 'Terminal Model Report', 0, NULL, NULL, NULL, NULL),
(1000000005, 'Reseller Terminal Report', 1, '', 'band-terminal-apk-param.xml','I', 'S', 1, 0, 1, 1, 0, 'Reseller Terminal Report', 0, NULL, NULL, NULL, NULL),
(1000000006, 'Online Terminal Report', 1, '', 'band-app-installed-terminals.xml','I', 'S', 1, 0, 1, 1, 0, 'Online Terminal Report', 0, NULL, NULL, NULL, NULL)
;


INSERT INTO `pax_report_privilege` (`report_id`,`super_admin_privilege`,`market_admin_privilege`,`reseller_admin_privilege`) VALUES
(1000000000, 0, 1, 0),
(1000000001, 0, 1, 1),
(1000000002, 1, 1, 0),
(1000000003, 1, 1, 0),
(1000000004, 1, 1, 1),
(1000000005, 1, 1, 0),
(1000000006, 1, 0, 0)
;

INSERT INTO `pax_report_output_format_map` (`report_id`, `output_format`) VALUES 
(1000000000, 'xlsx'),
(1000000001, 'xlsx'),
(1000000002, 'xlsx'),
(1000000003, 'xlsx'),
(1000000004, 'xlsx'),
(1000000005, 'xlsx'),
(1000000006, 'xlsx')
;

INSERT INTO `pax_report_schedule_type` (`report_id`, `schedule_type`) VALUES 
(1000000001, 'M'),
(1000000002, 'M'),
(1000000003, 'M'),
(1000000004, 'M'),
(1000000005, 'M'),
(1000000006, 'M')
;





INSERT INTO `pax_report_field`(`id`,`report_id`,`name`,`display_name`,`type`,`support_format`,`description`,`created_by`,`created_date`,`updated_by`,`updated_date`) VALUES 
(1000000000, 1000000000, 'userName', 'User Name', 'java.lang.String', 0,'User Name', NULL, NULL, NULL, NULL),
(1000000001, 1000000000, 'loginName', 'Login Name', 'java.lang.String', 0,'Login Name', NULL, NULL, NULL, NULL),
(1000000002, 1000000000, 'clientId', 'Clent','java.lang.String', 0, 'Client Id', NULL, NULL, NULL, NULL),
(1000000003, 1000000000, 'remoteAddr', 'IP Address', 'java.lang.String', 0, 'IP Address', NULL, NULL, NULL, NULL),
(1000000004, 1000000000, 'actionType','Action Type','java.lang.String',0, 'Action type(Login or Logout)', NULL, NULL, NULL, NULL),
(1000000005, 1000000000, 'actionStatus', 'Action Result', 'java.lang.String', 0, 'Action Result(Success or Fail)', NULL, NULL, NULL, NULL),
(1000000006, 1000000000, 'createdDate', 'Action Time', 'java.util.Date', 1,'Login or Logout time', NULL, NULL, NULL, NULL),

(1000000007, 1000000001, 'marketName', 'Marketplace', 'java.lang.String', 0,'Marketplace Name', 1, NOW(), 1, NOW()),
(1000000008, 1000000001, 'tid', 'TID', 'java.lang.String', 0,'TID', 1, NOW(), 1, NOW()),
(1000000009, 1000000001, 'resellerName', 'Reseller', 'java.lang.String', 0,'Reseller Name', 1, NOW(), 1, NOW()),
(1000000010, 1000000001, 'merchantName', 'Merchant', 'java.lang.String', 0,'Merchant Name', 1, NOW(), 1, NOW()),
(1000000011, 1000000001, 'originalSN', 'Original SN', 'java.lang.String', 0,'Original SN', 1, NOW(), 1, NOW()),
(1000000012, 1000000001, 'replacedSN', 'Replaced SN', 'java.lang.String', 0,'New SN', 1, NOW(), 1, NOW()),
(1000000013, 1000000001, 'replacedBy', 'Replaced By', 'java.lang.String', 0,'Replace By', 1, NOW(), 1, NOW()),
(1000000014, 1000000001, 'replacedDate', 'Replaced Date', 'java.util.Date', 1,'Replaced Date', 1, NOW(), 1, NOW()),

(1000000015, 1000000002, 'marketName', 'Marketplace', 'java.lang.String', 0,'Marketplace Name', 1, NOW(), 1, NOW()),
(1000000016, 1000000002, 'modelName', 'Model', 'java.lang.String', 0,'Model', 1, NOW(), 1, NOW()),
(1000000017, 1000000002, 'fmName', 'Firmware Version', 'java.lang.String', 0,'Firmware version', 1, NOW(), 1, NOW()),
(1000000018, 1000000002, 'terminalNumber', 'Terminal Count', 'java.lang.Integer', 0,'Terminal Count', 1, NOW(), 1, NOW()),

(1000000019, 1000000003, 'marketName', 'Marketplace', 'java.lang.String', 0,'Marketplace Name', 1, NOW(), 1, NOW()),
(1000000020, 1000000003, 'versionName', 'Version', 'java.lang.String', 0,'Version', 1, NOW(), 1, NOW()),
(1000000021, 1000000003, 'terminalCount', 'Terminal Count', 'java.lang.String', 0,'Terminal Count', 1, NOW(), 1, NOW()),

(1000000022, 1000000004, 'marketName', 'Marketplace', 'java.lang.String', 0,'Marketplace Name', 1, NOW(), 1, NOW()),
(1000000023, 1000000004, 'modelName', 'Model', 'java.lang.String', 0,'Model', 1, NOW(), 1, NOW()),
(1000000024, 1000000004, 'terminalCount', 'Terminal Count', 'int', 0,'Terminal Count', 1, NOW(), 1, NOW())
;

INSERT INTO `pax_report_field_format` (id,field_id,format,description,predefined,created_by,created_date,updated_by,updated_date)VALUES 
(1000000000, 1000000006, 'yyyy/MM/dd HH:mm:ss', 'Action Time Format', 1, NULL, NULL, NULL, NULL),
(1000000001, 1000000014, 'yyyy/MM/dd HH:mm:ss', 'Replaced Time Format', 1, NULL, NULL, NULL, NULL)
;




INSERT INTO `pax_report_template`(`id`, `report_id`, `name`, `status`, `output_format`, `document_name`, `template_file_id`, `predefined`, `is_default`, `del_flag`, `description`, `created_by`, `created_date`, `updated_by`, `updated_date`) VALUES 
(1000000000, 1000000000, 'default template', 'A', 'xlsx', 'tmp_login_log.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW()),
(1000000001, 1000000001, 'default template', 'A', 'xlsx', 'tmp_terminal_replacement_log.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW()),
(1000000002, 1000000002, 'default template', 'A', 'xlsx', 'tmp_firmware.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW()),
(1000000003, 1000000003, 'default template', 'A', 'xlsx', 'tmp_paxstore_version.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW()),
(1000000004, 1000000004, 'default template', 'A', 'xlsx', 'tmp_terminal_model.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW()),
(1000000005, 1000000005, 'default template', 'A', 'xlsx', 'tmp_terminal_apk_param.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW()),
(1000000006, 1000000006, 'default template', 'A', 'xlsx', 'tmp_app_installed_terminals.xlsx', '', 1, 1, 0, '', 1, NOW(), 1, NOW());


INSERT INTO `pax_report_parameter` (`id`, `report_id`, `name`, `alias`,`param_type`,`default_value`,`description`,`required`,`ui_component_type`,`data_source`,`data_source_parameter`,`data_source_depend`,`del_flag`,`created_by`,`created_date`,`updated_by`,`updated_date`)
VALUES
(1000000000,1000000000,'Sart Date','start','java.util.Date','','Start date of login/logout',1,4,null,null,null,0,1,NOW(),1,NOW()),
(1000000001,1000000000,'End Date','end','java.util.Date','','End date of login/logout',1,4,null,null,null,0,1,NOW(),1,NOW()),
(1000000002,1000000000,'Action Type','type','java.lang.String','','Auth Log Action Type(Login or Logout)',0,2,1,'auth_log_action_type',null,0,1,NOW(),1,NOW()),
(1000000003,1000000000,'Action Result','resultType','java.lang.Boolean','','Action Result(Success Fail)',0,2,1,'auth_log_action_result',null,0,1,NOW(),1,NOW()),
(1000000004,1000000000,'Current Market','currentMarketId','java.lang.Long','','Current Market',1,1,null,null,null,0,1,NOW(),1,NOW()),

(1000000005,1000000001,'Current Market','currentMarketId','java.lang.Long','','Current Market',1,1,null,null,null,0,1,NOW(),1,NOW()),
(1000000006,1000000001,'Reseller(s)','resellerIds','java.lang.String','','Resellers',0,5,3,null,1000000005,0,1,NOW(),1,NOW()),

(1000000007,1000000002,'Marketplace','marketIds','java.lang.String','','Marketplace',0,5,2,null,null,0,1,NOW(),1,NOW()),
(1000000008,1000000002,'Reseller','resellerIds','java.lang.String','','Resellers',0,5,3,null,1000000007,0,1,NOW(),1,NOW()),
(1000000009,1000000002,'Model(s)','modelIds','java.lang.String','','Models',1,3,null,null,null,0,1,NOW(),1,NOW()),

(1000000010,1000000003,'Marketplace','marketIds','java.lang.String','','Marketplace',0,5,2,null,null,0,1,NOW(),1,NOW()),
(1000000011,1000000003,'Reseller','resellerIds','java.lang.String','','Resellers',0,5,3,null,1000000010,0,1,NOW(),1,NOW()),
(1000000012,1000000003,'Version(s)','versionNames','java.lang.String','','Version(s)',0,5,3,null,null,0,1,NOW(),1,NOW()),

(1000000013,1000000004,'Marketplace','marketIds','java.lang.String','','Marketplace',0,5,2,null,null,0,1,NOW(),1,NOW()),
(1000000014,1000000004,'Reseller','resellerIds','java.lang.String','','Resellers',0,5,3,null,1000000013,0,1,NOW(),1,NOW()),
(1000000015,1000000004,'Merchant','merchantIds','java.lang.String','','Merchant',0,5,4,null,1000000014,0,1,NOW(),1,NOW()),
(1000000016,1000000004,'Model(s)','modelIds','java.lang.String','','Models',0,3,null,null,null,0,1,NOW(),1,NOW()),

(1000000017,1000000005,'Marketplace','marketIds','java.lang.String','','Marketplace',0,5,2,null,null,0,1,NOW(),1,NOW()),
(1000000018,1000000005,'Reseller','resellerIds','java.lang.String','','Resellers',0,5,3,null,1000000017,0,1,NOW(),1,NOW()),

(1000000019,1000000002,'Firmware Version(s)','firmwareName','java.lang.String','','Firmware Versions',0,5,8,null,null,0,1,NOW(),1,NOW())
;



/*

report band file id:
 group1/M00/00/A9/wKjJIlw3ApiAYM63AAAHggY_VuM907.xml
 group1/M00/00/B6/wKjJIlw9hv6AJw7eAAAGdspsYB8254.xml
 group1/M00/01/32/wKjJIlw9nteAK1bcAAAGPRUI1x0057.xml
 
 template file ID
 group1/M00/03/B4/wKjJIlxAL1GAbcBRAAApomxadms35.xlsx
 group1/M00/03/B4/wKjJIlxANimAHpTuAAAphoh3u5Q74.xlsx
 
*/ 
