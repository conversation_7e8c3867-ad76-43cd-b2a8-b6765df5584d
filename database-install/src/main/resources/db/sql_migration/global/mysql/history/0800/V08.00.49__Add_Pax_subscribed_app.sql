ALTER TABLE pax_purchased_app DROP INDEX uq_terminal_app_id;
ALTER TABLE pax_purchased_app ADD UNIQUE KEY uq_terminal_app_id(`merchant_id`,`terminal_id`, `app_id`);

DROP TABLE IF EXISTS `pax_subscribed_app`;

CREATE TABLE `pax_subscribed_app` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `merchant_id` int(11) NOT NULL COMMENT '商户编号',
  `terminal_id` int(11) NOT NULL COMMENT '终端编号',
  `app_id` bigint(20) NOT NULL COMMENT '应用编号',
  `subscribe_date` datetime NOT NULL COMMENT '订阅时间',
  `unsubscribe_date` datetime DEFAULT NULL COMMENT '取消订阅时间',
  PRIMARY KEY (`id`),
  KEY `fk_subscribed_app_merchant_id` (`merchant_id`),
  KEY `fk_subscribed_app_terminal_id` (`terminal_id`),
  KEY `fk_subscribed_app_app_id` (`app_id`),
  CONSTRAINT `fk_subscribed_app_merchant_id` FOREIGN KEY (`merchant_id`) REFERENCES `pax_merchant` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `fk_subscribed_app_app_id` FOREIGN KEY (`app_id`) REFERENCES `pax_app` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) COMMENT='商户已订阅应用关联表' AUTO_INCREMENT=1000000000;