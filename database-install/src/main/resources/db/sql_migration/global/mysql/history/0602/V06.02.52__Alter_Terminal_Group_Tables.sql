BEGIN;
SET FOREIGN_KEY_CHECKS = 0;

ALTER TABLE `pax_tm_group` ADD COLUMN `created_by_reseller` INT(11) DEFAULT NULL AFTER description;
ALTER TABLE pax_tm_group ADD CONSTRAINT fk_tm_group_created_by_reseller FOREIGN KEY (created_by_reseller) REFERENCES PAX_RESELLER (id) ON DELETE CASCADE;

UPDATE pax_tm_group a, pax_reseller b
SET a.created_by_reseller = (
    SELECT reseller_id
    FROM pax_user_role
    WHERE user_id = a.created_by AND market_id = b.market_id
    ORDER BY reseller_id ASC LIMIT 1
)
WHERE a.reseller_id = b.id;

UPDATE pax_tm_group
SET created_by_reseller = reseller_id
WHERE created_by_reseller IS NULL;

UPDATE pax_tm_group a, pax_reseller reseller, pax_reseller createdByReseller
SET a.created_by_reseller = a.reseller_id
WHERE a.reseller_id = reseller.id
AND a.created_by_reseller = createdByReseller.id
AND reseller.id != createdByReseller.id
AND reseller.parent_ids NOT LIKE concat(createdByReseller.parent_ids, createdByReseller.id,',%');

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;