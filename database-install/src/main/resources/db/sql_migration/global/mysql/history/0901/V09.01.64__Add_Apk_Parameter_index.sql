SET @x := (
    SELECT
        count( * )
    FROM
        information_schema.statistics
    WHERE
            table_name = 'pax_apk_parameter'
      AND index_name = 'fk_apk_parameter_market'
      AND table_schema = DATABASE ( )
);

SET @SQL :=
        IF
            ( @x > 0, 'alter table pax_apk_parameter drop index fk_apk_parameter_market;', 'select ''Index not exist.''' );
PREPARE stmt
    FROM
    @SQL;
EXECUTE stmt;

SET @x := (
    SELECT
        count( * )
    FROM
        information_schema.statistics
    WHERE
            table_name = 'pax_apk_parameter'
      AND index_name = 'fk_apk_parameter_reseller'
      AND table_schema = DATABASE ( )
);

SET @SQL :=
        IF
            ( @x > 0, 'alter table pax_apk_parameter drop index fk_apk_parameter_reseller;', 'select ''Index not exist.''' );
PREPARE stmt
    FROM
    @SQL;
EXECUTE stmt;

SET @x := (
    SELECT
        count( * )
    FROM
        information_schema.statistics
    WHERE
            table_name = 'pax_apk_parameter'
      AND index_name = 'fk_apk_parameter_apk'
      AND table_schema = DATABASE ( )
);

SET @SQL :=
        IF
            ( @x > 0, 'alter table pax_apk_parameter drop index fk_apk_parameter_apk;', 'select ''Index not exist.''' );
PREPARE stmt
    FROM
    @SQL;
EXECUTE stmt;

alter table pax_apk_parameter add index idx_apk_parameter(market_id, del_flag, reseller_id, apk_id);
alter table pax_apk_parameter add index idx_apk_id(apk_id, del_flag);