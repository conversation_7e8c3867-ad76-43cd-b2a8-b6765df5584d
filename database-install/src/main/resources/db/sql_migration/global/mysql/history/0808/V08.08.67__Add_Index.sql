SET @x := (
    SELECT
        count( * )
    FROM
        information_schema.statistics
    WHERE
            table_name = 'pax_client_apk_download_history'
      AND index_name = 'idx_client_apk_download_terminal'
      AND table_schema = DATABASE ( )
);

SET @SQL :=
        IF
            ( @x > 0, 'select ''Index exists.''',
              'alter table pax_client_apk_download_history add index idx_client_apk_download_terminal (`terminal_id`);' );
PREPARE stmt FROM @SQL;
EXECUTE stmt;



SET @x := (
    SELECT
        count( * )
    FROM
        information_schema.statistics
    WHERE
            table_name = 'pax_purchase'
      AND index_name = 'idx_purchase_terminal'
      AND table_schema = DATABASE ( )
);

SET @SQL :=
        IF
            ( @x > 0, 'select ''Index exists.''',
              'alter table pax_purchase add index idx_purchase_terminal (`terminal_id`);' );
PREPARE stmt FROM @SQL;
EXECUTE stmt;