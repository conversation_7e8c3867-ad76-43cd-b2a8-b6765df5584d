DROP PROCEDURE IF EXISTS alterAuditTrailHistoryTableAddDevId;
DROP PROCEDURE IF EXISTS alterAuditTrailHistory2020Table;

DELIMITER //
CREATE PROCEDURE alterAuditTrailHistoryTableAddDevId()
	BEGIN
	  DECLARE auditHistoryTableExists TINYINT DEFAULT 0;
	  DECLARE audit2019TableExists TINYINT DEFAULT 0;
	  DECLARE audit2020TableExists TINYINT DEFAULT 0;
	  DECLARE audit2021TableExists TINYINT DEFAULT 0;
	  DECLARE audit2022TableExists TINYINT DEFAULT 0;
	  SELECT 1 INTO auditHistoryTableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_history' AND TABLE_SCHEMA=DATABASE ( );
	  SELECT 1 INTO audit2019TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2019' AND TABLE_SCHEMA=DATABASE ( );

	  SELECT 1 INTO audit2020TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2020' AND TABLE_SCHEMA=DATABASE ( );
	  SELECT 1 INTO audit2021TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2021' AND TABLE_SCHEMA=DATABASE ( );
	  SELECT 1 INTO audit2022TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2022' AND TABLE_SCHEMA=DATABASE ( );

	  IF auditHistoryTableExists = 1 THEN
      ALTER TABLE pax_audit_trail_history ADD COLUMN dev_id BIGINT(20) DEFAULT NULL COMMENT '开发者ID' after user_id;
    END IF;

    IF audit2019TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2019 ADD COLUMN dev_id BIGINT(20) DEFAULT NULL COMMENT '开发者ID' after user_id;
    END IF;

    IF audit2020TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2020 ADD COLUMN dev_id BIGINT(20) DEFAULT NULL COMMENT '开发者ID' after user_id;
    END IF;

    IF audit2021TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2021 ADD COLUMN dev_id BIGINT(20) DEFAULT NULL COMMENT '开发者ID' after user_id;
    END IF;

    IF audit2022TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2022 ADD COLUMN dev_id BIGINT(20) DEFAULT NULL COMMENT '开发者ID' after user_id;
    END IF;
	END;




CREATE PROCEDURE alterAuditTrailHistory2020Table()
	BEGIN
	  DECLARE audit2020TableExists TINYINT DEFAULT 0;
	  DECLARE audit2021TableExists TINYINT DEFAULT 0;
	  DECLARE audit2022TableExists TINYINT DEFAULT 0;

	  SELECT 1 INTO audit2020TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2020' AND TABLE_SCHEMA=DATABASE ( );
	  SELECT 1 INTO audit2021TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2021' AND TABLE_SCHEMA=DATABASE ( );
	  SELECT 1 INTO audit2022TableExists FROM information_schema.`TABLES` WHERE TABLE_NAME='pax_audit_trail_2022' AND TABLE_SCHEMA=DATABASE ( );

	  IF audit2020TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2020 ADD COLUMN time_spent INT;
    END IF;

    IF audit2021TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2021 ADD COLUMN time_spent INT;
    END IF;

    IF audit2022TableExists = 1 THEN
      ALTER TABLE pax_audit_trail_2022 ADD COLUMN time_spent INT;
    END IF;
	END;

CALL alterAuditTrailHistoryTableAddDevId();
CALL alterAuditTrailHistory2020Table();