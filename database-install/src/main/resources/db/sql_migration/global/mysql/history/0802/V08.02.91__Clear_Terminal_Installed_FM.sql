DELETE
FROM
    pax_terminal_installed_fm
WHERE
        terminal_id IN ( SELECT terminal_id FROM ( SELECT terminal_id FROM pax_terminal_installed_fm GROUP BY terminal_id HAVING count( 1 ) > 1 ) AS temp )
  AND ( terminal_id, sync_date ) NOT IN ( SELECT terminal_id, lastSyncDate FROM ( SELECT terminal_id, max( sync_date ) AS "lastSyncDate" FROM pax_terminal_installed_fm GROUP BY terminal_id HAVING count( 1 ) > 1 ) AS temp );