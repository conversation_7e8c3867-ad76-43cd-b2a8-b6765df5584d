UPDATE pax_factory f
SET f.address = CONCAT(f.address,',',(
IF (
( SELECT c.label FROM pax_code c WHERE c.`value` = f.city AND c.lang = 'en' AND c.type = 'city' ) IS NOT NULL,
( CONCAT((SELECT c.label FROM pax_code c WHERE c.`value` = f.city AND c.lang = 'en' AND c.type = 'city'), ',', ( SELECT c.label FROM pax_code c WHERE c.`value` = f.province AND c.lang = 'en' AND c.type = 'province' ))),
IF (
( SELECT c.label FROM pax_code c WHERE c.`value` = f.province AND c.lang = 'en' AND c.type = 'province' ) IS NOT NULL,
( SELECT c.label FROM pax_code c WHERE c.`value` = f.province AND c.lang = 'en' AND c.type = 'province' ),'' ) )
)) WHERE LENGTH(f.address)<200;