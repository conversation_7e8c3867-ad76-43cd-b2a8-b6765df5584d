--/*      
-- * ===========================================================================================  
-- * = COPYRIGHT                
-- *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION      
-- *   This software is supplied under the terms of a license agreement or nondisclosure  
-- *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or 
-- *   disclosed except in accordance with the terms in that agreement.           
-- *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved. 
-- *      
-- * Revision History:        
-- * Date                     Author                  Action
-- * 20190128                 jianrong                Create
-- * ===========================================================================================  
-- */

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `pax_notify_msg_history`;
DROP TABLE IF EXISTS `pax_notify_user_msg_history`;

CREATE TABLE `pax_notify_msg_history`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `title` varchar(255) NOT NULL COMMENT 'message title',
  `content` TEXT NOT NULL COMMENT 'message rich text content',
  `plaintext_content` varchar(1000) COMMENT 'message plain text content',
  `attachment_fname` varchar(150) COMMENT 'message attachment file name',
  `attachment_fid` varchar(100) COMMENT 'message attachment file id',
  `msg_category` tinyint(1) NOT NULL COMMENT 'message category, eg. platform, task, subscription',
  `msg_type` tinyint(1) NOT NULL COMMENT 'message type, eg. report export, firmware version update',
  `priority` tinyint(1) NOT NULL COMMENT 'message priority, 1-High, 2-Medium, 3-Low',
  `sender_id` int(11) NOT NULL COMMENT 'message sender id',
  `receiver_type` tinyint(1) NOT NULL COMMENT 'message receiver type; 0-Global, 1-Subscriber, 2-Personal, etc.',
  `receiver_group_id` int(11) NOT NULL COMMENT 'message receiver group id',
  `published_on` bigint(13) NOT NULL COMMENT 'message publish date',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record create date',
  PRIMARY KEY (`id`),
  INDEX `idx_ntf_hist_msg_pubon`(`published_on`),
  INDEX `idx_ntf_hist_msg_rcvtp`(`receiver_type`)
) AUTO_INCREMENT = 1000000000;

CREATE TABLE `pax_notify_user_msg_history`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `receiver_id` int(11) NOT NULL COMMENT 'message receiver id',
  `src_msg_id` int(11) NOT NULL COMMENT 'message source id',
  `title` varchar(255) NOT NULL COMMENT 'message title',
  `msg_category` tinyint(1) NOT NULL COMMENT 'message category',
  `msg_type` tinyint(1) NOT NULL COMMENT 'message type',
  `published_on` bigint(13) NOT NULL COMMENT 'message publish date',
  `has_attachment` bit(1) NOT NULL DEFAULT '0' COMMENT 'message attachment indicator',
  `is_top_shown` bit(1) NOT NULL DEFAULT '0' COMMENT '1 - shown on top while not read',
  `is_read` bit(1) NOT NULL DEFAULT '0' COMMENT '0 - unread, 1 - read',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record creation date',
  `modified_on` datetime(0) NULL DEFAULT NULL COMMENT 'record modification date',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_ntf_hist_um_rcvid_srcmsgid`(`receiver_id`, `src_msg_id`),
  INDEX `idx_ntf_hist_um_pubon`(`published_on`)
) AUTO_INCREMENT = 1000000000;
  
SET FOREIGN_KEY_CHECKS = 1;