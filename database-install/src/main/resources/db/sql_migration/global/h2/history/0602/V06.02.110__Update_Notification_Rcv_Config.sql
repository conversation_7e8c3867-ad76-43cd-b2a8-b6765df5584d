--/*      
-- * ===========================================================================================  
-- * = COPYRIGHT                
-- *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION      
-- *   This software is supplied under the terms of a license agreement or nondisclosure  
-- *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or 
-- *   disclosed except in accordance with the terms in that agreement.           
-- *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved. 
-- *      
-- * Revision History:        
-- * Date                     Author                  Action
-- * 20190219                 jianrong                Create
-- * ===========================================================================================  
-- */

DELETE FROM pax_notify_user_config WHERE msg_type in (2, 3, 11, 12, 72, 73, 81);

DELETE FROM pax_notify_user_config WHERE msg_type in (23, 31, 43, 151);
INSERT INTO `pax_notify_user_config`(`receiver_id`, `msg_type`, `is_console_enabled`, `is_email_enabled`) VALUES
  (0, 23, 0, 1),
  (0, 31, 0, 1),
  (0, 43, 1, 0),
  (0, 151, 0, 1);

UPDATE pax_notify_user_config SET is_console_enabled=0 WHERE msg_type IN (21, 22, 41, 42) AND receiver_id = 0;

ALTER TABLE pax_notify_msg ADD `attachment_fname` varchar(150) COMMENT 'message attachment file name';
ALTER TABLE pax_notify_msg ADD `attachment_fid` varchar(100) COMMENT 'message attachment file id';

ALTER TABLE pax_notify_user_msg ADD `has_attachment` bit(1) NOT NULL DEFAULT '0' COMMENT 'message attachment indicator';