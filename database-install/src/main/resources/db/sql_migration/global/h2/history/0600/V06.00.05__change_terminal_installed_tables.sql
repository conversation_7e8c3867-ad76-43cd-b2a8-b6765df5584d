ALTER TABLE pax_terminal_installed_apk ADD COLUMN sync_date DATETIME;
ALTER TABLE pax_terminal_installed_fm ADD COLUMN sync_date DATETIME;
ALTER TABLE pax_terminal_hardware ADD COLUMN sync_date DATETIME;

UPDATE pax_terminal_installed_apk tia SET sync_date = (SELECT sync_date FROM pax_terminal t WHERE t.id = tia.terminal_id);
UPDATE pax_terminal_installed_fm tia SET sync_date = (SELECT sync_date FROM pax_terminal t WHERE t.id = tia.terminal_id);
UPDATE pax_terminal_hardware tia SET sync_date = (SELECT sync_date FROM pax_terminal t WHERE t.id = tia.terminal_id);

ALTER TABLE pax_terminal CHANGE sync_date app_sync_date DATETIME;
ALTER TABLE pax_terminal ADD COLUMN firmware_sync_date DATETIME AFTER app_sync_date;
ALTER TABLE pax_terminal ADD COLUMN hardware_sync_date DATETIME AFTER firmware_sync_date;