--/*      
-- * ===========================================================================================  
-- * = COPYRIGHT                
-- *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION      
-- *   This software is supplied under the terms of a license agreement or nondisclosure  
-- *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or 
-- *   disclosed except in accordance with the terms in that agreement.           
-- *     Copyright (C) YYYY-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved. 
-- *      
-- * Revision History:        
-- * Date                     Author                  Action
-- * 20181206                 jianrong                Create
-- * ===========================================================================================  
-- */

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `pax_notify_user_config`;
DROP TABLE IF EXISTS `pax_notify_topic`;
DROP TABLE IF EXISTS `pax_notify_topic_subscriber`;
DROP TABLE IF EXISTS `pax_notify_user_msg`;
DROP TABLE IF EXISTS `pax_notify_msg`;

CREATE TABLE `pax_notify_msg`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `title` varchar(100) NOT NULL COMMENT 'message title',
  `content` TEXT NOT NULL COMMENT 'message rich text content',
  `plaintext_content` varchar(1000) COMMENT 'message plain text content',
  `msg_category` tinyint(1) NOT NULL COMMENT 'message category, eg. platform, task, subscription',
  `msg_type` tinyint(1) NOT NULL COMMENT 'message type, eg. report export, firmware version update',
  `priority` tinyint(1) NOT NULL COMMENT 'message priority, 1-High, 2-Medium, 3-Low',
  `sender_id` int(11) NOT NULL COMMENT 'message sender id',
  `receiver_type` tinyint(1) NOT NULL COMMENT 'message receiver type; 0-Global, 1-Subscriber, 2-Personal, etc.',
  `receiver_group_id` int(11) NOT NULL COMMENT 'message receiver group id',
  `published_on` bigint(13) NOT NULL COMMENT 'message publish date',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record create date',
  PRIMARY KEY (`id`),
  INDEX `idx_ntf_msg_pubon`(`published_on`),
  INDEX `idx_ntf_msg_rcvtp`(`receiver_type`)
) AUTO_INCREMENT = 1000000000;

CREATE TABLE `pax_notify_user_msg`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `receiver_id` int(11) NOT NULL COMMENT 'message receiver id',
  `src_msg_id` int(11) NOT NULL COMMENT 'message source id',
  `title` varchar(100) NOT NULL COMMENT 'message title',
  `msg_category` tinyint(1) NOT NULL COMMENT 'message category',
  `msg_type` tinyint(1) NOT NULL COMMENT 'message type',
  `published_on` bigint(13) NOT NULL COMMENT 'message publish date',
  `is_top_shown` bit(1) NOT NULL DEFAULT '0' COMMENT '1 - shown on top while not read',
  `is_read` bit(1) NOT NULL DEFAULT '0' COMMENT '0 - unread, 1 - read',
  `is_deleted` bit(1) NOT NULL DEFAULT '0' COMMENT '0 - not deleted, 1 - deleted',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record creation date',
  `modified_on` datetime(0) NULL DEFAULT NULL COMMENT 'record modification date',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_ntf_um_rcvid_srcmsgid`(`receiver_id`, `src_msg_id`),
  INDEX `idx_ntf_um_pubon`(`published_on`)
) AUTO_INCREMENT = 1000000000;

CREATE TABLE `pax_notify_topic`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `external_id` int(11) NOT NULL COMMENT 'topic id in external system',
  `title` varchar(50) NOT NULL COMMENT 'topic title',
  `description` varchar(255) NOT NULL COMMENT 'topic description',
  `topic_category` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'topic category',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record creation date',
  `modified_on` datetime(0) NULL DEFAULT NULL COMMENT 'record modification date',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_ntf_t_externalid`(`external_id`)
) AUTO_INCREMENT = 1000000000;

CREATE TABLE `pax_notify_topic_subscriber`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `topic_id` int(11) NOT NULL COMMENT 'topic id',
  `subscriber_id` int(11) NOT NULL COMMENT 'topic subscriber id (user id)',
  `is_deleted` bit(1) NOT NULL DEFAULT '0' COMMENT '0 - not deleted, 1 - deleted',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record creation date',
  `modified_on` datetime(0) NULL DEFAULT NULL COMMENT 'record modification date',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_ntf_ts_mcols`(`is_deleted`, `subscriber_id`, `topic_id`),
  INDEX `idx_ntf_ts_st`(`subscriber_id`, `topic_id`)
) AUTO_INCREMENT = 1000000000;

CREATE TABLE `pax_notify_user_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'record id',
  `receiver_id` int(11) NOT NULL COMMENT 'message receiver id',
  `msg_type` smallint NOT NULL COMMENT 'message type',
  `is_console_enabled` bit(1) NOT NULL DEFAULT '0' COMMENT 'indicator for receiving internal message',
  `is_email_enabled` bit(1) NOT NULL DEFAULT '0' COMMENT 'indicator for receiving email',
  `is_top_shown` bit(1) NOT NULL DEFAULT '0' COMMENT '1 - shown on top while not read',
  `created_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record creation date',
  `modified_on` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'record modification date',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_ntf_uc_recvid_msgtype`(`receiver_id`, `msg_type`)
) AUTO_INCREMENT = 1000000000;

INSERT INTO `pax_notify_user_config`(`receiver_id`, `msg_type`, `is_console_enabled`, `is_email_enabled`) VALUES 
  (0, 1, '1', '0'),
  (0, 2, '1', '0'),
  (0, 3, '1', '0'),
  (0, 11, '1', '0'),
  (0, 12, '1', '0'),
  (0, 21, '1', '1'),
  (0, 22, '1', '1'),
  (0, 41, '1', '1'),
  (0, 42, '1', '1'),
  (0, 71, '1', '0'),
  (0, 72, '1', '0'),
  (0, 73, '1', '0'),
  (0, 81, '1', '0'),
  (0, 101, '1', '0'),
  (0, 102, '1', '0');
  
SET FOREIGN_KEY_CHECKS = 1;