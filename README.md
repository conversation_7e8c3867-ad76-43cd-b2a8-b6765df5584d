**~~_`****`_~~**# paxstore-backend

This project is using lombok for code generation, please install lombok IDE plugin first, see https://projectlombok.org/setup/overview for details.

### 本地启动auth

#### 1. 本地源码启动

- 从gitlab checkout paxstore-auth:

```shell
git checkout https://gitlab.paxszapp.com/paxstore-v2/paxstore-auth.git
```
- 进入auth-server子模块, 运行以下命令:

```shell
mvn clean install -DskipTests
cd auth-server
mvn spring-boot:run -Pss
```

#### 2. 本地Docker启动

-- TODO